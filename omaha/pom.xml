<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.dzpk</groupId>
	<artifactId>omaha</artifactId>
	<version>1.3.5</version>
	<packaging>jar</packaging>

	<properties>
		<package.classpath.prefix>omaha_lib</package.classpath.prefix>
		<mysql.version>5.1.18</mysql.version>
		<unknown.version>1.0</unknown.version>
        <lombok.version>1.18.4</lombok.version>

		<!-- Related to log START -->
		<log4j.version>2.7</log4j.version>
		<jcl.over.slf4j.version>1.7.25</jcl.over.slf4j.version>
		<jul.over.slf4j.version>1.7.25</jul.over.slf4j.version>
		<disruptor.version>3.3.6</disruptor.version>
		<bouncycastle.version>1.55</bouncycastle.version>
		<!-- Related to log END -->

		<!-- zookeeper client -->
		<zk.curator.version>2.12.0</zk.curator.version>

		<lombok.version>1.18.4</lombok.version>

		<!-- 设置打包时间格式， 对应${maven.build.timestamp}变量 -->
		<maven.build.timestamp.format>yyMMdd</maven.build.timestamp.format>
	</properties>

	<dependencies>
        <!-- lombok component start -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>
        <!-- lombok component end -->
		<dependency>
			<groupId>com.baidu.yun</groupId>
			<artifactId>bccs-api</artifactId>
			<version>3.0.1</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/bccs-api-3.0.1.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<version>1.9.2</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/commons-beanutils-1.9.2.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>commons-collections</groupId>
			<artifactId>commons-collections</artifactId>
			<version>3.2.1</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/commons-collections-3.2.1.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.4</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/commons-io-2.4.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>commons-jxpath</groupId>
			<artifactId>commons-jxpath</artifactId>
			<version>1.1</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/commons-jxpath-1.1.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>2.6</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/commons-lang-2.6.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>commons-logging</groupId>
			<artifactId>commons-logging</artifactId>
			<version>${unknown.version}</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/commons-logging.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
			<version>2.2</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/commons-pool2-2.2.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>commons-pool</groupId>
			<artifactId>commons-pool</artifactId>
			<version>1.1</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/commons-pool-1.1.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>oswego-concurrent</groupId>
			<artifactId>concurrent</artifactId>
			<version>${unknown.version}</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/concurrent.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>crimson</groupId>
			<artifactId>crimson</artifactId>
			<version>${unknown.version}</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/crimson.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>net.sf.ezmorph</groupId>
			<artifactId>ezmorph</artifactId>
			<version>1.0.6</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/ezmorph-1.0.6.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.8.1</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/gson-2.8.1.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>jcs</groupId>
			<artifactId>jcs</artifactId>
			<version>${unknown.version}</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/jcs.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>jdom</groupId>
			<artifactId>jdom</artifactId>
			<version>${unknown.version}</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/jdom.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>2.8.1</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/jedis-2.8.1.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>cn.jpush.api</groupId>
			<artifactId>jiguang-common</artifactId>
			<version>1.0.7</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/jiguang-common-1.0.7.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>cn.jpush.api</groupId>
			<artifactId>jpush-client</artifactId>
			<version>3.3.0</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/jpush-client-3.3.0.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20140107</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/json-20140107.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>net.sf.json-lib</groupId>
			<artifactId>json-lib</artifactId>
			<version>2.4-jdk15</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/json-lib-2.4-jdk15.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>jmsn</groupId>
			<artifactId>msnmlib</artifactId>
			<version>${unknown.version}</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/msnm.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>proxool</groupId>
			<artifactId>proxool</artifactId>
			<version>0.9.0RC3</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/proxool-0.9.0RC3.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>talkie</groupId>
			<artifactId>talkie</artifactId>
			<version>${unknown.version}</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/talkie.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>xerces</groupId>
			<artifactId>xercesImpl</artifactId>
			<version>${unknown.version}</version>
			<scope>system</scope>
			<systemPath>${basedir}/LIB/xercesImpl.jar</systemPath>
		</dependency>

		<!-- Related to logger -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.75</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-slf4j-impl</artifactId>
			<version>${log4j.version}</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>1.7.25</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>${log4j.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>${log4j.version}</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jcl-over-slf4j</artifactId>
			<version>${jcl.over.slf4j.version}</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jul-to-slf4j</artifactId>
			<version>${jul.over.slf4j.version}</version>
		</dependency>
		<dependency>
			<groupId>com.lmax</groupId>
			<artifactId>disruptor</artifactId>
			<version>${disruptor.version}</version>
		</dependency>

		<!-- Related to MySQL -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>${mysql.version}</version>
		</dependency>

		<!-- Related to ActiveMQ -->
		<dependency>
			<groupId>org.apache.activemq</groupId>
			<artifactId>activemq-all</artifactId>
			<version>5.11.1</version>
		</dependency>

		<!-- Related to Mongodb -->
		<dependency>
			<groupId>org.mongodb</groupId>
			<artifactId>mongo-java-driver</artifactId>
			<version>3.2.2</version>
		</dependency>

		<!-- Related to Netty -->
		<dependency>
			<groupId>io.netty</groupId>
			<artifactId>netty-all</artifactId>
			<version>4.1.68.Final</version>
		</dependency>

		<!-- zookeeper-curator client START-->
		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-framework</artifactId>
			<version>${zk.curator.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>io.netty</groupId>
					<artifactId>netty</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-recipes</artifactId>
			<version>${zk.curator.version}</version>
		</dependency>
		<!-- zookeeper-curator client END  -->

		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.2</version>
			<!--			<exclusions>
                            <exclusion>
                                <groupId>commons-codec</groupId>
                                <artifactId>commons-codec</artifactId>
                            </exclusion>
                        </exclusions>-->
		</dependency>
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcpkix-jdk15on</artifactId>
			<version>${bouncycastle.version}</version>
		</dependency>
        <!-- RabbitMQ依赖  -->
        <dependency>
            <groupId>com.rabbitmq</groupId>
            <artifactId>amqp-client</artifactId>
            <version>5.4.3</version>
        </dependency>

		<!-- lombok component start -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>${lombok.version}</version>
			<scope>provided</scope>
		</dependency>
		<!-- lombok component end -->
	</dependencies>
    
    <build>
		<finalName>${project.artifactId}-${env}-v${project.version}-${maven.build.timestamp}</finalName>
		<filters>
			<filter>filters/config-${env}.properties</filter>
		</filters>
		<resources>
            <resource>
                <directory>resource</directory>
				<includes>
					<include>**/*.*</include>
				</includes>
				<targetPath>${project.build.directory}/resource</targetPath>
				<filtering>true</filtering>
			</resource>
			<resource>
				<directory>${project.basedir}</directory>
				<includes>
					<include>log4j2.xml</include>
					<include>port.properties</include>
					<include>proxool.properties</include>
					<include>wg.properties</include>
					<include>zk-config.properties</include>
					<include>jackPot.properties</include>
				</includes>
				<targetPath>${project.build.directory}</targetPath>
				<filtering>true</filtering>
			</resource>
        </resources>
        <plugins>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>build-helper-maven-plugin</artifactId>
				<version>3.0.0</version>
				<executions>
					<execution>
						<id>add-source</id>
						<phase>generate-sources</phase>
						<goals>
							<goal>add-source</goal>
						</goals>
						<configuration>
							<sources>
                                <source>${basedir}/src/comm/src</source>
                                <source>${basedir}/src/common/src</source>
                                <source>${basedir}/src/component/src</source>
                                <source>${basedir}/src/feature/src</source>
                                <source>${basedir}/src/processor/src</source>
								<!-- 我们可以通过在这里添加多个source节点，来添加任意多个源文件夹 -->
							</sources>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>3.0.0</version>
				<configuration>
					<archive>
						<addMavenDescriptor>false</addMavenDescriptor>
						<manifest>
							<mainClass>com.i366.main.platformService</mainClass>
							<!-- 这种配置对scope=system的无效 -->
							<addClasspath>true</addClasspath>
							<classpathPrefix>${package.classpath.prefix}/</classpathPrefix>
							<classpathLayoutType>custom</classpathLayoutType>
							<customClasspathLayout>$${artifact.artifactId}-$${artifact.version}.jar</customClasspathLayout>
						</manifest>
						<!--
						   目前现有代码直接依赖指定的jar
						   为保证现有代码不受印象，只能通过scope=system方式建立依赖
						   导致在打包的jar中class-path必须手工指定
						   如果依赖jar发生改变，必须手动在此添加
						 -->
						<manifestEntries>
							<Class-Path>
								. ${package.classpath.prefix}/bccs-api-3.0.1.jar ${package.classpath.prefix}/commons-beanutils-1.9.2.jar ${package.classpath.prefix}/commons-collections-3.2.1.jar ${package.classpath.prefix}/commons-io-2.4.jar ${package.classpath.prefix}/commons-jxpath-1.1.jar ${package.classpath.prefix}/commons-lang-2.6.jar ${package.classpath.prefix}/commons-logging-${unknown.version}.jar ${package.classpath.prefix}/commons-pool2-2.2.jar ${package.classpath.prefix}/commons-pool-1.1.jar ${package.classpath.prefix}/concurrent-${unknown.version}.jar ${package.classpath.prefix}/crimson-${unknown.version}.jar ${package.classpath.prefix}/ezmorph-1.0.6.jar ${package.classpath.prefix}/gson-2.8.1.jar ${package.classpath.prefix}/j2ee-${unknown.version}.jar ${package.classpath.prefix}/jcs-${unknown.version}.jar ${package.classpath.prefix}/jdom-${unknown.version}.jar ${package.classpath.prefix}/jedis-2.8.1.jar ${package.classpath.prefix}/jiguang-common-1.0.7.jar ${package.classpath.prefix}/jpush-client-3.3.0.jar ${package.classpath.prefix}/json-20140107.jar ${package.classpath.prefix}/json-lib-2.4-jdk15.jar ${package.classpath.prefix}/msnm-${unknown.version}.jar ${package.classpath.prefix}/proxool-0.9.0RC3.jar ${package.classpath.prefix}/slf4j-api-1.7.25.jar ${package.classpath.prefix}/talkie-${unknown.version}.jar ${package.classpath.prefix}/tools-${unknown.version}.jar ${package.classpath.prefix}/xercesImpl-${unknown.version}.jar
							</Class-Path>
						</manifestEntries>
					</archive>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-dependencies</id>
						<phase>package</phase>
						<configuration>
							<overWriteReleases>false</overWriteReleases>
							<overWriteSnapshots>false</overWriteSnapshots>
							<overWriteIfNewer>true</overWriteIfNewer>
							<outputDirectory>
								${project.build.directory}/${package.classpath.prefix}
							</outputDirectory>
						</configuration>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.2</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

	<profiles>
		<profile>
			<id>develop</id>
			<properties>
				<env>develop</env>
			</properties>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
		<profile>
			<id>aws-online</id>
			<properties>
				<env>aws_online</env>
			</properties>
		</profile>
		<profile>
			<id>aws-test</id>
			<properties>
				<env>aws_test</env>
			</properties>
		</profile>
	</profiles>
</project>
