package com.dzpk.commission;

import com.dzpk.commission.impl.FeeServiceImpl;
import com.i366.model.room.Room;

public class CommissionFactory {
    /** 实例化工厂 */
    private static final CommissionFactory FACTORY_INSTANCE = new CommissionFactory();
    public static CommissionFactory getInstance(){
        return FACTORY_INSTANCE;
    }

    public IFeeService getFeeService(Room room){
        return new FeeServiceImpl(room);
    }
}
