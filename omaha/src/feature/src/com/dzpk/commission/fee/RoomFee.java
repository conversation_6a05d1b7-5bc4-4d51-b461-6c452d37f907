package com.dzpk.commission.fee;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

public class RoomFee {

    /**
     * 服务费变量
     */
    // 系统收入
    public BigDecimal sysIncome;
    // 系统仓
    public BigDecimal sysFee;

    // 社区仓,clubId -> fee
    public Map<Integer,BigDecimal> clubFeeMap;
    // 同盟仓,tribeId -> fee
    public Map<Integer,BigDecimal> tribeFeeMap;

    public RoomFee() {
        this.sysIncome = new BigDecimal(0.0000);
        this.sysFee = new BigDecimal(0.0000);
        this.clubFeeMap = new HashMap<>();
        this.tribeFeeMap = new HashMap<>();
    }

    public BigDecimal getSysIncome() {
        return sysIncome;
    }

    public void setSysIncome(BigDecimal sysIncome) {
        this.sysIncome = sysIncome;
    }

    public BigDecimal getSysFee() {
        return sysFee;
    }

    public void setSysFee(BigDecimal sysFee) {
        this.sysFee = sysFee;
    }

    public Map<Integer, BigDecimal> getClubFeeMap() {
        return clubFeeMap;
    }

    public Map<Integer, BigDecimal> getTribeFeeMap() {
        return tribeFeeMap;
    }
}
