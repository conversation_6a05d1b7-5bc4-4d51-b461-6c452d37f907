package com.dzpk.common.token.zk;

import com.dzpk.component.zk.ZkUtil;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.api.DeleteBuilder;
import org.apache.curator.framework.api.SetDataBuilder;
import org.apache.curator.framework.recipes.cache.ChildData;
import org.apache.curator.framework.recipes.cache.PathChildrenCache;
import org.apache.curator.framework.recipes.cache.PathChildrenCacheEvent;
import org.apache.curator.framework.recipes.cache.PathChildrenCacheListener;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.zookeeper.CreateMode;
import org.apache.zookeeper.data.Stat;

import java.nio.charset.Charset;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class TokenWatchService {
    /** 日志服务 */
    private static final Logger logger = LogManager.getLogger(TokenWatchService.class);

    /** 用户的会话token目录 */
    public static final String parentNodePath = "/dzpk/session/tokens";

    private static TokenWatchService watchServiceInstance = null;
    public static TokenWatchService getInstance(){
        if(watchServiceInstance==null){
            synchronized (TokenWatchService.class){
                if(watchServiceInstance==null){
                    watchServiceInstance = new TokenWatchService();
                }
            }
        }

        return watchServiceInstance;
    }

    /** token发生变化时的回调 */
    private IUserTokenCallback callback;
    public void setFundCallback(IUserTokenCallback callback){
        if(null != callback) {
            this.callback = callback;
        }
    }

    /** zookeeper客户端 */
    private CuratorFramework curatorFramework;

    /** 字符集 */
    private Charset defaultCharset;

    /**
     * 实例化服务对象
     */
    private TokenWatchService(){
        this.curatorFramework = ZkUtil.getClient();
        this.defaultCharset = Charset.forName("UTF-8");
    }

    /** 对外接口 */
    /**
     * 设置或更新用户的token
     * @param userId  待更新的用户ID
     * @param token   待更新的token值
     */
    public void updateToken(int userId,String token){
        TokenUpdator updator = new TokenUpdator(
                this,
                this.curatorFramework,
                userId,
                token,
                null,null);
        this.executorService.submit(updator);
    }

    /**
     * 删除用户的token
     * @param userId   待删除的用户ID
     * @param token    对应的token值
     * @param version  当前版本
     *
     * @return
     */
    public void deleteToken(int userId,String token,int version){
        TokenUpdator updator = new TokenUpdator(
                this,
                this.curatorFramework,
                userId,
                token,version);
        this.executorService.submit(updator);
    }

    /**
     * 创建节点
     * 节点不存在的情况下，才创建
     * @param nodePath
     */
    public void createNodeIfNeed(String nodePath,String token){
        try {
            Stat stat = this.curatorFramework.checkExists().forPath(nodePath);
            if (null == stat)
                this.curatorFramework.create()
                         .creatingParentsIfNeeded()
                         .withMode(CreateMode.PERSISTENT)
                         .forPath(nodePath, this.serialize(token==null?"":token.trim()));
        }catch (Exception ex){
            throw new RuntimeException("创建用户token节点失败:"+ex.getMessage(),ex);
        }
    }

    /**
     * 线程池
     */
    private ExecutorService executorService;

    /**
     * 子节点Cache
     */
    private PathChildrenCache parentNodeChildrenCache;
    private PathChildrenCache parentNodeChildrenCache(){
        return this.parentNodeChildrenCache;
    }
    private void tokenChanged(PathChildrenCacheEvent event){
        logger.info(String.format("====>用户token节点事件 -> %s",event.toString()));
        String nodePath;
        if(event.getType() == PathChildrenCacheEvent.Type.CHILD_REMOVED){
            nodePath = event.getData().getPath();
            byte[] data=event.getData().getData();
            int version=event.getData().getStat().getVersion();

            int userId = this.getUserId(nodePath);
            String token = this.deserialize(data);
            this.executorService.submit(new Runnable() {
                @Override
                public void run() {
                    if(null != TokenWatchService.this.callback) {
                        TokenWatchService.this.callback.delete(userId,token,version);
                    }
                }
            });
        }if(event.getType() == PathChildrenCacheEvent.Type.CHILD_ADDED ||
                event.getType() == PathChildrenCacheEvent.Type.CHILD_UPDATED) {
            nodePath = event.getData().getPath();
            byte[] data=event.getData().getData();
            int version=event.getData().getStat().getVersion();

            int userId = this.getUserId(nodePath);
            String token = this.deserialize(data);
            this.executorService.submit(new Runnable() {
                @Override
                public void run() {
                    if(null != TokenWatchService.this.callback) {
                        TokenWatchService.this.callback.update(userId, token, version);
                    }
                }
            });
        }
    }

    /**
     * 用户token的根目录
     */
    public String parentNodePath(){
        return TokenWatchService.parentNodePath;
    }

    public void initialize() {
        logger.info("====>Entering the Initialize of TokenWatchService..............");

        //初始化节点操作线程
        this.executorService = Executors.newCachedThreadPool();

        //用户token的节点Cache
        this.parentNodeChildrenCache = new PathChildrenCache(this.curatorFramework, parentNodePath(), true);
        this.parentNodeChildrenCache.getListenable().addListener(new ParentNodeChildrenListener(this));
        this.startParentNodeChildrenCache();

        logger.info("====>Exiting the Initialize of TokenWatchService ..............");
    }

    public void destroy(){
        logger.info("====>Entering the Destroy of TokenWatchService..............");
        this.stopParentNodeChildrenCache();
        if(null != this.executorService &&
                !this.executorService.isShutdown()){
            try {
                this.executorService.shutdownNow();
            }catch (Exception ex){
                logger.error("用户token回调线程池关闭失败："+ex.getMessage(),ex);
            }
        }
        logger.info("====>Exiting the destroy of TokenWatchService ..............");
    }

    /**
     * 启动用户token监控Cache
     */
    private void startParentNodeChildrenCache(){
        try {
            this.parentNodeChildrenCache().start(PathChildrenCache.StartMode.BUILD_INITIAL_CACHE);
        }catch (Exception ex){
            throw new RuntimeException("用户token监控Cache启动失败："+ex.getMessage(),ex);
        }
    }

    /**
     * 关闭用户token监控Cache
     */
    private void stopParentNodeChildrenCache(){
        if(null == this.parentNodeChildrenCache())
            return ;

        try{
            this.parentNodeChildrenCache().close();
        }catch (Exception ex){
            logger.error("用户token监控Cache关闭失败："+ex.getMessage(),ex);
        }
    }

    /**
     * 创建用户token节点路径
     * 每个用户对应一个节点
     * @param userId
     * @return
     */
    private String genPath(int userId){
        return String.format("%s/%s",this.parentNodePath(),String.valueOf(userId));
    }
    private int getUserId(String nodePath){
        int userId = 0;

        if(null == nodePath || "".equals(nodePath.trim()))
            return userId;

        nodePath = nodePath.trim();

        int seperatorIdx = nodePath.lastIndexOf("/");
        if(seperatorIdx==-1 || seperatorIdx== nodePath.length()-1)
        {
            return userId;
        }

        userId = Integer.parseInt(nodePath.substring(seperatorIdx+1));
        return userId;
    }

    private byte[] serialize(String value){
        return value.getBytes(this.defaultCharset);
    }
    private String deserialize(byte[] data){
        if(null == data || data.length<=0)
            return null;

        return new String(data,this.defaultCharset);
    }

    /**
     * 用户token节点事件监听器
     */
    private static class ParentNodeChildrenListener implements PathChildrenCacheListener{
        /** 日志组件 */
        private static  final Logger logger = LogManager.getLogger(ParentNodeChildrenListener.class);

        private TokenWatchService watchService;

        public ParentNodeChildrenListener(TokenWatchService watchService){
            this.watchService = watchService;
        }

        public void childEvent(CuratorFramework client, PathChildrenCacheEvent event) throws Exception{
            this.watchService.tokenChanged(event);
        }
    }

    public static class TokenUpdatedResult{
        private int userId;

        /** 更新前的值 */
        private String oldToken;
        private Integer oldVersion;

        /** 更新后的值 */
        private String token;
        private int version;

        private boolean deletion;

        public int getUserId() {
            return userId;
        }

        public void setUserId(int userId) {
            this.userId = userId;
        }

        public String getOldToken() {
            return oldToken;
        }

        public void setOldToken(String oldToken) {
            this.oldToken = oldToken;
        }

        public Integer getOldVersion() {
            return oldVersion;
        }

        public void setOldVersion(Integer oldVersion) {
            this.oldVersion = oldVersion;
        }

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public int getVersion() {
            return version;
        }

        public void setVersion(int version) {
            this.version = version;
        }

        public boolean isDeletion() {
            return deletion;
        }

        public void setDeletion(boolean deletion) {
            this.deletion = deletion;
        }
    }

    /**
     * Token更新器
     */
    private static class TokenUpdator implements Callable<TokenUpdatedResult>{
        /** 日志组件 */
        private static  final Logger logger = LogManager.getLogger(TokenUpdator.class);

        private CuratorFramework curatorFramework;
        private TokenWatchService watchService;

        private String nodePath;

        private int userId;
        private String newValue;
        private String originalValue;
        private Integer originalVersion;

        /**
         * 构造函数
         * @param curatorFramework  zookeeper客户端
         * @param originalValue     原始值
         * @param originalVersion   原始值的版本号
         */
        public TokenUpdator(TokenWatchService service,
                            CuratorFramework curatorFramework,
                            int userId,
                            String originalValue, Integer originalVersion) {
            this(service,curatorFramework,userId,null,originalValue,originalVersion);
        }

        /**
         * 构造函数
         * @param curatorFramework  zookeeper客户端
         * @param newValue          新值，如果null，则认为delete操作
         *                          否则更新
         * @param originalValue     原始值
         * @param originalVersion   原始值的版本号
         */
        public TokenUpdator(TokenWatchService service,
                            CuratorFramework curatorFramework,
                            int userId,
                            String newValue,
                            String originalValue, Integer originalVersion){
            this.curatorFramework = curatorFramework;
            this.watchService = service;

            this.userId = userId;
            this.newValue = newValue;

            this.originalValue = originalValue;
            this.originalVersion = originalVersion;

            this.nodePath = this.watchService.genPath(this.userId);
        }

        private boolean isDeletion(){
            return null == this.newValue || "".equals(this.newValue.trim());
        }

        private TokenUpdatedResult delete(){
            try {
                DeleteBuilder builder = this.curatorFramework.delete();
                if(null != this.originalVersion){
                    builder.withVersion(this.originalVersion);
                }
                builder.forPath(this.nodePath);
            }catch (Exception ex){
                logger.warn(String.format("====> 删除用户Token节点[ 路径=%s ,原版本号=%s ] 失败：%s",
                        this.nodePath,
                        this.originalVersion,
                        ex.getMessage()),ex);
            }

            TokenUpdatedResult result = new TokenUpdatedResult();
            result.setUserId(userId);
            result.setDeletion(true);

            return result;
        }

        public TokenUpdatedResult call() {
            boolean isDeletion = this.isDeletion();
            if(isDeletion){
                ChildData nodeData = this.watchService.parentNodeChildrenCache.getCurrentData(this.nodePath);
                if(null == nodeData)
                    return null;

                return this.delete();
            }

            TokenUpdatedResult result = new TokenUpdatedResult();
            while (true) {
                int newVersion = 0;
                boolean existing = false;
                try {
                    if(null == this.originalVersion) {
                        ChildData nodeData = this.watchService.parentNodeChildrenCache.getCurrentData(this.nodePath);
                        if(null != nodeData) {
                            this.originalValue = this.watchService.deserialize(nodeData.getData());
                            this.originalVersion = nodeData.getStat().getVersion();
                            existing = true;
                        }
                    }

                    if(existing) {
                        SetDataBuilder builder = this.curatorFramework.setData();
                        if (null != this.originalVersion) {
                            builder.withVersion(this.originalVersion);
                        }
                        newVersion = builder.forPath(this.nodePath, this.watchService.serialize(this.newValue.trim()))
                                .getVersion();
                    }else
                    {
                        this.curatorFramework.create()
                                .creatingParentsIfNeeded()
                                .withMode(CreateMode.PERSISTENT)
                                .forPath(nodePath, this.watchService.serialize(this.newValue==null?"":this.newValue.trim()));

                        ChildData nodeData = this.watchService.parentNodeChildrenCache.getCurrentData(this.nodePath);
                        newVersion = nodeData.getStat().getVersion();
                    }

                    if (logger.isDebugEnabled()) {
                        logger.debug(String.format("====> 成功修改用户Token节点：路径=%s , 原节点值=%s , 原版本号=%s , 新节点值=%s , 新版本号=%s",
                                this.nodePath,
                                this.originalValue,
                                this.originalVersion,
                                this.newValue,
                                newVersion));
                    }
                    result.setUserId(this.userId);
                    result.setOldToken(this.originalValue);
                    result.setOldVersion(this.originalVersion);
                    result.setToken(this.newValue);
                    result.setVersion(newVersion);
                    break;
                } catch (Exception ex) {
                    logger.debug(String.format("====> 修改用户Token节点失败：路径=%s , 原节点值=%s , 原版本号=%s , 新节点值=%s , 新版本号=%s -> %s",
                            this.nodePath,
                            this.originalValue,
                            this.originalVersion,
                            this.newValue,
                            newVersion,
                            ex.getMessage()),ex);
                    this.originalValue=null;
                    this.originalVersion=null;
                }
            }

            return result;
        }
    }

    public interface IUserTokenCallback {
        /**
         * Token删除通知
         * @param userId      用户ID
         * @param token       token值
         * @param version     版本
         */
        void delete(int userId, String token, int version);

        /**
         * Token变化通知
         * @param userId      用户ID
         * @param token       token值
         * @param version     版本
         */
        void update(int userId, String token, int version);
    }
}
