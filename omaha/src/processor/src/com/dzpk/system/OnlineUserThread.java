
package com.dzpk.system;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.work.WorkThreadService;
import com.i366.cache.Cache;
import com.i366.model.room.Room;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.TimeUnit;

/**
 * 维护在线用户进程
 * 策略：
 *     每隔20秒检测在线用户列表,如果60秒内没有发送过心跳则认为掉线,需要清除(ai用户除外)
 */
public class OnlineUserThread implements Runnable{

    private static final Logger logger = LogUtil.getLogger(OnlineUserThread.class);

	private static final long V_TIME = 60 * 1000 - 10;
	private static final long SPACE_LOAD_TIME = 20 * 1000; // 20S扫描一次在线列表 操作60秒不在线T掉

	public void start() {
		WorkThreadService.schedulePool.scheduleAtFixedRate(this, 0, SPACE_LOAD_TIME, TimeUnit.MILLISECONDS);
	}

    public void run() {
		try {
			action();
		} catch (Throwable t) {
			logger.error("OnlineUserThread error", t);
		}
	}
	
	private void action() {
		Cache.applyOnlineUserInfo((userInfo, it) -> {
			long idleTime = System.currentTimeMillis() - userInfo.getOnlineTime();
			if (idleTime > V_TIME) {
			    long timeout = idleTime/1000;
			    logger.debug("room detect persion timeout!!! roomid: " + userInfo.getRoomId() + " userid: " + userInfo.getUserId() + " timeout: " + timeout);
				Room room = Cache.getRoom(userInfo.getRoomId(), userInfo.getRoomPath());
				boolean needRemove = false;

				//AI用户不清理
				if (room != null) { // 托管状态不清理在线用户列表
					needRemove = room.getRoomService().userTimeOut(userInfo);
					logger.debug("need remove: " + needRemove + " userInfo=" +userInfo.getUserId());
				} else {
					logger.error("出现了个空房间......业务逻辑有问题！！");
				}

				if (needRemove) {
					it.remove();
				}
			}
        });
	}
}

