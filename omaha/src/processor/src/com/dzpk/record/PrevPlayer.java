package com.dzpk.record;

public class PrevPlayer {
    
    private int userId;
    private String nikeName;
    private String userHead;
    private int sex;
    private Integer[] handPocers = new Integer[4];      // 手牌
    private int pocerType;                              // 牌型
    private int winChip;                                // 输赢筹码
    private int winInsurance = 0;                       // 赢得保险筹码
    private Integer[] maxCardIndexs = new Integer[5];   // 最大牌型位置
    private Integer[] showCard = new Integer[] { 0, 0, 0, 0 };        // 是否选择亮牌 0不亮 1亮第一张 2亮第二张 3亮两张
    private int chips;                                  // 初始筹码
    private int seatId;                                 // 座位号
    private int straddleChip;                           // straddle chip

    public PrevPlayer() {
        userId = 0;
        handPocers[0] = -1;
        handPocers[1] = -1;
        handPocers[2] = -1;
        handPocers[3] = -1;
        pocerType = 10;         // -2盖牌 0弃牌 1皇家同花 2同花 3四条 4葫芦 5同花 6顺子 7三条 8两对 9一对 10高牌
        winChip = 0;
        for (int i = 0; i < maxCardIndexs.length; i++) {
            maxCardIndexs[i] = -1;
        }
        showCard = new Integer[]{ 0, 0, 0, 0 };
    }
    
    public void setUserId(int userId) {
        this.userId = userId;
    }
    
    public int getUserId() {
        return userId;
    }
    
    public Integer[] getPocers() {
        return handPocers;
    }
    
    public int getPocerType() {
        return pocerType;
    }
    
    public void setPocerType(int pocerType) {
        this.pocerType = pocerType;
    }
    
    public int getWinChip() {
        return winChip;
    }
    
    public void setWinChip(int winChip) {
        this.winChip = winChip;
    }

    public int getWinInsurance() {
        return winInsurance;
    }

    public void setWinInsurance(int winInsurance) {
        this.winInsurance = winInsurance;
    }
    
    public Integer[] getMaxCardIndexs() {
        return maxCardIndexs;
    }
    
    // 第i张最大牌对应7张牌中的第index张牌
    public void setMaxCardIndex(int i, int index) {
        maxCardIndexs[i] = index;
    }
    
    public String getNikeName() {
        return nikeName;
    }

    public void setNikeName(String nikeName) {
        this.nikeName = nikeName;
    }

    public String getUserHead() {
        return userHead;
    }

    public void setUserHead(String userHead) {
        this.userHead = userHead;
    }

    public int getSex() {
        return sex;
    }

    public void setSex(int sex) {
        this.sex = sex;
    }

    public Integer[] getShowCard() {
        return showCard;
    }

    public void setShowCard(Integer[] showCardId) {
        this.showCard = showCardId;
    }

    public int getChips() {
        return chips;
    }

    public void setChips(int chips) {
        this.chips = chips;
    }

    public int getSeatId() {
        return seatId;
    }

    public void setSeatId(int seatId) {
        this.seatId = seatId;
    }

    public int getStraddleChip() {
        return straddleChip;
    }

    public void setStraddleChip(int straddleChip) {
        this.straddleChip = straddleChip;
    }
}
