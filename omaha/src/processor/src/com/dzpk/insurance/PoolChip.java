package com.dzpk.insurance;

import java.util.HashSet;
import java.util.Set;

public class PoolChip {

    private Set<Integer> userIds;   // 占有边池玩家id集合
    private int totalChips;         // 边池总筹码数
    private int unitChips;          // 边池单位筹码数
    private int unit;               //是否为主池1是0否
    public PoolChip() {
        userIds = new HashSet<Integer>();
        totalChips = 0;
        unitChips = 0;
        unit=0;
    }
    public int getUnit() {
        return unit;
    }

    public void setUnit(int unit) {
        this.unit = unit;
    }
    public Set<Integer> getUserIds() {
        return userIds;
    }
        
    public void setTotalChips(int totalChips) {
        this.totalChips = totalChips;
    }
    
    public int getTotalChips() {
        return totalChips;
    }
    
    public void setUnitChips(int unitChips) {
        this.unitChips = unitChips;
    }
    
    public int getUnitChips() {
        return unitChips;
    }
}
