package com.dzpk.dealer.statistics;


/**
 * Created by baidu on 16/12/21.
 */
public class Main {
    public static final String HAND_CNT = "hand_cnt";             // 总手数
    public static final String SERIES_CNT = "series_cnt";         // 总局数
    public static final String CLUB_SERIES_CNT = "club_series_cnt";         // 总局数
    public static final String EARN = "total_earn";               // 总盈利
    public static final String POOL_CNT = "pool_cnt";             // 入池数
    public static final String POOL_WIN_CNT = "pool_win_cnt";     // 入池赢牌数
    public static final String POOL_HAND_CNT = "pool_hand_cnt";   // 相对入池数的总手数
    public static final String PER = "per";                       // per:翻牌前主动加注的频率
    public static final String STEAL = "steal";                   // 偷盲数
    public static final String BSTEAL = "before_steal";           // 可偷盲数
    public static final String BCBET = "bcbet";                   // 可cbet次数
    public static final String CBET = "cbet";                     // cbet次数
    public static final String TANPAI_CNT = "tanpai_cnt";         // 摊牌数
    public static final String FANPAI_CNT = "fanpai_cnt";         // 翻牌数
    public static final String USERHANDCNT = "user_hand_cnt";     // 相对手数
    public static final String AF_JIAZHU_CNT = "af_jiazhu_cnt";   // af加注手数
    public static final String AF_GENZHU_CNT = "af_genzhu_cnt";   // af跟注手数
    public static final String AF_XIAZHU_CNT = "af_xiazhu_cnt";   // af下注手数
    public static final String BRING_IN = "bring_in";             // 总带入
    public static final String BRING_SERIES_CNT = "bring_series_cnt";             // 总带入局数
    public static final String BET3_CNT = "bet3_cnt";             // 翻牌前再加注手数
    public static final String TANPAI_WIN_CNT = "tanpai_win"; // 摊牌赢手数
    public static final String CLUB_ROOM_CHARGE_TOTAL = "club_room_charge_total";   // 总服务费
    public static final String CLUB_ROOM_PL_TOTAL = "club_room_pl_total";   // 俱乐部牌局的总输赢
}
