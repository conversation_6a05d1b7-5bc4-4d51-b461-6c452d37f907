package com.dzpk.db.impl;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.repositories.mysql.dbpool.DBUtil;
import com.dzpk.db.dao.RoomManagerDao;
import org.apache.logging.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class RoomManagerDaoImp implements RoomManagerDao {

    private static Logger logger = LogUtil.getLogger(RoomManagerDaoImp.class);

    @Override
    public List<Integer> getAllRoomManager() {
        List<Integer> roomManagerList = new ArrayList<>();
        Connection dbConnection = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        String sql = "select user_id from room_manager";
        try {
            logger.debug("getAllRoomManager===>" + sql);
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next()) {
                int userId = rs.getInt("user_id");
                roomManagerList.add(userId);
            }
        } catch (Exception se) {
            logger.error("getAllRoomManager",se);
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }

        return roomManagerList;
    }
}
