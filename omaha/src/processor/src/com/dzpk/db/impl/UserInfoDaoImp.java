
package com.dzpk.db.impl;

import java.sql.*;
import java.util.*;
import java.util.Date;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.mq.rabbitmq.constant.EMessageCode;
import com.dzpk.db.model.*;
import com.dzpk.record.ReplayCacheImpl;
import com.i366.util.GsonUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.Logger;

import com.i366.model.pocer.Pocer;
import com.dzpk.component.repositories.mysql.dbpool.DBUtil;
import com.dzpk.db.dao.UserInfoDao;

/**
 * 用户信息数据库操作
 */
public class UserInfoDaoImp implements UserInfoDao {

    private static Logger logger = LogUtil.getLogger(UserInfoDaoImp.class);

    /**
     * 更新牌谱收藏个数
     *
     * @param userId
     * @param cnt
     */
    public void updateSpectrumCnt(int userId, int cnt) throws SQLException {
        Connection dbConnection = null;
        PreparedStatement ps = null;
        String updateStatement = "update user_details_info t set spectrum_cnt = " + cnt
                + "  where t.user_id = " + userId;
        try {
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(updateStatement);
            ps.execute();
        } catch (Exception se) {
            logger.error("", se);
            throw new SQLException("update user spectrum SQL Exception :" + se);
        } finally {
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }
    }


    private static String updateUserinfoSql = "UPDATE USER_DETAILS_INFO E " +
            "SET E.Win = ? ,E.LOSE = ? ,E.NUT_HAND = ? ,E.NUT_HAND_TYPE = ? ,E.HIGHEST_HAVE=? " +
            ",E.MAX_WIN=? ,E.GAME_INTEGRAL=?,E.MATCH_INTEGRAL=?,WEEKS_INTEGRAL = (WEEKS_INTEGRAL+?),MONTH_INTEGRAL = (MONTH_INTEGRAL + ?)" +
            " WHERE E.USER_ID = ?";

    public void updateUserInfo(UserInfo model) throws SQLException {

        Connection dbConnection = null;
        PreparedStatement ps = null;
        String insertStatement = updateUserinfoSql;

        try {
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(insertStatement);
            int i = 0;

            ps.setInt(++i, model.getWin());
            ps.setInt(++i, model.getLose());
            ps.setString(++i, model.getNutHand());
            ps.setInt(++i, model.getNutHandType());
            ps.setInt(++i, model.getHighestHave());
            ps.setInt(++i, model.getBiggestwinRate());
            ps.setInt(++i, model.getJifen1());
            ps.setInt(++i, model.getJifen2());
            ps.setInt(++i, model.getZoujifen());
            ps.setInt(++i, model.getYuejifen());
            ps.setInt(++i, model.getUserId());
            ps.execute();
        } catch (Exception se) {
            logger.error("updateUserInfo error", se);
            throw new SQLException("updateUserInfo SQL Exception :" + se);
        } finally {
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }

    }


    public UserInfo getUserInfo(int userId) throws SQLException {
        Connection dbConnection = null;
        PreparedStatement ps = null;
        ResultSet rs;
        String selectStatement = "SELECT u.USER_ID,NIKE_NAME,HEAD,SEX,WIN,LOSE,GAME_CNT,POOL_CNT,NUT_HAND,HIGHEST_HAVE,NUT_HAND_TYPE,MAX_WIN," +
                "GAME_INTEGRAL,MATCH_INTEGRAL,ADD_UP_COUNT,ADD_UP_TIME,WINNING_COUNT,RANDOM_NUM, " +
                "(a.chip + a.not_extract_chip) as chip,a.chip as extractChip,a.not_extract_chip as notExtractChip,a.pl_count as plCount " +
                "FROM USER_DETAILS_INFO u left join user_account a on u.USER_ID = a.user_id WHERE u.USER_ID =" + userId;
        UserInfo model = null;
        try {
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(selectStatement);
            rs = ps.executeQuery();
            if (rs.next()) {
                model = new UserInfo();
                model.setUserId(rs.getInt("USER_ID"));
                model.setNikeName(rs.getString("NIKE_NAME") == null ? "" : rs.getString("NIKE_NAME"));
                model.setHead(rs.getString("HEAD") == null ? "-1" : rs.getString("HEAD"));
                model.setSex(Integer.valueOf(rs.getInt("SEX")));
                model.setChip(rs.getInt("chip"));
                model.setExtractChip(rs.getInt("extractChip"));
                model.setNotExtractChip(rs.getInt("notExtractChip"));
                model.setPlCount(rs.getInt("plCount"));
                model.setWin(rs.getInt("WIN"));
                model.setLose(rs.getInt("LOSE"));
                model.setGameCnt(rs.getInt("GAME_CNT")); //总局数
                model.setPoolCnt(rs.getInt("POOL_CNT"));  //入池数
                model.setNutHand(rs.getString("NUT_HAND") == null ? "" : rs.getString("NUT_HAND"));
                model.setHighestHave(rs.getInt("HIGHEST_HAVE"));
                model.setNutHandType(rs.getInt("NUT_HAND_TYPE"));
                model.setBiggestwinRate(rs.getInt("MAX_WIN"));
                model.setJifen1(rs.getInt("GAME_INTEGRAL"));
                model.setJifen2(rs.getInt("MATCH_INTEGRAL"));
                model.setRandomNum(rs.getString("RANDOM_NUM"));
            }

            if (StringUtils.isNotBlank(model.getNutHand())) {
                model.setPocerType(model.getNutHandType());
                for (int i = 0; i < 5; i++) {
                    int a = Integer.valueOf(model.getNutHand().substring(i * 2, i * 2 + 2));
                    model.getPocer()[i] = new Pocer(a);
                }
            }
        } catch (Exception se) {
            logger.error("getUserInfo error,userid={},e={}",userId, se);
            throw new SQLException("SQL Exception :" + se);
        } finally {
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }
        return model;
    }


    @Override
    public int getUserMaxCollectNum(int userId) {
        Connection dbConnection = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        int maxCollectNum = ReplayCacheImpl.MAX_COLLECT_SIZE;
        Timestamp timestamp = new Timestamp(new Date().getTime());
        String sql = "select vip_config.priv_collect_spectrum_max_num,user_vip.end_date from user_vip,vip_config where user_vip.vip_code = vip_config.vip_code and user_id=" + userId + " and unix_timestamp(user_vip.end_date) >= unix_timestamp('" + timestamp.toString() + "')";
        try {
            logger.debug("getUserMaxCollectNum===>" + sql);
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(sql);
            rs = ps.executeQuery();
            if (rs.next()) {
                maxCollectNum = rs.getInt("priv_collect_spectrum_max_num");
            }
        } catch (Exception e) {
            logger.debug(e.getMessage());
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }
        return maxCollectNum;
    }

    @Override
    public Map<String, String> getMyRemarks(int userId, String friendIds) {
        Map<String, String> map = new HashMap<>();
        if (!friendIds.equals("")) {
            String sql = "select user_id, real_remark,remark_color from friend_relations where "
                    + "user_id in (" + friendIds + ") and friend_id =" + userId + " and real_remark != ''";
            Connection conn = null;
            PreparedStatement stam = null;
            ResultSet rs = null;
            try {
                conn = DBUtil.getConnection();
                stam = conn.prepareStatement(sql);
                rs = stam.executeQuery();
                while (rs.next()) {
                    int friendId = rs.getInt("user_id");
                    String remark = rs.getString("real_remark");
                    map.put(String.valueOf(friendId), remark);
                    map.put(friendId+"_color", String.valueOf(rs.getInt("remark_color")));
                }
            } catch (SQLException e) {
                logger.debug("sql:" + sql);
                logger.debug("get other remarks error: " + e);
            } finally {
                DBUtil.closeResultSet(rs);
                DBUtil.closeStatement(stam);
                DBUtil.closeConnection(conn);
            }
        }

        return map;
    }

    @Override
    public boolean checkUserIfFozen(int userId) throws SQLException {
        Connection dbConnection = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        String sql = "select forbid from user_basic_info where user_id = " + userId;
        try {
            logger.debug("checkUserIfFozen===>" + sql);
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(sql);
            rs = ps.executeQuery();
            if (rs.next()) {
                // 1正常登录  0禁止登陆  2封禁后解封或机器码误封
                int forbid = rs.getInt("forbid");
                return forbid == 0 ? true : false;
            }
        } catch (SQLException e) {
            logger.debug("sql:" + sql);
            logger.debug("checkUserIfFozen: " + e);
            throw new SQLException(e);
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }
        return false;
    }

    @Override
    public boolean checkUserAccountIfFozen(int userId) throws SQLException {
        Connection dbConnection = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        String sql = "select status from user_account where user_id = " + userId;
        try {
            logger.debug("checkUserAccountIfFozen===>" + sql);
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(sql);
            rs = ps.executeQuery();
            if (rs.next()) { //状态(1-正常,2-停用)
                int forbid = rs.getInt("status");
                return forbid == 2 ? true : false;
            }
        } catch (SQLException e) {
            logger.error("checkUserAccountIfFozen: " + e);
            throw new SQLException(e);
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }
        return false;
    }

    @Override
    public void insertUserAccountLog(Connection conn, UserAccountLog userAccountLog) throws SQLException{
        PreparedStatement stam = null;
        ResultSet rs = null;

        String insertPlatformAccountLogSql =
                "insert into user_account_log (user_id,type,change_source,current_chip,change_chip,curr_not_extract_chip,change_not_extract_chip," +
                        "curr_pl_count,change_pl_count,desction,external_id,op_id,created_time) values (?,?,?,?,?,?,?,?,?,?,?,?,?)";
        try {
            if(null == conn) {
                conn = DBUtil.getConnection();
            }
            stam = conn.prepareStatement(insertPlatformAccountLogSql, Statement.RETURN_GENERATED_KEYS);
            int i = 1;
            stam.setInt(i++, userAccountLog.getUserId());
            stam.setInt(i++, userAccountLog.getType());
            stam.setInt(i++,userAccountLog.getChangeSource());
            stam.setInt(i++, userAccountLog.getCurrentChip());
            stam.setInt(i++, userAccountLog.getChangeChip());
            stam.setInt(i++, userAccountLog.getNotExtractChip());
            stam.setInt(i++, userAccountLog.getChangeNotExtractChip());
            stam.setInt(i++, userAccountLog.getPlCount());
            stam.setInt(i++, userAccountLog.getChangePlCount());
            stam.setString(i++, userAccountLog.getDescription());
            stam.setString(i++, userAccountLog.getExternalId());
            stam.setInt(i++, userAccountLog.getOpId());
            stam.setTimestamp(i++,userAccountLog.getCreateTime());
            logger.debug("insertUserAccountLog===>" + stam.toString());

            stam.executeUpdate();
            rs = stam.getGeneratedKeys();

        } catch (Exception e) {
            logger.error("insertUserAccountLog error," + e.getMessage());
            throw new SQLException();
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(stam);
        }
    }

    @Override
    public int getClubByUserId(int userId) {
        Connection dbConnection = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        int userClubId = 0;
        String sql = "select user_id from club_members where club_id=(select club_id from club_members where user_id=" + userId + ") and type=1";
        try {
            logger.debug("getClubUserId===>" + sql);
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(sql);
            rs = ps.executeQuery();
            if (rs.next()) {
                userClubId = rs.getInt("user_id");
            }
        } catch (Exception e) {
            logger.debug(e.getMessage());
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }
        return userClubId;
    }

    @Override
    public int getClubByClubId(int clubId) {
        Connection dbConnection = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        int userClubId = 0;
        String sql = "select user_id from club_members where type=1 and club_id =" + clubId;
        try {
            logger.debug("getClubUserId===>" + sql);
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(sql);
            rs = ps.executeQuery();
            if (rs.next()) {
                userClubId = rs.getInt("user_id");
            }
        } catch (Exception e) {
            logger.debug(e.getMessage());
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }
        return userClubId;
    }

    /**
     * 添加带入的消息
     * @param clubId：俱乐部id
     * @param senderId：消息生成方userId
     * @param reciverId: 处理用户userId（俱乐部主）
     * @param userName：请求者的用户名
     * @param roomName：请求的房间名
     * @param chip：请求的筹码
     * @param roomId：房间id加入remark
     * @param roomPath：房间的类型加入remark
     */
    @Override
    public void saveRequestToBringInMessage(int clubId, int senderId, int reciverId, String userName, String roomName,
                                            int chip, int roomId, int roomPath, int bringIn) throws SQLException{
        PreparedStatement stam = null;
        ResultSet rs = null;
        Connection conn = null;
        String insertMessageSql =
                "insert into message_club_record (msg_id,club_id,sender_id,reciver_id,header,title,content,remark" +
                        ",type,msg_status,msg_status_type,create_time) values (?,?,?,?,?,?,?,?,?,?,?,?)";
        try {
            conn = DBUtil.getConnection();
            stam = conn.prepareStatement(insertMessageSql, Statement.RETURN_GENERATED_KEYS);
            int i = 1;
            String content=userName+"("+senderId+"),"+roomName+","+chip/100+","+bringIn/100;
            String remark = "roomId:"+roomId+",roomPath:"+roomPath;
            stam.setString(i++, UUID.randomUUID().toString());
            stam.setString(i++, String.valueOf(clubId));
            stam.setString(i++, String.valueOf(senderId));
            stam.setString(i++, String.valueOf(reciverId));
            stam.setString(i++, "-1");
            stam.setString(i++, "");
            stam.setString(i++, content);
            stam.setString(i++, remark);
            stam.setInt(i++, EMessageCode.CLUB_ROOM_REQUEST_TO_BRING_IN.getCode());
            stam.setInt(i++, 0);
            stam.setInt(i++, 0);
            stam.setTimestamp(i++,new Timestamp(new Date().getTime()));
            logger.debug("saveRequestToBringInMessage===>" + stam.toString());
            stam.executeUpdate();
            rs = stam.getGeneratedKeys();
            if (rs.next()) {
                try {
                    checkMssageRequestToBringInUnread(remark,content,reciverId);
                }catch (SQLException e){
                    logger.error("checkMssageRequestToBringInUnread error," + e.getMessage());
                }
            }  else {
                logger.error("插入失败");
            }
        } catch (Exception e) {
            logger.error("saveRequestToBringInMessage error," + e.getMessage());
            throw new SQLException();
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(stam);
        }
    }
    private static String SQL_ADD_CLUB_MESSAGE_UNREAD_RECORD =
            "UPDATE message_unread SET club_num = club_num+1,club_msg = ? WHERE user_id=?";
    private void checkMssageRequestToBringInUnread(String remark,String content, int clubId) throws SQLException {
//        RequestToBringInMessage message=new RequestToBringInMessage();
//        message.setId(messageId);
//        message.setChips(chips);
//        message.setUserId(senderId);
//        message.setRoomId(roomId);
        MessageUnreadDetailBo message=MessageUnreadDetailBo.builder().remark(remark)
                .content(content).time(System.currentTimeMillis()).type(17).build();
        PreparedStatement stam = null;
        ResultSet rs = null;
        Connection conn = null;
        try {
            conn = DBUtil.getConnection();
            stam = conn.prepareStatement(SQL_ADD_CLUB_MESSAGE_UNREAD_RECORD, Statement.RETURN_GENERATED_KEYS);
            int i = 0;
            stam.setString(++i, GsonUtil.getInstance().toJson(message));
            stam.setInt(++i, clubId);
            stam.executeUpdate();
        } catch (Exception e) {
            throw new SQLException();
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(stam);
        }
    }
    @Override
    public void updateUserAccount(Connection conn,int userId, int chip,int notExtractChip) throws SQLException{

        PreparedStatement ps = null;
        String insertStatement = "update user_account set chip= chip + ?, not_extract_chip= not_extract_chip + ? where user_id=?";

        try {
            if(null == conn) {
                conn = DBUtil.getConnection();
            }
            ps = conn.prepareStatement(insertStatement);
            int i = 0;

            ps.setInt(++i, chip);
            ps.setInt(++i, notExtractChip);
            ps.setInt(++i, userId);
            ps.execute();
        } catch (Exception se) {
            logger.error("updateUserAccount error", se);
            throw new SQLException("updateUserAccount SQL Exception :" + se);
        } finally {
            DBUtil.closeStatement(ps);
        }
    }

    @Override
    public void updateUserAccountWithCommission(Connection conn,int userId, int chip,int notExtractChip,int plAcount) throws SQLException {
        PreparedStatement ps = null;
        String insertStatement = "update user_account set chip= chip + ?, not_extract_chip= not_extract_chip + ? , pl_count = pl_count + ? where user_id=?";

        try {
            if(null == conn) {
                conn = DBUtil.getConnection();
            }
            ps = conn.prepareStatement(insertStatement);
            int i = 0;

            ps.setInt(++i, chip);
            ps.setInt(++i, notExtractChip);
            ps.setInt(++i, plAcount);
            ps.setInt(++i, userId);
            ps.execute();
        } catch (Exception se) {
            logger.error("updateUserAccountWithCommission error", se);
            throw new SQLException("updateUserAccountWithCommission SQL Exception :" + se);
        } finally {
            DBUtil.closeStatement(ps);
        }
    }

    @Override
    public UserAccount getUserAccountInfo(Connection conn,int userId) throws SQLException{

        PreparedStatement ps = null;
        ResultSet rs = null;

        String sql = "select chip,not_extract_chip,pl_count,lock_chip from user_account where user_id = " + userId;
        try {
            logger.debug("getUserAccountInfo===>" + sql);
            if(null == conn) {
                conn = DBUtil.getConnection();
            }
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            if (rs.next()) {
                int chip = rs.getInt("chip");
                int notExtractChip = rs.getInt("not_extract_chip");
                int plCount = rs.getInt("pl_count");
                int lockChip = rs.getInt("lock_chip");
                UserAccount userAccount = new UserAccount();
                userAccount.setUserId(userId);
                userAccount.setChip(chip);
                userAccount.setPlCount(plCount);
                userAccount.setLockChip(lockChip);
                userAccount.setNotExtractChip(notExtractChip);
                return userAccount;
            }
        } catch (SQLException e) {
            logger.error("getUserAccountInfo error," + e);
            throw new SQLException(e);
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(ps);
        }

        return null;
    }

    @Override
    public void updateUserAccountBeforeChange(Connection conn,int userId, long updateTime) throws SQLException{
        PreparedStatement ps = null;
        String insertStatement = "update user_account set updated_time = ? where user_id = ?";

        try {
            if(null == conn) {
                conn = DBUtil.getConnection();
            }
            ps = conn.prepareStatement(insertStatement);
            int i = 0;

            ps.setTimestamp(++i,new Timestamp(updateTime));
            ps.setInt(++i, userId);
            ps.execute();
        } catch (Exception se) {
            logger.error("updateUserAccountBeforeChange error", se);
            throw new SQLException("updateUserAccountBeforeChange SQL Exception :" + se);
        } finally {
            DBUtil.closeStatement(ps);
        }
    }

    /**
     * 根据用户id和俱乐部id查询数据
     *
     * @param conn
     * @param clubId 俱乐部id
     * @param userId 用户id
     * @return
     */
    @Override
    public int queryUserIntegral(Connection conn, int clubId, int userId) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "select * from club_members where club_id = " + clubId + " and user_id = " + userId;
        try {
            logger.debug("根据俱乐部id和用户id查询用户的积分数据===>" + sql);
            if (null == conn) {
                conn = DBUtil.getConnection();
            }
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            if (rs.next()) {
                int integral = rs.getInt("integral");
                int type = rs.getInt("type");
                if (type == 1) {
                    // 会长
                    return -1;
                }
                logger.info("查询到的积分数据位:{}", integral);
                return integral;
            }
            logger.info("该sql:{},未能查询到用户积分数据", sql);
        } catch (SQLException e) {
            e.printStackTrace();
            logger.error("根据俱乐部id和用户id查询用户的积分数据,出现异常，异常信息为," + e);
            return 0;
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(ps);
        }
        return 0;
    }

    @Override
    public int queryUserTribeChip(Connection conn, int clubId, int userId) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "select * from user_tribe_account where tribe_id = (select tribe_id from tribe_members " +
                "where club_id = ?) and user_id = " + userId;
        try {
            logger.debug("根据联盟id和用户id查询用户的积分数据===>" + sql);
            if (null == conn) {
                conn = DBUtil.getConnection();
            }
            ps = conn.prepareStatement(sql);
            ps.setInt(1,clubId);
            logger.debug("根据联盟id和用户id查询用户的积分数据===>{}" ,ps);
            rs = ps.executeQuery();
            if (rs.next()) {
                int integral = rs.getInt("chips");
                logger.info("查询到的积分数据位:{}", integral);
                return integral;
            }
            logger.info("该sql:{},未能查询到用户积分数据", sql);
        } catch (SQLException e) {
            e.printStackTrace();
            logger.error("根据俱乐部id和用户id查询用户的积分数据,出现异常，异常信息为," + e);
            return 0;
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(ps);
        }
        return 0;
    }
}