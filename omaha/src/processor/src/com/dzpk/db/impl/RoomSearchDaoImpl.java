package com.dzpk.db.impl;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.repositories.mysql.dbpool.DBUtil;
import com.dzpk.component.repositories.mysql.impl.DZPKDaoImp;
import com.dzpk.db.dao.RoomSearchDao;
import com.dzpk.db.model.RoomSearchInfo;
import org.apache.logging.log4j.Logger;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

public class RoomSearchDaoImpl implements RoomSearchDao {

    /** 日志服务 */
    private static Logger logger = LogUtil.getLogger(RoomSearchDaoImpl.class);

    @Override
    public int updateRoomSeat(int updateNum, int roomId) {
        if(updateNum==0){
            logger.debug("updateRoomSeat:roomid={},updateNum={} -> skipped !",roomId,updateNum);
            return 1;
        }

        int updatedRows = 0; // 更新行数
        Object nowEmptySeat = null; // 更新后的记录数量

        // 查询更新后的数量
        String selectSql = "select empty_seat from room_search where room_id =?";
        // 坐下座位更新，-1
        String downSql = "update room_search set empty_seat = empty_seat + ? where room_id=? and status!=0 and empty_seat>=?";
        // 离开座位更新，+1
        String upSql = "update room_search set empty_seat = empty_seat + ? where room_id=? and status!=0 and empty_seat+?<=player_count";
        try {
            DZPKDaoImp dao = new DZPKDaoImp();
            if(updateNum < 0){//坐下或占座
                int compareVal = Math.abs(updateNum);
                updatedRows = dao.updateOrDel(downSql, new Object[]{updateNum, roomId,compareVal});
            }else { // 离开座位
                updatedRows = dao.updateOrDel(upSql, new Object[]{updateNum, roomId,updateNum});
            }

            nowEmptySeat = dao.getSingleValue(selectSql,new Object[]{roomId});
            logger.debug("updateRoomSeat:roomid={},updateNum={} -> updatedRows={},emptySeatNumAfterUpdated={} !",
                    roomId,updateNum,updatedRows,nowEmptySeat);
        } catch (SQLException e) {
            logger.debug("updateRoomSeat:roomid={},updateNum={} -> exception occurred: {} !",roomId,updateNum,e.getMessage());
            return 0;
        }
        return 1;
    }

    @Override
    public int beginRoom(int roomId) {
        String sql = "update room_search set status=4, start_time=? where room_id=? and status!=0";
        try {
            DZPKDaoImp dao = new DZPKDaoImp();
            dao.operation(sql, new Object[]{new Timestamp(System.currentTimeMillis()), roomId});		// 更新房间状态
        } catch (SQLException e) {
            logger.debug("beginRoom sql error ", e);
            return 0;
        }
        return 1;
    }

    @Override
    public int closeRoom(int roomId) {
        String sql = "update room_search set status=0, updated_time=?,updated_item=? where room_id=? and status!=0";
        try {
            DZPKDaoImp dao = new DZPKDaoImp();
            dao.operation(sql, new Object[]{new Timestamp(System.currentTimeMillis()),"close", roomId});		// 更新房间时间
        } catch (SQLException e) {
            logger.debug("closeRoom sql error ", e);
            return 0;
        }
        return 1;
    }

    @Override
    public int delayRoomPlayTime(int roomId, int delayTime) {
        String sql = "update room_search set delay_sec = delay_sec + ? where room_id=? and status!=0";
        try {
            DZPKDaoImp dao = new DZPKDaoImp();
            dao.operation(sql, new Object[]{delayTime, roomId});		// 更新房间状态
        } catch (SQLException e) {
            logger.error("delayRoomPlayTime sql error ", e);
            return 0;
        }
        return 1;
    }

    @Override
    public void clearAllRoom(String roomServerNumber) throws SQLException {
        Connection dbConnection = null;
        PreparedStatement ps = null;
        String updateStatement = "delete from room_search where server_id = " + roomServerNumber;
        try {
            logger.debug("clearAllRoom===>" + updateStatement);
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(updateStatement);
            ps.execute();
        } catch (Exception se) {
            logger.error("clearAllRoom sql error ", se);
            throw new SQLException(se);
        } finally {
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }
    }

    @Override
    public List<RoomSearchInfo> getAllRoomByServerNumber(String roomServerNumber) throws SQLException {
        List<RoomSearchInfo> roomList = new ArrayList<>();
        Connection dbConnection = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        String sql = "select room_id,room_path from room_search where server_id = " + roomServerNumber + " and status !=0 ";
        try {
            logger.debug("getAllRoomByServerNumber===>" + sql);
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next()) {
                int roomId = rs.getInt("room_id");
                int roomPath = rs.getInt("room_path");

                RoomSearchInfo roomSearchInfo = new RoomSearchInfo();
                roomSearchInfo.setRoomId(roomId);
                roomSearchInfo.setRoomPath(roomPath);

                roomList.add(roomSearchInfo);
            }
        } catch (Exception se) {
            logger.error("getAllRoomByServerNumber",se);
            throw new SQLException(se);
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }

        return roomList;
    }


    /**
     * 查询自动开房计划创建的，符合自动解散条件的空房间。  （游戏开始30min后）
     *
     * @return
     */
    @Override
    public List<RoomSearchInfo> queryEmptyPlanRoomIds(int roomServerNumber) {
        List<RoomSearchInfo> roomList = new ArrayList<>();
        Connection dbConnection = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        String sql = "select t2.room_id, t2.room_path from plan_room_relations t1, room_search t2, room_creation_plan_config t3 " +
                " where t1.room_id = t2.room_id and t2.`status` = 4 and t2.server_id ="+ roomServerNumber +"  and t2.empty_seat = t2.player_count " +
                " and t2.start_time is not null and t3.id = t1.plan_id and t3.auto_dismiss = 1 and DATE_ADD(t2.start_time, interval 30 MINUTE) < now() ";
        try {
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next()) {
                int roomId = rs.getInt("room_id");
                int roomPath = rs.getInt("room_path");

                RoomSearchInfo roomSearchInfo = new RoomSearchInfo();
                roomSearchInfo.setRoomId(roomId);
                roomSearchInfo.setRoomPath(roomPath);

                roomList.add(roomSearchInfo);
            }
        } catch (Exception se) {
            logger.error("queryEmptyPlanRoomIds",se);
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }

        return roomList;
    }
}
