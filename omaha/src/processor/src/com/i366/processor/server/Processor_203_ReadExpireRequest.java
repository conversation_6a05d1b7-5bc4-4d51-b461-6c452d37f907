package com.i366.processor.server;

import com.dzpk.common.utils.LogUtil;
import com.work.comm.client.protocal.BaseRequest;
import com.work.comm.io.Handler;
import org.apache.logging.log4j.Logger;

/**
 * @deprecated
 *
 * 清理过期审批消息
 * 疯狂扑克中已去掉审批,该协议已废除,暂时保留
 */
public class Processor_203_ReadExpireRequest extends Handler {

    private Logger logger = LogUtil.getLogger(Processor_203_ReadExpireRequest.class);

    @Override
    public byte[] handleRequest(BaseRequest req) {
//        Request request = (Request) req;
//        int userId = request.getUserId();
//        logger.debug("Processor_203_ReadExpireRequest id " + userId);
//        int[][] int2 = {
//                {130, I366ServerPickUtil.TYPE_STRING_UTF16}    // read to message id
//        };
//        Map<Integer, Object> map = I366ServerPickUtil.pickAll(request.getBt(), int2);
//        String id = (String) map.get(130);
//        logger.debug("id:" + id);
//
//        int status = RoomRequest.removeExpire(id, userId);
//
//        Object[][] objs = {
//                {60, status, I366ServerPickUtil.TYPE_INT_1},
//                {130, id, I366ServerPickUtil.TYPE_STRING_UTF16}
//        };
//        byte[] bytes = I366ServerPickUtil.packAll(objs, Constant.REQ_REQUEST_READ);

        return null;
    }

}
