
package com.i366.processor.client;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.db.model.UserInfo;
import com.i366.cache.Cache;
import com.i366.constant.Constant;
import com.i366.room.RoomService;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.BaseRequest;
import com.work.comm.client.protocal.Request;
import com.work.comm.io.Handler;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * 心跳包处理
 * 客户端每5秒发送一次
 */
public class Processor_97_Xintiao extends Handler {

    private final Logger logger = LogUtil.getLogger(Processor_97_Xintiao.class);

    @Override
    public byte[] handleRequest(BaseRequest req) {
        Request request = (Request) req;
        logger.trace("Processor_97_Xintiao userId is : {}, channel is : {}", request.getUserId(), request.getChannel().remoteAddress());

        List<UserInfo> userInfoFound = Cache.filterOnlineUserInfo(request.getUserId(), userInfo -> userInfo.getChannel() == request.getChannel());

        if (userInfoFound.isEmpty()) {
            logger.warn("Received heartbeat from userId {}, but user is offline!", request.getUserId());
            return I366ClientPickUtil.packAll(new Object[][]{
                    {60, 1, I366ClientPickUtil.TYPE_INT_1}
            }, Constant.REQ_GAME_ROOM_XITIAO);
        }

        userInfoFound.get(0).setOnlineTime(System.currentTimeMillis());

        return I366ClientPickUtil.packAll(new Object[][]{
                {60, 0, I366ClientPickUtil.TYPE_INT_1}
        }, Constant.REQ_GAME_ROOM_XITIAO);
    }

}

