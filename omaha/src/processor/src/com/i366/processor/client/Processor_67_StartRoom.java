
package com.i366.processor.client;

import java.util.Map;

import com.dzpk.common.utils.LogUtil;
import com.i366.constant.Constant;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.BaseRequest;
import com.work.comm.client.protocal.Request;
import org.apache.logging.log4j.Logger;

import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;
import com.i366.room.RoomService;
import com.work.comm.io.Handler;

import com.dzpk.db.model.UserInfo;

/**
 * 房主点击牌局内开始游戏按钮
 */
public class Processor_67_StartRoom extends Handler {

    private final Logger logger = LogUtil.getLogger(Processor_67_StartRoom.class);

    @Override
    public byte[] handleRequest(BaseRequest req) {
        Request request = (Request) req;
        logger.debug("Processor_67_StartRoom id " + request.getUserId());
        int[][] int2 = {
                {130, I366ClientPickUtil.TYPE_INT_4},           // roomPath
                {131, I366ClientPickUtil.TYPE_INT_4}            // roomId
        };
        Map<Integer, Object> map = I366ClientPickUtil.pickAll(request.getBt(), int2);
        int roomPath = (Integer) map.get(130);
        int roomId = (Integer) map.get(131);

        RoomService.setUserChannel(request, roomId);

        logger.debug("roomId:" + roomId + " roomPath :" + roomPath);
        Task task = new Task(Constant.REQ_GAME_START_ROOM, map, request, roomId, roomPath);
        WorkThreadService.submit(roomId, task);
        return null;
    }

}

