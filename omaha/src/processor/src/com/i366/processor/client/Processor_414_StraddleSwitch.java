
package com.i366.processor.client;

import java.util.Map;

import com.dzpk.common.utils.LogUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.BaseRequest;
import com.work.comm.client.protocal.Request;
import org.apache.logging.log4j.Logger;

import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;
import com.i366.constant.Constant;
import com.work.comm.io.Handler;


/**
 * 是否开启straddle开关
 */
public class Processor_414_StraddleSwitch extends Handler {

    private Logger logger = LogUtil.getLogger(Processor_414_StraddleSwitch.class);

    @Override
    public byte[] handleRequest(BaseRequest req) {
        Request request = (Request) req;

        int[][] int2 = {
                { 131, I366ClientPickUtil.TYPE_INT_4 }, // roomId
                { 132, I366ClientPickUtil.TYPE_INT_4 }, // roomPath
                { 133, I366ClientPickUtil.TYPE_INT_4 }, // straddle 0关闭 1开启
        };
        Map<Integer, Object> map = I366ClientPickUtil.pickAll(request.getBt(), int2);
        int roomId = (Integer) map.get(131);
        int roomPath = (Integer) map.get(132);
        int straddle = (Integer) map.get(133);
        logger.debug("Processor_414_StraddleSwitch id: " + request.getUserId() + " straddle:" + straddle);

        Task task = new Task(Constant.REQ_ROOM_STRADDLE_SWITCH, map, request, roomId, roomPath);
        WorkThreadService.submit(roomId, task);
        return null;
    }

}
