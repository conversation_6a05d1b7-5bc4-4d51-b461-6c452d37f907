package com.i366.processor.client;

import java.util.Map;

import com.dzpk.common.utils.LogUtil;
import com.i366.constant.Constant;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.BaseRequest;
import com.work.comm.client.protocal.Request;
import org.apache.logging.log4j.Logger;

import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;
import com.work.comm.io.Handler;


/**
 * 玩家保险操作
 */
public class Processor_401_InsuranceRecieve extends Handler {
    private Logger logger = LogUtil.getLogger(Processor_401_InsuranceRecieve.class);

    public byte[] handleRequest(BaseRequest req) {
        Request request = (Request) req;
        int userId = request.getUserId();
        int[][] int2 = {
                {130, I366ClientPickUtil.TYPE_INT_4},           // room id
                {131, I366ClientPickUtil.TYPE_INT_4},           // room path
                {132, I366ClientPickUtil.TYPE_INT_4},           // 座位号
                {133, I366ClientPickUtil.TYPE_INT_4},           // 是否投保
                {134, I366ClientPickUtil.TYPE_INT_1_ARRAY},     // 投保下标(多个分池)
                {135, I366ClientPickUtil.TYPE_INT_4_ARRAY},     // 投保额(多个分池)
                {136, I366ClientPickUtil.TYPE_INT_4_ARRAY}      // 所选outs列表
        };
        Map<Integer, Object> map = I366ClientPickUtil.pickAll(request.getBt(), int2);
        int roomId = (Integer) map.get(130);
        int roomPath = (Integer) map.get(131);
        int seat = (Integer) map.get(132);
        int insuranceStatus = (Integer) map.get(133);

        logger.debug("userId:" + userId);
        logger.debug("seat:" + seat);
        logger.debug("insuranceStatus:" + insuranceStatus);

        Task task = new Task(Constant.REQ_INSURANCE_OPERATE, map, request, roomId, roomPath);
        WorkThreadService.submit(roomId, task);
        return null;
    }

}
