
package com.i366.processor.client;

import java.util.Map;

import com.dzpk.common.utils.LogUtil;
import com.i366.constant.Constant;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.BaseRequest;
import com.work.comm.client.protocal.Request;
import org.apache.logging.log4j.Logger;

import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;
import com.i366.room.RoomService;
import com.work.comm.io.Handler;

import com.dzpk.db.model.UserInfo;

/**
 * 玩家参与牌局方式：过庄还是补盲
 */
public class Processor_222_PlayType extends Handler {

    private final Logger logger = LogUtil.getLogger(Processor_222_PlayType.class);

    @Override
    public byte[] handleRequest(BaseRequest req) {
        Request request = (Request) req;
        logger.debug("Processor_222_PlayType id " + request.getUserId());
        int[][] int2 = {
                {130, I366ClientPickUtil.TYPE_INT_4},       // roomId
                {131, I366ClientPickUtil.TYPE_INT_4},       // roomPath
                {132, I366ClientPickUtil.TYPE_INT_1},       // 0 过庄 1补盲 2超时
        };
        Map<Integer, Object> map = I366ClientPickUtil.pickAll(request.getBt(), int2);
        int roomId = (Integer) map.get(130);
        int roomPath = (Integer) map.get(131);

        RoomService.setUserChannel(request, roomId);

        Task task = new Task(Constant.REQ_GAME_PLAY_TYPE, map, request, roomId, roomPath);
        WorkThreadService.submit(roomId, task);
        return null;
    }

}

