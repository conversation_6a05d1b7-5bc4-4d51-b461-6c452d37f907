package com.i366.util;

import com.dzpk.common.utils.LogUtil;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.BufferedHttpEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * Created by baidu on 16/6/6.
 */
public class HttpUtil {
    /** 日志服务 */
    private static Logger logger = LogUtil.getLogger(HttpUtil.class);

    /** HTTP调用连接池 */
    private static CloseableHttpClient httpClient = null;

    static {
        logger.info("HttpUtil init");
        initHttpClient();
    }
    private static synchronized CloseableHttpClient initHttpClient(){
        if(null == httpClient) {
            // connection管理器
            PoolingHttpClientConnectionManager connMgr = new PoolingHttpClientConnectionManager();
            connMgr.setValidateAfterInactivity(30 * 1000); // 30 秒
            connMgr.setDefaultMaxPerRoute(50);

            HttpClientBuilder builder = HttpClients.custom();
            builder.evictExpiredConnections()
                    // .evictIdleConnections(1, TimeUnit.MINUTES)
                    .setConnectionManager(connMgr);

            httpClient = builder.build();
        }

        return httpClient;
    }

    public static String post(String url, String content) throws IOException {
        HttpURLConnection connection = null;
        OutputStream ostream = null;
        BufferedReader reader = null;
        try {
            //logger.info("post:" + url + ", content:" + content);
            connection = (HttpURLConnection) new URL(url).openConnection();
            connection.setDoInput(true);
            connection.setDoOutput(true);
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);
            ostream = connection.getOutputStream();
            ostream.write(content.getBytes());
            ostream.flush();
            int respCode = connection.getResponseCode();
            if (respCode != HttpURLConnection.HTTP_OK) {
                throw new IOException("Post failed, HTTP CODE:" + respCode);
            }
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            StringBuffer resp = new StringBuffer(connection.getContentLength());
            String output;
            while ((output = reader.readLine()) != null) {
                resp.append(output);
            }
            String respStr = resp.toString();
            //logger.info("resp:" + respStr);

            return respStr;
        }finally {
            close(connection,reader,ostream);
        }
    }

    public static String TimPost(String url, String content) throws IOException {
        CloseableHttpResponse response = null;
        try {
            url = url.trim();
            HttpPost postReq = new HttpPost(url);

            //设置内容
            if(null != content && !"".equals(content.trim())){
                ContentType contentType = ContentType.create("application/json","UTF-8");
                StringEntity body = new StringEntity(content, contentType);
                postReq.setEntity(new BufferedHttpEntity(body));
            }

            response = httpClient.execute(postReq);
            //HTTP内容
            String responseContent = "";
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                responseContent = EntityUtils.toString(entity, "UTF-8");
            }

            return responseContent;
        } finally {
            if(null != response) {
                try {
                    response.close();
                }catch (Exception ex){
                    String msg = String.format("Close HttpResponse failed: %s -> %s",ex.getMessage(),url);
                    if(logger.isDebugEnabled())
                        logger.debug(msg,ex);
                    else
                        logger.warn(msg);
                }
            }
        }
    }

    /*public static String TimPost(String url, String content) throws IOException {
        HttpURLConnection connection = null;
        OutputStream ostream = null;
        BufferedReader reader = null;
        try {
            //logger.info("post:" + url + ", content:" + content);
            connection = (HttpURLConnection) new URL(url).openConnection();
            connection.setDoInput(true);
            connection.setDoOutput(true);
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            ostream = connection.getOutputStream();
            ostream.write(content.getBytes());
            ostream.flush();
            int respCode = connection.getResponseCode();
            if (respCode != HttpURLConnection.HTTP_OK) {
                throw new IOException("Post failed, HTTP CODE:" + respCode);
            }
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            StringBuffer resp = new StringBuffer(connection.getContentLength());
            String output;
            while ((output = reader.readLine()) != null) {
                resp.append(output);
            }
            String respStr = resp.toString();
            //logger.info("resp:" + respStr);

            return respStr;
        } finally {
            close(connection, reader, ostream);
        }
    }*/

    private static void close(HttpURLConnection connection, Closeable instream,Closeable outstream){
        try {
            if (null != outstream) {
                outstream.close();
            }
        }catch (Exception ex){
            logger.warn("关闭输出流对象失败：{}",ex.getMessage());
            if(logger.isDebugEnabled())
                logger.debug(ex);
        }

        try {
            if (null != instream) {
                instream.close();
            }
        }catch (Exception ex){
            logger.warn("关闭输入流对象失败：{}",ex.getMessage());
            if(logger.isDebugEnabled())
                logger.debug(ex);
        }

        try {
            if (null != connection) {
                connection.disconnect();
            }
        }catch (Exception ex){
            logger.warn("关闭HttpURLConnection对象失败：{}",ex.getMessage());
            if(logger.isDebugEnabled())
                logger.debug(ex);
        }
    }

    /**
     * 253短信请求方式
     * @param url
     * @param params
     * @return
     */
    public static String postMsg(String url, String params) {
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        try {
            httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);
            StringEntity sEntity = new StringEntity(params, "UTF-8");
            httpPost.setEntity(sEntity);
            response = httpClient.execute(httpPost);

            HttpEntity entity = response.getEntity();
            if (entity != null) {
                return EntityUtils.toString(entity, "UTF-8");
            }

            return null;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }finally {
            try {
                if(null != response) {
                    response.close();
                }
            } catch (Exception ex){
                logger.warn("关闭CloseableHttpResponse对象失败：{}",ex.getMessage());
                if(logger.isDebugEnabled())
                    logger.debug(ex);
            }

            try {
                if(null != httpClient) {
                    httpClient.close();
                }
            } catch (Exception ex){
                logger.warn("关闭CloseableHttpClient对象失败：{}",ex.getMessage());
                if(logger.isDebugEnabled())
                    logger.debug(ex);
            }
        }

    }

}
