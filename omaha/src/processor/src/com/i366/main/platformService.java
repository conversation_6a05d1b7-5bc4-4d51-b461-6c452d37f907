package com.i366.main;

import com.dzpk.common.config.ConfigUtil;
import com.dzpk.common.token.service.ZkCacheService;
import com.dzpk.common.utils.FileMonitorUtil;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.mq.rabbitmq.RabbitMQService;
import com.dzpk.component.repositories.redis.RedisService;
import com.dzpk.component.zk.ZkUtil;
import com.dzpk.db.dao.RoomDao;
import com.dzpk.db.dao.RoomSearchDao;
import com.dzpk.db.dao.UserInfoDao;
import com.dzpk.db.impl.RoomDaoImpl;
import com.dzpk.db.impl.RoomSearchDaoImpl;
import com.dzpk.db.impl.UserInfoDaoImp;
import com.dzpk.db.model.RequestToBringInMessage;
import com.dzpk.db.model.RoomSearchInfo;
import com.dzpk.db.model.UserInfo;
import com.dzpk.system.JackPotFundManage;
import com.dzpk.system.OnlineUserThread;
import com.dzpk.system.RoomThread;
import com.dzpk.system.RoomsManage;
import com.dzpk.work.Task;
import com.dzpk.work.TaskConstant;
import com.dzpk.work.WorkThreadService;
import com.i366.cache.Cache;
import com.i366.constant.Constant;
import com.i366.model.player.RoomPlayer;
import com.i366.model.room.Room;
import com.i366.room.RoomLoad;
import com.work.comm.client.protocal.Request;
import com.work.comm.endpoints.res.ResServerManager;
import com.work.comm.endpoints.room.RoomNodeManager;
import com.work.comm.io.Service;
import com.work.comm.s2s.client.AppConfig;
import com.work.comm.s2s.client.ServerManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.lang.management.ManagementFactory;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 启动类
 */
public class platformService {

    private static final Logger logger = LogUtil.getLogger(platformService.class);

    private static String pid ;

    public static String getPid()
    {
    	return pid;
    }

    public static void main(String[] args) {
        Service service = new Service();

        ServerManager.initialize(Cache.p);
        AppConfig appConfig = ServerManager.getInstance().getAppConfig();
        String roomServerNumber = appConfig.getAppId();

        RoomDao roomDao = new RoomDaoImpl();
        RoomSearchDao roomSearchDao = new RoomSearchDaoImpl();

        // 初始化Rabbitmq
        RabbitMQService.getInstance();

        /**
         * 重置group_room表中对应服务器上的房间状态
         * 清空redis中对应服务器的key
         * 清空room_rearch表中对应服务器上的房间
         */
        try {
            logger.debug("begin to clear roomServerNumber: " + roomServerNumber + " data");
            List<RoomSearchInfo> roomList = roomSearchDao.getAllRoomByServerNumber(appConfig.getAppId());
            if(null != roomList && !roomList.isEmpty()){
                logger.debug("need to clear room number: " + roomList.size());
                Set<Integer> roomIdSet = new HashSet<>();
                for(RoomSearchInfo roomSearchInfo:roomList){
                    roomIdSet.add(roomSearchInfo.getRoomId());
                }
                RedisService.getRedisService().clear(roomList);
                roomDao.resetRoom(roomIdSet);
                roomDao.resetUserJoinRoom(roomIdSet);
                roomSearchDao.clearAllRoom(appConfig.getAppId());
            }

        } catch (SQLException e) {
            logger.error("clear roomServerNumber={},sql error={}",roomServerNumber,e);
        } catch (Exception e) {
            logger.error("clear roomServerNumber={},redis error={}",roomServerNumber,e);
        }

        // 启动访问内部服务
        ResServerManager.initialize();
        RoomNodeManager.getInstance().initialize(ServerManager.getInstance().getAppConfig());
        Runtime.getRuntime().addShutdownHook(new Thread(){
            @Override
            public void run(){
                WorkThreadService.stop();
                ServerManager.getInstance().destroy();
                RoomNodeManager.getInstance().destroy();
                ZkUtil.getClient().close();
                RabbitMQService.getInstance().destroy();
                LogManager.shutdown();
            }
        });

        // 启动客户端监听端口
        service.start(Service.DZPK_SERVICE, Integer.parseInt(Cache.p.getProperty("socket.port")));

        //加载配置文件路径信息,运行过程不能修改
        RoomLoad rl = new RoomLoad();
        rl.loadRoomXML();

        //在线用户维护线程
        OnlineUserThread outThread = new OnlineUserThread();
        outThread.start();

        //強制踢人/站起
        RoomThread roomThread = new RoomThread();
        roomThread.start();

        //检查是否需要关闭牌局
        RoomsManage.init(Integer.parseInt(roomServerNumber));

        //初始化配置信息
        ConfigUtil.initConfig();

        //获取进程id
        setPid();

        //启动彩池监听
        JackPotFundManage.init();

        //初始化zk配置
        ZkCacheService.getInstance();

        //监听配置文件是否改动
        FileMonitorUtil.addMonitor(System.getProperty("user.dir"));

        logger.info("init room service success");

        //TODO 监听请求带入的需求；后期想办法换MQ
        WorkThreadService.schedulePool.scheduleAtFixedRate(new RequestToBringInMessageHandler(), 0, 2000, TimeUnit.MILLISECONDS);
    }
    
    private static void setPid()
    {
    	String fullPid = ManagementFactory.getRuntimeMXBean().getName();
    	pid = fullPid.split("@")[0];
    }

    static class RequestToBringInMessageHandler implements Runnable {

        @Override
        public void run() {
            try {
                handle();
            } catch (Throwable t) {
                logger.error("RequestToBringInMessageHandler error", t);
            }
        }

        private void handle() {
            RoomDao roomDao = new RoomDaoImpl();
            List<RequestToBringInMessage> requestToBringInMessage = roomDao.getRequestToBringInMessage();
//                logger.info("是否有消息："+requestToBringInMessage.size());
            for (RequestToBringInMessage message: requestToBringInMessage) {
                Room room = Cache.getRoom(message.getRoomId(), 91);
                if (room!=null){
                    int userId = message.getUserId();
                    int chips = message.getChips();
                    if (message.isMessageType()){
                        room.getRequestToBrind().add(userId);
                        logger.info("roomMessage=>获得消息");
                        RoomPlayer roomPlayer = room.getRoomPlayers().get(userId);
                        UserInfo userInfo = room.getAudMap().get(userId).getUserInfo();
                        roomPlayer.setRequestToBringInTimes(0);
                        Map<Integer, Object> map1 = new HashMap<>();
                        map1.put(60,roomPlayer.getSeat());
                        map1.put(61,1);
                        map1.put(62,String.valueOf(userInfo.getLongitude()));
                        map1.put(63,String.valueOf(userInfo.getLatitude()));
                        map1.put(64,String.valueOf(userInfo.getIp()));
                        map1.put(130,chips * 100);
                        map1.put(131,room.getRoomPath());
                        map1.put(132,room.getRoomId());
                        map1.put(134,0);
                        map1.put(136,userInfo.getImei());
                        map1.put(137,null);
                        map1.put(138,userInfo.getIsVirtual());
                        map1.put(139,"");
                        Request request = new Request();
                        request.setChannel(userInfo.getChannel());
                        request.setUserId(userId);
                        Task task = new Task(Constant.REQ_GAME_RECV_ADD_CHIPS, map1, request, room.getRoomId(), room.getRoomPath());
                        WorkThreadService.submit(room.getRoomId(), task);
                        UserInfoDao userInfoDao = new UserInfoDaoImp();
                        //俱乐部主id
                        int clubUserId = userInfoDao.getClubByClubId(room.getClubId());
                        roomDao.deleteMessage(message.getId(), clubUserId);
                    }else {
                        //处理拒绝请求
                        logger.info("roomMessage=>获得拒绝消息");
                        RoomPlayer roomPlayer = room.getRoomPlayers().get(userId);
                        if (null != roomPlayer) {
                            Map<Integer, Object> map = new HashMap<Integer, Object>();
                            map.put(1, userId);
                            map.put(2, roomPlayer.getSeat());
                            Task task = new Task(TaskConstant.TASK_OCCUPY_SEAT, map, room.getRoomId(), room.getRoomPath());
                            // 设置该玩家最新的有效任务id，让该玩家的其他10018任务失效
                            task.setId(roomPlayer.getValidTask10018Id());
                            WorkThreadService.submit(room.getRoomId(), task);
                            UserInfoDao userInfoDao = new UserInfoDaoImp();
                            //俱乐部主id
                            int clubUserId = userInfoDao.getClubByClubId(room.getClubId());
                            roomDao.deleteMessage(message.getId(), clubUserId);
                        }
                    }
                }
            }
        }
    }
}
