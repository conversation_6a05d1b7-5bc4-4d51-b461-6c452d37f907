package com.work.comm.server.protocal;

import com.i366.cache.Cache;

/**
 * 内部通讯协议
 */
public class Protocal {

	public static final byte HEADER_INDICATER_0 = 'D';
	public static final byte HEADER_INDICATER_1 = 'Z';
	public static final byte HEADER_INDICATER_2 = 'P';
	public static final byte HEADER_INDICATER_3 = 'K';

	/**
	 * ************************** Client Request Package ******************************
	 */

	public static final int SRP_PACKE_LEVEL = 6;

	public static final int SRP_REQUEST_HIGH = 4;
	public static final int SRP_REQUEST_LOW = 5;

	public static final int SRP_SIZE_HIGH = 7;
	public static final int SRP_SIZE_LOW = 8;
	/**
	 * ************************** Server Response Package ****************************************
	 */
    public static final int RP_PROTOCOL_VERSION = 4;
    
    public static final int RP_LANGUAGE_ID = 5;
    
    public static final int RP_MAIN_VERSION = 6;
    public static final int RP_MINOR_VERSION = 7;
    
    public static final int RP_CLIENT_BUILD_LANGUGAD = 8;
    public static final int RP_CLIENT_BUILD_NUMBER = 9;
    
    public static final int RP_SERVER_NUMBER_1 = 10;
    public static final int RP_SERVER_NUMBER_2 = 11;
    public static final int RP_SERVER_NUMBER_3 = 12;
    public static final int RP_SERVER_NUMBER_4 = 13;
    
    public static final int RP_PRODUCT_ID_HIGH = 14;
    public static final int RP_PRODUCT_ID_LOW = 15;
    
    public static final int RP_REQUEST_CODE_HIGH = 16;
    public static final int RP_REQUEST_CODE_LOW = 17;
    
    public static final int RP_SIZE_HIGH = 18;
    public static final int RP_SIZE_LOW = 19;

    //客户端协议头相关信息
    public final static byte PROTOCOL_VERSION = (byte) 0;
    public final static byte LANGUAGE_ID = (byte) 1;
    public final static byte MAIN_VERSION = (byte) 1;
    public final static byte MINOR_VERSION = (byte) 1;
    public final static byte CLIENT_BUILD_LANGUAG_ID = (byte) 1;
    public final static byte CLIENT_BUILD_NUMBER = (byte) 1;
    public final static int SERVER_NUMBER = Integer.valueOf(Cache.p.getProperty("service.name"));
    public final static int PRODUCT_ID = 1; //1 loginService 2 roomService 3 resServer 4coreServer

}
