/**
 * $RCSfile: ServicePesponse.java,v $
 * $Revision: 1.1  $
 * $Date: 2012-8-9  $
 * <p/>
 * Copyright (C) 2005 Bettem, Inc. All rights reserved.
 * <p/>
 * This software is the proprietary information of Bettem, Inc.
 * Use is subject to license terms.
 */
package com.work.comm.server.protocal;

/**
 * <p>Title: ServicePesponse</p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2006</p>
 * <AUTHOR>
 * @version 1.0
 */
public class ServiceProtocal {
    public static final byte HEADER_INDICATER_0 = 'D';
    public static final byte HEADER_INDICATER_1 = 'Z';
    public static final byte HEADER_INDICATER_2 = 'P';
    public static final byte HEADER_INDICATER_3 = 'K';

    /**
     * ************************** Client Request Package ******************************
     */
    public static final int RP_PROTOCOL_VERSION = 4;

    public static final int RP_LANGUGAD = 5;

    public static final int RP_MAIN_VERSION = 6;
    public static final int RP_MINOR_VERSION = 7;

    public static final int RP_CLIENT_BUILD_LANGUGAD = 8;
    public static final int RP_CLIENT_BUILD_NUMBER = 9;

    public static final int RP_SERVER_NUMBER_1 = 10;
    public static final int RP_SERVER_NUMBER_2 = 11;
    public static final int RP_SERVER_NUMBER_3 = 12;
    public static final int RP_SERVER_NUMBER_4 = 13;

    public static final int RP_PRODUCT_ID_HIGH = 14;
    public static final int RP_PRODUCT_ID_LOW = 15;

    public static final int RP_REQUEST_CODE_HIGH = 16;
    public static final int RP_REQUEST_CODE_LOW = 17;

    public static final int RP_SIZE_HIGH = 18;
    public static final int RP_SIZE_LOW = 19;


    /**
     * ************************** Server Response Package ****************************************
     */
    //  Server Response Package Header
    public static final int SRP_TYPE = 4;

//    public static final int SRP_SIZE_HIGH = 5;
//    public static final int SRP_SIZE_LOW = 6;

    public static final int SRP_FEE_NEXT_YEAR = 20;
    public static final int SRP_FEE_NEXT_MONTH = 21;
    public static final int SRP_FEE_NEXT_DATE = 22;

    public static final int SRP_DATA_FEE_DATA_SIZE = 31;
    public static final int SRP_DATA_FEE_PAGE_DATA_SIZE = 33;

    public static final int SRP_DATA_FEE_FILE_SIZE = 35;
    public static final int SRP_DATA_START = 51;

    public static final int SRP_PACKE_LEVEL = 6;

    public static final int SRP_REQUEST_HIGH = 4;
    public static final int SRP_REQUEST_LOW = 5;

    public static final int SRP_SIZE_HIGH = 7;
    public static final int SRP_SIZE_LOW = 8;
    /**
     * ***************************** INPUTSTRING *******************************************************
     *
     */
    public static final String IMSI_CARD = "imsi=";

    public static final String SMS_CENTER_NUMBER = "sms_sc=";

    public static final String SMS_NUMBER = "sms_num=";

    public static final String SMS_CONTENT = "sms_cnt=";

    public static final String SMS_SEND_UP = "sms_up=";

    public static final String SMS_SEND_UP_SP = "sp=";

    public static final String SMS_SEND_UP_COUNT = "count=";

    public static final String SMS_SEND_UP_CONTENT = "content=";

    /**
     * ******************************* max fee size**************************************************
     */
    public static final int MAX_FEE = 8;

    /**
     * ******************************* secondType return **************************************************
     */
    public static final int SECOND_TYPE_NORMAL = 1;

    public static final String SECOND_TYPE_NORMAL_RETURN = "是";

    public static final int SECOND_TYPE_CHANGE = 2;
}

