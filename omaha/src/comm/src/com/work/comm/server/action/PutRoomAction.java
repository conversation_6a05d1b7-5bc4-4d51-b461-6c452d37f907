/*
 * $RCSfile: PutRoomAction.java,v $
 * $Revision: 1.1  $
 * $Date: 2012-8-22  $
 *
 * Copyright (C) 2005 Bettem, Inc. All rights reserved.
 *
 * This software is the proprietary information of Bettem, Inc.
 * Use is subject to license terms.
 */
package com.work.comm.server.action;


import com.work.comm.server.protocal.ServerRequest;
import com.work.comm.server.protocal.ServerResponse;

/**
 * <p>Title: PutRoomAction</p> 
 * <p>Description: </p> 
 * <p>Copyright: Copyright (c) 2006</p> 
 * <AUTHOR>
 * @version 1.0
 */
public class PutRoomAction extends ClientAction {

    @Override
    public ServerResponse action(ServerRequest request, ServerResponse response) {
        return null;
    }
}

