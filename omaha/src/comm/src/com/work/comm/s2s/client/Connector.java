package com.work.comm.s2s.client;

import com.work.comm.s2s.common.*;
import com.work.comm.s2s.common.matcher.ChannelIpPortMatcherImpl;
import com.work.comm.s2s.common.matcher.IExChannelMatcher;
import com.work.comm.s2s.handler.S2ChannelInitializer;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.*;
import io.netty.channel.group.ChannelGroup;
import io.netty.channel.group.DefaultChannelGroup;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.util.Attribute;
import io.netty.util.concurrent.GenericFutureListener;
import io.netty.util.concurrent.ScheduledFuture;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 每台应用服务对应一个实例
 *
 * 功能：打开连接/重连的功能
 */
@Slf4j
public class Connector{
    /** 服务访问的地址 */
    private String accessIp;
    private int accessPort;

    /** 停止标志 */
    private boolean isDestroy = false;
    public void destroy(){
        if(this.isDestroy)
            return;

        this.isDestroy = true;
        if(null != this.channelPoolMonitorScheduledFuture){
            ScheduledFuture<?> future = this.channelPoolMonitorScheduledFuture;
            this.channelPoolMonitorScheduledFuture = null;
            if(future.isCancellable() && !future.isDone())
                future.cancel(true);
        }

        if(null != this.channelGroup)
            this.channelGroup.close();

        log.info("服务器[{}:{}]连接器已经销毁！",this.accessIp,this.accessPort);
    }

    // 已准备好的Channel
    // Channel关闭会自动从此集合中移除
    private ChannelGroup channelGroup = null;

    // 同时建立的连接数
    // 必须大于或等于1
    // 默认：2
    private int poolCount=2;
    // 池监控时间间隔,单位：秒
    // 重启生效
    private int poolCheckIntervalSec = 2;

    // 服务器连接配置
    //private ServerConfig serverConfig=null;

    // 启动器
    private Bootstrap bootstrap = null;

    // IO线程组
    // 外部传入
    private EventLoopGroup workerGroup=null;

    // channel初始化Handler
    // 外部传入
    private S2ChannelInitializer channelInitializer = null;

    // 到本服务的Channel匹配器
    private IExChannelMatcher ipPortMatcher = null;

    // 当前已建立的channel数量
    private AtomicBoolean isOpening = new AtomicBoolean(false);
    private AtomicInteger activeChannelNum = new AtomicInteger(0);
    private ChannelOpenedHandler channelOpenedHandler = new ChannelOpenedHandler();
    private int changeActiveChannelNum(int num){
        int currentNum = Connector.this.activeChannelNum.addAndGet(num);
        return currentNum;
    }

    // 连接池监控定时器
    private ChannelPoolMonitor channelPoolMonitor = null;
    private ScheduledFuture<?> channelPoolMonitorScheduledFuture = null;
    private void setupMonitorTask(){
        Throwable exception = null;
        try {
            this.channelPoolMonitorScheduledFuture = this.workerGroup
                    .next().schedule(this.channelPoolMonitor,
                            this.poolCheckIntervalSec, TimeUnit.SECONDS);
        }catch (Exception ex){
            exception = ex;
        }finally {
            if(null != exception){
               log.error(String.format("设置服务器[ %s:%s ]连接池监控器失败：%s",
                       this.accessIp,this.accessPort,exception.getMessage()),exception);
            }else {
                log.debug(String.format("成功设置服务器[ %s:%s ]连接池监控器!",
                        this.accessIp, this.accessPort));
            }
        }
    }

    // 连接关闭监控器
    // 用于触发重连
    private ChannelClosureMonitor closureMonitor = null;

    public Connector(String accessIp,int accessPort,
                     int poolCount,int poolCheckIntervalSec,
                     EventLoopGroup workerGroup,
                     S2ChannelInitializer channelInitializer){
        this.accessIp = accessIp;
        this.accessPort = accessPort;
        this.poolCount = poolCount;
        this.poolCheckIntervalSec = poolCheckIntervalSec;
        this.workerGroup = workerGroup;
        this.channelInitializer = channelInitializer;

        this.channelGroup = new DefaultChannelGroup(this.workerGroup.next());

        this.bootstrap = new Bootstrap();
        this.bootstrap.group(this.workerGroup);
        this.bootstrap.channel(NioSocketChannel.class);
        this.bootstrap.option(ChannelOption.SO_KEEPALIVE, true);
        this.bootstrap.handler(this.channelInitializer);

        this.ipPortMatcher = new ChannelIpPortMatcherImpl(this.accessIp,this.accessPort);

        this.channelPoolMonitor = new ChannelPoolMonitor();
        this.setupMonitorTask();

        this.closureMonitor = new ChannelClosureMonitor();
    }

    private void checkAndOpenChannel(){
        if(this.isDestroy){
            log.warn("服务器[{}:{}]正在销毁中!!",this.accessIp,this.accessPort);
            return;
        }
        int allowMaxNum = this.poolCount;
        if(this.activeChannelNum.get()<allowMaxNum){
            boolean isOpening = this.isOpening.getAndSet(true);
            try {
                if(isOpening)
                    return;

                ChannelFuture future = bootstrap.connect(this.accessIp.trim(), this.accessPort);
                future.addListener(channelOpenedHandler);
            }catch (Exception ex){
                this.isOpening.set(false);
                log.error(String.format("尝试连接服务器[%s:%s]失败：%s",
                        this.accessIp,this.accessPort,ex.getMessage()),ex);
            }
        }
        this.setupMonitorTask();
    }
    private void channelOpened(ChannelFuture future){
        future.removeListener(this.channelOpenedHandler);
        try{
            if(future.isSuccess()) {
                int currentNum = this.changeActiveChannelNum(1);
                Channel ch = future.channel();
                ch.closeFuture().addListener(this.closureMonitor);

                // 新连接添加属性
                this.ipPortMatcher.addAttr(ch);

                log.info("CHANNEL-OPENED:ID={},C-ADDRESS={},L-ADDRESS={} -> TIME={} -> activeNum={}",
                        ch.id(),ch.remoteAddress(),ch.localAddress(),new Date(),currentNum);
            }else{
                String errMsg = "";
                if(future.isCancelled())
                    errMsg = "Has been Cancelled";
                if(null != future.cause()) {
                    if (future.isCancelled()) errMsg += ": ";
                    errMsg += future.cause().getMessage();
                }

                String msg = String.format("尝试连接服务器[%s:%s]不成功：%s",
                        this.accessIp,this.accessPort,errMsg);
                log.error(msg,future.cause());
            }
        }catch (Exception ex){
            log.error(String.format("尝试连接服务器[%s:%s]失败：%s",
                    this.accessIp,this.accessPort,ex.getMessage()),ex);
        }finally {
            Connector.this.isOpening.set(false);
        }
    }

    private class ChannelPoolMonitor implements Runnable{
        public void run(){
            Connector.this.checkAndOpenChannel();
        }
    }
    private class ChannelOpenedHandler implements GenericFutureListener<ChannelFuture>{
        public void operationComplete(ChannelFuture future) throws Exception{
            Connector.this.channelOpened(future);
        }
    }
    private class ChannelClosureMonitor implements GenericFutureListener<ChannelFuture>{
        public void operationComplete(ChannelFuture f) throws Exception{
            int activeNum = Connector.this.changeActiveChannelNum(-1);
            Channel ch = f.channel();
            Attribute<Long> batch = ch.attr(ChannelAttributeKey.configBatchNumKey);
            Attribute<Integer> counter = ch.attr(ChannelAttributeKey.readIdleCounterKey);
            Attribute<String> appType = ch.attr(ChannelAttributeKey.typeKey);
            Attribute<String> appId = ch.attr(ChannelAttributeKey.idKey);

            log.info("CHANNEL-CLOSED:ID={},R-ADDRESS={},L-ADDRESS={} -> appType={},appId={} -> TIME={} -> ACTIVE-NUM={},BATCH-NUM={},COUNTER={}",
                    ch.id(),ch.remoteAddress(),ch.localAddress(),
                    appType.get(),appId.get(),new Date(),
                    activeNum,batch.get(),counter.get());
        }
    }
}
