package com.work.comm.s2s.handler;

import com.work.comm.s2s.common.IChannelEvent;
import io.netty.channel.ChannelDuplexHandler;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@ChannelHandler.Sharable
public class ChannelManageHandler extends ChannelDuplexHandler {
    private IChannelEvent channelEvent;
    public ChannelManageHandler(IChannelEvent channelEvent){
        this.channelEvent = channelEvent;
    }

    /**
     * 功能：完成连接建立后，添加到连接池
     * @param ctx          完成握手的连接
     * @throws Exception
     */
    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        if(null != this.channelEvent)
            this.channelEvent.active(ctx);
        ctx.fireChannelActive();
    }

    /**
     * 功能：处理readTimeout时间
     * @param ctx  触发timeout的Channel
     * @param evt  IdleStateEvent
     * @throws Exception
     */
    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if(null != this.channelEvent) {
            IdleStateEvent event;
            if ((evt instanceof IdleStateEvent) &&
                    (event = (IdleStateEvent) evt).state() == IdleState.READER_IDLE.READER_IDLE) {
                this.channelEvent.readIdle(ctx.channel(), event.isFirst());
                return;
            }
        }

        ctx.fireUserEventTriggered(evt);
    }
}
