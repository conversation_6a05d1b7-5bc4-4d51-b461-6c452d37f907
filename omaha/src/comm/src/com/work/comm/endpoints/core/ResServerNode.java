package com.work.comm.endpoints.core;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ResServerNode {
    private String serverId;
    /** 客户端的访问地址 */
    // IP
    private String outAccessIp;
    // port
    private int outAccessPort=0;

    /** 服务之间的访问地址 */
    // IP
    private String inAccessIp;
    // port
    private int inAccessPort=0;

    // 客户端连接数
    private int clientConnNum;

    /**
     * 从core收到上报数据的最后最后流水号
     * 当所有的连接都已经断开时，则清零流水号
     */
    private int seqNoStartTime = 0;
    private int seqNo = 0;

    public boolean update(int seqNoStartTime,int seqNo,ResServerNode newInfo){
        boolean updated = false;
        if(null == newInfo)
            return updated;
        if(seqNoStartTime<=this.seqNoStartTime &&
                seqNo<=this.seqNo)
            return updated;

        if(newInfo.getOutAccessPort()!= this.outAccessPort){
            updated = true;
            this.outAccessPort = newInfo.getOutAccessPort();
        }
        if(newInfo.getInAccessPort()!= this.inAccessPort) {
            updated = true;
            this.inAccessPort = newInfo.getInAccessPort();
        }
        boolean same = this.stringEqual(this.outAccessIp,newInfo.getOutAccessIp());
        if(!same){
            updated = true;
            this.outAccessIp = null == newInfo.getOutAccessIp()?"":newInfo.getOutAccessIp().trim();
        }
        same = this.stringEqual(this.inAccessIp,newInfo.getInAccessIp());
        if(!same){
            updated = true;
            this.inAccessIp = null == newInfo.getInAccessIp()?"":newInfo.getInAccessIp().trim();
        }
        if(newInfo.getClientConnNum() != this.clientConnNum) {
            updated = true;
            this.clientConnNum = newInfo.getClientConnNum() < 0 ? 0 : newInfo.getClientConnNum();
        }

        this.seqNo = seqNo;
        this.seqNoStartTime = seqNoStartTime;
        return updated;
    }
    private boolean stringEqual(String old,String info){
        old = null == old?"":old.trim();
        info = null ==info?"":info.trim();
        return old.equals(info);
    }

    public boolean checkAndAdjust(){
        boolean valid = false;

        if(this.inAccessPort<=0 && this.outAccessPort<=0)
            return valid;

        if(this.inAccessPort>0){
            this.inAccessIp = null == this.inAccessIp?"":this.inAccessIp.trim();
            if("".equals(this.inAccessIp))
                return valid;
        }

        this.seqNo = this.seqNo<0?0:this.seqNo;
        this.seqNoStartTime = this.seqNoStartTime<0?0:this.seqNoStartTime;
        this.clientConnNum = this.clientConnNum<0?0:this.clientConnNum;
        this.outAccessIp = null == this.outAccessIp?"":this.outAccessIp.trim();
        valid = true;

        return valid;
    }
}
