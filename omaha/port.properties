## 服务类型
service.type=${service.type}
## 服务器编号
## omaha从4001000开始
service.name=${service.name}

## 客户端访问端口
socket.port=${socket.port}
## 客户端访问IP/DNS
socket.ip=${socket.ip}

## 连接其它内部服务的配置
server.list=${server.list}
client.maxWorkerThreadNum=${client.maxWorkerThreadNum}
client.channel.pool.maxNum=${client.channel.pool.maxNum}
client.channel.pool.checkInterval=${client.channel.pool.checkInterval}
client.channel.readIdleTimeout=${client.channel.readIdleTimeout}
client.channel.readIdleMaxNum=${client.channel.readIdleMaxNum}
client.auth.timeout=${client.auth.timeout}
client.netty.logLevel=${client.netty.logLevel}

##定时读取配置文件间隔时间
load.room.space.time=${load.room.space.time}

#redis ip
redis.ip=${redis.ip}
##redis port
redis.port=${redis.port}
## redis password
redis.pwd=${redis.pwd}
## timeout(ms),default:5000
redis.timeout=${redis.timeout}

##mongo ip
mongodb.url=${mongodb.url}
mongodb.database=${mongodb.database}

cards.control=${cards.control}

#配置文件的ip限制开关  如果为0则是以房间为准，为1则ip限制失效
ip.limit = ${ip.limit}
#服务费的百分比 用带入记分牌*百分比即为服务费所需要的钻石数
service.fee=${service.fee}

##rabbitmq config
rabbitmq.host=${rabbitmq.host}
rabbitmq.user=${rabbitmq.user}
rabbitmq.password=${rabbitmq.password}
rabbitmq.port=${rabbitmq.port}

# ActiveMQ
activemq.url=${activemq.url}
activemq.userName=${activemq.userName}
activemq.password=${activemq.password}