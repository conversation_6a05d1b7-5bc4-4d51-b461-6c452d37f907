package com.dzpk.jackpot.repositories;

import com.dzpk.jackpot.repositories.mongodb.IJackpotRewardDao;
import com.dzpk.jackpot.repositories.mongodb.impl.JackpotRewardDaoImpl;
import com.dzpk.jackpot.repositories.mysql.IRoomJackpotDao;
import com.dzpk.jackpot.repositories.mysql.impl.RoomJackpotDaoImpl;

public class RepositoriesFactory {
    public static IRoomJackpotDao getRoomJackpotDao(){
        return new RoomJackpotDaoImpl();
    }
    public static IJackpotRewardDao getJackpotRewardDao(){
        return new JackpotRewardDaoImpl();
    }
}
