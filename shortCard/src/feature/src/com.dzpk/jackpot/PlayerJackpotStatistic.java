package com.dzpk.jackpot;

public class PlayerJackpotStatistic {
    private int userId;
    private String nickName;

    //玩家当前局的应投彩累计值
    private int totalJpBet = 0;
    //玩家当前局的实际投彩累计值
    private int totalRealJpBet = 0;

    //玩家当前局的JP奖励累计值
    private int totalJpReward = 0;
    //玩家当前局的JP奖励服务费累计值
    private double totalJpRewardFee = 0;

    public PlayerJackpotStatistic(int userId,String nickName){
        this.userId = userId;
        this.nickName = nickName;
    }

    /** 以下字段，每手开始前重置 */
    //玩家当前手的应投彩
    private int jpBetPerHand = 0;
    //玩家当前手的实际投彩
    private int realJpBetPerHand = 0;

    //玩家当前手的JP奖励
    private int jpRewardPerHand = 0;
    //玩家当前手的JP奖励服务费
    private double jpRewardFeePerHand = 0;

    public void addJackpotBet(int jpBet,int realJpBet){
        this.jpBetPerHand = jpBet;
        this.realJpBetPerHand = realJpBet;

        this.totalJpBet += this.jpBetPerHand;
        this.totalRealJpBet += this.realJpBetPerHand;
    }

    public void addJackpotReward(int jpReward,double jpRewardFee){
        this.jpRewardPerHand = jpReward;
        this.jpRewardFeePerHand = jpRewardFee;

        this.totalJpReward += this.jpRewardPerHand;
        this.totalJpRewardFee += this.jpRewardFeePerHand;
    }

    /**
     * 牌局每手开始前，调用
     */
    public void resetPerHand(){
        jpBetPerHand = 0;
        realJpBetPerHand = 0;
        jpRewardPerHand = 0;
        jpRewardFeePerHand = 0;
    }

    public int getUserId() {
        return userId;
    }

    public int getTotalJpBet() {
        return totalJpBet;
    }

    public int getTotalRealJpBet() {
        return totalRealJpBet;
    }

    public int getTotalJpReward() {
        return totalJpReward;
    }

    public double getTotalJpRewardFee() {
        return totalJpRewardFee;
    }

    public int getJpBetPerHand() {
        return jpBetPerHand;
    }

    public int getRealJpBetPerHand() {
        return realJpBetPerHand;
    }

    public int getJpRewardPerHand() {
        return jpRewardPerHand;
    }

    public double getJpRewardFeePerHand() {
        return jpRewardFeePerHand;
    }

    public String getNickName() {
        return nickName;
    }
    public void setNickName(String nickName) {
        this.nickName = nickName;
    }
}
