package com.dzpk.weight.flipcard;

import com.i366.model.pocer.Pocer;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * 翻牌干预结果
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
public class FlipCardResult {
	/**
	 * 手牌的牌型, 可能为空
	 */
	private FlipCardHandType handType;
	/**
	 * 目标牌型, handType为空时为空
	 */
	private FlipCardTargetType targetType;
	/**
	 * 是否生成了指定的牌型
	 */
	private Boolean success;
	/**
	 * 翻牌干预需要发的牌
	 * success=true, 该集合有3张牌, 否则为空集合
	 */
	private List<Pocer> pokers;
	/**
	 * 不是指定牌型必须的牌, 只是为了凑够pokers中的3张牌而随机返回一些牌
	 * 该字段的牌已经在pokers中包含
	 * success=true, 该集合可能有值, 也有可能是空集合
	 */
	private List<Pocer> otherPokers;
	private List<Pocer> handPokers;
	private List<Pocer> hidePokers;

	public static FlipCardResult success(List<Pocer> pokers) {
		return success(pokers, new ArrayList<>(0));
	}

	public static FlipCardResult success(List<Pocer> pokers, List<Pocer> otherPokers) {
		return new FlipCardResult(null, null, true, pokers, otherPokers, null, null);
	}

	public static FlipCardResult fail() {
		return new FlipCardResult(null, null, false, new ArrayList<>(0), new ArrayList<>(0), null, null);
	}
}
