package com.dzpk.weight.flipcard;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FlipCardConfigRow {
	/**
	 * 两对
	 */
	private String towPair;
	/**
	 * 抽花面
	 */
	private String lockFlush;
	/**
	 * 两头抽顺面
	 */
	private String lockStraight;
	/**
	 * 暗3条Set
	 */
	private String darkThreeOfKind;
	/**
	 * 明3条Set
	 */
	private String brightThreeOfKind;
	/**
	 * 顺子
	 */
	private String straight;
	/**
	 * 同花
	 */
	private String flush;
	/**
	 * 葫芦
	 */
	private String fullHouse;
	/**
	 * 四条
	 */
	private String fourOfKind;
	/**
	 * 同花顺
	 */
	private String straightFlush;
	/**
	 * 皇家同花顺
	 */
	private String royalStraightFlush;
}
