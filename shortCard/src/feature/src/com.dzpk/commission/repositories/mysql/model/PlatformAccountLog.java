package com.dzpk.commission.repositories.mysql.model;

import lombok.*;

import java.sql.Timestamp;


/**
 * 仓位变动日志记录
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PlatformAccountLog {

    private int platformCode; //仓位标识符
    private int type; //类型 1.JPB注入,2.营销账户增加注入,3-战绩分润,4-赠送头像 5.营销账户减少注入，6.营销账户重置金额,7-破隐,8-开通或者升级vip,9-修改昵称,10-玩家消费行为
    private int changeSource; //变更渠道 :1-api,2-room,3-omaha,4-大菠萝,5-sng,6-mtt,7-运营后台,8-shortCar
    private long currentChip;  //仓内更新前金豆数量
    private int changeChip;   //更新金豆数量
    private String description;  //描述
    private String externalId;   //业务相关id 可为空
    private int opId;            //操作人id -1为系统
    private Timestamp createTime; //创建时间
}
