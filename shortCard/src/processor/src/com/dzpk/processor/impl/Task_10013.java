package com.dzpk.processor.impl;

import com.dzpk.processor.IProcessor;
import com.dzpk.record.AnteAction;
import com.dzpk.work.Task;
import com.i366.cache.Cache;
import com.i366.model.room.Room;
import com.dzpk.common.utils.LogUtil;
import org.apache.logging.log4j.Logger;

/**
 * 保险延迟发牌
 * Created by baidu on 16/11/11.
 */
public class Task_10013 implements IProcessor {
    private Logger logger = LogUtil.getLogger(Task_10013.class);

    @Override
    public void handle(Task task) {
        if (!task.isValid()) {
            logger.debug("invalid message");
            return;
        }

        Room room = Cache.getRoom(task.getRoomId(), task.getRoomPath());
        logger.debug("roomId: " + task.getRoomId() + ", roomPath: " + task.getRoomPath());
        // 进入保险状态
        if (room != null) {
            int deanCount = (Integer) task.getMap().get(1);
            int size = (Integer) task.getMap().get(2);
            int progress = (Integer) task.getMap().get(3);
            logger.debug("deanCount: " + deanCount + ", size: " + size + ", progress: " + progress);
            room.getRoomService().sendCards(deanCount, size);

            // 牌谱记录 －－－--
            room.getRoomReplay().setProgress(progress);
            if (room.getRoomReplay().getAnteAction() == null) {
                room.getRoomReplay().setAnteAction(new AnteAction());
                room.getRoomReplay().getAnteAction().setCard(String.valueOf(room.getPocer()[progress + 1].getSize1()));
                room.getRoomReplay().getAnteAction().setPot(room.getPoolChip());
            }
            // -----

            room.roomProcedure.delayTaskMap.remove(task.getId());
        }
    }
}
