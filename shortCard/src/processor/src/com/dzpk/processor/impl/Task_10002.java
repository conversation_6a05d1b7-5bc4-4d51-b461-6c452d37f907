package com.dzpk.processor.impl;

import com.dzpk.common.utils.LogUtil;
import org.apache.logging.log4j.Logger;

import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.dzpk.work.TaskConstant;
import com.dzpk.work.WorkThreadService;
import com.i366.cache.Cache;
import com.i366.model.player.RoomPersion;
import com.i366.model.room.Room;

/**
 * 服务端超时
 * <AUTHOR>
 *
 */
public class Task_10002 implements IProcessor {

    private Logger logger = LogUtil.getLogger(Task_10002.class);
    
    @Override
    public void handle(Task task) {
        if (!task.isValid()) {
            logger.debug("invalid message");
            return;
        }
        Room room = Cache.getRoom(task.getRoomId(), task.getRoomPath());
        if (room != null) {
            int seat = (Integer) task.getMap().get(1);
            int userId = (Integer) task.getMap().get(2);
            long betIndex = (Long) task.getMap().get(3);
            logger.debug("seat: " + seat + ", userId: " + userId + ", betIndex: " + betIndex + " roomBetIndex: " + room.getBetIndex());
            if (betIndex >= room.getBetIndex()) {
                int roomCurrentNumber = room.getCurrentNumber();
                if(roomCurrentNumber < 0){
                    logger.debug("don't have current op user at this monent");
                }else{
                    RoomPersion roomPersion = room.getRoomPersions()[roomCurrentNumber];
                    if (roomPersion != null) {
                        logger.debug("current op user timeout, seat: " + room.getCurrentNumber() + ", userId: "
                                + roomPersion.getUserId());
                        if (seat == room.getCurrentNumber() && userId == roomPersion.getUserId()) {
                            if (room.getDelayTime() > 0) {
                                // 设置超时任务
                                Task newTask = new Task(TaskConstant.TASK_BET_TIMEOUT, task.getMap(), room.getRoomId(),
                                        room.getRoomPath());
                                WorkThreadService.submitDelayTask(room.getRoomId(), newTask, room.getDelayTime());
                                room.roomProcedure.delayTaskMap.put(newTask.getId(), newTask);
                                room.setDelayTime(0);
                            } else {
                                room.roomProcedure.setOpTimeOut(true);
                                room.roomProcedure.runByRoomStatus();
                            }
                        }
                    }
                }
            } else {
                logger.error("invalid message: betIndex: " + betIndex + ", current betIndex: " + room.getBetIndex());
            }
            room.roomProcedure.delayTaskMap.remove(task.getId());
        }
    }

}
