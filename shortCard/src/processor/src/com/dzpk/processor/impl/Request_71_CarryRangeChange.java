package com.dzpk.processor.impl;

import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.constant.Constant;
import com.i366.cache.Cache;
import com.i366.model.room.Room;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.server.pack.I366ServerPickUtil;
import com.work.comm.client.protocal.Request;
import com.dzpk.common.utils.LogUtil;
import com.i366.util.PublisherUtil;
import com.dzpk.db.dao.RoomDao;
import com.dzpk.db.imp.RoomDaoImpl;
import org.apache.logging.log4j.Logger;

public class Request_71_CarryRangeChange implements IProcessor {

    private Logger logger = LogUtil.getLogger(Request_71_CarryRangeChange.class);
    
    @Override
    public void handle(Task task) {
        int roomId = task.getRoomId();
        int roomPath = task.getRoomPath();
        Request request = (Request) task.getRequest();
        int minRate = (Integer) task.getMap().get(132);
        int maxRate = (Integer) task.getMap().get(133);
        logger.debug("roomId:" + roomId + ", roomPath:" + roomPath + ", minRate:" + minRate + ", maxRate:" + maxRate);

        try {
            Room room = Cache.getRoom(roomId, roomPath);
            if (room != null) {
                if(request.getUserId() == room.getOwner()){//房主才有权限
                    if (room.getRoomStatus() >= 2 && room.getRoomStatus() <= 7) {    // 游戏状态，下局生效
                        room.setRequestMinRate(minRate);
                        room.setRequestMaxRate(maxRate);
                    } else {        // 非游戏状态，修改马上生效
                        room.setMinRate(minRate);
                        room.setMaxRate(maxRate);
                        room.setRequestMinRate(0);
                        room.setRequestMaxRate(0);
                    }
                    RoomDao roomDao = new RoomDaoImpl();
                    roomDao.updateRoomRate(roomId, minRate, maxRate);

                    Object[][] objs = {
                            {60, 0, I366ClientPickUtil.TYPE_INT_1},            // 0 成功 1 失败
                            {130, minRate, I366ClientPickUtil.TYPE_INT_4},    // 新的最小倍数
                            {131, maxRate, I366ClientPickUtil.TYPE_INT_4},    // 新的最大倍数
                    };
                    byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_CARRY_RANGE_CHANGE);
                    PublisherUtil.send(room,bytes, request.getUserId());
                    PublisherUtil.publisher(request, bytes);
                    return;
                }
            }
        } catch (Exception e) {
            logger.debug("", e);
        }
        PublisherUtil.publisher(request, pusUser(1));
    }

    public byte[] pusUser(int status) {
        Object[][] objs = {
                {60, status, I366ClientPickUtil.TYPE_INT_1},
        };
        byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_CARRY_RANGE_CHANGE);
        return bytes;
    }

}
