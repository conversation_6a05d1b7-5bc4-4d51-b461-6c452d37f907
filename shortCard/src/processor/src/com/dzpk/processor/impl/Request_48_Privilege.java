package com.dzpk.processor.impl;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.repositories.mysql.dbpool.DBUtil;
import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.cache.Cache;
import com.i366.constant.Constant;
import com.i366.model.pocer.Pocer;
import com.i366.model.room.Room;
import com.i366.util.PublisherUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.Request;
import org.apache.logging.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;

public class Request_48_Privilege implements IProcessor {
    private Logger logger = LogUtil.getLogger(Request_48_Privilege.class);
    @Override
    public void handle(Task task) {
        Request request = (Request) task.getRequest();
        int roomId = task.getRoomId();
        int roomPath = task.getRoomPath();
        Connection dbConnection = null;
        PreparedStatement ps = null;
        ResultSet rs=null;
        String selectPrivilege = "select user_id from privilege_user where user_id="+request.getUserId();

        try {
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(selectPrivilege);
            rs=ps.executeQuery();
            boolean next = rs.next();
            if (next){
                logger.info("成功用户" + request.getUserId());
                Room room = Cache.getRoom(roomId, roomPath);

                if (room == null ) {  //校验房间是否存在
                    PublisherUtil.publisher(request, pusUser( 1,new Integer[0] ,0));
                    return;
                }
                List<Pocer> unSendCards = room.getPocerLink().getUnSendCards();
                Integer[] systemCards = new Integer[unSendCards.size()];

                for (int i=0;i<unSendCards.size();i++)systemCards[i]=unSendCards.get(i).getSize1();

                Arrays.sort(systemCards);
                int cardsLength=0;
                if (room.getPocer()[0]==null){
                    cardsLength=3;
                }else {
                    cardsLength=1;
                }
                PublisherUtil.publisher(request, pusUser(0, systemCards,cardsLength));
            }else {
                PublisherUtil.publisher(request, pusUser(1, new Integer[0],0));
            }

        } catch (SQLException e){
            e.getSQLState();
        }finally {
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
            DBUtil.closeResultSet(rs);
        }
    }
    private byte[] pusUser(int status, Integer[] json,int cards) {
        logger.debug("status: " + status);
        Object[][] objs2 = {
                // 0成功 1失败
                { 61, status, I366ClientPickUtil.TYPE_INT_1 },
                { 62, cards, I366ClientPickUtil.TYPE_INT_1 },
                { 63, json, I366ClientPickUtil.TYPE_INT_1_ARRAY}
        };
        byte[] bytes = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_GET_PRIVILEGE);
        return bytes;
    }
}
