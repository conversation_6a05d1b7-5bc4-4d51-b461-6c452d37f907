package com.dzpk.processor.impl;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.cache.Cache;
import com.i366.model.room.Room;
import org.apache.logging.log4j.Logger;

/**
 * 第一次带入延时带入2-3s
 */
public class Task_10026 implements IProcessor {

    private Logger logger = LogUtil.getLogger(Task_10026.class);

    @Override
    public void handle(Task task) {
        logger.debug("at玩家第一次带入积分");

        if (!task.isValid()) {
            logger.debug("invalid message");
            return;
        }

        Room room = Cache.getRoom(task.getRoomId(), task.getRoomPath());
        if (null != room) {
            int userId = (int) task.getMap().get(1);
            int seat = (int) task.getMap().get(2);

            room.roomProcedure.delayTaskMap.remove(task.getId());
        }
    }
}
