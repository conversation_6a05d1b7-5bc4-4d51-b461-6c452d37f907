package com.dzpk.db.model;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class ServiceFee {
    //房间id
    private int roomId;
    //房间名称
    private String roomName;
    //用户Id
    private int userId;
    //用户名
    private String userName;
    //抽水费用
    private int fee;
    //盈利的值
    private int earn;
    //属于的俱乐部
    private int cludId;
    //用户的联盟Id没有为0
    private int tribeId;
    //俱乐部的分成
    private int cludeProportion;
    //联盟的分成
    private int tribeProportion;
    //小盲
    private int mangzhu;
    //是否为机器人0:false ; 1:true
    private int AIstate;
    //最后一位
    private int ending;
    //游戏路径
    private int roompath;
    //保险赔付
    private int insuranceExpenditure;
    //第一次投入保险
    private int insuranceProfitOne;
    //第一次投入保险
    private int insuranceProfitTow;
    //投入彩池的额度
    private int contribution;
    //投入是那个池子
    private int sign;
    //几手
    private int version;
    //俱乐部房间clubRoomType
    private int clubRoomType;

    public ServiceFee(int roomId, String roomName, int mangzhu, int roompath, int version, int clubRoomType) {
        this.roomId = roomId;
        this.roomName = roomName;
        this.mangzhu = mangzhu;
        this.roompath = roompath;
        this.version = version;
        this.clubRoomType = clubRoomType;
    }

}
