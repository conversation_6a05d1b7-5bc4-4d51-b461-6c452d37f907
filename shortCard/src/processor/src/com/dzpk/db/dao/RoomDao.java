package com.dzpk.db.dao;

import com.dzpk.db.model.BroadcastModel;
import com.dzpk.db.model.RequestToBringInMessage;

import java.sql.SQLException;
import java.util.List;
import java.util.Set;
import java.util.Vector;

public interface RoomDao {

    /**
     * 更新房间进度状态
     * 房间状态：0:close 1:create 2:pending 3:playing 4:played
     * @param roomId
     * @param progress
     * @return
     */
    boolean updateRoomProgress(int roomId, int progress);

    /**
     * 获取房间进度 status  0:close 1:create 2:pending 3:playing 4:played
     * @param roomId
     * @return
     */
    int getRoomProgress(int roomId);

    /**
     * 删除俱乐部房间扩展的信息
     * @param roomId
     * @return
     */
    int delRoomClub(int roomId);
    /**
     * 更新房间暂时状态
     *
     * @param roomId
     * @param pause
     * @return
     */
    boolean updateRoomPause(int roomId, boolean pause);

    /**
     * 获取所有的请求消息
     * @return
     */
    List<RequestToBringInMessage> getRequestToBringInMessage();

    /**
     * 修改消息
     * @param roomId 坊间id
     *
     */
    void updateMessage(int roomId,int sendId);
    /**
     * 删除消息
     * @param id
     */
    void deleteMessage(int id, int sendId);
    /**
     * 更新房间带入倍数
     *
     * @param roomId
     * @param minRate
     * @param maxRate
     * @return
     */
    boolean updateRoomRate(int roomId, int minRate, int maxRate);

    /**
     * 更新房间游戏时间
     * @param roomId
     * @param maxPlayTime
     * @return
     */
    boolean updateRoomMaxPlayTime(int roomId, int maxPlayTime);

    /**
     * 更新房间straddle状态
     * 房间状态：0:关闭 1:打开
     * @param roomId
     * @param straddle
     * @return
     */
    boolean updateRoomStraddle(int roomId, boolean straddle);

    /**
     * 更新房间muck开关状态
     * 房间状态：0:关闭 1:打开
     * @param roomId
     * @param muckSwitchValue
     * @return
     */
    boolean updateRoomMuckSwitch(int roomId, int muckSwitchValue);

    /**
     * 根据用户id，俱乐部id更新积分
     * @param clubId 俱乐部id
     * @param userId 用户id
     * @param integral 本次积分
     */
    void updateClubIntegral(int clubId,int userId,int integral);

    void updateTribeIntegral(int clubId, int userId, int integral);

    /**
     * 更新房间进度状态和开始时间
     * 房间状态：0:close 1:create 2:pending 3:playing 4:played
     * @param roomId
     * @param progress
     * @return
     */
    boolean updateRoomProgressAndStartTime(int roomId,long startTime, int progress);

    /**
     * 新建房间
     * @param roomPath
     * @param roomId
     * @return
     */
    boolean createRoom(int roomPath, int roomId);

    /**
     * 获取超过24小时没开局的房间列表
     * @param timeOutSecond  一天前超时的秒数
     * @param serverId  游戏服务器id
     * @param roomPath  房间类型
     * @return
     */
    Vector<Integer> getTimeOutRoomList(long timeOutSecond,int serverId,int roomPath);

    /**
     * 获取房间进度和开局时间（如果已开局）
     *
     * @param roomId
     * @return
     */
     Object[] getRoomInfo(int roomId);

    /**
     * 房主开始游戏
     * 设置开局时间和房间状态4
     * @param roomId
     * @param startTime
     * @param status
     * @return
     */
    boolean setRoomStart(int roomId, long startTime, int status);

    /**
     * 重置group_room表中的状态
     * @param roomIds  对应服务器上的房间集合
     * @throws SQLException
     */
    void resetRoom(Set<Integer> roomIds) throws SQLException;

    /**
     * 重置user_join_room表中的状态
     * @param roomIds  对应服务器上的房间集合
     * @throws SQLException
     */
    void resetUserJoinRoom(Set<Integer> roomIds) throws SQLException;

}
