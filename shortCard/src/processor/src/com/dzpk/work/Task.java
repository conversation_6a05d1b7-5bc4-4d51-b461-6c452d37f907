package com.dzpk.work;

import java.util.Arrays;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ScheduledFuture;

import com.work.comm.client.protocal.BaseRequest;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Task {

    // id
    private String id;
    // 请求号
    private int taskId;
    // 任务是否有效 0无效 1有效
    private boolean isValid = true;
    // 任务延迟执行时间(单位ms)
    private long delayTime = 0;
    // 任务开始时间
    private long beginTime = 0;
    // 任务还剩多少时间开始执行
    private long leftTime = 0;
    
    // 请求参数字典
    private Map<Integer, Object> map;
    // 请求头信息
    private BaseRequest request;
    private int roomId;
    private int roomPath;

    //底层任务句柄
    private ScheduledFuture taskFuture;
    public ScheduledFuture getTaskFuture() {
        return taskFuture;
    }

    public void setTaskFuture(ScheduledFuture taskFuture) {
        this.taskFuture = taskFuture;
    }

    public Task(int taskId) {
        this.taskId = taskId;
        id = UUID.randomUUID().toString();
        beginTime = System.currentTimeMillis();
    }
    
    public Task(int taskId, Map<Integer, Object> map) {
        this(taskId);
        this.taskId = taskId;
        this.map = map;
    }
    
    public Task(int taskId, Map<Integer, Object> map, BaseRequest request) {
        this(taskId);
        this.map = map;
        this.request = request;
        id = UUID.randomUUID().toString();
    }
    
    public Task(int taskId, Map<Integer, Object> map, BaseRequest request, int roomId, int roomPath) {
        this(taskId);
        this.map = map;
        this.request = request;
        this.roomId = roomId;
        this.roomPath = roomPath;
        id = UUID.randomUUID().toString();
    }
    
    public Task(int taskId, Map<Integer, Object> map, int roomId, int roomPath) {
        this(taskId);
        this.map = map;
        this.roomId = roomId;
        this.roomPath = roomPath;
        id = UUID.randomUUID().toString();
    }

    @Override
    public String toString() {
        if (map != null) {
            return "taskId: " + taskId + ", map: " + Arrays.toString(map.entrySet().toArray());
        } else {
            return "taskId: " + taskId;
        }
    }
}
