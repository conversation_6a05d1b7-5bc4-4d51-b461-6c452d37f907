
package com.i366.processor.client;

import java.util.Map;

import com.work.comm.client.pack.I366ClientPickUtil;

import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;
import com.i366.constant.Constant;
import com.work.comm.io.Handler;
import com.work.comm.client.protocal.BaseRequest;
import com.work.comm.client.protocal.Request;

/**
 * 房主延长牌局时间
 */
public class Processor_413_AddTime extends Handler {

    @Override
    public byte[] handleRequest(BaseRequest req) {
        Request request = (Request) req;

        int[][] int2 = {
                { 131, I366ClientPickUtil.TYPE_INT_4 }, // roomId
                { 132, I366ClientPickUtil.TYPE_INT_4 }, // roomPath
                { 133, I366ClientPickUtil.TYPE_INT_4 }, // 延时时间30，60，90...
        };
        Map<Integer, Object> map = I366ClientPickUtil.pickAll(request.getBt(), int2);
        int roomId = (Integer) map.get(131);
        int roomPath = (Integer) map.get(132);

        Task task = new Task(Constant.REQ_ROOM_ADD_TIME, map, request, roomId, roomPath);
        WorkThreadService.submit(roomId, task);
        return null;
    }

}
