package com.i366.processor.client;

import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;
import com.i366.constant.Constant;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.BaseRequest;
import com.work.comm.client.protocal.Request;
import com.work.comm.io.Handler;

import java.util.Map;

public class Request_49_SetPublicCard_Handler extends Handler {
    @Override
    public byte[] handleRequest(BaseRequest req) {
        Request request = (Request) req;
        byte[] bytes = req.getBt();
        int[][] int2 = {
                {130, I366ClientPickUtil.TYPE_INT_4},          // 房间编号
                {131, I366ClientPickUtil.TYPE_INT_4},           // path
                {61, I366ClientPickUtil.TYPE_INT_1_ARRAY}
        };
        Map<Integer, Object> map = I366ClientPickUtil.pickAll(bytes, int2);
        int roomId = Integer.valueOf(map.get(130).toString());
        int roomPath = Integer.valueOf(map.get(131).toString());
        Task task = new Task(Constant.REQ_GAME_SET_PRIVILEGE, map, request, roomId, roomPath);
        WorkThreadService.submit(roomId, task);
        return null;
    }
}
