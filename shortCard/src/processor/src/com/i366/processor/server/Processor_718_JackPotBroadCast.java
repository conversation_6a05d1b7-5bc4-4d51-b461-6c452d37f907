package com.i366.processor.server;

import com.i366.constant.Constant;
import com.i366.cache.Cache;
import com.i366.model.room.Room;
import com.i366.util.JackPotUtil;
import com.i366.util.PublisherUtil;
import com.work.comm.s2s.processor.impl.AbstractProcessorImpl;
import com.work.comm.s2s.protocal.Protocal;
import com.work.comm.s2s.protocal.S2PacckageUtil;
import com.work.comm.s2s.protocal.ServiceRequest;
import com.work.comm.server.pack.I366ServerPickUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 接收资源服务器发过来的jp广播功能请求
 */
@Slf4j
public class Processor_718_JackPotBroadCast extends AbstractProcessorImpl {
    /**
     * 接收的数据包的处理方法
     *
     * @param req  请求对象，必填
     *
     * @return  返回的业务数据
     *   null或byte[0] 表示不需要返回客户端
     */
    @Override
    public byte[] handle(ServiceRequest req) {
        int[][] int2 = {
                {130, Protocal.TYPE_INT_4},
                {131, Protocal.TYPE_INT_4},
                {132, Protocal.TYPE_INT_4},
                {133, Protocal.TYPE_STRING_UTF16},
                {134, Protocal.TYPE_INT_4},
                {135, Protocal.TYPE_INT_4},
                {136, Protocal.TYPE_INT_4},
                {137, Protocal.TYPE_INT_4}
        };
        Map<Integer, Object> map = S2PacckageUtil.pickAll(req.getDataBuffer(), int2);

        Integer roomId = Integer.valueOf(map.get(130).toString());
        Integer userId = Integer.valueOf(map.get(131).toString());
        String nickName = String.valueOf(map.get(133).toString());
        Integer pokerType = Integer.valueOf(map.get(134).toString());
        Integer jackpotId = Integer.valueOf(map.get(135).toString());
        Integer jackPotBetScore = Integer.valueOf(map.get(136).toString());
        Integer seatIndex = Integer.valueOf(map.get(137).toString());

        log.debug("Processor_718_JackPotBroadCast roomId:" + roomId + " userId: " + userId + " nickName: " + nickName + " jackpotId: " + jackpotId);

        try {
            JackPotUtil.pushJackPotPocerTypeMsgToClient(Cache.getJackpotRoomInfo(),nickName,pokerType,jackPotBetScore,roomId,seatIndex);
//            CopyOnWriteArrayList<Room> roomList = Cache.getJackpotRoomInfo();
//
//            Object[][] objs = {
//                    {60, 0, I366ServerPickUtil.TYPE_INT_1},
//                    {130, nickName, I366ServerPickUtil.TYPE_STRING_UTF16},
//                    {131, pokerType, I366ServerPickUtil.TYPE_INT_4},
//                    {132, jackPotBetScore, I366ServerPickUtil.TYPE_INT_4},
//                    {133, roomId, I366ClientPickUtil.TYPE_INT_4},
//                    {134, seatIndex, I366ClientPickUtil.TYPE_INT_1},
//            };
//            byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_JACKPOT_BROADCAST);
//            for(Room room:roomList){
//                if(room != null){
//                    PublisherUtil.send(room,bytes);
//                }
//            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("", e);
        }

        return null;
    }

}

