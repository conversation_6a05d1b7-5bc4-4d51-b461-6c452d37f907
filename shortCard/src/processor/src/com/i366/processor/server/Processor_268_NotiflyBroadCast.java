package com.i366.processor.server;

import com.dzpk.db.model.BroadcastModel;
import com.i366.cache.Cache;
import com.i366.model.room.LeveRoom;
import com.i366.model.room.Room;
import com.i366.util.GsonUtil;
import com.i366.util.RoomUtil;
import com.work.comm.s2s.processor.impl.AbstractProcessorImpl;
import com.work.comm.s2s.protocal.Protocal;
import com.work.comm.s2s.protocal.S2PacckageUtil;
import com.work.comm.s2s.protocal.ServiceRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 接收资源服务器发过来的通知广播功能请求
 */
@Slf4j
public class Processor_268_NotiflyBroadCast extends AbstractProcessorImpl {
    /**
     * 接收的数据包的处理方法
     *
     * @param req  请求对象，必填
     *
     * @return  返回的业务数据
     *   null或byte[0] 表示不需要返回客户端
     */
    @Override
    public byte[] handle(ServiceRequest req) {
        int[][] int2 = {
                {130, Protocal.TYPE_STRING_UTF16},
        };
        Map<Integer, Object> map = S2PacckageUtil.pickAll(req.getDataBuffer(), int2);

        String jsonStr = String.valueOf(map.get(130).toString());
        if(null == jsonStr || jsonStr.isEmpty()){
            log.error("Processor_268_NotiflyBroadCast message has error");
            return null;
        }

        try {
            BroadcastModel broadcast = GsonUtil.getInstance().fromJson(jsonStr,BroadcastModel.class);

            log.debug("Processor_268_NotiflyBroadCast messsage=>" + broadcast.toString());

            if(broadcast !=null) {

                long timeDelta = (long) broadcast.getBroadcastTime() * 1000 - System.currentTimeMillis();
                log.debug("BroadcastCheckTask timeDelta: " + timeDelta);
                if (timeDelta <= 0) {
                    if(broadcast.getSendType() == 2) {//指定牌局
                        Map<Integer, LeveRoom> leveRoomMap = Cache.getSecondRoom(broadcast.getRoomPath()).getLeveRoomMap();
                        if(leveRoomMap.size()>0) {
                            for(int gameId : leveRoomMap.keySet()){
                                LeveRoom leveRoom = leveRoomMap.get(gameId);
                                if(leveRoom != null) {
                                    for(int roomId : leveRoom.getRoomMap().keySet()){
                                        Room room = leveRoom.getRoomMap().get(roomId);
                                        if(room !=null) {
                                            RoomUtil.sendBroadcast(room,room.getRoomId(),broadcast.getBroadcastCount(),broadcast.getBroadcastContent());
                                        }
                                    }
                                }
                            }
                        }
                    }else if(broadcast.getSendType() == 3) {//指定定房间
                        Room room = Cache.getRoom(broadcast.getRoomId(), broadcast.getRoomPath());
                        if(room != null) {
                            RoomUtil.sendBroadcast(room,broadcast.getRoomId(),broadcast.getBroadcastCount(),broadcast.getBroadcastContent());
                        }
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.error("", e);
        }

        return null;
    }

}

