package com.i366.model.room;

import lombok.Data;

@Data
public class CloseRoomMessage {
    /**
     * 牌局ID：
     */
    private int roomId;
    /**
     *  牌局名称：
     */
    private String roomName;
    /**
     * 盲注级别：
     */
    private int mangzhu;
    /**
     * 牌局时长：
     */
    private int gameTime;
    /**
     * 结束时间：
     */
    private long roomCloseTime;
    /**
     * 总手数：
     */
    private int roomStage;
    /**
     * 总带入：
     */
    private int total;
    /**
     * 水上数据：
     */
    private int water;
    /**
     * 水下数据
     */
    private int underwater;
    /**
     * 手续费：
     */
    private int fee;
    /**
     * 保险：
     */
    private int insurance;
    /**
     * JackPot
     */
    private int jp;

}
