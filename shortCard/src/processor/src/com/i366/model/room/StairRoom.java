/*
 * $RCSfile: StairRoom.java,v $
 * $Revision: 1.1  $
 * $Date: 2012-8-10  $
 *
 * Copyright (C) 2005 Bettem, Inc. All rights reserved.
 *
 * This software is the proprietary information of Bettem, Inc.
 * Use is subject to license terms.
 */
package com.i366.model.room;

import java.util.HashMap;
import java.util.Map;

/**
 * 一级房间分类 
 * <p>Title: StairRoom</p> 
 * <p>Description: </p> 
 * <p>Copyright: Copyright (c) 2006</p> 
 * <AUTHOR>
 * @version 1.0
 */
public class StairRoom {
	
	/**
	 * 对应新手、初级、中级、高级、比赛场 自建场
	 */
	public static int[] SECOND_ROOM_TYPE = {1,2,3,4,5,6};
	private String ip;
	private int port;
	private Map<Integer, SecondRoom> secondRoom = new HashMap<Integer, SecondRoom>();
	public String getIp() {
		return ip;
	}
	public void setIp(String ip) {
		this.ip = ip;
	}
	public int getPort() {
		return port;
	}
	public void setPort(int port) {
		this.port = port;
	}
	public Map<Integer, SecondRoom> getSecondRoom() {
		return secondRoom;
	}
}

