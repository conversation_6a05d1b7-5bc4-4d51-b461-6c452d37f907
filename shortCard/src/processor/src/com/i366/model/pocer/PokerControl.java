package com.i366.model.pocer;

import com.dzpk.common.utils.LogUtil;
import com.i366.model.player.RoomPersion;
import com.i366.model.room.Room;
import com.dzpk.component.repositories.mongo.MongodbService;
import com.dzpk.component.repositories.redis.RedisService;
import com.mongodb.MongoClient;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import org.apache.logging.log4j.Logger;
import org.bson.Document;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 控制发牌
 */
public class PokerControl {

    public static Logger logger = LogUtil.getLogger(PocerLink.class);

    private String PUBLIC_CARDS = "pcs"; //指定发牌公共牌
    private String HAND_CARDS = "hcs"; //指定发牌手牌
    private String CONTROL_USERID = "controlid"; //操作人id

    private String PUBLIC_CARDS_SPLITER = ";";  //公共牌分隔符
    private String HAND_CARDS_SPLITER = ",";  //手牌分隔符

    private  Pocer[] showPokers = new Pocer[5];  //花钱看的牌

    private static final String db = "dzpk";

    private static final String _cardControlRecord = "game_action";

    public Pocer[] getShowPokers() {
        return showPokers;
    }

    public void setShowPokers(Pocer[] showPokers) {
        this.showPokers = showPokers;
    }

    private PocerControlInfo pocerControlInfo;

    public PocerControlInfo getPocerControlInfo() {
        return pocerControlInfo;
    }

    public void setPocerControlInfo(PocerControlInfo pocerControlInfo) {
        this.pocerControlInfo = pocerControlInfo;
    }

    /**
     * 初始化需要加载的牌型
     * 从redis中获取数据
     * @param room
     */
    public void checkCardControl(Room room) {

        try {
            logger.debug("checkCardControl roomid: " + room.getRoomId());
            //获取指定的牌型
            RedisService redisService = RedisService.getRedisService();
            Map<String,String> cardInfo  = redisService.getCardControlInfo(room.getRoomId(),room.getRoomPath());
            if(cardInfo == null || cardInfo.isEmpty()){
                logger.debug("roomid: " + room.getRoomId() + " don't have card control info");
                return;
            }

            logger.debug("checkCardControl roomid: " + room.getRoomId() + " need to card control, now hand: " + room.getStage());

            String publicCards = cardInfo.get(PUBLIC_CARDS);  //公共牌包含花钱看牌的5张牌
            String handCards =  cardInfo.get(HAND_CARDS);     //手牌包括所有人的
            String controlUserId =  cardInfo.get(CONTROL_USERID);     //操作人id
            logger.debug("publicCards: {} handCards: {} controlUserId : {}" , publicCards, handCards , controlUserId);
            //检查是否牌是否合法
            if(!verify(publicCards,handCards)){
                logger.error("card control have same cards");
                //删除该key
                redisService.delCardControlInfo(room.getRoomId(),room.getRoomPath());
                return;
            }

            //校验牌是否已经发过 公共牌和手牌一模一样才算相同
            if(checkIfSendedSameCards(publicCards,handCards,room)){
                //删除该key
                redisService.delCardControlInfo(room.getRoomId(),room.getRoomPath());
                logger.error("card control have send same cards");
                return;
            }

            //保存发牌信息
            PocerControlInfo pocerControlInfo = new PocerControlInfo();
            pocerControlInfo.setControlUserId(controlUserId == null ? 0 : Integer.parseInt(controlUserId));
            pocerControlInfo.setHandCards(handCards);
            pocerControlInfo.setPublicCards(publicCards);
            pocerControlInfo.setRoomId(room.getRoomId());
            pocerControlInfo.setRoomName(room.getName());
            this.pocerControlInfo = pocerControlInfo;

            //设置公共牌  找到庄家位置，按位置分配手牌
            setAllPokers(publicCards,handCards,room);

            //记录到已经发的牌型集合中并且删除该key
            recordControlCards(publicCards,handCards,room);
        } catch (Exception e) {
            logger.error("checkCardControl", e);
        }

    }

    //检查是否牌是否合法
    private boolean verify(String publicCards,String handCards){
        if (publicCards != null && handCards != null) {
            String[] publicPokers = publicCards.split(PUBLIC_CARDS_SPLITER);
            if(publicPokers.length != 10){  //公共牌+看的牌一起是10张
                logger.error("publicPokers length invalid " + publicPokers.length);
                return false;
            }

            String[] playerPokers = handCards.split(PUBLIC_CARDS_SPLITER);
            if(playerPokers.length != 9){  //手牌是9组
                logger.error("playerPokers length invalid " + playerPokers.length);
                return false;
            }

            Set<Integer> cardsSet = new HashSet<>();  //所有的牌
            for(String publicPoker:publicPokers){
                int card = Integer.parseInt(publicPoker);
                if(card < 0 || card > 51){ //必须在0~51之间
                    logger.debug("public cards have invalid card: " + card);
                    return false;
                }

                if(cardsSet.contains(card)){  //包含相同牌
                    logger.debug("public cards have same card: " + card);
                    return false;
                }else{
                    cardsSet.add(card);
                }
            }


            for(String playerPoker:playerPokers){
                String[] eachPlayerPokers = playerPoker.split(HAND_CARDS_SPLITER); //每个人的手牌已逗号分割，必须是2张
                if(eachPlayerPokers.length != 2){
                    logger.debug("eachPlayerPokers length invalid " + eachPlayerPokers.length);
                    return false;
                }else{
                    for(String eachPlayerPoker:eachPlayerPokers){
                        int card = Integer.parseInt(eachPlayerPoker);
                        if(card < 0 || card > 51){ //必须在0~51之间
                            logger.debug("hand cards have invalid card: " + card);
                            return false;
                        }
                        if(cardsSet.contains(card)){  //包含相同牌
                            logger.debug("hand cards have same card: " + card);
                            return false;
                        }else{
                            cardsSet.add(card);
                        }
                    }
                }
            }
            return true;
        } else {
            return false;
        }
    }

    //检查该牌局是否之前发过相同牌
    //公共牌或者手牌相同都算相同牌
    private boolean checkIfSendedSameCards(String publicCards,String handCards,Room room){
        if(room.getCardControlPublicCardsList().contains(publicCards) || room.getCardControlHandCardsList().contains(handCards)){
            logger.debug("checkIfSendedSameCards have sended same cards: publicCards= " + publicCards + " handCards= " + handCards);
            return true;
        }
        return false;
    }

    //设置手牌和公共牌
    private void setAllPokers(String publicCards,String handCards,Room room){

        logger.debug("setAllPokers");
        //清空之前的设置
        PocerLink pocerLink = room.getPocerLink();
        pocerLink.playerPokers.clear();
        pocerLink.players.clear();

        String[] publicPokers = publicCards.split(PUBLIC_CARDS_SPLITER);
        //设置公共牌
        pocerLink.publicPoker[0] = new Pocer(Integer.valueOf(publicPokers[0]));
        pocerLink.publicPoker[1] = new Pocer(Integer.valueOf(publicPokers[1]));
        pocerLink.publicPoker[2] = new Pocer(Integer.valueOf(publicPokers[2]));
        pocerLink.publicPoker[3] = new Pocer(Integer.valueOf(publicPokers[3]));
        pocerLink.publicPoker[4] = new Pocer(Integer.valueOf(publicPokers[4]));
        //设置花钱看牌
        showPokers[0] = new Pocer(Integer.valueOf(publicPokers[5]));
        showPokers[1] = new Pocer(Integer.valueOf(publicPokers[6]));
        showPokers[2] = new Pocer(Integer.valueOf(publicPokers[7]));
        showPokers[3] = new Pocer(Integer.valueOf(publicPokers[8]));
        showPokers[4] = new Pocer(Integer.valueOf(publicPokers[9]));

        String[] handPokers = handCards.split(PUBLIC_CARDS_SPLITER);

        //设置庄家手牌
        int zhuangjiaSeat = room.getZhuangjiaNumber();
        RoomPersion zhuangjiaRoomPersion = room.getRoomPersions()[zhuangjiaSeat];
        String[] zhuangjiaHandPoker = handPokers[0].split(HAND_CARDS_SPLITER);
        setHandPoker(zhuangjiaRoomPersion.getUserId(),zhuangjiaHandPoker,pocerLink);

        logger.debug("zhuangjiaSeat: " + zhuangjiaSeat + " zhuangjiaHandPoker: " + Arrays.toString(zhuangjiaHandPoker));
        int seat = zhuangjiaSeat + 1; //从庄家位下一个位置开始分配手牌
        for(int i = 1; i < room.getPlayerCount(); i++){
            String[] handPoker = handPokers[i].split(HAND_CARDS_SPLITER);
            seat =  seat >= room.getPlayerCount() ? seat - room.getPlayerCount() : seat;
            RoomPersion roomPersion = room.getRoomPersions()[seat];
            if(roomPersion != null){
                setHandPoker(roomPersion.getUserId(),handPoker,pocerLink);
                logger.debug("seat： " + seat + " handPoker: " + Arrays.toString(handPoker));
            }
            seat++;
        }
    }

    /**
     * 记录该手牌的信息
     * roomid、公共牌、手牌、用户id + 这手牌赢家 、 赢的筹码
     * @param winUserIds  赢家userid (可能存在多个)
     * @param winChips   赢家赢得筹码 (可能存在多个)
     * @param jackPotBetScores   击中jp的奖励 (可能存在多个)
     */
    public void recordControlInfoToMongo(Integer[] winUserIds,Integer[] winChips,Integer[] jackPotBetScores){
        MongoClient mongo = null;
        try {
            mongo = MongodbService.getMongoInstance();
            MongoDatabase database = mongo.getDatabase(db);
            MongoCollection<Document> requestCollection = database.getCollection(_cardControlRecord);

            Document requestRecord = new Document();

            logger.debug("recordControlInfoToMongo, controlInfo:" + pocerControlInfo.toString());
            logger.debug("winUserIds: " + Arrays.toString(winUserIds));
            logger.debug("winChips: " + Arrays.toString(winChips));
            logger.debug("jackPotBetScores: " + Arrays.toString(jackPotBetScores));
            requestRecord.put("win_userid_array", Arrays.toString(winUserIds).replace("[", "").replace("]", "").replace(" ", ""));  //赢家用户id
            requestRecord.put("win_chip_array", Arrays.toString(winChips).replace("[", "").replace("]", "").replace(" ", ""));     //赢家赢利筹码
            requestRecord.put("jp_bet_chip_array", Arrays.toString(jackPotBetScores).replace("[", "").replace("]", "").replace(" ", ""));  //赢家击中jp奖励
            requestRecord.put("room_id", pocerControlInfo.getRoomId());
            requestRecord.put("control_userid", pocerControlInfo.getControlUserId());
            requestRecord.put("room_id", pocerControlInfo.getRoomId());
            requestRecord.put("room_name", pocerControlInfo.getRoomName());
            requestRecord.put("public_cards", pocerControlInfo.getPublicCards());
            requestRecord.put("hand_cards", pocerControlInfo.getHandCards());

            Date date = new Date();
            SimpleDateFormat formatter;
            formatter = new SimpleDateFormat ("yyyy-MM-dd HH:mm:ss");
            String time = formatter.format(date);
            requestRecord.put("time",time);

            requestCollection.insertOne(requestRecord);
        }catch(Exception e){
            logger.error(e.getMessage());
        }finally {
            MongodbService.close(mongo);
        }
    }

    //记录该牌型到内存中并且删除redis中的key
    private void recordControlCards(String publicCards, String handCards, Room room){
        room.getCardControlHandCardsList().add(handCards);  //记录手牌牌型
        room.getCardControlPublicCardsList().add(publicCards); //记录公共牌牌型

        //删除该key
        RedisService redisService = RedisService.getRedisService();
        redisService.delCardControlInfo(room.getRoomId(),room.getRoomPath());

        room.setCardControl(true); //设置房间为控制发牌状态
    }


    //设置玩家手牌
    private void setHandPoker(int userId, String[] p, PocerLink pocerLink) {
        Pocer[] pokers = new Pocer[2];
        pokers[0] = new Pocer(Integer.valueOf(p[0]));
        pokers[1] = new Pocer(Integer.valueOf(p[1]));
        pocerLink.playerPokers.put(userId, pokers);
    }
}
