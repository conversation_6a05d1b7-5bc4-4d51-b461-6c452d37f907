package com.i366.room;

import com.dzpk.activity.redwallet.constant.RedWalletConstant;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.db.dao.RoomSearchDao;
import com.dzpk.db.imp.RoomSearchDaoImpl;
import com.dzpk.eventtrack.constant.EventTrackingCode;
import com.i366.constant.RoomSeatChangeCode;
import com.i366.model.room.Room;
import org.apache.logging.log4j.Logger;

/**
 * 房间座位变化逻辑处理
 */
public class RoomSeatChangeService {

    private static Logger logger = LogUtil.getLogger(RoomSeatChangeService.class);

    private Room room;

    public RoomSeatChangeService(Room room) {
        this.room = room;
    }

    public void checkSeatChangeTpye(int roomId, int userId, int roomStage, int hand, RoomSeatChangeCode roomSeatChangeCode){
        logger.debug("牌局座位数变化,rid={},uid={},变化类型={}",
                roomId,userId,roomStage,hand,roomSeatChangeCode.getDesc());

        if(RoomSeatChangeCode.DOWN_ROOM == roomSeatChangeCode){
            afterDownRoom(roomId,userId,roomStage,hand,roomSeatChangeCode);
        }else if(RoomSeatChangeCode.STANDUP_ROOM == roomSeatChangeCode){
            afterStandupRoom(roomId,userId,hand,roomSeatChangeCode);
        }
    }

    /**
     * 成功坐下座位后处理逻辑
     * @param roomId 房间id
     * @param userId 玩家id
     * @param roomStage  房间总手数
     * @param hand 当前玩家累计手数
     * @param roomSeatChangeCode 变化类型
     */
    private void afterDownRoom(int roomId,int userId,int roomStage,int hand,RoomSeatChangeCode roomSeatChangeCode){
        updateRoomSeat(roomId, roomSeatChangeCode);  //更新座位数
        room.getEventTrackService().addEventTrack(room.getRoomId(), userId, EventTrackingCode.DOWN_ROOM_EVENT); //添加埋点

        if (room.getActivityConfig().isSkyRedWallet()) {
            if (roomStage >= RedWalletConstant.ACTIVITY_RED_WALLET_HAND) {
                room.getReaWalletService().updateSingleUser(roomId, userId, hand, roomSeatChangeCode);//更新玩家红包资格
            }
        }

    }

    /**
     * 成功站起座位后处理逻辑
     * @param roomId 房间id
     * @param userId 玩家id
     * @param hand 当前玩家累计手数
     * @param roomSeatChangeCode 变化类型
     */
    private void afterStandupRoom(int roomId,int userId,int hand,RoomSeatChangeCode roomSeatChangeCode){
        updateRoomSeat(roomId, roomSeatChangeCode);  //更新座位数
        room.getEventTrackService().addEventTrack(room.getRoomId(), userId, EventTrackingCode.STANDUP_ROOM_EVENT); //添加埋点
        if (room.getActivityConfig().isSkyRedWallet()) {
            room.getReaWalletService().updateSingleUser(roomId, userId, hand, roomSeatChangeCode);//更新玩家红包资格
        }
    }

    /**
     * 更新房间剩余座位数
     * @param roomId 房间id
     * @param updateType 1座位数增加 -1座位数减少
     */
    private static void updateRoomSeat(int roomId,RoomSeatChangeCode updateType){
        RoomSearchDao roomSearchDao = new RoomSearchDaoImpl();
        int updateFlag = roomSearchDao.updateRoomSeat(updateType.getCode(),roomId);
        logger.debug("updateRoomSeat updateFlag, " + updateFlag);
    }
}
