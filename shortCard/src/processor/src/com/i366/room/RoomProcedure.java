package com.i366.room;

import com.dzpk.commission.fee.TotalRoomFee;
import com.dzpk.commission.repositories.mysql.ClubTribeDao;
import com.dzpk.commission.repositories.mysql.impl.ClubTribeDaoImpl;
import com.dzpk.commission.repositories.mysql.model.ClubFeeConfig;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.repositories.redis.RedisService;
import com.dzpk.db.dao.GameHistoryDao;
import com.dzpk.db.dao.RoomDao;
import com.dzpk.db.dao.RoomManagerDao;
import com.dzpk.db.dao.UserInfoDao;
import com.dzpk.db.imp.GamehistoryImp;
import com.dzpk.db.imp.RoomDaoImpl;
import com.dzpk.db.imp.UserInfoDaoImp;
import com.dzpk.db.model.UserInfo;
import com.dzpk.dealer.Player;
import com.dzpk.jackpot.PlayerJpRewardDetail;
import com.dzpk.record.AnteAction;
import com.dzpk.record.GameReportModel;
import com.dzpk.record.LogManage;
import com.dzpk.record.ReplayDao;
import com.dzpk.weight.IUserWeightService;
import com.dzpk.weight.impl.UserWeightServiceImpl;
import com.dzpk.work.Task;
import com.dzpk.work.TaskConstant;
import com.dzpk.work.WorkThreadService;
import com.i366.cache.Cache;
import com.i366.constant.Constant;
import com.i366.constant.RoomFinishCode;
import com.i366.constant.RoomStartCode;
import com.i366.constant.StandUpRoomCode;
import com.i366.model.player.RoomPersion;
import com.i366.model.player.RoomPlayer;
import com.i366.model.pocer.Pocer;
import com.i366.model.room.CloseRoomMessage;
import com.i366.model.room.Room;
import com.i366.util.PublisherUtil;
import com.i366.util.RabbitMqUtil;
import com.i366.util.RoomUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.endpoints.room.RoomNodeManager;
import org.apache.logging.log4j.Logger;

import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


public class RoomProcedure {

    private Logger logger = LogUtil.getLogger(RoomProcedure.class);
    
    private RoomService roomService;
    private Room room;
    // 暂停任务队列 任务索引
    public Map<String, Task> delayTaskMap = new ConcurrentHashMap<String, Task>();
    
    private long wait;      // 个人操作时间
    private long wait2;     // 个人操作总时间，增加了2s缓冲

    private int roomPath;

    private int dy = -1;    // 本轮是否操作过
    private int dyCurrentSize;  // 本轮第一个操作的人位置
    private boolean isOpTimeOut = false;    // 操作人是否超时
    // 是否关闭房间线程
    public boolean forceCloseRoom = false;

    public int bipaiIndex = 0; // 比牌index，防止两次比牌

    public RoomProcedure(Room room) {
        this.room = room;
        roomService = room.getRoomService();
        wait = Cache.getLeveRoom(room.getRoomId(), room.getRoomPath()).getWait() * 1000;
        wait2 = wait + Constant.EXTRA_TIME;
        roomPath = room.getRoomPath();
    }

    public void checkAutoStart() {

        if(room.isAutoStartRoom() && room.getStatus() == 1){
            room.getRoomService().wait2Play(true, true);

            int count = 0;
            for (int i = 0; i < room.getRoomPersions().length; i++) {// 监控一旦有两个玩家已经坐下已经准备 则开始倒计时
                if (room.getRoomPersions()[i] != null
                        && room.getRoomPersions()[i].getNowcounma() >= room.getMinChip()) {
                    count++;
                }
            }

            logger.debug("牌局已设置满X人自动开局,rid={},目前玩家数={},开桌玩家数={}",room.getRoomId(),count,room.getAutoStartPlayerCount());
            if (count >= room.getAutoStartPlayerCount()) {
                RoomUtil.startRoom(room, RoomStartCode.AUTO_START);
                roomService.beginCountdown();
                room.setRoomStatus(2);
                status2();
            }
        }
    }

    /**
     * 初始化状态
     * 对应 站起用户 超时直接删除 坐下用户超时直接踢掉 /没有玩家则直接关闭该房间
     * @param finishType 1 牌局时间到且没有人在打牌 2 每手开始调用
     */
    public void status1(int finishType) {
        // 1 判断房间是否超时 超时则下发时间到的提示并关闭房间
        // 2 检查是否需要关闭所有房间(重启服务时用)
        logger.debug("MaxPlayTime=" + (room.getGameBeginTime() + room.getMaxPlayTime() * 60000) + 
                ", currentTimeMillis=" + System.currentTimeMillis() + ", roomStatus]= " + room.getRoomStatus() + " finishType: " + finishType);

        int roomStatus; //房间状态

            RoomDao roomDao = new RoomDaoImpl();
            if(finishType == 1){
                if (forceCloseRoom) {
                    roomStatus = roomDao.getRoomProgress(room.getRoomId());
                    if(roomStatus <= 3){
                        return;
                    }

                    room.setFinished(true);
                    timesup(RoomFinishCode.NORMAL_FINISH);
                    return;
                }
            }else{
                if (room.getGameBeginTime() + room.getMaxPlayTime() * 60000 <= System.currentTimeMillis() || forceCloseRoom) {
                    roomStatus = roomDao.getRoomProgress(room.getRoomId());
                    if(roomStatus <= 3){
                        return;
                    }

                    room.setFinished(true);
                    timesup(RoomFinishCode.NORMAL_FINISH);
                    return;
                }
            }

        if (room.getPlayType() == 1 && room.getStage() > 0) {// 过庄并且不是第一手
            room.getRoomService().wait2Play(false, true);
        } else {
            room.getRoomService().wait2Play(true, true);
        }

        int count = 0;
        for (int i = 0; i < room.getRoomPersions().length; i++) {// 监控一旦有两个玩家已经坐下已经准备 则开始倒计时
            if (room.getRoomPersions()[i] != null
                    && room.getRoomPersions()[i].getNowcounma() >= room.getMinChip()) {
                count++;
            }
        }


        logger.debug("status1,roomid={},now all players num={}",room.getRoomId(),count);
        if (count > 1) {
            roomService.beginCountdown();
            room.setRoomStatus(2);
            status2();
        }
    }
    
    // 倒计时(发每人两张牌)
    private void status2() {
        int count = 0;
        for (int i = 0; i < room.getRoomPersions().length; i++) {
            RoomPersion persion = room.getRoomPersions()[i];
            if (persion != null && persion.getNowcounma() >= room.getMinChip()) {
                count++;
            }
        }

        logger.debug("status2,roomid={},now all players num={}",room.getRoomId(),count);
        if (count < 2) {         // 判断如果当前座位少于2人 则需要重新等待
            logger.debug("status2 room players less 2,roomid={},now all players num={}",room.getRoomId(),count);
            room.setRoomStatus(1);
            return;
        }

        standUp(); //再次检查是否有积分不足的玩家

        roomService.sendedCards.clear(); //清空上一手牌的发牌记录
        roomService.beginGameOne(); // 通知开始发牌 (发两张底牌) 开局
    }
    
    public void beginStatus3() {
        logger.debug("beginStatus3 roomid: " + room.getRoomId());
        if (room.getRoomStatus() == 3) {// 发完牌直接进入3
            //进入第三轮清空前注额
            roomService.clearAuneCount();
            RoomPersion rp = room.getRoomPersions()[room.getCurrentNumber()];
            if (rp != null && !rp.isAutoOp()) { // 非托管模式
                room.setFirstOpSize(rp.getUserId());
                logger.debug("当前房间第一行动人,userid={},nickname={}",rp.getUserId(),rp.getUserInfo().getNikeName());
                Map<Integer, Object> map = new HashMap<Integer, Object>();
                map.put(1, room.getCurrentNumber());
                map.put(2, room.getRoomPersions()[room.getCurrentNumber()].getUserId());
                map.put(3, room.getBetIndex());
                Task task = new Task(TaskConstant.TASK_BET_TIMEOUT, map, room.getRoomId(), room.getRoomPath());
                RoomPlayer roomPlayer = room.getRoomPlayers().get(rp.getUserId());
                if (null != roomPlayer && roomPlayer.hasLeft()) { //玩家已经中途离开了
                    logger.debug("user has left early when beginStatus3,roomid={},userid={}",room.getRoomId(),roomPlayer.getUserId());
                }
                WorkThreadService.submitDelayTask(room.getRoomId(), task, (room.getOpTime() + Constant.FIRST_OPERATE_DELAY_TIME) * 1000);
                delayTaskMap.put(task.getId(), task);
            } else { //托管模式
                bet(3, 3);
            }
        }
    }
    
    // 投注完成分配奖金
    public void status7() {
        room.setCurrentNumber(-1);
        int deanCount = 0;
        for (int i = 4; i >= 0; i--) {
            if (room.getPocer()[i] != null) {
                break;
            }
            deanCount += 1;
        }

        // 保险模式被激活
        if (room.getInsuranceActive()) {
            try {
                // 没有亮牌的话先亮牌
                if (!room.getInsurer().getShowCards()) {
                    room.getRoomService().showCards();
                    room.getInsurer().setShowCards(true);
                }

                // 如果还有五张牌没发 先发三张 然后进入保险操作时间
                if (deanCount == 5) {

                    logger.debug("insuranceActive should send 3 cards !!! roomid: " + room.getRoomId());
                    roomService.sendCards(3, -1);
                    
                    // 牌谱记录（发完牌初始化）－－－－
                    room.getRoomReplay().setProgress(1);
                    if (room.getRoomReplay().getAnteAction() == null) {
                        room.getRoomReplay().setAnteAction(new AnteAction());
                        String card = room.getPocer()[0].getSize1() + " " + room.getPocer()[1].getSize1() + " "
                                + room.getPocer()[2].getSize1();
                        room.getRoomReplay().getAnteAction().setCard(card);
                        room.getRoomReplay().getAnteAction().setPot(room.getPoolChip());
                    }
                    // －－－－－

                    try {
                        Thread.sleep(3000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    checkInsure(bipaiIndex);
                    return;
                } else if (deanCount == 2) {
                    int insureCheckoutTime = 0;
                    if (room.getInsurer().getInsuranceCalCount() > 0) {
                        insureCheckoutTime = 2 * Constant.INSURE_FLOP_TIME;

                        logger.debug("insuranceActive should send turn card !!! roomid: " + room.getRoomId());
                        // 发一张公共牌
                        Map<Integer, Object> map = new HashMap<Integer, Object>();
                        map.put(1, 1);      // 需要发几张牌
                        map.put(2, -1);     // 当前操作玩家 -1无
                        map.put(3, 2);      // 翻牌进度 2
                        Task sendCardTask = new Task(TaskConstant.TASK_INSURANCE_SENDCARD, map, room.getRoomId(),
                                room.getRoomPath());
                        WorkThreadService.submitDelayTask(room.getRoomId(), sendCardTask, Constant.INSURE_FLOP_TIME);
                        delayTaskMap.put(sendCardTask.getId(), sendCardTask);

                        // 结账
                        Map<Integer, Object> map1 = new HashMap<Integer, Object>();
                        map1.put(1, bipaiIndex);      // 比牌index
                        map1.put(2, 2);               // turn card
                        Task checkoutTask = new Task(TaskConstant.TASK_INSURANCE_CHECKOUT, map1, room.getRoomId(),
                                room.getRoomPath());
                        WorkThreadService.submitDelayTask(room.getRoomId(), checkoutTask, insureCheckoutTime);
                        delayTaskMap.put(checkoutTask.getId(), checkoutTask);
                        /**
                         * 将10008任务中的逻辑判断是否进入保险模式移到10009保险结账任务中，防止出现未结完账就判断是否进入保险模式的场景，
                         * 从而解决all in卡主问题
                         *
                         * 保险结账时流程：
                         * 1 发下一张牌(延时1.5s)
                         * 2 上轮保险结账(延时3s Task_10009)
                         * 3 计算发完牌后是否进入保险模式(延时3s + 180ms Task_10008)
                         *
                         * 但是某种情况下(线程池的问题)10008会比10009先执行。此时holderMap(投保人的列表)还未清空，又回去判断是否进入保险模式，会拿上一手的数据
                         * 去判断，发现可以进入保险模式(因为上一手进入了)，又会给可以投保的人发送投保信息，但是该玩家点击买或者是不买都无效
                         * 至此，牌局卡主
                         *
                         * Question:
                         * 2个延时任务，为什么延时时间长的反而先执行，如果当时延时任务线程池已满，后面的延时任务加入堵塞队列，当线程池中有释放出来的线程时
                         * 根据什么来优先执行等待队列里的任务
                         */

                    } else {
                        checkInsure(bipaiIndex);
                    }
                    return;
                } else if (deanCount == 1) {
                    if (room.getInsurer().getInsuranceCalCount() > 0) {

                        logger.debug("insuranceActive should send river card !!! roomid: " + room.getRoomId());
                        // 发一张公共牌
                        Map<Integer, Object> map = new HashMap<Integer, Object>();
                        map.put(1, 1);      // 需要发几张牌
                        map.put(2, -1);     // 当前操作玩家 -1无
                        map.put(3, 2);      // 翻牌进度 2
                        Task sendCardTask = new Task(TaskConstant.TASK_INSURANCE_SENDCARD, map, room.getRoomId(),
                                room.getRoomPath());
                        WorkThreadService.submitDelayTask(room.getRoomId(), sendCardTask, Constant.FLOP_TIME);
                        delayTaskMap.put(sendCardTask.getId(), sendCardTask);

                        Map<Integer, Object> map1 = new HashMap<Integer, Object>();
                        map1.put(1, bipaiIndex);      // 比牌index
                        map1.put(2, 1);               // river card
                        // 结账
                        Task checkoutTask = new Task(TaskConstant.TASK_INSURANCE_CHECKOUT, map1, room.getRoomId(),
                                room.getRoomPath());
                        WorkThreadService.submitDelayTask(room.getRoomId(), checkoutTask, 2 * Constant.FLOP_TIME);
                        delayTaskMap.put(checkoutTask.getId(), checkoutTask);

                        // 结束保险状态
                        Task insureTask = new Task(TaskConstant.TASK_END_INSURANCE, null, room.getRoomId(),
                                room.getRoomPath());
                        WorkThreadService.submitDelayTask(room.getRoomId(), insureTask, 2 * Constant.FLOP_TIME + 1);
                        delayTaskMap.put(insureTask.getId(), insureTask);
                    } else {
                        checkInsure(bipaiIndex);
                    }

                    return;
                }
            } catch (Exception e) {
                logger.debug("insurance error:", e);
            }
        }

        int lastTurn = 0;    // 最后没有弃牌玩家数
        int turnIndex = -1;
        int allinNum = 0;
        RoomPersion[] rps = room.getRoomPersions();
        for (int i = 0; i < rps.length; i++) {
            if (rps[i] != null && rps[i].getStatus() != -2 && rps[i].getStatus() != -3
                    && rps[i].getStatus() != -4 && rps[i].getOnlinerType() == 1) {
                lastTurn++;
                turnIndex = i;
                if (rps[i].getStatus() == 3) {
                    allinNum++;
                }
            }
        }
        int bipaiTime = 0;
        if (lastTurn > 1) {
            int flopTime = 0;
            // 有人all in导致无需进行加注，此时先翻用户牌，然后在翻底牌
            if (deanCount > 0 || (lastTurn > 1 && lastTurn == allinNum)) {
                logger.debug("left " + deanCount + " cards to show");
                if (room.getInsurance() == 0 || (room.getInsurance() == 1 && room.getInsurer().getShowCards() == false)) {
                    room.getRoomService().showCards();
                }
                // 等待展示玩家牌
                flopTime = Constant.FLOP_TIME;
            }
            // 需要等待展示手牌时间
            if (flopTime > 0) {
                Map<Integer, Object> map = new HashMap<Integer, Object>();
                map.put(1, deanCount);      // 需要发几张牌
                map.put(2, -1);             // 当前操作玩家 -1无
                Task flopTask = new Task(TaskConstant.TASK_SEND_LEFT_CARDS, map, room.getRoomId(), room.getRoomPath());
                WorkThreadService.submitDelayTask(room.getRoomId(), flopTask, flopTime);
                delayTaskMap.put(flopTask.getId(), flopTask);
            } else {
                roomService.sendCards(deanCount, -1);
            }
            // 翻牌动画等待时间
            bipaiTime += flopTime + deanCount * Constant.FLOP_TIME;
            logger.debug("flop time: " + flopTime);
        }
        logger.debug("bipai time: " + bipaiTime);
        // 比牌前翻公共牌时间
        if (bipaiTime > 0) {
            Map<Integer, Object> map = new HashMap<Integer, Object>();
            map.put(1, lastTurn);       // 最后没有弃牌玩家数
            map.put(2, turnIndex);      // 如果只剩一个玩家，玩家座位号
            map.put(3, bipaiIndex);     // 比牌index
            Task bipaiTask = new Task(TaskConstant.TASK_BIPAI, map, room.getRoomId(), room.getRoomPath());
            WorkThreadService.submitDelayTask(room.getRoomId(), bipaiTask, bipaiTime);
        } else {
            bipai(lastTurn, turnIndex, bipaiIndex);
        }
    }

    // 比牌 -->通知比牌结果
    public void bipai(int lastTurn, int turnIndex, int bipaiIndex) {
        if (bipaiIndex < this.bipaiIndex || room.isStageFin()) {
            return;
        }
        // 比牌 -->通知比牌结果
        Object[] objs = new Object[0];
        try {
            objs = roomService.bipai(lastTurn, turnIndex);
        } catch (SQLException e) {
            e.printStackTrace();
        }
        int winCount = (Integer) objs[0];
        winCount = winCount < 1 ? 1 : winCount;

        this.room.setLastComparePocerTime(System.currentTimeMillis());//记录比牌完成时间戳

        // 停三秒用户动画播放时间 如果是比赛场地不延迟
        long totalTime = Constant.BIPAI_TIME;

        //JP牌局不播放未击中JP的大牌动画
        logger.debug("playbigcard roomid: " + room.getRoomId() + " isJackPotPlayBigCard: " + room.isJackPotPlayBigCard() + " isJackPot: " + room.isJackPot() + " bigcardNum: " + room.getBigCardNum());
        if(room.isJackPot()){
            if (room.getBigCardNum() == 1 && room.isJackPotPlayBigCard()) {
                totalTime += Constant.BIGCARD_SHOW_TIME;
            }
        }else{
            if (room.getBigCardNum() == 1) {
                totalTime += Constant.BIGCARD_SHOW_TIME;
            }
        }

        int canTurnTimes = 0;
        if (null == room.getPocer()[0]) {
            // 未开始发牌
            canTurnTimes = 3;
        } else if (null == room.getPocer()[3]){
            // 未开始发转牌
            canTurnTimes = 2;
        } else if (null == room.getPocer()[4]) {
            // 未开始发河牌
            canTurnTimes = 1;
        }
        this.bipaiIndex += 1;
        room.setCurrentNumber(-1);
        room.setStageFin(true);

        if (canTurnTimes > 0) {
            // 翻牌/转牌/河牌之前，本手牌就结束了
            StringBuilder sb = new StringBuilder("canTurnTimes=" + canTurnTimes + ", now cards=");
            for (Pocer item : room.getPocer()) {
                if (null != item) {
                    sb.append(item.getSize1() + " ");
                } else {
                    break;
                }
            }
            logger.debug(sb.toString());

            logger.debug("end hand when cards don't show all, roomStatus=" + room.getRoomStatus());
            room.setUserCanTurnTimes(canTurnTimes);
            Task task = new Task(TaskConstant.TASK_SERVER_PENDING_ROOM, null, room.getRoomId(), room.getRoomPath());
            WorkThreadService.submitDelayTask(room.getRoomId(), task, Constant.MAX_PENDING_TIME);
            delayTaskMap.put(task.getId(), task);
        } else {
            genNextGameTask(totalTime);
        }
    }

    // 启动一个TASK_NEXT_GAME延时任务
    public void genNextGameTask(long delay) {
        logger.debug("gen a new next game task");
        Task task = new Task(TaskConstant.TASK_NEXT_GAME, null, room.getRoomId(), room.getRoomPath());
        WorkThreadService.submitDelayTask(room.getRoomId(), task, delay);
        delayTaskMap.put(task.getId(), task);
    }

    // 本局收尾和下局开始
    public void nextGame() {
        // 记录每局结束时间
        room.getRoomReplay().setRoomRecord(room.getRoomRecord());
        room.getRoomReplay().setEndTime(System.currentTimeMillis() / 1000);

        try {
            ReplayDao.saveGameReplay(room, room.getRoomReplay());
        } catch (Exception e) {
            logger.error("save game replay error", e);
        }

        try {
            if(1 == room.getAheadLeaveMode() && null != room.getFeeService()){ //如果开启了提前离桌模式,需要检查是否需要给提前离桌的人结算
                room.getFeeService().saveAheadCommission(2);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("save ahead commission error", e);
        }

        roomService.processLeftUsers(); // 将上一局提前离开的玩家离开

        // 玩家可以通过钻石翻牌的数据进行重置
        room.setUserCanTurnTimes(0);
        room.setPendingTimeOut(false);
        room.setInRequest410(false);
        room.resetPocerType();
        room.setShowCardsUserId(-1);
        room.setShowCardsUserName("");

        logger.debug("MaxPlayTime=" + (room.getGameBeginTime() + room.getMaxPlayTime() * 60000) + ", currentTimeMillis=" + System.currentTimeMillis());
        // 1 判断房间是否超时 超时则下发时间到的提示并关闭房间
        // 2 检查是否需要关闭所有房间(重启服务时用)
        if (room.getGameBeginTime() + room.getMaxPlayTime() * 60000 <= System.currentTimeMillis()) {
            room.setFinished(true);
            timesup(RoomFinishCode.NORMAL_FINISH);
        } else {
            // 重新洗牌 开始新一局
            roomService.nextGame();
        }
    }

    // 将延时任务都暂停（设置为无效消息）
    public void pauseDelayTask() {
        for (Map.Entry<String, Task> delayTask : delayTaskMap.entrySet()) {
            Task task = delayTask.getValue();
            task.setValid(false);
            long passTime = room.getPauseBeginTime() - task.getBeginTime() > 0
                    ? room.getPauseBeginTime() - task.getBeginTime() : 0;
            long leftTime = task.getLeftTime() - passTime > 0 ? task.getLeftTime() - passTime : 0;
            task.setLeftTime(leftTime);
            logger.debug("taskId: " + task.getTaskId() + ", leftTime: " + leftTime);
        }
    }

    // 提交延时任务（设置为有效消息）
    public void continueDelayTask() {
        for (Map.Entry<String, Task> delayTask : delayTaskMap.entrySet()) {
            Task task = delayTask.getValue();
            task.setValid(true);
            task.setBeginTime(System.currentTimeMillis());
            WorkThreadService.submitDelayTask(room.getRoomId(), task, task.getLeftTime());
        }
    }

    public void status8() {
        // 暂停时间到
        Object[][] objs = {
                {60, 0, I366ClientPickUtil.TYPE_INT_1},        // 0 continue 1 pause 2 failed
                {130, 0, I366ClientPickUtil.TYPE_INT_4}
        };
        byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_ROOM_PAUSE);
        PublisherUtil.send(room,bytes);
        // 继续游戏
        roomService.continueRoom();
    }

    // 检查是否需要进入保险
    public void checkInsure(int bipaiIndex) {

        logger.debug("roomid: " + room.getRoomId() + "bipaiIndex: " + bipaiIndex);
        if (bipaiIndex < this.bipaiIndex) {
            return;
        }
        int insurance = room.getInsurer().calculate(room);

        logger.debug("roomid: " + room.getRoomId() + "insurance: " + insurance);
        if (insurance == 1) {
            // 进入保险
            room.setRoomStatus(9);
            room.setInsuranceBeginTime(System.currentTimeMillis());
        } else {
            // outs不在范围内 继续
            room.setRoomStatus(7);
        }
    }

    // 保险操作状态
    public void status9() {
        room.getInsurer().checkAllMustPay(room);
    }

    /**
     * @param onWheel   第几轮下注 + 2（当前状态）
     * @param deanCount 下完注发几张公共牌
     */
    private void bet(int onWheel, int deanCount) {
        logger.debug("bet info: roomid: " + room.getRoomId() + " deancount: " + deanCount);
        // 设置相关状态
        // 不用继续比牌了
        logger.debug("t3:" + getT3());
        if (getT3() == -989) {
            roomService.sendPoolInfo();         // 发送分池数据
            room.setRoomStatus(7);              // 直接比牌
            client();                           // 还原相关状态
            return;
        }

        // 如果需要盖牌
        if (onWheel == 6) {
            if(room.getMuckSwitch() == 1){//开了muck功能才有可能触发盖牌
                room.setMuck(true);
            }
        }

        // 判断最后一个操作玩家是否下注
        if (!room.isLastBetPlayerOp()) {
            if (room.getLastBetPlayer() == room.getCurrentNumber()) {
                logger.debug("last bet player operated!!!");
                room.setLastBetPlayerOp(true);
            } else if (room.getRoomPersions()[room.getLastBetPlayer()] == null
                    || room.getRoomPersions()[room.getLastBetPlayer()].getOnlinerType() == -1) {
                logger.debug("offline last bet player operated!!!");
                room.setLastBetPlayerOp(true);
            }
        }
        RoomPersion roomPersion = room.getRoomPersions()[room.getCurrentNumber()];
        logger.debug("current user: " + room.getCurrentNumber());

        // 异常，当前操作玩家为空
        if (roomPersion == null) {
            logger.error("exception!!!, current player is null!!!");
            int nextCurrent = room.getRoomService().nextCurrentNumber2();
            if (nextCurrent == -1) {
                room.setRoomStatus(7);
                room.setCurrentNumber(-1);
                client();
            }
            return;
        }

        if (roomPersion.isAutoOp()) {   // 托管
            // 能过牌自动帮过，不能过弃牌
            roomPersion.setBetStatus(2);
            if (roomPersion.getBetChouma() < room.getMaxChouma()) {
                roomPersion.setStatus(-3);
                room.getRoomService().setClientAction(roomPersion, 6, 0);
            } else {
                roomPersion.setStatus(1);
                room.getRoomService().setClientAction(roomPersion, 5, 0);
            }
            // 通知托管 或者弃牌
            newCurrent2(onWheel, deanCount, roomPersion, 1);
        }
        // 发现掉线 玩家设为弃牌状态 不用通知客户端 发送客户端协议为 该玩家离开的房间（已经操作）
        else if (roomPersion.getOnlinerType() == -1) {
            roomPersion.setBetStatus(2);
            roomPersion.setStatus(-2);
            // roomPersion.setClientStatus(6);
            room.getRoomService().setClientAction(roomPersion, 6, 0);
            newCurrent2(onWheel, deanCount, roomPersion, -1);
        } else if (roomPersion.getStatus() == -4) { // 玩牌站起操作，先弃牌再站起
            roomPersion.setBetStatus(2);
            roomPersion.setStatus(-3);
            // roomPersion.setClientStatus(6);
            room.getRoomService().setClientAction(roomPersion, 6, 0);
            newCurrent2(onWheel, deanCount, roomPersion, -1);
            roomPersion.setType(5);
            roomPersion.setStandupType(0);     // 主动站起
            room.getRoomService().zhanqi2(roomPersion,StandUpRoomCode.USER_SELF_PLAYING_GAME_STANDUP);
        } else {
            // 超时
            if (isOpTimeOut) {
                // 超时，能过牌自动帮过，不能过弃牌
                roomPersion.setBetStatus(2);
                if (roomPersion.getBetChouma() < room.getMaxChouma()) {
                    // roomPersion.setType(4);
                    roomPersion.setStatus(-3);
                    // roomPersion.setClientStatus(6);
                    room.getRoomService().setClientAction(roomPersion, 6, 0);
                    roomPersion.addTimeoutFoldOpTimes(1);
                } else {
                    roomPersion.setStatus(1);
                    // roomPersion.setClientStatus(5);
                    room.getRoomService().setClientAction(roomPersion, 5, 0);
                    roomPersion.addTimeoutCheckOpTimes(1);
                }
                // 通知托管 或者弃牌
                newCurrent2(onWheel, deanCount, roomPersion, 1);
                // 未超时
            } else {
                // 0等待下注     1下注并且比上次betChouma有增加     2筹码没有增加
                if (roomPersion.getBetStatus() > 0) {
                    newCurrent2(onWheel, deanCount, roomPersion, -1);
                }
            }

            // 连续4次自动check，或者连续3次自动fold，站起
            if (roomPersion.getTimeoutCheckOpTimes() > Constant.MAX_TIMEOUT_CHECK_TIME
                    || roomPersion.getTimeoutFoldOpTimes() >= Constant.MAX_TIMEOUT_FOLD_TIME) {
                long leftTime = RoomAutoOp.needAutoOp(room,roomPersion.getUserId());
                if (leftTime > 0) {  // 进入被动托管模式，ai不会进入此模式
                    roomPersion.setAutoOp(true);
                    Integer[] opStatus = RoomAutoOp.autoOpStatus(room);
                    // 通知其他人
                    PublisherUtil.send(room,RoomAutoOp.autoOpPus(0, 5, 0, false, false,
                            opStatus, ""), roomPersion.getUserId());
                    // 通知自己
                    PublisherUtil.sendByUserId(room,RoomAutoOp.autoOpPus(0, 1, (int) leftTime, false, false,
                            opStatus, ""), roomPersion.getUserId());
                } else {
                    roomPersion.setType(5);
                    roomPersion.setStandupType(1);  // 被动站起
                    room.getRoomService().zhanqi2(roomPersion, StandUpRoomCode.CHECK_FOLD_FULL_TIMES_STANDUP);
                }
            }
        }

        //更新累计操作次数
        room.updateCurrentRoomActionTimes();
        logger.debug("bet ===> action times===>" + room.getCurrentRoomActionTimes());

    }

    // 根据房间状态执行不同任务
    public void runByRoomStatus() {
        logger.debug("runByRoomStatus roomid: " + room.getRoomId() + "roomStatus:" + room.getRoomStatus());
        if (room.getRoomStatus() == 3) {
            // 第一轮投注(发三张公共牌)
            bet(3, 3);
        } else if (room.getRoomStatus() == 4) {
            // 第二轮投注(发第四张公共牌)
            bet(4, 1);
        } else if (room.getRoomStatus() == 5) {
            // 第三轮投注(发第五张公共牌)
            bet(5, 1);
        }   else if (room.getRoomStatus() == 6) {
            // 第四轮投注
            bet(6, 0);
        }
    }

    /**
     * 1、判断玩家家是否需要继续下注-1不需要操作了    1 需要操作但是不用发公共牌     2需要操作但是不用发公共牌
     * 2、发送玩家当前操作（掉线跟退出房间就当中弃牌操作）
     * 3、type == 2 发送公共牌
     *
     * @param onWheel
     * @param deanCount
     * @param roomPersion
     */
    private void newCurrent2(int onWheel, int deanCount, RoomPersion roomPersion, int z) {
        synchronized (roomService) {
            // 流转到下个人，betIndex+1
            room.incrBetIndex();
            setOpTimeOut(false);
            logger.debug("roomId: " + room.getRoomId() + ", userId: " + roomPersion.getUserId() + ", currentNumber: "
                    + room.getCurrentNumber() + ", seat: " + roomPersion.getSize());
            // 下个操作玩家是否还需要操作
            int i = nextCurrent(onWheel, deanCount, roomPersion);
            logger.debug("i: " + i + ", currentNumber: " + room.getCurrentNumber());
//          room.getRoomService().nextCurrentNumber2();

            int genzhu; //需要跟注的值
            if (i < 0) {
                genzhu = roomService.gameAction(roomPersion.getUserId(), roomPersion.getSize(),
                        roomPersion.getClientStatus(), roomPersion.getAnteNumber(), i);
                roomService.sendPoolInfo();
            } else {
                genzhu = roomService.gameAction(roomPersion.getUserId(), roomPersion.getSize(), roomPersion.getClientStatus(),
                        roomPersion.getAnteNumber(), i == 2 ? -1 : room.getCurrentNumber());

                if (i == 2) {
                    room.anteClean();   // 开始下一轮操作
                    roomService.sendPoolInfo();
                    roomService.sendCards2(deanCount, room.getCurrentNumber());
                    //每次发完牌需要重新allin的用户次数
                    room.setAllinCount(0);
                    //每发完一张牌需要重置翻牌后call/raise用户次数
                    room.setCallOrRaiseCount(0);

                    if(room.getRoomStatus() > 3){  //翻牌后记录第一个行动的人
                        room.setFirstOpFlopSize(room.getCurrentNumber());
                    }

                    room.setIsfirstAfNormal(-1);
                    room.setAtOperated(false);
                    room.setFirstAfBetChip(0);
                }

                RoomPersion rp = room.getRoomPersions()[room.getCurrentNumber()];
                if (rp != null && !rp.isAutoOp()) { // 非托管模式
                    // 设置超时任务
                    Map<Integer, Object> map = new HashMap<Integer, Object>();
                    map.put(1, room.getCurrentNumber());    // seat
                    map.put(2, room.getRoomPersions()[room.getCurrentNumber()].getUserId());    // userId
                    map.put(3, room.getBetIndex());         // bet index
                    Task task = new Task(TaskConstant.TASK_BET_TIMEOUT, map, room.getRoomId(), room.getRoomPath());
                    logger.debug("newCurrent2 begin bet timeout task, currentNumber" + room.getCurrentNumber() + " betIndex: " + room.getBetIndex());
                    RoomPlayer roomPlayer = room.getRoomPlayers().get(rp.getUserId());
                    if (null != roomPlayer && roomPlayer.hasLeft()) {  // 玩家已经中途离开了，延时时间缩短到 18 s
                        logger.debug("userId=" + rp.getUserId() + " has left early when newCurrent2");
                        WorkThreadService.submitDelayTask(room.getRoomId(), task, room.getOpTime() * 1000);
                    }
                    WorkThreadService.submitDelayTask(room.getRoomId(), task, room.getOpTime() * 1000);
                    delayTaskMap.put(task.getId(), task);

                } else {
                    runByRoomStatus();
                }

            }
            setT3(System.currentTimeMillis());
            room.setDelayTime(0);
            room.setPauseTime(0);
        }
    }

    /**
     * @param onWheel   第几轮下注
     * @param deanCount 发几张公告牌
     * @return -1不需要操作了    1 发送当前玩家操作     2本轮结束 发送当前玩家操作 + 下发公共牌
     */
    private int nextCurrent(int onWheel, int deanCount, RoomPersion roomPersion) {
        // 本轮是否需要继续下注
        boolean b = true;   // true 不需要
        // 0 1 2 -1三情况下需要继续下注
        int tem = 0;
        int tem2 = 0;
        int tem3 = 0;
        RoomPersion p2 = null;

        Set<Integer> lastUserSet = new HashSet<Integer>();
        lastUserSet.addAll(checkUserSet);
        checkUserSet.clear();

        for (RoomPersion p : room.getRoomPersions()) {
            if (p != null && p.getOnlinerType() == 1) {
                if (p.getStatus() == 0 || p.getStatus() == 1 || p.getStatus() == 2 || p.getStatus() == -1) {
                    p2 = p;
                    checkUserSet.add(p.getUserId());
                    tem++;
                    // 判断当前可操作玩家当中是否有 跟最大筹码不同的
                    if (room.getMaxChouma() != p.getBetChouma() || !room.isLastBetPlayerOp()) {
                        b = false;
                    }
                } else if (p.getStatus() == 3) {
                    tem2++;
                }

                if (p.getStatus() > 0) {
                    tem3++;
                }
            }
        }

        // 1.没有人需要操作
        // 2.当前操作玩家只剩一个并且为本人
        // 3.当前只有一人需要操作 并且他的筹码大于等于当前牌桌里面的最大筹码
        // 4.没有有效操作的玩家
        if (tem == 0 || tem == 1 && roomPersion.getUserId() == p2.getUserId()
                || tem == 1 && (p2.getBetChouma() >= room.getMaxChouma() || tem2 == 0)) {
            //  保险模式
            if (room.getInsurance() == 1 && tem3 > 1 && onWheel >= 3 && onWheel <= 5) {
                logger.debug("feng1 setInsuranceActive: true nextCurrent roomId: " + room.getRoomId());
                room.setInsuranceActive(true);
            }

            // allin，翻手牌
            if (tem3 > 1) {
                if (room.getRoomReplay().getAnteAction() == null) {
                    room.getRoomReplay().setAnteAction(new AnteAction());
                }
                room.getRoomReplay().getAnteAction().setAllin(1);
            }

            // 跳步的比牌 得把还没弃牌的人的 翻牌数 转牌数 河牌数补上
            room.getDealer().JumpAddCountData(tem3);

            room.setRoomStatus(7);                              //  直接比牌
            room.setCurrentNumber(-1);
            //  还原相关状态
            client();
            return -1;
        }

        //  第一轮大家都是让牌情况继续下注
        if (dy < 0) {
            dy = 1;
            wuxiaoCishu = tem - 1;
            // 用户弃牌，起始值需加一
            if (roomPersion.getStatus() == -3) {
                wuxiaoCishu++;
            }
            dyCurrentSize = roomPersion.getSize();
        }

        opUserSet.add(roomPersion.getUserId());
        if (wuxiaoCishu > 0) {
            Iterator<Integer> iterator = lastUserSet.iterator();
            while (iterator.hasNext()) {
                int userId = (int) iterator.next();
                // 上轮有本轮无，并且没有操作过的玩家
                if (!checkUserSet.contains(userId) && !opUserSet.contains(userId)) {
                    wuxiaoCishu--;
                }
            }
        }
        if (wuxiaoCishu > 0) {
            if (roomPersion.getBetStatus() == 2) {
                b = false;
                wuxiaoCishu--;
            } else {
                wuxiaoCishu = -1;
                checkUserSet.clear();
            }
        }

        logger.debug("userId: " + roomPersion.getUserId() + ", seatId: " + roomPersion.getSize() + "roomId:" + room.getRoomId());
        logger.debug("tem: " + tem + ", tem2: " + tem2 + ", b: " + b + ", dy: " + dy + ", wuxiaocishu:" + wuxiaoCishu);
        roomPersion.setBetStatus(0);

        //  本轮不需要继续下注
        if (b) {
            room.setCurrentNumber(room.getZhuangjiaNumber());   // 下一轮下注，设置当前为庄家
            room.getRoomService().nextCurrentNumber2();         // 寻找庄家下一个在线玩家为起始加注点，小盲在线默认小盲
            room.setRoomStatus(onWheel + 1);
            room.setCurrentMaxChouma(room.getMaxChouma());      // 当前轮一开始的最大投注筹码
            room.setPreAttackId(room.getAttackId());
            //  还原相关状态
            client();
            if (room.getRoomStatus() == 7) {
                room.setCurrentNumber(-1);
                return -1;
            } else {
                return 2;
            }
        } else {
            //  本轮需要继续下注
            room.getRoomService().nextCurrentNumber2();
            return 1;
        }
    }

    private int wuxiaoCishu = 0;
    // 需要check玩家集合
    private Set<Integer> checkUserSet = new HashSet<Integer>();
    // 本轮已操作玩家
    private Set<Integer> opUserSet = new HashSet<Integer>();

    /**
     * 还原相关状态
     */
    private void client() {
        dy = -1;
        dyCurrentSize = -1;
        checkUserSet.clear();
        opUserSet.clear();
        room.getRoomService().clearAuneCount();
        room.setBetFirstId(-1);
        room.setAttackId(-1);
    }

    // 当前操作玩家是否超时
    public boolean isTimeout() {
        logger.debug("room pause time: " + room.getPauseTime());
        if (room.getPauseTime() > 0) {    // 考虑暂停时间
            setT3(getT3() + room.getPauseTime());
            room.setPauseTime(0);
        }
        if (room.getDelayTime() > 0) {    // 考虑用户购买延时
            setT3(getT3() + room.getDelayTime());
            room.setDelayTime(0);      // 延迟需求需要的
        }
        long pastTime = System.currentTimeMillis() - getT3();
        logger.debug("past time: " + pastTime);
        if (pastTime > wait2) {
            logger.debug("time out!");
            return true;
        } else {
            logger.debug("not time out!");
            return false;
        }
    }
    /**
     * 牌局时间到 通知客户端 关闭房间
     */
    public synchronized void timesup(RoomFinishCode roomFinishCode) {
        logger.debug("roomid " + room.getRoomId() + " timesup now, roomFinishCode=" + roomFinishCode.toString());

        for (int i = 0; i < room.getRoomPersions().length; i++) { //防止牌局结束前一手仍有人带入积分,结算时未计算该部分
            RoomPersion rp = room.getRoomPersions()[i];
            if (rp != null && rp.getRequestChip() > 0) {
                RoomRequest.bindUserChip(rp, rp.getRequestChip(),room);
                rp.setRequestChip(0);
            }
        }
        // 生成战绩数据
        String sp = "@%";

        StringBuilder userNickName = new StringBuilder();//所有玩家的昵称
        StringBuilder userIdStr = new StringBuilder();  //所有玩家id

        /**
         * 土豪(带入最多),大鱼(盈利最少),鲨鱼(MVP 盈利最多)
         * 保存的排序规则 土豪/鲨鱼(MVP)/大鱼
         * 头像
         * 昵称
         * 盈亏
         */
        String userIcon = "";  //头像
        String userNick = "";  //昵称
        int richer = 0;
        int richerBring = 0;
        int shark = 0;
        int sharkWin = -9999999;
        int fish = 0;
        int fishLoss = 9999999;

        int status = 0;
        ArrayList<Object[]> records = LogManage.genGameReport(this.room);
        int maxPot = room.getDealer().getMaxPot();

        int allBring = 0;
        int playTime = (int) (System.currentTimeMillis() - room.getGameBeginTime()) / 1000;


        List<Integer> userIdArray = new ArrayList<>();
        List<Integer> plArray = new ArrayList<>();
        List<Integer> plFeeArray = new ArrayList<>();
        List<Integer> handArray = new ArrayList<>();
        List<Integer> bringArray = new ArrayList<>();

        List<Integer> jpBetArray = new ArrayList<>();//玩家投入Jackpot的总额
        List<Integer> jpRealBetArray = new ArrayList<>();//玩家投入jackpot的实际总额
        List<Integer> jpRewardPlArray = new ArrayList<>();//玩家击中jackpot的总额
        List<Double> jpRewardFeePlArray = new ArrayList<>();//玩家击中jackpot的服务费总额

        List<Integer> vpArray = new ArrayList<>();
        // 牌局结束战绩页
        List<Integer> jpUserIdArray = new ArrayList<>();
        List<Integer> jpTypeArray = new ArrayList<>();
        List<Integer> jpHandArray = new ArrayList<>();
        List<Integer> jpRewardArray = new ArrayList<>();
        StringBuilder jpNickNames = new StringBuilder();//击中玩家名
        CloseRoomMessage closeRoomMessage=new CloseRoomMessage();
        List<String> clubNameArray = new ArrayList<>();
        List<Integer> clubIdArray = new ArrayList<>();
        List<Integer> tribeIdArray = new ArrayList<>(); //同盟id(不重复的)
        List<Integer> tribeIdRepeatArray = new ArrayList<>(); //同盟id(重复的)
        List<Integer> clubFeeArray = new ArrayList<>(); //俱乐部服务费
        List<Integer> tribeFeeArray = new ArrayList<>(); //联盟服务费

        // 社区和盈亏集合
        HashMap<Integer, Integer> clubMap = new HashMap<>();
        // 保险
        List<Integer> buyInsuranceArray = new ArrayList<>();

        String plStr = "";
        String handStr = "";
        String bringStr = "";

        boolean includeOwnerId = false;
        int totalPL = 0; // 临时校正，水上、水下和保险不零和
        int totalJp = 0; // jp总和

        if (records.size() > 0) {

            Map<Integer,ClubFeeConfig> clubFeeConfigMap = new HashMap<>();

            if(room.getFeeService().getBringClubs().size() > 0) {
                /**
                 * 获取俱乐部分润配置
                 * **/
                ClubTribeDao clubTribeDao = new ClubTribeDaoImpl();

                StringBuilder bringClubIds = new StringBuilder();
                for(int clubId : room.getFeeService().getBringClubs()){
                    if(0 != clubId){
                        bringClubIds.append(clubId).append(",");
                    }
                }

                String clubIds = bringClubIds.substring(0,bringClubIds.toString().length() - 1);
                clubFeeConfigMap = clubTribeDao.getFeeConfig(clubIds);
            }
            for (int i = 0; i < records.size(); i++) {
                Object[] obj = records.get(i);
                int userId = (Integer) obj[0];
                int pl = (Integer) obj[1];
                int plFee = 0;
                int hand = (Integer) obj[2];
                int bring = (Integer) obj[3];
                int clubId = (Integer)obj[6];
                int insurance = (Integer)obj[7];
                int vpRate = (Integer)obj[10];
                int totalJpReward = 0;

                userIdStr.append(userId);
                if (i != records.size() - 1) {
                    userIdStr.append(",");
                    if (room.getOwner() == userId) {
                        includeOwnerId = true;
                    }
                }

                logger.debug("timesup getRoomId=" + room.getRoomId() + " userId=" + userId + " clubId:" + clubId);

                // 只有带入不为0的才会保存
                if (bring != 0) {
                    userIdArray.add(userId);
                    handArray.add(hand);
                    bringArray.add(bring);
                    vpArray.add(vpRate);

                    if(null==room.getJackpotService()){
                        jpBetArray.add(0);
                        jpRealBetArray.add(0);
                        jpRewardPlArray.add(0);
                        jpRewardFeePlArray.add(0d);
                    }else{
                        jpBetArray.add(room.getJackpotService().getTotalJpBet(userId));
                        jpRealBetArray.add(room.getJackpotService().getTotalRealJpBet(userId));
                        totalJpReward = room.getJackpotService().getTotalJpReward(userId);
                        jpRewardPlArray.add(totalJpReward);
                        jpRewardFeePlArray.add(room.getJackpotService().getTotalJpRewardFee(userId));
                    }


                    clubIdArray.add(clubId);
                    clubMap.put(clubId, (clubMap.get(clubId) == null ? 0 : clubMap.get(clubId)) + pl);
                    buyInsuranceArray.add(insurance);

                    if(null != room.getFeeService()) {

                        RoomPlayer roomPlayer = room.getRoomPlayers().get(userId);
                        if(null != roomPlayer){
                            Player player = room.getDealer().getPlayers().get(userId);
                            clubNameArray.add(player.getClubName()); //俱乐部名字
                            int tribeId = player.getTribeId();  //联盟id
                            if(!tribeIdArray.contains(tribeId)){
                                tribeIdArray.add(tribeId);
                            }
                            tribeIdRepeatArray.add(tribeId);

                            if(roomPlayer.isSettlement()){  //提前离座的玩家不需要再次计算服务费
                                plFee=(int) room.getDealer().getPlayers().get(userId).getPlFee();
                                pl = roomPlayer.getAheadLeavePl();
                            }else{
                                ClubFeeConfig clubFeeConfig = clubFeeConfigMap.get(clubId);
                                if(null != clubFeeConfig){
                                   plFee= (int) room.getDealer().getPlayers().get(userId).getPlFee();
                                }else{
                                    logger.warn("cannot find clubFeeConfig when calculating commission:rid={},uid={},clubId={}",
                                            room.getRoomId(),userId,clubId);
                                }
                            }
                        }
                    }

                    plArray.add(pl);
                    if (plFee!=0)plFeeArray.add(plFee);

                    // 判断土豪
                    if (richerBring <= bring) {
                        richerBring = bring;
                        richer = userId;
                    }

                    // 判断鲨鱼(MVP)
                    if (sharkWin <= pl) {
                        sharkWin = pl;
                        shark = userId;
                    }

                    // 判断大鱼
                    if (fishLoss >= pl) {
                        fishLoss = pl;
                        fish = userId;
                    }
                }


                totalPL += pl;
                if(null != room.getJackpotService())
                    totalJp += room.getJackpotService().getTotalJpBet(userId);



                // 所有用户总带入
                allBring += bring;

                //刪除redis的離桌
                RedisService.getRedisService().delRoomSettlementSet(userId,room.getRoomId());
            }


            for(Integer cid : clubMap.keySet()) { // 设置每个俱乐部的战绩的服务费值
                clubFeeArray.add(room.getClubFee().get(cid));
            }
            for(int trbieId: tribeIdArray){ // 设置每个联盟的战绩的服务费值
                tribeFeeArray.add(room.getTribeFee().get(trbieId));
            }
            try {
                if (!includeOwnerId) userIdStr.append(",").append(room.getOwner());
                UserInfoDao userInfoDao = new UserInfoDaoImp();
                Map<Integer, Object[]> userInfo = userInfoDao.getUserHeadNickname(userIdStr.toString());

                StringBuilder userIconBuilder = new StringBuilder();
                StringBuilder userNickBuilder = new StringBuilder();
                Object[] richerUser = userInfo.get(richer);
                Object[] sharkUser = userInfo.get(shark);
                Object[] fishUser = userInfo.get(fish);

                if(null != richerUser && richerUser.length >= 2){
                    userIconBuilder.append(richerUser[0]).append(sp);
                    userNickBuilder.append(richerUser[1]).append(sp);
                }
                if(null != sharkUser && sharkUser.length >= 2){
                    userIconBuilder.append(sharkUser[0]).append(sp);
                    userNickBuilder.append(sharkUser[1]).append(sp);
                }
                if(null != fishUser && fishUser.length >= 2){
                    userIconBuilder.append(fishUser[0]);
                    userNickBuilder.append(fishUser[1]);
                }

                userNick = userNickBuilder.toString();
                userIcon = userIconBuilder.toString();

                if(null != room.getJackpotService()) {
                    List<PlayerJpRewardDetail> list = room.getJackpotService().playerRewardDetailLst();
                    if(null != list && !list.isEmpty()) {
                        for (PlayerJpRewardDetail reward : list) {
                            jpUserIdArray.add(reward.getUserId());
                            jpTypeArray.add(reward.getProcerType());
                            jpHandArray.add(reward.getHandNo());
                            jpRewardArray.add(reward.getReward());
                            if (jpNickNames.toString().length() == 0) {
                                jpNickNames.append(reward.getNickName());
                            } else {
                                jpNickNames.append("@%").append(reward.getNickName());
                            }
                        }
                    }
                }

                for (int j = 0; j < userIdArray.size(); j++) {
                    Object[] userObj = userInfo.get(userIdArray.get(j));
                    userNickName.append(userObj[1]);
                    if (j != userIdArray.size() - 1) {
                        userNickName.append(sp);
                    }
                }

                if (room.getStage() > 0) {
                    // 战绩数据保存 包括统计数据
                    GameReportModel.saveGame(room, userNick, userIcon, userIdArray, plArray,plFeeArray,
                            handArray, bringArray, allBring,  userNickName.toString(),
                            maxPot, records, clubIdArray,clubFeeArray, clubMap, buyInsuranceArray,
                            jpBetArray,jpRealBetArray,jpRewardPlArray,jpRewardFeePlArray,
                            vpArray,tribeIdArray,tribeFeeArray,clubNameArray,tribeIdRepeatArray);
                    // 这里的数据是用户id第几位就是第几的获取
                    for (int i = 0; i < userIdArray.size(); i++) {
                        Integer userId = userIdArray.get(i);
                        Integer braing = bringArray.get(i) / 100;
                        Integer pl = plArray.get(i) / 100;
                        Integer sum = braing + pl;
                        if (room.getTiqianjiesunSet().contains(userId)) {
                            logger.info("用户:{},进行了提前结算，这里不需要进行结算", userId);
                        } else {
                            logger.info("玩-进行统计计算：userid:{},braing:{},pl:{},sum:{}", userId, braing, pl, sum);
                            if(room.getTribeRoomType() == 0){
                                room.getRoomService().getRoomDao().updateClubIntegral(room.getClubId(), userId, sum * 100);
                            }else {
                                room.getRoomService().getRoomDao().updateTribeIntegral(room.getClubId(), userId, sum * 100);
                            }
                        }
                    }
                    // 进行服务费保存
                    if(null != room.getFeeService()) {
                        room.getFeeService().saveCommission();
                    }
                } else {
                    for (int i = 0; i < userIdArray.size(); i++) {
                        Integer userId = userIdArray.get(i);
                        Integer braing = bringArray.get(i) / 100;
                        Integer pl = plArray.get(i) / 100;
                        Integer sum = braing + pl;
                        if (room.getTiqianjiesunSet().contains(userId)) {
                            logger.info("用户:{},进行了提前结算，这里不需要进行结算", userId);
                        } else {
                            logger.info("没有玩-进行统计计算：userid:{},braing:{},pl:{},sum:{}", userId, braing, pl, sum);
                            if(room.getTribeRoomType() == 0){
                                room.getRoomService().getRoomDao().updateClubIntegral(room.getClubId(), userId, sum * 100);
                            }else {
                                room.getRoomService().getRoomDao().updateTribeIntegral(room.getClubId(), userId, sum * 100);
                            }
                        }
                    }
                }

            } catch (SQLException e) {
                status = 1;
                logger.error("generate game report data error", e);
            } catch (Exception e) {
                status = 1;
                logger.error("game report exception: ", e);
            }
        } else {
            status = 1;
            logger.error("game report data is empty");
        }

        logger.info("user head:" + userIcon);
        logger.info("user Nick:" + userNick);
        logger.info("max pot:" + maxPot);
        logger.info("all userId str:" + userIdStr);
        logger.info("all nickname str:" + userNickName);
        logger.info("win lose str:" + plStr);
        logger.info("hand str:" + handStr);
        logger.info("bringin str:" + bringStr);
        logger.info("insurance:" + room.getInsurance());
        logger.info("insurance chip:" + room.getInsuranceChip());
        logger.info("vp:" + room.getVp());

        String[] allNickArr =  userNickName.toString().split("@%");
        List<Integer> userIdList = new ArrayList<>();
        List<Integer> plList = new ArrayList<>();
        List<Integer> handList = new ArrayList<>();
        List<Integer> bringList = new ArrayList<>();
        List<Integer> vpList = new ArrayList<>();
        StringBuilder userNickNameFinal = new StringBuilder();
        for (int i = 0; i < bringArray.size(); i++) { // 客户端只展示有带入的数据，减少数据量传输，过滤掉数据
            if (bringArray.get(i) > 0) {
                userIdList.add(userIdArray.get(i));
                plList.add(plArray.get(i));
                handList.add(handArray.get(i));
                bringList.add(bringArray.get(i));
                vpList.add(vpArray.get(i));
                userNickNameFinal.append(allNickArr[i]);
                if (i != allNickArr.length - 1) {
                    userNickNameFinal.append("@%");
                }
            }
        }

        for (Integer key : room.getAudMap().keySet()) {
            if (room.getAudMap().get(key) != null) {
                RoomPersion rp = room.getAudMap().get(key);
                UserInfo userInfo = rp.getUserInfo();
                if (userInfo == null) {
                    continue;
                }
                UserInfo onlineUserInfo = Cache.getOnlineUserInfo(rp.getUserId(), room.getRoomId());
                if (onlineUserInfo != null && rp.getOnlinerType() == 1) {

                    // 土豪/鲨鱼/大鱼 的备注
                    String[] topNickArr = userNick.split("@%");
                    String userNickFinal = topNickArr.length == 3 ? topNickArr[0] + "@%" + topNickArr[1] + "@%" + topNickArr[2] : "";
                    Object[][] objs = new Object[21][];
                    objs[0] = new Object[]  { 60,  status, I366ClientPickUtil.TYPE_INT_1};                  //  0成功 1异常
                    objs[1] = new Object[]  { 130, userNickFinal, I366ClientPickUtil.TYPE_STRING_UTF16};    //  昵称 土豪/鲨鱼(MVP)/大鱼
                    objs[2] = new Object[]  { 131, userIcon, I366ClientPickUtil.TYPE_STRING_UTF16 };        //  头像 土豪/鲨鱼(MVP)/大鱼
                    objs[3] = new Object[]  { 132, userIdList.toArray(new Integer[0]), I366ClientPickUtil.TYPE_INT_4_ARRAY };
                    objs[4] = new Object[]  { 133, plList.toArray(new Integer[0]), I366ClientPickUtil.TYPE_INT_4_ARRAY };
                    objs[5] = new Object[]  { 134, handList.toArray(new Integer[0]), I366ClientPickUtil.TYPE_INT_4_ARRAY };
                    objs[6] = new Object[]  { 135, bringList.toArray(new Integer[0]), I366ClientPickUtil.TYPE_INT_4_ARRAY };
                    objs[7] = new Object[]  { 136, allBring, I366ClientPickUtil.TYPE_INT_4 };
                    objs[8] = new Object[]  { 137, userNickNameFinal.toString(), I366ClientPickUtil.TYPE_STRING_UTF16 };
                    objs[9] = new Object[]  { 138, maxPot, I366ClientPickUtil.TYPE_INT_4 };
                    objs[10] = new Object[] { 139, room.getInsurance(), I366ClientPickUtil.TYPE_INT_4 };
                    objs[11] = new Object[] { 140, room.getInsuranceChip(), I366ClientPickUtil.TYPE_INT_4 };
                    objs[12] = new Object[] { 141, -1, I366ClientPickUtil.TYPE_INT_4 };
                    objs[13] = new Object[] { 142, playTime, I366ClientPickUtil.TYPE_INT_4 };
                    objs[14] = new Object[] { 143, jpNickNames.toString(), I366ClientPickUtil.TYPE_STRING_UTF16 };
                    objs[15] = new Object[] { 144, jpUserIdArray.toArray(new Integer[0]), I366ClientPickUtil.TYPE_INT_4_ARRAY };
                    objs[16] = new Object[] { 145, jpTypeArray.toArray(new Integer[0]), I366ClientPickUtil.TYPE_INT_4_ARRAY };
                    objs[17] = new Object[] { 146, jpRewardArray.toArray(new Integer[0]), I366ClientPickUtil.TYPE_INT_4_ARRAY };
                    objs[18] = new Object[] { 147, room.getVp(), I366ClientPickUtil.TYPE_INT_4 };
                    objs[19] = new Object[] { 148, vpArray.toArray(new Integer[0]), I366ClientPickUtil.TYPE_INT_4_ARRAY};
                    objs[20] = new Object[] { 149, room.getRoomChargeTotal() , I366ClientPickUtil.TYPE_INT_4};


                    byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_ENDING);
                    PublisherUtil.publisher(userInfo, bytes);
                }
            }
        }
        int chipFee=0;
        for (Map.Entry<Integer, Integer> integerIntegerEntry : room.getPlFee().entrySet()) {
            chipFee=chipFee+integerIntegerEntry.getValue();
        }
        int underwater=0;
        int water=0;
        for (Integer integer : plList) {
            if (integer>0)water=water+integer;

            if (integer<0)underwater=underwater+integer;
        }
        closeRoomMessage.setRoomId(room.getRoomId());
        closeRoomMessage.setRoomName(room.getName());
        closeRoomMessage.setRoomStage(room.getStage());
        closeRoomMessage.setTotal(allBring);
        closeRoomMessage.setFee(chipFee);
        closeRoomMessage.setJp(totalJp);
        closeRoomMessage.setUnderwater(underwater);
        closeRoomMessage.setWater(water);
        closeRoomMessage.setGameTime(playTime);
        closeRoomMessage.setInsurance(room.getInsuranceChip());
        closeRoomMessage.setRoomCloseTime(System.currentTimeMillis());
        RabbitMqUtil.sendGameCloseMessage(closeRoomMessage);
        try {
            room.getRoomService().closeRoom2(); // 关闭房间
            logger.info("close room2, roomid:{}", room.getRoomId());
            RoomNodeManager.getInstance().decreaseRuByDelta(1);
            logger.info("decreaseRuByDelta, roomid:{}", room.getRoomId());
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("======time's up, but close room error====== {},roomid:{}", e,room.getRoomId());
        }
        if (room.getClubRoomType() != 0){
            GameHistoryDao historyDao=new GamehistoryImp();
            historyDao.updateClubRoom(records);
        }
    }

    /**
     * 校正盈利。当水上、水下和保险不零和时，个人盈亏用带入减去当前筹码来校正。
     * @param records
     */
    private List<Map<String, Integer>> correctPL(ArrayList<Object[]> records) {
        List<Map<String, Integer>> list = new ArrayList<>();
        for (int i = 0; i < records.size(); i++) {
            Object[] obj = records.get(i);
            int userId = (Integer) obj[0];
            int pl = (Integer) obj[1];
            int hand = (Integer) obj[2];
            int bring = (Integer) obj[3];
            if (bring == 0) {
                continue;
            }
            int chouma = 0;
            RoomPersion roomPersion = room.getAudMap().get(userId);
            // 用户最后在房间
            if (roomPersion != null && roomPersion.getSize() >= 0
                    && roomPersion.getSize() < room.getPlayerCount()) {
                chouma = roomPersion.getNowcounma();
                logger.info("room id:" + room.getRoomId() + " userId=" + userId + " chouma=" + chouma);
            } else {    // 用户最后不在房间
                if(hand > 0){  //参与过牌局的获取离开房间的剩余筹码
                    chouma = room.getDealer().getUserLeftChip(userId);
                }else{         //未参与过牌局的获取带入的筹码
                    chouma = bring;
                }
                logger.info("room id:" + room.getRoomId() + " userId=" + userId + "not in room chouma=" + chouma);
            }

            logger.info("room id:" + room.getRoomId() + " userId=" + userId + " pl=" + pl + " bring=" + bring + " hand=" + hand + " chouma=" + chouma + " i=" + i);
            if (chouma - bring != pl) {
                Map<String, Integer> map = new HashMap<String, Integer>();
                map.put("index", i);
                map.put("userId", userId);
                map.put("bring", bring);
                map.put("hand", hand);
                map.put("pl", chouma - bring);
                list.add(map);
            }
        }
        return list;
    }

    /**
     * 检查是否还有积分不足的玩家
     */
    private void standUp() {
        for (int i = 0; i < room.getRoomPersions().length; i++) {
            if (room.getRoomPersions()[i] != null && room.getRoomPersions()[i].getNowcounma() < room.getMinChip()) {
                RoomPersion roomPersion = room.getRoomPersions()[i];
                roomService.zhanqi(roomPersion,StandUpRoomCode.NEXT_GAME_NOT_ENOUGH_CHIP_STANDUP);
            }
        }
    }
    
    /**
     * 个人倒计时
     *
     * @return
     */
    private long getT3() {
        return room.getT3();
    }

    private void setT3(long t3) {
        room.setT3(t3);
    }

    public boolean isOpTimeOut() {
        return isOpTimeOut;
    }

    public void setOpTimeOut(boolean isOpTimeOut) {
        this.isOpTimeOut = isOpTimeOut;
    }

}
