/*
 * $RCSfile: BiPai.java,v $
 * $Revision: 1.1  $
 * $Date: 2012-11-30  $
 *
 * Copyright (C) 2005 Bettem, Inc. All rights reserved.
 *
 * This software is the proprietary information of Bettem, Inc.
 * Use is subject to license terms.
 */
package com.i366.room;

import com.i366.model.player.RoomPersion;
import com.i366.model.pocer.Pocer;
import com.i366.model.pocer.PocerLink;

import java.util.ArrayList;

/**
 * <p>Title: BiPai</p> 
 * <p>Description: </p> 
 * <p>Copyright: Copyright (c) 2006</p> 
 * <AUTHOR>
 * @version 1.0
 */
public class BiPai {
	/**
	 * 最大牌 牌类型 和
	 * 
	 * @param pocer 需要比的七张牌
	 * @return
	 */
	public static Object[] zuidapai(Pocer[] pocer) {

		Object[] obj = new Object[3];
		Pocer[] p6 =  new Pocer[5];
		//1 对牌大小进行排序 重小到大
		for (int i = 0; i < pocer.length; i++) {
			for (int j = 0; j < pocer.length - i - 1; j++) {
				if (pocer[j].getSize2() > pocer[j + 1].getSize2()){// 大的往上冒,由小到大
					swap(pocer, j, j + 1);
				}
			}
		}
		int ss[] = {-1,-1,-1,-1};
		Pocer[] p1 = new Pocer[7];
		Pocer[] p2 = new Pocer[7];
		Pocer[] p3 = new Pocer[7];
		Pocer[] p4 = new Pocer[7];
		//
		Pocer[][] p11 = new Pocer[7][4];//扑克按相同数字分组 最多7组每组最多4张
		int ss3[] = {-1,-1,-1,-1,-1,-1,-1};
		int dui4 = 0;
		int dui4_tm1 = -1;
		int dui3 = 0;
		int dui3_tm1 = -1; //第一个三对
		int dui3_tm2 = -1;//第二个三对
		int dui2 = 0;
		int dui2_tm1 = -1; //
		int dui2_tm2 = -1;
		int dui2_tm3 = -1; //
		//分颜色
		for (int i = 0; i < pocer.length; i++) {
			if (pocer[i].getType() == 1) {
				ss[0] = ss[0] + 1;
				p1[ss[0]] = pocer[i];
			}else if (pocer[i].getType() == 2) {
				ss[1] = ss[1] + 1;
				p2[ss[1]] = pocer[i];
			}else if (pocer[i].getType() == 3) {
				ss[2] = ss[2] + 1;
				p3[ss[2]] = pocer[i];
			}else if (pocer[i].getType() == 4) {
				ss[3] = ss[3] + 1;
				p4[ss[3]] = pocer[i];
			}
		}
		int bj = -1;
		if (ss[0] > 3) {
			bj = 0;
		}
		if (ss[1] > 3) {
			bj = 1;
		}
		if (ss[2] > 3) {
			bj = 2;
		}
		if (ss[3] > 3) {
			bj = 3;
		}
		//是个同花
		if (bj != -1) {
			Pocer[] p5 = null;
			if (bj == 0) {
				p5 = p1;
			}else if (bj == 1) {
				p5 = p2;
			}else if (bj == 2) {
				p5 = p3;
			}else if (bj == 3) {
				p5 = p4;
			}

			//看看是不是顺子 如果是顺子就是同花顺 如果是A-5的顺子也要考虑
			int bj2 = 0;
			int bj3 = -1;
			int insize = ss[bj];
			for (int k = 0 ; k < insize ; k ++) {
				int p_ = p5[insize - k].getSize2() - p5[insize - k -1].getSize2();
				if (p_ == 1 || (p_ == 9 && p5[insize - k].getSize2() == 14)) {
					p6[4 - bj2] = p5[insize - k];
					p6[3 - bj2] = p5[insize - k - 1];
					if (bj2 == 3) {
						bj3 = k;
						break;
					}
					bj2 ++;
				}else {
					bj2 = 0;
				}
			}
			
			//同花顺
			if (bj3 != -1) {
//				p6[0] = p5[ss[bj3]];
				if (p6[4].getSize2() == 14 && p6[3].getSize2() != 13) {
					Pocer[] p6_ = {null,null,null,null,null};
					p6_[0] = p6[4];
					p6_[1] = p6[0];
					p6_[2] = p6[1];
					p6_[3] = p6[2];
					p6_[4] = p6[3];
					p6 = p6_;
				}
				if (p6[4].getSize2() == 14 && p6[3].getSize2() == 13) {
				    String pokers = "";
				    for (int t = 0; t < 5; t++) {
				        pokers += p6[t].getSize2() + " ";
				    }
				    System.out.println(pokers);
					obj[1] = PocerLink.POCER_TYPE[0];
				}else {
					obj[1] = PocerLink.POCER_TYPE[1];
				}
				obj[0] = p6;
				obj[2] = p6[0].getSize2() + p6[1].getSize2() + p6[2].getSize2() + p6[3].getSize2() + p6[4].getSize2();
				return obj;
			}
			
			if (p5[ss[bj]].getSize2() == 14 && p5[0].getSize2() == 2 && p5[1].getSize2() == 3 && p5[2].getSize2() == 4 && p5[3].getSize2() == 5) {
				p6[0] = p5[ss[bj]];
				p6[1] = p5[0];
				p6[2] = p5[1];
				p6[3] = p5[2];
				p6[4] = p5[3];
				obj[0] = p6;
				obj[1] = PocerLink.POCER_TYPE[1];
				obj[2] = 15;
				return obj;
			}
			//是个同花但是不是同花顺
			p6[4] = p5[ss[bj]];
			p6[3] = p5[ss[bj] - 1];
			p6[2] = p5[ss[bj] - 2];
			p6[1] = p5[ss[bj] - 3];
			p6[0] = p5[ss[bj] - 4];
			obj[0] = p6;
			obj[1] = PocerLink.POCER_TYPE[3];
			obj[2] = p6[0].getSize2() + p6[1].getSize2() + p6[2].getSize2() + p6[3].getSize2() + p6[4].getSize2();
			return obj;
		}
		for (int e = 0 ; e < pocer.length ; e ++) {
			for (int f = 0 ; f < 7 ; f ++) {
				if (ss3[f] == -1) {
					ss3[f] = 0;
					p11[f][0] = pocer[e];
					break;
				}
				if (p11[f][0].getSize2() == pocer[e].getSize2()) {
					ss3[f] = ss3[f] + 1;
					p11[f][ss3[f]] = pocer[e];
					if (ss3[f] == 3) {//4张相同
						dui4 ++;
						dui4_tm1 = f;
					}
					if (ss3[f] == 2) {//3张相同
						dui3 ++;
						if (dui3 == 1) {
							dui3_tm1 = f;
						}else {
							dui3_tm2 = f;
						}
					}
					if (ss3[f] == 1) {//2张相同
						dui2 ++;
						if (dui2 == 1) {
							dui2_tm1 = f;
						}else if (dui2 == 2) {
							dui2_tm2 = f;
						}else {
							dui2_tm3 = f;
						}
					}
					break;
				}
			}
		}
		//葫芦
		if (dui3 > 0 && dui2 >= 2) {
			if (dui3 == 2) {
				if (p11[dui3_tm1][0].getSize2() > p11[dui3_tm2][0].getSize2()) {
					p6[0] = p11[dui3_tm1][0];
					p6[1] = p11[dui3_tm1][1];
					p6[2] = p11[dui3_tm1][2];
					p6[3] = p11[dui3_tm2][1];
					p6[4] = p11[dui3_tm2][2];
				}else {
					p6[0] = p11[dui3_tm2][0];
					p6[1] = p11[dui3_tm2][1];
					p6[2] = p11[dui3_tm2][2];
					p6[3] = p11[dui3_tm1][1];
					p6[4] = p11[dui3_tm1][2];
				}
			}else {
				p6[0] = p11[dui3_tm1][0];
				p6[1] = p11[dui3_tm1][1];
				p6[2] = p11[dui3_tm1][2];
				if (dui2 == 2) {
					if (dui2_tm1 == dui3_tm1) {
						p6[3] = p11[dui2_tm2][0];
						p6[4] = p11[dui2_tm2][1];
					}else {
						p6[3] = p11[dui2_tm1][0];
						p6[4] = p11[dui2_tm1][1];
					}
				}else {
					if (dui2_tm1 == dui3_tm1) {
						if (p11[dui2_tm2][0].getSize2() > p11[dui2_tm3][0].getSize2()) {
							p6[3] = p11[dui2_tm2][0];
							p6[4] = p11[dui2_tm2][1];
						}else {
							p6[3] = p11[dui2_tm3][0];
							p6[4] = p11[dui2_tm3][1];
						}
					} else if (dui2_tm2 == dui3_tm1) {
						if (p11[dui2_tm1][0].getSize2() > p11[dui2_tm3][0].getSize2()) {
							p6[3] = p11[dui2_tm1][0];
							p6[4] = p11[dui2_tm1][1];
						}else {
							p6[3] = p11[dui2_tm3][0];
							p6[4] = p11[dui2_tm3][1];
						}
					} else {
						if (p11[dui2_tm1][0].getSize2() > p11[dui2_tm2][0].getSize2()) {
							p6[3] = p11[dui2_tm1][0];
							p6[4] = p11[dui2_tm1][1];
						}else {
							p6[3] = p11[dui2_tm2][0];
							p6[4] = p11[dui2_tm2][1];
						}
					}
				}
			}
			obj[0] = p6;
			obj[1] = PocerLink.POCER_TYPE[4];
			obj[2] =  p6[0].getSize2() + p6[1].getSize2() + p6[2].getSize2() + p6[3].getSize2() + p6[4].getSize2();
			return obj;
		}
		//四条
		if (dui4 != 0) {
			p6[0] = p11[dui4_tm1][0];
			p6[1] = p11[dui4_tm1][1];
			p6[2] = p11[dui4_tm1][2];
			p6[3] = p11[dui4_tm1][3];
			if (p6[0].getSize2() == pocer[6].getSize2()) {
				p6[4] = pocer[2];
			}else {
				p6[4] = pocer[6];
			}
			obj[0] = p6;
			obj[1] = PocerLink.POCER_TYPE[2];
			obj[2] = p6[0].getSize2() + p6[1].getSize2() + p6[2].getSize2() + p6[3].getSize2() + p6[4].getSize2();
			return obj;
		}

		
		//顺子
		int bj7 = 0;
		int bj8 = -1;
		Pocer[] target =new Pocer[pocer.length];
		if (pocer[6].getSize2()==14){
			System.arraycopy(pocer, 0, target, 1,
					pocer.length-1);
			target[0]=pocer[6];
		}else {
			target = pocer;
		}
		for (int i_ = 0 ; i_ < target.length - 1 ; i_ ++) {
			int pocer1 = target[6 - i_].getSize2();
			int pocer2 = target[5 - i_].getSize2();
			if ((pocer1 - pocer2 == 1)||( pocer2==14 && pocer1 - pocer2 == -8)) {
				p6[4 - bj7] = target[6 - i_];
				p6[3 - bj7] = target[5 - i_];
				if (bj7 >= 3) {
					bj8 = i_;
					break;
				}
				bj7 ++;
			}else {
				if (pocer1 - pocer2 != 0) {
					bj7 = 0;
				}
			}
		}
		if (bj8 != -1) {
			obj[0] = p6;
			obj[1] = PocerLink.POCER_TYPE[5];
			obj[2] =  p6[0].getSize2() + p6[1].getSize2() + p6[2].getSize2() + p6[3].getSize2() + p6[4].getSize2();
			return obj;
		}
		
		// A - 5 的顺子
		if (pocer[6].getSize2() == 14 && pocer[0].getSize2() == 2) {
			p6[0] = pocer[6];
			int bj9 = 1;
			for (int i_ = 0 ; i_ < pocer.length - 2 ; i_ ++) {
				if (pocer[i_ + 1].getSize2() - pocer[i_].getSize2() == 1) {
					p6[bj9] = pocer[i_];
					p6[bj9 + 1] = pocer[i_ + 1];
					if (bj9 == 3) {
						bj9 = 4;
						break;
					}
					bj9 ++;
				}else {
					if (pocer[i_ + 1].getSize2() - pocer[i_].getSize2() == 0) {
						continue;
					}else {
						bj9 = 1;
						break;
					}
				}
			}
			if (bj9 == 4) {
				obj[0] = p6;
				obj[1] = PocerLink.POCER_TYPE[5];
				obj[2] = 15;
				return obj;
			}
		}
		
		//三条
		if (dui3 == 1) {
			p6[0] = p11[dui3_tm1][0];
			p6[1] = p11[dui3_tm1][1];
			p6[2] = p11[dui3_tm1][2];
			if (pocer[6].getSize2() != p6[0].getSize2() && pocer[5].getSize2() != p6[0].getSize2()) {
				p6[3] = pocer[5];
				p6[4] = pocer[6];
			}else if (pocer[6].getSize2() != p6[0].getSize2()) {
				p6[3] = pocer[2];
				p6[4] = pocer[6];
			}else {
				p6[3] = pocer[2];
				p6[4] = pocer[3];
			}
			obj[0] = p6;
			obj[1] = PocerLink.POCER_TYPE[6];
			obj[2] =  p6[0].getSize2() + p6[1].getSize2() + p6[2].getSize2() + p6[3].getSize2() + p6[4].getSize2();
			return obj;
		}
		
		//两对
		if (dui2 == 2) {			
			p6[0] = p11[dui2_tm1][0];
			p6[1] = p11[dui2_tm1][1];
			p6[2] = p11[dui2_tm2][0];
			p6[3] = p11[dui2_tm2][1];
			for (int i = 6 ; i >= 0 ; i --) {
				if (p6[0].getSize2() != pocer[i].getSize2() && p6[2].getSize2() != pocer[i].getSize2()) {
					p6[4] = pocer[i];
					break;
				}
			}
			obj[0] = p6;
			obj[1] = PocerLink.POCER_TYPE[7];
			obj[2] =  p6[0].getSize2() + p6[1].getSize2() + p6[2].getSize2() + p6[3].getSize2() + p6[4].getSize2();
			return obj;
		}
		//三对
		if (dui2 == 3) {
			p6[0] = p11[dui2_tm2][0];
			p6[1] = p11[dui2_tm2][1];
			p6[2] = p11[dui2_tm3][0];
			p6[3] = p11[dui2_tm3][1];
			for (int i = 6 ; i >= 0 ; i --) {
				if (p6[0].getSize2() != pocer[i].getSize2() && p6[2].getSize2() != pocer[i].getSize2()) {
					p6[4] = pocer[i];
					break;
				}
			}
			obj[0] = p6;
			obj[1] = PocerLink.POCER_TYPE[7];
			obj[2] =  p6[0].getSize2() + p6[1].getSize2() + p6[2].getSize2() + p6[3].getSize2() + p6[4].getSize2();
			return obj;
		}
		
		//一对
		if (dui2 == 1) {
			p6[0] = p11[dui2_tm1][0];
			p6[1] = p11[dui2_tm1][1];
			int i_ = 4;
			for (int i = 6 ; i >= 0 ; i --) {
				if (p6[0].getSize2() != pocer[i].getSize2()) {
					p6[i_] = pocer[i];
					i_ --;
					if (i_ == 1) {
						break;
					}
				}
			}
			obj[0] = p6;
			obj[1] = PocerLink.POCER_TYPE[8];
			obj[2] =  p6[0].getSize2() + p6[1].getSize2() + p6[2].getSize2() + p6[3].getSize2() + p6[4].getSize2();
			return obj;
		}
		//高牌
		p6[0] = pocer[2];
		p6[1] = pocer[3];
		p6[2] = pocer[4];
		p6[3] = pocer[5];
		p6[4] = pocer[6];
		obj[0] = p6;
		obj[1] = PocerLink.POCER_TYPE[9];
		obj[2] =  p6[0].getSize2() + p6[1].getSize2() + p6[2].getSize2() + p6[3].getSize2() + p6[4].getSize2();
		return obj;
	}
	
    protected static void swap(Object[] array, int i, int j) {
    	Object temp;
        temp = array[i];
        array[i] = array[j];
        array[j] = temp;
    }
    
	/**
	 * 两对牌对比查看那个大 -1一样大 0：rp1大       1：rp2大
	 * 
	 * @param rp1
	 * @param rp2
	 * @return
	 */
	public static int bipai2(RoomPersion rp1 ,RoomPersion rp2) {
		//如果allin了 掉线则牌也有效
		if (rp1.getOnlinerType() == -1 && rp1.getStatus() != 3 && rp2.getOnlinerType() == -1 && rp2.getStatus() != 3) {
			return -1;
		}
		if (rp1.getOnlinerType() == -1 && rp1.getStatus() != 3) {
			return 1;
		}
		if (rp2.getOnlinerType() == -1 && rp2.getStatus() != 3) {
			return 0;
		}
		
		if ((rp1.getStatus() == -2 || rp1.getStatus() == -3) && (rp2.getStatus() == -2 || rp2.getStatus() == -3)) {
			return -1;
		}else if ((rp1.getStatus() == -2 || rp1.getStatus() == -3)) {
			return 1;
		}else if ((rp2.getStatus() == -2 || rp2.getStatus() == -3)){
			return 0;
		}
		if (rp1.getPocerType() == rp2.getPocerType()) {
			switch (rp1.getPocerType()) {
			case 1://皇家同花
				return -1;
			case 2: //同花顺
				if (rp1.getZuidaPocers()[4].getSize2() > rp2.getZuidaPocers()[4].getSize2()) {
					return 0;
				}else if (rp2.getZuidaPocers()[4].getSize2() > rp1.getZuidaPocers()[4].getSize2()) {
					return 1;
				}else {
					return -1;
				}
			case 3://四条
				if (rp1.getZuidaPocers()[0].getSize2() > rp2.getZuidaPocers()[0].getSize2()) {
					return 0;
				}else if ((rp2.getZuidaPocers()[0].getSize2() > rp1.getZuidaPocers()[0].getSize2())){
					return 1;
				}
				if (rp1.getZuidaPocers()[4].getSize2() > rp2.getZuidaPocers()[4].getSize2()) {
					return 0;
				}else if ((rp2.getZuidaPocers()[4].getSize2() > rp1.getZuidaPocers()[4].getSize2())){
					return 1;
				}
				return -1;
			case 4://同花
				for (int i = 0 ; i < 5 ; i ++) {
					if (rp1.getZuidaPocers()[4-i].getSize2() > rp2.getZuidaPocers()[4-i].getSize2()) {
						return 0;
					}else if (rp2.getZuidaPocers()[4-i].getSize2() > rp1.getZuidaPocers()[4-i].getSize2()){
						return 1;
					}
				}
				return -1;
			case 5://葫芦
				if (rp1.getZuidaPocers()[0].getSize2() > rp2.getZuidaPocers()[0].getSize2()) {
					return 0;
				}else if (rp2.getZuidaPocers()[0].getSize2() > rp1.getZuidaPocers()[0].getSize2()) {
					return 1;
				}
				if (rp1.getZuidaPocers()[4].getSize2() > rp2.getZuidaPocers()[4].getSize2()) {
					return 0;
				}else if ((rp2.getZuidaPocers()[4].getSize2() > rp1.getZuidaPocers()[4].getSize2())){
					return 1;
				}
				return -1;
			case 6://顺子
				if (rp1.getZuidaPocers()[4].getSize2() > rp2.getZuidaPocers()[4].getSize2()) {
					return 0;
				}else if (rp2.getZuidaPocers()[4].getSize2() > rp1.getZuidaPocers()[4].getSize2()) {
					return 1;
				}else {
					return -1;
				}
			case 7://三条
				if (rp1.getZuidaPocers()[1].getSize2() > rp2.getZuidaPocers()[1].getSize2()) {
					return 0;
				}else if (rp2.getZuidaPocers()[1].getSize2() > rp1.getZuidaPocers()[1].getSize2()) {
					return 1;
				}
				if (rp1.getZuidaPocers()[4].getSize2() > rp2.getZuidaPocers()[4].getSize2()) {
					return 0;
				}else if (rp2.getZuidaPocers()[4].getSize2() > rp1.getZuidaPocers()[4].getSize2()) {
					return 1;
				}
				if (rp1.getZuidaPocers()[3].getSize2() > rp2.getZuidaPocers()[3].getSize2()) {
					return 0;
				}else if (rp2.getZuidaPocers()[3].getSize2() > rp1.getZuidaPocers()[3].getSize2()) {
					return 1;
				}
				return -1;
			case 8://两对
				if (rp1.getZuidaPocers()[2].getSize2() > rp2.getZuidaPocers()[2].getSize2()) {
					return 0;
				}else if (rp2.getZuidaPocers()[2].getSize2() > rp1.getZuidaPocers()[2].getSize2()) {
					return 1;
				}
				if (rp1.getZuidaPocers()[0].getSize2() > rp2.getZuidaPocers()[0].getSize2()) {
					return 0;
				}else if (rp2.getZuidaPocers()[0].getSize2() > rp1.getZuidaPocers()[0].getSize2()) {
					return 1;
				}
				if (rp1.getZuidaPocers()[4].getSize2() > rp2.getZuidaPocers()[4].getSize2()) {
					return 0;
				}else if (rp2.getZuidaPocers()[4].getSize2() > rp1.getZuidaPocers()[4].getSize2()) {
					return 1;
				}
				return -1;
			case 9:
				if (rp1.getZuidaPocers()[0].getSize2() > rp2.getZuidaPocers()[0].getSize2()) {
					return 0;
				}else if (rp2.getZuidaPocers()[0].getSize2() > rp1.getZuidaPocers()[0].getSize2()) {
					return 1;
				}
				if (rp1.getZuidaPocers()[4].getSize2() > rp2.getZuidaPocers()[4].getSize2()) {
					return 0;
				}else if (rp2.getZuidaPocers()[4].getSize2() > rp1.getZuidaPocers()[4].getSize2()) {
					return 1;
				}
				if (rp1.getZuidaPocers()[3].getSize2() > rp2.getZuidaPocers()[3].getSize2()) {
					return 0;
				}else if (rp2.getZuidaPocers()[3].getSize2() > rp1.getZuidaPocers()[3].getSize2()) {
					return 1;
				}
				if (rp1.getZuidaPocers()[2].getSize2() > rp2.getZuidaPocers()[2].getSize2()) {
					return 0;
				}else if (rp2.getZuidaPocers()[2].getSize2() > rp1.getZuidaPocers()[2].getSize2()) {
					return 1;
				}
				return -1;
			case 10:
				if (rp1.getZuidaPocers()[4].getSize2() > rp2.getZuidaPocers()[4].getSize2()) {
					return 0;
				}else if (rp2.getZuidaPocers()[4].getSize2() > rp1.getZuidaPocers()[4].getSize2()) {
					return 1;
				}
				if (rp1.getZuidaPocers()[3].getSize2() > rp2.getZuidaPocers()[3].getSize2()) {
					return 0;
				}else if (rp2.getZuidaPocers()[3].getSize2() > rp1.getZuidaPocers()[3].getSize2()) {
					return 1;
				}
				if (rp1.getZuidaPocers()[2].getSize2() > rp2.getZuidaPocers()[2].getSize2()) {
					return 0;
				}else if (rp2.getZuidaPocers()[2].getSize2() > rp1.getZuidaPocers()[2].getSize2()) {
					return 1;
				}
				if (rp1.getZuidaPocers()[1].getSize2() > rp2.getZuidaPocers()[1].getSize2()) {
					return 0;
				}else if (rp2.getZuidaPocers()[1].getSize2() > rp1.getZuidaPocers()[1].getSize2()) {
					return 1;
				}
				if (rp1.getZuidaPocers()[0].getSize2() > rp2.getZuidaPocers()[0].getSize2()) {
					return 0;
				}else if (rp2.getZuidaPocers()[0].getSize2() > rp1.getZuidaPocers()[0].getSize2()) {
					return 1;
				}
				return -1;
			}
			return -1;
		}else if (rp1.getPocerType() < rp2.getPocerType()) {
			return 0;
		}else {
			return 1;
		}
	}
	
	// 牌型判断算法，适合5-7张牌
	public static Object[] zuidapai1(Pocer[] pocer) {
        Object[] obj = new Object[3];
        Pocer[] p6 =  new Pocer[5];
        //1 对牌大小进行排序 重小到大
        for (int i = 0; i < pocer.length; i++) {
            for (int j = 0; j < pocer.length - i - 1; j++) {
                if (pocer[j].getSize2() > pocer[j + 1].getSize2()){// 大的往上冒,由小到大
                    swap(pocer, j, j + 1);
                }
            }
        }
        int ss[] = {-1, -1, -1, -1};
        Pocer[] p1 = new Pocer[7];
        Pocer[] p2 = new Pocer[7];
        Pocer[] p3 = new Pocer[7];
        Pocer[] p4 = new Pocer[7];
        //分颜色
        for (int i = 0; i < pocer.length; i++) {
            if (pocer[i].getType() == 1) {
                ss[0] = ss[0] + 1;
                p1[ss[0]] = pocer[i];
            } else if (pocer[i].getType() == 2) {
                ss[1] = ss[1] + 1;
                p2[ss[1]] = pocer[i];
            } else if (pocer[i].getType() == 3) {
                ss[2] = ss[2] + 1;
                p3[ss[2]] = pocer[i];
            } else if (pocer[i].getType() == 4) {
                ss[3] = ss[3] + 1;
                p4[ss[3]] = pocer[i];
            }
        }
        int bj = -1;
        if (ss[0] > 3) {
            bj = 0;
        }
        if (ss[1] > 3) {
            bj = 1;
        }
        if (ss[2] > 3) {
            bj = 2;
        }
        if (ss[3] > 3) {
            bj = 3;
        }
        //是个同花
        if (bj != -1) {
            Pocer[] p5 = null;
            if (bj == 0) {
                p5 = p1;
            } else if (bj == 1) {
                p5 = p2;
            } else if (bj == 2) {
                p5 = p3;
            } else if (bj == 3) {
                p5 = p4;
            }
            
            //看看是不是顺子 如果是顺子就是同花顺 如果是A-5的顺子也要考虑
            int bj2 = 0;
            int bj3 = -1;
            int insize = ss[bj];
            for (int k = 0; k < insize; k++) {
                int p_ = p5[insize - k].getSize2() - p5[insize - k - 1].getSize2();
                if (p_ == 1 || (p_ == 9 && p5[insize - k].getSize2() == 14)) {
                    p6[4 - bj2] = p5[insize - k];
                    p6[3 - bj2] = p5[insize - k -1];
                    if (bj2 == 3) {
                        bj3 = k;
                        break;
                    }
                    bj2++;
                } else {
                    bj2 = 0;
                }
            }
            
            // 同花顺
            if (bj3 != -1) {
                if (p6[4].getSize2() == 14 && p6[3].getSize2() != 13) {
                    Pocer[] p6_ = {null, null, null, null, null};
                    p6_[0] = p6[4];
                    p6_[1] = p6[0];
                    p6_[2] = p6[1];
                    p6_[3] = p6[2];
                    p6_[4] = p6[3];
                    p6 = p6_;
                }
                if (p6[4].getSize2() == 14 && p6[3].getSize2() == 13) {
                    obj[1] = PocerLink.POCER_TYPE[0];
                } else {
                    obj[1] = PocerLink.POCER_TYPE[1];
                }
                obj[0] = p6;
                obj[2] = p6[0].getSize2() + p6[1].getSize2() + p6[2].getSize2() + p6[3].getSize2() + p6[4].getSize2();
                return obj;
            }
            
            if (p5[ss[bj]].getSize2() == 14 && p5[0].getSize2() == 2 && p5[1].getSize2() == 3 && p5[2].getSize2() == 4
                    && p5[3].getSize2() == 5) {
                p6[0] = p5[ss[bj]];
                p6[1] = p5[0];
                p6[2] = p5[1];
                p6[3] = p5[2];
                p6[4] = p5[3];
                obj[0] = p6;
                obj[1] = PocerLink.POCER_TYPE[1];
                obj[2] = 15;
                return obj;
            }
            //是个同花但是不是同花顺
            p6[4] = p5[ss[bj]];
            p6[3] = p5[ss[bj] - 1];
            p6[2] = p5[ss[bj] - 2];
            p6[1] = p5[ss[bj] - 3];
            p6[0] = p5[ss[bj] - 4];
            obj[0] = p6;
            obj[1] = PocerLink.POCER_TYPE[4];
            obj[2] = p6[0].getSize2() + p6[1].getSize2() + p6[2].getSize2() + p6[3].getSize2() + p6[4].getSize2();
            return obj;
        }

        Pocer[][] p11 = new Pocer[7][4];    // 扑克按相同数字分组 最多7组每组最多4张
        int ss3[] = {-1, -1, -1, -1, -1, -1, -1};
        int dui4 = 0;
        int dui4_tm1 = -1;
        int dui3 = 0;
        int dui3_tm1 = -1;  // 第一个三对
        int dui3_tm2 = -1;  // 第二个三对
        int dui2 = 0;
        int dui2_tm1 = -1;
        int dui2_tm2 = -1;
        int dui2_tm3 = -1;
        for (int e = 0; e < pocer.length; e++) {
            for (int f = 0; f < 7; f++) {
                if (ss3[f] == -1) {
                    ss3[f] = 0;
                    p11[f][0] = pocer[e];
                    break;
                }
                if (p11[f][0].getSize2() == pocer[e].getSize2()) {
                    ss3[f] = ss3[f] + 1;
                    p11[f][ss3[f]] = pocer[e];
                    if (ss3[f] == 3) {  // 4张相同
                        dui4++;
                        dui4_tm1 = f;
                    }
                    if (ss3[f] == 2) {  // 3张相同
                        dui3++;
                        if (dui3 == 1) {
                            dui3_tm1 = f;
                        } else {
                            dui3_tm2 = f;
                        }
                    }
                    if (ss3[f] == 1) {  // 2张相同
                        dui2++;
                        if (dui2 == 1) {
                            dui2_tm1 = f;
                        } else if (dui2 == 2) {
                            dui2_tm2 = f;
                        } else {
                            dui2_tm3 = f;
                        }
                    }
                    break;
                }
            }
        }
        
        // 四条
        if (dui4 != 0) {
            p6[0] = p11[dui4_tm1][0];
            p6[1] = p11[dui4_tm1][1];
            p6[2] = p11[dui4_tm1][2];
            p6[3] = p11[dui4_tm1][3];
            if (p6[0].getSize2() == pocer[pocer.length - 1].getSize2()) {
                p6[4] = pocer[pocer.length - 5];
            } else {
                p6[4] = pocer[pocer.length - 1];
            }
            obj[0] = p6;
            obj[1] = PocerLink.POCER_TYPE[2];
            obj[2] = p6[0].getSize2() + p6[1].getSize2() + p6[2].getSize2() + p6[3].getSize2() + p6[4].getSize2();
            return obj;
        }
        // 葫芦
        if (dui3 > 0 && dui2 >= 2) {
            if (dui3 == 2) {
                if (p11[dui3_tm1][0].getSize2() > p11[dui3_tm2][0].getSize2()) {
                    p6[0] = p11[dui3_tm1][0];
                    p6[1] = p11[dui3_tm1][1];
                    p6[2] = p11[dui3_tm1][2];
                    p6[3] = p11[dui3_tm2][1];
                    p6[4] = p11[dui3_tm2][2];
                } else {
                    p6[0] = p11[dui3_tm2][0];
                    p6[1] = p11[dui3_tm2][1];
                    p6[2] = p11[dui3_tm2][2];
                    p6[3] = p11[dui3_tm1][1];
                    p6[4] = p11[dui3_tm1][2];
                }
            } else {
                p6[0] = p11[dui3_tm1][0];
                p6[1] = p11[dui3_tm1][1];
                p6[2] = p11[dui3_tm1][2];
                if (dui2 == 2) {
                    if (dui2_tm1 == dui3_tm1) {
                        p6[3] = p11[dui2_tm2][0];
                        p6[4] = p11[dui2_tm2][1];
                    } else {
                        p6[3] = p11[dui2_tm1][0];
                        p6[4] = p11[dui2_tm1][1];
                    }
                } else {
                    if (dui2_tm1 == dui3_tm1) {
                        if (p11[dui2_tm2][0].getSize2() > p11[dui2_tm3][0].getSize2()) {
                            p6[3] = p11[dui2_tm2][0];
                            p6[4] = p11[dui2_tm2][1];
                        } else {
                            p6[3] = p11[dui2_tm3][0];
                            p6[4] = p11[dui2_tm3][1];
                        }
                    } else if (dui2_tm2 == dui3_tm1) {
                        if (p11[dui2_tm1][0].getSize2() > p11[dui2_tm3][0].getSize2()) {
                            p6[3] = p11[dui2_tm1][0];
                            p6[4] = p11[dui2_tm1][1];
                        } else {
                            p6[3] = p11[dui2_tm3][0];
                            p6[4] = p11[dui2_tm3][1];
                        }
                    } else {
                        if (p11[dui2_tm1][0].getSize2() > p11[dui2_tm2][0].getSize2()) {
                            p6[3] = p11[dui2_tm1][0];
                            p6[4] = p11[dui2_tm1][1];
                        } else {
                            p6[3] = p11[dui2_tm2][0];
                            p6[4] = p11[dui2_tm2][1];
                        }
                    }
                }
            }
            obj[0] = p6;
            obj[1] = PocerLink.POCER_TYPE[3];
            obj[2] = p6[0].getSize2() + p6[1].getSize2() + p6[2].getSize2() + p6[3].getSize2() + p6[4].getSize2();
            return obj;
        }
        
        // 顺子
        int bj7 = 0;
        int bj8 = -1;
        for (int i_ = 0; i_ < pocer.length - 1; i_++) {
        	int pocer1 = pocer[pocer.length - i_ - 1].getSize2();
			int pocer2 = pocer[pocer.length - i_ - 2].getSize2();
			if ((pocer1 - pocer2 == 1) ||
					((pocer1==12||pocer1==25||pocer1==38||pocer1==51) && pocer1 - pocer2 == 8)) {
                p6[4 - bj7] = pocer[pocer.length - i_ - 1];
                p6[3 - bj7] = pocer[pocer.length - i_ - 2];
                if (bj7 >= 3) {
                    bj8 = i_;
                    break;
                }
                bj7 ++;
            } else {
                if (pocer1 - pocer2 != 0) {
                    bj7 = 0;                    
                }
            }
        }
        if (bj8 != -1) {
            obj[0] = p6;
            obj[1] = PocerLink.POCER_TYPE[5];
            obj[2] = p6[0].getSize2() + p6[1].getSize2() + p6[2].getSize2() + p6[3].getSize2() + p6[4].getSize2();
            return obj;
        }
        
        // A - 5 的顺子
        if (pocer[pocer.length - 1].getSize2() == 14 && pocer[0].getSize2() == 2) {
            p6[0] = pocer[pocer.length - 1];
            int bj9 = 1;
            for (int i_ = 0; i_ < pocer.length - 2; i_++) {
                if (pocer[i_ + 1].getSize2() - pocer[i_].getSize2() == 1) {
                    p6[bj9] = pocer[i_];
                    p6[bj9 + 1] = pocer[i_ + 1];
                    if (bj9 == 3) {
                        bj9 = 4;
                        break;
                    }
                    bj9++;
                } else {
                    if (pocer[i_ + 1].getSize2() - pocer[i_].getSize2() == 0) {
                        continue;
                    } else {
                        bj9 = 1;
                        break;
                    }
                }
            }
            if (bj9 == 4) {
                obj[0] = p6;
                obj[1] = PocerLink.POCER_TYPE[5];
                obj[2] = 15;
                return obj;
            }
        }
        
        // 三条
        if (dui3 == 1) {
            p6[0] = p11[dui3_tm1][0];
            p6[1] = p11[dui3_tm1][1];
            p6[2] = p11[dui3_tm1][2];
            if (pocer[pocer.length - 1].getSize2() != p6[0].getSize2() 
                    && pocer[pocer.length - 2].getSize2() != p6[0].getSize2()) {
                p6[3] = pocer[pocer.length - 2];
                p6[4] = pocer[pocer.length - 1];
            } else if (pocer[pocer.length - 1].getSize2() != p6[0].getSize2()) {
                p6[3] = pocer[pocer.length - 5];
                p6[4] = pocer[pocer.length - 1];
            } else {
                p6[3] = pocer[pocer.length - 5];
                p6[4] = pocer[pocer.length - 4];
            }
            obj[0] = p6;
            obj[1] = PocerLink.POCER_TYPE[6];
            obj[2] = p6[0].getSize2() + p6[1].getSize2() + p6[2].getSize2() + p6[3].getSize2() + p6[4].getSize2();
            return obj;
        }
        
        // 两对
        if (dui2 == 2) {            
            p6[0] = p11[dui2_tm1][0];
            p6[1] = p11[dui2_tm1][1];
            p6[2] = p11[dui2_tm2][0];
            p6[3] = p11[dui2_tm2][1];
            for (int i = pocer.length - 1; i >= 0; i--) {
                if (p6[0].getSize2() != pocer[i].getSize2() && p6[2].getSize2() != pocer[i].getSize2()) {
                    p6[4] = pocer[i];
                    break;
                }
            }
            obj[0] = p6;
            obj[1] = PocerLink.POCER_TYPE[7];
            obj[2] = p6[0].getSize2() + p6[1].getSize2() + p6[2].getSize2() + p6[3].getSize2() + p6[4].getSize2();
            return obj;
        }
        // 三对
        if (dui2 == 3) {
            p6[0] = p11[dui2_tm2][0];
            p6[1] = p11[dui2_tm2][1];
            p6[2] = p11[dui2_tm3][0];
            p6[3] = p11[dui2_tm3][1];
            for (int i = pocer.length - 1; i >= 0; i--) {
                if (p6[0].getSize2() != pocer[i].getSize2() && p6[2].getSize2() != pocer[i].getSize2()) {
                    p6[4] = pocer[i];
                    break;
                }
            }
            obj[0] = p6;
            obj[1] = PocerLink.POCER_TYPE[7];
            obj[2] = p6[0].getSize2() + p6[1].getSize2() + p6[2].getSize2() + p6[3].getSize2() + p6[4].getSize2();
            return obj;
        }
        
        // 一对
        if (dui2 == 1) {
            p6[0] = p11[dui2_tm1][0];
            p6[1] = p11[dui2_tm1][1];
            int i_ = 4;
            for (int i = pocer.length - 1; i >= 0; i--) {
                if (p6[0].getSize2() != pocer[i].getSize2()) {
                    p6[i_] = pocer[i];
                    i_--;
                    if (i_ == 1) {
                        break;
                    }
                }
            }
            obj[0] = p6;
            obj[1] = PocerLink.POCER_TYPE[8];
            obj[2] = p6[0].getSize2() + p6[1].getSize2() + p6[2].getSize2() + p6[3].getSize2() + p6[4].getSize2();
            return obj;
        }
        // 高牌
        for (int i = 0; i < 5; i++) {
            p6[i] = pocer[pocer.length + i - 5];
        }
//        p6[0] = pocer[2];
//        p6[1] = pocer[3];
//        p6[2] = pocer[4];
//        p6[3] = pocer[5];
//        p6[4] = pocer[6];
        obj[0] = p6;
        obj[1] = PocerLink.POCER_TYPE[9];
        obj[2] = p6[0].getSize2() + p6[1].getSize2() + p6[2].getSize2() + p6[3].getSize2() + p6[4].getSize2();
        return obj;
    }
	
	//
	//0皇家同花 1同花 2四条 3葫芦4同花 5顺子 6三条 7两对 8一对 9高牌
	//
	public static int POCER_TYPE[] = {1,2,3,4,5,6,7,8,9,10};
//	public static void test() {
//		Map<Integer, String> map = new HashMap<Integer, String>();
//		map.put(0, "同花顺");
//		map.put(1, "四条");
//		map.put(2, "同花");
//		map.put(3, "顺子");
//		map.put(4, "三条");
//		map.put(5, "两对");
//		for (int i = 0 ; i < 100000 ; i ++) {
//			Pocer[] pocer = new Pocer[7];
//			int[] zuida = RobotPock.getZuidapai2();
//			Pocer pocer1 = new Pocer(zuida[0]);
//			Pocer pocer2 = new Pocer(zuida[1]);
//			Pocer pocer3 = new Pocer(zuida[2]);
//			Pocer pocer4 = new Pocer(zuida[3]);
//			Pocer pocer5 = new Pocer(zuida[4]);
//			Pocer pocer6 = new Pocer(zuida[5]);
//			Pocer pocer7 = new Pocer(zuida[6]);
//			pocer[0] = pocer1;
//			pocer[1] = pocer2;
//			pocer[2] = pocer3;
//			pocer[3] = pocer4;
//			pocer[4] = pocer5;
//			pocer[5] = pocer6;
//			pocer[6] = pocer7;
////			System.out.print(map.get(zuida[7]));
//			Object[] obj = zuidapai(pocer);
//			Pocer[] ps = (Pocer[])obj[0];
//			int pocerType = (Integer)obj[1];
//			if ((zuida[7] + 1) != pocerType) {
//				System.out.println("得到的是：" + map.get(zuida[7]));
//				for (Pocer p : pocer) {
//					System.out.println((p.getType() == 1 ? "方块" : p.getType() == 2? "草花" : p.getType() == 3 ? "红桃" : "黑桃") + p.getSize2() + " / " + p.getSize1() );
//				}
//				System.out.println("这是：" + (pocerType == 1 ? "皇家同花顺" : pocerType == 2 ? "同花顺" : pocerType == 3 ? "四条" : pocerType == 4 ? "葫芦" : pocerType == 5 ? "同花" : pocerType == 6 ? "顺子" : pocerType == 7 ? "三条" : pocerType == 8 ? "两对" : pocerType == 9 ? "一对" : "高牌"));
//				System.out.println("计算出来的最大牌是：");
//				for (Pocer p : ps) {
//					System.out.println((p.getType() == 1 ? "方块" : p.getType() == 2? "草花" : p.getType() == 3 ? "红桃" : "黑桃") + p.getSize2() + " / " + p.getSize1() );
//				}
//				System.out.println();
//				System.out.println();
//			}
//		}
//	}
	
	public static void main(String[] args) {
//		test();
		Pocer[] pocer = new Pocer[7];
		Pocer pocer1 = new Pocer(50);
		Pocer pocer2 = new Pocer(24);
		Pocer pocer3 = new Pocer(25);
		Pocer pocer4 = new Pocer(34);
		Pocer pocer5 = new Pocer(41);
		Pocer pocer6 = new Pocer(46);
		Pocer pocer7 = new Pocer(6);
		pocer[0] = pocer1;
		pocer[1] = pocer2;
		pocer[2] = pocer3;
		pocer[3] = pocer4;
		pocer[4] = pocer5;
		pocer[5] = pocer6;
		pocer[6] = pocer7;
		for (Pocer p : pocer) {
			System.out.println((p.getType() == 1 ? "方块" : p.getType() == 2? "草花" : p.getType() == 3 ? "红桃" : "黑桃") + p.getSize2() + " / " + p.getSize1() );
		}
		Object[] obj = zuidapai1(pocer);
		Pocer[] p5 = (Pocer[])obj[0];
		int pocerType = (Integer)obj[1];
//		int size = (Integer)obj[2];
		System.out.println("=========================================");
		System.out.println("这是：" + (pocerType == 1 ? "皇家同花顺" : pocerType == 2 ? "同花顺" : pocerType == 3 ? "四条" : pocerType == 4 ? "葫芦" : pocerType == 5 ? "同花" : pocerType == 6 ? "顺子" : pocerType == 7 ? "三条" : pocerType == 8 ? "两对" : pocerType == 9 ? "一对" : "高牌"));
		for (int i = 0 ; i < 5 ;i ++) {
			System.out.println((p5[i].getType() == 1 ? "方块" : p5[i].getType() == 2? "草花" : p5[i].getType() == 3 ? "红桃" : "黑桃") + p5[i].getSize2() + " / " + p5[i].getSize1() );
		}
		
		RoomPersion rp1 = new RoomPersion();
		rp1.setZuidaPocers( (Pocer[])obj[0]);
		rp1.setPocerType(pocerType);
		//================
		System.out.println();
		System.out.println("///------------------------------------------------------------------------------------------------------------");
		System.out.println();

		Pocer pocer11 = new Pocer(10);
		Pocer pocer22 = new Pocer(47);
		Pocer pocer33 = new Pocer(25);
		Pocer pocer44 = new Pocer(34);
		Pocer pocer55 = new Pocer(41);
		Pocer pocer66 = new Pocer(46);
		Pocer pocer77 = new Pocer(6);
		pocer[0] = pocer11;
		pocer[1] = pocer22;
		pocer[2] = pocer33;
		pocer[3] = pocer44;
		pocer[4] = pocer55;
		pocer[5] = pocer66;
		pocer[6] = pocer77;
		for (Pocer p : pocer) {
			System.out.println((p.getType() == 1 ? "方块" : p.getType() == 2? "草花" : p.getType() == 3 ? "红桃" : "黑桃") + p.getSize2() + " / " + p.getSize1() );
		}
		Object[] obj2 = zuidapai(pocer);
		p5 = (Pocer[])obj2[0];
		pocerType = (Integer)obj2[1];
//		int size = (Integer)obj[2];
		System.out.println("=========================================");
		System.out.println("这是：" + (pocerType == 1 ? "皇家同花顺" : pocerType == 2 ? "同花顺" : pocerType == 3 ? "四条" : pocerType == 4 ? "葫芦" : pocerType == 5 ? "同花" : pocerType == 6 ? "顺子" : pocerType == 7 ? "三条" : pocerType == 8 ? "两对" : pocerType == 9 ? "一对" : "高牌"));
		for (int i = 0 ; i < 5 ;i ++) {
			System.out.println((p5[i].getType() == 1 ? "方块" : p5[i].getType() == 2? "草花" : p5[i].getType() == 3 ? "红桃" : "黑桃") + p5[i].getSize2() + " / " + p5[i].getSize1() );
		}
		RoomPersion rp2 = new RoomPersion();
		rp2.setZuidaPocers( (Pocer[])obj2[0]);
		rp2.setPocerType(pocerType);
		System.out.println(bipai2(rp1,rp2));
	}
}

