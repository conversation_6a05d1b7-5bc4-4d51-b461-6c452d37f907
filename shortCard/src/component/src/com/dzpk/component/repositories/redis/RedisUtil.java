package com.dzpk.component.repositories.redis;

import java.util.Properties;

public final class RedisUtil {
    public static int getTimeout(Properties properties,String key){
        int timeout = 5000;

        if(null == properties ||
                null == key ||
                "".equals(key.trim()))
            return timeout;

        String timeoutStr = properties.getProperty(key.trim());
        if(null == timeoutStr || "".equals(timeoutStr.trim())){
            return timeout;
        }

        try{
            timeout = Integer.parseInt(timeoutStr.trim());
            if(timeout<2000)
                timeout = 5000;
        }catch (Exception ex){
            timeout = 5000;
        }

        return timeout;
    }

    public static int getHostPort(Properties properties,String key){
        int port = 6379;

        if(null == properties ||
                null == key ||
                "".equals(key.trim()))
            return port;

        String portStr = properties.getProperty(key.trim());
        if(null == portStr || "".equals(portStr.trim())){
            return port;
        }

        port = Integer.parseInt(portStr.trim());

        return port;
    }

    public static String getPasswd(Properties properties,String key){
       String pwd = null;

        if(null == properties ||
                null == key ||
                "".equals(key.trim()))
            return pwd;

        String pwdStr = properties.getProperty(key.trim());
        if(null == pwdStr || "".equals(pwdStr.trim())){
            return pwd;
        }

        pwd = pwdStr.trim();

        return pwd;
    }

    public static String getHostAddress(Properties properties,String key){
        String result = null;

        if(null == properties ||
                null == key ||
                "".equals(key.trim()))
            return result;

        String valueStr = properties.getProperty(key.trim());
        if(null == valueStr || "".equals(valueStr.trim())){
            return result;
        }

        result = valueStr.trim();

        return result;
    }
}
