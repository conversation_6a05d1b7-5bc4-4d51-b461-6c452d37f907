package com.work.comm.client.protocal;

import com.dzpk.common.token.TokenManager;
import com.dzpk.record.GameReportModel;
import com.work.comm.client.pack.Functions;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.server.pack.I366ServerPickUtil;
import com.dzpk.common.utils.LogUtil;
import com.work.comm.io.ClientHandlerFactory;

import io.netty.channel.Channel;
import lombok.Getter;
import lombok.Setter;
import org.apache.logging.log4j.Logger;

import java.nio.charset.Charset;
import java.util.Arrays;

/**
 * Created by IntelliJ IDEA.
 * User: Administrator
 * Date: 2010-11-22
 * Time: 16:05:36
 * To change this template use File | Settings | File Templates.
 */
@Setter
@Getter
public class Request extends BaseRequest {

    private static final Logger logger = LogUtil.getLogger(Request.class);

    // Package data
    private int protocol;
    private int userId;
    private int languageId;
    private int clientPlatform;
    private int clientBuildNumber;
    private int customId;
    private int productId;
    private int size;
    private String Token;

    public boolean init(Channel channel, byte[] buffer) {
        this.channel = channel;
        this.bt = buffer;
        return parse(bt);
    }

    public static int getPackageSize(byte[] bt) {
        if (bt == null || bt.length < Protocal.RP_SIZE_HIGH + 2) {
            return -1;
        }
        return Functions.byteArrayToShortInt(bt, Protocal.RP_SIZE_HIGH);
    }

    public boolean parse(byte[] bt2) {
        if (bt2 == null || bt2.length < 30) {
            return false;
        }
        if (bt2[0] != Protocal.HEADER_INDICATER_0 || bt2[1] != Protocal.HEADER_INDICATER_1
                || bt2[2] != Protocal.HEADER_INDICATER_2 || bt2[3] != Protocal.HEADER_INDICATER_3) {
            return false;
        }

        this.protocol = bt2[Protocal.RP_PROTOCOL] & 0xff;
        this.userId = Functions.byteArrayToInt(bt2, Protocal.RP_USER_ID_1);
        this.languageId = bt2[Protocal.RP_LANGUAGE_ID] & 0xff;
        this.clientPlatform = bt2[Protocal.RP_CLIENT_PLATFORM] & 0xff;
        this.clientBuildNumber = bt2[Protocal.RP_CLIENT_BUILD_NUMBER] & 0xff;
        this.customId = Functions.byteArrayToShortInt(bt2, Protocal.RP_CUSTOM_ID_1);
        this.productId = Functions.byteArrayToShortInt(bt2, Protocal.RP_PRODUCT_ID_1);
        this.requestCode = Functions.byteArrayToShortInt(bt2, Protocal.RP_REQUEST_CODE_HIGH);
        this.size = Functions.byteArrayToShortInt(bt2, Protocal.RP_SIZE_HIGH);

        //校验请求协议是否合法
        if(null == ClientHandlerFactory.getInstance().getHandler(this.getRequestCode())){
            logger.error("handle client request error, can't find request code={}, userid={}", this.getRequestCode(), this.getUserId());
            return false;
        }

        if(this.getUserId() <= 0){
            logger.error("handle client request error, userid is invalid, userid={}", this.getUserId());
            return false;
        }

        setToken(new String(Arrays.copyOfRange(bt2,Protocal.RP_TOKEN_HIGH,Protocal.RP_TOKEN_HIGH + 66), Charset.forName(I366ClientPickUtil.UTF_16)).trim());
        logger.debug("code={},token={},userId={},clientPlatform={}",this.getRequestCode(),this.getToken(),this.getUserId(),this.getClientPlatform());
        return TokenManager.getInstance().verify(this.getToken(),this.getUserId(),"") > 0;
    }

    @Override
    public String toString() {
        return "Request{" +
                "protocol=" + protocol +
                ", userId=" + userId +
                ", languageId=" + languageId +
                ", clientPlatform=" + clientPlatform +
                ", clientBuildNumber=" + clientBuildNumber +
                ", customId=" + customId +
                ", productId=" + productId +
                ", size=" + size +
                ", Token='" + Token + '\'' +
                '}';
    }

    public static void main(String[] args) {
        String str = "3ca3b2d0c2b640adbcfcc8b05015db32";
        System.out.println(str.length());
        byte[] b = str.getBytes();
        System.out.println(b.length);
        System.out.println(new String(b));
    }
}
