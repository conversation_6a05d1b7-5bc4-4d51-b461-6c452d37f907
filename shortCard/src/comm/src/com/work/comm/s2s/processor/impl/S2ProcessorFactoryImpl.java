package com.work.comm.s2s.processor.impl;

import com.dzpk.common.config.PropertiesPathUtil;
import com.work.comm.s2s.common.AbstractChannelManager;
import com.work.comm.s2s.common.IHeartbeat;
import com.work.comm.s2s.common.IWho;
import com.work.comm.s2s.processor.IProcessor;
import com.work.comm.s2s.processor.IProcessorFactory;
import lombok.extern.slf4j.Slf4j;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.input.SAXBuilder;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统内服务器之间的通信
 */
@Slf4j
public class S2ProcessorFactoryImpl implements IProcessorFactory {
    /** processor配置文件路径 */
    private String eventMappingPath;

    /** channel认证 */
    private AbstractChannelManager channelManager;

    /**
     * 系统支持的processor集合
     * requestCode -> class
     */
    private Map<Integer, Class<?>> processorMap = new HashMap<>();

    /**
     * 系统支持的processor集合
     * requestCode -> instance
     */
    private Map<Integer, IProcessor> processorCacheMap = new HashMap<>();

    public S2ProcessorFactoryImpl(String eventMappingPath,AbstractChannelManager channelManager){
        this.eventMappingPath = eventMappingPath;
        if(null == this.eventMappingPath ||
                "".equals(this.eventMappingPath.trim()))
            throw new IllegalArgumentException("eventMappingPath required!");

        this.channelManager = channelManager;
        this.initialize();
    }

    /**
     * 根据reqCode获取对应的processor
     * 一个reqCode对应一类processor
     *
     * @param reqCode   请求代码
     *
     * @return
     */
    public IProcessor getProcessor(int reqCode){
        IProcessor processor = null;

        // 共享实例缓存中查找
        processor = processorCacheMap.get(reqCode);
        if(null != processor) {
            if(processor instanceof IWho) {
                IWho who = (IWho) processor;
                who.setAuthChannel(this.channelManager);
            }
            return processor;
        }

        Class<?> clazz=null;
        try{
            clazz = this.processorMap.get(reqCode);
            if(null != clazz) {
                processor = (IProcessor) clazz.newInstance();
                processor.reqCode(reqCode);
                if(processor instanceof IWho) {
                    IWho who = (IWho) processor;
                    who.setAuthChannel(this.channelManager);
                }
            }
        }catch (Exception ex){
            log.error(String.format("实例化Processor失败：%s",clazz),ex);
        }

        return processor;
    }

    private void initialize() {
        // 解析配置文件
        Map<Integer, String> tempMap = parseMappingTable();

        // className转换成Class对象
        String className = "";
        try {
            StringBuilder traceLog = null;
            if(log.isDebugEnabled()){
                traceLog = new StringBuilder("系统内通信支持的请求如下：");
            }

            for (Integer reqCode : tempMap.keySet()) {
                className = tempMap.get(reqCode);
                if(null == className || "".equals(className.trim()))
                    continue;

                Class<?> clazz = Class.forName(className);
                IProcessor processor = (IProcessor) clazz.newInstance();
                processor.reqCode(reqCode);
                processorMap.put(reqCode, clazz);
                if(processor.isSharable())
                    processorCacheMap.put(reqCode,processor);
                if(processor instanceof IHeartbeat)
                    this.channelManager.setHeartbeat((IHeartbeat)processor);
                if(processor instanceof IWho)
                    this.channelManager.setWho((IWho)processor);
                if(traceLog != null){
                    traceLog.append(String.format("%s %s -> %s",
                            System.lineSeparator(),reqCode,className));
                }
            }

            if(null != traceLog)
                log.debug(traceLog.toString());
        } catch (Exception ex) {
            throw new RuntimeException(String.format("Processor类名无效：%s",className),ex);
        }
    }

    private Map<Integer, String> parseMappingTable() {
        Map<Integer, String> classMapping = new HashMap<>();

        String mappingFile = "";
        String saxParser = "";
        try {
            saxParser = PropertiesPathUtil.getProperty("wg.SAXParser");
            SAXBuilder saxBuilder = new SAXBuilder(saxParser);
            mappingFile = PropertiesPathUtil.getProperty(this.eventMappingPath);
            Document document = saxBuilder.build(new File(mappingFile));

            Element root = document.getRootElement();

            List requestList = root.getChildren();
            for (Object aRequestList : requestList) {
                Element element = (Element) aRequestList;
                String code = element.getAttributeValue("code");
                String className = element.getAttributeValue("classname");
                classMapping.put(new Integer(code), className);

            }
        } catch (Exception ex) {
            throw new RuntimeException(String.format("加载/解析event-mapping文件失败:saxParser=%s," +
                    "mappingFileKey=%s,mappingFile=%s",saxParser,eventMappingPath,mappingFile),ex);
        }

        return classMapping;
    }
}

