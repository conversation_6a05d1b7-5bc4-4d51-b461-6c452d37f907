package com.work.comm.s2s.common.matcher;

import com.work.comm.s2s.common.ChannelAttributeKey;
import io.netty.channel.Channel;
import io.netty.util.Attribute;
import lombok.Getter;
import lombok.ToString;

/**
 * 每类客户端Channel对应一个实例
 * Type/ID对标识channel
 */
@Getter
@ToString
public class ChannelTypeIdMatcherImpl implements IExChannelMatcher {
    /** channal中的属性值 */
    // 类型,必填
    private String type;
    // ID,必填
    private String id;

    public ChannelTypeIdMatcherImpl(String id, String type){
        this.type = null==type?"":type.trim();
        this.id = null==id?"":id.trim();
    }

    /**
     * 增加属性到channel
     *
     * @param ch  channel，必填
     */
    public void addAttr(Channel ch){
        if(null == ch)
            return;

        Attribute<String> type = ch.attr(ChannelAttributeKey.typeKey);
        type.setIfAbsent(this.type);
        if(!"".equals(this.id)) {
            Attribute<String> id = ch.attr(ChannelAttributeKey.idKey);
            id.setIfAbsent(this.id);
        }
    }

    @Override
    public boolean matches(Channel ch){
        boolean matched = false;
        if(null == ch)
            return matched;

        // 检查type是否匹配
        if(ch.hasAttr(ChannelAttributeKey.typeKey)){
            String value = ch.attr(ChannelAttributeKey.typeKey).get();
            matched = null != value && this.type.equalsIgnoreCase(value.trim());
            if(!matched)
                return matched;
        }

        // 检查ID是否匹配
        if(!"".equals(this.id) && ch.hasAttr(ChannelAttributeKey.idKey)){
            String value = ch.attr(ChannelAttributeKey.idKey).get();
            matched = this.id.equalsIgnoreCase(value);
        }

        return matched;
    }
}
