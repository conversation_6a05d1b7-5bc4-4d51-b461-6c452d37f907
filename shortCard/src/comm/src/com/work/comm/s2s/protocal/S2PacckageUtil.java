package com.work.comm.s2s.protocal;

import lombok.extern.slf4j.Slf4j;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class S2PacckageUtil {
	/**
	 * 提取数据包中的基本数据
	 *
	 * @param bt          原始数据包，byte数组，长度必须大于或等于头部长度
	 * @return ServiceRequest实例
	 *     数据包不符合格式，则返回null
	 */
	public static ServiceRequest pickBase(byte[] bt){
		ServiceRequest result = null;
		if(null == bt || bt.length< Protocal.HEAD_DATA_LENGTH){
			log.error("当前数据包长度不符合规约: 长度至少{}字节！", Protocal.HEAD_DATA_LENGTH);
			return result;
		}
		if(bt.length> Protocal.DATA_MAX_LENGTH){
			log.error("当前数据包长度不符合规约: 长度最多{}字节！", Protocal.DATA_MAX_LENGTH);
			return result;
		}

		ByteBuffer buffer = ByteBuffer.wrap(bt);
		buffer.order(ByteOrder.BIG_ENDIAN);

		// 提取请求号
		short reqCode = buffer.getShort(Protocal.HEAD_REQCODE_OFFSET);

		// 业务数据条目
		byte itemCount = buffer.get(Protocal.HEAD_ITEMCOUNT_OFFSET);

		result = new ServiceRequest();
		result.setItemCount(itemCount);
		result.setReqCode(reqCode);
		result.setDataBuffer(buffer);
		return result;
	}

	/**
	 * @param buffer 数据包
	 * @param int2 2维数组 格式规定如：{{1,2} , {2 ,3}}
	 *             其中第一位是:itemID 第二位是:要转换的类型
	 * @return Map key itemId Value 对应解码出来的值
	 */
	public static Map<Integer, Object> pickAll(ByteBuffer buffer, int[][] int2) {
		Map<Integer, Object> resMap = new HashMap<>();
		if(null == int2 || int2.length ==0)
			return resMap;

		if(null == buffer)
			return resMap;

		if(buffer.limit()<= Protocal.HEAD_DATA_LENGTH) {
			log.warn("当前请求数据包可用长度小于或等于{}字节，无法提取指定的业务条目数据！",
					Protocal.HEAD_DATA_LENGTH);
			return resMap;
		}

		buffer.position(Protocal.HEAD_DATA_LENGTH);
		for(int[] row : int2){
			if(null == row || row.length!=2)
				continue;

			// 判断buffer是否已经达到limit
			if (buffer.position() >= buffer.limit())
				break;

			int reqItemId = row[0];
			int realItemId = buffer.get()&0XFF;
			if(reqItemId != realItemId) {
				buffer.position(buffer.position()-1);
				continue;
			}

			int type = row[1];
			int strLen=0;
			byte[] strBytes = null;
			switch (type) {
				case Protocal.TYPE_INT_1:
					byte byteVal = buffer.get();
					resMap.put(reqItemId,byteVal);
					break;
				case Protocal.TYPE_INT_2:
					short shortVal = buffer.getShort();
					resMap.put(reqItemId,shortVal);
					break;
				case Protocal.TYPE_INT_4:
					int intVal = buffer.getInt();
					resMap.put(reqItemId,intVal);
					break;
				case Protocal.TYPE_STRING_UNICODE:
					strLen = buffer.getShort()&0XFFFF;
					if(strLen>0){
						strBytes = new byte[strLen];
						buffer.get(strBytes);
						String str = Functions.toUnicode(strBytes);
						resMap.put(realItemId,str);
					}
					break;
				case Protocal.TYPE_STRING_UTF16:
					strLen = buffer.getShort()&0XFFFF;
					if(strLen>0){
						strBytes = new byte[strLen];
						buffer.get(strBytes);
						String str = Functions.toUtf16(strBytes);
						resMap.put(realItemId,str);
					}
					break;
				case Protocal.TYPE_INT_1_ARRAY:
					/**
					 * 1. byte[0]/byte[1]放入数组的长度
					 * 2. byte[2]开始依次放入每个byte值
					 */
					strLen = buffer.getShort()&0XFFFF;
					if(strLen>0){
						strBytes = new byte[strLen];
						buffer.get(strBytes);
						resMap.put(realItemId,strBytes);
					}
					break;
				case Protocal.TYPE_INT_2_ARRAY:
					/**
					 * 将short数组转换成byte[]数组
					 * 1. byte[0]/byte[1]放入数组的长度
					 * 2. byte[2]开始依次放入每个short值
					 */
					strLen = buffer.getShort()&0XFFFF;
					if(strLen>0){
						short[] shortArr = new short[strLen];
						for(int i = 0;i<strLen;i++)
							shortArr[i] = buffer.getShort();
						resMap.put(realItemId,shortArr);
					}
					break;
				case Protocal.TYPE_INT_4_ARRAY:
					/**
					 * 将int数组转换成byte[]数组
					 * 1. byte[0]/byte[1]放入数组的长度
					 * 2. byte[2]开始依次放入每个int值
					 */
					strLen = buffer.getShort()&0XFFFF;
					if(strLen>0){
						int[] intArr = new int[strLen];
						for(int i = 0;i<strLen;i++)
							intArr[i] = buffer.getInt();
						resMap.put(realItemId,intArr);
					}
					break;
				case Protocal.TYPE_INT_4_WAPPER_ARRAY:
					/**
					 * 将int数组转换成byte[]数组
					 * 1. byte[0]/byte[1]放入数组的长度
					 * 2. byte[2]开始依次放入每个int值
					 */
					strLen = buffer.getShort()&0XFFFF;
					if(strLen>0){
						Integer[] intArr = new Integer[strLen];
						for(int i = 0;i<strLen;i++)
							intArr[i] = buffer.getInt();
						resMap.put(realItemId,intArr);
					}
					break;
			}
		}

		return resMap;
	}

    /**
     * @param int2  2维数组 格式规定如：{{1,2,3} , {2 ,3,4}}
	 *              中第一位是:itemID 第二位是:value 第三位是：type
     * @param requestCode
     * @return
     */
    public static byte[] packAll(Object[][] int2 , int requestCode) {
    	if(int2 == null || int2.length> Protocal.BODY_ITEM_MAXNUM)
    		throw new RuntimeException("业务数据条目最多不能超过"+ Protocal.BODY_ITEM_MAXNUM+"!");

		ByteBuffer buffer = createByteBuffer();
		// 初始化为头部长度
		int bodyPos = Protocal.HEAD_DATA_LENGTH;

        // 请求号
		buffer.putShort(Protocal.HEAD_REQCODE_OFFSET,(short)requestCode);

		// 业务数据条目
		buffer.put(Protocal.HEAD_ITEMCOUNT_OFFSET,(byte)int2.length);

        int itemId;
        byte[] dataArr;
        buffer.position(bodyPos);
        for (Object[] row : int2) {
			itemId = (Integer) row[0];
			int type = (Integer) row[2];
			switch (type) {
				case Protocal.TYPE_INT_1:
					byte byteVal = (byte) row[1];
					buffer.put((byte) itemId);
					buffer.put(byteVal);
					break;
				case Protocal.TYPE_INT_2:
					short shortVal = (short) row[1];
					buffer.put((byte) itemId);
					buffer.putShort(shortVal);
					break;
				case Protocal.TYPE_INT_4:
					int intVal = (int) row[1];
					buffer.put((byte) itemId);
					buffer.putInt(intVal);
					break;
				case Protocal.TYPE_STRING_UNICODE:
					dataArr = Functions.toBytesByUnicode((String) row[1]);
					if (null != dataArr && dataArr.length > 0) {
						buffer.put((byte) itemId);
						buffer.putShort((short) dataArr.length);
						buffer.put(dataArr);
					}
					break;
				case Protocal.TYPE_STRING_UTF16:
					dataArr = Functions.toBytesByUtf16((String) row[1]);
					if (null != dataArr && dataArr.length > 0) {
						buffer.put((byte) itemId);
						buffer.putShort((short) dataArr.length);
						buffer.put(dataArr);
					}
					break;
				case Protocal.TYPE_INT_1_ARRAY:
					/**
					 * 1. byte[0]/byte[1]放入数组的长度
					 * 2. byte[2]开始依次放入每个byte值
					 */
					dataArr = (byte[]) row[1];
					if (null != dataArr && dataArr.length > 0) {
						buffer.put((byte) itemId);
						buffer.putShort((short) dataArr.length);
						buffer.put(dataArr);
					}
					break;
				case Protocal.TYPE_INT_2_ARRAY:
					/**
					 * 将short数组转换成byte[]数组
					 * 1. byte[0]/byte[1]放入数组的长度
					 * 2. byte[2]开始依次放入每个short值
					 */
					short[] shortData = (short[]) row[1];
					if (null != shortData && shortData.length > 0) {
						buffer.put((byte) itemId);
						buffer.putShort((short) shortData.length);
						for (short s : shortData)
							buffer.putShort(s);
					}
					break;
				case Protocal.TYPE_INT_4_ARRAY:
					/**
					 * 将int数组转换成byte[]数组
					 * 1. byte[0]/byte[1]放入数组的长度
					 * 2. byte[2]开始依次放入每个int值
					 */
					int[] intData = (int[]) row[1];
					if (null != intData && intData.length > 0) {
						buffer.put((byte) itemId);
						buffer.putShort((short) intData.length);
						for (int s : intData)
							buffer.putInt(s);
					}
					break;
				case Protocal.TYPE_INT_4_WAPPER_ARRAY:
					/**
					 * 将int数组转换成byte[]数组
					 * 1. byte[0]/byte[1]放入数组的长度
					 * 2. byte[2]开始依次放入每个int值
					 */
					Integer[] integerData = (Integer[]) row[1];
					if (null != integerData && integerData.length > 0) {
						buffer.put((byte) itemId);
						buffer.putShort((short) integerData.length);
						for (int s : integerData)
							buffer.putInt(s);
					}
					break;
			}
		}

    	// 设置总长度
        buffer.putInt(Protocal.HEAD_BODYLENGTH_OFFSET,buffer.position());
		buffer.flip();

		byte[] byteArr = new byte[buffer.limit()];
		buffer.get(byteArr);
		log.debug("Pack byte[] : {}", Arrays.toString(byteArr));
    	return byteArr;
    }

	/**
	 * 创建一个堆的ByteBuffer
	 * 并且order
	 * @return
	 */
	public static ByteBuffer createByteBuffer() {
		ByteBuffer buffer = ByteBuffer.allocate(Protocal.DATA_MAX_LENGTH);
		buffer.order(ByteOrder.BIG_ENDIAN);

		return buffer;
    }

    public static void main(String[] args){
		byte[] data = new byte[]{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, -24, 9, 0, 0, 0, 100, -126, 0, 8, -2, -1, 0, 114, 0, 101, 0, 115, -125, 0, 18, -2, -1, 0, 50, 0, 48, 0, 48, 0, 49, 0, 48, 0, 48, 0, 48, 0, 49, -123, 0, 0, 33, 53, -122, 0, 20, -2, -1, 0, 49, 0, 50, 0, 55, 0, 46, 0, 48, 0, 46, 0, 48, 0, 46, 0, 49, -121, 0, 0, -100, 88, -120, 0, 0, 0, 0, -119, -1, -24, -115, -49, -118, 0, 0, 0, 1};

		ServiceRequest req = S2PacckageUtil.pickBase(data);

		int[][] matchObjs = {
				{130, Protocal.TYPE_STRING_UTF16},
				{131, Protocal.TYPE_STRING_UTF16},
				{132, Protocal.TYPE_STRING_UTF16},
				{133, Protocal.TYPE_INT_4},
				{134, Protocal.TYPE_STRING_UTF16},
				{135, Protocal.TYPE_INT_4},
				{136, Protocal.TYPE_INT_4},
				{137, Protocal.TYPE_INT_4},
				{138, Protocal.TYPE_INT_4}
		};
		Map<Integer, Object> map = S2PacckageUtil.pickAll(req.getDataBuffer(), matchObjs);
		String appType = (String)map.get(130);
		String appId = (String)map.get(131);
		String outIp = (String)map.get(132);
		Integer outPort = (Integer)map.get(133);
		String inIp = (String)map.get(134);
		Integer inPort = (Integer)map.get(135);
		Integer clientConnNum = (Integer)map.get(136);
		Integer seqNoStartTime = (Integer)map.get(137);
		Integer seqNo = (Integer)map.get(138);

		System.out.println(String.format("Res服务上报连接数-%s : appType=%s,appId=%s,outIp=%s,outPort=%s,inIp=%s,inPort=%s,clientNum=%s,seqNoTime=%s,seqNo=%s",
				req.getReqCode(),appType,appId,outIp,outPort,inIp,inPort,clientConnNum,seqNoStartTime,seqNo ));
	}
}
