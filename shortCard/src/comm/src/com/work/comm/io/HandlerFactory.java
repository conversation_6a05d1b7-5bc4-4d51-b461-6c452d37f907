package com.work.comm.io;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.common.config.PropertiesPathUtil;
import org.apache.logging.log4j.Logger;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.JDOMException;
import org.jdom.input.SAXBuilder;

/**
 * Created by IntelliJ IDEA.
 * User: Administrator
 * To change this template use File | Settings | File Templates.
 */
public class HandlerFactory {

    public static Logger logger = LogUtil.getLogger(HandlerFactory.class);
    // private  Map<Integer,StackObjectPool> cache = new HashMap<Integer, StackObjectPool>();
    private  static HandlerFactory instance = new HandlerFactory();
    private String eventMappingPath = "wg.requestclassmapping.path";
    private static Map<Integer, Class<?>> requestHandlerMap = new HashMap<Integer, Class<?>>();

    public static HandlerFactory getInstance() {
        return instance;
    }
    
    public Handler getHandler(int requestCode) {
        if (requestHandlerMap.get(requestCode) != null) {
            try {
                return (Handler) requestHandlerMap.get(requestCode).newInstance();
            } catch (InstantiationException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            } catch (IllegalAccessException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        return null;
    }

    public void init() {
        logger.info("loading handlers");

        String className = null;
        try {
            Map<Integer, String> tempMap = parseMappingTable();
            for (Object o : tempMap.keySet()) {
                Integer requestCode = (Integer) o;
                className = tempMap.get(requestCode);
                if (className != null) {
                    Class<?> subclass = Class.forName(className);
                    System.out.println(className);
                    requestHandlerMap.put(requestCode, subclass);
                }
            }
        } catch (Exception ex) {
            logger.error("can't init handler, className=" + className , ex);
        }

        logger.info("ObjectFactory init ok");
    }

    private Map<Integer, String> parseMappingTable() {
        Map<Integer, String> classMapping = new HashMap<Integer, String>();

        try {
            SAXBuilder saxBuilder = new SAXBuilder(PropertiesPathUtil.getProperty("wg.SAXParser"));
            Document document = saxBuilder.build(new File(PropertiesPathUtil.getProperty(eventMappingPath)));
            Element root = document.getRootElement();

            List requestList = root.getChildren();
            for (Object aRequestList : requestList) {
                Element element = (Element) aRequestList;
                String code = element.getAttributeValue("code");
                String className = element.getAttributeValue("classname");
                classMapping.put(new Integer(code), className);
            }

        } catch (JDOMException ex) {
            logger.error("parse class mapping error", ex);
        } catch (IOException ex) {
            logger.error("xml config read error", ex);
        }

        return classMapping;
    }

    public String getEventMappingPath() {
        return eventMappingPath;
    }

    public void setEventMappingPath(String eventMappingPath) {
        this.eventMappingPath = eventMappingPath;
    }
}

