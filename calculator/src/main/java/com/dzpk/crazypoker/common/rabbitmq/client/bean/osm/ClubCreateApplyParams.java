package com.dzpk.crazypoker.common.rabbitmq.client.bean.osm;

import com.dzpk.crazypoker.common.rabbitmq.constant.EOsmMessageCode;
import lombok.*;

/**
 * json参数：申请创建俱乐部
 *
 * <AUTHOR>
 * @see EOsmMessageCode#CLUB_CREATE_APPLY
 */
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ClubCreateApplyParams extends OsmParams {
    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 用户名称
     */
    private String userNickname;

    /**
     * 关联的消息ID
     * 例如：
     * 申请创建俱乐部消息ID（message_club_request.msg_id）
     */
    private String relateMsgId;
}
