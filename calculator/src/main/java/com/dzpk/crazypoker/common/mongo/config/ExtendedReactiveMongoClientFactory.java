package com.dzpk.crazypoker.common.mongo.config;

import com.mongodb.MongoClientSettings;
import com.mongodb.reactivestreams.client.MongoClient;
import org.springframework.boot.autoconfigure.mongo.MongoClientSettingsBuilderCustomizer;
import org.springframework.boot.autoconfigure.mongo.MongoProperties;
import org.springframework.boot.autoconfigure.mongo.ReactiveMongoClientFactory;
import org.springframework.core.env.Environment;

import java.util.List;

public class ExtendedReactiveMongoClientFactory extends ReactiveMongoClientFactory {
    private MongoClientSettings settings;

    public ExtendedReactiveMongoClientFactory(MongoProperties properties, Environment environment,
                                              List<MongoClientSettingsBuilderCustomizer> builderCustomizers,
                                              MongoClientSettings settings) {
        super(properties,environment,builderCustomizers);
        this.settings = settings;
    }

    public MongoClient createMongoClient() {
        return super.createMongoClient(this.settings);
    }
}
