package com.dzpk.crazypoker.common.web.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@ApiModel
public class CommonResponse<T> {
    @ApiModelProperty(value = "执行状态码：成功(0)",position = 1)
    private int status=0;
    @ApiModelProperty(value = "错误描述,只做错误描述，不建议直接用来提示终端用户",position = 2)
    private String msg="success";

    @ApiModelProperty(value = "业务数据，成功情况才有数据",position = 3)
    private T data;
}
