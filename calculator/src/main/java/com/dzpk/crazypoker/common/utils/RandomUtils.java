package com.dzpk.crazypoker.common.utils;

import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import java.util.Random;

/**
 * 随机生成
 *
 * <AUTHOR>
 */
public class RandomUtils {

    /**
     * 给定范围获取随机数
     * @param min
     * @param max
     * @return
     */
    public static Integer getRandomIntegerNumber(int min, int max) {
        if(max < min) {
            return null;
        }
        Integer random = (int)(Math.random() * (max - min + 1));
        return random + min;
    }


    public static Double getRandomDoubleNumber(Double min, Double max) {
        if(max < min) {
            return null;
        }
        Double random = Math.random() * (max - min);
        return random + min;
    }

    public static void main(String[] args) {
        for(int i = 0; i < 100; i++) {
            System.out.println(getRandomIntegerNumber(-1, 1));
        }
    }
}
