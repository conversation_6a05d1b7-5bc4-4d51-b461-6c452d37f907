package com.dzpk.crazypoker.common.rabbitmq.client.bean;

import lombok.*;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by jayce on 2019/4/3
 *
 * <AUTHOR>
 */
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class MoneyMessage {

    //发送者id
    protected String senderId;

    //接收者用户id  服务于tim 和 极光
    private List<String> reciverUserIds = new ArrayList<>();

    //头像
    private String header;

    //消息标题
    private String title;

    //消息内容
    private String content;

    //备注
    private String remark;

    //消息类型
    protected Integer type;

    //消息状态 0成功 1失败
    protected Integer msgStatus;

    // 1、tim 2、jpush 3、（1+2）
    protected Integer pushChannel;

}
