package com.dzpk.crazypoker.common.mybatis.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Setter
@Getter
@ConfigurationProperties(prefix = "jdbc")
public class MybatisProperties {
    /** 连接字符串、用户名、密码 */
    private String url;
    private String userName;
    private String password;

    /** 配置初始化大小、最小、最大 */
    private int initialSize=20;
    private int minIdle=20;
    private int maxActive=150;

    /**
     * 配置获取连接等待超时的时间，
     * 单位：毫秒
     */
    private long maxWait=60000;

   /**
    * 配置间隔多久才进行一次检测，
    * 检测需要关闭的空闲连接
    * 单位: 毫秒
    */
   private long timeBetweenEvictionRunsMillis=3000;
   /**
    * 配置一个连接在池中最小生存的时间
    * 单位: 毫秒
    */
   private long minEvictableIdleTimeMillis=300000;

   /**
    * 用来检测连接是否有效的sql，要求是一个查询语句。
    * 如果validationQuery为null，testOnBorrow、testOnReturn、
    * testWhileIdle都不会其作用
    */
   private String validationQuery="SELECT 'x'";
   private boolean testWhileIdle=true;
   private boolean testOnBorrow=true;
   private boolean testOnReturn=true;

   /**
    * 打开PSCache，并且指定每个连接上PSCache的大小
    * Oracle，则把poolPreparedStatements配置为true
    * mysql可以配置为false
    */
   private boolean cachePStatement=false;
   private int maxSizeOfCachedPStatementPerConn=20;

   /**
    * 配置监控统计拦截的filters
    * 去掉后监控界面sql无法统计
    */
   private String filters="stat,wall,slf4j";

   /** 超过时间限制是否回收 */
   private boolean removeAbandoned=false;
   /** 超时时间；单位:秒 */
   private int removeAbandonedTimeout=60;
   /** 关闭abanded连接时输出错误日志 */
   private boolean logAbandoned=true;
}
