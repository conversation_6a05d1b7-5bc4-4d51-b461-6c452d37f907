package com.dzpk.crazypoker.business.handler.bean;


import lombok.*;

import java.sql.Timestamp;

/**
 * TribePaymentActivityTier
 *
 * <AUTHOR> @since 2025/6/20
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TribePaymentActivityTier {

    private Long id;

    /**
     * 联盟ID
     */
    private Integer tribeId;

    /**
     * 层级名称
     */
    private String tierName;

    /**
     * 等级（未分层为0）
     */
    private Integer level;

    /**
     * 备注
     */
    private String remark;

    /**
     * 最低充值笔数
     */
    private Integer minRechargeQuantity;

    /**
     * 最低充值金额
     */
    private Integer minRechargeAmount;

    /**
     * 最低提款笔数
     */
    private Integer minWithdrawQuantity;

    /**
     * 最低提款金额
     */
    private Integer minWithdrawAmount;

    /**
     * 配置唯一ID
     * 形式为 tribe_id:min_recharge_quantity:min_recharge_amount:min_withdraw_quantity:min_withdraw_amount
     */
    private String uniqueId;

    /**
     * 启用状态: 0=禁用, 1=启用
     */
    private Integer isUse;

    /**
     * 锁定状态: 0=解锁, 1=锁定
     */
    private Integer isLock;

    /**
     * 创建时间
     */
    private Timestamp createdAt;

    /**
     * 联盟是否可以管理：0-否，1-是，默认数据，不能删除
     */
    private Boolean manageable;
}
