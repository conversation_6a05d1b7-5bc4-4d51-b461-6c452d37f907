package com.dzpk.crazypoker.business.repositories.mysql;


import com.dzpk.crazypoker.business.handler.bean.BackendTaskRecord;
import org.apache.ibatis.annotations.*;


/**
 * BackendTaskDao
 *
 * <AUTHOR>
 * @since 2025/6/20
 */
@Mapper
public interface BackendTaskDao {

    /**
     * 查询任务
     * @param uniqueId
     * @return
     */
    @Select("select btr.* from crazy_poker.backend_task_record btr where btr.unique_id = #{uniqueId}")
    @Results({
            @Result(property = "taskKey", column = "task_key"),
            @Result(property = "paramsJson", column = "params_json"),
            @Result(property = "errorMessage", column = "error_message"),
            @Result(property = "uniqueId", column = "unique_id"),
            @Result(property = "startAt", column = "start_at"),
            @Result(property = "endAt", column = "end_at"),
            @Result(property = "createdAt", column = "created_at"),
            @Result(property = "updatedAt", column = "updated_at")
    })
    BackendTaskRecord getBackendTaskRecord(@Param("uniqueId") String uniqueId);

    /**
     * update任务进度progress
     */
    @Update({
            "UPDATE crazy_poker.backend_task_record SET progress = #{progress} WHERE unique_id = #{uniqueId}"
    })
    void updateBackendTaskProgress(@Param("uniqueId") String uniqueId, @Param("progress") Integer progress);

    @Update({
            "UPDATE crazy_poker.backend_task_record SET status = 1, start_at = NOW() WHERE unique_id = #{uniqueId}"
    })
    void startTask(@Param("uniqueId") String uniqueId);

    @Update({
            "UPDATE crazy_poker.backend_task_record SET status = 2, end_at = NOW() WHERE unique_id = #{uniqueId}"
    })
    void successTask(@Param("uniqueId") String uniqueId);

    @Update({
            "UPDATE crazy_poker.backend_task_record SET status = 3, end_at = NOW(), error_message = #{errorMessage} WHERE unique_id = #{uniqueId}"
    })
    void failTask(@Param("uniqueId") String uniqueId, @Param("errorMessage") String errorMessage);

}
