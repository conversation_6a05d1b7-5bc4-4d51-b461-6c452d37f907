package com.dzpk.crazypoker.business.util;


import java.net.*;
import java.util.Enumeration;

/**
 * LocalIpUtils
 *
 * <AUTHOR>
 * @since 2025/6/3
 */
public class LocalIpUtils {
    private static final String DEFAULT_IP = "127.0.0.1";

    /**
     * 获取本机IP地址（跳过虚拟接口和回环地址）
     */
    public static String getLocalIP() {
        String ip = getIPFromNetworkInterfaces();
        if (ip != null) return ip;
        try {
            String fallbackIp = InetAddress.getLocalHost().getHostAddress();
            if (!fallbackIp.startsWith("192.")) return fallbackIp;
        } catch (UnknownHostException ignored) {}
        return DEFAULT_IP;
    }

    /**
     * 通过遍历网络接口获取真实IPv4地址
     */
    private static String getIPFromNetworkInterfaces() {
        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface iface = interfaces.nextElement();
                if (shouldSkipInterface(iface)) continue;

                Enumeration<InetAddress> addresses = iface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress addr = addresses.nextElement();
                    if (isValidInetAddress(addr)) {
                        return addr.getHostAddress();
                    }
                }
            }
        } catch (SocketException ignored) {}
        return null;
    }

    // 判断是否应跳过的网络接口
    private static boolean shouldSkipInterface(NetworkInterface iface) throws SocketException {
        String name = iface.getDisplayName().toLowerCase();
        return iface.isLoopback() ||
                !iface.isUp() ||
                name.contains("docker") ||
                name.contains("virtual") ||
                name.contains("br-");
    }

    // 判断是否为有效的IPv4地址
    private static boolean isValidInetAddress(InetAddress addr) {
        return !addr.isLoopbackAddress() &&
                addr instanceof Inet4Address &&
                !addr.getHostAddress().startsWith("192.");
    }
}
