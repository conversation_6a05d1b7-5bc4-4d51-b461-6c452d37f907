package com.dzpk.crazypoker.business.repositories.mysql;


import com.dzpk.crazypoker.business.handler.bean.TribePaymentActivityTier;
import com.dzpk.crazypoker.business.handler.bean.TribeUserPaymentActivityTier;
import com.dzpk.crazypoker.business.handler.bean.UserBalanceCount;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * TribePaymentActivityTierDao
 *
 * <AUTHOR>
 * @since 2025/6/20
 */
@Mapper
public interface TribePaymentActivityTierDao {

    @Select("SELECT * FROM crazy_poker.tribe_payment_activity_tier WHERE tribe_id = #{tribeId} AND is_use = 1 AND is_lock = 0")
    @Results({
            @Result(property = "tribeId", column = "tribe_id"),
            @Result(property = "tierName", column = "tier_name"),
            @Result(property = "minRechargeQuantity", column = "min_recharge_quantity"),
            @Result(property = "minRechargeAmount", column = "min_recharge_amount"),
            @Result(property = "minWithdrawQuantity", column = "min_withdraw_quantity"),
            @Result(property = "minWithdrawAmount", column = "min_withdraw_amount"),
            @Result(property = "uniqueId", column = "unique_id"),
            @Result(property = "isUse", column = "is_use"),
            @Result(property = "isLock", column = "is_lock"),
            @Result(property = "createdAt", column = "created_at"),
    })
    List<TribePaymentActivityTier> findAllByTribeId(@Param("tribeId") Integer tribeId);


    @Select("SELECT * FROM crazy_poker.tribe_payment_activity_tier WHERE tribe_id = #{tribeId} AND is_use = 1 AND is_lock = 0 AND manageable = 0 LIMIT 1")
    @Results({
            @Result(property = "tribeId", column = "tribe_id"),
            @Result(property = "tierName", column = "tier_name"),
            @Result(property = "minRechargeQuantity", column = "min_recharge_quantity"),
            @Result(property = "minRechargeAmount", column = "min_recharge_amount"),
            @Result(property = "minWithdrawQuantity", column = "min_withdraw_quantity"),
            @Result(property = "minWithdrawAmount", column = "min_withdraw_amount"),
            @Result(property = "uniqueId", column = "unique_id"),
            @Result(property = "isUse", column = "is_use"),
            @Result(property = "isLock", column = "is_lock"),
            @Result(property = "createdAt", column = "created_at"),
    })
    TribePaymentActivityTier findDefaultByTribeId(@Param("tribeId") Integer tribeId);

    @Select({
            "<script>",
            "SELECT * FROM crazy_poker.tribe_payment_activity_tier",
            "WHERE is_use = 1 AND is_lock = 0 AND tribe_id = #{tribeId}",
            "AND id IN",
            "<foreach collection='tierIds' item='id' open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            "</script>"
    })
    @Results({
            @Result(property = "tribeId", column = "tribe_id"),
            @Result(property = "tierName", column = "tier_name"),
            @Result(property = "minRechargeQuantity", column = "min_recharge_quantity"),
            @Result(property = "minRechargeAmount", column = "min_recharge_amount"),
            @Result(property = "minWithdrawQuantity", column = "min_withdraw_quantity"),
            @Result(property = "minWithdrawAmount", column = "min_withdraw_amount"),
            @Result(property = "uniqueId", column = "unique_id"),
            @Result(property = "isUse", column = "is_use"),
            @Result(property = "isLock", column = "is_lock"),
            @Result(property = "createdAt", column = "created_at"),
    })
    List<TribePaymentActivityTier> findByTribeIdAndTierIds(@Param("tribeId") Integer tribeId, @Param("tierIds") List<Integer> tierIds);


    @Select({
            "<script>",
            "SELECT * FROM crazy_poker.tribe_user_payment_activity_tier",
            "WHERE tribe_id = #{tribeId}",
            "AND tpa_tier_id IN",
            "<foreach collection='tpaTierIds' item='id' open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            "</script>"
    })
    @Results({
            @Result(property = "tribeId", column = "tribe_id"),
            @Result(property = "clubId", column = "club_id"),
            @Result(property = "userId", column = "user_id"),
            @Result(property = "tpaTierId", column = "tpa_tier_id"),
            @Result(property = "createdAt", column = "created_at")
    })
    List<TribeUserPaymentActivityTier> findByTribeIdAndTpaTierIds(@Param("tribeId") Integer tribeId, @Param("tpaTierIds") List<Integer> tpaTierIds);


    @Select("SELECT " +
            "COUNT(CASE WHEN ubal.type IN (19, 11) THEN ubal.id END) AS recharge_quantity, " +
            "IFNULL(SUM(CASE WHEN ubal.type IN (19, 11, 21) THEN ubal.balance_change END), 0) AS recharge_amount, " +
            "COUNT(CASE WHEN ubal.type IN (15, 20) THEN ubal.id END) AS withdraw_quantity, " +
            "IFNULL(SUM(CASE WHEN ubal.type IN (15, 20, 22) THEN ubal.balance_change END), 0) AS withdraw_amount " +
            "FROM user_balance_audit_log ubal " +
            "WHERE ubal.user_id = #{userId} " +
            "AND ubal.club_id = #{clubId} " +
            "AND (ubal.tribe_id IS NULL OR ubal.tribe_id = #{tribeId})")
    @Results({
            @Result(property = "rechargeQuantity", column = "recharge_quantity"),
            @Result(property = "rechargeAmount", column = "recharge_amount"),
            @Result(property = "withdrawQuantity", column = "withdraw_quantity"),
            @Result(property = "withdrawAmount", column = "withdraw_amount")
    })
    UserBalanceCount countUserBalance(@Param("userId") Integer userId, @Param("clubId") Integer clubId, @Param("tribeId") Integer tribeId);

    @Update("update tribe_user_payment_activity_tier set tpa_tier_id = #{tpaTierId} where tribe_id = #{tribeId} and id = #{id}")
    void updateTribeUserPaymentActivityTier(@Param("tpaTierId") Integer tpaTierId, @Param("tribeId") Integer tribeId, @Param("id") Integer id);

}
