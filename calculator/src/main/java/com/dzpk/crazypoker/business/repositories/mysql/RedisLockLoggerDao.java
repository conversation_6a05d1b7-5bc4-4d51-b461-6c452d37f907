package com.dzpk.crazypoker.business.repositories.mysql;


import com.dzpk.crazypoker.business.handler.bean.RedisLockLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.*;

import java.sql.Timestamp;

/**
 * RedisLockLogger
 *
 * <AUTHOR>
 * @since 2025/6/16
 */
@Mapper
public interface RedisLockLoggerDao {

    @Insert("INSERT INTO crazy_poker.redis_lock_log (" +
            "name, code, config_expire_time, config_time_unit, config_retry_count, config_retry_interval_ms, " +
            "lock_key, lock_value, status, created_at, ip, thread_id) " +
            "VALUES (#{name}, #{code}, #{configExpireTime}, #{configTimeUnit}, #{configRetryCount}, #{configRetryIntervalMs}, " +
            "#{lockKey}, #{lockValue}, #{status}, NOW(), #{ip}, #{threadId})")
    void insertStart(RedisLockLog entity);

    @Update("UPDATE crazy_poker.redis_lock_log SET " +
            "status = #{status}, wait_time_ms = #{waitTimeMs}, real_get_start_at = #{realGetStartAt}, real_get_end_at = #{realGetEndAt}, " +
            "lock_at = #{lockAt}, real_retry_count = #{realRetryCount} " +
            "WHERE lock_key = #{lockKey} AND lock_value = #{lockValue}")
    void updateSuccess(RedisLockLog entity);

    @Update("UPDATE crazy_poker.redis_lock_log SET " +
            "status = #{status}, wait_time_ms = #{waitTimeMs}, error_msg = #{errorMsg}, real_get_end_at = NOW() , real_retry_count = #{realRetryCount} " +
            "WHERE lock_key = #{lockKey} AND lock_value = #{lockValue}")
    void updateFailure(RedisLockLog entity);

    @Update("UPDATE crazy_poker.redis_lock_log SET " +
            "status = #{status}, real_expire_time = NOW(), unlock_at = NOW(), lock_time_ms = #{lockTimeMs} " +
            "WHERE lock_key = #{lockKey} AND lock_value = #{lockValue}")
    void updateUnlock(RedisLockLog entity);

    @Update("UPDATE crazy_poker.redis_lock_log SET " +
            "status = #{status}, error_msg = #{errorMsg}, unlock_at = #{unlockAt}, lock_time_ms = #{lockTimeMs}, real_expire_time = #{realExpireTime} " +
            "WHERE lock_key = #{lockKey} AND lock_value = #{lockValue}")
    void updateUnlockError(@Param("entity") RedisLockLog entity);


}
