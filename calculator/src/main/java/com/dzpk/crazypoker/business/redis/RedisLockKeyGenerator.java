package com.dzpk.crazypoker.business.redis;


import java.util.UUID;

/**
 * RedisLockKeys
 *
 * <AUTHOR>
 * @since 2025/5/8
 */
public class RedisLockKeyGenerator {

    public interface Keys {
        String PLATFORM_ACCOUNT_LOCK = "ACCOUNT_LOCK:PLATFORM:";

        String TRIBE_ACCOUNT_LOCK = "ACCOUNT_LOCK:TRIBE:";

        String CLUB_ACCOUNT_LOCK = "ACCOUNT_LOCK:CLUB:";

        String USER_ACCOUNT_LOCK = "ACCOUNT_LOCK:USER:";

        String BATCH_COINS_RECYCLING_LOCK = "BATCH_COINS_RECYCLING_LOCK:CLUB:";

        String COINS_RECYCLING_LOCK = "COINS_RECYCLING_LOCK:ROOM_USER:";

        String GAME_RECORD_RDBMS_LOCK = "GAME_RECORD_RDBMS_LOCK:ROOM:";

        String GAME_DATA_DAILY_RDBMS_LOCK = "GAME_DATA_DAILY_RDBMS_LOCK:DAY:";
    }

    public static String generatePlatformKey(Integer platformCode) {
        return Keys.PLATFORM_ACCOUNT_LOCK + platformCode;
    }

    public static String generateTribeKey(Integer tribeId) {
        return Keys.TRIBE_ACCOUNT_LOCK + tribeId;
    }

    public static String generateClubKey(Integer clubId) {
        return Keys.CLUB_ACCOUNT_LOCK + clubId;
    }

    public static String generateUserKey(Integer userId) {
        return Keys.USER_ACCOUNT_LOCK + userId;
    }

    public static String generateBatchCoinsRecyclingLock(Integer clubId) {
        return Keys.BATCH_COINS_RECYCLING_LOCK + clubId;
    }

    public static String generateCoinsRecyclingLock(Integer roomId, Integer userId) {
        return Keys.COINS_RECYCLING_LOCK + roomId + ":" + userId;
    }

    public static String generateGameRecordRdbmsLock(Integer roomId) {
        return Keys.GAME_RECORD_RDBMS_LOCK + roomId;
    }

    public static String generateGameDataDailyRdbmsLock(Integer day) {
        return Keys.GAME_DATA_DAILY_RDBMS_LOCK + day;
    }

    public static String generateUUID() {
        return UUID.randomUUID().toString();
    }

}
