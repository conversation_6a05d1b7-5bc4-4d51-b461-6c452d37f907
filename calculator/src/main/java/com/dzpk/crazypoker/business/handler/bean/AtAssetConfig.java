package com.dzpk.crazypoker.business.handler.bean;


import lombok.*;

import java.time.LocalDateTime;

/**
 * AtAssetConfig
 *
 * <AUTHOR>
 * @since 2025/4/25
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class AtAssetConfig {
    private Integer id;
    private Integer alarm;
    private Integer tribeId;
    private String clubRandomId;
    private String clubName;
    private Integer clubId;
    private Long recyclingLimit;
    private Long supplementLimit;
    private Long balanceLimit;
    private Boolean recyclingLimitEnable;
    private Boolean supplementLimitEnable;
    private Boolean balanceLimitEnable;
    private String recyclingLimitRemark;
    private String supplementLimitRemark;
    private LocalDateTime createdTime;
    private Integer createdBy;
    private LocalDateTime updatedTime;
    private Integer updatedBy;
    private String otherSettings;
}
