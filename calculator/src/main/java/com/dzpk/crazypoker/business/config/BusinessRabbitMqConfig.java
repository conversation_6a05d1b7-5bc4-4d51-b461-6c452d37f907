package com.dzpk.crazypoker.business.config;


/**
 * BusinessRabbitMqKeys
 *
 * <AUTHOR>
 * @since 2025/4/25
 */
public interface BusinessRabbitMqConfig {

    interface DelayRetry {

        // 最大重试次数
        int MAX_RETRY_COUNT = 10;

        // 重试间隔时间 (毫秒)
        int RETRY_INTERVAL = 3000;

    }


    interface ExchangeTypes {
        String TOPIC = "topic";
    }

    interface Exchange {
        // 战绩相关
        String GAME_RECORD_SYNC = "poker.topic.exchange.game.record.sync";
        String GAME_RECORD_HAND = "poker.topic.exchange.game.record.hand";
        String GAME_RECORD_DETAIL = "poker.topic.exchange.game.record.detail";
        String GAME_RECORD_PROFIT = "poker.topic.exchange.game.record.profit";
        String GAME_RECORD_RDBMS = "poker.topic.exchange.game.record.rdbms";
        String GAME_DATA_DAILY_RDBMS = "poker.topic.exchange.game.data.daily.rdbms";
        String SYNC_GAME_RECORD_RDBMS = "poker.topic.exchange.sync.game.record.rdbms";
        // 自动回收币类相关
        String COINS_RECYCLING = "poker.topic.exchange.coins.recycling";
        String BATCH_COINS_RECYCLING = "poker.topic.exchange.batch.coins.recycling";
        // 后台任务
        String BACKEND_TASK_HANDLE = "poker.topic.exchange.backend.task.handle";
    }

    interface Queue {
        // 战绩相关
        String GAME_RECORD_SYNC = "poker.queue.game.record.sync";
        String GAME_RECORD_HAND = "poker.queue.game.record.hand";
        String GAME_RECORD_DETAIL = "poker.queue.game.record.detail";
        String GAME_RECORD_PROFIT = "poker.queue.game.record.profit";
        String GAME_RECORD_RDBMS = "poker.queue.game.record.rdbms";
        String GAME_DATA_DAILY_RDBMS = "poker.queue.game.data.daily.rdbms";
        String SYNC_GAME_RECORD_RDBMS = "poker.queue.sync.game.record.rdbms";
        // 自动回收币类相关
        String COINS_RECYCLING = "poker.queue.coins.recycling";
        String BATCH_COINS_RECYCLING = "poker.queue.batch.coins.recycling";
        // 后台任务
        String BACKEND_TASK_HANDLE = "poker.queue.backend.task.handle";
    }

    interface RoutingKey {
        // 战绩相关
        String GAME_RECORD_SYNC = "poker.routing.game.record.sync";
        String GAME_RECORD_HAND = "poker.routing.game.record.hand";
        String GAME_RECORD_DETAIL = "poker.routing.game.record.detail";
        String GAME_RECORD_PROFIT = "poker.routing.game.record.profit";
        String GAME_RECORD_RDBMS = "poker.routing.game.record.rdbms";
        String GAME_DATA_DAILY_RDBMS = "poker.routing.game.data.daily.rdbms";
        String SYNC_GAME_RECORD_RDBMS = "poker.routing.sync.game.record.rdbms";
        // 自动回收币类相关
        String COINS_RECYCLING = "poker.routing.coins.recycling";
        String BATCH_COINS_RECYCLING = "poker.routing.batch.coins.recycling";
        // 后台任务
        String BACKEND_TASK_HANDLE = "poker.routing.backend.task.handle";
    }

}
