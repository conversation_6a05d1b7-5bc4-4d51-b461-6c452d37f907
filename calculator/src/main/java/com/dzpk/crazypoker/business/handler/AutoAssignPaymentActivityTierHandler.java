package com.dzpk.crazypoker.business.handler;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dzpk.crazypoker.business.config.BackendTaskRecordStatus;
import com.dzpk.crazypoker.business.handler.bean.BackendTaskRecord;
import com.dzpk.crazypoker.business.handler.bean.TribePaymentActivityTier;
import com.dzpk.crazypoker.business.handler.bean.TribeUserPaymentActivityTier;
import com.dzpk.crazypoker.business.handler.bean.UserBalanceCount;
import com.dzpk.crazypoker.business.repositories.mysql.BackendTaskDao;
import com.dzpk.crazypoker.business.repositories.mysql.TribePaymentActivityTierDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * AutoAssignPaymentActivityTierHandler
 *
 * <AUTHOR>
 * @since 2025/6/20
 */
@Slf4j
@Service
public class AutoAssignPaymentActivityTierHandler {

    @Resource
    TribePaymentActivityTierDao tribePaymentActivityTierDao;

    @Resource
    private BackendTaskDao backendTaskDao;

    /**
     * 处理自动分配支付活动等级任务
     * @param backendTaskRecord 任务记录
     */
    @Transactional
    public void handle(BackendTaskRecord backendTaskRecord) {
        try {
            String paramsJson = backendTaskRecord.getParamsJson();
            log.info("[AutoAssignPaymentActivityTierHandler] Handling auto assign payment activity tier task, uniqueId: {}, paramsJson: {}",
                    backendTaskRecord.getUniqueId(), paramsJson);

            // 转成JSONObject
            JSONObject params = JSONObject.parseObject(paramsJson);

            // 获取联盟ID
            Integer tribeId = params.getInteger("tribeId");

            if (tribeId == null) {
                log.error("[AutoAssignPaymentActivityTierHandler] Tribe ID is null, uniqueId: {}", backendTaskRecord.getUniqueId());
                backendTaskDao.failTask(backendTaskRecord.getUniqueId(), "Tribe ID is null");
                return;
            }

            // 获取分层数组
            JSONArray tierIdsJson = params.getJSONArray("tierIds");

            List<TribePaymentActivityTier> tierList = null;

            // 如果tierIds为空则查询联盟底下的所有分层
            if (tierIdsJson == null || tierIdsJson.isEmpty()) {
                log.info("[AutoAssignPaymentActivityTierHandler] No tier IDs provided, querying all tiers for tribeId: {}", tribeId);
                // 查询联盟底下的所有分层
                tierList = tribePaymentActivityTierDao.findAllByTribeId(tribeId);
            } else {
                // 根据ID查询
                log.info("[AutoAssignPaymentActivityTierHandler] Tier IDs provided, querying specific tiers for tribeId: {}, tierIds: {}", tribeId, tierIdsJson.toJSONString());
                tierList = tribePaymentActivityTierDao.findByTribeIdAndTierIds(tribeId, tierIdsJson.toJavaList(Integer.class));
            }

            if (tierList == null || tierList.isEmpty()) {
                log.warn("[AutoAssignPaymentActivityTierHandler] No tiers found for tribeId: {}, tierIds: {}", tribeId, tierIdsJson);
                backendTaskDao.failTask(backendTaskRecord.getUniqueId(), "No tiers found for the provided tribe ID");
                return;
            }

            // tierList 排序，按照level倒序
            tierList.sort((t1, t2) -> Integer.compare(t2.getLevel(), t1.getLevel()));

            log.info("[AutoAssignPaymentActivityTierHandler] sorted tierList: {}", JSONArray.toJSONString(tierList));

            // 查询默认未分层
            TribePaymentActivityTier defaultTier= tribePaymentActivityTierDao.findDefaultByTribeId(tribeId);

            // 获取所有分层ID
            List<Integer> tierIds = tierList.stream()
                    .map(tier -> tier.getId().intValue())
                    .collect(Collectors.toList());

            // 查询这批用户
            List<TribeUserPaymentActivityTier> users = tribePaymentActivityTierDao.findByTribeIdAndTpaTierIds(tribeId, tierIds);

            // 迭代每个用户
            for (TribeUserPaymentActivityTier user : users) {
                // 统计用户账户
                UserBalanceCount userBalanceCount = tribePaymentActivityTierDao.countUserBalance(user.getUserId(), user.getClubId(), user.getTribeId());

                TribePaymentActivityTier assignTier = checkUserTier(userBalanceCount, tierList, defaultTier);

                if (assignTier == null) {
                    assignTier = defaultTier;
                }

                // 更新用户分层
                tribePaymentActivityTierDao.updateTribeUserPaymentActivityTier(
                        assignTier.getId().intValue(),
                        user.getTribeId(),
                        user.getId().intValue()
                );
            }

            // update task status
            log.info("[AutoAssignPaymentActivityTierHandler] Successfully handled auto assign payment activity tier task, uniqueId: {}", backendTaskRecord.getUniqueId());
            backendTaskDao.successTask(backendTaskRecord.getUniqueId());
        } catch (Exception e) {
            // update task status to failed
            log.error("[AutoAssignPaymentActivityTierHandler] Error handling auto assign payment activity tier task, uniqueId: {}, error: {}", backendTaskRecord.getUniqueId(), e.getMessage(), e);
            backendTaskDao.failTask(backendTaskRecord.getUniqueId(), e.getMessage());
        }
    }

    /**
     * 检查用户的分层
     * @param userBalanceCount
     * @param tierList
     * @param defaultTier
     * @return
     */
    public TribePaymentActivityTier checkUserTier(
            UserBalanceCount userBalanceCount,
            List<TribePaymentActivityTier> tierList,
            TribePaymentActivityTier defaultTier) {

        // tierList 已经按等级排序，因此只要有一个满足的则直接返回
        for (TribePaymentActivityTier tribePaymentActivityTier : tierList) {
            // 获取用户的数据
            Long userRechargeQuantity = userBalanceCount.getRechargeQuantity();
            Long userRechargeAmount = Math.abs(userBalanceCount.getRechargeAmount());
            Long userWithdrawQuantity = userBalanceCount.getWithdrawQuantity();
            Long userWithdrawAmount = Math.abs(userBalanceCount.getWithdrawAmount());

            // 获取分层的配置,配置的值需要*100
            Long minRechargeQuantity = tribePaymentActivityTier.getMinRechargeQuantity().longValue();
            Long minRechargeAmount = Math.abs(tribePaymentActivityTier.getMinRechargeAmount() * 100L);
            Long minWithdrawQuantity = tribePaymentActivityTier.getMinWithdrawQuantity().longValue();
            Long minWithdrawAmount = Math.abs(tribePaymentActivityTier.getMinWithdrawAmount() * 100L);

            log.info("[AutoAssignPaymentActivityTierHandler] checkUserTier => rechargeQuantity = user[{}] >= tier[{}] = {}, " +
                            "rechargeAmount = user[{}] >= tier[{}] = {}, " +
                            "withdrawQuantity = user[{}] >= tier[{}] = {}, " +
                            "withdrawAmount = user[{}] >= tier[{}] = {}",
                    userRechargeQuantity, minRechargeQuantity, userRechargeQuantity >= minRechargeQuantity,
                    userRechargeAmount, minRechargeAmount, userRechargeAmount >= minRechargeAmount,
                    userWithdrawQuantity, minWithdrawQuantity, userWithdrawQuantity >= minWithdrawQuantity,
                    userWithdrawAmount, minWithdrawAmount, userWithdrawAmount >= minWithdrawAmount);

            // 需要全部条件都满足才能分配到该分层
            if (userRechargeQuantity >= minRechargeQuantity &&
                userRechargeAmount >= minRechargeAmount &&
                userWithdrawQuantity >= minWithdrawQuantity &&
                userWithdrawAmount >= minWithdrawAmount) {

                // 满足条件，返回该分层
                return tribePaymentActivityTier;
            }
        }

        // 如果没有找到合适的分层，则返回默认分层
        return defaultTier;
    }

}
