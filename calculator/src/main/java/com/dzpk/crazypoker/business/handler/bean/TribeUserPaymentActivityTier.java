package com.dzpk.crazypoker.business.handler.bean;


import lombok.*;

import java.sql.Timestamp;

/**
 * TribeUserPaymentActivityTier
 *
 * <AUTHOR> @since 2025/6/20
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TribeUserPaymentActivityTier {

    private Long id;

    /**
     * 联盟ID
     */
    private Integer tribeId;

    /**
     * 俱乐部ID
     */
    private Integer clubId;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 联盟支付活动分层ID
     */
    private Long tpaTierId;

    /**
     * 创建时间
     */
    private Timestamp createdAt;

}
