package com.dzpk.crazypoker.business.receiver;

import com.dzpk.crazypoker.business.config.BusinessRabbitMqConfig;
import com.dzpk.crazypoker.business.handler.BackendTaskHandler;
import com.dzpk.crazypoker.business.handler.bean.BackendTask;
import com.dzpk.crazypoker.business.receiver.bean.DelayRetryConfig;
import com.dzpk.crazypoker.business.util.JSONObjectUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.concurrent.atomic.AtomicReference;

/**
 * ChannelCoinsRecyclingReceiver
 *
 * <AUTHOR>
 * @since 2025/4/25
 */
@Slf4j
@Component
public class BackendTaskReceiver extends AbstractBusinessReceiver {

    private static final DelayRetryConfig DELAY_RETRY;

    static {
        DELAY_RETRY = DelayRetryConfig.builder()
                .maxRetryCount(20)
                .baseInterval(1500)
                .backoffStrategy(DelayRetryConfig.BackoffStrategy.EXPONENTIAL)
                .backoffFactor(1.2)
                .dataTTL(120)
                .queue(BusinessRabbitMqConfig.Queue.BACKEND_TASK_HANDLE)
                .exchange(BusinessRabbitMqConfig.Exchange.BACKEND_TASK_HANDLE)
                .routingKey(BusinessRabbitMqConfig.RoutingKey.BACKEND_TASK_HANDLE)
                .build();
    }

    @Override
    public DelayRetryConfig getDelayRetryConfig() {
        return DELAY_RETRY;
    }


    @Resource
    BackendTaskHandler backendTaskHandler;


    @RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(
                            value = BusinessRabbitMqConfig.Queue.BACKEND_TASK_HANDLE,
                            durable = "true"
                    ),
                    exchange = @Exchange(
                            value = BusinessRabbitMqConfig.Exchange.BACKEND_TASK_HANDLE,
                            type = ExchangeTypes.TOPIC
                    ),
                    key = BusinessRabbitMqConfig.RoutingKey.BACKEND_TASK_HANDLE
            ),
            concurrency = "5-10"
    )
    public void receive(
            @Payload BackendTask message,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
            Channel channel) {
        try {
            // 检查参数
            checkParams(message);
            AtomicReference<Boolean> result = new AtomicReference<>(false);
            result.set(backendTaskHandler.handle(message));
            if (result.get()) {
                log.info("BACKEND_TASK_HANDLE success, params: {}", JSONObjectUtils.toJsonString(message));
                ack(channel, deliveryTag);
            } else {
                log.error("BACKEND_TASK_HANDLE failed, params: {}", JSONObjectUtils.toJsonString(message));
                errorRetry(channel, deliveryTag, "BACKEND_TASK_HANDLE failed.", message, generateRetryId(message));
            }
        } catch (Exception e) {
            errorAck(channel, deliveryTag, e.getMessage());
        }
    }

    /**
     * 生成重试ID
     * @param params 参数
     * @return 重试ID
     */
    private String generateRetryId(BackendTask params) {
        // retry:message:COINS_RECYCLING:roomId:userId
        return String.format("retry:message:BACKEND_TASK_HANDLE:%s", params.getUniqueId());
    }


    /**
     * 校验参数
     */
    private void checkParams(BackendTask message) {
        log.info("BACKEND_TASK_HANDLE receive message : {}", message);
        // 转成json
        Assert.notNull(message, "params is null, BACKEND_TASK_HANDLE failed.");
        // 校验参数
        Assert.notNull(message.getUniqueId(), "uniqueId is null, BACKEND_TASK_HANDLE failed.");
    }

}
