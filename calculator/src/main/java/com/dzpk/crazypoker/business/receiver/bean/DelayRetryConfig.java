package com.dzpk.crazypoker.business.receiver.bean;


import com.dzpk.crazypoker.business.config.BusinessRabbitMqConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DelayRetry
 *
 * <AUTHOR>
 * @since 2025/4/25
 */
@Data
@Builder
@AllArgsConstructor
public class DelayRetryConfig {

    /**
     * 最大重试次数
     * 示例：3（最多尝试3次，包括首次执行）
     */
    private final int maxRetryCount;

    /**
     * 基础间隔时间（单位：毫秒）
     * 示例：5000（5秒）
     */
    private final long baseInterval;

    /**
     * 退避策略（默认指数退避）
     */
    @Builder.Default
    private final BackoffStrategy backoffStrategy = BackoffStrategy.EXPONENTIAL;

    /**
     * 退避因子（不同策略下作用不同）
     * - 指数退避：间隔 = baseInterval * (backoffFactor)^n
     * - 线性退避：间隔 = baseInterval + backoffFactor * n
     * - 固定间隔：backoffFactor无效
     */
    @Builder.Default
    private final double backoffFactor = 2.0;

    /**
     * Redis数据保留时间（单位：分钟）
     * 防止内存泄漏，默认24小时
     */
    @Builder.Default
    private final int dataTTL = 24 * 60;

    /**
     * 目标Exchange名称
     */
    private final String exchange;

    /**
     * 目标RoutingKey
     */
    private final String routingKey;

    /**
     * 目标队列名称
     */
    private final String queue;

    /**
     * 退避策略枚举
     */
    public enum BackoffStrategy {
        /**
         * 固定间隔：每次间隔相同
         * 公式：baseInterval
         */
        FIXED,

        /**
         * 线性增长间隔
         * 公式：baseInterval + backoffFactor * attempt
         */
        LINEAR,

        /**
         * 指数增长间隔（默认）
         * 公式：baseInterval * (backoffFactor ^ attempt)
         */
        EXPONENTIAL
    }



}
