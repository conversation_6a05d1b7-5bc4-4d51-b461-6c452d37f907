package com.dzpk.crazypoker.business.handler.bean;


import lombok.*;

import java.sql.Timestamp;

/**
 * BackendTaskRecord
 *
 * <AUTHOR>
 * @since 2025/6/20
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class BackendTaskRecord {


    private Long id;

    /**
     * 任务KEY
     */
    private String taskKey;

    /**
     * 任务编码
     */
    private String code;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 状态: 0待执行, 1执行中, 2执行成功, 3执行失败
     */
    private Integer status;

    /**
     * 任务参数 JSON 格式
     */
    private String paramsJson;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 唯一ID，格式: code:yyyymmhhmm
     */
    private String uniqueId;

    /**
     * 任务进度
     */
    private Integer progress;

    /**
     * 开始时间
     */
    private Timestamp startAt;

    /**
     * 结束时间
     */
    private Timestamp endAt;

    /**
     * 创建时间
     */
    private Timestamp createdAt;

    /**
     * 更新时间
     */
    private Timestamp updatedAt;


}
