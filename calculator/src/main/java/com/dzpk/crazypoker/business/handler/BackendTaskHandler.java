package com.dzpk.crazypoker.business.handler;


import com.dzpk.crazypoker.business.config.BackendTaskRecordKey;
import com.dzpk.crazypoker.business.config.BackendTaskRecordStatus;
import com.dzpk.crazypoker.business.handler.bean.BackendTask;
import com.dzpk.crazypoker.business.handler.bean.BackendTaskRecord;
import com.dzpk.crazypoker.business.repositories.mysql.BackendTaskDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * BackendTaskHandler
 *
 * <AUTHOR>
 * @since 2025/6/20
 */
@Slf4j
@Component
public class BackendTaskHandler {

    @Resource
    BackendTaskDao backendTaskDao;

    @Resource
    AutoAssignPaymentActivityTierHandler autoAssignPaymentActivityTierHandler;

    /**
     * 处理
     * @param param
     * @return
     */
    public Boolean handle(BackendTask param) {

        // 直接查询任务
        BackendTaskRecord backendTaskRecord = backendTaskDao.getBackendTaskRecord(param.getUniqueId());

        if (backendTaskRecord == null) {
            return true;
        }

        log.info("[BackendTaskHandler] Processing task, uniqueId: {}, status: {}, key: {}",
                param.getUniqueId(), backendTaskRecord.getStatus(), backendTaskRecord.getTaskKey());

        // 如果状态为执行中或者执行成功，则直接返回
        if (Objects.equals(backendTaskRecord.getStatus(), BackendTaskRecordStatus.IN_PROGRESS)
                || Objects.equals(backendTaskRecord.getStatus(), BackendTaskRecordStatus.SUCCESS)) {
            log.info("[BackendTaskHandler] Task already processed, uniqueId: {}, status: {}",
                    param.getUniqueId(), backendTaskRecord.getStatus());
            return true;
        }

        // 修改成执行中
        backendTaskDao.startTask(param.getUniqueId());

        // 根据key区分任务
        String key = backendTaskRecord.getTaskKey();

        // 处理自动分配支付活动等级任务
        if (key.equals(BackendTaskRecordKey.AUTO_ASSIGN_PAYMENT_ACTIVITY_TIER)) {
            autoAssignPaymentActivityTierHandler.handle(backendTaskRecord);
            // 处理游戏数据每日同步任务
            log.info("[BackendTaskHandler] Handling game data daily RDBMS task, uniqueId: {}", param.getUniqueId());
        }
        log.info("[BackendTaskHandler] Unhandled task key: {}, uniqueId: {}", key, param.getUniqueId());
        return true;
    }

}
