package com.dzpk.crazypoker.promotion.repository;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Field;

@NoArgsConstructor
@Setter
@Getter
public class AofGameDataProfitPo {

    @Field("user_id")
    private Integer userId;
    @Field("af_rate")
    private Integer afRate;
    @Field("pool_rate")
    private Integer poolRate;
    @Field("week_win_rate")
    private Integer weekWinRate;
    @Field("week_pool_rate")
    private Integer weekPoolRate;
    @Field("week_af_rate")
    private Integer weekAfRate;
    @Field("week_not_fanpai_win_rate")
    private Integer weekNotFanpaiWinRate;
    @Field("pool_win_rate")
    private Integer poolWinRate;
    @Field("month_bring_in")
    private Integer monthBringIn;
    @Field("week_total_hand")
    private Integer weekTotalHand;
    @Field("week_tanpai_win_rate")
    private Integer weekTanPaiWinRate;
    @Field("month_pool_rate")
    private Integer monthPoolRate;
    @Field("week_allin_win_rate")
    private Integer weekAllinWinRate;
    @Field("month_total_hand")
    private Integer monthTotalHand;
    @Field("game_cnt")
    private Integer gameCnt;
    @Field("month_af_rate")
    private Integer monthAfRate;
    @Field("cbet_rate")
    private Integer cbetRate;
    @Field("month_pool_win_rate")
    private Integer monthPoolWinRate;
    @Field("month_per")
    private Integer monthPer;
    @Field("bring_in")
    private Integer bringIn;
    @Field("month_fanpai_win_rate")
    private Integer monthFanpaiWinRate;
    @Field("fanpai_win_rate")
    private Integer fanpaiWinRate;
    @Field("per")
    private Integer per;
    @Field("win_rate")
    private Integer winRate;
    @Field("week_pool_win_rate")
    private Integer weekPoolWinRate;
    @Field("month_not_fanpai_win_rate")
    private Integer monthNotFanpaiWinRate;
    @Field("total_earn")
    private Integer totalEarn;
    @Field("month_cbet_rate")
    private Integer monthCbetRate;
    @Field("month_bet3_rate")
    private Integer monthBet3Rate;
    @Field("week_total_earn")
    private Integer weekTotalEarn;
    @Field("total_hand")
    private Integer totalHand;
    @Field("not_fanpai_win_rate")
    private Integer notFanpaiWinRate;
    @Field("week_game_cnt")
    private Integer weekGameCnt;
    @Field("week_bring_in")
    private Integer weekBringIn;
    @Field("month_tanpai_win_rate")
    private Integer monthTanPaiWinRate;
    @Field("tanpai_win_rate")
    private Integer tanPaiWinRate;
    @Field("month_win_rate")
    private Integer monthWinRate;
    @Field("bet3_rate")
    private Integer bet3Rate;
    @Field("month_total_earn")
    private Integer monthTotalEarn;
    @Field("week_per")
    private Integer weekPer;
    @Field("week_bet3_rate")
    private Integer weekBet3Rate;
    @Field("month_game_cnt")
    private Integer monthGameCnt;
    @Field("month_allin_win_rate")
    private Integer monthAllinWinRate;
    @Field("week_fanpai_win_rate")
    private Integer weekFanpaiWinRate;
    @Field("allin_win_rate")
    private Integer allinWinRate;
    @Field("week_cbet_rate")
    private Integer weekCbetRate;
}
