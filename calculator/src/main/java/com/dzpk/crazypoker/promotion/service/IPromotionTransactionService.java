package com.dzpk.crazypoker.promotion.service;

import com.dzpk.crazypoker.promotion.service.bean.Achievements;
import com.dzpk.crazypoker.promotion.service.bean.FeedBackPromotionBeans;
import com.dzpk.crazypoker.promotion.service.bean.GameRecordRawData;

/**
 * Description:
 * <p>
 * Created by <PERSON><PERSON> on 2019/5/9 14:54
 */
public interface IPromotionTransactionService {

    /**
     * 处理原始数据
     * @param rawData
     * @throws Exception
     */
    public void handle(GameRecordRawData rawData) throws  Exception;

    /**
     * 计算返豆
     * @param achievement
     * @throws Exception
     */
    public void calculateFeedback(Achievements achievement) throws  Exception;

    /**
     * 每天6点返豆
     * @param feedBackPromotionBeans
     * @throws Exception
     */
    public void feedBackPromotionBeansTransaction(FeedBackPromotionBeans feedBackPromotionBeans) throws  Exception;

    /**
     * 推送分享返豆消息
     * @param feedBackPromotionBeans
     * @throws Exception
     */
    public void pushPromotionFeedbackMessageTransaction(FeedBackPromotionBeans feedBackPromotionBeans) throws Exception;

}
