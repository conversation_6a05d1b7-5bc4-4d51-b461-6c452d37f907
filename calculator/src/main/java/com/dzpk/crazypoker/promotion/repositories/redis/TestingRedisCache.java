package com.dzpk.crazypoker.promotion.repositories.redis;

import com.alibaba.fastjson.JSONObject;
import com.dzpk.crazypoker.common.redis.factory.IRedisInstanceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Service
public class TestingRedisCache {

    private String SYSTEM_CALCULATEPROMOTIONBEANS_TESTING_REDIS_KEY = "system:calculator:calculatePromotionBeans";

    /** 操作redis的template */
    @Autowired
    private IRedisInstanceFactory redisInstanceFactory;

    protected RedisTemplate redisTemplate(){
        return this.redisInstanceFactory.defaultRedisInstance().getTemplate();
    }

    protected StringRedisTemplate stringRedisTemplate(){
        return this.redisInstanceFactory.defaultRedisInstance().getStringTemplate();
    }

    public Map<String,Integer> getTestingValue(){
        ValueOperations<String, String> stringStringValueOperations = this.stringRedisTemplate().opsForValue();
        String s = stringStringValueOperations.get(SYSTEM_CALCULATEPROMOTIONBEANS_TESTING_REDIS_KEY);
        Map<String,Integer> objectMap=new HashMap<>();
        objectMap.put("newValue",JSONObject.parseObject(s).getIntValue("newTestingValue"));
        objectMap.put("oldValue",JSONObject.parseObject(s).getIntValue("oldTestingValue"));
        return objectMap;
    }
    public void addTestingValue(Map<String,Integer> map){
        this.stringRedisTemplate().opsForValue().set(SYSTEM_CALCULATEPROMOTIONBEANS_TESTING_REDIS_KEY,
                JSONObject.toJSONString(map));

    }
}
