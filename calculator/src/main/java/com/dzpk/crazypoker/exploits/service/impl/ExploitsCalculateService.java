package com.dzpk.crazypoker.exploits.service.impl;

import com.dzpk.crazypoker.common.utils.DateUtils;
import com.dzpk.crazypoker.exploits.bean.FeedbackExploitsBeans;
import com.dzpk.crazypoker.exploits.repositories.mysql.IExploitsDao;
import com.dzpk.crazypoker.exploits.service.IExploitsCalculateService;
import com.dzpk.crazypoker.exploits.service.IExploitsTransactionService;
import com.dzpk.crazypoker.promotion.repositories.mysql.IPromotionDao;
import com.dzpk.crazypoker.promotion.service.bean.GameRawDataUseType;
import com.dzpk.crazypoker.promotion.service.bean.GameRecordRawData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Description:
 * <p>
 * Created by <PERSON><PERSON> on 2019/4/23 20:50
 */
@Service
@Slf4j
public class ExploitsCalculateService implements IExploitsCalculateService {

    @Autowired
    private IExploitsDao iExploitsDao;

    @Autowired
    private IPromotionDao iPromotionDao;

    @Autowired
    private IExploitsTransactionService exploitsTransactionService;

    @Override
    public void processExploitsAchievements() throws Exception {
        try {
            List<GameRecordRawData> gameRecordRawDatas = iPromotionDao.loadUntreatedGameRecord(GameRawDataUseType.EXPLOITS.getCode());
            if(gameRecordRawDatas != null && gameRecordRawDatas.size() > 0) {
                // 更新状态为处理中
                iPromotionDao.updateGameRawDataStatus(gameRecordRawDatas, 2, GameRawDataUseType.EXPLOITS.getCode());

                // 拆分数据,计算业绩
                for(GameRecordRawData rawData : gameRecordRawDatas) {
                    exploitsTransactionService.handle(rawData);
                }
            }
        } catch (Exception e) {
            log.error("process exploits achievements error.", e);
            throw e;
        }
    }

    /**
     * 返豆
     */
    @Override
    public void feedbackExploitsBeans() throws Exception {
        Date today = DateUtils.getDateWithZeroTime(new Date(), 0);
        while(true) {
            List<FeedbackExploitsBeans> feedbackExploitsBeans = iExploitsDao.queryFeedbackExploitsBeans(today);
            if(feedbackExploitsBeans != null && feedbackExploitsBeans.size() != 0) {
                for(FeedbackExploitsBeans feedbackExploitsBean : feedbackExploitsBeans) {
                    try {
                        exploitsTransactionService.feedBackExploitsBeansTransaction(feedbackExploitsBean);
                    } catch (Exception e) {
                        // 修改返豆记录为手工返佣，不抛出异常，让循环继续下去
                        iExploitsDao.updateFeedbackManualStatus(feedbackExploitsBean.getId());
                        log.error("feed back exploits beans error.", e);
                    }
                }
            } else {
                break;
            }
        }
    }

    /**
     * 返回战绩返佣消息
     * @throws Exception
     */
    @Override
    public void pushExploitsFeedbackMessage() {
        while(true) {
            // 查询昨日的未发送消息，发送MQ消息
            List<FeedbackExploitsBeans>  exploitsBeansList = iExploitsDao.queryUnpushMessage();

            if(exploitsBeansList != null && exploitsBeansList.size() != 0) {
                log.debug("pushExploitsFeedbackMessage size:{}.", exploitsBeansList.size());
                for(FeedbackExploitsBeans exploitsBeans : exploitsBeansList) {
                    try {
                        exploitsTransactionService.pushExploitsFeedbackMessageTransaction(exploitsBeans);
                    } catch (Exception e) {
                        log.error("pushExploitsFeedbackMessageTransaction error.", e);
                    }
                }
            } else {
                log.debug("pushExploitsFeedbackMessage end.");
                break;
            }
        }

    }
}
