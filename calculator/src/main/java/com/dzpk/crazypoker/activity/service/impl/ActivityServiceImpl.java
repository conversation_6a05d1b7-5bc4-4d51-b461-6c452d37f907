package com.dzpk.crazypoker.activity.service.impl;

import com.dzpk.crazypoker.activity.repositories.mysql.IActivityDao;
import com.dzpk.crazypoker.activity.repositories.mysql.IPlatformAccountCustomizeDao;
import com.dzpk.crazypoker.activity.repositories.mysql.IPlatformAccountDao;
import com.dzpk.crazypoker.activity.repositories.redis.ActivityRedisCache;
import com.dzpk.crazypoker.activity.service.IActivityService;
import com.dzpk.crazypoker.activity.service.bean.*;
import com.dzpk.crazypoker.common.rabbitmq.client.MessageSender;
import com.dzpk.crazypoker.common.rabbitmq.client.bean.InteriorMessage;
import com.dzpk.crazypoker.common.rabbitmq.client.bean.user.UserAccountGiveInfo;
import com.dzpk.crazypoker.common.rabbitmq.client.bean.user.UserDataMessage;
import com.dzpk.crazypoker.common.rabbitmq.constant.EMessageCode;
import com.dzpk.crazypoker.common.rabbitmq.constant.EUserEventCode;
import com.dzpk.crazypoker.common.utils.PhoneNumUtil;
import com.dzpk.crazypoker.game.repositories.mysql.IUserDataDao;
import com.dzpk.crazypoker.game.service.IUserDataService;
import com.dzpk.crazypoker.promotion.repositories.mongo.bean.UserDataStatistics;
import com.dzpk.crazypoker.promotion.repositories.mongo.dao.UserDataStatisticsDao;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.dzpk.crazypoker.common.mongo.factory.IMongoInstanceFactory;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import com.dzpk.crazypoker.activity.repositories.mongo.AddRequestInfo;

/**
 * Created by jayce on 2019/6/21
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ActivityServiceImpl implements IActivityService {

    @Autowired
    private IActivityDao activityDao;
	
	@Autowired
    private IMongoInstanceFactory iMongoInstanceFactory;

    private ReactiveMongoTemplate getReactiveMongoTemplate() {
        return iMongoInstanceFactory.defaultInstance().getTemplate();
    }

    @Autowired
    private UserDataStatisticsDao userDataStatisticsDao;

    @Autowired
    private IPlatformAccountCustomizeDao platformAccountCustomizeDao;

    @Autowired
    private IPlatformAccountDao platformAccountDao;

    @Autowired
    private MessageSender messageSender;

    @Autowired
    private ActivityRedisCache activityRedisCache;

    @Autowired
    private IUserDataService userDataService;

    @Autowired
    private IUserDataDao userDataDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processSkyRpTask() throws Exception{
        boolean needPrintLog = false;
        List<String> allUserIdList = new ArrayList<>();
        List<ActivityRpUserInfo> rpUserInfoList = new ArrayList<>();
        try {
            //1、从数据库查询活动是否开启
            ActivityRpLimitConfig activityConfig = this.activityDao.findRpActivityStatus();
            if(null == activityConfig || null == activityConfig.getStatus() || activityConfig.getStatus() != 1){//没有该活动或者活动不是开启状态
                log.warn("processSkyRpTask activity not running={}" + activityConfig.toString());
                return;
            }else{//活动正在进行中
                Calendar cal = Calendar.getInstance();
                cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
                Date startDate = cal.getTime();
                cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH), 23, 59, 59);
                Date endDate = cal.getTime();
                Long count = this.activityDao.countRpActivityCurrentDayCount(startDate,endDate);
                if(count >= activityConfig.getCountLimit()){//如果超出今日赠送上限，停止今日赠送
                    log.warn("processSkyRpTask activity current day count limit");
                    return;
                }else{
                    //2、从数据库（room_search）中查询出当前正在进行中的牌局id（这一步是避免房间结束没有清理干净）
                    List<Integer> playingRoomIdList = this.activityDao.findPlayingRoomId();
                    if(null == playingRoomIdList || playingRoomIdList.size() <= 0){//没有正常进行的房间
                        log.warn("processSkyRpTask not playing game");
                        return;
                    }
                    List<String> allUserPhoneList = new ArrayList<>();
                    List<UserInfo> allUserInfo = new ArrayList<>();
                    Map<String,Integer> tempAllUserInfoMap = new HashMap<>();
                    Map<String,Integer> allUserInfoMap = new HashMap<>();

                    Map<String,Set<String>> userJoinedRoomidMap = new HashMap<>();
                    Map<String,Set<String>> playingRoomMap = new HashMap<>();

                    //3、从redis查询对应房间的资格key，遍历房间对应的玩家set集合，取出玩家userid，记录一个玩家map，key对应玩家userid，val对应一个set
                    playingRoomMap = this.activityRedisCache.getPlayIngPlayer();
                    if(null == playingRoomMap || playingRoomMap.size() <= 0){
                        log.warn("processSkyRpTask activity don't have can join player 1");
                        return;
                    }
                    for(String roomId : playingRoomMap.keySet()){
                        if(playingRoomIdList.contains(Integer.parseInt(roomId))){//与数据库正在进行的牌局相匹配
                            for(String userId : playingRoomMap.get(roomId)){
                                if(userJoinedRoomidMap.containsKey(userId)){
                                    userJoinedRoomidMap.get(userId).add(roomId);
                                }else{
                                    Set<String> set = new HashSet<>();
                                    set.add(roomId);
                                    userJoinedRoomidMap.put(userId,set);
                                    allUserIdList.add(userId);
                                }
                            }
                        }
                    }

                    // 4、从发放记录查出今天已经获取资格的玩家userid，剔除第三步没有资格的玩家id
                    List<String> gainUserIdList = this.activityDao.findCurrentDayGainUserId(startDate,endDate);
                    allUserIdList.removeAll(gainUserIdList);

                    //5、随机从map中随机取出百分之5的玩家，不满10人则上调至10人，全部玩家不足10人则全部玩家都抽中资格
                    if(allUserIdList.size() <= 0){//没有资格
                        log.warn("processSkyRpTask activity don't have can join player 2");
                        return;
                    }

                    //加入绿名单因素 随机之前，先查出当前正在打的绿名单玩家，优先给予资格 by 20190718
                    List<String> greenUserIdList = this.userDataDao.findUserIds(allUserIdList,2);
                    boolean hasGrrenUser = greenUserIdList != null && greenUserIdList.size() > 0 ? true : false;//这次活动中是否有绿名单用户
                    boolean needCalGrren = true;//需要执行绿名单优先逻辑

                    int rewardUserCount = 0;
                    int userSize = allUserIdList.size();
                    if(userSize < 200){//200人以下都是不满足百分之五10个人的条件，则取10
                        if(userSize < 10){
                            rewardUserCount = userSize;
                            needCalGrren = false;
                        }else{
                            if(userSize == 10){
                                needCalGrren = false;
                            }
                            rewardUserCount = 10;
                        }
                    }else{
                        BigDecimal allUserCount = new BigDecimal(userSize);
                        allUserCount = allUserCount.multiply(new BigDecimal(0.05));
                        rewardUserCount = (int)Math.ceil(allUserCount.doubleValue());
                    }
                    if(hasGrrenUser && needCalGrren){
                        //绿名单人数没有那么多，先让绿名单有资格，其他再随机
                        if(greenUserIdList.size() < rewardUserCount){
                            allUserIdList.removeAll(greenUserIdList);
                            Collections.shuffle(allUserIdList);//打乱
                            allUserIdList = allUserIdList.subList(0,rewardUserCount - greenUserIdList.size());
                            allUserIdList.addAll(greenUserIdList);
                        }else{//绿名单人数大于等于，都采取切割列表拿到数据
                            Collections.shuffle(greenUserIdList);//打乱
                            allUserIdList = greenUserIdList.subList(0,rewardUserCount);
                        }
                    }else{
                        Collections.shuffle(allUserIdList);//打乱
                        allUserIdList = allUserIdList.subList(0,rewardUserCount);//得出被抽中的玩家
                    }


                    needPrintLog = true;//已经进入实质发奖环节，异常需要打印日志
                    //6、从数据库查出级别，金豆，战绩等级匹配条件配置
                    List<ActivityRpLevelConfig> levelConfigList = this.activityDao.findRpActivityLevelConfig();
                    Map<String,ActivityRpLevelConfig> levelMap = levelConfigList.stream().collect(Collectors.toMap(ActivityRpLevelConfig::getLevel,Function.identity()));

                    //7.1、查询出抽中资格的玩家的手机号码
                    allUserInfo = this.activityDao.findPhoneByUserIds(allUserIdList);
                    tempAllUserInfoMap = allUserInfo.stream().collect(Collectors.toMap(UserInfo::getPhone,UserInfo::getUserId));
                    for(String encryptionStr : tempAllUserInfoMap.keySet()){
                        String phone = PhoneNumUtil.decoder(encryptionStr).split("-")[1];
                        allUserPhoneList.add(phone);
                        allUserInfoMap.put(phone,tempAllUserInfoMap.get(encryptionStr));
                    }

                    //7.2、批量查询出玩家是否在名单内，如果在，取出级别存进list（元素是一个对象，对象包含userid和对应级别和对应抽中的金豆数（当前步暂时无金豆数值））
                    List<Integer> needQueryMongoUserIdList = new ArrayList<>(allUserIdList.stream().map(Integer::parseInt).collect(Collectors.toList()));
                    List<ActivityRpUserConfigInfo> rpUserConfigInfoList = this.activityDao.findRpActivityInListUser(allUserPhoneList);
                    for(ActivityRpUserConfigInfo d : rpUserConfigInfoList){
                        Integer userId = allUserInfoMap.get(d.getPhone());
                        rpUserInfoList.add(ActivityRpUserInfo.builder().userId(String.valueOf(userId)).level(d.getLevel()).build());
                        needQueryMongoUserIdList.remove(userId);
                    }

                    //8、非名单内，从mongo的user_data_statistics集合，得出每个人的历史总战绩，得到级别，存入list
                    List<UserDataStatistics> userDataStatisticsList = userDataStatisticsDao.selectByUserIds(needQueryMongoUserIdList);
                    for(UserDataStatistics d : userDataStatisticsList){
                        String level = getLevel(d.getTotalPl(),levelConfigList);
                        rpUserInfoList.add(ActivityRpUserInfo.builder().userId(String.valueOf(d.getUserId())).level(level).build());
                        needQueryMongoUserIdList.remove(d.getUserId());//如果有则移除
                    }
                    if(needQueryMongoUserIdList.size() > 0){//查询mongo后数据还大于0，需要置0处理
                        for(Integer userId : needQueryMongoUserIdList){
                            String level = getLevel(0.0,levelConfigList);
                            rpUserInfoList.add(ActivityRpUserInfo.builder().userId(String.valueOf(userId)).level(level).build());
                        }
                    }

                    if(rpUserInfoList.size() != allUserIdList.size()){//有资格的玩家和实际生成的数据有差异，打印日志
                        log.error("processSkyRpTask activity data have error alluserIdSize={},dataSize={}",allUserIdList.size(),rpUserInfoList.size());
                        log.error("processSkyRpTask activity alluserId data={}",Arrays.toString(allUserIdList.toArray()));
                        log.error("processSkyRpTask activity ser data={}",Arrays.toString(rpUserInfoList.toArray()));
                    }

                    if(rpUserInfoList.size() == 0){//实际发放玩家竟然等于0，有问题
                        log.error("processSkyRpTask activity need give player size is 0,have problem");
                        throw new Exception("需要发放的玩家为0,数据有问题");
                    }

                    //9、运算出list中对象的抽中金豆数值
                    List<UserAccountLog> userAccountLogList = new ArrayList<>();
                    Random random = new Random();
                    List<UserAccountInfo> userAccountInfoList = this.activityDao.findUserAccountInfoByUserIds(allUserIdList);
                    Map<Integer,Integer> userAccountMap = userAccountInfoList.stream().collect(Collectors.toMap(UserAccountInfo::getUserId,UserAccountInfo::getNotExtractChip));
                    List<UserAccountGiveInfo> giveList = new ArrayList<>();
                    int kdouCount = 0;//金豆发放总额
                    for(ActivityRpUserInfo rui:rpUserInfoList){
                        ActivityRpLevelConfig activityLevelConfig = levelMap.get(rui.getLevel());
                        Integer randomNum = (activityLevelConfig.getNum() * (random.nextInt(activityLevelConfig.getMax() - activityLevelConfig.getMin()) + activityLevelConfig.getMin())) / 100;
                        rui.setNum(randomNum*1.00);
                        rui.setJoinedRoomIds(userJoinedRoomidMap.get(rui.getUserId()));
                        userAccountLogList.add(UserAccountLog.builder().userId(Integer.parseInt(rui.getUserId())).type(34).changeSource(7).
                                currNotExtractChip(userAccountMap.get(Integer.parseInt(rui.getUserId()))).changeNotExtractChip(randomNum).desction("天降红包雨活动").opId(-1).build());
                        kdouCount += randomNum;
                        giveList.add(UserAccountGiveInfo.builder().userId(Integer.parseInt(rui.getUserId())).chip(randomNum).type(3).build());
                    }

                    //10、记录发放记录、发放金豆
                    //检查营销账户，扣除
                    int change = this.platformAccountCustomizeDao.incChipByCodeAndChipGTENum(100,
                            -kdouCount, -50000000L);
                    if(change <= 0){//没有在营销账户扣款成功
                        //不需要重发，打印日志就可以啦
                        log.error("processSkyRpTask marketAccount not monty={}",Arrays.toString(rpUserInfoList.toArray()));
                    }else{//营销账户有钱才发放
                        long marketChip =  this.platformAccountDao.findPlatformAccountChip(100);
                        change = this.platformAccountDao.batchInsertPlatformAccountLog(100,25,7,marketChip + kdouCount,
                                "天降红包赠送",-1,rpUserInfoList);
                        if(change <= 0){
                            throw new Exception("更新营销仓扣款记录出错");
                        }

                        //记录发放记录
                        change = this.activityDao.batchInsertRpActivityRecord(rpUserInfoList);
                        if(change <= 0){
                            throw new Exception("更新天降红包活动发放记录出错");
                        }
                        //记录金豆发放记录
                        change = this.activityDao.batchInsertAccountLog(userAccountLogList);
                        if(change <= 0){
                            throw new Exception("更新金豆发放记录出错");
                        }
                        //发放金豆
                        change = this.activityDao.batchUpdateUserAccountNotEChip(rpUserInfoList);
                        if(change <= 0){
                            throw new Exception("更新用户金豆出错");
                        }
                        ObjectMapper mapper = new ObjectMapper();

                        //11、发送mq消息（包含需要发送的房间，以及哪个玩家击中多少信息）到消息模块，通知客户端播放天降红包动画
                        //12、发送mq消息，告知抽中的用户获得了多少金豆（记录玩家的未读消息等数据）
                        //因为要除以一百所以多处理一次
                        List<ActivityRpUserInfo> sendActivityRpUser=new ArrayList<>();
                        for (ActivityRpUserInfo activityRpUserInfo : rpUserInfoList) {
                            activityRpUserInfo.setNum(activityRpUserInfo.getNum()/100.00);
                            sendActivityRpUser.add(activityRpUserInfo);
                        }
                        this.messageSender.sendInteriorMessage(InteriorMessage.builder().type(EMessageCode.INTERIOR_SKY_ACTIVITY.getCode()).param1(mapper.writeValueAsString(sendActivityRpUser)).build());
                        log.error("发送IM消息。sendActivityRpUser：{}.", sendActivityRpUser);
                        try {
                            //累计玩家赠送金豆
                            this.userDataService.batchDisposeGiveInfo(giveList);
                        }catch (Exception e){
                        }
                    }

                }
            }

            return;
        }catch (Exception e){
            log.error("processSkyRpTask 出现异常={}",e);
            if(needPrintLog){
                if(rpUserInfoList.size() > 0){
                    log.error("processSkyRpTask activity error rpUser data={}",Arrays.toString(rpUserInfoList.toArray()));
                }else{
                    log.error("processSkyRpTask activity error alluserId data={}",Arrays.toString(allUserIdList.toArray()));
                }
            }
        }
        throw new Exception("processSkyRpTask 有异常情况，回滚事务");
    }

    @Override
    public void checkKdouQua(Integer userId) {
        Date regTime = this.activityDao.checkIsNewRegUser(userId);
        if(null == regTime){
            return;
        }
        if(regTime.getTime() < 1571241600000L){
            return;
        }

        ActivityLimitConfig activityConfig = this.activityDao.findKdActivityConfig();

        //计算用户资格
        ActivityKdouRecord record = this.activityDao.findKdRecordByUserId(userId);

        if(null != record && record.getStatus() > 0){//已经没有资格领取了
            log.info("checkKdouQua is gained={}",userId);
            return;
        }
        if(null != record && record.getStatus() == 0){//待领取，提醒领取
            //检查是否过期
            long currentDay = (System.currentTimeMillis() + TimeZone.getDefault().getRawOffset()) / 3600 /24 /1000;
            long oldDay = (record.getCreateTime().getTime() + TimeZone.getDefault().getRawOffset()) / 3600 /24 /1000;
            if((currentDay - oldDay + 1) > activityConfig.getTimeLimit()){//超过时效
                log.info("checkKdouQua is time out={}",userId);
                this.activityDao.updateUserKdActStatus(3,userId);//将用户资格置为已过期
            }else{//还没超过时效
                log.info("checkKdouQua notify gain={}",userId);
                //设置此次通知时间，用于记录下次登录是否通知弹窗
                this.activityDao.updateUserKdActTime(userId);

                //通知消息模块，通知弹窗
                this.messageSender.sendInteriorMessage(InteriorMessage.builder().type(EMessageCode.INTERIOR_REG_ACTIVITY.getCode()).
                        param1(String.valueOf(userId)).param2(String.valueOf(record.getNum())).param3("1").build());
            }
            return;
        }
        if(activityConfig.getStatus() != 1){//活动没有开启
            log.info("checkKdouQua activity is colse={}",userId);
            return;
        }
        //查询是否在指定的名单里
        String phoneMd5str = this.activityDao.findUserPhoneByUserId(userId);
        String phone = PhoneNumUtil.decoder(phoneMd5str).split("-")[1];

        String level = this.activityDao.findKdAppointByPhone(phone);
        if(null == level){
            log.info("checkKdouQua user not qua={}",userId);
            return;
        }
        Random random = new Random();
        ActivityLevelConfig activityLevelConfig = this.activityDao.findKdLevelConfig(level);
        if(null == activityLevelConfig){
            log.info("checkKdouQua query level config don't have={}",userId);
            return;
        }
        int num = (activityLevelConfig.getNum() * (random.nextInt(activityLevelConfig.getMax() - activityLevelConfig.getMin()) + activityLevelConfig.getMin())) / 100;
        this.activityDao.insertKdRecord(userId,level,activityLevelConfig.getMin(),activityLevelConfig.getMax(),activityLevelConfig.getNum(),num,0);//待领取
        //通知消息模块，通知弹窗
        this.messageSender.sendInteriorMessage(InteriorMessage.builder().type(EMessageCode.INTERIOR_REG_ACTIVITY.getCode()).
                param1(String.valueOf(userId)).param2(String.valueOf(num)).param3("1").build());
    }
	
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processMessage(ActivityInfo activityInfo) {
        if(activityInfo != null) {
            log.debug("have message ={}",activityInfo.toString());

            ActivityLimitConfig activityConfig = this.activityDao.findActivityConfig();
            //计算用户资格
            ActivityKdouRecord record = activityDao.findActivityKdouRecordByUserId(activityInfo.getUserId());
            //计算资格
            if(activityInfo.getType() == 1){
                //检查活动是否开启
                if(activityConfig.getStatus() == 1){
                    if(null == record){//没有记录的时候才执行
                        if(activityConfig.getType() == 2){//指定俱乐部首充活动
                            //如果没有第一次登录记录，可能是系统问题，或者是用户直接调用api
                            FirstLoginInfo firstLoginInfo = this.activityDao.findUserFirstLoginByUserId(activityInfo.getUserId());
                            if(null == firstLoginInfo || firstLoginInfo.getPayStatus() != 1 || null != firstLoginInfo.getPayOrderId()){//没有登录记录或者不是待生成资格状态或者已经不是首充
                                log.debug("RechargeActivityHandler type 2 query user login record has error={}",null == firstLoginInfo ? firstLoginInfo : firstLoginInfo.toString());
                                return;
                            }

                            if(activityInfo.getSimulator() == 1){
                                this.activityDao.insertRecord(activityInfo.getUserId(),"",0,2);//没有资格领取
                                this.activityDao.updateUserPayStatus(activityInfo.getUserId(),5);//没有资格
                                log.debug("RechargeActivityHandler type 2 is simulator = {}",activityInfo.getUserId());
                                return;
                            }

                            // 查询是否在指定俱乐部内
                            Integer clubLimit = this.activityDao.findRechargeClubLimit(activityInfo.getUserId());
                            if(null == clubLimit){
                                this.activityDao.insertRecord(activityInfo.getUserId(),"",0,2);//没有资格领取
                                this.activityDao.updateUserPayStatus(activityInfo.getUserId(),5);//没有资格
                                log.debug("RechargeActivityHandler type 2 not in club = {}",activityInfo.getUserId());
                                return;
                            }

                            //查询该俱乐部是否上限，已达上限则不发放资格
                            Integer clubCount = this.activityDao.findRechargeClubCount(activityInfo.getUserId());
                            if(clubCount >= clubLimit){
                                this.activityDao.insertRecord(activityInfo.getUserId(),"",0,2);//没有资格领取
                                this.activityDao.updateUserPayStatus(activityInfo.getUserId(),5);//没有资格
                                log.debug("RechargeActivityHandler type 2 club is limit = {}",activityInfo.getUserId());
                                return;
                            }

                            Integer sameIpNum = this.activityDao.findSameIp(activityInfo.getIp(),activityInfo.getUserId());
                            if(null == sameIpNum || sameIpNum >= 5){//有相同ip的参加过活动
                                log.debug("RechargeActivityHandler type 2 ip has same so no qua=>" + activityInfo.getUserId() + "," + activityInfo.getIp() + ",same num=>" + sameIpNum);
                                this.activityDao.insertRecord(activityInfo.getUserId(),"",0,2);//想要刷豆的傻屌
                                this.activityDao.updateUserPayStatus(activityInfo.getUserId(),5);//设置为异常原因无状态
                                return;
                            }
                            //查询相同imei
                            boolean hasSameImei = querySameImei(activityInfo.getUserId(),activityInfo.getImei());
                            if(hasSameImei){
                                log.debug("RechargeActivityHandler type 2 hsa same imei no qua={},={}",activityInfo.getUserId(),activityInfo.getImei());
                                this.activityDao.insertRecord(activityInfo.getUserId(),"",0,2);//没有资格领取
                                this.activityDao.updateUserPayStatus(activityInfo.getUserId(),5);//设置为异常原因无状态
                                return;
                            }

                            if(clubCount + 10000 >= clubLimit){
                                // 停止该俱乐部活动资格
                                this.activityDao.updateRechargeClubStatus(activityInfo.getUserId());
                            }

                            this.activityDao.insertRatioRecord(activityInfo.getUserId(),"777",100,100,10000,10000,5);//已有资格，待首充
                            this.activityDao.updateUserPayStatus(activityInfo.getUserId(),2);//设置为有资格状态

                        }else if(activityConfig.getType() == 1){//普通首充活动
                            String phoneMd5str = this.activityDao.findUserPhoneByUserId(activityInfo.getUserId());
                            String phone = PhoneNumUtil.decoder(phoneMd5str).split("-")[1];
                            Integer appointNum = this.activityDao.findUserNumByPhone(phone);

                            if(null != appointNum){//指定用户发放特定金豆
                                this.activityDao.insertRecord(activityInfo.getUserId(),"888",appointNum,0);//待领取
                                this.activityDao.updateUserPayKdou(3,activityInfo.getUserId(),appointNum,"system appoint give");//设置为待领取状态，并且赋值可领取金豆数
                            }else if(activityInfo.getImei().isEmpty() || activityInfo.getIp().isEmpty()){
                                log.debug("RechargeActivityHandler param is empty so no qua={},={},={}",activityInfo.getUserId(),activityInfo.getImei(), activityInfo.getIp());
                                this.activityDao.insertRecord(activityInfo.getUserId(),"",0,2);//没有资格领取
                                this.activityDao.updateUserPayStatus(activityInfo.getUserId(),5);//设置为异常原因无状态
                            }else{
                                //如果没有第一次登录记录，可能是系统问题，或者是用户直接调用api
                                FirstLoginInfo firstLoginInfo = this.activityDao.findUserFirstLoginByUserId(activityInfo.getUserId());
                                if(null == firstLoginInfo || firstLoginInfo.getPayStatus() != 1 || null != firstLoginInfo.getPayOrderId()){//没有登录记录或者不是待生成资格状态或者已经不是首充
                                    log.debug("RechargeActivityHandler query user login record has error={}",null == firstLoginInfo ? firstLoginInfo : firstLoginInfo.toString());
                                }else{
                                    Integer sameIpNum = this.activityDao.findSameIp(activityInfo.getIp(),activityInfo.getUserId());
                                    if(null == sameIpNum || sameIpNum >= 20){//有相同ip的参加过活动
                                        log.debug("RechargeActivityHandler ip has same so no qua=>" + activityInfo.getUserId() + "," + activityInfo.getIp() + ",same num=>" + sameIpNum);
                                        this.activityDao.insertRecord(activityInfo.getUserId(),"",0,2);//想要刷豆的傻屌
                                        this.activityDao.updateUserPayStatus(activityInfo.getUserId(),5);//设置为异常原因无状态
                                    }else{
                                        //查询相同imei
                                        boolean hasSameImei = querySameImei(activityInfo.getUserId(),activityInfo.getImei());
                                        if(hasSameImei){
                                            log.debug("RechargeActivityHandler hsa same imei no qua={},={}",activityInfo.getUserId(),activityInfo.getImei());
                                            this.activityDao.insertRecord(activityInfo.getUserId(),"",0,2);//没有资格领取
                                            this.activityDao.updateUserPayStatus(activityInfo.getUserId(),5);//设置为异常原因无状态
                                        }else{
                                            int num = 0;//可获得金豆
                                            String level = "";
                                            boolean isOldPlayer = false;
                                            if(this.activityDao.findBlackByPhone(phone) > 0){//黑名单用户
                                                level = "0a";
                                            }else if(activityInfo.getSimulator() == 1 || this.activityDao.findGrayByPhone(phone) > 0){//模拟器用户或者灰名单用户
                                                level = "0b";
                                            }else if(activityInfo.getGps() == 0){//获取不到定位
                                                level = "U";
                                            }else{
                                                //查询关联账号
                                                int relevanceNum = 0;
                                                relevanceNum = getRelevenNum(activityInfo.getIp(),activityInfo.getImei(),activityInfo.getUserId());
                                                if(relevanceNum  > 30){//灰名单
                                                    log.debug("relevanceNum =>" + activityInfo.getUserId() + "," + relevanceNum + "," + activityInfo.getIp() + "," + activityInfo.getImei());
                                                    level = "0b";
                                                }else{
                                                    String levelConfig = this.activityDao.findOldPlayerByPhone(phone);
                                                    if(null != levelConfig){//老用户
                                                        isOldPlayer = true;
                                                        level = levelConfig;
                                                    }else{//新用户
                                                        String modelConfig = this.activityDao.findModelLevelByModel(activityInfo.getPhoneModel());
                                                        String relevanceConfig = this.activityDao.findRevelanceLevelByRelNum(relevanceNum);
                                                        if(null == modelConfig){
                                                            modelConfig = "4";
                                                        }
                                                        level = modelConfig + relevanceConfig;
                                                    }
                                                }
                                            }
                                            Random random = new Random();
                                            ActivityLevelConfig activityLevelConfig = null;
                                            if(isOldPlayer){
                                                activityLevelConfig = this.activityDao.findUserLevelConfig(level);
                                                num = (activityLevelConfig.getNum() * (random.nextInt(activityLevelConfig.getMax() - activityLevelConfig.getMin()) + activityLevelConfig.getMin())) / 100;
                                            }else{
                                                activityLevelConfig = this.activityDao.findLevelConfig(level);
//                                                num = (activityLevelConfig.getNum() * (random.nextInt(activityLevelConfig.getMax() - activityLevelConfig.getMin()) + activityLevelConfig.getMin())) / 100;
                                            }
                                            //如果是名单内的老用户，这个时候先计算金豆，首充后直接改状态即可，不是老用户则需要比例与之后的充值额做匹配算出金豆
                                            this.activityDao.insertRatioRecord(activityInfo.getUserId(),level,activityLevelConfig.getMin(),activityLevelConfig.getMax(),isOldPlayer ? activityLevelConfig.getNum() : 0,num,5);//已有资格，待首充
                                            this.activityDao.updateUserPayStatus(activityInfo.getUserId(),2);//设置为有资格状态
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }else if(activityInfo.getType() == 2){//充值回调
                //这种类型gps字段数据是充值金额，ip字段是订单号
                // 判断用户是不是首充，并且是不是有资格状态
                FirstLoginInfo firstLoginInfo = this.activityDao.findUserFirstLoginByUserId(activityInfo.getUserId());
                if(null == firstLoginInfo){//没有首次登录记录
                    log.error("RechargeActivityHandler query first record is null,but it recharge succ={},={},={}",activityInfo.getUserId(),activityInfo.getIp(),activityInfo.getGps());
                }else if(null != firstLoginInfo.getPayOrderId()){//不为空，说明已经首充过
                    log.error("RechargeActivityHandler query first recharge record not null={},={},={}",activityInfo.getUserId(),activityInfo.getIp(),activityInfo.getGps());
                }else{
                    if(firstLoginInfo.getPayStatus() != 2){//没有资格
                        log.error("RechargeActivityHandler query user staut,than not qua={},={},={},={}",activityInfo.getUserId(),firstLoginInfo.getPayStatus(),activityInfo.getIp(),activityInfo.getGps());
                    }else{//有资格
                        if(null == record){//没有资格记录？？
                            log.error("RechargeActivityHandler query record is null,but it recharge succ={},={},={}",activityInfo.getUserId(),activityInfo.getIp(),activityInfo.getGps());
                        }else if(record.getStatus() != 5){
                            log.error("RechargeActivityHandler record status is error ,why need callback={}={}",activityInfo.getUserId(),record.getStatus());
                        }else{
                            if(activityConfig.getType() == 1 || activityConfig.getType() == 2){
                                if(record.getLevel().equals("777")){//这种只会是活动2的  避免活动切换引起的问题，兼容代码
                                    // 只校验俱乐部是否有在指定俱乐部内
                                    Integer clubId = this.activityDao.checkClubHasQua(activityInfo.getUserId());
                                    if(null == clubId){//不在指定俱乐部内
                                        log.error("RechargeActivityHandler user not in club={}",activityInfo.toString());
                                    }else{
                                        this.activityDao.updateUserActivityStatus(0,activityInfo.getUserId());//设置为待领取
                                        this.activityDao.updateUserPayKdou(3,activityInfo.getUserId(),record.getNum(),activityInfo.getIp());//设置为待领取状态，并且赋值可领取金豆数
                                    }
                                }else{
                                    //已有资格待首充状态,需要修改为待领取状态
                                    if(record.getNum() == 0 && record.getMaxNum() == 0){//新用户
                                        //需要计算充值额度
                                        Integer maxNum = this.activityDao.findRechargeLevelNum(record.getLevel(),activityInfo.getGps());
                                        if(null == maxNum){
                                            maxNum = 0;
                                        }
                                        Random random = new Random();
                                        int num = (maxNum * (random.nextInt(record.getMax() - record.getMin()) + record.getMin())) / 100;
                                        //将计算出来的额度和适用的最高值更新到用户的record表
                                        record.setNum(num);
                                        this.activityDao.updateUserActivityKdouNum(num,maxNum,activityInfo.getUserId(),0);//设置为待领取

                                    }else{
                                        this.activityDao.updateUserActivityStatus(0,activityInfo.getUserId());//设置为待领取
                                    }
                                    //老用户只需要更新这个表的值
                                    this.activityDao.updateUserPayKdou(3,activityInfo.getUserId(),record.getNum(),activityInfo.getIp());//设置为待领取状态，并且赋值可领取金豆数
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public void cleanUserActivityStatus() {
        //清理过期玩家状态
        ActivityLimitConfig activityConfig = this.activityDao.findActivityConfig();
        if(null != activityConfig){
            Calendar cal = Calendar.getInstance();
            cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
            cal.add(Calendar.DAY_OF_YEAR, -activityConfig.getTimeLimit());
            Date invailDate = new Date(cal.getTimeInMillis() / 1000 * 1000);
            int updateCount = this.activityDao.updateExpiredUserStatus(invailDate);
            log.info("cleanUserActivityStatus updateCount={},type={}",updateCount,activityConfig.getType());
        }
    }

    private String getLevel(Double totalPl,List<ActivityRpLevelConfig> levelConfigList){
        for(ActivityRpLevelConfig c : levelConfigList){
            if(totalPl >= c.getMinProfit() && totalPl <= c.getMaxProfit()){
                return c.getLevel();
            }
        }
        return "1";
    }

    //查询关联账号
    private final static String ADD_REQUEST_INFO = "add_request_info";
    private int getRelevenNum(String ip,String imei,Integer userId){
        ReactiveMongoTemplate rTemplate = getReactiveMongoTemplate();
        Query query = new Query();
        query.addCriteria(Criteria.where("user_id").ne(userId)).addCriteria(Criteria.where("ip").in(ip));
        List<AddRequestInfo> ipList = rTemplate.find(query, AddRequestInfo.class, ADD_REQUEST_INFO).collectList().block();
        Set<Integer> relevenSet = new HashSet<>();
        if(null != ipList && ipList.size() > 0){
            relevenSet.addAll(ipList.stream().map(AddRequestInfo::getUserId).collect(Collectors.toSet()));
        }
        query = new Query();
        query.addCriteria(Criteria.where("user_id").ne(userId)).addCriteria(Criteria.where("imei").in(imei));
        List<AddRequestInfo> imeiList = rTemplate.find(query, AddRequestInfo.class, ADD_REQUEST_INFO).collectList().block();
        if(null != imeiList && imeiList.size() > 0){
            relevenSet.addAll(imeiList.stream().map(AddRequestInfo::getUserId).collect(Collectors.toSet()));
        }
        return relevenSet.size();
    }

    /**
     * 查询相同机器码
     * @param userId
     * @param imei
     * @return
     */
    private boolean querySameImei(Integer userId,String imei){
        Integer count = this.activityDao.findSameImei(userId, imei);
        return null == count ? true : count > 0;
    }
}
