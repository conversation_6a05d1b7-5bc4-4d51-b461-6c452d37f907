package com.dzpk.crazypoker.activity.service.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 用户账号变化记录表  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class UserAccountLog {

    /**
     * userId
     */
    @ApiModelProperty("userId")
    private Integer userId;

    /**
     *     1-查看未发的牌消费,2-用户操作延时,3-保险操作延时,4-击中jp,5-带入筹码,6-比赛结束赢取,7-提前离桌返还,8-mtt重购费退还,9-mtt报名费费退还,10-mtt比赛结束赢取,11-mtt猎人赛赢取,12-mtt重购费,13-兑换王者币,14-使用表情花费,15-获得基金,16-sng报名费,17-sng报名费退还,18-修改头像费用,19-修改昵称费用,20-设置头像赠送费用,21-俱乐部基金充值,22-破隐消耗,23-分享赚金豆返豆,24-开通或升级vip,25-提豆消耗,26-提豆返还,27-转出金豆, 28-转入金豆,29-充值金豆 30-摇奖送金豆 31-商城充值 32-俱乐部个人充值,33-手工充豆,34-天降红包
     */
    @ApiModelProperty("    1-查看未发的牌消费,2-用户操作延时,3-保险操作延时,4-击中jp,5-带入筹码,6-比赛结束赢取,7-提前离桌返还,8-mtt重购费退还,9-mtt报名费费退还,10-mtt比赛结束赢取,11-mtt猎人赛赢取,12-mtt重购费,13-兑换王者币,14-使用表情花费,15-获得基金,16-sng报名费,17-sng报名费退还,18-修改头像费用,19-修改昵称费用,20-设置头像赠送费用,21-俱乐部基金充值,22-破隐消耗,23-分享赚金豆返豆,24-开通或升级vip,25-提豆消耗,26-提豆返还,27-转出金豆, 28-转入金豆,29-充值金豆 30-摇奖送金豆 31-商城充值 32-俱乐部个人充值,33-手工充豆,34-天降红包")
    private Integer type;

    /**
     * 变化来源:1-api,2-room(aof),3-omaha,4-bp,5-sng,6-mtt,7-yunying,8-aof
     */
    @ApiModelProperty("变化来源:1-api,2-room(aof),3-omaha,4-bp,5-sng,6-mtt,7-yunying,8-aof")
    private Integer changeSource;
    /**
     * 当前不可提余额
     */
    @ApiModelProperty("当前不可提余额")
    private Integer currNotExtractChip;

    /**
     * 当前不可提余额变化数量,当前要增加或减少的数值,增加为正,减少为负
     */
    @ApiModelProperty("当前不可提余额变化数量,当前要增加或减少的数值,增加为正,减少为负")
    private Integer changeNotExtractChip;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String desction;

    /**
     * 操作人id (-1为系统操作)
     */
    @ApiModelProperty("操作人id (-1为系统操作)")
    private Integer opId;
}