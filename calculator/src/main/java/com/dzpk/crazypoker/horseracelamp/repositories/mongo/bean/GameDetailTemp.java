package com.dzpk.crazypoker.horseracelamp.repositories.mongo.bean;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * Description:
 * <p>
 * Created by L on 2019/7/22 10:50
 */
@Document(collection = "game_detail_temp")
@Data
public class GameDetailTemp {

    @Id
    private String id;

    @Field("user_id_array")
    private String userIdArray;

    @Field("hand_array")
    private String handArray;

    @Field("room_id")
    private String roomId;

    @Field("blind")
    private String blind;
}
