package com.dzpk.crazypoker.horseracelamp.repositories.mysql;

import com.dzpk.crazypoker.horseracelamp.service.bean.*;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * Description:
 * <p>
 * Created by L on 2019/7/19 15:14
 */
@Repository
public interface IHorseRaceLampDao {

    @Select("select count(0) from horse_race_lamp_activity_config where status = 1")
    int countHorseRaceLampActivityOpen();

    /**
     * 查询奖池仓奖金
     * @return
     */
    @Select("select chip from platform_account where code = 600")
    Integer queryPrizePool();

    @Insert("insert into horse_race_lamp_activity(prize_pool, start_time, status, end_time, create_time) " +
            "values (#{prizePool},#{startTime},#{status},#{endTime},#{createTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    void insertHorseRaceLampActivity(HorseRaceLampActivity activity);

    @Select("select t1.room_id from user_join_room t1, group_room t2 where t1.room_id = t2.room_id and t2.`status` = 4 " +
            "and t1.is_close = 0 and t1.on_seat = 1 and t1.has_bringin=1 GROUP BY room_id HAVING count(t1.user_id) >= 4")
    List<Integer> queryPrizeRoomids();
    //TODO 测试房间跑马灯
    @Select("select t1.room_id from user_join_room t1, group_room t2 where t1.room_id = t2.room_id and t2.`status` = 4 " +
            "and t1.is_close = 0 and t1.on_seat = 1 and t1.has_bringin=1 GROUP BY room_id HAVING count(t1.user_id) >= 2")
    List<Integer> queryTestPrizeRoomids();
    @Select("select user_id from user_join_room where room_id = #{roomid} and is_close = 0 and on_seat = 1 and has_bringin=1")
    List<Integer> queryActivityUserIdsByRoomid(Integer roomid);

    @Select({"<script>",
            "select user_id from user_online_statistics where login_device_model = 'simulator' and user_id in ",
            "(" ,
            "<foreach collection='userIds' item='item' separator=','>" ,
            "#{item}" ,
            "</foreach>)",
            "</script>" })
    List<Integer> querySimulatorUserIds(@Param("userIds") List<Integer> userIds);

    @Select({"<script>",
            "select user_id from horse_race_lamp_activity_user_prize where room_id = #{roomid} and user_id in ",
            "(" ,
            "<foreach collection='userIds' item='item' separator=','>" ,
            "#{item}" ,
            "</foreach>)",
            "</script>" })
    List<Integer> queryWinnersInRoom(Integer roomid, @Param("userIds") List<Integer> userIds);

    @Select({"<script>",
            "select user_id from horse_race_lamp_activity_user_prize where date = #{date} and user_id in",
            "(" ,
            "<foreach collection='userIds' item='item' separator=','>" ,
            "#{item}" ,
            "</foreach>)" +
            " group by user_id having count(0) >= 3 ",
            "</script>" })
    List<Integer> queryTouchLimitUserIds(@Param("userIds") List<Integer> userIds, Date date);

    @Select({"<script>",
            "select user_id from user_level where level_code = 2 and user_id in ",
            "(" ,
            "<foreach collection='userIds' item='item' separator=','>" ,
            "#{item}" ,
            "</foreach>)" +
            "</script>" })
    List<Integer> queryGreenUserIds(@Param("userIds") List<Integer> userIds);

    @Select("select total_recharge as totalRecharge,total_withdraw as totalWithdraw, login_device_model as loginDeviceModel," +
            " user_id as userId" +
            " from user_online_statistics where user_id = #{userId}")
    UserOnlineStatistics queryUserOnlineStatistics(Integer userId);

    @Update("update horse_race_lamp_activity set end_time = #{endTime}, update_time = #{updateTime},status = #{status}" +
            " where id = #{id}")
    void updateHorseRaceLampActivity(HorseRaceLampActivity activity);

    @Select("select prize_level as level, proportion from horse_race_lamp_activity_prize_level_config")
    List<PrizeLevelConfig> queryPrizeLevelConfig();

    @Insert("INSERT INTO `platform_account_log`(`platform_code`, `type`, `change_source`, `current_chip`, `change_chip`, " +
            "`desction`, `external_id`, `op_id`, `created_time`) " +
            "VALUES (600, 30, 2, (select chip from platform_account where code = 600), #{kdou} * -1, #{level}, NULL, NULL, now())")
    void insertPlatformAccuntLog(ActivityUserInfo activityUserInfo);

    @Update("update platform_account set chip = chip - #{rewards} where code = 600")
    void reducePlatformAccount(int rewards);

    @Insert("insert into user_account_log(user_id, type, change_not_extract_chip, change_source, curr_not_extract_chip) " +
            "values(#{userId}, 37, #{kdou}, 2, (select not_extract_chip from user_account where user_id = #{userId}))")
    void insertUserAccountLog(ActivityUserInfo activityUserInfo);

    @Update("update user_account set not_extract_chip = not_extract_chip + #{kdou} where user_id = #{userId}")
    void addUserAccount(ActivityUserInfo activityUserInfo);

    @Insert("INSERT INTO `horse_race_lamp_activity_user_prize`(`activity_id`, `user_id`, `room_id`, `prize_level`, `kdou`, `date`, `create_time`) " +
            "VALUES (#{id}, #{userId}, #{roomId}, #{level}, #{kdou},#{date}, now())")
    void insertRewardRecord(ActivityUserInfo activityUserInfo);
    @Insert({"<script>",
            "INSERT INTO `horse_race_lamp_activity_statistic`(`activity_id`, `prize_level`, `user_count`, `kdou`, `total_kdou`, `date`, `create_time`) " +
                "VALUES " +
                "<foreach collection='activityStatisticsList' item='item' separator=','>" ,
                    "(",
                    "#{item.activityId}, #{item.level}, #{item.userCount}, #{item.kdou}, #{item.totalKdou}, #{item.date}, #{item.createTime}",
                    ")",
                "</foreach>",
            "</script>"})
    void batchInsertActivityStatistics(@Param("activityStatisticsList") List<ActivityStatistics> activityStatisticsList);

    @Insert("INSERT INTO `horse_race_lamp_activity_user_grand_prize`(`activity_id`, `user_id`, `room_id`, `prize_level`, `kdou`, `date`, `create_time`) " +
            "VALUES (#{id}, #{userId}, #{roomId}, #{level}, #{kdou},#{date}, now())")
    void insertGrandRewardRecord(ActivityUserInfo activityUserInfo);

    @Select("select model_level as modelLevel, prize_level as prizeLevel, proportion from horse_race_lamp_activity_prize_probability_config")
    List<ActivityPrizeProbabilityConfig> queryActivityPrizeProbabilityConfigs();

    @Select("select prize_level from horse_race_lamp_activity_user_grand_prize where user_id = #{userId} and prize_level in (1,2)")
    List<Integer> queryGrandPrizes(Integer userId);

    @Insert("insert into message_money_record(msg_id, sender_id, reciver_id, content, type, create_time) values (#{msgId}, #{system}, #{userId}, #{content}, #{type}, #{createTime})")
    void insertMoneyRecord(String msgId, String system, Integer userId, double content, int type, Date createTime);

    @Update("UPDATE message_unread SET money_num = ifnull(money_num, 0) + #{num}, money_msg = #{msg} WHERE user_id=#{userId}")
    void updateMoneyUnreadMsg(int num, String msg, Integer userId);

    @Select("select remark from activity_kdou_model_config where model = #{loginDeviceModel}")
    String queryDeviceModel(String loginDeviceModel);
}
