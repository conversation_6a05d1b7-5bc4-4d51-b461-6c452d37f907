package com.dzpk.crazypoker.autoroom.repositories.mysql;

import com.dzpk.crazypoker.autoroom.service.bean.ClubInfo;
import com.dzpk.crazypoker.autoroom.service.bean.RoomCreationPlanConfig;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * Description:
 * <p>
 */
@Repository
public interface IRoomCreationPlanDao {

    @Update("update room_creation_plan_config set empty_seat_sum = #{emptySeats} where id = #{roomCreationPlanId} ")
    void updateRoomPlanEmptySeats(Integer roomCreationPlanId, Integer emptySeats);

    @Select("select t1.empty_seat_sum from room_creation_plan_config t1, plan_room_relations t2 " +
            "where t1.id = t2.plan_id and t2.room_id = #{roomId}")
    Integer queryRoomCreationPlanEmptySeats(Integer roomId);

    @Select("select t2.room_id from plan_room_relations t1, plan_room_relations t2 where t1.plan_id = t2.plan_id and t1.room_id = #{roomId}")
    List<Integer> queryPlanRoomIds(Integer roomId);

    @Select({"select sum(empty_seat) from room_search where status != 0 and ",
             "exists (select 1 from plan_room_relations where room_id = room_search.room_id and plan_id = #{planId})"})
    Integer calculateRoomCreationPlanEmptySeats(@Param("planId") Integer planId);

    @Select("select t1.plan_id from plan_room_relations t1, room_creation_plan_config t2 " +
            "where t1.plan_id = t2.id and t2.status !=2 and t1.room_id = #{roomId}")
    Integer queryPlanIdByRoomId(Integer roomId);

    @Select("select room_id from plan_room_relations where plan_id = #{planId}")
    List<Integer> queryPlanRoomIdsByPlanId(Integer planId);

    @Select("select id, room_path as roomPath, name, player_count as playerCount, auto_player_count as autoPlayerCount, sb_chip as sbChip, " +
            "in_chip as inChip, max_play_time as maxPlayTime, op_time as opTime, min_rate as minRate, max_rate as maxRate, qianzhu," +
            "min_play_time as minPlayTime, straddle, ip, leave_table as leaveTable, vp, muck_switch as muckSwitch, insurance, limit_gps as limitGps," +
            "room_mode as roomMode, empty_seat_config as emptySeatConfig, create_by as createBy " +
            "from room_creation_plan_config where empty_seat_sum <= empty_seat_config and end_time > #{time} and start_time < #{time}")
    List<RoomCreationPlanConfig> queryCreateRoomPlan(@Param("time") Date time);

    @Select("select count(0) from user_basic_info where user_id = #{opUserId} and forbid = 1")
    Integer queryNotFreezeUser(int opUserId);

    @Select("SELECT cr.club_status, cr.id FROM club_members as cm," +
            "club_record as cr WHERE cm.user_id = #{opUserId} and cm.club_id = cr.id")
    ClubInfo queryUserClub(int opUserId);

    @Select("SELECT tm.status FROM tribe_members tm,tribe_record tr" +
            " WHERE tm.club_id = #{clubId} AND tr.id = tm.tribe_id")
    Integer findTribeStatusByClubId(Integer clubId);

    @Select("SELECT random_num " +
            "  FROM (SELECT FLOOR(10000000 + RAND() * 89999999) AS random_num " +
            "          FROM room_id_generator ) AS ss " +
            " WHERE random_num NOT IN (SELECT IFNULL(room_id, 0) FROM room_id_generator) " +
            " LIMIT 1")
    int genRandomId();

    @Insert("INSERT INTO `room_id_generator` (`room_id`, `updated_time`) " +
            " VALUES (#{roomId}, #{updatedTime})")
    int addRoomId(@Param("roomId") int roomId,
                  @Param("updatedTime") Date updatedTime);

    @Select("select id from room_creation_plan_config  where status != 2")
    List<Integer> queryPlanIds();
}
