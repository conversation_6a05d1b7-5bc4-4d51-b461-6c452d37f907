package com.dzpk.crazypoker.audit.service.bean;

import java.io.Serializable;
import java.util.Date;

public class UserDetailPo implements Serializable {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.USER_ID
     *
     * @mbggenerated
     */
    private Integer userId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.nike_name
     *
     * @mbggenerated
     */
    private String nikeName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.HEAD
     *
     * @mbggenerated
     */
    private String head;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.SEX
     *
     * @mbggenerated
     */
    private Integer sex;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.CHIP
     *
     * @mbggenerated
     */
    private Integer chip;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.WIN
     *
     * @mbggenerated
     */
    private Integer win;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.LOSE
     *
     * @mbggenerated
     */
    private Integer lose;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.NUT_HAND
     *
     * @mbggenerated
     */
    private String nutHand;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.HIGHEST_HAVE
     *
     * @mbggenerated
     */
    private Integer highestHave;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.NUT_HAND_TYPE
     *
     * @mbggenerated
     */
    private Integer nutHandType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.MAX_WIN
     *
     * @mbggenerated
     */
    private Integer maxWin;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.FREE_GET
     *
     * @mbggenerated
     */
    private Integer freeGet;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.GAME_INTEGRAL
     *
     * @mbggenerated
     */
    private Integer gameIntegral;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.MATCH_INTEGRAL
     *
     * @mbggenerated
     */
    private Integer matchIntegral;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.WEEKS_INTEGRAL
     *
     * @mbggenerated
     */
    private Integer weeksIntegral;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.MONTH_INTEGRAL
     *
     * @mbggenerated
     */
    private Integer monthIntegral;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.ROOM_PERSION_TYPE
     *
     * @mbggenerated
     */
    private Integer roomPersionType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.ROOM_ID
     *
     * @mbggenerated
     */
    private Integer roomId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.IDOU
     *
     * @mbggenerated
     */
    private Integer idou;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.ADD_UP_COUNT
     *
     * @mbggenerated
     */
    private Integer addUpCount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.ADD_UP_TIME
     *
     * @mbggenerated
     */
    private Integer addUpTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.WINNING_COUNT
     *
     * @mbggenerated
     */
    private Integer winningCount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.POOL_CNT
     *
     * @mbggenerated
     */
    private Integer poolCnt;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.POOL_WIN_CNT
     *
     * @mbggenerated
     */
    private Integer poolWinCnt;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.HAND_CNT
     *
     * @mbggenerated
     */
    private Integer handCnt;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.GAME_CNT
     *
     * @mbggenerated
     */
    private Integer gameCnt;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.USER_TYPE
     *
     * @mbggenerated
     */
    private Integer userType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.UP_MSG_ID
     *
     * @mbggenerated
     */
    private Integer upMsgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.DAY_INDEX
     *
     * @mbggenerated
     */
    private Integer dayIndex;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.DAY_LOGIN_DATE
     *
     * @mbggenerated
     */
    private Date dayLoginDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.birthday
     *
     * @mbggenerated
     */
    private Date birthday;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.PERSON_SIGN
     *
     * @mbggenerated
     */
    private String personSign;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.MODIFY_NAME_TIMES
     *
     * @mbggenerated
     */
    private Integer modifyNameTimes;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.modify_head_time
     *
     * @mbggenerated
     */
    private Integer modifyHeadTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.PHONE
     *
     * @mbggenerated
     */
    private String phone;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.country
     *
     * @mbggenerated
     */
    private String country;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.city
     *
     * @mbggenerated
     */
    private String city;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.province
     *
     * @mbggenerated
     */
    private String province;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.spectrum_cnt
     *
     * @mbggenerated
     */
    private Integer spectrumCnt;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.coin
     *
     * @mbggenerated
     */
    private Integer coin;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.password
     *
     * @mbggenerated
     */
    private String password;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.send_code_time
     *
     * @mbggenerated
     */
    private String sendCodeTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.random_num
     *
     * @mbggenerated
     */
    private String randomNum;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.pay_password
     *
     * @mbggenerated
     */
    private String payPassword;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.pay_pwd_error_time
     *
     * @mbggenerated
     */
    private Integer payPwdErrorTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.pay_pwd_status
     *
     * @mbggenerated
     */
    private Integer payPwdStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.pay_pwd_lock_time
     *
     * @mbggenerated
     */
    private Long payPwdLockTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.pic_frame
     *
     * @mbggenerated
     */
    private String picFrame;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.pic_effective_time
     *
     * @mbggenerated
     */
    private Long picEffectiveTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.honor_num
     *
     * @mbggenerated
     */
    private Long honorNum;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_details_info.ctime
     *
     * @mbggenerated
     */
    private Date ctime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table user_details_info
     *
     * @mbggenerated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.USER_ID
     *
     * @return the value of user_details_info.USER_ID
     *
     * @mbggenerated
     */
    public Integer getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.USER_ID
     *
     * @param userId the value for user_details_info.USER_ID
     *
     * @mbggenerated
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.nike_name
     *
     * @return the value of user_details_info.nike_name
     *
     * @mbggenerated
     */
    public String getNikeName() {
        return nikeName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.nike_name
     *
     * @param nikeName the value for user_details_info.nike_name
     *
     * @mbggenerated
     */
    public void setNikeName(String nikeName) {
        this.nikeName = nikeName == null ? null : nikeName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.HEAD
     *
     * @return the value of user_details_info.HEAD
     *
     * @mbggenerated
     */
    public String getHead() {
        return head;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.HEAD
     *
     * @param head the value for user_details_info.HEAD
     *
     * @mbggenerated
     */
    public void setHead(String head) {
        this.head = head == null ? null : head.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.SEX
     *
     * @return the value of user_details_info.SEX
     *
     * @mbggenerated
     */
    public Integer getSex() {
        return sex;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.SEX
     *
     * @param sex the value for user_details_info.SEX
     *
     * @mbggenerated
     */
    public void setSex(Integer sex) {
        this.sex = sex;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.CHIP
     *
     * @return the value of user_details_info.CHIP
     *
     * @mbggenerated
     */
    public Integer getChip() {
        return chip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.CHIP
     *
     * @param chip the value for user_details_info.CHIP
     *
     * @mbggenerated
     */
    public void setChip(Integer chip) {
        this.chip = chip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.WIN
     *
     * @return the value of user_details_info.WIN
     *
     * @mbggenerated
     */
    public Integer getWin() {
        return win;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.WIN
     *
     * @param win the value for user_details_info.WIN
     *
     * @mbggenerated
     */
    public void setWin(Integer win) {
        this.win = win;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.LOSE
     *
     * @return the value of user_details_info.LOSE
     *
     * @mbggenerated
     */
    public Integer getLose() {
        return lose;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.LOSE
     *
     * @param lose the value for user_details_info.LOSE
     *
     * @mbggenerated
     */
    public void setLose(Integer lose) {
        this.lose = lose;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.NUT_HAND
     *
     * @return the value of user_details_info.NUT_HAND
     *
     * @mbggenerated
     */
    public String getNutHand() {
        return nutHand;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.NUT_HAND
     *
     * @param nutHand the value for user_details_info.NUT_HAND
     *
     * @mbggenerated
     */
    public void setNutHand(String nutHand) {
        this.nutHand = nutHand == null ? null : nutHand.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.HIGHEST_HAVE
     *
     * @return the value of user_details_info.HIGHEST_HAVE
     *
     * @mbggenerated
     */
    public Integer getHighestHave() {
        return highestHave;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.HIGHEST_HAVE
     *
     * @param highestHave the value for user_details_info.HIGHEST_HAVE
     *
     * @mbggenerated
     */
    public void setHighestHave(Integer highestHave) {
        this.highestHave = highestHave;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.NUT_HAND_TYPE
     *
     * @return the value of user_details_info.NUT_HAND_TYPE
     *
     * @mbggenerated
     */
    public Integer getNutHandType() {
        return nutHandType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.NUT_HAND_TYPE
     *
     * @param nutHandType the value for user_details_info.NUT_HAND_TYPE
     *
     * @mbggenerated
     */
    public void setNutHandType(Integer nutHandType) {
        this.nutHandType = nutHandType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.MAX_WIN
     *
     * @return the value of user_details_info.MAX_WIN
     *
     * @mbggenerated
     */
    public Integer getMaxWin() {
        return maxWin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.MAX_WIN
     *
     * @param maxWin the value for user_details_info.MAX_WIN
     *
     * @mbggenerated
     */
    public void setMaxWin(Integer maxWin) {
        this.maxWin = maxWin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.FREE_GET
     *
     * @return the value of user_details_info.FREE_GET
     *
     * @mbggenerated
     */
    public Integer getFreeGet() {
        return freeGet;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.FREE_GET
     *
     * @param freeGet the value for user_details_info.FREE_GET
     *
     * @mbggenerated
     */
    public void setFreeGet(Integer freeGet) {
        this.freeGet = freeGet;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.GAME_INTEGRAL
     *
     * @return the value of user_details_info.GAME_INTEGRAL
     *
     * @mbggenerated
     */
    public Integer getGameIntegral() {
        return gameIntegral;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.GAME_INTEGRAL
     *
     * @param gameIntegral the value for user_details_info.GAME_INTEGRAL
     *
     * @mbggenerated
     */
    public void setGameIntegral(Integer gameIntegral) {
        this.gameIntegral = gameIntegral;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.MATCH_INTEGRAL
     *
     * @return the value of user_details_info.MATCH_INTEGRAL
     *
     * @mbggenerated
     */
    public Integer getMatchIntegral() {
        return matchIntegral;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.MATCH_INTEGRAL
     *
     * @param matchIntegral the value for user_details_info.MATCH_INTEGRAL
     *
     * @mbggenerated
     */
    public void setMatchIntegral(Integer matchIntegral) {
        this.matchIntegral = matchIntegral;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.WEEKS_INTEGRAL
     *
     * @return the value of user_details_info.WEEKS_INTEGRAL
     *
     * @mbggenerated
     */
    public Integer getWeeksIntegral() {
        return weeksIntegral;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.WEEKS_INTEGRAL
     *
     * @param weeksIntegral the value for user_details_info.WEEKS_INTEGRAL
     *
     * @mbggenerated
     */
    public void setWeeksIntegral(Integer weeksIntegral) {
        this.weeksIntegral = weeksIntegral;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.MONTH_INTEGRAL
     *
     * @return the value of user_details_info.MONTH_INTEGRAL
     *
     * @mbggenerated
     */
    public Integer getMonthIntegral() {
        return monthIntegral;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.MONTH_INTEGRAL
     *
     * @param monthIntegral the value for user_details_info.MONTH_INTEGRAL
     *
     * @mbggenerated
     */
    public void setMonthIntegral(Integer monthIntegral) {
        this.monthIntegral = monthIntegral;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.ROOM_PERSION_TYPE
     *
     * @return the value of user_details_info.ROOM_PERSION_TYPE
     *
     * @mbggenerated
     */
    public Integer getRoomPersionType() {
        return roomPersionType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.ROOM_PERSION_TYPE
     *
     * @param roomPersionType the value for user_details_info.ROOM_PERSION_TYPE
     *
     * @mbggenerated
     */
    public void setRoomPersionType(Integer roomPersionType) {
        this.roomPersionType = roomPersionType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.ROOM_ID
     *
     * @return the value of user_details_info.ROOM_ID
     *
     * @mbggenerated
     */
    public Integer getRoomId() {
        return roomId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.ROOM_ID
     *
     * @param roomId the value for user_details_info.ROOM_ID
     *
     * @mbggenerated
     */
    public void setRoomId(Integer roomId) {
        this.roomId = roomId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.IDOU
     *
     * @return the value of user_details_info.IDOU
     *
     * @mbggenerated
     */
    public Integer getIdou() {
        return idou;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.IDOU
     *
     * @param idou the value for user_details_info.IDOU
     *
     * @mbggenerated
     */
    public void setIdou(Integer idou) {
        this.idou = idou;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.ADD_UP_COUNT
     *
     * @return the value of user_details_info.ADD_UP_COUNT
     *
     * @mbggenerated
     */
    public Integer getAddUpCount() {
        return addUpCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.ADD_UP_COUNT
     *
     * @param addUpCount the value for user_details_info.ADD_UP_COUNT
     *
     * @mbggenerated
     */
    public void setAddUpCount(Integer addUpCount) {
        this.addUpCount = addUpCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.ADD_UP_TIME
     *
     * @return the value of user_details_info.ADD_UP_TIME
     *
     * @mbggenerated
     */
    public Integer getAddUpTime() {
        return addUpTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.ADD_UP_TIME
     *
     * @param addUpTime the value for user_details_info.ADD_UP_TIME
     *
     * @mbggenerated
     */
    public void setAddUpTime(Integer addUpTime) {
        this.addUpTime = addUpTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.WINNING_COUNT
     *
     * @return the value of user_details_info.WINNING_COUNT
     *
     * @mbggenerated
     */
    public Integer getWinningCount() {
        return winningCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.WINNING_COUNT
     *
     * @param winningCount the value for user_details_info.WINNING_COUNT
     *
     * @mbggenerated
     */
    public void setWinningCount(Integer winningCount) {
        this.winningCount = winningCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.POOL_CNT
     *
     * @return the value of user_details_info.POOL_CNT
     *
     * @mbggenerated
     */
    public Integer getPoolCnt() {
        return poolCnt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.POOL_CNT
     *
     * @param poolCnt the value for user_details_info.POOL_CNT
     *
     * @mbggenerated
     */
    public void setPoolCnt(Integer poolCnt) {
        this.poolCnt = poolCnt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.POOL_WIN_CNT
     *
     * @return the value of user_details_info.POOL_WIN_CNT
     *
     * @mbggenerated
     */
    public Integer getPoolWinCnt() {
        return poolWinCnt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.POOL_WIN_CNT
     *
     * @param poolWinCnt the value for user_details_info.POOL_WIN_CNT
     *
     * @mbggenerated
     */
    public void setPoolWinCnt(Integer poolWinCnt) {
        this.poolWinCnt = poolWinCnt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.HAND_CNT
     *
     * @return the value of user_details_info.HAND_CNT
     *
     * @mbggenerated
     */
    public Integer getHandCnt() {
        return handCnt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.HAND_CNT
     *
     * @param handCnt the value for user_details_info.HAND_CNT
     *
     * @mbggenerated
     */
    public void setHandCnt(Integer handCnt) {
        this.handCnt = handCnt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.GAME_CNT
     *
     * @return the value of user_details_info.GAME_CNT
     *
     * @mbggenerated
     */
    public Integer getGameCnt() {
        return gameCnt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.GAME_CNT
     *
     * @param gameCnt the value for user_details_info.GAME_CNT
     *
     * @mbggenerated
     */
    public void setGameCnt(Integer gameCnt) {
        this.gameCnt = gameCnt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.USER_TYPE
     *
     * @return the value of user_details_info.USER_TYPE
     *
     * @mbggenerated
     */
    public Integer getUserType() {
        return userType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.USER_TYPE
     *
     * @param userType the value for user_details_info.USER_TYPE
     *
     * @mbggenerated
     */
    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.UP_MSG_ID
     *
     * @return the value of user_details_info.UP_MSG_ID
     *
     * @mbggenerated
     */
    public Integer getUpMsgId() {
        return upMsgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.UP_MSG_ID
     *
     * @param upMsgId the value for user_details_info.UP_MSG_ID
     *
     * @mbggenerated
     */
    public void setUpMsgId(Integer upMsgId) {
        this.upMsgId = upMsgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.DAY_INDEX
     *
     * @return the value of user_details_info.DAY_INDEX
     *
     * @mbggenerated
     */
    public Integer getDayIndex() {
        return dayIndex;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.DAY_INDEX
     *
     * @param dayIndex the value for user_details_info.DAY_INDEX
     *
     * @mbggenerated
     */
    public void setDayIndex(Integer dayIndex) {
        this.dayIndex = dayIndex;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.DAY_LOGIN_DATE
     *
     * @return the value of user_details_info.DAY_LOGIN_DATE
     *
     * @mbggenerated
     */
    public Date getDayLoginDate() {
        return dayLoginDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.DAY_LOGIN_DATE
     *
     * @param dayLoginDate the value for user_details_info.DAY_LOGIN_DATE
     *
     * @mbggenerated
     */
    public void setDayLoginDate(Date dayLoginDate) {
        this.dayLoginDate = dayLoginDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.birthday
     *
     * @return the value of user_details_info.birthday
     *
     * @mbggenerated
     */
    public Date getBirthday() {
        return birthday;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.birthday
     *
     * @param birthday the value for user_details_info.birthday
     *
     * @mbggenerated
     */
    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.PERSON_SIGN
     *
     * @return the value of user_details_info.PERSON_SIGN
     *
     * @mbggenerated
     */
    public String getPersonSign() {
        return personSign;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.PERSON_SIGN
     *
     * @param personSign the value for user_details_info.PERSON_SIGN
     *
     * @mbggenerated
     */
    public void setPersonSign(String personSign) {
        this.personSign = personSign == null ? null : personSign.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.MODIFY_NAME_TIMES
     *
     * @return the value of user_details_info.MODIFY_NAME_TIMES
     *
     * @mbggenerated
     */
    public Integer getModifyNameTimes() {
        return modifyNameTimes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.MODIFY_NAME_TIMES
     *
     * @param modifyNameTimes the value for user_details_info.MODIFY_NAME_TIMES
     *
     * @mbggenerated
     */
    public void setModifyNameTimes(Integer modifyNameTimes) {
        this.modifyNameTimes = modifyNameTimes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.modify_head_time
     *
     * @return the value of user_details_info.modify_head_time
     *
     * @mbggenerated
     */
    public Integer getModifyHeadTime() {
        return modifyHeadTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.modify_head_time
     *
     * @param modifyHeadTime the value for user_details_info.modify_head_time
     *
     * @mbggenerated
     */
    public void setModifyHeadTime(Integer modifyHeadTime) {
        this.modifyHeadTime = modifyHeadTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.PHONE
     *
     * @return the value of user_details_info.PHONE
     *
     * @mbggenerated
     */
    public String getPhone() {
        return phone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.PHONE
     *
     * @param phone the value for user_details_info.PHONE
     *
     * @mbggenerated
     */
    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.country
     *
     * @return the value of user_details_info.country
     *
     * @mbggenerated
     */
    public String getCountry() {
        return country;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.country
     *
     * @param country the value for user_details_info.country
     *
     * @mbggenerated
     */
    public void setCountry(String country) {
        this.country = country == null ? null : country.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.city
     *
     * @return the value of user_details_info.city
     *
     * @mbggenerated
     */
    public String getCity() {
        return city;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.city
     *
     * @param city the value for user_details_info.city
     *
     * @mbggenerated
     */
    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.province
     *
     * @return the value of user_details_info.province
     *
     * @mbggenerated
     */
    public String getProvince() {
        return province;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.province
     *
     * @param province the value for user_details_info.province
     *
     * @mbggenerated
     */
    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.spectrum_cnt
     *
     * @return the value of user_details_info.spectrum_cnt
     *
     * @mbggenerated
     */
    public Integer getSpectrumCnt() {
        return spectrumCnt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.spectrum_cnt
     *
     * @param spectrumCnt the value for user_details_info.spectrum_cnt
     *
     * @mbggenerated
     */
    public void setSpectrumCnt(Integer spectrumCnt) {
        this.spectrumCnt = spectrumCnt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.coin
     *
     * @return the value of user_details_info.coin
     *
     * @mbggenerated
     */
    public Integer getCoin() {
        return coin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.coin
     *
     * @param coin the value for user_details_info.coin
     *
     * @mbggenerated
     */
    public void setCoin(Integer coin) {
        this.coin = coin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.password
     *
     * @return the value of user_details_info.password
     *
     * @mbggenerated
     */
    public String getPassword() {
        return password;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.password
     *
     * @param password the value for user_details_info.password
     *
     * @mbggenerated
     */
    public void setPassword(String password) {
        this.password = password == null ? null : password.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.send_code_time
     *
     * @return the value of user_details_info.send_code_time
     *
     * @mbggenerated
     */
    public String getSendCodeTime() {
        return sendCodeTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.send_code_time
     *
     * @param sendCodeTime the value for user_details_info.send_code_time
     *
     * @mbggenerated
     */
    public void setSendCodeTime(String sendCodeTime) {
        this.sendCodeTime = sendCodeTime == null ? null : sendCodeTime.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.random_num
     *
     * @return the value of user_details_info.random_num
     *
     * @mbggenerated
     */
    public String getRandomNum() {
        return randomNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.random_num
     *
     * @param randomNum the value for user_details_info.random_num
     *
     * @mbggenerated
     */
    public void setRandomNum(String randomNum) {
        this.randomNum = randomNum == null ? null : randomNum.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.pay_password
     *
     * @return the value of user_details_info.pay_password
     *
     * @mbggenerated
     */
    public String getPayPassword() {
        return payPassword;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.pay_password
     *
     * @param payPassword the value for user_details_info.pay_password
     *
     * @mbggenerated
     */
    public void setPayPassword(String payPassword) {
        this.payPassword = payPassword == null ? null : payPassword.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.pay_pwd_error_time
     *
     * @return the value of user_details_info.pay_pwd_error_time
     *
     * @mbggenerated
     */
    public Integer getPayPwdErrorTime() {
        return payPwdErrorTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.pay_pwd_error_time
     *
     * @param payPwdErrorTime the value for user_details_info.pay_pwd_error_time
     *
     * @mbggenerated
     */
    public void setPayPwdErrorTime(Integer payPwdErrorTime) {
        this.payPwdErrorTime = payPwdErrorTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.pay_pwd_status
     *
     * @return the value of user_details_info.pay_pwd_status
     *
     * @mbggenerated
     */
    public Integer getPayPwdStatus() {
        return payPwdStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.pay_pwd_status
     *
     * @param payPwdStatus the value for user_details_info.pay_pwd_status
     *
     * @mbggenerated
     */
    public void setPayPwdStatus(Integer payPwdStatus) {
        this.payPwdStatus = payPwdStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.pay_pwd_lock_time
     *
     * @return the value of user_details_info.pay_pwd_lock_time
     *
     * @mbggenerated
     */
    public Long getPayPwdLockTime() {
        return payPwdLockTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.pay_pwd_lock_time
     *
     * @param payPwdLockTime the value for user_details_info.pay_pwd_lock_time
     *
     * @mbggenerated
     */
    public void setPayPwdLockTime(Long payPwdLockTime) {
        this.payPwdLockTime = payPwdLockTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.pic_frame
     *
     * @return the value of user_details_info.pic_frame
     *
     * @mbggenerated
     */
    public String getPicFrame() {
        return picFrame;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.pic_frame
     *
     * @param picFrame the value for user_details_info.pic_frame
     *
     * @mbggenerated
     */
    public void setPicFrame(String picFrame) {
        this.picFrame = picFrame == null ? null : picFrame.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.pic_effective_time
     *
     * @return the value of user_details_info.pic_effective_time
     *
     * @mbggenerated
     */
    public Long getPicEffectiveTime() {
        return picEffectiveTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.pic_effective_time
     *
     * @param picEffectiveTime the value for user_details_info.pic_effective_time
     *
     * @mbggenerated
     */
    public void setPicEffectiveTime(Long picEffectiveTime) {
        this.picEffectiveTime = picEffectiveTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.honor_num
     *
     * @return the value of user_details_info.honor_num
     *
     * @mbggenerated
     */
    public Long getHonorNum() {
        return honorNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.honor_num
     *
     * @param honorNum the value for user_details_info.honor_num
     *
     * @mbggenerated
     */
    public void setHonorNum(Long honorNum) {
        this.honorNum = honorNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_details_info.ctime
     *
     * @return the value of user_details_info.ctime
     *
     * @mbggenerated
     */
    public Date getCtime() {
        return ctime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_details_info.ctime
     *
     * @param ctime the value for user_details_info.ctime
     *
     * @mbggenerated
     */
    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }
}