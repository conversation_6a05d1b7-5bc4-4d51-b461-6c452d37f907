package com.dzpk.crazypoker.audit.service;

import com.dzpk.crazypoker.audit.service.bean.AuditWarnTaskInfo;

/**
 * Description:
 * <p>
 */
public interface IAuditServiceImplTransaction {

    /**
     * 金额不一致校验
     * @param auditWarnTaskInfo
     */
    public void amountErrorProcess(AuditWarnTaskInfo auditWarnTaskInfo) throws Exception;

    /**
     * 提现告警校验
     * @param auditWarnTaskInfo
     */
    public void withdrawAudit(AuditWarnTaskInfo auditWarnTaskInfo) throws Exception;

    /**
     * 充值报警校验
     * @param auditWarnTaskInfo
     */
    public void rechargeAudit(AuditWarnTaskInfo auditWarnTaskInfo) throws Exception;
}
