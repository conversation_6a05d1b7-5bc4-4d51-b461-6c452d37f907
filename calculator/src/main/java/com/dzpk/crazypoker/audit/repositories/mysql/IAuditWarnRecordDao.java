package com.dzpk.crazypoker.audit.repositories.mysql;

import org.apache.ibatis.annotations.Insert;
import org.springframework.stereotype.Repository;

import java.util.Date;


@Repository
public interface IAuditWarnRecordDao {

    @Insert("insert into audit_warn_record(nick_name, random_id, remark, type, status, created_time) " +
            "values(#{nickName},#{randomId},#{remark},#{type},#{status},#{date})")
    void insert(String nickName, String randomId, String remark, Integer type, Integer status, Date date);
}
