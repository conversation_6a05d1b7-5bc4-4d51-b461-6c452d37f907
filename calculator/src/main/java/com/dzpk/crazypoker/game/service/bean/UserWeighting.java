package com.dzpk.crazypoker.game.service.bean;

import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserWeighting {

	/**
	 * 玩家ID
	 */
	private Integer userId;

	/**
	 * 是否手工加权
	 */
	private Boolean weightByHand;

	/**
	 * 加权类型：0=未加权，1=永久注销加权，2=新用户加权，3=浅水加权，4=深水加权，5=回归加权
	 */
	private Integer weightCode;

	/**
	 * 本类型加权的次数,【回归加权】有效
	 */
	private Integer weightNum;

	/**
	 * 本加权开始时间
	 */
	private LocalDate startTime;

	/**
	 * 本加权结束时间, null表示无限期
	 */
	private LocalDate endTime;

	/**
	 * 是否完成：0=未完成，1=完成
	 */
	private Boolean isFinish;

	/**
	 * 加权时盲注，新加权时重置
	 */
	private Integer sbChip;

	/**
	 * 加权时分值，新加权时重置
	 */
	private Integer userScore;

	/**
	 * 本加权失效的盲注倍数，新加权时重置
	 */
	private Integer abolishSbTimes;

	/**
	 * 本加权失效的最大盈亏，新加权时重置
	 */
	private BigDecimal abolishMaxDzProfit;

	/**
	 * 本加权累计战绩盈亏，新加权时重置
	 */
	private BigDecimal totalDzProfit;

	/**
	 * 新加权时重置，玩家首次带入牌局时，此字段+1,对应的编号就是玩家在此牌局中的编号
	 */
	private Integer roomCounter;

	/**
	 * updatedTime
	 */
	private LocalDateTime updatedTime;

	/**
	 * createdTime
	 */
	private LocalDateTime createdTime;
}
