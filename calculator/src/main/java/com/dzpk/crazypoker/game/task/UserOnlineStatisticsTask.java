package com.dzpk.crazypoker.game.task;

import com.dzpk.crazypoker.game.service.IUserDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 用户在线玩家数据定时器
 * Created by jayce on 2019/7/1
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class UserOnlineStatisticsTask {

    @Autowired
    private IUserDataService userDataService;

    /**
     * 每天0点30分处理上七天环比数据
     */
    @Scheduled(cron="0 30 0 * * ?")
    //@Scheduled(cron="* */1 * * * ?")
    public void processStatisticsTask(){
        try {
            long start = System.currentTimeMillis();
            log.info("this is scheduler calculate processStatisticsTask task runing...");

            this.userDataService.calculateStatisticsData();

            log.info("this is scheduler calculate processStatisticsTask task end. Cost time :{}ms.", System.currentTimeMillis() - start);
        } catch (Exception e) {
            log.error("processSkyRpTask error.", e);
        }
    }

}
