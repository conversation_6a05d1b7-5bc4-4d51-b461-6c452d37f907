<?xml version="1.0" encoding="UTF-8"?>
<request-mapping>
	<request code="12" classname="com.i366.processor.client.Processor_44_Enter"/>
	<request code="18" classname="com.i366.processor.client.Processor_45_Seta"/>
	<request code="19" classname="com.i366.processor.client.Processor_47_SendAction"/>

	<request code="28" classname="com.i366.processor.client.Processor_57_AddChips"/>

	<request code="30" classname="com.i366.processor.client.Processor_66_EnterPending"/>
	<request code="32" classname="com.i366.processor.client.Processor_67_StartRoom"/>

	<request code="40" classname="com.i366.processor.client.Processor_71_CarryRangeChange"/>
	<request code="43" classname="com.i366.processor.client.Processor_73_ShowCards"/>

	<request code="53" classname="com.i366.processor.client.Processor_97_Xintiao"/>

	<request code="67" classname="com.i366.processor.client.Processor_157_Straddle"/>
	<request code="68" classname="com.i366.processor.client.Processor_158_AutoOp"/>
	<request code="69" classname="com.i366.processor.client.Processor_159_ForceStop"/>

	<request code="72" classname="com.i366.processor.client.Processor_165_CheckPermission"/>

	<request code="80" classname="com.i366.processor.client.Processor_182_UpdateRoomProgress"/>
	<request code="82" classname="com.i366.processor.client.Processor_185_GameReport"/>
	<request code="86" classname="com.i366.processor.client.Processor_186_UserDelay"/>
	<request code="87" classname="com.i366.processor.client.Processor_187_RoomPause"/>

	<request code="90" classname="com.i366.processor.client.Processor_191_PrevGameReview"/>

	<request code="118" classname="com.i366.processor.client.Processor_217_ReplayCollect"/>

	<request code="122" classname="com.i366.processor.client.Processor_222_PlayType"/>
	<request code="123" classname="com.i366.processor.client.Processor_123_OperateLeftTime"/>

	<request code="141" classname="com.i366.processor.client.Processor_401_InsuranceRecieve"/>
	<request code="143" classname="com.i366.processor.client.Processor_403_InsuranceAddTime"/>
	<request code="148" classname="com.i366.processor.client.Processor_410_ShowCardsByUser"/>

	<request code="152" classname="com.i366.processor.client.Processor_412_OccupySeat"/>
	<request code="153" classname="com.i366.processor.client.Processor_413_AddTime"/>
	<request code="154" classname="com.i366.processor.client.Processor_414_StraddleSwitch"/>
	<request code="155" classname="com.i366.processor.client.Processor_415_MuckSwitch"/>

	<request code="160" classname="com.i366.processor.client.Processor_160_VoiceSwitch"/>

</request-mapping>