package com.i366.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 玩家离开房间枚举类
 */
@Getter
@AllArgsConstructor
@ToString
public enum LeaveRoomCode {

    USER_SELF_LEAVE(1,"玩家主动点击离开,未在游戏中"),
    TIMEDOUT_LEAVE(2,"玩家掉线离开"),
    FOBBIDEN_LEAVE(3,"玩家被冻结离开"),
    AI_ONLY_ONE_PERSION_LEAVE(4,"AI玩家只有1个人离开"),
    AI_ROOM_NOT_START_LEAVE(5,"AI玩家房主未点击开始按钮离开"),
    AI_PLAY_ENOUGH_HANDS_LEAVE(6,"AI玩家打够指定手数离开"),
    NEXT_GAME_LEAVE(7,"玩家在上局请求过离开且在游戏中")
    ;

    private int code;

    private String desc;

}
