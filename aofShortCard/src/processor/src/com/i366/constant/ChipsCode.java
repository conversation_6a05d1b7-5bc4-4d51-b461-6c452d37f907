package com.i366.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 金豆变更枚举类
 */
@Getter
@AllArgsConstructor
@ToString
public enum ChipsCode {

    SHOW_CARDS(1,"查看未发公共牌,更新金豆"),
    OPERATE_DELAY(2,"操作延时,更新金豆"),
    INSURE_DELAY(3,"购买保险延时,更新金豆"),
    JACKPOT_RECORD(4,"击中jp,更新金豆"),
    BRINGIN_CHIP(5,"带入积分,更新金豆"),
    ROOM_TIMES_UP(6,"房间结束,更新金豆"),
    USER_AHEAD_LEAVE(7,"提前离桌,更新金豆"),
            ;

    private int code;

    private String desc;
}
