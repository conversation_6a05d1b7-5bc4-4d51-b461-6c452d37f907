
package com.i366.processor.client;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;
import com.i366.constant.Constant;
import com.i366.room.RoomService;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.BaseRequest;
import com.work.comm.client.protocal.Request;
import com.work.comm.io.Handler;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * 上局回顾接口
 */
public class Processor_191_PrevGameReview extends Handler {

    private final Logger logger = LogUtil.getLogger(Processor_191_PrevGameReview.class);

    @Override
    public byte[] handleRequest(BaseRequest req) {
        Request request = (Request) req;
        logger.debug("Processor_191_PrevGameReview id " + request.getUserId());
        int[][] int2 = {
                {130, I366ClientPickUtil.TYPE_INT_4},    // roomId
                {131, I366ClientPickUtil.TYPE_INT_4},    // roomPath
                {132, I366ClientPickUtil.TYPE_INT_4},    // page
        };
        Map<Integer, Object> map = I366ClientPickUtil.pickAll(request.getBt(), int2);
        int roomId = (Integer) map.get(130);
        int roomPath = (Integer) map.get(131);

        RoomService.setUserChannel(request, roomId);

        Task task = new Task(Constant.REQ_GAME_PREV_GAME_REVIEW, map, request, roomId, roomPath);
        WorkThreadService.submit(roomId, task);
        return null;
    }

}