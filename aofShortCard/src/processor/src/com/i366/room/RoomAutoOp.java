package com.i366.room;

import com.dzpk.common.utils.LogUtil;
import com.i366.constant.Constant;
import com.i366.model.player.RoomPersion;
import com.i366.model.room.Room;
import com.work.comm.client.pack.I366ClientPickUtil;
import org.apache.logging.log4j.Logger;

import java.util.Arrays;

/**
 * 玩家托管类操作
 */
public class RoomAutoOp {

    private static Logger logger = LogUtil.getLogger(RoomAutoOp.class);

    // 是否需要托管
    public static long needAutoOp(Room room, int userId) {
        // 房主未点开局
        if (room.getRoomStatus() == 0) {
            return 0;
        }
        if (room.getMinPlayTime() > 0
                && room.getRoomPlayers().get(userId).getRealPlayTime() + 1 < room.getMinPlayTime()) {
            logger.debug("minPlayTime: " + room.getMinPlayTime());
            long roomLeftTime = room.getGameBeginTime() + room.getMaxPlayTime() * 60
                    - System.currentTimeMillis() / 1000;

            long userLeftTime = room.getMinPlayTime() - room.getRoomPlayers().get(userId).getRealPlayTime();
            logger.debug("roomLeftTime: " + roomLeftTime + ", userLeftTime: " + userLeftTime);
            return roomLeftTime < userLeftTime ? roomLeftTime : userLeftTime;
        }
        return 0;
    }

    /**
     * 托管
     * @param rp
     * @param autoOp
     */
    public static void setUserAutoOp(Room room,RoomPersion rp, boolean autoOp) {
        setUserAutoOp(room,rp, autoOp, false);
    }

    /**
     * 托管
     * @param rp
     * @param autoOp
     * @param active
     */
    public static void setUserAutoOp(Room room,RoomPersion rp, boolean autoOp, boolean active) {
        if (!rp.isAutoOp() && autoOp) { // 托管
            if (room.getRoomStatus() >= 3 && room.getRoomStatus() <= 6
                    && room.getCurrentNumber() != -1
                    && room.getRoomPersions()[room.getCurrentNumber()].getUserId() == rp.getUserId()) {
                room.roomProcedure.runByRoomStatus();
            }
            rp.setActive(active);   // 设置主动托管
        }
        else if (rp.isAutoOp() && !autoOp) {  // 取消托管
            rp.setTimeoutCheckOpTimes(0);   // 取消托管，check次数从新计数
            rp.setTimeoutFoldOpTimes(0);    // 取消托管，fold次数从新计数
        }
        rp.setAutoOp(autoOp);
    }

    /**
     * 获取房间内所有人的托管状态
     * @param room
     * @return
     */
    public static Integer[] autoOpStatus(Room room) {
        Integer[] opStatus = new Integer[room.getPlayerCount()];
        for (int i = 0; i < room.getPlayerCount(); i++) {
            RoomPersion rp = room.getRoomPersions()[i];
            if (rp == null) {
                rp = room.getDdRoomPersions()[i];
            }
            if (rp != null) {
                opStatus[i] = rp.isAutoOp() ? 1 : 0;
            } else {
                opStatus[i] = 0;
            }
        }
        return opStatus;
    }

    /**
     * 158 托管状态相关通知
     * @param status
     * @param op
     * @param leftTime
     * @param isFirstDown
     * @param isAutoOp
     * @param opStatus
     * @param roomName
     * @return
     */
    public static byte[] autoOpPus(int status, int op, int leftTime, boolean isFirstDown, boolean isAutoOp,
                                   Integer[] opStatus, String roomName) {
        return autoOpPus(status, op, leftTime, isFirstDown, isAutoOp, opStatus, roomName, false);
    }


    /**
     * 158 托管状态相关通知
     * @param status
     * @param op
     * @param leftTime
     * @param isFirstDown
     * @param isAutoOp
     * @param opStatus
     * @return
     */
    public static byte[] autoOpPus(int status, int op, int leftTime, boolean isFirstDown, boolean isAutoOp,
                                   Integer[] opStatus, String roomName, boolean active) {
        logger.debug("status: " + status + ", op: " + op + ", leftTime: " + leftTime + ", isFirstDown: "
                + isFirstDown + ", opStatus" + Arrays.toString(opStatus) + ", roomName: " + roomName + ", active: " + active);
        Object[][] objs2 = {
                {60, status, I366ClientPickUtil.TYPE_INT_1},        // 返回状态 0成功 1失败
                {61, op, I366ClientPickUtil.TYPE_INT_1},            // 0取消托管 1托管 2进入请求时间 3托管时间到 4其它房间状态查询
                {130, leftTime, I366ClientPickUtil.TYPE_INT_4},     // 倒计时时间，单位S
                {131, isFirstDown ? 1 : 0, I366ClientPickUtil.TYPE_INT_4},  // 是否第一次坐下，0不是，1是
                {132, opStatus, I366ClientPickUtil.TYPE_INT_4_ARRAY},       // 托管状态数组 0没托管 1托管
                {133, isAutoOp ? 1 : 0, I366ClientPickUtil.TYPE_INT_4},     // 是否有被托管(其他房间)
                {134, roomName, I366ClientPickUtil.TYPE_STRING_UTF16},      // 留盲代打的房间名
                {135, active ? 1 : 0, I366ClientPickUtil.TYPE_INT_4},       // 是否是主动托管
        };
        byte[] bytes = I366ClientPickUtil.packAll(objs2, Constant.REQ_REQUEST_AUTO_OP);
        return bytes;
    }

}
