
package com.dzpk.system;

import com.dzpk.db.dao.RoomSearchDao;
import com.dzpk.db.imp.RoomSearchDaoImpl;
import com.dzpk.db.model.RoomSearchInfo;
import com.i366.constant.Constant;
import com.i366.cache.Cache;
import com.i366.constant.RoomFinishCode;
import com.i366.model.room.Room;
import com.dzpk.component.repositories.redis.RedisService;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.db.dao.RoomDao;
import com.dzpk.db.imp.RoomDaoImpl;
import com.i366.util.RoomUtil;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * 检查关闭房间定时器
 */
public class RoomsManage {
	public static Logger  logger = LogUtil.getLogger(RoomsManage.class);

    private static int[] roomPathConfig = {23};
	
	public static void init(int roomServerNumber) {
		RoomsManage rm = new RoomsManage();
		rm.roomCloseTask();
		rm.autoCloseRoomTask(roomServerNumber);
		rm.autoCreateRoomTask(Constant.ROOM_PATH_AOF);
        rm.autoCloseEmptyPlanRoomTask(roomServerNumber);
	}
	
    public void roomCloseTask() {
    	Timer tim = new Timer();
    	TimerTask tt = new TimerTask() {
			public void run() {

                // 检查是否有需要关闭的房间
                try {
                    for (int roomPath : roomPathConfig) {
                        Set<String> roomInfoSet = forceClose(roomPath);
                        if (null == roomInfoSet) {
                            continue;
                        } else {
                            if (roomInfoSet.size() <= 0) {
                                continue;
                            } else {
                                for (String removeId : roomInfoSet) {
                                    logger.debug("RoomsManage:roomCloseTask key=> " + removeId + ",size=>" + roomInfoSet.size());
                                    String[] removeRoomInfo = removeId.replaceAll("\"","").split(",");
                                    if(removeRoomInfo.length == 2){
                                        int closeType = Integer.parseInt(removeRoomInfo[0]);
                                        int roomId = Integer.parseInt(removeRoomInfo[1]);
                                        Room room = Cache.getRoom(roomId,roomPath);
                                        if (room != null) {
                                            if(closeType == 1){ //普通解散
                                                room.getRoomService().forceStopRoom(RoomFinishCode.UPDATED_SERVER_FINISH);
                                            }else{ //强制解散
                                                room.roomProcedure.forceCloseRoom = true;
                                                room.roomProcedure.timesup(RoomFinishCode.FORCE_STOP_FINISH);
                                                logger.debug("sunron-forcecloseroom====end:" + System.currentTimeMillis() + ", roomId: " + roomId);
                                            }
                                            RedisService.getRedisService().removeCancelledGame(roomPath,removeId);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }catch (Exception e){
                    logger.error("roomCloseTask=>{} =>{}",e.getMessage(),e);
                }

			}
		};
    	tim.schedule(tt, 0, 1000 * 60);
    }
    
    /**
     * 自动关闭超过24小时没开启房间任务
     * 
     */
    public void autoCloseRoomTask(int roomServerNumber) {
        Timer tim = new Timer();
        TimerTask tt = new TimerTask() {
            public void run() {
                closeTimeOutRoom(roomServerNumber,Constant.ROOM_PATH_AOF);
            }

        };
        tim.schedule(tt, 0, 1000 * 5 * 60 );
    }

    /**
     * 扫描自动创建的牌局且初始化
     * @param roomPath
     */
    public void autoCreateRoomTask(int roomPath){
        Timer tim1 = new Timer();
        TimerTask tt1 = new TimerTask() {
            public void run() {
                Set<String> roomInfoSet = autoCreate(roomPath);
                if (null != roomInfoSet && roomInfoSet.size() >= 0) {
                    for (String createRoomId : roomInfoSet) {
                        int autoCreateRoomId = Integer.parseInt(createRoomId);
                        logger.debug("RoomsManage:autoCreateRoomTask createRoomId=> " + createRoomId + ",size=>" + roomInfoSet.size());
                        Room room = Cache.getRoom(autoCreateRoomId, roomPath);
                        if (null == room) {
                            RoomDao roomDao = new RoomDaoImpl();
                            boolean autoCreateFlag = roomDao.createRoom(roomPath,autoCreateRoomId);
                            if(autoCreateFlag){
                                logger.debug("autoCreateRoomTask successfuly,createRoomId={}",createRoomId);
                                RedisService redisService = RedisService.getRedisService();
                                redisService.removeAutoCreateRooms(roomPath,createRoomId);
                            }
                        }
                    }
                }
            }
        };
        tim1.schedule(tt1, 0, 1000 * 30);
    }

    /**
     * 关闭超过24小时没开启房间
     * @param roomServerNumber 游戏服务器id
     * @param roomPath 房间路径
     */
    private void closeTimeOutRoom(int roomServerNumber,int roomPath){
        long timeOutSecond = System.currentTimeMillis()/1000;//当前秒数
        //timeOutSecond -= 20;//测试
        timeOutSecond-=Constant.ONE_DAY_SECONDS;
        RoomDao dao = new RoomDaoImpl();
        Vector<Integer> roomTimeOutLists =  dao.getTimeOutRoomList(timeOutSecond,roomServerNumber,roomPath);
        logger.debug("关闭超过24小时没开启的房间,timeOutSecond={},roomServerNumber={},roomPath={},需要关闭的房间数量={}",timeOutSecond,
                roomServerNumber,roomPath,roomTimeOutLists.size());
        if(null != roomTimeOutLists && roomTimeOutLists.size() > 0)
            for (int roomId : roomTimeOutLists) {
                Room room = Cache.getRoom(roomId, roomPath);
                if (room != null) {
                    room.roomProcedure.forceCloseRoom = true;
                    room.roomProcedure.timesup(RoomFinishCode.AUTO_CLOSE_FINISH);
                    logger.debug("autoCloseRoom end:" + System.currentTimeMillis() + ", roomId: " + roomId);
                }
            }
    }

    /**
     * 是否有需要解散的房间
     *
     * @return
     */
    private static Set<String> forceClose(int roomPath) {
        try {
            RedisService redisService = RedisService.getRedisService();
            Set<String> cancelledIds = redisService.getCancelledGame(roomPath);
            return cancelledIds;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 是否有需要自动创建的房间
     * @return
     */
    private static Set<String> autoCreate(int roomPath) {
        try {
            RedisService redisService = RedisService.getRedisService();
            Set<String> cancelledIds = redisService.getAutoCreateRooms(roomPath);
            return cancelledIds;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 自动解散自动开房计划的空房间/ 30s
     */
    private void autoCloseEmptyPlanRoomTask(int roomServerNumber) {
        Timer tim = new Timer();
        TimerTask tt = new TimerTask() {
            public void run() {
                RoomSearchDao dao = new RoomSearchDaoImpl();
                // 查询符合条件的空房间
                List<RoomSearchInfo> cancelRoomSearchInfos = dao.queryEmptyPlanRoomIds(roomServerNumber);
                logger.info("autoCloseEmptyPlanRoomTask cancelRoomSearchInfos: {}, roomServerNumber:{}.", cancelRoomSearchInfos, roomServerNumber);

                // 关闭房间
                if(cancelRoomSearchInfos != null) {
                    for(RoomSearchInfo roomSearchInfo : cancelRoomSearchInfos) {
                        if(roomSearchInfo != null) {
                            Room room = Cache.getRoom(roomSearchInfo.getRoomId(), roomSearchInfo.getRoomPath());
                            if(room != null && RoomUtil.getCanPlayNum(room) <= 0) { // 查询打牌人数为0的牌局，才进行解散
                                logger.info("autoCloseEmptyPlanRoomTask force stop room:{}.", room.getRoomId());
                                RoomUtil.stopRoomNotice(room, "");  // 发送解散房间通知房间内玩家
                                room.getRoomService().forceStopRoom(RoomFinishCode.PLAN_ROOM_AUTO_FINISH);
                            }
                        }
                    }
                }
            }
        };
        tim.schedule(tt, 0, 1000 * 30);
    }
}

