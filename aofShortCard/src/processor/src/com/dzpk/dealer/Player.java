package com.dzpk.dealer;


import com.dzpk.aof.model.AofPlayer;

public class Player {
    /**** start 基础数据 ****/
    private int userId;
    private int intPoolCnt = 0;
    private int inPoolWinCnt = 0;
    private int handCnt = 0;
    private int insurance = 0;          // 保险收益
    private int totalInsurance = 0;   // 仅用于战绩保险明细展示，个人保险收益(记录用户在某个房间里的所有保险营收记录 包含：保险盈利和保险赔付)

    private int leftChip = 0;
    private int earn = 0;               // 总盈亏
    private int bringIn = 0;            // 总带入
    private int winCnt = 0;             // 赢的手数
    private int jackPotScore = 0;       // jackPot总得分
    private int jackPotRealScore = 0;   // jackPot实际总得分
    private int jackPotBetScore = 0;    // jackPot击中总得分

    /**** end 基础数据 ****/

    /**** start allin面板相关 ****/
    private int allCnt = 0;
    private int allinWinCnt = 0;
    private int allinLoseCnt = 0;
    private int allinEarn = 0;
    /**** end allin面板相关 ****/

    /**** start 牌局阶段数据 ****/
    private int manpaiCnt = 0;
    private int fanpaiCnt = 0;
    private int zhuanpaiCnt = 0;
    private int hepaiCnt = 0;
    private int tanpaiCnt = 0;

    private int manpaiWinCnt = 0;
    private int fanpaiWinCnt = 0;
    private int zhuanpaiWinCnt = 0;
    private int hepaiWinCnt = 0;
    private int tanpaiWinCnt = 0;
    private int tanpaiEarn = 0;
    /**** end 牌局阶段数据 ****/

    /**** start 操作数据 ****/
    private int perCnt = 0;
    private int bStealCnt = 0;
    private int stealCnt = 0;
    private int cbet = 0;
    private int bCbet = 0;
    private int bet3 = 0;
    private int afJiazhuCnt = 0;
    private int afGenzhuCnt = 0;
    private int afXiazhuCnt = 0;
    /**** end 操作数据 ****/
    
    private int isManager = 0;          // 是否管理员，0 否 1 是
    private int isTribeCreator = 0;     // 是否同盟创建人，0 否 1 是

    private int clubId;                 //俱乐部id
    private String clubName;            //俱乐部名称
    private int tribeId;                //联盟id
    private double plFee;               //玩家盈利服务费
    private int bringinExtractChip;  //当局带入的可提取金豆
    private int bringinNotExtrackChip;  //当局带入的不可提取金豆
    private int bringInTimes;        //当局带入次数
    private int clubRoomUserCharge = 0;  //牌局服务费

    public int getClubRoomUserCharge() {
        return clubRoomUserCharge;
    }

    public void addClubRoomUserCharge(int clubRoomUserCharge) {
        this.clubRoomUserCharge = this.clubRoomUserCharge + clubRoomUserCharge;
    }
    public int getBringinExtractChip() {
        return bringinExtractChip;
    }

    public void setBringinExtractChip(int bringinExtractChip) {
        this.bringinExtractChip = bringinExtractChip;
    }

    public int getBringinNotExtrackChip() {
        return bringinNotExtrackChip;
    }

    public void setBringinNotExtrackChip(int bringinNotExtrackChip) {
        this.bringinNotExtrackChip = bringinNotExtrackChip;
    }

    public double getPlFee() {
        return plFee;
    }

    public void setPlFee(double plFee) {
        this.plFee = plFee;
    }

    private AofPlayer aofPlayer;        //aof所需数据

    public int getClubId() {
        return clubId;
    }

    public void setClubId(int clubId) {
        this.clubId = clubId;
    }

    public String getClubName() {
        return clubName;
    }

    public void setClubName(String clubName) {
        this.clubName = clubName;
    }

    public Player() {
    }

    public Player(int isManager, int isTribeCreator,AofPlayer aofPlayer) {
        this.isManager = isManager;
        this.isTribeCreator = isTribeCreator;
        this.aofPlayer = aofPlayer;
    }

    public void setTanpaiWinCnt(int tanpaiWinCnt) {
        this.tanpaiWinCnt = tanpaiWinCnt;
    }

    public int getTanpaiWinCnt() {
        return tanpaiWinCnt;
    }

    public void setHepaiWinCnt(int hepaiWinCnt) {
        this.hepaiWinCnt = hepaiWinCnt;
    }

    public int getHepaiWinCnt() {
        return hepaiWinCnt;
    }

    public void setZhuanpaiWinCnt(int zhuanpaiWinCnt) {
        this.zhuanpaiWinCnt = zhuanpaiWinCnt;
    }

    public int getZhuanpaiWinCnt() {
        return zhuanpaiWinCnt;
    }

    public void setFanpaiWinCnt(int fanpaiWinCnt) {
        this.fanpaiWinCnt = fanpaiWinCnt;
    }

    public int getFanpaiWinCnt() {
        return fanpaiWinCnt;
    }

    public void setManpaiWinCnt(int manpaiWinCnt) {
        this.manpaiWinCnt = manpaiWinCnt;
    }

    public int getManpaiWinCnt() {
        return manpaiWinCnt;
    }

    public void setTanpaiCnt(int tanpaiCnt) {
        this.tanpaiCnt = tanpaiCnt;
    }

    public int getTanpaiCnt() {
        return tanpaiCnt;
    }

    public void setHepaiCnt(int hepaiCnt) {
        this.hepaiCnt = hepaiCnt;
    }

    public int getHepaiCnt() {
        return hepaiCnt;
    }

    public void setZhuanpaiCnt(int zhuanpaiCnt) {
        this.zhuanpaiCnt = zhuanpaiCnt;
    }

    public int getZhuanpaiCnt() {
        return zhuanpaiCnt;
    }

    public void setFanpaiCnt(int fanpaiCnt) {
        this.fanpaiCnt = fanpaiCnt;
    }

    public int getFanpaiCnt() {
        return fanpaiCnt;
    }

    public void setManpaiCnt(int manpaiCnt) {
        this.manpaiCnt = manpaiCnt;
    }

    public int getManpaiCnt() {
        return manpaiCnt;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public int getUserId() {
        return userId;
    }

    public void setAllCnt(int allCnt) {
        this.allCnt = allCnt;
    }

    public int getAllCnt() {
        return allCnt;
    }

    public void setAllinWinCnt(int allinWinCnt) {
        this.allinWinCnt = allinWinCnt;
    }

    public int getAllinWinCnt() {
        return allinWinCnt;
    }

    public void setAllinLoseCnt(int allinLoseCnt) {
        this.allinLoseCnt = allinLoseCnt;
    }

    public int getAllinLoseCnt() {
        return allinLoseCnt;
    }

    public void setAllinEarn(int allinEarn) {
        this.allinEarn = allinEarn;
    }

    public int getAllinEarn() {
        return allinEarn;
    }

    public void setInPoolCnt(int intPoolCnt) {
        this.intPoolCnt = intPoolCnt;
    }

    public int getInPoolCnt() {
        return intPoolCnt;
    }

    public void setInPoolWinCnt(int inPoolWinCnt) {
        this.inPoolWinCnt = inPoolWinCnt;
    }

    public int getInPoolWinCnt() {
        return inPoolWinCnt;
    }

    public void setHandCnt(int handCnt) {
        this.handCnt = handCnt;
    }

    public int getHandCnt() {
        return handCnt;
    }

    public void setInsurance(int insurance) {
        this.insurance = insurance;
    }

    public int getInsurance() {
        return insurance;
    }
    
    public int getTotalInsurance() {
        return totalInsurance;
    }

    public void setTotalInsurance(int totalInsurance) {
        this.totalInsurance = totalInsurance;
    }

    public void setLeftChip(int leftChip) {
        this.leftChip = leftChip;
    }

    public int getLeftChip() {
        return leftChip;
    }

    public void setEarn(int earn) {
        this.earn = earn;
    }

    public int getEarn() {
        return earn;
    }

    public void setBringIn(int bringIn) {
        this.bringIn = bringIn;
    }

    public int getBringIn() {
        return bringIn;
    }

    public void setPerCnt(int perCnt) {
        this.perCnt = perCnt;
    }

    public int getPerCnt() {
        return perCnt;
    }

    public void setBStealCnt(int bStealCnt) {
        this.bStealCnt = bStealCnt;
    }

    public int getBStealCnt() {
        return bStealCnt;
    }

    public void setStealCnt(int stealCnt) {
        this.stealCnt = stealCnt;
    }

    public int getStealCnt() {
        return stealCnt;
    }

    public void setbCbet(int bCbet) {
        this.bCbet = bCbet;
    }

    public int getbCbet() {
        return bCbet;
    }

    public void setCbet(int cbet) {
        this.cbet = cbet;
    }

    public int getCbet() {
        return cbet;
    }

    public void setTanpaiEarn(int tanpaiEarn) {
        this.tanpaiEarn = tanpaiEarn;
    }

    public int getTanpaiEarn() {
        return tanpaiEarn;
    }

    public void setWinCnt(int winCnt) {
        this.winCnt = winCnt;
    }

    public int getWinCnt() {
        return winCnt;
    }

    public int getIsManager() {
        return isManager;
    }

    public void setIsManager(int isManager) {
        this.isManager = isManager;
    }

    public int getIsTribeCreator() {
        return isTribeCreator;
    }

    public void setIsTribeCreator(int isTribeCreator) {
        this.isTribeCreator = isTribeCreator;
    }

    public int getJackPotScore() {
        return jackPotScore;
    }

    public void setJackPotScore(int jackPotScore) {
        this.jackPotScore = jackPotScore;
    }

    public int getJackPotBetScore() {
        return jackPotBetScore;
    }

    public void setJackPotBetScore(int jackPotBetScore) {
        this.jackPotBetScore = jackPotBetScore;
    }

    public int getJackPotRealScore() {
        return jackPotRealScore;
    }

    public void setJackPotRealScore(int jackPotRealScore) {
        this.jackPotRealScore = jackPotRealScore;
    }

    public int getAfJiazhuCnt() {
        return afJiazhuCnt;
    }

    public void setAfJiazhuCnt(int afJiazhuCnt) {
        this.afJiazhuCnt = afJiazhuCnt;
    }

    public int getAfGenzhuCnt() {
        return afGenzhuCnt;
    }

    public void setAfGenzhuCnt(int afGenzhuCnt) {
        this.afGenzhuCnt = afGenzhuCnt;
    }

    public int getAfXiazhuCnt() {
        return afXiazhuCnt;
    }

    public void setAfXiazhuCnt(int afXiazhuCnt) {
        this.afXiazhuCnt = afXiazhuCnt;
    }

    public int getBet3() {
        return bet3;
    }

    public void setBet3(int bet3) {
        this.bet3 = bet3;
    }

    public int getTribeId() {
        return tribeId;
    }

    public void setTribeId(int tribeId) {
        this.tribeId = tribeId;
    }

    public AofPlayer getAofPlayer() {
        return aofPlayer;
    }

    public void setAofPlayer(AofPlayer aofPlayer) {
        this.aofPlayer = aofPlayer;
    }

    public int getBringInTimes() {
        return bringInTimes;
    }

    public void setBringInTimes(int bringInTimes) {
        this.bringInTimes = bringInTimes;
    }
}
