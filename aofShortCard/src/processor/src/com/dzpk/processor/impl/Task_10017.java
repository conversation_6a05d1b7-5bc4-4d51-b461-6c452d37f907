package com.dzpk.processor.impl;

import com.dzpk.common.utils.LogUtil;
import org.apache.logging.log4j.Logger;

import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.cache.Cache;
import com.i366.model.room.Room;

/**
 * 给玩家花钻石看牌的5s延时任务已到期
 * <AUTHOR>
 * @email  <EMAIL>
 * @date   2016年12月22日
 *
 */
public class Task_10017 implements IProcessor {
    private Logger logger = LogUtil.getLogger(Task_10017.class);

    @Override
    public void handle(Task task) {
        if (!task.isValid()) {
            logger.debug("invalid message");
            return;
        }
        
        Room room = Cache.getRoom(task.getRoomId(), task.getRoomPath());
        if (null != room) {
            logger.debug("Task_10017 is timeout");
//            if (!task.isValid()) {
//                room.roomProcedure.delayTaskMap.remove(task.getId());
//                logger.debug("task " + task.getId() + " is timeout, but shouldn't do anything!!!");
//                return;
//            }
        
            logger.debug("inRequest410?=" + room.isInRequest410() + ", pendingTimeOut?=" + room.isPendingTimeOut());
            if (!room.isInRequest410()) {
                // 未在处理410请求时，时间一到立即进入下一手任务TASK_NEXT_GAME
                room.setUserCanTurnTimes(0);
                logger.debug("now need begin task [TASK_NEXT_GAME]");
                // 延时0.5s进入下一手的逻辑 还是直接进入下一手?
                room.roomProcedure.genNextGameTask(500);
            }
            room.setPendingTimeOut(true);
            room.roomProcedure.delayTaskMap.remove(task.getId());
        }       
    }
}
