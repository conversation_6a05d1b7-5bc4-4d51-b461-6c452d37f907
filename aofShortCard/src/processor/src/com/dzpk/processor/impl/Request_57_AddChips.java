package com.dzpk.processor.impl;

import com.dzpk.audit.IAuditService;
import com.dzpk.audit.constant.AuditOperationCode;
import com.dzpk.audit.impl.AuditServiceImpl;
import com.dzpk.commission.repositories.mysql.ClubTribeDao;
import com.dzpk.commission.repositories.mysql.impl.ClubTribeDaoImpl;
import com.dzpk.commission.repositories.mysql.model.ClubTribeModel;
import com.dzpk.common.config.ConfigUtil;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.repositories.mysql.dbpool.DBUtil;
import com.dzpk.component.repositories.redis.RedisService;
import com.dzpk.db.dao.UserInfoDao;
import com.dzpk.db.dao.UserLevelDao;
import com.dzpk.db.imp.UserInfoDaoImp;
import com.dzpk.db.imp.UserLevelDaoImp;
import com.dzpk.db.model.UserInfo;
import com.dzpk.db.model.UserLevel;
import com.dzpk.dealer.Player;
import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.dzpk.work.TaskConstant;
import com.dzpk.work.WorkThreadService;
import com.i366.cache.Cache;
import com.i366.constant.Constant;
import com.i366.constant.UserLevelCode;
import com.i366.model.player.RoomPersion;
import com.i366.model.player.RoomPlayer;
import com.i366.model.room.Room;
import com.i366.model.room.UserRequetInfo;
import com.i366.room.RoomAutoOp;
import com.i366.room.RoomIpGps;
import com.i366.room.RoomRequest;
import com.i366.util.PublisherUtil;
import com.i366.util.RoomUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.Request;
import org.apache.logging.log4j.Logger;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * 玩家请求带入积分
 */
public class Request_57_AddChips implements IProcessor {

    private final Logger logger = LogUtil.getLogger(Request_57_AddChips.class);

    public void handle(Task task) {
        int roomId = task.getRoomId();
        int roomPath = task.getRoomPath();
        Request request = (Request) task.getRequest();

        int chips = (Integer) task.getMap().get(130); // 需要添加的筹码
        int selectClubId = (Integer) task.getMap().get(134); // 社区id
        logger.debug("receive=" + task.getMap().toString());

        int creditValue = Integer.MAX_VALUE;
        int userId = request.getUserId();
        String longitudeStr = task.getMap().get(62) == null ? "" : (String) task.getMap().get(62);
        String latitudeStr = task.getMap().get(63) == null ? "" : (String) task.getMap().get(63);
        String clientIp  = task.getMap().get(64) == null ? "" : (String) task.getMap().get(64);

        String IMEI = task.getMap().get(136) == null ? "" : (String) task.getMap().get(136);
        String MAC = task.getMap().get(137) == null ? "" : (String) task.getMap().get(137);
        int virtual = task.getMap().get(138) == null ? 0 : (Integer) task.getMap().get(138);
        String formattedAddress = task.getMap().get(139) == null ? "" : (String) task.getMap().get(139);

        logger.debug("查询带入相关数据：roomId=" + roomId + " userId=" + request.getUserId() + " selectClubId="
                + selectClubId + " longitudeStr=" + longitudeStr + ", latitudeStr=" + latitudeStr + ", chips: " + chips + ", formattedAddress: " + formattedAddress);

        UserInfo uInfo = Cache.getOnlineUserInfo(userId, roomId);
        try (Connection conn = DBUtil.getConnection()) {

            Room room = Cache.getRoom(roomId, roomPath);
            if (room == null || uInfo == null) {  //校验房间是否存在
                PublisherUtil.publisher(request, pusUser(1, creditValue, ""));
                return;
            }

            uInfo.setImei(IMEI);
            uInfo.setIsVirtual(virtual);
            //校验玩家是否存在
            RoomPersion reqPersion = room.getAudMap().get(userId);
            if (reqPersion == null) {
                PublisherUtil.publisher(request, pusUser(1, creditValue, ""));
                return;
            }

            //用户的总带入
            int bringIn = room.getDealer().getPlayers().get(userId).getBringIn();
            UserInfoDao userInfoDao = new UserInfoDaoImp();
            //俱乐部主id
            int clubUserId = userInfoDao.getClubByClubId(room.getClubId());
            logger.info("查询带入相关数据： clubUserId:{},userId:{},roomClubId:{},roomClubType:{}", clubUserId, userId, room.getClubId(), room.getClubRoomType());
            //判断是否同意带入或是否为俱乐部主
            RoomPlayer roomPlayer = room.getRoomPlayers().get(userId);
            if ((!room.getRequestToBrind().contains(userId) && room.isRequestToBringIn())
                    && room.getClubRoomType() == 1) {
                // 带入审核已停用
                logger.error("带入审核已停用");
                PublisherUtil.publisher(request, pusUser(1, creditValue, ""));
                return;
            } else if (!room.isRequestToBringIn() && room.getClubRoomType() == 1) {
                // 非审批模式，俱乐部牌局，不是at，直接进行积分判断
                //增加联盟币判断
                if(room.getTribeRoomType() == 1){
                    int tribeChip = userInfoDao.queryUserTribeChip(conn, room.getClubId(), userId);
                    if (tribeChip < chips) {
                        logger.debug("查询带入相关数据,联盟币不足, rid={},uid={} 获取到的联盟币为：{}，当前联盟币:{}", room.getRoomId(), userId, tribeChip, chips);
                        PublisherUtil.publisher(request, pusUser(22, creditValue, ""));
                        return;
                    } else {
                        RoomPersion roomPersion = room.getAudMap().get(userId);
                        if (roomPersion != null) {
                            logger.info("对用户：{},进行联盟币预设置，预扣为:{}", userId, chips);
                            //扣除联盟币
                            room.getRoomService().getRoomDao().updateTribeIntegral(room.getClubId(), userId, -chips);
                        }
                    }
                }else{ //俱乐部牌局
                    int integral = userInfoDao.queryUserIntegral(conn, room.getClubId(), userId);
                    if (integral == -1) { //FIXME club fund not handled
                        //会长，直接不处理
                        logger.info("数据为会长，不需要审批，userId:{},clubId:{},chips：{},integer:{}", userId, room.getClubId(), chips, integral);
                    } else if (integral < chips) {
                        // 说明不是会长，积分也不足
                        logger.debug("查询带入相关数据,积分不足，rid={},uid={}，获取到的积分为：{}，当前积分:{}", room.getRoomId(), userId, integral, chips);
                        // 积分不足
                        PublisherUtil.publisher(request, pusUser(21, creditValue, ""));
                        return;
                    } else {
                        // 说明积分足够，这里现在需要做： 直接预扣积分，记录积分数据
                        // 对玩家进行一个积分的设置
                        RoomPersion roomPersion = room.getAudMap().get(userId);
                        if (roomPersion != null) {
                            logger.info("对用户：{},进行积分预设置，预扣为:{}", userId, chips);
                            // 进行数据库扣除
                            room.getRoomService().getRoomDao().updateClubIntegral(room.getClubId(), userId, -chips);
                        }
                        logger.info("数据为积分足够，不需要审批，userId:{},clubId:{},chips：{},integer:{}", userId, room.getClubId(), chips, integral);
                    }
                }
            } else {
                logger.debug("查询带入相关数据,未进入if语句 rid={},uid={}", room.getRoomId(), userId);
            }

            boolean walletFrozen = false;
            try {
                walletFrozen = userInfoDao.checkUserAccountIfFozen(userId);
            } catch (SQLException e) {
                logger.error("checkUserIfFozen error",e);
                PublisherUtil.publisher(request, pusUser(1, creditValue, ""));
                return;
            }
            if(walletFrozen){ //判断玩家钱包是否被冻结
                logger.debug("can't bring in, user wallet is frozen rid={},uid={}",room.getRoomId(),userId);
                PublisherUtil.publisher(request, pusUser(18, creditValue, ""));
                return;
            }

            if (room.getChouma() > chips) {   //校验客户端传过来的积分是否小于最小带入的值
                logger.error("can't bring in, chips ({}) is less than min bring in chips ({}) rid={},uid={}", chips, room.getChouma(), room.getRoomId(), userId);
                PublisherUtil.publisher(request, pusUser(1, creditValue, ""));
                return;
            }

            if(ConfigUtil.getBringBlackUserIdList().contains(String.valueOf(userId)) &&
                    !ConfigUtil.getBringBlackIpList().contains(clientIp)){ //校验是否是带入黑名单用户且ip是否在配置的范围中
                logger.error("userId belong to blacklist,can't bring in");
                PublisherUtil.publisher(request, pusUser(15, creditValue, ""));
                return;
            }

            if (room.roomProcedure.forceCloseRoom) {  //解散中的牌局不允许带入
                PublisherUtil.publisher(request, pusUser(9, creditValue, ""));
                return;
            }
            RoomIpGps.updateUserIpGpsInfo(longitudeStr,latitudeStr,request.getChannel(),uInfo,clientIp); //更新玩家ip、gps信息

            if (room.isLimitGPS() && RoomIpGps.checkGPS(userId, uInfo,room)) {
                PublisherUtil.publisher(request, pusUser(10, creditValue, ""));
                return;
            }

            if (room.isLimitIp() && RoomIpGps.checkIp(userId, uInfo.getIp(),room)) {
                PublisherUtil.publisher(request, pusUser(6, creditValue, ""));
                return;
            }

            int bringInTriberId ; //玩家带入时所在的联盟id
            String bringInTribeName = ""; //玩家带入时所在的联盟名称
            int bringInClubId = selectClubId; //玩家带入时所在的俱乐部id
            String bringClubName = ""; //玩家带入时所在的俱乐部名称

            /**
             * 获取玩家的俱乐部和俱乐部所在同盟的信息
             * 只是带入当时的同盟信息,并不一定是最后结算的
             * 俱乐部处于被踢出和转移中时,玩家不允许在牌局内带入
             * 俱乐部或者联盟处于关闭状态时不允许带入
             */

            ClubTribeDao clubTribeDao = new ClubTribeDaoImpl();
            ClubTribeModel clubTribeModel = clubTribeDao.getClubTribeModelByUserId(userId);
            if (null != clubTribeModel) {

                int clubTribeStatus = clubTribeModel.getClubTribeStatus();
                int clubStatus = clubTribeModel.getClubStatus();
                int tribeStatus = clubTribeModel.getTribeStatus();

                bringInClubId = clubTribeModel.getClubId();
                bringInTriberId = clubTribeModel.getTribeId();
                bringInTribeName = clubTribeModel.getTribeName();
                bringClubName = clubTribeModel.getClubName();

                if(2 == clubTribeStatus || 3 == clubTribeStatus){ //处于被踢出中或者转移中的俱乐部不允许带入
                    logger.error("can't bring in,club is kick out,userId={},clubInfo={}",userId,clubTribeModel.toString());
                    PublisherUtil.publisher(request, pusUser(16, creditValue, ""));
                    return;
                }

                if(1 == clubStatus || 1 == tribeStatus){ //俱乐部、联盟处于关闭状态时不允许带入
                    logger.error("can't bring in,club or tribe is closed,userId={},clubInfo={}",userId,clubTribeModel.toString());
                    PublisherUtil.publisher(request, pusUser(17, creditValue, ""));
                    return;
                }
            }else{
                logger.error("can't bring in,club or user is not find,userId={},clubInfo={}",userId,clubTribeModel.toString());
                PublisherUtil.publisher(request, pusUser(1, creditValue, ""));
                return;
            }
            if (room.getClubRoomType()==0){
                // 带入操作之前进行审计
                IAuditService auditService = new AuditServiceImpl();
                auditService.auditOperation(userId, AuditOperationCode.BRING_IN_ROOM.getCode());
            }

            int res = RoomRequest.addChipRequest(userId, chips,room);  //发起带入
            logger.debug("addChipRequest result: " + res);
            if (res == 0) {
                Player player = room.getDealer().getPlayers().get(userId);
                /**
                 * 记录带入相关信息
                 */
                if(null != room.getFeeService()){
                    room.getFeeService().addBringClubAndTribe(bringInClubId,bringInTriberId,bringClubName); //增加到带入的俱乐部和同盟集合中
                }
                if(null != player){
                    player.setClubId(bringInClubId);
                    player.setClubName(clubTribeModel.getClubName());
                    player.setTribeId(bringInTriberId);
                    player.setBringInTimes(player.getBringInTimes() + 1);
                }
                RoomRequest.addApproveRecord(room, chips, bringInClubId,bringInTriberId,bringInTribeName, "",uInfo);
                RoomRequest.addRequestInfoRecord(userId,roomId,new UserRequetInfo(uInfo.getIp(),longitudeStr+","+latitudeStr,IMEI,MAC,virtual, formattedAddress));

                /**
                 * 记录redis相关信息
                 */
                RedisService redisService = RedisService.getRedisService();
                redisService.addClubBringRoomSet(bringInClubId,roomId);  //增加俱乐部中带入的房间集合
                redisService.addUserRoomApplySet(userId,room.getRoomPath(),room.getRoomId());//增加用户已经审批过的房间集合
                redisService.addLastRoomIdToUser(room.getRoomId(), userId);// 记录用户最后一次带入成功的房间


                if (roomPlayer == null) {
                    roomPlayer = new RoomPlayer(userId);
                }

                reqPersion.setPassHand(reqPersion.getPassHand() + 1);  //将该值设置加1 当大于1时客户端会显示过庄
                roomPlayer.setIsBringIn(1);   //设置为已带入过

                if(roomPlayer.getUserLevelCode() == null){  //设置玩家的名单等级
                    UserLevelDao userLevelDao = new UserLevelDaoImp();
                    UserLevel userLevel = userLevelDao.checkNameList(userId);
                    if(null != userLevel){
                        roomPlayer.setUserLevelCode(UserLevelCode.fromValue(userLevel.getLevelCode()));
                    }else{
                        roomPlayer.setUserLevelCode(UserLevelCode.NORMAL);
                    }
                }

                int roomPersionType = reqPersion.getType();
                int seatStatus = roomPlayer.getSeatStatus();
                logger.debug("user bring in chip,roomPerison type={},seatstatus={}",roomPersionType,seatStatus);
                if(roomPersionType == 1 && seatStatus != 3){ //站起状态且不是留座离桌状态一定不发坐下协议
                    return;
                }else{

                    if(reqPersion.getType() != 3){  //不在游戏中时设置为坐下状态
                        reqPersion.setType(4);
                    }

                    room.getDealer().removeWatcher(userId);// 坐下后得从观察者移除
                    room.removeLeaveUser(userId);

                    roomPlayer.incrSeatTimes();
                    if(roomPlayer.getDownTime() <= 0){ //第一次坐下时才设置坐下时间
                        roomPlayer.setDownTime(System.currentTimeMillis() / 1000);
                    }
                    roomPlayer.setSeat(reqPersion.getSize());
                    roomPlayer.setSeatSize(reqPersion.getSize()); // 被占座位编号
                    roomPlayer.setSeatStatus(1);  //坐下打牌状态
                    roomPlayer.setNickName(reqPersion.getUserInfo().getNikeName());
                    roomPlayer.setHeader(reqPersion.getUserInfo().getHead());
                    roomPlayer.setSex(reqPersion.getUserInfo().getSex());
                    roomPlayer.setChouma(reqPersion.getUserInfo().getChip());

                    room.getRoomPlayers().put(userId, roomPlayer);

                    long leftTime = RoomAutoOp.needAutoOp(room,userId);
                    if (leftTime > 0) {
                        reqPersion.setAutoOp(false);
                        Map<Integer, Object> map = new HashMap<Integer, Object>();
                        map.put(1, userId);
                        Task task2 = new Task(TaskConstant.TASK_MIN_PLAYTIME, map, room.getRoomId(), room.getRoomPath());
                        WorkThreadService.submitDelayTask(room.getRoomId(), task2, leftTime * 1000);// 坐下，设置最短打牌时间任务（开启最短打牌任务）
                        room.roomProcedure.delayTaskMap.put(task2.getId(), task2);

                        Map<String, String> hash = new HashMap<String, String>();
                        hash.put("roomId", String.valueOf(room.getRoomId()));
                        hash.put("name", room.getName());
                        RedisService.getRedisService().setAutoOp(userId, room.getRoomId(), hash); // 记录正在留盲代打房间信息
                    }

                    /**
                     * 不开控制带入的牌局，补充带入时，将该玩家加入到下一手牌局中
                     */
                    reqPersion.setPassHand(reqPersion.getPassHand() + 1);
                    reqPersion.setBetStatus(0);
                    room.getDdRoomPersions()[reqPersion.getSize()] = reqPersion;

                    /**
                     * 房间开始前,如果开启自动开局,需要校验是否自动开局
                     * 房间开始后，按照之前逻辑
                     **/
                    if(room.isAutoStartRoom() && room.getStatus() == 1){
                        Task task1 = new Task(TaskConstant.TASK_ROOMSTATUS1, null, room.getRoomId(), room.getRoomPath());
                        WorkThreadService.submit(room.getRoomId(), task1);
                    }else{
                        if(room.getRoomStatus() == 1){
                            Task task1 = new Task(TaskConstant.TASK_ROOMSTATUS1, null, room.getRoomId(), room.getRoomPath());
                            WorkThreadService.submit(room.getRoomId(), task1);
                        }
                    }

                    if (reqPersion.getLastRoomId() != 0 && reqPersion.getLastRoomId() != room.getRoomId()) {// 玩家还在其他房间打牌，直接踢掉
                        Room room2 = Cache.getRoom(reqPersion.getLastRoomId(), reqPersion.getLastRoomPath());
                        if (room2 != null) {
                            room2.getRoomService().userTimeOut2(reqPersion.getUserInfo(), 2);
                        }
                    }

                    // 是否需要选择过庄或者补盲
                    int playTypeChoose = 1;
                    if (room.getStage() == 0 || reqPersion.isCanPlay() || RoomUtil.getCanPlayNum(room) < 2 || reqPersion.getPlayType() != 0) {
                        playTypeChoose = 0;
                    }

                    //第一次坐下不需要补盲
                    if(reqPersion.getNowcounma() == 0){
                        playTypeChoose = 0;
                    }

                    logger.debug("p.isCanPlay()=" + reqPersion.isCanPlay() + ", p.getPlayType()=" + reqPersion.getPlayType());
                    logger.debug("playTypeChoose: " + playTypeChoose + ", stage: " + room.getStage());

                    Object[][] objs2 = {
                            {60, 0, I366ClientPickUtil.TYPE_INT_1},
                            {61, 1, I366ClientPickUtil.TYPE_INT_1},
                            {130, reqPersion.getNowcounma(), I366ClientPickUtil.TYPE_INT_4},
                            {131, reqPersion.getChouma(), I366ClientPickUtil.TYPE_INT_4},
                            {132, reqPersion.getSize(), I366ClientPickUtil.TYPE_INT_1},
                            {133, playTypeChoose, I366ClientPickUtil.TYPE_INT_4},		// 是否需要弹过庄补盲选择框 0不需要 1需要
                    };
                    byte[] bytes = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
                    PublisherUtil.publisher(reqPersion.getUserInfo(), bytes);
                    if (room.getRequestToBrind().contains(userId)){
                        room.getRequestToBrind().remove((Integer) userId);
                    }
                    return;
                }

            } else if (1 == res) { // 金豆不足
                PublisherUtil.publisher(request, pusUser(23, creditValue, ""));
                return;
            }else if (2 == res) { // 到达最大带入倍数
                PublisherUtil.publisher(request, pusUser(3, creditValue, ""));
                return;
            }

        } catch (Exception e) {
            logger.error("addchips error", e);
            PublisherUtil.publisher(request, pusUser(1, creditValue, ""));
            return;
        }

    }

    private byte[] pusUser(int status, int creditValue, String tirbeClub) {
        logger.debug("status: " + status);
        Object[][] objs2 = {
                // 0成功 1失败 2等待房主确认 3加筹码失败，但是筹码满足坐下条件 4房主拒绝加筹码请求 5房主不在房间直接拒绝 6相同ip
                // 7信用额度不足 8有结算请求未被处理 9房间解散中 10 GPS限制 11 占座玩家申请带入，头像下方进行倒计时 12 社区在同盟的积分不足
                // 15带入黑名单 16 玩家所在俱乐部被联盟踢出 17俱乐部、联盟处于关闭状态时不允许带入 18玩家被冻结,不允许带入
                // 19等待俱乐部主的确认20 俱乐部主没在线 21 俱乐部币不足 22 联盟币不足 23 金豆不足
                { 60, 0, I366ClientPickUtil.TYPE_INT_1 },
                { 61, status, I366ClientPickUtil.TYPE_INT_1 },
                { 133, 0, I366ClientPickUtil.TYPE_INT_4 }, // 请求待确认倒计时(秒)
                { 134, creditValue, I366ClientPickUtil.TYPE_INT_4 },
                { 135, tirbeClub, I366ClientPickUtil.TYPE_STRING_UTF16 } // 社区积分不足，客户端显示xx社区在xx同盟积分不足
        };
        byte[] bytes = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_RECV_ADD_CHIPS);
        return bytes;
    }



}
