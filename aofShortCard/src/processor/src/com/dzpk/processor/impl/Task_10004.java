package com.dzpk.processor.impl;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.cache.Cache;
import com.i366.constant.Constant;
import com.i366.model.room.Room;
import com.i366.util.PublisherUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import org.apache.logging.log4j.Logger;

/**
 * 上一手收尾和下手开始
 * <AUTHOR>
 *
 */
public class Task_10004 implements IProcessor {

    private Logger logger = LogUtil.getLogger(Task_10004.class);
    
    @Override
    public void handle(Task task) {
        if (!task.isValid()) {
            logger.debug("invalid message");
            return;
        }
        
        Room room = Cache.getRoom(task.getRoomId(), task.getRoomPath());
        if (room != null) {
            // 先发送通知给客户端清空牌桌
            Object[][] objs = {
                    {60, 1, I366ClientPickUtil.TYPE_INT_1},
            };
            byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_NOTIFY_CLEAR_TABLE);
            PublisherUtil.send(room,bytes);
            
            room.roomProcedure.nextGame();
            room.roomProcedure.delayTaskMap.remove(task.getId());
        }
    }

}