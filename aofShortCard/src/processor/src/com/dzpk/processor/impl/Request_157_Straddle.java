
package com.dzpk.processor.impl;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.cache.Cache;
import com.i366.constant.Constant;
import com.i366.model.room.Room;
import com.i366.util.PublisherUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.Request;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 * @version 1.0
 */
public class Request_157_Straddle implements IProcessor {

    private Logger logger = LogUtil.getLogger(Request_157_Straddle.class);

    @Override
    public void handle(Task task) {
        int roomId = task.getRoomId();
        int roomPath = task.getRoomPath();
        Request request = (Request) task.getRequest();
        
        int userId = request.getUserId();
        int type = (Integer) task.getMap().get(132);
        long chooseTime = (Long) task.getMap().get(133);
        logger.debug("roomId: " + roomId + ", roomPath: " + roomPath + ", type: " + type);
        
        Room room = Cache.getRoom(roomId, roomPath);
        if (room != null) {
            if (room.getStraddlePlayers().containsKey(userId)) {
                int straddleChip = room.getStraddlePlayers().get(userId);
                if (type == 0) {
                    room.getAudMap().get(userId).setStraddleChip(straddleChip);
                } else {
                    room.getAudMap().get(userId).setStraddleChip(0);
                }
                PublisherUtil.publisher(request, pusUser(0));
                room.getStraddlePlayers().remove(userId);
                if ((straddleChip == room.getQianzhu() * 2 && type == 2 || room.getStraddlePlayers().size() == 0)
                        && !room.isStraddleFin()) {
                    logger.debug("straddle end");
                    // 5s straddle选择时间结束
                    room.getRoomService().beginNewHand(true);
                }
            } else {
                logger.debug("1");
                PublisherUtil.publisher(request, pusUser(1));
            }
        } else {
            logger.debug("room null");
            PublisherUtil.publisher(request, pusUser(1));
        }
    }
    
    private byte[] pusUser(int status) {
        logger.debug("status: " + status);
        Object[][] objs2 = {
                {60, status, I366ClientPickUtil.TYPE_INT_1},        // 返回状态 0成功 1失败
        };
        byte[] bytes = I366ClientPickUtil.packAll(objs2, Constant.REQ_REQUEST_STRADDLE);
        return bytes;
    }
    
}
