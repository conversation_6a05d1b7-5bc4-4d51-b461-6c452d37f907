package com.dzpk.processor.impl;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.repositories.redis.RedisService;
import com.dzpk.db.model.UserInfo;
import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.cache.Cache;
import com.i366.constant.Constant;
import com.i366.constant.LeaveRoomCode;
import com.i366.constant.RemoveUserCode;
import com.i366.constant.StandUpRoomCode;
import com.i366.model.player.RoomPersion;
import com.i366.model.player.RoomPlayer;
import com.i366.model.room.Room;
import com.i366.room.RoomAutoOp;
import com.i366.room.RoomIpGps;
import com.i366.util.PermissionUtil;
import com.i366.util.PublisherUtil;
import com.i366.util.RoomUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.Request;
import org.apache.logging.log4j.Logger;


public class Request_45_Seta implements IProcessor {

    private final Logger logger = LogUtil.getLogger(Request_45_Seta.class);

    public void handle(Task task) {
        long l = System.currentTimeMillis();
        Request request = (Request) task.getRequest();

        int action = (Integer) task.getMap().get(60);
        int seatId = (Integer) task.getMap().get(61);
        if (seatId == 255) {
            seatId = -1;
        }
        String longitudeStr = task.getMap().get(62) == null ? "" : (String) task.getMap().get(62);
        String latitudeStr  = task.getMap().get(63) == null ? "" : (String) task.getMap().get(63);
        String clientIp  = task.getMap().get(64) == null ? "" : (String) task.getMap().get(64);
        int userId = request.getUserId();
        int roomId = task.getRoomId();
        int roomPath = task.getRoomPath();

        UserInfo userInfo = Cache.getOnlineUserInfo(userId, roomId);
        
        Room room = Cache.getRoom(roomId, roomPath);

        if (room == null) {
            Object[][] objs2 = {
                    {60, 1, I366ClientPickUtil.TYPE_INT_1}
            };

            byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
            PublisherUtil.publisher(request, bytes2);
            return;
        }

        if (userInfo == null) {
            Object[][] objs2 = {
                    {60, 1, I366ClientPickUtil.TYPE_INT_1}
            };

            byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
            PublisherUtil.publisher(request, bytes2);
            return;
        }

        RoomPlayer roomPlayer = room.getRoomPlayers().get(userId);
        if (roomPlayer == null) {
            Object[][] objs2 = {
                    {60, 1, I366ClientPickUtil.TYPE_INT_1}
            };

            byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
            PublisherUtil.publisher(request, bytes2);
            return;
        }

        RoomIpGps.updateUserIpGpsInfo(longitudeStr,latitudeStr,request.getChannel(),userInfo,clientIp); //更新玩家ip、gps信息

        if(action == 1){
            if (room.isLimitGPS() && RoomIpGps.checkSeatGPS(userId, userInfo,room)) {// 开启gps限制
                Object[][] objs2 = {
                        {60, 7, I366ClientPickUtil.TYPE_INT_1}
                };

                byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
                PublisherUtil.publisher(request, bytes2);
                return;
            }

            if (room.isLimitIp() && RoomIpGps.checkSeatIp(userId, userInfo.getIp(),room)) {// 开启ip限制
                Object[][] objs2 = {
                        {60, 2, I366ClientPickUtil.TYPE_INT_1}
                };

                byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
                PublisherUtil.publisher(request, bytes2);
                return;
            }
        }

        switch (action) {
            case 1: //坐下房间
                logger.debug("seat down room,roomid={},userid={},seatid={}",roomId,userId,seatId);
                if (seatId < 0 || seatId >= Constant.ROOM_MAX_PLAYER) {    // 座位号不符合要求
                    logger.error("seat down room error,seat size is illgeal,roomid={},userid={},seatid={}",roomId,userId,seatId);
                    PublisherUtil.publisher(request, pusUser(1, null, action));
                    return;
                }

                if(1 == roomPlayer.getAheadLeave()){
                    logger.error("seat down room error,seat size is illgeal,roomid={},userid={},aheadLeave={}",roomId,userId,roomPlayer.getAheadLeave());
                    PublisherUtil.publisher(request, pusUser(12, null, action));
                    return;
                }

                int seatStatus = room.getRoomService().downRoom(userId, seatId, userInfo); //坐下房间逻辑
                if (seatStatus > -1) {
                    logger.error("seat down room error,roomid={},userid={},seatStatus={}",roomId,userId,seatStatus);
                    PublisherUtil.publisher(request, pusUser(seatStatus, null, action));
                    return;
                }

                RoomPersion p = room.getDdRoomPersions()[seatId];
                if(p == null || p.getOnlinerType() == -1){
                    p = room.getAudMap().get(userId);
                }

                if(p.getNowcounma() < room.getChouma()){  //积分不足一个带入设置为占座状态
                    room.getRoomService().cancelOccupySeat(userId, 4);
                }else{
                    room.getRoomService().cancelOccupySeat(userId, 1);
                }
                //积分房的带入逻辑
                long requestToBringInTimes = room.getRoomPlayers().get(userId).getRequestToBringInTimes();
                int time = (int) ((requestToBringInTimes - System.currentTimeMillis()) / 1000);

                if (room.getClubRoomType()!=0&& room.isRequestToBringIn() && requestToBringInTimes != 0 &&
                        0 == room.getRoomService().newOccupySeatTask(userId, time*1000) &&
                        System.currentTimeMillis() < requestToBringInTimes){
                    // 通知别的玩家有人占座了
                    Object[][] objs3 = {
                            {60, 0, I366ClientPickUtil.TYPE_INT_1},// 0成功 1 失败 2未到最短时间 3下一手生效
                            {130, userId, I366ClientPickUtil.TYPE_INT_4},   // 玩家id
                            {132, p.getSize(), I366ClientPickUtil.TYPE_INT_4},     // 座位号
                            {133, time , I366ClientPickUtil.TYPE_INT_4}, // 剩余倒计时
                    };
                    PublisherUtil.send(room,I366ClientPickUtil.packAll(objs3, Constant.REQ_WAITE_SEAT));
                }
                logger.debug(".....坐下用去时间: " + (System.currentTimeMillis() - l));
                return;
            case 2: //站起房间

                if (seatId < 0 || seatId >= room.getPlayerCount()) { //校验座位号
                    PublisherUtil.publisher(request, pusUser(1, null, action));
                    return;
                }

                RoomPersion rp = room.getRoomPersions()[seatId];
                if (rp == null || rp.getOnlinerType() == -1) {
                    rp = room.getDdRoomPersions()[seatId];
                }
                if (null == rp) {
                        // 判断该玩家是否在留座离桌状态

                        /***
                         * 防止客户端下发过来的站起座位号不是该userid的座位号
                         * 比如4号位置的用户发送站起操作时，seatID传过来是5号位置
                         */

                        RoomPersion sitRoomPersion = room.getAudMap().get(userId);
                        if(sitRoomPersion != null && (sitRoomPersion.getType() == 3 || sitRoomPersion.getType() == 4)){ //考虑正在游戏中获取过庄补盲状态的情况
                            logger.error("the stand up user " + userId + " is not on seat " + seatId + " real seat: " + sitRoomPersion.getSize());
                            PublisherUtil.publisher(request, pusUser(1, rp, action));
                            return;
                        }

                        logger.debug("the stand up user " + userId + " is occuping seat " + roomPlayer.getSeatSize());
                        room.getRoomService().cancelOccupySeat(userId, 0);
                        room.getRoomService().notifyOccupyUserStand(2, userId);
                        return;

                }

                if (RoomUtil.isOnlyPlayingUser(userId,room)) { // 玩家为唯一再玩玩家，该次站起请求失败
                    logger.error("stand up room error,the only player,roomid={},userid={}",roomId,userId);
                    PublisherUtil.publisher(request, pusUser(1, rp, action));
                    return;
                }

                if(room.getMinPlayTime() > 0){ //开启最短上桌时间的房间,游戏中未到最短打牌时间不让站起
                    long userLeftPlayTime = RoomAutoOp.needAutoOp(room,userId);
                    if(userLeftPlayTime > 0){
                        logger.error("stand up room error,player not play min playtime,roomid={},userid={},userLeftPlayTime={}",roomId,userId,userLeftPlayTime);
                        PublisherUtil.publisher(request, pusUser(1, rp, action));
                        return;
                    }
                }

                if (3 == rp.getType() && -3 != rp.getStatus()) { // 正在游戏中且未有操作行为时,不让站起
                    rp.setNextGameStandup(1);
                    rp.setStandupType(0); // 主动站起
                    PublisherUtil.publisher(request, pusUser(5, rp, action));
                    return;
                }

                rp.setStandupType(0); // 主动站起
                room.getRoomService().zhanqi(rp, StandUpRoomCode.USER_SELF_STANDUP); //调用站起逻辑

                logger.debug(".....站起用去时间: " + (System.currentTimeMillis() - l));
                return;
                
            case 3: //离开房间
                RoomPersion persion = null;

                /** 重新确认玩家位置，防止客户端发送错误玩家座位id **/
                for (int i = 0; i < room.getRoomPersions().length; i++) {
                    if (room.getRoomPersions()[i] != null
                            && room.getRoomPersions()[i].getUserId() == request.getUserId()
                            && room.getRoomPersions()[i].getOnlinerType() != -1) {
                        seatId = i;
                        break;
                    } else if (room.getDdRoomPersions()[i] != null
                            && room.getDdRoomPersions()[i].getUserId() == request.getUserId()) {
                        seatId = i;
                        break;
                    }
                }

                if (seatId > -1 && seatId < Constant.ROOM_MAX_PLAYER) {
                    persion = room.getRoomPersions()[seatId];
                    if (persion == null || persion.getOnlinerType() == -1) {
                        persion = room.getDdRoomPersions()[seatId];
                    }
                }

                logger.debug("after correct seat,seatid={}",seatId);

                if (persion == null) { //玩家还未坐下座位,点击离开房间
                    byte[] bytes = pusUser(0, room.getRoomService().noDownLeave(userId), action);
                    PublisherUtil.publisher(request, bytes);
                    return;
                }

                if (RoomUtil.isOnlyPlayingUser(userId,room)) { // 玩家为唯一再玩玩家，该次离开请求失败
                    logger.error("leave room error,the only player,roomid={},userid={}",roomId,userId);
                    PublisherUtil.publisher(request, pusUser(1, persion, action));
                    return;
                }

                /**
                 * 离开逻辑
                 * 开启最短上桌时间的牌局
                 *      未到达最短上桌时间时,如果玩家未托管,让玩家设置托管;如果玩家已经托管，直接返回离开房间成功
                 *      已经达到最短上桌时间时,直接返回离开房间成功,并且设置玩家下一手离开房间
                 * 未开启最短上桌时间的牌局
                 *      玩家在游戏中  直接返回离开房间成功,并且设置玩家下一手离开房间
                 *      玩家未在游戏中  玩家离开房间
                 */
                if(room.getMinPlayTime() > 0){

                    long userLeftPlayTime = RoomAutoOp.needAutoOp(room,userId);
                    if (userLeftPlayTime > 0) {
                        logger.debug("leave room,player not play min playtime,roomid={},userid={},userLeftPlayTime={}",roomId,userId,userLeftPlayTime);
                    }else{
                        roomPlayer.setHasLeft(true);
                        roomPlayer.setSeat(seatId);
                    }

                    Object[][] objs = {  // 通知本人离开成功
                            {60, 0, I366ClientPickUtil.TYPE_INT_1},
                            {61, 3, I366ClientPickUtil.TYPE_INT_1},
                            {130, persion.getNowcounma(), I366ClientPickUtil.TYPE_INT_4},
                            {131, persion.getChouma(), I366ClientPickUtil.TYPE_INT_4}
                    };
                    byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_SEND_SEAT_ACTION);
                    PublisherUtil.publisher(persion.getUserInfo(), bytes);
                    return;
                }else{
                    if (3 == persion.getType()) {
                        logger.debug("leave room,player is playing game now,roomid={},userid={},roomPersion type={}",roomId,userId,persion.getType());

                        roomPlayer.setHasLeft(true);
                        roomPlayer.setSeat(seatId);

                        Object[][] objs = {  // 通知本人离开成功
                                {60, 0, I366ClientPickUtil.TYPE_INT_1},
                                {61, 3, I366ClientPickUtil.TYPE_INT_1},
                                {130, persion.getNowcounma(), I366ClientPickUtil.TYPE_INT_4},
                                {131, persion.getChouma(), I366ClientPickUtil.TYPE_INT_4}
                        };
                        byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_SEND_SEAT_ACTION);
                        PublisherUtil.publisher(persion.getUserInfo(), bytes);

                        return;
                    }
                }

                room.getRoomService().likai(persion, LeaveRoomCode.USER_SELF_LEAVE); //调用离开逻辑

                logger.debug("....离开用去时间: " + (System.currentTimeMillis() - l));
                return;
            case 4: //强制站起

                if (seatId < 0 || seatId >= room.getPlayerCount()) { //校验座位号
                    PublisherUtil.publisher(request, pusUser(1, null, action));
                    return;
                }

                RoomPersion rpStand = room.getRoomPersions()[seatId];
                if (rpStand == null || rpStand.getOnlinerType() == -1) {
                    rpStand = room.getDdRoomPersions()[seatId];
                }

                if(null == rpStand){  // 判断该玩家是否在留座离桌/占座状态
                    for (Integer key : room.getRoomPlayers().keySet()) {
                        RoomPlayer player = room.getRoomPlayers().get(key);
                        if(player.getSeat() == seatId){
                            if (null != player) {
                                logger.debug("the zhanqi user is occuping seat,userid={},seat={}",player.getUserId(),player.getSeat());
                                room.getRoomService().cancelOccupySeat(player.getUserId(), 0);
                                room.getRoomService().notifyOccupyUserStand(4,player.getUserId());
                                return;
                            }
                        }
                    }

                }else{
                    int permissionZhanqi = PermissionUtil.havePermission(userId,rpStand.getUserId(),room);  //查询申请踢出的玩家对被踢玩家是否有权限
                    int status = permissionZhanqi == 1 ? 10 : 9;  //9没有权限  10有权限

                    Object[][] objs = {
                            {60, status, I366ClientPickUtil.TYPE_INT_1},
                            {61, action, I366ClientPickUtil.TYPE_INT_1}
                    };
                    byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_SEND_SEAT_ACTION);
                    if(status == 9){

                        logger.error("zhanqi error,not have permission,userid={},outuserid={},seat={},roomid={}",userId,rpStand.getUserId(),seatId,roomId);
                        PublisherUtil.publisher(request, bytes);
                        return;
                    }else{

                        if(rpStand.isKickOut() || rpStand.isStandUp()){ //如果该玩家已经被站起或者踢出牌局，则直接返回
                            PublisherUtil.publisher(request, bytes);
                            return;
                        }

                        rpStand.setStandupType(1);
                        rpStand.setStandUp(true);

                        if(rpStand.getType() != 3){
                            //占座状态马上站起
                            logger.debug("zhanqi user not playing game,zhanqi now,userid={},outuserid={},seat={}" +
                                    ",roomid={},roomPersion type={}",userId,rpStand.getUserId(),seatId,roomId,rpStand.getType());
                            room.getRoomService().zhanqi2(rpStand,StandUpRoomCode.FORCE_STANDUP);
                        }

                        logger.debug("zhanqi user successfully,userid={},outuserid={},seat={},roomid={}",userId,rpStand.getUserId(),seatId,roomId);
                        PublisherUtil.publisher(request, bytes);
                        return;
                    }

                }

            case 5: //强制踢出
                if (seatId < 0 || seatId >= room.getPlayerCount()) { //校验座位号
                    PublisherUtil.publisher(request, pusUser(1, null, action));
                    return;
                }

                RoomPersion rpOut = room.getRoomPersions()[seatId];
                if (rpOut == null || rpOut.getOnlinerType() == -1) {
                    rpOut = room.getDdRoomPersions()[seatId];
                }

                if(null == rpOut){  // 判断该玩家是否在留座离桌/占座状态
                    for (Integer key : room.getRoomPlayers().keySet()) {
                        RoomPlayer player = room.getRoomPlayers().get(key);
                        if(player.getSeat() == seatId){
                            if (null != player) {
                                logger.debug("the kickout user is occuping seat,userid={},seat={}",player.getUserId(),player.getSeat());
                                room.getRoomService().cancelOccupySeat(player.getUserId(), 0);
                                room.getRoomService().notifyOccupyUserStand(5,player.getUserId());

                                RedisService.getRedisService().addKickOutUser(player.getUserId(),roomId);   //将用户加入到该房间被踢的集合中，在进入房间时判断
                                return;
                            }
                        }
                    }
                }else{
                    int permissionKickOut = PermissionUtil.havePermission(userId,rpOut.getUserId(),room); //查询申请踢出的玩家对被踢玩家是否有权限
                    int status2 = permissionKickOut == 1 ? 10 : 9; //9没有权限  10有权限

                    Object[][] objs2 = {
                            {60, status2, I366ClientPickUtil.TYPE_INT_1},
                            {61, action, I366ClientPickUtil.TYPE_INT_1}
                    };

                    byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
                    if(status2 == 9){   //没有权限 直接返回

                        logger.error("kickout error,not have permission,userid={},outuserid={},seat={},roomid={}",userId,rpOut.getUserId(),seatId,room.getRoomId());
                        PublisherUtil.publisher(request, bytes2);
                        return;
                    }else{

                        if(rpOut.isKickOut()){ //如果该玩家已经被踢出牌局，则直接返回
                            PublisherUtil.publisher(request, bytes2);
                            return;
                        }

                        if(rpOut.isStandUp()){ //如果该玩家之前被执行了站起操作，则按照踢出处理
                            rpOut.setStandUp(false);
                        }

                        rpOut.setStandupType(1);
                        rpOut.setKickOut(true);

                        if(rpOut.getType() != 3){
                            logger.debug("kickout user not playing game,kickout now,userid={},outuserid={},seat={}" +
                                    ",roomid={},roomPersion type={}",userId,rpOut.getUserId(),seatId,roomId,rpOut.getType());
                            room.getRoomService().zhanqi2(rpOut,StandUpRoomCode.KICK_OUT_STANDUP);
                            room.delRoomAud(rpOut.getUserId(), RemoveUserCode.KICKOUT_NOT_PLAYING); //从房间中删除该玩家
                        }

                        RedisService.getRedisService().addKickOutUser(rpOut.getUserId(),roomId);   //将用户加入到该房间被踢的集合中，在进入房间时判断

                        logger.debug("kickout user successfully,userid={},outuserid={},seat={},roomid={}",userId,rpOut.getUserId(),seatId,roomId);
                        PublisherUtil.publisher(request, bytes2);
                    }

                    return;
                }
            case 6: //提前离桌
                if(room.getAheadLeaveMode() == 0){//非提前离桌模式不走代码
                    logger.error("ahead leave error,room is not aheadLeaveMode,userid={},roomid={}",userId,roomId);
                    Object[][] objs2 = {
                            {60, 1, I366ClientPickUtil.TYPE_INT_1},
                            {61, action, I366ClientPickUtil.TYPE_INT_1}
                    };

                    byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
                    PublisherUtil.publisher(request, bytes2);
                    return;
                }

                if(1 == roomPlayer.getAheadLeave()){  //如果该玩家已经点击了提前离桌，则直接返回
                    logger.error("ahead leave error,user have click aheadLeave button,userid={},roomid={},aheadLeave={}",userId,roomId,roomPlayer.getAheadLeave());
                    Object[][] objs2 = {
                            {60, 1, I366ClientPickUtil.TYPE_INT_1},
                            {61, action, I366ClientPickUtil.TYPE_INT_1}
                    };

                    byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
                    PublisherUtil.publisher(request, bytes2);
                    return;
                }

                if(room.getMinPlayTime() > 0){ //游戏中未到最短打牌时间不让提前离桌

                    long userLeftPlayTime = RoomAutoOp.needAutoOp(room,userId);
                    if (userLeftPlayTime > 0) {
                        logger.debug("ahead leave error,player not play min playtime,roomid={},userid={},userLeftPlayTime={}",roomId,userId,userLeftPlayTime);
                        Object[][] objs2 = {
                                {60, 1, I366ClientPickUtil.TYPE_INT_1},
                                {61, action, I366ClientPickUtil.TYPE_INT_1}
                        };

                        byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
                        PublisherUtil.publisher(request, bytes2);
                        return;
                    }
                }

                roomPlayer.setAheadLeave(1);  //设置成提前离桌即可

                /**
                 * 玩家站起状态点击提前离桌
                 * 玩家在游戏中点击提前离桌
                 * 玩家处于留座离桌状态点击提前离桌
                 */
                if( -1 == seatId){
                    logger.debug("ahead leave,user is stand up now,userid={},seat={},roomid={}",userId,roomPlayer.getSeatSize(),roomId);
                }else{

                    RoomPersion aheadLeavePersion = room.getRoomPersions()[seatId];
                    if (aheadLeavePersion == null || aheadLeavePersion.getOnlinerType() == -1) {
                        aheadLeavePersion = room.getDdRoomPersions()[seatId];
                    }

                    if(null == aheadLeavePersion){   //留座离桌状态
                        logger.debug("ahead leave,user is occuping seat,userid={},seat={},roomid={}",userId,roomPlayer.getSeatSize(),roomId);
                        room.getRoomService().cancelOccupySeat(userId, 0);
                        room.getRoomService().notifyOccupyUserStand(2, userId);
                    }else{
                        aheadLeavePersion.setStandupType(0);
                        if(aheadLeavePersion.getType() != 3){  // 不在游戏中的时候直接站起
                            logger.debug("ahead leave,user is not playing game,stand up now,userid={},seat={},roomid={},roomPersion type={}",userId,roomPlayer.getSeatSize(),roomId,aheadLeavePersion.getType());
                            room.getRoomService().zhanqi2(aheadLeavePersion,StandUpRoomCode.AHEAD_LEAVE_STANDUP);
                        }
                    }

                }

                /**
                 *  1.房间状态为0，牌局还没有开始。玩家带入了.此时提前离桌
                 *  2.房间状态为1，下一手发牌开始之前，此时还没有扣取大小盲。提前离桌
                 */
                if(room.getRoomStatus() <= 1 ){
                    if(null != room.getFeeService()){
                        room.getFeeService().saveAheadCommission(1);
                    }
                }

                Object[][] objs2 = {
                        {60, 11, I366ClientPickUtil.TYPE_INT_1},
                        {61, action, I366ClientPickUtil.TYPE_INT_1}
                };
                byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);

                logger.debug("ahead leave successfully,userId={},roomid={},seat={}",userId,roomId,seatId);
                PublisherUtil.publisher(request, bytes2);
                return;
            case 7: //冻结玩家
                RoomPersion roomPersion = room.getAudMap().get(userId);
                if(null != roomPersion){
                    int seat = roomPersion.getSize();
                    logger.debug("forbbiden user,userId={},roomid={},seat={},iskickout={}",userId,roomId,seat,roomPersion.isKickOut());
                    if(seat <= -1){  //站起状态
                        room.getRoomService().likai(roomPersion,LeaveRoomCode.FOBBIDEN_LEAVE);
                    }else{   //在座位上
                        int roomStatus = room.getRoomStatus();
                        if(roomStatus <= 1){ //房间未在游戏中玩家在座位上
                            room.getRoomService().likai(roomPersion,LeaveRoomCode.FOBBIDEN_LEAVE);
                        }else{
                            if(!roomPersion.isKickOut()){ //此时未被踢出过
                                if(roomPersion.isStandUp()){    //如果该玩家之前被执行了站起操作，则按照踢出处理
                                    roomPersion.setStandUp(false);
                                }

                                roomPersion.setStandupType(2);
                                roomPersion.setKickOut(true);
                            }

                        }
                    }
                    logger.debug("forbbiden user successfully,userId={},roomid={},seat={}",userId,roomId,seatId);
                }else{
                    logger.error("forbbiden user error,user not in room,userId={},roomid={},seat={}",userId,roomId,seatId);
                }

                Object[][] objs = {
                        {60, 13, I366ClientPickUtil.TYPE_INT_1},  //您的账号已经被冻结
                        {61, action, I366ClientPickUtil.TYPE_INT_1}
                };
                byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_SEND_SEAT_ACTION);
                PublisherUtil.publisher(request, bytes);

                return;
            default:
                return;
        }
    }

    private byte[] pusUser(int status, RoomPersion roomPersion, int action) {
        byte[] bytes;
        if (roomPersion == null) {
            Object[][] objs = {
                    {60, status, I366ClientPickUtil.TYPE_INT_1},
                    {61, action, I366ClientPickUtil.TYPE_INT_1}
            };
            bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_SEND_SEAT_ACTION);
        } else {
            Object[][] objs = {
                    {60, status, I366ClientPickUtil.TYPE_INT_1},
                    {61, action, I366ClientPickUtil.TYPE_INT_1},
                    {130, roomPersion.getNowcounma(), I366ClientPickUtil.TYPE_INT_4},
                    {131, roomPersion.getChouma(), I366ClientPickUtil.TYPE_INT_4}
            };
            bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_SEND_SEAT_ACTION);
        }
        return bytes;
    }
}
