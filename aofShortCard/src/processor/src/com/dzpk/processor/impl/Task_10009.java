package com.dzpk.processor.impl;

import com.dzpk.common.utils.LogUtil;
import org.apache.logging.log4j.Logger;

import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.cache.Cache;
import com.i366.model.room.Room;

/**
 * 保险结账
 * <AUTHOR>
 *
 */
public class Task_10009 implements IProcessor {

    private Logger logger = LogUtil.getLogger(Task_10009.class);
    
    @Override
    public void handle(Task task) {
        if (!task.isValid()) {
            logger.debug("invalid message");
            return;
        }
        
        Room room = Cache.getRoom(task.getRoomId(), task.getRoomPath());
        logger.debug("roomId: " + task.getRoomId() + ", roomPath: " + task.getRoomPath());
        // 进入保险状态
        if (room != null) {

            int bipaiIndex = (int) task.getMap().get(1);
            int cardIndex = (int) task.getMap().get(2);
            long time1 = System.currentTimeMillis();
            logger.debug("Task_10009 begin execute roomId: " + task.getRoomId() + ", time1: " + time1);
            room.getInsurer().checkout(room, bipaiIndex, cardIndex);
            long time2 = System.currentTimeMillis();
            logger.debug("Task_10009 end execute roomId: " + task.getRoomId() + ", time2: " + time2);
            logger.debug("Task_10009 execute time " + (time2 - time1));
            room.roomProcedure.delayTaskMap.remove(task.getId());
        }
    }

}
