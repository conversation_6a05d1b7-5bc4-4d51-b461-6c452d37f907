package com.dzpk.component.zk;

import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.apache.curator.retry.RetryForever;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.FileInputStream;
import java.net.URL;
import java.net.URLDecoder;
import java.util.Properties;

public final class ZkUtil {
    /** 日志组件 */
    private static final Logger logger = LogManager.getLogger(ZkUtil.class);

    /**
     * Zookeeper服务器的地址列表
     * 格式: ip(hostname):port[,ip(hostname):port]*
     * 必须提供
     */
    private static String connectString="*************:2181,*************:3181,*************:4181";

    /**
     * 可选
     * 必须大于0，否则忽略此设置
     * 默认值：60 秒
     */
    private static Integer sessionTimeoutMs;
    /**
     * 可选
     * 必须大于0，否则忽略此设置
     * 默认值：15 秒
     */
    private static Integer connectionTimeoutMs;
    /**
     * 可选
     * 必须大于0，否则忽略此设置
     * 默认值：1 秒
     */
    private static Integer maxCloseWaitMs;
    /**
     * 创建CuratorTempFramework时使用
     * 可选
     * 必须大于0，否则忽略此设置
     * 默认值：3 分
     */
    private static Integer inactiveThresholdMs;

    /**
     * 设置当前这个Zookeeper访问的命名空间
     * 如果设置了，通过此实例访问的路径都将自动附加
     * 上此设置作为路径的前缀。
     * null或mepty，忽略此参数
     */
    private static String namespace;

    /**
     */
    private static Boolean canBeReadOnly;
    /**
     */
    private static Boolean useContainerParentsIfAvailable;

    private static CuratorFramework client;
    public static CuratorFramework getClient() {
        return client;
    }

    static{
        URL url = ZkUtil.class.getProtectionDomain().getCodeSource().getLocation();
        String fullPath = "";
        try {
            String filePath = URLDecoder.decode(url.getPath(), "utf-8");// 转化为utf-8编码，支持中文
            if (filePath.endsWith(".jar")) {// 可执行jar包运行的结果里包含".jar"
                // 获取jar包所在目录
                filePath = filePath.substring(0, filePath.lastIndexOf("/") + 1);
            }

            fullPath = filePath + "/zk-config.properties";
            Properties config = new Properties();
            config.load(new FileInputStream(fullPath));

            String propVal = config.getProperty("zk.connectString");
            if(null != propVal && !"".equals(propVal.trim()))
                connectString = propVal.trim();
            propVal = config.getProperty("zk.namespace");
            if(null != propVal && !"".equals(propVal.trim()))
                namespace = propVal.trim();

            propVal = config.getProperty("zk.sessionTimeoutMs");
            Integer value = tryParse(propVal,"zk.sessionTimeoutMs");
            if(null != value && value>0)
                sessionTimeoutMs = value;

            propVal = config.getProperty("zk.connectionTimeoutMs");
            value = tryParse(propVal,"zk.connectionTimeoutMs");
            if(null != value && value>0)
                connectionTimeoutMs = value;

            propVal = config.getProperty("zk.maxCloseWaitMs");
            value = tryParse(propVal,"zk.maxCloseWaitMs");
            if(null != value && value>0)
                maxCloseWaitMs = value;

            propVal = config.getProperty("zk.inactiveThresholdMs");
            value = tryParse(propVal,"zk.inactiveThresholdMs");
            if(null != value && value>0)
                inactiveThresholdMs = value;

            propVal = config.getProperty("zk.canBeReadOnly");
            value = tryParse(propVal,"zk.canBeReadOnly");
            if(null != value)
                canBeReadOnly = value==1?true:false;

            propVal = config.getProperty("zk.useContainerParentsIfAvailable");
            value = tryParse(propVal,"zk.useContainerParentsIfAvailable");
            if(null != value)
                useContainerParentsIfAvailable = value==1?true:false;

        } catch (Exception e) {
            logger.error(String.format("Load configuration file [ %s ] failed:%s",fullPath,e.getMessage()),e);
        }

        //加载配置文件
        CuratorFrameworkFactory.Builder builder = CuratorFrameworkFactory.builder();
        builder.connectString(connectString);
        builder.retryPolicy(new RetryForever(2000));
        if(sessionTimeoutMs != null &&
                sessionTimeoutMs>0)
            builder.sessionTimeoutMs(sessionTimeoutMs);
        if(null != connectionTimeoutMs &&
                connectionTimeoutMs>0)
            builder.connectionTimeoutMs(connectionTimeoutMs);
        if(null != maxCloseWaitMs &&
                maxCloseWaitMs>0)
            builder.maxCloseWaitMs(maxCloseWaitMs);
        if(null != namespace && !"".equals(namespace.trim()))
            builder.namespace(namespace.trim());
        if(null != canBeReadOnly)
            builder.canBeReadOnly(canBeReadOnly);
        if(null != useContainerParentsIfAvailable
                && !useContainerParentsIfAvailable)
            builder.dontUseContainerParents();

        client = builder.build();
        client.start();
    }

    private static Integer tryParse(String val,String key){
        Integer result = null;
        if(null == val || "".equals(val.trim()))
            return result;

        val = val.trim();
        try{
            result = Integer.parseInt(val);
        }catch (Exception ex){
            logger.warn(String.format("%s=%s invalid:%s",key,val,ex.getMessage()),ex);
        }

        return result;
    }
}
