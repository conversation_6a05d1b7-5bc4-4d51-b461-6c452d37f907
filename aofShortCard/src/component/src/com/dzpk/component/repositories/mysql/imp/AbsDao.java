package com.dzpk.component.repositories.mysql.imp;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.repositories.mysql.dbpool.DBUtil;
import org.apache.logging.log4j.Logger;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * 数据库操作基础类
 * 增加由调用者管理事务
 * 任何数据库操作最终必须调用close方法
 */
public abstract class AbsDao implements AutoCloseable {
    private Connection conn = null;
    public static Logger logger = LogUtil.getLogger(AbsDao.class);


    /**
     * 外部控制事务标识
     */
    private boolean isTranstion = false;

    public AbsDao() {
    }


    /**
     * 外部传入连接则为调用者控制事务，调用者必须回收连接资源
     * 手动执行commit操作
     * 如果传入的连接为空 ，调用者事务管理失效，数据将采用新的连接做后续操作
     *
     * @param conn
     */
    public AbsDao(Connection conn) {
        if (conn != null) {
            try {
                this.conn = conn;
                this.conn.setAutoCommit(false);
            } catch (SQLException e) {
                logger.error("database connet close error "+e.toString());
            }

            isTranstion = true;
        }
    }

    public Connection getConn() throws SQLException {
        if (conn == null) {
            conn = DBUtil.getConnection();
        }
        return conn;
    }

    /**
     * 判断是否由调用者控制事务
     *
     * @return
     */
    public boolean isTranstion() {
        return isTranstion;
    }

    /**
     * 关闭连接资源
     */
    @Override
    public void close() {
        try {
            if (conn != null && !conn.isClosed()) {
                conn.close();
            }
        } catch (SQLException e) {
            logger.error("database connet close error");
        }

    }

    /**
     * 关闭Statement
     *
     * @param stmt 要关闭的Statement对象
     */
    public void closeStatement(Statement stmt) throws SQLException {
        if (stmt != null && !stmt.isClosed()) {
            stmt.close();
            stmt = null;
        }

    }

    /**
     * 关闭resultSet
     *
     * @param resultSet 要关闭的resultSet对象
     */
    public void closeResultSet(ResultSet resultSet) throws SQLException {
        if (resultSet != null && !resultSet.isClosed()) {
            resultSet.close();
            resultSet = null;
        }

    }
}
