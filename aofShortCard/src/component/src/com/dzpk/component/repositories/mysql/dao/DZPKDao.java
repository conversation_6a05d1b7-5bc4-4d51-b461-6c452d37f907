
package com.dzpk.component.repositories.mysql.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: ConnDB</p> 
 * <p>Description: </p> 
 * <p>Copyright: Copyright (c) 2006</p> 
 * <AUTHOR>
 * @version 1.0
 */
public interface DZPKDao {
	
	/**
	 * 获取PreparedStatement对象
	 * @return
	 * @throws SQLException 
	 */
	PreparedStatement createStam(String sql,Object[] prams) throws SQLException;
	
	 /**
     * 关闭结果集
     *
     * @param result 要关闭的结果集对象
     */
    void closeResultSet(ResultSet result);
    /**
     * 关闭PreparedStatement
     *
     * @param stam 要关闭的PreparedStatement对象
     */
    void closePreparedStatement(PreparedStatement stam);
    /**
     * 关闭Connection
     *
     * @param conn 要关闭的Connection对象
     */
    void closeConnection(Connection conn);
    /**
     * 清除连接对象
     */
    void clear(ResultSet result, PreparedStatement stam, Connection conn);

}

