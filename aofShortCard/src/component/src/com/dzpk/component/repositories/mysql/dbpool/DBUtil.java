package com.dzpk.component.repositories.mysql.dbpool;

import com.dzpk.common.utils.LogUtil;
import org.apache.logging.log4j.Logger;

import javax.naming.InitialContext;
import javax.sql.DataSource;
import java.sql.*;

/**
 * Created by IntelliJ IDEA.
 * User: Administrator
 * To change this template use File | Settings | File Templates.
 */
public class DBUtil {

    private static Logger logger = LogUtil.getLogger(DBUtil.class);

    private static DataSource ds = null;

    public static synchronized DataSource getDataSource(String jndiStr) {
        if (ds == null) {
            logger.info("DataSource JNDI is " + jndiStr);
            try {
                InitialContext ic = new InitialContext();
                ds = (DataSource) ic.lookup(jndiStr);
            } catch (Exception ne) {
                logger.error("Exception while lookup JNDI:" + jndiStr + ",Error is:" + ne);
            }
        }
        return ds;
    }

    public static synchronized DataSource getDataSource() {
        if (ds == null)
            try {
                InitialContext ic = new InitialContext();
                ds = (DataSource) ic.lookup("java:/OracleDS");
            } catch (Exception ne) {
                logger.error("Exception while lookup JNDI:java:/OracleDS,Error is:" + ne);
            }
        return ds;
    }

    public static Connection getConnection(DataSource ds)
            throws SQLException {
        Connection con = null;
        if (ds == null) {
            logger.info("DataSource is NULL!!!");
            throw new SQLException("DataSource is NULL!!!");
        }
        try {
            con = ds.getConnection();
            logger.debug("getConnection sucessful!");
        } catch (Exception e) {
            logger.debug("getConnection() Exception : " + e);
            throw new SQLException("Exception while get connection");
        }
        return con;
    }

    /**
     * 获得数据库连接
     *
     * @return 数据库连接
     */
    public static Connection getConnection() throws SQLException {
        return ConnectionPool.getConnection();
    }

    /**
     * 关闭结果集
     *
     * @param result 要关闭的结果集对象
     */
    public static void closeResultSet(ResultSet result) {
        try {
            if (result != null) {
                result.close();
                result = null;
            }
        } catch (Exception se) {
            logger.error("SQL Exception while closing " + "Response Set : \n");
        }
    }

    /**
     * 关闭Statement
     *
     * @param stmt 要关闭的Statement对象
     */
    public static void closeStatement(Statement stmt) {
        try {
            if (stmt != null) {
                stmt.close();
                stmt = null;
            }
        } catch (Exception se) {
            logger.error("SQL Exception while closing " + "Statement : \n");
        }
    }

    /**
     * 关闭PreparedStatement
     *
     * @param preStmt 要关闭的PreparedStatement对象
     */
    public static void closePreparedStatement(PreparedStatement preStmt) {
        try {
            if (preStmt != null) {
                preStmt.close();
                preStmt = null;
            }
        } catch (Exception se) {
            logger.error("SQL Exception while closing " + "PreparedStatement : \n");
        }
    }

    /**
     * 重置连接的AutoCommit
     *
     * @param dbConnection Connection对象
     */
    public static void resetAutoCommit(Connection dbConnection,boolean autoCommit) {
        try {
            if (dbConnection != null && !dbConnection.isClosed()) {
                dbConnection.setAutoCommit(autoCommit);
            }
        } catch (Exception se) {
            logger.warn("SQL Exception while reset autocommit for DB Connection : "+se.getMessage());
            if(logger.isDebugEnabled())
                logger.debug(se);
        }
    }

    /**
     * 关闭Connection
     *
     * @param dbConnection 要关闭的Connection对象
     */
    public static void closeConnection(Connection dbConnection) {
        try {
            if (dbConnection != null && !dbConnection.isClosed()) {
                dbConnection.close();
                dbConnection = null;
            }
        } catch (Exception se) {
            logger.error("SQL Exception while closing " + "DB Connection : \n");
        }
    }

    public static void rollback(Connection dbConnection){
        try {
            if (dbConnection != null && !dbConnection.isClosed()) {
                dbConnection.rollback();
            }
        } catch (Exception se) {
            logger.warn("SQL Exception while rollbacking with DB Connection : "+se.getMessage());
            if(logger.isDebugEnabled())
                logger.debug(se);
        }
    }

}
