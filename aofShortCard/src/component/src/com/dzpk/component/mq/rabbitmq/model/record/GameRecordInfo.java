package com.dzpk.component.mq.rabbitmq.model.record;

import java.util.List;

public class GameRecordInfo {

    //房间id
    private String roomId;

    //房间类型
    private String roomPath;

    //房间名称
    private String roomName;

    //房间结束时间
    private long finishTime;
    //俱乐部的房间
    private String clubRoomType;
    //房间内玩家盈亏
    private List<PlayerPl> playerPlList;

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getRoomPath() {
        return roomPath;
    }

    public void setRoomPath(String roomPath) {
        this.roomPath = roomPath;
    }

    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    public List<PlayerPl> getPlayerPlList() {
        return playerPlList;
    }

    public void setPlayerPlList(List<PlayerPl> playerPlList) {
        this.playerPlList = playerPlList;
    }

    public long getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(long finishTime) {
        this.finishTime = finishTime;
    }

    public String getClubRoomType() {
        return clubRoomType;
    }

    public void setClubRoomType(String clubRoomType) {
        this.clubRoomType = clubRoomType;
    }
}
