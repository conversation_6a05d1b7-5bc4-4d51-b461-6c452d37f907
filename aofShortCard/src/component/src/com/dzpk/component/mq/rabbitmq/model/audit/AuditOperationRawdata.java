package com.dzpk.component.mq.rabbitmq.model.audit;

import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * Description:
 * <p>
 * Created by L on 2019/7/8 15:04
 */
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AuditOperationRawdata implements Serializable {

    private Integer userId;

    private Integer type;

    private Integer userAccount;

    private Date auditTime;
}
