package com.dzpk.component.mq.rabbitmq;

import com.i366.cache.Cache;
import com.rabbitmq.client.*;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 消息队列
 */
@Slf4j
public class RabbitMQService {
    /** 服务实例 */
    private static RabbitMQService _INSTANCE = new RabbitMQService();
    public static RabbitMQService getInstance(){
        return _INSTANCE;
    }

    /** 连接池 */
    private ConnectionFactory connectionFactory = null;

    // 构造方法; 接收一个路由地址参数
    private RabbitMQService() {
        String host = Cache.p.getProperty("rabbitmq.host");
        String user = Cache.p.getProperty("rabbitmq.user");
        String password = Cache.p.getProperty("rabbitmq.password");
        int port = Integer.parseInt(Cache.p.getProperty("rabbitmq.port"));

        // 创建一个连接工厂 connection factory
        ConnectionFactory factory = new ConnectionFactory();

        // 设置rabbitmq-server服务IP地址、用户名、密码、端口
        factory.setHost(host);
        factory.setUsername(user);
        factory.setPassword(password);
        factory.setPort(port);
        factory.setVirtualHost("/");

        this.connectionFactory = factory;
    }


    /**
     * 发送消息
     * @param queueName  队列名称
     * @param exchange   交换机名称
     * @param routeKey   路由key
     * @param message    消息
     * @param exchangeType   交换机类型
     * @throws Exception
     */
    public void sendMessage(String exchangeType,String queueName,String exchange,String routeKey,String message){
        // 连接通道
        Channel channel = null;
        Connection connection = null;
        try {
            // 获取连接
            connection = this.connectionFactory.newConnection();

            // 获取通道
            channel = connection.createChannel();

            // 声明一个永久的、不使用时不会被删除的交换机
            channel.exchangeDeclare(exchange, exchangeType, true, false, null);

            //声明一个永久的、不使用时不会被删除的队列
            channel.queueDeclare(queueName, true, false, false, null);

            //将消息队列绑定到Exchange
            channel.queueBind(queueName, exchange, routeKey);

            //开启消息确认机制
            channel.confirmSelect();

            Map<String,Object> header = new HashMap<>();
            header.put("spring_returned_message_correlation",UUID.randomUUID().toString());
            AMQP.BasicProperties pro = new AMQP.BasicProperties().builder()
                    .contentType("application/json")
                    .headers(header)
                    .contentEncoding("UTF-8")
                    .deliveryMode(2)  //消息设置为持久化的,RabbitMQ就会将消息持久化到磁盘上去
                    .build();
            //发送消息
            channel.basicPublish(exchange, routeKey, pro, message.getBytes("UTF-8"));

            //开启消息监听
            channel.addConfirmListener(new ConfirmListener() {

                /**
                 * deliveryTag 消息id
                 * multiple 是否批量
                 *      如果是true，就意味着，小于等于deliveryTag的消息都处理成功了
                 *      如果是false，只是成功了deliveryTag这一条消息
                 */
                @Override
                public void handleAck(long deliveryTag, boolean multiple){
                     log.debug("已确认消息, 消息id={},是否多个消息={}",deliveryTag,multiple);
                }

                /**
                 * deliveryTag 消息id
                 * multiple 是否批量
                 *      如果是true，就意味着，小于等于deliveryTag的消息都处理失败了
                 *      如果是false，只是失败了deliveryTag这一条消息
                 */
                @Override
                public void handleNack(long deliveryTag, boolean multiple) {
                    log.debug("未确认消息, 消息id={},是否多个消息={}",deliveryTag,multiple);
                }
            });

            log.info("消息发送成功,message is {}",new String(message));
        } catch (Exception ex) {
            throw new RuntimeException("发送消息到MQ失败:"+ex.getMessage(),ex);
        }finally {
            this.closeChannel(channel);
            this.closeConnection(connection);
        }
    }

    public void destroy(){
    }

    private void closeChannel(Channel channel){
        try{
            if(null != channel)
                channel.close();
        }catch (Exception ex){
            log.warn("关闭到Rabbitmq的Channel失败：{}",ex.getMessage());
        }
    }

    private void closeConnection(Connection connection){
        try{
            if(null != connection)
                connection.close();
        }catch (Exception ex){
            log.warn("关闭到Rabbitmq的Connection失败：{}",ex.getMessage());
        }
    }
}
