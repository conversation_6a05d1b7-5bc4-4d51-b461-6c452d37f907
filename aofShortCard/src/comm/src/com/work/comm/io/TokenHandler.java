package com.work.comm.io;

import com.dzpk.common.token.TokenManager;
import com.i366.cache.Cache;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.server.pack.I366ServerPickUtil;
import com.work.comm.client.pack.Functions;
import com.work.comm.client.protocal.Protocal;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.db.model.UserInfo;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import org.apache.logging.log4j.Logger;

/**
 * 校验token Handler
 */
public class TokenHandler extends SimpleChannelInboundHandler<Object> {

    private static Logger logger = LogUtil.getLogger(TokenHandler.class);


    @Override
    protected void channelRead0(ChannelHandlerContext ctx, Object msg) throws Exception {
//        ByteBuf inMsg = (ByteBuf) msg;
//
//        byte[] tokenBytes = new byte[32];
//        for(int i = 0; i <= 31; i++){
//            tokenBytes[i] = inMsg.getByte(i);
//        }
//
//        String token = new String(tokenBytes);
//
//        byte[] userIdBytes = new byte[4];
//        byte[] requestCodeBytes = new byte[2];
//
//        for(int i = 0; i <= 1; i++){
//            requestCodeBytes[i] = inMsg.getByte(Protocal.RP_REQUEST_CODE_HIGH + i);
//        }
//
//        for(int i = 0; i <= 3; i++){
//            userIdBytes[i] = inMsg.getByte(Protocal.RP_USER_ID_1 + i);
//        }
//
//        int userId = Functions.byteArrayToInt(userIdBytes, 0);
//        int requestCode = Functions.byteArrayToShortInt(requestCodeBytes, 0);
//
//        logger.debug("token: " + token + " userId: " + userId + " requestCode: " + requestCode);
//
//        UserInfo info = Cache.getOnlineUserInfo().get(userId);
//
//        if(requestCode != 97 && requestCode != 66 && requestCode != 44 && info != null){
//
//            boolean checkToken = checkToken(token);
//
//            if(!checkToken ){
//                logger.debug("tokenHandler token error: " + token);
//
//                try {
//
//                    Object[][] objs = {
//                            {60, 20, I366ServerPickUtil.TYPE_INT_1}       //  token校验失败
//                    };
//
//                    byte[] retBytes = I366ClientPickUtil.packAll(objs, requestCode);
//                    ctx.writeAndFlush(Unpooled.copiedBuffer(retBytes));
//                } catch (Exception e) {
//                    logger.error(" tokenHandler error, " , e);
//                }
//            }else{
//                ctx.fireChannelRead(msg);
//            }
//
//        }else{
//            ctx.fireChannelRead(msg);
//        }
    }

    /**
     * 校验token
     * @param token
     * @return
     */
    private boolean checkToken(String token){

        return TokenManager.getInstance().verify(token,null,"") > 0 ? true :false;
    }
}
