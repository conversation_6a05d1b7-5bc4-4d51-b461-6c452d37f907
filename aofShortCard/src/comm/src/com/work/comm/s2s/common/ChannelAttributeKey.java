package com.work.comm.s2s.common;

import io.netty.util.AttributeKey;

public final class ChannelAttributeKey {
    // 对端应用IP
    public static final AttributeKey<String> ipKey =AttributeKey.valueOf("PERSERVER_BYIP");
    // 对端应用PORT
    public static final AttributeKey<Integer> portKey =AttributeKey.valueOf("PERSERVER_BYPORT");
    // 对端应用ID
    public static final AttributeKey<String> idKey =AttributeKey.valueOf("PERSERVER_BYID");
    // 对端应用类型
    public static final AttributeKey<String> typeKey =AttributeKey.valueOf("PERSERVER_BYTYPE");
    // channel配置批次
    public static final AttributeKey<Long> configBatchNumKey =AttributeKey.valueOf("CONFIG_BATCHNUM");
    // 闲读计数器
    public static final AttributeKey<Integer> readIdleCounterKey = AttributeKey.valueOf("READIDLE_COUNTER");
}
