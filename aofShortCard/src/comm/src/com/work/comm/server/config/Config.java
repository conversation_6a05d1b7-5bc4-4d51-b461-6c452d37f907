
package com.work.comm.server.config;

import com.i366.cache.Cache;
import com.work.comm.server.action.PutRoomAction;

import java.util.HashMap;
import java.util.Map;

/**
 * 内部服务器配置信息
 */
public class Config {
	
	public static Object[] CONNECT_ZEPK_CORE_SERVER = {Cache.p.getProperty("core.service.ip") ,Integer.valueOf(Cache.p.getProperty("core.service.port"))};
	public static Object[] CONNECT_ZEPK_RES_SERVER = {Cache.p.getProperty("res.service.ip") ,Integer.valueOf(Cache.p.getProperty("res.service.port"))};

    public final static int SERVER_NUMBER = Integer.valueOf(Cache.p.getProperty("service.name"));

    /**
	 * 控制调整配置<br/>
	 * KEY :requestCode<br/>
	 * VALUE:实现类的class对象
	 */
	@SuppressWarnings("rawtypes")
	public static Map<Integer, Class> eventMapping = new HashMap<Integer, Class>();

	static {
		eventMapping.put(85, PutRoomAction.class);
		eventMapping.put(91, PutRoomAction.class);
	}
}

