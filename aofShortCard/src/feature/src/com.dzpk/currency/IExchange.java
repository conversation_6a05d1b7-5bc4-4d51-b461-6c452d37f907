package com.dzpk.currency;

import com.dzpk.common.constant.ERoundingMode;

public interface IExchange {
    /**
     * 将计分牌数量兑换为金豆数量
     *
     * @param scorecardNum  计分牌数量，正负皆可
     * @param mode          取整模式,可选，默认：HALF_UP
     *
     * @return 等价值的金豆数量
     */
    int scorecard2Jdou(double scorecardNum, ERoundingMode mode);

    /**
     * 将金豆数量兑换为计分牌数量
     *
     * @param jdouNum       金豆数量，正负皆可
     * @param mode          取整模式,可选，默认：HALF_UP
     *
     * @return 等价值的计分牌数量
     */
    int jdou2Scorecard(int jdouNum, ERoundingMode mode);
}
