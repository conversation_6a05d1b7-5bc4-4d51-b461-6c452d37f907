package com.dzpk.audit.constant;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 功能模块代号定义（用于功能模块显示）
 *
 */

@AllArgsConstructor
@Getter
public enum AuditOperationCode {

    FLOW_CHIP(1, "转豆"),
    WITHDRAW(2, "提豆"),
    RECHARGE_CLUB_FUND(3, "俱乐部充值"),
    GIVEN_FUND(4, "发放基金"),
    BRING_IN_ROOM(5, "牌局带入"),
    ;

    private int code;

    private String desc;

    public static AuditOperationCode getByTypeCode(int typeCode) {
        AuditOperationCode[] functionCodes = AuditOperationCode.values();
        for (int i = 0; i < functionCodes.length; i++) {
            if (functionCodes[i].getCode() == typeCode) {
                return functionCodes[i];
            }
        }
        return null;
    }

    public static Integer queryCodeByName(String name) {
        AuditOperationCode[] respCodes = AuditOperationCode.values();
        for (int i = 0; i < respCodes.length; i++) {
            if (respCodes[i].getDesc().equals(name)) {
                return respCodes[i].getCode();
            }
        }
        return null;
    }

}
