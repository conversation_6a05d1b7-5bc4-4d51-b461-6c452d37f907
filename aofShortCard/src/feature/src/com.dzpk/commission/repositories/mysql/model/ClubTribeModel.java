package com.dzpk.commission.repositories.mysql.model;

public class ClubTribeModel {

    private int clubCreator;  //俱乐部创建者

    private int tribeCreator;  //同盟创建者

    private String clubName;     //俱乐部名称

    private String tribeName;    //同盟名称

    private int clubId;       //俱乐部id

    private int tribeId;     //同盟id

    private int clubTribeStatus;     //俱乐部与同盟的关系 1正常 2被踢出 3转移中

    private int clubStatus;   //俱乐部状态 0正常 1 关闭

    private int tribeStatus;  //同盟状态  0正常  1 关闭
    //联盟分成
    private int tribeProportion;
    //俱乐部分成
    private int clubProportion;

    public int getTribeProportion() {
        return tribeProportion;
    }
    public void setTribeProportion(int tribeProportion) {
        this.tribeProportion = tribeProportion;
    }

    public int getClubProportion() {
        return clubProportion;
    }

    public void setClubProportion(int clubProportion) {
        this.clubProportion = clubProportion;
    }
    public int getClubCreator() {
        return clubCreator;
    }

    public void setClubCreator(int clubCreator) {
        this.clubCreator = clubCreator;
    }

    public int getTribeCreator() {
        return tribeCreator;
    }

    public void setTribeCreator(int tribeCreator) {
        this.tribeCreator = tribeCreator;
    }

    public String getClubName() {
        return clubName;
    }

    public void setClubName(String clubName) {
        this.clubName = clubName;
    }

    public String getTribeName() {
        return tribeName;
    }

    public void setTribeName(String tribeName) {
        this.tribeName = tribeName;
    }

    public int getClubId() {
        return clubId;
    }

    public void setClubId(int clubId) {
        this.clubId = clubId;
    }

    public int getTribeId() {
        return tribeId;
    }

    public void setTribeId(int tribeId) {
        this.tribeId = tribeId;
    }

    public int getClubStatus() {
        return clubStatus;
    }

    public void setClubStatus(int clubStatus) {
        this.clubStatus = clubStatus;
    }

    public int getClubTribeStatus() {
        return clubTribeStatus;
    }

    public void setClubTribeStatus(int clubTribeStatus) {
        this.clubTribeStatus = clubTribeStatus;
    }

    public int getTribeStatus() {
        return tribeStatus;
    }

    public void setTribeStatus(int tribeStatus) {
        this.tribeStatus = tribeStatus;
    }

    @Override
    public String toString() {
        return "ClubTribeModel{" +
                "clubCreator=" + clubCreator +
                ", tribeCreator=" + tribeCreator +
                ", clubName='" + clubName + '\'' +
                ", tribeName='" + tribeName + '\'' +
                ", clubId=" + clubId +
                ", tribeId=" + tribeId +
                ", clubTribeStatus=" + clubTribeStatus +
                ", clubStatus=" + clubStatus +
                ", tribeStatus=" + tribeStatus +
                '}';
    }
}
