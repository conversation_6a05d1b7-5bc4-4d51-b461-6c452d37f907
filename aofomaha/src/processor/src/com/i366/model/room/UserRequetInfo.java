/**
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.i366.model.room;

/**
 * 记录用户带入的ip ,gps,机器码
 * <AUTHOR>
 *
 */
public class UserRequetInfo {

    private String ip = "";//ip

    private String gps = "";//经度,纬度

    private String imei = "";//机器码

    private String mac = "";//mac地址

    private int virtual = 0;//是否模拟器  0否 1是

    private String formattedAddress = ""; //带入详细地址

    private UserRequetInfo() {
    }

    public UserRequetInfo(String ip, String gps, String imei, String mac) {
        this.ip = ip;
        this.gps = gps;
        this.imei = imei;
        this.mac = mac;
    }

    public UserRequetInfo(String ip, String gps, String imei, String mac, int virtual, String formattedAddress) {
        this.ip = ip;
        this.gps = gps;
        this.imei = imei;
        this.mac = mac;
        this.virtual = virtual;
        this.formattedAddress = formattedAddress;
    }



    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getGps() {
        return gps;
    }

    public void setGps(String gps) {
        this.gps = gps;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public int getVirtual() {
        return virtual;
    }

    public void setVirtual(int virtual) {
        this.virtual = virtual;
    }

    public String getFormattedAddress() {
        return formattedAddress;
    }

    public void setFormattedAddress(String formattedAddress) {
        this.formattedAddress = formattedAddress;
    }
}
