package com.i366.processor.server;

import com.dzpk.common.utils.LogUtil;
import com.work.comm.client.protocal.BaseRequest;
import com.work.comm.io.Handler;
import org.apache.logging.log4j.Logger;

/**
 * @deprecated
 *
 * 处理带入审批消息
 * 疯狂扑克中已去掉审批,该协议已废除,暂时保留
 */
public class Processor_205_BatchControl extends Handler {
    private Logger logger = LogUtil.getLogger(Processor_205_BatchControl.class);

    @Override
    public byte[] handleRequest(BaseRequest req) {
//        BaseRequest request = (BaseRequest) req;
//        Map<Integer, Object> map = null;
//        int[][] int1 = {
//                { 60, I366ServerPickUtil.TYPE_INT_1 }, // 同意0 拒绝1
//                { 130, I366ServerPickUtil.TYPE_STRING_UTF16 }, // message ids
//                { 131, I366ServerPickUtil.TYPE_INT_4 },
//        };
//        map = I366ServerPickUtil.pickAll(request.getBt(), int1);
//        int userId = (Integer) map.get(131);
//        int operate = (Integer) map.get(60);
//        String ids = (String) map.get(130);
//
//        logger.debug("Processor_205_BatchControl id " + userId);
//        logger.debug("operate:" + operate + ", ids:" + ids);
//
//        String[] res = RoomRequest.batchOperate(userId, operate, ids, RoomRequest.REQ_TYPE_ROOM_NORMAL);
//
//        Object[] config = new Object[] {
//                Cache.p.getProperty("res.service.ip"),
//                Integer.valueOf(Cache.p.getProperty("res.service.port"))
//        };
//        logger.debug("ip: " + config[0] + " port: " + config[1]);
//        try {
//            Registration reg = Registration.getShortReg(config);
//            operate = Integer.parseInt(res[2]);
//            Object[][] objs = {
//                    { 60, operate, I366ServerPickUtil.TYPE_INT_1 }, // 0成功 1过期 2不在房间
//                    { 130, res[0], I366ServerPickUtil.TYPE_STRING_UTF16 }, // 成功处理的请求id
//                    { 131, res[1], I366ServerPickUtil.TYPE_STRING_UTF16 }, // 过期的请求id
//                    { 132, userId, I366ServerPickUtil.TYPE_INT_4 },
//                    { 133, res[3], I366ServerPickUtil.TYPE_STRING_UTF16}, //  成功处理的请求id, 但社区积分不足，同意审批失败
//                    { 134, res[4], I366ServerPickUtil.TYPE_STRING_UTF16}, //  需通知其他管理员的消息
//            };
//            logger.debug("send Processor_205_BatchControl res[3]===" + res[3]);
//            byte[] bytes = I366ServerPickUtil.packAll(objs, Constant.REQ_RES_BATCH_CONTROL);
//            reg.publishByte(bytes);
//            reg.close();
//            logger.info("send Processor_205_BatchControl notify success!");
//        } catch (Exception e) {
//            logger.error("send Processor_205_BatchControl Notify", e);
//        }
        return null;
    }

}
