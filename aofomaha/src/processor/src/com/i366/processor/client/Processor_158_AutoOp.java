
package com.i366.processor.client;

import java.util.Map;

import com.dzpk.common.utils.LogUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.BaseRequest;
import com.work.comm.client.protocal.Request;
import org.apache.logging.log4j.Logger;

import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;
import com.i366.constant.Constant;
import com.i366.room.RoomService;
import com.work.comm.io.Handler;

import com.dzpk.db.model.UserInfo;

/**
 * 托管请求
 * 玩家在进房时会主动请求一次
 */
public class Processor_158_AutoOp extends Handler {
    private final Logger logger = LogUtil.getLogger(Processor_158_AutoOp.class);

    @Override
    public byte[] handleRequest(BaseRequest req) {
        Request request = (Request) req;
        int userId = request.getUserId();
        logger.debug("Processor_158_AutoOp id " + userId);
        int[][] int2 = {
                {130, I366ClientPickUtil.TYPE_INT_4},        // roomId
                {131, I366ClientPickUtil.TYPE_INT_4},        // roomPath
                {132, I366ClientPickUtil.TYPE_INT_1},        // 0取消托管 1托管 2进入请求时间 3托管时间到 4其它房间状态查询
                {133, I366ClientPickUtil.TYPE_INT_1},        // 是否是主动托管
        };
        Map<Integer, Object> map = I366ClientPickUtil.pickAll(request.getBt(), int2);
        int roomId = (Integer) map.get(130);
        int roomPath = (Integer) map.get(131);
        int type = (Integer) map.get(132);
        int active = map.get(133) != null ? (Integer) map.get(133) : 0;

        RoomService.setUserChannel(request, roomId);

        Task task = new Task(Constant.REQ_REQUEST_AUTO_OP, map, request, roomId, roomPath);
        WorkThreadService.submit(roomId, task);
        return null;
    }

}
