
package com.i366.processor.client;

import java.util.Map;

import com.dzpk.common.utils.LogUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.BaseRequest;
import com.work.comm.client.protocal.Request;
import org.apache.logging.log4j.Logger;

import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;
import com.i366.constant.Constant;
import com.i366.room.RoomService;
import com.work.comm.io.Handler;

import com.dzpk.db.model.UserInfo;

/**
 * @deprecated
 *
 * 玩家主动点击straddle
 * 疯狂扑克中已废弃,暂时保留
 */
public class Processor_157_Straddle extends Handler {

    private final Logger logger = LogUtil.getLogger(Processor_157_Straddle.class);

    @Override
    public byte[] handleRequest(BaseRequest req) {
        Request request = (Request) req;
        int userId = request.getUserId();
        logger.debug("Processor_157_Straddle id " + userId);
        int[][] int2 = {
                {130, I366ClientPickUtil.TYPE_INT_4},        // roomId
                {131, I366ClientPickUtil.TYPE_INT_4},        // roomPath
                {132, I366ClientPickUtil.TYPE_INT_1},        // 选择类型 0straddle 1no 2超时
        };
        Map<Integer, Object> map = I366ClientPickUtil.pickAll(request.getBt(), int2);
        int roomId = (Integer) map.get(130);
        int roomPath = (Integer) map.get(131);
        int type = (Integer) map.get(132);
        map.put(133, System.currentTimeMillis());           // 选择时间

        RoomService.setUserChannel(request, roomId);

        Task task = new Task(Constant.REQ_REQUEST_STRADDLE, map, request, roomId, roomPath);
        WorkThreadService.submit(roomId, task);
        return null;
    }

}
