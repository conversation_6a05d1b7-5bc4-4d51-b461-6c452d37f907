package com.dzpk.processor.impl;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.cache.Cache;
import com.i366.constant.Constant;
import com.i366.constant.RoomStartCode;
import com.i366.model.room.Room;
import com.i366.util.PublisherUtil;
import com.i366.util.RoomUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.Request;
import org.apache.logging.log4j.Logger;


public class Request_67_StartRoom implements IProcessor {

    private Logger logger = LogUtil.getLogger(Request_67_StartRoom.class);

    public void handle(Task task) {
        int roomId = task.getRoomId();
        int roomPath = task.getRoomPath();
        Request request = (Request) task.getRequest();

        try {
            Room room = Cache.getRoom(roomId, roomPath);
            if (room == null) // 牌局不存在,应该是牌局加载初始化发生异常
                return;

            if (room.getRoomStatus() != 0) // 牌局加载后，需要房主调用本协议进行开局，只处理一次
                return;

            RoomUtil.startRoom(room, RoomStartCode.NORMAL_START);
            return;
        } catch (Exception e) {
            logger.error("", e);
        }
        PublisherUtil.publisher(request, pusUser(1));
    }

    private byte[] pusUser(int status) {
        Object[][] objs = {
                {60, status,I366ClientPickUtil.TYPE_INT_1}        // 0 成功 1失败
        };
        byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_START_ROOM);
        return bytes;
    }
}
