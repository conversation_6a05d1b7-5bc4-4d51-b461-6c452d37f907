package com.dzpk.processor.impl;

import com.dzpk.common.utils.LogUtil;
import com.i366.constant.ChipsCode;
import com.i366.model.player.RoomPersion;
import com.i366.util.ChipUtils;
import com.dzpk.db.impl.UserInfoDaoImp;
import com.i366.util.PublisherUtil;
import com.i366.util.RoomUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.Request;
import com.work.comm.server.pack.I366ServerPickUtil;
import org.apache.logging.log4j.Logger;

import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.dzpk.work.TaskConstant;
import com.dzpk.work.WorkThreadService;
import com.i366.constant.Constant;
import com.i366.cache.Cache;
import com.i366.model.room.Room;


/**
 * 玩家请求花金豆主动翻牌
 */
public class Request_410_ShowCardsByUser implements IProcessor {

    private Logger logger = LogUtil.getLogger(Request_410_ShowCardsByUser.class);
    
    @Override
    public void handle(Task task) {
        int roomId = task.getRoomId();
        int roomPath = task.getRoomPath();
        Request request = (Request) task.getRequest();
        int userId = request.getUserId();
        int nowCardCnt = (Integer) task.getMap().get(60);
        logger.debug("userId: " + userId + ", received cardCnt: " + nowCardCnt);

        try {

            Room room = Cache.getRoom(roomId, roomPath);
            if (room == null) { // 房间不存在
                logger.error("room is null");
                PublisherUtil.publisher(request, pusUser(1, 0));
                return;
            }

            if (7 != room.getRoomStatus()) { // 房间当前所处状态不对
                logger.error("showcards error,roomstatus is wrong," +
                        "roomstatus={},userid={},roomid={}",room.getRoomStatus(),userId,roomId);
                PublisherUtil.publisher(request, pusUser(1, 0));
                return;
            }

            if (room.isPendingTimeOut()) { // 5s延时已经到了, 进入下一手任务TASK_NEXT_GAME，不再接受花钻石看牌的请求
                logger.error("showcards error,5s delay is over do not accept show cards req," +
                        "isPendingTimeOut={},userid={},roomid={}",room.isPendingTimeOut(),userId,roomId);
                PublisherUtil.publisher(request, pusUser(2, 0));
                return;
            }

            int userCanTurnTimes = room.getUserCanTurnTimes();
            if (userCanTurnTimes <= 0 || nowCardCnt >= 5) { // 本手牌结束时没有可以花钻石能翻牌的机会或者已进入TASK_NEXT_GAME任务
                logger.error("showcards error,canturntimes is enough,userCanTurnTimes={},nowCardCnt={},userid={},roomid={}",userCanTurnTimes,nowCardCnt,userId,roomId);
                PublisherUtil.publisher(request, pusUser(1, 0));
                return;
            }

            RoomPersion roomPersion = room.getAudMap().get(userId);
            if(null == roomPersion){
                logger.error("showcards error,roomPersion is null,roomid={},userid={}",roomId,userId);
                PublisherUtil.publisher(request, pusUser(1, 0));
                return;
            }

            int newCardCnt = 0;
            int canTurnTimes = 0;
            if (0 == nowCardCnt) { // 未进入翻牌阶段，此次请求需要翻3张牌，玩家可以请求的次数为3
                newCardCnt = 3;
                canTurnTimes = 3;
            } else { // 未进入转牌/河牌阶段，此次请求需要翻1张牌，玩家可以请求的次数为2(未发转牌)或1(未发河牌)
                newCardCnt = 1;
                canTurnTimes = 5 - nowCardCnt;
            }

            if (canTurnTimes != userCanTurnTimes) { // 玩家申请翻的牌与服务器当前可以翻的牌不一致
                logger.debug("counted turn times=" + canTurnTimes + ", newCardCnt=" + newCardCnt);
                PublisherUtil.publisher(request, pusUser(1, 0));
                return;
            }

            int showCardsFee = Constant.USER_SHOW_CARDS_FEE.get(room.getManzhu()) / 2 <= 40 ? 40 : Constant.USER_SHOW_CARDS_FEE.get(room.getManzhu())/2;

            if(ChipUtils.updateChipWithNoCommisson(roomPersion,null,showCardsFee,roomId, ChipsCode.SHOW_CARDS)){  //扣减金豆

                room.setInRequest410(true); // 当前房间正在处理玩家的花钻石看底牌任务
                int status = room.getRoomService().showCardsByUser(newCardCnt, userId);
                room.setInRequest410(false);

                if (0 == status) {

                    room.setUserCanTurnTimes(userCanTurnTimes - 1); // 成功翻完一次底牌后，玩家可通过花钻石看底牌的次数减少1

                    Task newTask = new Task(TaskConstant.TASK_SERVER_PENDING_ROOM, null, room.getRoomId(), room.getRoomPath()); // 重新延迟1s启动TASK_NEXT_GAME延时任务
                    for (String key : room.roomProcedure.delayTaskMap.keySet()) { // 使之前的所有10017延时任务无效
                        Task value = room.roomProcedure.delayTaskMap.get(key);
                        int taskId = value.getTaskId();
                        if (TaskConstant.TASK_SERVER_PENDING_ROOM == taskId) {
                            value.setValid(false);
                            value.getTaskFuture().cancel(true);
                            room.roomProcedure.delayTaskMap.remove(taskId);
                        }
                    }
                    WorkThreadService.submitDelayTask(room.getRoomId(), newTask, 1500);
                    room.roomProcedure.delayTaskMap.put(newTask.getId(), newTask);

                    if (room.isPendingTimeOut()) { // 5s延时已经到了, 进入下一手任务TASK_NEXT_GAME，不再接受花钻石看牌的请求
                        logger.debug("5s delay is over, now need begin task TASK_NEXT_GAME");
                        room.setUserCanTurnTimes(0);
                        room.roomProcedure.genNextGameTask(Constant.BIPAI_TIME);
                    }

                    logger.debug("showcards successfully,roomid={},userid={},nowCardCnt={}",roomId,userId,nowCardCnt);
                    PublisherUtil.publisher(request, pusUser(status, roomPersion.getUserInfo().getChip()));
                }else{
                    logger.error("showcards error,showCardsByUser error,roomid={},userid={}",roomId,userId);
                    PublisherUtil.publisher(request, pusUser(1, 0));
                    return;
                }

            }else{
                PublisherUtil.publisher(request, pusUser(3, roomPersion.getUserInfo().getChip())); // 金豆不足
                return;
            }


        } catch (Exception e) {
            logger.error("showcards error,showCardsByUser error,roomid={},userid={}",roomId,userId);
            PublisherUtil.publisher(request, pusUser(1, 0));
            return;
        }
    }
    
    private byte[] pusUser(int status, int lefChip) {
        logger.debug("status: " + status);
        Object[][] objs2 = {
                {60, status,I366ClientPickUtil.TYPE_INT_1},
                // 0成功 1失败 2请求失效 3筹码不足
                {130, lefChip,I366ClientPickUtil.TYPE_INT_4}
        };
        byte[] bytes = I366ClientPickUtil.packAll(objs2, Constant.REQ_SHOW_CARDS_BY_USER);
        return bytes;
    }

}
