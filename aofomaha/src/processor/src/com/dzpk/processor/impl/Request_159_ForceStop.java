
package com.dzpk.processor.impl;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.constant.Constant;
import com.i366.cache.Cache;
import com.i366.constant.RoomFinishCode;
import com.i366.model.room.Room;

import com.i366.util.PublisherUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.Request;
import com.work.comm.server.pack.I366ServerPickUtil;
import org.apache.logging.log4j.Logger;

/**
 * 房主解散房间
 */
public class Request_159_ForceStop implements IProcessor {

    private Logger logger = LogUtil.getLogger(Request_159_ForceStop.class);

    @Override
    public void handle(Task task) {
        int roomId = task.getRoomId();
        int roomPath = task.getRoomPath();
        Request request = (Request) task.getRequest();
        
        int userId = request.getUserId();
        logger.debug("roomId: " + roomId + ", roomPath: " + roomPath + ", userId: " + userId);
        
        Room room = Cache.getRoom(roomId, roomPath);
        if (room != null && room.getOwner() == userId) {
            PublisherUtil.send(room,stopRoomPus(0, ""));     // 下发通知
            room.getRoomService().forceStopRoom(RoomFinishCode.OWNER_FORCE_FINISH);
        } else {
            logger.debug("room null or user not master");
            PublisherUtil.publisher(request, stopRoomPus(1, ""));
        }
    }


    // 房间解散通知
    private byte[] stopRoomPus(int status, String notice) {
        logger.debug("status: " + status);
        Object[][] objs2 = {
                { 60, status,I366ClientPickUtil.TYPE_INT_1 }, // 返回状态 0成功 1失败 2无限局结束
                { 130, notice,I366ClientPickUtil.TYPE_STRING_UTF16 } // 通知
        };
        byte[] bytes = I366ClientPickUtil.packAll(objs2, Constant.REQ_REQUEST_FORCE_STOP);
        return bytes;
    }
    
}
