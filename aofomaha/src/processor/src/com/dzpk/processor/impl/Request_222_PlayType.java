package com.dzpk.processor.impl;

import com.dzpk.common.utils.LogUtil;
import com.i366.util.PublisherUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.Request;
import com.work.comm.server.pack.I366ServerPickUtil;
import org.apache.logging.log4j.Logger;

import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.constant.Constant;
import com.i366.cache.Cache;
import com.i366.model.player.RoomPersion;
import com.i366.model.room.Room;


public class Request_222_PlayType implements IProcessor {

    private Logger logger = LogUtil.getLogger(Request_222_PlayType.class);
    
    @Override
    public void handle(Task task) {
        int roomId = task.getRoomId();
        int roomPath = task.getRoomPath();
        Request request = (Request) task.getRequest();
        int playType = (Integer) task.getMap().get(132);
        int userId = request.getUserId();

        logger.debug("roomId: " + roomId + ", roomPath: " + roomPath + ", playType: " + playType);

        playType = playType != 1 ? 0 : playType;    // 默认补盲
        try {
            Room room = Cache.getRoom(roomId, roomPath);
            if (room != null) {
                for (RoomPersion rp : room.getRoomPersions()) {
                    if (rp != null && rp.getOnlinerType() != -1 && rp.getUserId() == userId) {  // 玩家正在打牌，无效
                        logger.debug("user is playing!");
                        PublisherUtil.publisher(request, pusUser(1, 0));
                        return;
                    }
                }
                for (RoomPersion rp : room.getDdRoomPersions()) {
                    if (rp != null && rp.getUserId() == userId) {
                        rp.setPlayType(playType);
                        if (playType == 1) {
                            int seat = rp.getSize();
                            int nextSeat = room.getRoomService().getNextSeat(seat);
                            int preSeat = room.getRoomService().getPreSeat(seat);
                            
                            int sbSeat = room.getManzhuNumber();
                            int bbSeat = room.getDamanzhuNumber();
                            int dealerSeat = room.getZhuangjiaNumber();
                            logger.debug("userSeat=" + seat + ", preSeat=" + preSeat + ", nextSeat=" + nextSeat);
                            logger.debug("dealerSeat=" + dealerSeat + ", sbSeat=" + sbSeat + ", bbSeat=" + bbSeat);
                            
                            if ((preSeat == dealerSeat && nextSeat == sbSeat)
                                    || (preSeat == dealerSeat && sbSeat == dealerSeat && nextSeat == bbSeat)) {
                                // 补盲玩家在庄家和小盲位中间
                                rp.setBlindState(1);
                                logger.debug("seat(" + seat + ") between dealer and sb");
                            } else if ((preSeat == sbSeat && nextSeat == bbSeat)
                                    || (sbSeat == dealerSeat && preSeat == bbSeat && nextSeat == dealerSeat)) {
                                // 补盲玩家在小盲位和大盲位中间
                                rp.setBlindState(2);
                                logger.debug("seat(" + seat + ") between sb and bb");
                            }
                            rp.setCanPlay(true);
                        }
                        PublisherUtil.publisher(request, pusUser(0, playType));
                        return;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("Request_222_PlayType error", e);
        }
        PublisherUtil.publisher(request, pusUser(1, 0));
    }

    private byte[] pusUser(int status, int playType) {
        Object[][] objs = {
                {60, status,I366ClientPickUtil.TYPE_INT_1},    // 0成功 1失败
                {61, playType,I366ClientPickUtil.TYPE_INT_1}   // 0过庄 1补盲
        };
        byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_PLAY_TYPE);
        return bytes;
    }
    
}
