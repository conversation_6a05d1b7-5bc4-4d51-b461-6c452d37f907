package com.dzpk.processor.impl;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.dealer.Player;
import com.dzpk.processor.IProcessor;
import com.dzpk.record.LogManage;
import com.dzpk.work.Task;
import com.i366.cache.Cache;
import com.i366.constant.Constant;
import com.i366.model.player.RoomPersion;
import com.i366.model.player.RoomPlayer;
import com.i366.model.room.Room;
import com.i366.util.PublisherUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.Request;
import org.apache.logging.log4j.Logger;

import java.sql.SQLException;
import java.util.*;

public class Request_185_GameReport implements IProcessor {

    private Logger logger = LogUtil.getLogger(Request_185_GameReport.class);

    @Override
    public void handle(Task task) {

        Request request = (Request) task.getRequest();

        int roomId = task.getRoomId();
        int roomPath = task.getRoomPath();
        int userId = request.getUserId();

        int fail = 0;

        Room room = Cache.getRoom(roomId, roomPath);
        if (room == null) {
            PublisherUtil.publisher(request, pusUser(1));
            return;
        }

        Map<Integer, Player> allPlayers = room.getDealer().getPlayers();
        Map<Integer, Player> players = new HashMap<>();

        for (Integer uid : allPlayers.keySet()) { // 过滤掉带入为0的用户
            Player player = allPlayers.get(uid);
            if (player.getBringIn() != 0) {
                players.put(uid, player);
            }
        }

        List<Integer> watchers = new LinkedList<>();  //返回客户端的观众集合 暂时人数设置为20
        Iterator<Integer> rWatchers = room.getDealer().getWathers().iterator();
        this.add(rWatchers,watchers);
        
        int size = players.size();
        Integer[] userIdArray = new Integer[size];
        Integer[] plArray = new Integer[size];
        Integer[] bringArray = new Integer[size];
        Integer[] playingPersonArray = new Integer[size];
        Integer[] watcherIds = new Integer[watchers.size()];
        Integer[] watcherSexs = new Integer[watchers.size()];
        Integer[] watcherPlayStatus = new Integer[watchers.size()];
        Integer[] handArray = new Integer[size];

        String userStr = "";
        List<String> userArr = new ArrayList<String>();
        String headStr = "";
        String nickNameStr = "";
        String watcherHead = "";
        String watcherNickname = "";

        Set<Integer> playingPerson = new HashSet<>();  // 房间在玩玩家集合
        for (int i = 0; i < room.getRoomPersions().length; i++) {
            RoomPersion rp = room.getRoomPersions()[i];
            if (rp != null && rp.getOnlinerType() != -1) {
                playingPerson.add(rp.getUserId());
            }
        }

        Set<Integer> aheadLeave = new HashSet<>();
        for (int userid:room.getRoomPlayers().keySet()) {  //提前离桌的人
            RoomPlayer roomPlayer = room.getRoomPlayers().get(userid);
            if(roomPlayer != null && 1 == roomPlayer.getAheadLeave()){
                aheadLeave.add(userid);
            }
        }
        logger.debug("aheadLeave: " + aheadLeave.toString());

        // 打过牌的人的用户ID
        int i = 0;
        for (Integer uid : players.keySet()) {
            Player player = players.get(uid);
            userIdArray[i] = uid;
            plArray[i] = player.getEarn() + player.getInsurance();
            //logger.debug("[[res[2]=" + player.getBringIn());
            bringArray[i] = player.getBringIn();
            userArr.add(uid.toString());
            playingPersonArray[i] = playingPerson.contains(uid) ? 0 : 1;

            if(1 == playingPersonArray[i]){
                playingPersonArray[i] = aheadLeave.contains(uid) ? 2 : 1;

            }

            handArray[i] = player.getHandCnt();
            i++;
        }
        int num = i;

        // 加上观察者的用户ID然后再去查一次库
        Iterator<Integer> it = watchers.iterator();
        while (it.hasNext()) {
            userArr.add(it.next().toString());
        }

        // 拼ID串
        if (userArr.size() > 0) {
            for (i = 0; i < userArr.size(); i++) {
                userStr += userArr.get(i);
                if (i != userArr.size() - 1) {
                    userStr += ",";
                }
            }
        }

        String sp = "@%";
        logger.debug("user ids:" + userStr);
        if (userStr != "") {
            try {
                StringBuilder headBuilder = new StringBuilder();
                StringBuilder nicknameBuilder = new StringBuilder();
                Map<Integer, Object[]> userInfos = LogManage.getUserHeadNickname(userStr);

                // 拼打牌用户的数据
                for (int z = 0; z < userIdArray.length; z++) {
                    Object[] usr = userInfos.get(userIdArray[z]);
                    if (usr != null) {
                        headBuilder.append(usr[0]);
                        nicknameBuilder.append(usr[1]);
                        if (z != userIdArray.length - 1) {
                            headBuilder.append(sp);
                            nicknameBuilder.append(sp);
                        }
                    }
                }

                // 拼观察者的数据
                StringBuilder wHeadBuilder = new StringBuilder();
                StringBuilder wNicknameBuilder = new StringBuilder();
                int t = 0;
                for (Integer id : watchers) {
                    Object[] usr = userInfos.get(id);
                    watcherIds[t] = id;
                    watcherSexs[t] = Integer.valueOf(usr[2].toString());
                    if (usr[0].equals("1") || usr[0].equals("0")) {
                        usr[0] = -1;
                    }
                    wHeadBuilder.append(usr[0]);
                    wNicknameBuilder.append(usr[1]);

                    if (t != watchers.size() - 1) {
                        wHeadBuilder.append(sp);
                        wNicknameBuilder.append(sp);
                    }
                    t++;
                }

                headStr = headBuilder.toString();
                nickNameStr = nicknameBuilder.toString();
                watcherHead = wHeadBuilder.toString();
                watcherNickname = wNicknameBuilder.toString();
            } catch (SQLException e) {
                e.printStackTrace();
                fail = 1;
                logger.error("Request_185_GameReport error, get user head and nickname error");
            }
        } else {
            fail = 1;
        }

        // 无限牌局里 只有房主可以看到保险营收
        int insurance = room.getInsuranceChip();
        int showInsure = 0;

        // 下发备注
        String[] nickArr = nickNameStr.split(sp);
        String[] watherArr = watcherNickname.split(sp);
        logger.debug("userIdArray:" + Arrays.toString(userIdArray));

        String finalNickStr = "";
        for (int j = 0; j < userIdArray.length; j++) {
            finalNickStr += nickArr[j];
            if (j != nickArr.length - 1) {
                finalNickStr += sp;
            }
        }
        String finalWatcherNickStr = "";
        for (int j = 0; j < watcherIds.length; j++) {
            finalWatcherNickStr += watherArr[j];
            if (j != watherArr.length - 1) {
                finalWatcherNickStr += sp;
            }
            watcherPlayStatus[j] = 0;	// 默认为1 -- sunron
        }

        int roomLeftTime = -1;	// 牌局剩余时间(s), 未开始返回－1
        if (room.getRoomStatus() >= 1) {	// 牌局开始
            roomLeftTime = (int)((room.getGameBeginTime() + room.getMaxPlayTime() * 60000
                        - System.currentTimeMillis()) / 1000);
        }

        logger.debug("userid:" + userId);
        logger.debug("user ids:" + Arrays.toString(userIdArray));
        logger.debug("head:" + headStr);
        logger.debug("nickName:" + nickNameStr);
        logger.debug("nickName final:" + finalNickStr);
        logger.debug("plArray:" + Arrays.toString(plArray));
        logger.debug("bringArray:" + Arrays.toString(bringArray));
        logger.debug("watcherIds:" + Arrays.toString(watcherIds));
        logger.debug("watcherHead:" + watcherHead);
        logger.debug("watcherNickname:" + finalWatcherNickStr);
        logger.debug("watcherPlayStatus:" + Arrays.toString(watcherPlayStatus));
        logger.debug("playingPersonArray:" + Arrays.toString(playingPersonArray));
        logger.debug("watcherSexs:" + Arrays.toString(watcherSexs));
        logger.debug("room insure:" + insurance);
        logger.debug("show insure:" + showInsure);
        logger.debug("handArray:" + Arrays.toString(handArray));
        logger.debug("roomLeftTime:" + roomLeftTime);


        try {
            Object[][] objs = {
                    {60, fail,I366ClientPickUtil.TYPE_INT_1},                          // 0成功 1失败
                    {130, userIdArray,I366ClientPickUtil.TYPE_INT_4_ARRAY},            // 用户ID
                    {131, headStr,I366ClientPickUtil.TYPE_STRING_UTF16},               // 用户头像
                    {132, finalNickStr,I366ClientPickUtil.TYPE_STRING_UTF16},          // 用户昵称
                    {133, plArray,I366ClientPickUtil.TYPE_INT_4_ARRAY},                // 净胜筹码
                    {134, bringArray,I366ClientPickUtil.TYPE_INT_4_ARRAY},             // 总带入
                    {135, watcherIds,I366ClientPickUtil.TYPE_INT_4_ARRAY},             // 观察者用户ID
                    {136, watcherHead,I366ClientPickUtil.TYPE_STRING_UTF16},           // 观察者头像
                    {137, finalWatcherNickStr,I366ClientPickUtil.TYPE_STRING_UTF16},   // 观察者昵称
                    {138, playingPersonArray,I366ClientPickUtil.TYPE_INT_1_ARRAY},     // 再玩用户 0在玩 1不在 2提前离桌
                    {139, watcherSexs,I366ClientPickUtil.TYPE_INT_1_ARRAY},            // 观众性别
                    {140, insurance,I366ClientPickUtil.TYPE_INT_4},                    // 保险积分
                    {141, -1,I366ClientPickUtil.TYPE_INT_4},                           // 总基金
                    {142, showInsure,I366ClientPickUtil.TYPE_INT_4},                   // 显示保险池 0 显示 1 不显示
                    {143, handArray,I366ClientPickUtil.TYPE_INT_4_ARRAY},              // 实时战况手数
                    {144, watcherPlayStatus,I366ClientPickUtil.TYPE_INT_1_ARRAY},      // 观察者是否离开牌局 0 否 1是
                    {145, roomLeftTime,I366ClientPickUtil.TYPE_INT_4},                 // 牌局剩余时间

            };
            byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_REPORT);
            PublisherUtil.publisher(request, bytes);
            return;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("Request_185_GameReport error");
        }

    }

    private byte[] pusUser(int status) {
        Object[][] objs2 = {
                {60, status,I366ClientPickUtil.TYPE_INT_1}    // 创建是否成功：0成功 1失败
        };
        byte[] bytes = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_REPORT);
        return bytes;
    }

    private void add(Iterator<Integer> src,List<Integer> dst){
        if(null == src || null == dst)
            return;

        while(src.hasNext()){
            Integer w = src.next();
            if (null == w)
                continue;

            if (dst.contains(w))
                continue;

            dst.add(w);

            /*if (dst.size() >= Constant.MAX_AUDIENCE)
                break;*/
        }
    }
}