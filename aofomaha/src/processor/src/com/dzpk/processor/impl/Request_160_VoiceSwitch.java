package com.dzpk.processor.impl;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.cache.Cache;
import com.i366.constant.Constant;
import com.i366.model.room.Room;
import com.i366.util.PublisherUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.Request;
import org.apache.logging.log4j.Logger;

public class Request_160_VoiceSwitch implements IProcessor {

    private Logger logger = LogUtil.getLogger(Request_160_VoiceSwitch.class);

    @Override
    public void handle(Task task) {

        Request request = (Request) task.getRequest();
        int roomId = (Integer) task.getMap().get(131);
        int roomPath = (Integer) task.getMap().get(132);
        int voiceSwitch = (Integer) task.getMap().get(133);

        Room room = Cache.getRoom(roomId,roomPath);
        if(room == null){
            return ;
        }

        if(room.getSpectatorVoiceSwitch() == voiceSwitch){//客户端传错参数  无须变动
            logger.debug("Request_160_VoiceSwitch if==>" + room.getSpectatorVoiceSwitch() + "want ===>" + voiceSwitch);
            pusUser(1,room);
        }else{
            room.setSpectatorVoiceSwitch(voiceSwitch);
            logger.debug("Request_160_VoiceSwitch else==>" + room.getSpectatorVoiceSwitch() + "want ===>" + voiceSwitch);
            pusUser(0,room);
        }

    }

    public void pusUser(int status, Room room) {
        Object[][] objs = {
                {60, status,I366ClientPickUtil.TYPE_INT_1},
                {130, room.getSpectatorVoiceSwitch(),I366ClientPickUtil.TYPE_INT_1}
        };
        byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_ROOM_VOICE_SWITCH);
        PublisherUtil.send(room,bytes);
    }
}
