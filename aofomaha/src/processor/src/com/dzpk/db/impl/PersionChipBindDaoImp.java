/*
 * $RCSfile: PersionChipBindDaoImp.java,v $
 * $Revision: 1.1  $
 * $Date: 2012-12-29  $
 *
 * Copyright (C) 2005 Bettem, Inc. All rights reserved.
 *
 * This software is the proprietary information of Bettem, Inc.
 * Use is subject to license terms.
 */
package com.dzpk.db.impl;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import com.i366.model.player.RoomPersion;
import com.dzpk.component.repositories.mysql.dbpool.DBUtil;
import com.dzpk.db.dao.PersionChipBindDao;

/**
 * <p>Title: PersionChipBindDaoImp</p> 
 * <p>Description: </p> 
 * <p>Copyright: Copyright (c) 2006</p> 
 * <AUTHOR>
 * @version 1.0
 */
public class PersionChipBindDaoImp implements PersionChipBindDao {

	/* (non-Javadoc)
	 * @see com.dzpk.db.dao.PersionChipBindDao#initBind(int, int, int)
	 */
	public void initBind(int userId, int chip, int roomId) throws SQLException {
        Connection dbConnection = null;
        PreparedStatement ps = null;
        String selectSql = "select user_id from persion_chip_binding t where user_id = " + userId;
        String insertStatement = "insert into persion_chip_binding (user_id ,bind_chip,bind_room_id) values("+userId+","+chip+","+roomId+")";
        String updateSql = "update persion_chip_binding set bind_chip = "+chip+" ,bind_room_id = "+roomId+", LAST_UPDATE_TIME =  NOW() where user_id= "+userId;
        ResultSet rs  = null;
        try {
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(selectSql);
            rs = ps.executeQuery();
            if (rs.next()) {
            	 ps = dbConnection.prepareStatement(updateSql);
            }else {
            	ps = dbConnection.prepareStatement(insertStatement);
            }
            ps.execute();
        } catch (Exception se) {
            throw new SQLException("update user_details_info SQL Exception :" + se);
        } finally {
        	DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }
	}

	/* (non-Javadoc)
	 * @see com.dzpk.db.dao.PersionChipBindDao#updateChip(int, int)
	 */
	public void updatBindAll(RoomPersion[] roomPersions) throws SQLException {
        String updateSql = "update persion_chip_binding set bind_chip = ?, LAST_UPDATE_TIME =  NOW() where user_id= ?";
        Connection dbConnection = null;
        PreparedStatement ps = null;
        try {
            dbConnection = DBUtil.getConnection();
            for (int i = 0 ; i < roomPersions.length ; i ++) {
            	RoomPersion r = roomPersions[i];
            	if (r != null) {
            		 ps = dbConnection.prepareStatement(updateSql);
            		 int ii = 0 ;
            		 ps.setInt(++ ii, r.getNowcounma());
            		 ps.setInt(++ ii, r.getUserId());
            		 ps.execute();
            	}
            }
           
        } catch (Exception se) {
            throw new SQLException("update user_details_info SQL Exception :" + se);
        } finally {
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }
	}

	public void updateChip(int usId, int chip) throws SQLException {
        String updateSql = "update persion_chip_binding set bind_chip = "+chip+" , LAST_UPDATE_TIME =  NOW() where user_id= "+usId;
        Connection dbConnection = null;
        PreparedStatement ps = null;
        try {
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(updateSql);
            ps.execute();
        } catch (Exception se) {
            throw new SQLException("update user_details_info SQL Exception :" + se);
        } finally {
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }
	}
}

