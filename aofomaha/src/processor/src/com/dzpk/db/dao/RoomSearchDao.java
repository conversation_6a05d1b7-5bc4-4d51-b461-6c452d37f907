package com.dzpk.db.dao;

import com.dzpk.db.model.RoomSearchInfo;

import java.sql.SQLException;
import java.util.List;

/**
 * 房间列表中使用的room信息维护
 */
public interface RoomSearchDao {

    /**
     * 更新房间剩余座位数
     * @param updateNum 有人离开 +1  2有人坐下 -1
     * @param roomId 房间id
     * @return
     */
    int updateRoomSeat(int updateNum, int roomId);


    /**
     * 点击开始游戏
     * @param roomId 房间id
     * @return
     */
    int beginRoom(int roomId);


    /**
     * 关闭房间
     * @param roomId 房间id
     * @return
     */
    int closeRoom(int roomId);

    /**
     * 房间延时
     * @param roomId 房间id
     * @param delayTime 延时的时间(单位:秒)
     * @return
     */
    int delayRoomPlayTime(int roomId, int delayTime);


    /**
     * 重启牌局需要清空room_research表中的数据
     * @param roomServerNumber 服务器编号 唯一
     */
    void clearAllRoom(String roomServerNumber) throws SQLException;

    /**
     * 获取指定服务器创建的牌局
     * @param roomServerNumber
     * @return
     * @throws SQLException
     */
    List<RoomSearchInfo> getAllRoomByServerNumber(String roomServerNumber) throws SQLException;

    /**
     * 查询自动开房计划创建的，符合自动解散条件的空房间。  （游戏开始30min后）
     * @return
     */
    List<RoomSearchInfo> queryEmptyPlanRoomIds(int roomServerNumber);
}
