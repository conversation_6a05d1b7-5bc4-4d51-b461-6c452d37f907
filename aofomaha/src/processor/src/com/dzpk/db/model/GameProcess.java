package com.dzpk.db.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;

import java.util.List;

@Getter
@Setter
@Builder
public class GameProcess {
    //房间id
    private Integer roomId;
    //手数code1：preflop，2flop，3turn，4river
    private Integer shoushuCode;
    //底池
    private Integer chip;
    //公共牌
    private List<Integer> publicCard;
    //在位用户人数
    private Integer users;
    //详细记录的id
    private List<ObjectId> singleProcess;
    //详细的记录
    private List<SingleProcess> singleProcessList;
    //第几手
    private Integer version;
}
