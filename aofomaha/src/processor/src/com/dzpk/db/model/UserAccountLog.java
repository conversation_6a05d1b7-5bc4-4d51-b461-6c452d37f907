package com.dzpk.db.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.sql.Timestamp;

/**
 * 玩家账户变动日志bean
 */
@Getter
@Setter
@ToString
public class UserAccountLog {

    private int userId;
    private int type; // 类型 1-查看未发的牌消费,2-用户操作延时,3-保险操作延时,4-击中jp,5-带入筹码,6-比赛结束赢取,7-mtt报名费,8-mtt重购费退还,9-mtt报名费费退还,10-mtt比赛结束赢取,11-mtt猎人赛赢取,12-mtt重购费,13-兑换王者币,14-使用表情花费,15-获得基金,16-sng报名费,17-sng报名费退还,18-修改头像费用,19-修改昵称费用,20-设置头像赠送费用,21-俱乐部基金充值,22-破隐消耗,23-分享赚金豆返豆
    private int changeSource; //变化来源 1-api,2-room(aof),3-omaha,4-bp,5-sng,6-mtt,7-yunying,8-aof
    private int currentChip;  //当前金豆数量
    private int changeChip;   //变化金豆数量,当前要增加或减少的数值,增加为正,减少为负
    private int notExtractChip; //当前不可提余额
    private int changeNotExtractChip; //变化不可提余额
    private int plCount; //当前战绩流水总和
    private int changePlCount; //变化战绩流水总和
    private String description; //描述
    private String externalId;  //外部id
    private int opId;           //操作人id(系统为-1)
    private Timestamp createTime;
}
