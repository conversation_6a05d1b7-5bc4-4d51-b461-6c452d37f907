package com.dzpk.record;


import com.dzpk.commission.fee.TotalRoomFee;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.mq.activemq.ActiveMQProducerService;
import com.dzpk.component.repositories.mongo.MongodbService;
import com.dzpk.db.dao.UserInfoDao;
import com.dzpk.db.impl.UserInfoDaoImp;
import com.dzpk.dealer.Player;
import com.dzpk.dealer.statistics.Allin;
import com.dzpk.dealer.statistics.Main;
import com.dzpk.jackpot.PlayerJpRewardDetail;
import com.i366.model.room.Room;
import com.mongodb.BasicDBObject;
import com.mongodb.MongoClient;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.FindOneAndUpdateOptions;
import net.sf.json.JSONObject;
import org.apache.logging.log4j.Logger;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

import javax.jms.TextMessage;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 牌局战绩记录模型
 *
 * <AUTHOR>
 */
public class GameReportModel {
    private static final Logger logger = LogUtil.getLogger(GameReportModel.class);
    private static final String split = "@%";
    private static final String sp = ",";
    private static final String db = "dzpk";
    private static final String _recordCollection = "game_record";
    private static final String _detailCollection = "game_detail";
    private static final String _dataRoomCollection = "game_data_room";
    private static final String _dataOmahaMainCollection = "aof_game_data_main";
    private static final String _dataOmahaAllinCollection = "aof_game_data_allin";
    private static final String _dataProfitCollection = "aof_game_data_profit";
    /**
     * 保存牌局记录
     *
     * @param room
     * @param userNick
     * @param userIcon
     * @param userIdArray
     * @param plArray
     * @param handArray
     * @param bringArray
     * @param allBring
     * @param userNickName
     * @param maxPot
     * @param records
     * @param insuranceArray
     * @param clubMap
     * @param clubIdArray
     */
    public static void saveGame(Room room, String userNick, String userIcon, List<Integer> userIdArray,
                                List<Integer> plArray, List<Integer> plFeeArray, List<Integer> handArray, List<Integer> bringArray,
                                int allBring, String userNickName, int maxPot,
                                ArrayList<Object[]> records, List<Integer> clubIdArray, List<Integer> clubFeeArray, HashMap<Integer,Integer> clubMap,
                                List<Integer> insuranceArray, List<Integer> jpBetArray, List<Integer> jpRealBetArray,
                                List<Integer> jpRewardArray, List<Double> jpRewardFeeArray,
                                List<Integer> vpArray,List<Integer> tribeIdArray,List<Integer> tribeFeeArray,List<String> clubNameArray,List<Integer> tribeIdRepeatArray) throws SQLException {
        // 获取房主的昵称和头像 还有所有用户的头像
        String ownerName = "";
        String ownerHead = "";
        Map<Integer, Object[]> userInfos = new HashMap<>();
        String ownerId = String.valueOf(room.getOwner());
        try {
            boolean includeOwnerId = false;
            StringBuilder userIdStr = new StringBuilder();
            for (int i = 0; i < records.size(); i++) {
                Object[] obj = records.get(i);
                String userId = obj[0].toString();
                if (userId.equals(ownerId)) {
                    includeOwnerId = true;
                }
                userIdStr.append(userId);
                if (i != records.size() - 1) {
                    userIdStr.append(",");
                }
            }

            // 查询的用户id必须包括房主的user id 房主有可能自己开了房然后不打
            if (!includeOwnerId) {
                userIdStr.append(",").append(ownerId);
            }

            userInfos = LogManage.getUserHeadNickname(userIdStr.toString());
            Object[] user = userInfos.get(room.getOwner());
            ownerHead = user[0].toString();
            ownerName = user[1].toString();
        } catch (SQLException e) {
            logger.error("get user name and head fail!!!");
        } catch (Exception e) {
            logger.error("save game report data error!!!");
        }

        // 所有用户头像
        StringBuilder headStr = new StringBuilder();
        for (int i = 0; i < userIdArray.size(); i++) {
            //userIds.add(userIdArray[i]);
            Object[] user = userInfos.get(userIdArray.get(i));
            headStr.append(user[0].toString());
            if (i != userIdArray.size() - 1) {
                headStr.append(split);
            }
        }

        MongoClient mongo = null;
        try {
            mongo = MongodbService.getMongoInstance();
            MongoDatabase database = mongo.getDatabase(db);
            MongoCollection<Document> recordCollection = database.getCollection(_recordCollection);
            MongoCollection<Document> detailCollection = database.getCollection(_detailCollection);
            MongoCollection<Document> dataAllinCollection = database.getCollection(_dataOmahaAllinCollection);
            MongoCollection<Document> dataRoomCollection = database.getCollection(_dataRoomCollection);
            MongoCollection<Document> dataMainCollection = database.getCollection(_dataOmahaMainCollection);
            long endTime = System.currentTimeMillis();
            // 牌局详情记录
            Document detailRecord = new Document();
            detailRecord.put("_id", new ObjectId(room.getRoomUUID()));
            detailRecord.put("room_id", room.getRoomId());
            detailRecord.put("room_name", room.getName());
            detailRecord.put("room_path", room.getRoomPath());
            detailRecord.put("time", room.getGameBeginTime());
            detailRecord.put("end_time", endTime);
            detailRecord.put("tribe_id", tribeIdArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
            detailRecord.put("tribe_id_repeat", tribeIdRepeatArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
            detailRecord.put("owner_id", room.getOwner());
            detailRecord.put("user_nick", userNick);
            detailRecord.put("user_icon", userIcon);
            detailRecord.put("user_head", headStr.toString());
            detailRecord.put("user_id_array", userIdArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
            detailRecord.put("pl_array", plArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
            detailRecord.put("hand_array", handArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
            detailRecord.put("bring_array", bringArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
            detailRecord.put("all_bring", allBring);
            detailRecord.put("user_nick_name", userNickName);
            detailRecord.put("max_pot", maxPot);
            detailRecord.put("creator", ownerId);
            detailRecord.put("total_hand", room.getStage());
            detailRecord.put("qianzhu", room.getQianzhu());                   // 前注
            detailRecord.put("insurance", room.getInsurance());               // 保险
            detailRecord.put("blind", String.valueOf(room.getManzhu()) + "/" + String.valueOf(room.getDamanzhu())); // 盲注
            detailRecord.put("club_name",clubNameArray.toString().replace("[", "").replace("]", "").replace(" ", ""));

            detailRecord.put("room_mode", room.getRoomMode());
            if (room.getInsurance() == 1) {
                detailRecord.put("insurance_pool", room.getInsuranceChip());  // 保险收支
            }

            // logger.info("clubId=" + clubIdArray);
            detailRecord.put("club_id", clubIdArray.toString().replace("[", "").replace("]", "").replace(" ", ""));                 // 社区id（重复的）

            List<Integer> clubIdlist = new ArrayList<Integer>(clubMap.keySet());    // 社区id（去重后的）
            List<Integer> clubPLlist = new ArrayList<Integer>(clubMap.values());
            
            Integer[] clubIds = new Integer[clubIdlist.size()];
            Integer[] clubPLs = new Integer[clubIdlist.size()];
            for (int i = 0; i < clubIdlist.size(); i++) {
                clubIds[i] = clubIdlist.get(i);
                clubPLs[i] = clubPLlist.get(i);
            }
            
            detailRecord.put("club_id_no_repeat", arrayToString(clubIds));     // 社区id
            detailRecord.put("club_pl", arrayToString(clubPLs));               // 社区盈亏
            detailRecord.put("all_insurance", insuranceArray.toString().replace("[", "").replace("]", "").replace(" ", ""));           // 所有个人保险
            // logger.info("clubIdArray====" + arrayToString(clubIdArray) + "insuranceArray==" + arrayToString(insuranceArray));

            detailRecord.put("jackpot",room.isJackPotSwitch() ? 1 : 0);//房间是否开启jackpot
            detailRecord.put("jp_array", jpBetArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
            detailRecord.put("jp_real_array", jpRealBetArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
            detailRecord.put("jp_pl_array", jpRewardArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
            StringBuilder jpUserIds = new StringBuilder();
            StringBuilder jpProcerType = new StringBuilder();
            StringBuilder jpHands = new StringBuilder();
            StringBuilder jpReward = new StringBuilder();
            StringBuilder jpRewardFee = new StringBuilder();
            if(null != room.getJackpotService()) {
                List<PlayerJpRewardDetail> list = room.getJackpotService().playerRewardDetailLst();
                if (null != list && !list.isEmpty()) {
                    for (PlayerJpRewardDetail reward : list) {
                        if(jpUserIds.toString().length() ==  0){
                            jpUserIds.append(reward.getUserId());
                            jpProcerType.append(reward.getProcerType());
                            jpHands.append(reward.getHandNo());
                            jpReward.append(reward.getReward());
                            jpRewardFee.append(reward.getRewardFee());
                        }else{
                            jpUserIds.append(",").append(reward.getUserId());
                            jpProcerType.append(",").append(reward.getProcerType());
                            jpHands.append(",").append(reward.getHandNo());
                            jpReward.append(",").append(reward.getReward());
                            jpRewardFee.append(",").append(reward.getRewardFee());
                        }
                    }
                }
            }
            detailRecord.put("jp_user_array", jpUserIds.toString());
            detailRecord.put("jp_poker_type", jpProcerType.toString());
            detailRecord.put("jp_hand_array", jpHands.toString());
            detailRecord.put("jp_reward_array", jpReward.toString());
            detailRecord.put("jp_reward_fee_array", jpRewardFee.toString());
            detailRecord.put("vp", room.getVp());
            detailRecord.put("vp_array", vpArray.toString().replace("[", "").replace("]", "").replace(" ", ""));

            /** 服务费 */
            detailRecord.put("jp_pl_fee_array", jpRewardFeeArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
            detailRecord.put("pl_fee_array", plFeeArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
            detailRecord.put("commission_club", clubFeeArray.toString().replace("[", "").replace("]", "").replace(" ", ""));                 // 社区id（重复的）
            detailRecord.put("commission_tribe", tribeFeeArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
            TotalRoomFee totalRoomFee = (TotalRoomFee)room.getFeeService().getRoomFeeByType(2);
            detailRecord.put("commission_sys", null== room.getFeeService()?0.00d:room.getFeeService().sysFee(totalRoomFee));
            detailRecord.put("commission_income", null== room.getFeeService()?0.00d:room.getFeeService().feeIncome(totalRoomFee));
            detailRecord.put("room_charge_total", room.getRoomChargeTotal());
            detailRecord.put("club_room_type", room.getClubRoomType());
            //保存战绩
            detailCollection.insertOne(detailRecord);
            String roomId = detailRecord.getObjectId("_id").toString();

            // 房间记录
            List<Document> list = new ArrayList<>();

            for (Object[] obj : records) {
                String tempOwnerName = ownerName;
                int userId = (Integer) obj[0];
                int pl = (Integer) obj[1];

                tempOwnerName = tempOwnerName.isEmpty() ?ownerName : tempOwnerName;

                Document roomRecord = new Document();
                roomRecord.put("user_id", userId);
                roomRecord.put("room_id", roomId);
                roomRecord.put("room_name", room.getName());
                roomRecord.put("blind", String.valueOf(room.getManzhu()) + "/" + String.valueOf(room.getDamanzhu()));
                roomRecord.put("max_play_time", room.getMaxPlayTime());
                roomRecord.put("time", room.getGameBeginTime());
                roomRecord.put("end_time", endTime);
                roomRecord.put("profit_lose", pl);
                roomRecord.put("owner_name", tempOwnerName);
                roomRecord.put("owner_head", ownerHead);
                roomRecord.put("qianzhu", room.getQianzhu());               // 前注
                roomRecord.put("insurance", room.getInsurance());           // 保险
                roomRecord.put("status", 1);
                roomRecord.put("room_path", room.getRoomPath());
                //*********jackpot*********//
                roomRecord.put("jackpot", null==room.getJackpotService()?0: room.getJackpotService().getTotalJpBet(userId));//玩家在当前牌局向彩池贡献总额
                roomRecord.put("jackpot_real", null==room.getJackpotService()?0: room.getJackpotService().getTotalRealJpBet(userId));//玩家在当前牌局实际向彩池贡献总额
                roomRecord.put("jackpot_pl", null==room.getJackpotService()?0: room.getJackpotService().getTotalJpReward(userId));//玩家在当前牌局的 JackPot 中奖额
                roomRecord.put("jackpot_pl_fee", null==room.getJackpotService()?0: room.getJackpotService().getCurrentJpRewardFee(userId));//玩家在当前牌局的 JackPot 中奖额的服务费

                int type = 0;
                type = (Integer) obj[4];
                String clubName = (String) obj[5];
                int clubId = (Integer) obj[6];

                roomRecord.put("type", type);
                roomRecord.put("club_name", clubName);
                roomRecord.put("club_id", clubId);
                roomRecord.put("club_room_type", room.getClubRoomType()!=0 ? 0 : 1);
                list.add(roomRecord);
            }
            recordCollection.insertMany(list);

            // 存储统计数据
            Map<Integer, Player> players = room.getDealer().getPlayers();
            if (players.size() > 0) {
                List<Document> dataList = new ArrayList<>();
                for (Integer userId : players.keySet()) {
                    Player player = players.get(userId);
                    if (player.getBringIn() > 0) {
                        Document row = new Document();
                        row.put("user_id", userId);
                        row.put("room_id", roomId);
                        row.put("time", room.getGameBeginTime());
                        row.put("end_time", endTime);
                        row.put("tanpai_earn", player.getTanpaiEarn());
                        // 记录数量
                        row.put("manpai_cnt", player.getManpaiCnt());
                        row.put("fanpai_cnt", player.getFanpaiCnt());
                        row.put("zhuanpai_cnt", player.getZhuanpaiCnt());
                        row.put("hepai_cnt", player.getHepaiCnt());
                        row.put("tanpai_cnt", player.getTanpaiCnt());
                        // 记录赢的数量
                        row.put("manpai_win", player.getManpaiWinCnt());
                        row.put("fanpai_win", player.getFanpaiWinCnt());
                        row.put("zhuanpai_win", player.getZhuanpaiWinCnt());
                        row.put("hepai_win", player.getHepaiWinCnt());
                        row.put("tanpai_win", player.getTanpaiWinCnt());
                        // 记录入池数量
                        row.put("in_pool_cnt", player.getInPoolCnt());
                        row.put("pool_win", player.getInPoolWinCnt());
                        row.put("pool_hand_cnt", player.getHandCnt());
                        // 记录赢牌数
                        row.put("win_cnt", player.getWinCnt());
                        // 用户总手数
                        row.put("user_hand_cnt", player.getHandCnt());
                        // 记录房间总手数
                        row.put("total_hand_cnt", room.getStage());
                        //记录房间个人盈利
                        row.put("earn", player.getEarn());
                        row.put("per_cnt", player.getPerCnt());
                        row.put("total_allin_cnt", player.getAllCnt());
                        row.put("total_allin_win_cnt", player.getAllinWinCnt());
                        //记录激进程度数据
                        row.put("af_xiazhu_cnt",player.getAfXiazhuCnt());
                        row.put("af_genzhu_cnt",player.getAfGenzhuCnt());
                        row.put("af_jiazhu_cnt",player.getAfJiazhuCnt());
                        //记录翻牌前再加注次数
                        row.put("bet3_cnt",player.getBet3());
                        //记录cbet和bcbet
                        row.put("cbet",player.getCbet());
                        row.put("bcbet",player.getbCbet());
                        //记录带入
                        row.put("bring_in",player.getBringIn());
                        //记录俱乐部房间服务费
                        row.put("club_room_user_charge",player.getClubRoomUserCharge());
                        dataList.add(row);
                    }
                }
                dataRoomCollection.insertMany(dataList);

                FindOneAndUpdateOptions options = new FindOneAndUpdateOptions();
                options.upsert(true);
                List<Integer> userIdArr = new ArrayList<>();
                for (Integer uid : players.keySet()) {
                    Player player = players.get(uid);
                    if (player.getHandCnt() > 0) {
                        saveData(uid, player, dataMainCollection, dataAllinCollection, options
                                ,room.getClubRoomType()!=0);
                        userIdArr.add(uid);
                    }
                }

                updateProfitDataAsyn(roomId, userIdArr);// 重新统计盈利数据
                
            } else {
                logger.debug("data report empty!!! ");
            }
            logger.debug("save game report successfully!");
        } catch (Exception e) {
            logger.error("save game report error:", e);
        } finally {
            MongodbService.close(mongo);
        }
    }

    /**
     * 数组转字符串
     *
     * @return
     */
    private static String arrayToString(Integer[] ints) {
        String res = "";
        for (int i = 0; i < ints.length; i++) {
            // logger.info("arrayToString:" + i + "==" + ints[i]);
            res += ints[i];
            if (i != ints.length - 1) {
                res += sp;
            }
        }

        return res;
    }

    /**
     * 保存数据
     *
     * @param uid
     * @param player
     * @param dataMainCollection
     * @param dataAllinCollection
     * @param options
     */
    private static void saveData(int uid, Player player, MongoCollection<Document> dataMainCollection,
                                 MongoCollection<Document> dataAllinCollection,
                                 FindOneAndUpdateOptions options, boolean clubRoomType) {
        // 个人统计数据
        Bson fil = new Document("user_id", uid);
        Document doc = new Document(
                "$inc",
                new Document(Main.HAND_CNT, player.getHandCnt())                // 总手数
                        .append(Main.SERIES_CNT, 1)                             // 总局数
                        .append(Main.EARN, player.getEarn())                    // 总盈利
                        .append(Main.POOL_CNT, player.getInPoolCnt())           // 入池数
                        .append(Main.POOL_WIN_CNT, player.getInPoolWinCnt())    // 入池赢牌数
                        .append(Main.POOL_HAND_CNT, player.getHandCnt())        // 总手数(入池率用的)
                        .append(Main.PER, player.getPerCnt())                   // 翻牌前加注
                        .append(Main.STEAL, player.getStealCnt())               // 偷盲数
                        .append(Main.BSTEAL, player.getBStealCnt())             // 可偷盲数
                        .append(Main.CBET, player.getCbet())                    // cbet次数
                        .append(Main.BCBET, player.getbCbet())                  // 可cbet次数
                        .append(Main.TANPAI_CNT, player.getTanpaiCnt())         // 摊牌数
                        .append(Main.FANPAI_CNT, player.getFanpaiCnt())         // 翻牌数
                        .append(Main.USERHANDCNT, player.getHandCnt())          // 相对手数
                        .append(Main.AF_JIAZHU_CNT, player.getAfJiazhuCnt())    // af加注手数
                        .append(Main.AF_GENZHU_CNT, player.getAfGenzhuCnt())    // af跟注手数
                        .append(Main.AF_XIAZHU_CNT, player.getAfXiazhuCnt())    // af下注手数
                        .append(Main.BRING_IN, player.getBringIn())             // 总带入
                        .append(Main.BRING_SERIES_CNT, 1)                       // 总带入局数(重新统计,不与之前总局数公用)
                        .append(Main.BET3_CNT, player.getBet3())                // 翻牌前再加注次数
                        .append(Main.CLUB_ROOM_CHARGE_TOTAL, player.getClubRoomUserCharge())
                        .append(Main.CLUB_ROOM_PL_TOTAL,clubRoomType ? player.getEarn() : 0)


        );
        dataMainCollection.findOneAndUpdate(fil, doc, options);

        // allin数据
        Document docAllin = new Document(
                "$inc",
                // todo user_id 空
                new Document(Allin.TOTAL_CNT, player.getAllCnt())               // allin数
                        .append(Allin.WIN_CNT, player.getAllinWinCnt())         // allin赢牌数
                        .append(Allin.LOSE_CNT, player.getAllinLoseCnt())       // allin输牌数
                        .append(Allin.EARN, player.getAllinEarn())              // allin盈利
        );
        dataAllinCollection.findOneAndUpdate(fil, docAllin, options);
    }

    /**
     * 异步更新数据
     *
     * @param userIdArr
     */
    private static void updateProfitDataAsyn(String roomId, List<Integer> userIdArr) {
        logger.debug("updating profit data...");
        String userIds = "";
        for (int i = 0; i < userIdArr.size(); i++) {
            userIds += userIdArr.get(i);
            if (i != userIdArr.size() - 1) {
                userIds += "@";
            }
        }

        ActiveMQProducerService mqProducer = new ActiveMQProducerService("httpQueue");
        if (null != mqProducer) {
            try {
                mqProducer.setPriority(9);
                TextMessage message = mqProducer.getSession()
                        .createTextMessage("update profit record");
                message.setStringProperty("msgType", "updateOmahaAofReport");
                message.setStringProperty("userIds", userIds);
                message.setStringProperty("roomId", roomId);

                if (mqProducer.sendMsg(message)) {
                    logger.info("update mongodb record successfully");
                }
                mqProducer.closeConnection();
            } catch (Exception e1) {
                e1.printStackTrace();
            }
        }
    }
}
