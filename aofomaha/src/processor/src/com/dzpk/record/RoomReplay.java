package com.dzpk.record;

// 牌局回放
public class RoomReplay {
    
    private String id;              // 记录id
    private int stage = 0;          // 房间内的第几手
    private int startTime;         // 牌局开始时间(s)
    private int endTime;           // 牌局结束时间(s)
    private String handId;          // 房间手数记录id
    /** 
     * 0: 翻公共牌前
     * 1: 发三张公共牌
     * 2: 发第四张公共牌
     * 3: 发第五张公共牌
     */
    private AnteAction[] anteActions = new AnteAction[4];
    public static final String[] anteActionName = {"PREFLOP", "FLOP", "TURN", "RIVER"};
    private RoomRecord roomRecord;  // 牌局记录
    private int progress = 0;       // 当前处在那个阶段 preFlop->flop->turn->river
    private int roomId;             // 房间id
    private String roomName;        // 房间名
    private int owner;              // 房主id
    private int straddle;           // straddle
    private Integer[] winners;      // 最后的赢家
    private int roomType;
    
    public String getId() {
        return id;
    }
    public void setId(String id) {
        this.id = id;
    }
    public int getStage() {
        return stage;
    }
    public void setStage(int stage) {
        this.stage = stage;
    }
    public int getStartTime() {
        return startTime;
    }
    public void setStartTime(int startTime) {
        this.startTime = startTime;
    }
    public void setStartTime(long startTime) {
        this.startTime = (int) startTime;
    }
    public String getHandId() {
        return handId;
    }
    public void setHandId(String handId) {
        this.handId = handId;
    }
    public int getEndTime() {
        return endTime;
    }
    public void setEndTime(int endTime) {
        this.endTime = endTime;
    }
    public void setEndTime(long endTime) {
        this.endTime = (int) endTime;
    }
    public RoomRecord getRoomRecord() {
        return roomRecord;
    }
    public void setRoomRecord(RoomRecord roomRecord) {
        this.roomRecord = roomRecord;
    }
    public int getProgress() {
        return progress;
    }
    public void setProgress(int progress) {
        this.progress = progress;
    }
    public void addProgress(int progress) {
        this.progress += progress;
    }
    public AnteAction[] getAnteActions() {
        return anteActions;
    }
    public void setAnteActions(AnteAction[] anteActions) {
        this.anteActions = anteActions;
    }
    public AnteAction getAnteAction() {
        return anteActions[progress];
    }
    public void setAnteAction(AnteAction anteAction) {
        this.anteActions[progress] = anteAction;
    }
    public void setAnteAction(int progress, AnteAction anteAction) {
        this.anteActions[progress] = anteAction;
    }
    public int getRoomId() {
        return roomId;
    }
    public void setRoomId(int roomId) {
        this.roomId = roomId;
    }
    public String getRoomName() {
        return roomName;
    }
    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }
    public int getOwner() {
        return owner;
    }
    public void setOwner(int owner) {
        this.owner = owner;
    }
    public int getStraddle() {
        return straddle;
    }
    public void setStraddle(int straddle) {
        this.straddle = straddle;
    }
    public Integer[] getWinners() {
        return winners;
    }
    public void setWinners(Integer[] winners) {
        this.winners = winners;
    }
    public int getRoomType() {
        return roomType;
    }
    public void setRoomType(int roomType) {
        this.roomType = roomType;
    }
    
}
