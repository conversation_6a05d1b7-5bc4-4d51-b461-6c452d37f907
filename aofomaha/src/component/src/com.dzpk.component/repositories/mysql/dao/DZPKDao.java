/*
 * $RCSfile: ConnDB.java,v $
 * $Revision: 1.1  $
 * $Date: 2012-8-7  $
 *
 * Copyright (C) 2005 Bettem, Inc. All rights reserved.
 *
 * This software is the proprietary information of Bettem, Inc.
 * Use is subject to license terms.
 */
package com.dzpk.component.repositories.mysql.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <p>Title: ConnDB</p> 
 * <p>Description: </p> 
 * <p>Copyright: Copyright (c) 2006</p> 
 * <AUTHOR>
 * @version 1.0
 */
public interface DZPKDao {
	
	/**
	 * 获取PreparedStatement对象
	 * @return
	 * @throws SQLException 
	 */
	public PreparedStatement createStam(String sql,Object[] prams) throws SQLException;
	
	 /**
     * 关闭结果集
     *
     * @param result 要关闭的结果集对象
     */
    public void closeResultSet(ResultSet result);
    /**
     * 关闭PreparedStatement
     *
     * @param stam 要关闭的PreparedStatement对象
     */
    public void closePreparedStatement(PreparedStatement stam);
    /**
     * 关闭Connection
     *
     * @param conn 要关闭的Connection对象
     */
    public void closeConnection(Connection conn);
    /**
     * 清除连接对象
     */
    public void clear(ResultSet result, PreparedStatement stam, Connection conn);
    
}

