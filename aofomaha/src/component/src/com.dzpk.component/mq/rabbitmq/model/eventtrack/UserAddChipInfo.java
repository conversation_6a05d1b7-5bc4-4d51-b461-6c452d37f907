package com.dzpk.component.mq.rabbitmq.model.eventtrack;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 玩家带入积分信息
 */
@Data
@ToString
public class UserAddChipInfo extends EventTrackBasicInfo implements Serializable {

    private int extractChip;  //可提取账户金豆数量
    private int notExtractChip;  //不可以提取账户金豆数量
    private int extractChangeChip;  //可提取账户变化金豆数量
    private int notExtractChangeChip;  //不可以提取账户金豆数量
    private int clubId;  //俱乐部id
    private int tribeId;  //联盟id
    private String clubName; //俱乐部名称
    private String tribeName; //联盟名称

    private String ip;         // ip
    private double longitude;  // GPS经度信息
    private double latitude;   // GPS纬度信息
    private int seatSize;      // 座位号
    private String imei;       // 机器码
    private int isVirtual;  // 是否为模拟器

}
