package com.dzpk.common.utils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.core.config.ConfigurationSource;
import org.apache.logging.log4j.core.config.Configurator;

import java.io.File;
import java.io.FileInputStream;
import java.net.URL;
import java.net.URLDecoder;

public class LogUtil {

    static {
        URL url = LogUtil.class.getProtectionDomain().getCodeSource().getLocation();

        try {
            String filePath = URLDecoder.decode(url.getPath(), "utf-8");// 转化为utf-8编码，支持中文
            if (filePath.endsWith(".jar")) {// 可执行jar包运行的结果里包含".jar"
                // 获取jar包所在目录
                filePath = filePath.substring(0, filePath.lastIndexOf("/") + 1);
            }

            File file = new File(filePath + "/log4j2.xml");
            final ConfigurationSource source = new ConfigurationSource(new FileInputStream(file));
            Configurator.initialize(null, source);
        } catch (Exception e) {
            System.err.println("Load log4j2.xml fail! ");
            e.printStackTrace();
        }
    }

    public static Logger getLogger(Class<?> clazz) {
        String category = null;
        if (null != clazz) {
            category = clazz.getName();
        }

        return LogManager.getLogger(category);
    }

    public static Logger getLogger(String categoryName) {
        String category = null;
        if (null != categoryName && !"".equals(categoryName.trim())) {
            category = categoryName.trim();
        }

        return LogManager.getLogger(category);
    }
}
