package com.dzpk.common.token;

public interface ITokenChecker {
    /**
     * 校验Token
     * @param   token          ticket,必填
     * @param   reqUserId      当前使用此token的用户ID，可选，
     *                         >0则需校验与此token生成的userID是否匹配
     * @param   reqDeviceId    当前使用此token请求的设备ID，可选
     *                         不为空，则需要校验与此token生成的设备ID是否匹配
     * @return  userId
     *     <=0 表示失败
     *    其他 表示成功，对应的用户ID
     */
    int verify(String token, Integer reqUserId, String reqDeviceId);
}
