package com.dzpk.common.token.service;


import com.dzpk.common.token.vo.ICachedToken;

public interface ICacheService<T> {
    /**
     * 获取token对象
     * @param token
     * @return
     */
    ICachedToken<T> getToken(String token);

    /**
     * 创建token对象
     * @param token
     * @param userInfo
     * @param deviceId
     * @return
     */
    ICachedToken<T> addToken(String token, T userInfo, String deviceId);

    /**
     * 将token设置为无效
     * @param token
     */
    void chgInvalid(ICachedToken<T> token);
}
