package com.dzpk.commission.repositories.mysql.model;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class ClubFundHistoryPo {
    private int id;                 // 主键ID
    private int clubId;             // 所属社区主键ID
    private String operatorName;    // 操作者姓名
    private int userId;             // 玩家ID
    private String userName;        // 玩家昵称
    private int amount;             // 基金金额
    private Timestamp createTime;   // 创建时间
    private int type;               // 记录类型 (1:待审核; 2:已发放; 0:已拒绝; 3:主动发放; 4:充值; 6:社区jp注入; 7:同盟jp注入; 8社区购买)
    private int msgId;              // 社区请求列表中的主键ID
    private int opId;				// 操作人id
    private double clubProfit;      // 俱乐部累计分润
}
