package com.dzpk.commission.repositories.mysql.model;

public class ClubFeeConfig {

    private int clubId;  //俱乐部id
    private int tribeId; //同盟id
    private double clubConfig;  //俱乐部比例
    private double tribeConfig; //同盟比例
    private double sysConfig;   //系统比例

    public int getClubId() {
        return clubId;
    }

    public void setClubId(int clubId) {
        this.clubId = clubId;
    }

    public int getTribeId() {
        return tribeId;
    }

    public void setTribeId(int tribeId) {
        this.tribeId = tribeId;
    }

    public double getClubConfig() {
        return clubConfig;
    }

    public void setClubConfig(double clubConfig) {
        this.clubConfig = clubConfig;
    }

    public double getTribeConfig() {
        return tribeConfig;
    }

    public void setTribeConfig(double tribeConfig) {
        this.tribeConfig = tribeConfig;
    }

    public double getSysConfig() {
        return sysConfig;
    }

    public void setSysConfig(double sysConfig) {
        this.sysConfig = sysConfig;
    }

    @Override
    public String toString() {
        return "ClubFeeConfig{" +
                "clubId=" + clubId +
                ", tribeId=" + tribeId +
                ", clubConfig=" + clubConfig +
                ", tribeConfig=" + tribeConfig +
                ", sysConfig=" + sysConfig +
                '}';
    }
}
