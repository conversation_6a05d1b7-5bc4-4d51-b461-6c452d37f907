package com.dzpk.commission.repositories.mysql.impl;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.record.ReplayCacheImpl;
import com.dzpk.component.repositories.mysql.dbpool.DBUtil;
import com.dzpk.commission.repositories.mysql.ClubTribeDao;
import com.dzpk.commission.repositories.mysql.model.ClubFeeConfig;
import com.dzpk.commission.repositories.mysql.model.ClubTribeModel;
import org.apache.logging.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.util.*;

public class ClubTribeDaoImpl implements ClubTribeDao {

    private static Logger logger = LogUtil.getLogger(ClubTribeDaoImpl.class);

    @Override
    public ClubTribeModel getClubTribeModelByUserId(int userId) {

        Connection dbConnection = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String tribeSql = "select cr.id clubId,cr.creator clubCreator,cr.name clubName,tr.creator tribeCreator,tr.id tribeId," +
                "tr.tribe_name tribeName,cr.profit cProfit,tr.profit tribeProfit,cr.club_status  clubStatus,tr.tribe_status " +
                "tribeStatus from club_record cr left join club_members cm on cm.club_id = cr.id left join tribe_members tm on" +
                " tm.club_id = cr.id left join tribe_record tr on tr.id = tm.tribe_id where cm.user_id="+userId+" and cr.club_" +
                "status=0 and tr.status!=2";

        String clubSql="select cr.id clubId,cr.profit cProfit,cr.club_status clubStatus,cr.name clubName,cr.creator clubCreator" +
                " from club_record cr inner join club_members cm on cm.club_id = cr.id where cm.user_id="+ userId+" and cr.club_" +
                "status=0";
        try {
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(tribeSql);
            rs = ps.executeQuery();
            ClubTribeModel clubTribeModel = new ClubTribeModel();
            if (rs.next()){
                clubTribeModel.setClubId(rs.getInt("clubId"));
                clubTribeModel.setClubProportion(rs.getInt("cProfit"));
                clubTribeModel.setClubCreator(rs.getInt("clubCreator"));
                clubTribeModel.setClubName(rs.getString("clubName"));
                clubTribeModel.setClubStatus(rs.getInt("clubStatus"));
                clubTribeModel.setTribeId(rs.getInt("tribeId"));
                clubTribeModel.setTribeProportion(rs.getInt("tribeProfit"));
                clubTribeModel.setTribeCreator(rs.getInt("tribeCreator"));
                clubTribeModel.setTribeName(rs.getString("tribeName"));
                clubTribeModel.setTribeStatus(rs.getInt("tribeStatus"));
            }
            if (clubTribeModel.getClubId()==0){
                ps = dbConnection.prepareStatement(clubSql);
                rs=ps.executeQuery();
                if (rs.next()){
                    clubTribeModel.setClubId(rs.getInt("clubId"));
                    clubTribeModel.setClubProportion(rs.getInt("cProfit"));
                    clubTribeModel.setClubCreator(rs.getInt("clubCreator"));
                    clubTribeModel.setClubName(rs.getString("clubName"));
                    clubTribeModel.setClubStatus(rs.getInt("clubStatus"));
                    clubTribeModel.setTribeId(0);
                    clubTribeModel.setTribeProportion(0);
                    clubTribeModel.setTribeCreator(0);
                    clubTribeModel.setTribeName("");
                    clubTribeModel.setTribeStatus(0);
                }
            }
            return clubTribeModel;
        } catch (Exception e) {
            logger.debug(e.getMessage());
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }
        return null;
    }

    @Override
    public List<Integer> getClubCreators(String clubIds) {
        List<Integer> clubCreators = new ArrayList<>();

        Connection dbConnection = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql =
                "select cr.creator as clubCreator from club_record cr " +
                        " where cr.id in (" + clubIds + " ) ";
        try {
            logger.debug("getClubCreators===>" + sql);
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next()) {
                int clubCreator = rs.getInt("clubCreator");
                clubCreators.add(clubCreator);
            }
        } catch (Exception e) {
            logger.debug(e.getMessage());
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }
        return clubCreators;
    }

    @Override
    public Map<Integer, Integer> getClubCreatorsToMap(String clubIds) {
        Map<Integer, Integer> clubCreatorsMap = new HashMap<>();

        Connection dbConnection = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql =
                "select cr.id as clubId,cr.creator as clubCreator from club_record cr " +
                        " where cr.id in (" + clubIds + ")";
        try {
            logger.debug("getClubCreatorsToMap===>" + sql);
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next()) {
                int clubId = rs.getInt("clubId");
                int clubCreator = rs.getInt("clubCreator");
                clubCreatorsMap.put(clubId,clubCreator);
            }
        } catch (Exception e) {
            logger.debug(e.getMessage());
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }
        return clubCreatorsMap;
    }

    @Override
    public List<Integer> getTribeCreators(String tribeIds) {
        List<Integer> triberCreators = new ArrayList<>();

        Connection dbConnection = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        String sql =
                "select tr.creator as tribeCreator from tribe_record tr" +
                        " where tr.id in (" + tribeIds + " ) ";
        try {
            logger.debug("getTribeCreators===>" + sql);
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next()) {
                int tribeCreator = rs.getInt("tribeCreator");
                triberCreators.add(tribeCreator);
            }
        } catch (Exception e) {
            logger.debug(e.getMessage());
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }
        return triberCreators;
    }

    @Override
    public Map<Integer, ClubFeeConfig> getFeeConfig(String clubIds) {
        Connection dbConnection = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        Map<Integer,ClubFeeConfig> clubFeeConfigMap = new HashMap<>();

        String sql =
                "select tm.tribe_id as tribeId,tm.club_id as clubId,cr.profit as clubConfig,tr.profit as tribeConfig from club_record cr " +
                        " left join tribe_members tm  on tm.club_id = cr.id " +
                        " left join tribe_record tr  on tr.id = tm.tribe_id " +
                        " where cr.id in (" + clubIds + ")";
        try {
            logger.debug("getFeeConfig===>" + sql);
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next()) {

                ClubFeeConfig clubFeeConfig = new ClubFeeConfig();

                int tribeId = rs.getInt("tribeId");
                int clubId = rs.getInt("clubId");
                int clubConfig = rs.getInt("clubConfig");
                int tribeConfig = rs.getInt("tribeConfig");

                clubFeeConfig.setClubId(clubId);
                clubFeeConfig.setTribeId(tribeId);
                clubFeeConfig.setClubConfig(new Double(clubConfig) * 0.01);
                clubFeeConfig.setTribeConfig(new Double(tribeConfig) * 0.01);
                int systemConfig = 100 - clubConfig - tribeConfig;
                clubFeeConfig.setSysConfig(new Double(systemConfig) * 0.01);

                logger.debug("clubId={},clubFeeConfig={}",clubId,clubFeeConfig.toString());
                clubFeeConfigMap.put(clubId,clubFeeConfig);

            }

            return clubFeeConfigMap;

        } catch (Exception e) {
            logger.debug(e.getMessage());
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }
        return null;
    }


}
