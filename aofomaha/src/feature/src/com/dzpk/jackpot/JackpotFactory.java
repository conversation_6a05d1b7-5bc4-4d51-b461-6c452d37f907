package com.dzpk.jackpot;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.jackpot.impl.JackpotServiceImpl;
import com.i366.model.room.Room;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class JackpotFactory {
    /** 日志服务 */
    private static final Logger logger = LogUtil.getLogger(JackpotFactory.class);

    /** 实例化工厂 */
    private static final JackpotFactory FACTORY_INSTANCE = new JackpotFactory();
    public static JackpotFactory getInstance(){
        return FACTORY_INSTANCE;
    }
    private JackpotFactory(){}

    /**
     * 初始化
     * *牌局JP的配置数据
     * *白名单控制
     * @param room 牌局对象
     *    roomId   牌局ID
     *    roomPath 牌局类型
     *    manzhu   牌局盲注
     */
    public IJackpotService getJapckpotService(Room room){
        if(null == room)
            return null;

        JackpotServiceImpl impl = new JackpotServiceImpl(room);
        return impl;
    }

    //jackpotid池的累计次数
    private Map<Integer,Integer> accumulativeTimeMap = new ConcurrentHashMap<>();
    public void initAccumulativeTime(int jackpotId){
        this.accumulativeTimeMap.putIfAbsent(jackpotId,0);
    }
    /**
     * 返回当前累计次数，后缓存中则增加1
     * @param jackpotId
     * @return 返回增加前的累计次数
     */
    public int addAccumulativeTime(int jackpotId){
        int curTimes;

        synchronized (this.accumulativeTimeMap){
            curTimes = this.accumulativeTimeMap.getOrDefault(jackpotId,0);
            this.accumulativeTimeMap.put(jackpotId,curTimes+1);
        }

        return curTimes;
    }
}
