package com.dzpk.eventtrack;

import com.dzpk.component.mq.rabbitmq.model.eventtrack.MessageInfo;
import com.dzpk.eventtrack.constant.EventTrackingCode;
import com.i366.util.RabbitMqUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 埋点服务
 *
 * 带入、站起、托管、坐下
 */
public abstract class EventTrackService {

    private static final Logger logger = LogManager.getLogger(EventTrackService.class);

    /**
     * 带入积分
     */
    protected abstract MessageInfo addChip(int userId,int roomId,MessageInfo messageInfo);

    /**
     * 站起
     */
    protected abstract MessageInfo standUp(int userId,int roomId,MessageInfo messageInfo);

    /**
     * 托管/取消托管
     */
    protected abstract MessageInfo autoOp(int userId,MessageInfo messageInfo);

    /**
     * 坐下
     */
    protected abstract MessageInfo downRoom(int userId,int roomId,MessageInfo messageInfo);

    /**
     * 获取触发的时间类型
     * @param roomId
     * @param userId
     * @param eventTrackingCode
     */
    public void addEventTrack(int roomId,int userId,EventTrackingCode eventTrackingCode){

      logger.debug("添加数据埋点,rid={},uid={},埋点类型={}",roomId,userId,eventTrackingCode.getDesc());

      MessageInfo messageInfo = new MessageInfo();
      messageInfo.setMsgType(eventTrackingCode.getCode());

      if(EventTrackingCode.ADDCHIP_EVENT == eventTrackingCode){
          messageInfo = addChip(userId,roomId,messageInfo);
      }else if(EventTrackingCode.STANDUP_ROOM_EVENT == eventTrackingCode){
          messageInfo = standUp(userId,roomId,messageInfo);
      }else if(EventTrackingCode.AUTO_OP_EVENT == eventTrackingCode){
          messageInfo = autoOp(userId,messageInfo);
      }else if(EventTrackingCode.DOWN_ROOM_EVENT == eventTrackingCode){
          messageInfo = downRoom(userId,roomId,messageInfo);
      }else if(EventTrackingCode.CANCAL_AUTO_OP_EVENT == eventTrackingCode){
          messageInfo = autoOp(userId,messageInfo);
      }

      RabbitMqUtil.sendEventTrackingPoint(roomId,userId,messageInfo);
    }
}
