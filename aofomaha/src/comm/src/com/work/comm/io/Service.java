package com.work.comm.io;

import com.dzpk.common.config.PropertiesPathUtil;
import com.dzpk.component.repositories.mysql.dbpool.DBUtil;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;

/**
 * Created by IntelliJ IDEA.
 * User: Administrator
 * To change this template use File | Settings | File Templates.
 */
@Slf4j
public class Service {
    public static String DZPK_SERVICE = "DZPK_SERVICE";

    public Service() {
        initService();
    }

    public void start(String servername, int clientPort) {
        //PropertyConfigurator.configure("log4j.properties");
        try {
            log.debug("begin to init " + servername);


            // 客户端通信 (TCP)
            Server server = new Server(clientPort, 0);
            new Thread(server).start();

            // 客户端通信 (WS)
            WSServer wsServer = new WSServer(clientPort + 1, 0);
            new Thread(wsServer).start();

            // 服务端内部进程间通信
            /*try {
                Server server = new Server(serverPort, 1);
                new Thread(server).start();
            } catch (Exception e) {
                log.error("start server netty server error", e);
            }*/

        } catch (Exception ex) {
            log.error(servername + " start error:", ex);
        }
        log.info(servername + "  start ok!");
    }

    private static void initService() {
        log.info("loading Game Config file");
        PropertiesPathUtil.loadWGProperties();

        log.info("initializing db pool");
        Connection conn = null;
        try {
            conn = DBUtil.getConnection();
        } catch (Exception ex) {
            log.error("error getConnection", ex);
            System.exit(-1);
        } finally {
            DBUtil.closeConnection(conn);
        }

        log.info("loading server handlers");
        HandlerFactory handlerCacheFactory = HandlerFactory.getInstance();
        handlerCacheFactory.init();
        log.info("loading client handlers");
        ClientHandlerFactory clientHandlerCacheFactory = ClientHandlerFactory.getInstance();
        clientHandlerCacheFactory.init();
    }

}
