package com.work.comm.s2s.protocal;

import lombok.extern.slf4j.Slf4j;

import java.nio.ByteBuffer;
import java.nio.charset.Charset;

@Slf4j
public final class Functions {
    /**
     * 向Buffer当前位置写入一个short整数
     *
     * @param buffer    字节流，必填
     * @param data      待写入的整数，必填
     */
    public static void putAsShort(ByteBuffer buffer, int data){
        buffer.put((byte)((data & 0xFF00) >> 8 ));
        buffer.put((byte)(data & 0xFF));
    }

    /**
     * 向Buffer当前位置写入一个short整数
     *
     * @param offset    指定位置,必填
     * @param buffer    字节流，必填
     * @param data      待写入的整数，必填
     */
    public static void putAsShort(int offset,ByteBuffer buffer, int data){
        buffer.put(offset++,(byte)((data & 0xFF00) >> 8 ));
        buffer.put(offset,(byte)(data & 0xFF));
    }

    /**
     * 读取一个short整数
     *
     * @param buffer  字节流，必填
     *
     * @return
     */
    public static int getAsShort(ByteBuffer buffer){
        return (0     & 0xff) << 24 |
                (0     & 0xff) << 16 |
                ( buffer.get() & 0xff) << 8  |
                ( buffer.get() & 0xff);
    }

    /**
     * 读取一个short整数
     *
     * @param offset    指定位置,必填
     * @param buffer  字节流，必填
     *
     * @return
     */
    public static int getAsShort(int offset,ByteBuffer buffer){
        return (0     & 0xff) << 24 |
                (0     & 0xff) << 16 |
                ( buffer.get(offset++) & 0xff) << 8  |
                ( buffer.get(offset) & 0xff);
    }

    /**
     * 向Buffer当前位置写入一个int整数
     *
     * @param buffer    字节流，必填
     * @param data      待写入的整数，必填
     */
    public static void setInt(ByteBuffer buffer, int data){
        buffer.put((byte)((data & 0xFF000000) >> 24 ));
        buffer.put((byte)((data & 0xFF0000) >> 16 ));
        buffer.put((byte)((data & 0xFF00) >> 8 ));
        buffer.put((byte)(data & 0xFF));
    }

    /**
     * 向Buffer当前位置写入一个int整数
     *
     * @param offset    指定位置,必填
     * @param buffer    字节流，必填
     * @param data      待写入的整数，必填
     */
    public static void setInt(int offset,ByteBuffer buffer, int data){
        buffer.put(offset++,(byte)((data & 0xFF000000) >> 24 ));
        buffer.put(offset++,(byte)((data & 0xFF0000) >> 16 ));
        buffer.put(offset++,(byte)((data & 0xFF00) >> 8 ));
        buffer.put(offset,(byte)(data & 0xFF));
    }

    /**
     * 读取一个Int整数
     *
     * @param buffer  字节流，必填
     *
     * @return
     */
    public static int getInt(ByteBuffer buffer){
        return  (buffer.get() & 0xff) << 24 |
                (buffer.get() & 0xff) << 16 |
                (buffer.get() & 0xff) << 8  |
                (buffer.get() & 0xff);
    }

    /**
     * 读取一个Int整数
     *
     * @param offset    指定位置,必填
     * @param buffer  字节流，必填
     *
     * @return
     */
    public static int getInt(int offset,ByteBuffer buffer){
        return  (buffer.get(offset++) & 0xff) << 24 |
                (buffer.get(offset++) & 0xff) << 16 |
                (buffer.get(offset++) & 0xff) << 8  |
                (buffer.get(offset) & 0xff);
    }

    /**
     * 将字符串按UNICODE编码方式转换成byte[]
     *
     * @param ss  字符串，必填
     *
     * @return 以下格式的byte[]
     *         2字节的byte作为字符串的长度
     *         UNICODE编码的byte[]内容
     */
    public static byte[] toBytesByUnicode(String ss){
        return toBytes(ss,Protocal.CHARSET_UNICODE);
    }

    /**
     * 将字符串按UTF-16编码方式转换成byte[]
     *
     * @param ss  字符串，必填
     *
     * @return 以下格式的byte[]
     *         2字节的byte作为字符串的长度
     *         UTF-16编码的byte[]内容
     */
    public static byte[] toBytesByUtf16(String ss){
        return toBytes(ss,Protocal.CHARSET_UTF16);
    }

    /**
     * 将UNICODE编码的byte[]转换成String
     *
     * @param dataArr  UNICODE编码的byte[]，必填
     *
     * @return
     */
    public static String toUnicode(byte[] dataArr){
        return toStr(dataArr,Protocal.CHARSET_UNICODE);
    }

    /**
     * 将UTF-16编码的byte[]转换成String
     *
     * @param dataArr  UTF-16编码的byte[]，必填
     *
     * @return
     */
    public static String toUtf16(byte[] dataArr){
        return toStr(dataArr ,Protocal.CHARSET_UTF16);
    }

    private static byte[] toBytes(String str, Charset charset){
        byte[] resultArr = null;
        if(null == str)
            return resultArr;
        if(null == charset)
            throw new RuntimeException(String.format("Not specified charset when converting String to byte[]: %s",str));

        try {
            resultArr = str.getBytes(charset);
        }catch (Exception ex){
            throw new RuntimeException(String.format("Cannot convert String[%s] to byte[] with charset[%s]: %s",str,charset,ex.getMessage()),ex);
        }

        return resultArr;
    }
    private static String toStr(byte[] dataArr, Charset charset){
        String result = null;
        if(null == dataArr || dataArr.length==0)
            return result;

        if(null == charset)
            throw new RuntimeException(String.format("Not specified charset when converting byte[] to String: %s",dataArr));

        try {
            result = new String(dataArr,charset);
        }catch (Exception ex){
            log.error("{}",dataArr);
            throw new RuntimeException(String.format("Cannot convert byte[] to String with charset[%s]: %s",charset,ex.getMessage()),ex);
        }

        return result;
    }
}
