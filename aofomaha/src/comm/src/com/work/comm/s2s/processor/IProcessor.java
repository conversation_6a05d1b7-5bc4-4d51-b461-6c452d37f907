package com.work.comm.s2s.processor;

import com.work.comm.s2s.protocal.ServiceRequest;

/**
 * 数据包处理器
 * socket中发过来的每类数据包
 * 对应一个实列
 */
public interface IProcessor extends IRequestCode {
    /**
     * 接收的数据包的处理方法
     *
     * @param req  请求对象，必填
     *
     * @return  返回的业务数据
     *   null或byte[0] 表示不需要返回客户端
     */
    byte[] handle(ServiceRequest req);

    /**
     * 是否可共享
     * 如果是可共享，则只有一份实例
     * 否则每次都新new一个实例
     * @return
     */
    boolean isSharable();
}
