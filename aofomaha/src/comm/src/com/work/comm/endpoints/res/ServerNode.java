package com.work.comm.endpoints.res;

import lombok.ToString;

@ToString
public class ServerNode {
    private ZkResServerNode serverNode;

    public ZkResServerNode updateNode(ZkResServerNode serverNode){
        ZkResServerNode old = this.serverNode;
        this.serverNode = serverNode;
        return old;
    }

    // 登陆服务到此Res的连接数
    // 完成认证时+1
    // 连接关闭时-1
    private int selfConnNum=0;

    /**
     * 节点连接数
     * @param isAdd  是否+1
     *
     * @return  是否无连接数
     *    用于进入计，如果超过X时间，则停止改服务
     */
    public boolean addConnNum(boolean isAdd){
        int currVal = this.selfConnNum;
        currVal = currVal < 0 ? 0 : currVal;
        if (isAdd)
            currVal += 1;
        else if (currVal > 0)
            currVal -= 1;
        this.selfConnNum = currVal;

        return currVal == 0;
    }

    public String getServerId() {
        return this.serverNode.getServerId();
    }

    public String getOutAccessIp() {
        return this.serverNode.getOutAccessIp();
    }

    public int getOutAccessPort() {
        return this.serverNode.getOutAccessPort();
    }

    public String getInAccessIp() {
        return this.serverNode.getInAccessIp();
    }

    public int getInAccessPort() {
        return this.serverNode.getInAccessPort();
    }

    public int getClientConnNum() {
        return this.serverNode.getClientConnNum();
    }

    public int getAssignedNum() {
        return this.serverNode.getAssignedNum();
    }

    public int getSelfConnNum() {
        return this.selfConnNum;
    }

    public ZkResServerNode getServerNode() {
        return serverNode;
    }
}
