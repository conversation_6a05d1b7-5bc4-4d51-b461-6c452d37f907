package com.dzpk.crazypoker.vip.constant;

public enum EVipCard {
    MONTH((byte) 1, "月卡", new int[]{1, 3, 6, 9}),
    YEAR((byte) 2, "年卡", new int[]{1, 2, 3, 5});

    private byte value;
    private String desc;
    /**
     * 有效周期
     */
    private int[] validPeriods;

    EVipCard(byte value, String desc, int[] validPeriods) {
        this.value = value;
        this.desc = desc;
        this.validPeriods = validPeriods;
    }

    public String desc() {
        return this.desc;
    }

    public byte value() {
        return this.value;
    }

    /**
     * 检查指定周期值是否支持
     *
     * @param period
     * @return true  : 有效
     * false : 无效
     */
    public boolean checkPeriod(int period) {
        boolean isOk = false;

        for (int p : this.validPeriods) {
            if (p == period) {
                isOk = true;
                break;
            }
        }

        return isOk;
    }
}
