package com.dzpk.crazypoker.vip.repositories.mysql.autogen.model;

import java.io.Serializable;

public class VipUserBreakPoKey implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column vip_user_break.user_id
     *
     * @mbg.generated
     */
    private Integer userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column vip_user_break.break_user_id
     *
     * @mbg.generated
     */
    private Integer breakUserId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table vip_user_break
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column vip_user_break.user_id
     *
     * @return the value of vip_user_break.user_id
     *
     * @mbg.generated
     */
    public Integer getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column vip_user_break.user_id
     *
     * @param userId the value for vip_user_break.user_id
     *
     * @mbg.generated
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column vip_user_break.break_user_id
     *
     * @return the value of vip_user_break.break_user_id
     *
     * @mbg.generated
     */
    public Integer getBreakUserId() {
        return breakUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column vip_user_break.break_user_id
     *
     * @param breakUserId the value for vip_user_break.break_user_id
     *
     * @mbg.generated
     */
    public void setBreakUserId(Integer breakUserId) {
        this.breakUserId = breakUserId;
    }
}