package com.dzpk.crazypoker.backpack.service.transaction.impl;

import com.dzpk.crazypoker.backpack.repositories.mysql.autogen.mapper.UserRewardLogPoMapper;
import com.dzpk.crazypoker.backpack.repositories.mysql.autogen.model.UserRewardLogPo;
import com.dzpk.crazypoker.backpack.service.bean.UserRewardLogBo;
import com.dzpk.crazypoker.backpack.service.transaction.IUserRewardLogTransService;
import com.dzpk.crazypoker.common.dozer.BeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class UserRewardLogTransServiceImpl implements IUserRewardLogTransService {

    @Autowired
    private BeanUtil beanUtil;

    @Autowired
    private UserRewardLogPoMapper mapper;

    @Override
    @Transactional
    public void save(UserRewardLogBo bo) {
        UserRewardLogPo po = beanUtil.map(bo, UserRewardLogPo.class);
        mapper.insert(po);
        bo.setId(po.getId());
    }
}
