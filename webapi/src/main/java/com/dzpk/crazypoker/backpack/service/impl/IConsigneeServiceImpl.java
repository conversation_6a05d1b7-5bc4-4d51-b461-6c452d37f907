package com.dzpk.crazypoker.backpack.service.impl;

import com.dzpk.crazypoker.backpack.repositories.mysql.autogen.mapper.UserConsigneeInfoPoMapper;
import com.dzpk.crazypoker.backpack.repositories.mysql.autogen.model.UserConsigneeInfoPo;
import com.dzpk.crazypoker.backpack.repositories.mysql.autogen.model.UserConsigneeInfoPoExample;
import com.dzpk.crazypoker.backpack.service.IConsigneeService;
import com.dzpk.crazypoker.backpack.service.bean.UserConsigneeInfoBo;
import com.dzpk.crazypoker.backpack.service.transaction.IConsigneeTransService;
import com.dzpk.crazypoker.common.dozer.BeanUtil;
import com.dzpk.crazypoker.common.utils.PhoneNumUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class IConsigneeServiceImpl implements IConsigneeService {

    @Autowired
    private UserConsigneeInfoPoMapper mapper;


    @Autowired
    private IConsigneeTransService consigneeTransService;

    @Autowired
    private BeanUtil beanUtil;

    @Override
    public boolean existByUserId(Integer userId) {
        UserConsigneeInfoPoExample example = new UserConsigneeInfoPoExample();
        example.or().andUserIdEqualTo(userId);
        List<UserConsigneeInfoPo> poList = mapper.selectByExample(example);
        if (poList.size() == 0) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public UserConsigneeInfoBo findByUserId(Integer userId) {
        UserConsigneeInfoPoExample example = new UserConsigneeInfoPoExample();
        example.or().andUserIdEqualTo(userId);
        List<UserConsigneeInfoPo> poList = mapper.selectByExample(example);
        if (poList.size() == 0) {
            return null;
        }
        UserConsigneeInfoPo po = poList.get(0);
        po.setMobile(PhoneNumUtil.decoder(po.getMobile()));
        return beanUtil.map(po, UserConsigneeInfoBo.class);
    }

    @Override
    public void saveOrModify(UserConsigneeInfoBo bo) {
        consigneeTransService.saveOrModify(bo);
    }


}
