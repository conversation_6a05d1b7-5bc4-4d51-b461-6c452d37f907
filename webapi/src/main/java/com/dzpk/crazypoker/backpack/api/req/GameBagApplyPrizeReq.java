package com.dzpk.crazypoker.backpack.api.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Setter
@Getter
@ApiModel(value = "背包物品申请")
public class GameBagApplyPrizeReq {
    /**
     * 物品id
     */
    @ApiModelProperty(name = "物品id",
            notes = "物品id",
            required = true,
            position = 1)
    @NotNull
    private Integer id;
}
