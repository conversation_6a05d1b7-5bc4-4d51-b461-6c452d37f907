package com.dzpk.crazypoker.promotion.repositories.mysql;

import com.dzpk.crazypoker.promotion.repositories.mysql.autogen.model.PromotionUserAchievementRecordPo;
import com.dzpk.crazypoker.promotion.repositories.mysql.autogen.model.PromotionUserFeedbackRecordPo;
import com.dzpk.crazypoker.promotion.service.bean.PromoterBo;
import com.dzpk.crazypoker.promotion.service.bean.UserInfoBo;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * Description:
 * <p>
 * Created by <PERSON><PERSON> on 2019/3/30 17:13
 */
@Repository
public interface IPromotionDao {

    /**
     * 更新推广用户关系层级表，更新为推广员
     * @param userId
     * @return
     */
    @Update("update promotion_user_relations set promotion_type= 1 where user_id=#{userId}")
    int updatePromotionStatus(@Param(value = "userId") int userId);


    @Insert("insert into promotion_user_relations(user_id,parent_user_id,level,club_id,promotion_type) values(#{userId},#{parentUserId},#{level}," +
            "#{clubId},#{promotionType})")
    int insertPromotionUserRelation(@Param(value = "userId")Integer userId, @Param(value = "parentUserId")Integer parentUserId,
                                    @Param(value = "level")int level, @Param(value = "clubId")Integer clubId, @Param(value = "promotionType")int promotionType);

    /**
     * 修改分享用户表数据
     * @param userId  玩家id
     * @param addGroupNumber 我的团队人数增加量
     * @param addMyPromoterNumber 我的推广员人数增加量
     */
    @Update("update promotion_user_infomation set group_members=IFNULL(group_members,0) + #{addGroupNumber}, my_promoters_number=IFNULL(my_promoters_number,0) + #{addMyPromoterNumber} where user_id=#{userId}")
    int updatePromotionUserNumbers(@Param(value = "userId")int userId, @Param(value = "addGroupNumber")int addGroupNumber, @Param(value = "addMyPromoterNumber")int addMyPromoterNumber);

    /**
     * 修改今日推广员数据
     * @param userId  玩家id
     * @param todayIncreaseNum 今日下级新增人数
     * @param today 今天日期
     */
    @Update("update promotion_user_infomation set today_increase_number=if(TO_DAYS(today_date) = TO_DAYS(#{today}), today_increase_number + #{todayIncreaseNum}, #{todayIncreaseNum}), " +
            "today_date=if(TO_DAYS(today_date) = TO_DAYS(#{today}), today_date, #{today})  where user_id=#{userId}")
    int updateTodayPromotionUserNumbers(@Param(value = "userId")int userId, @Param(value = "todayIncreaseNum")int todayIncreaseNum,
                                        @Param(value = "today")Date today);

    /**
     * 修改今周业绩数据
     * @param userId  玩家id
     * @param addWeekAchievement 我的本周业绩增加量(自然周，周一到周日)
     * @param weekBeginDate 今周開始日期
     */
    @Update("update promotion_user_infomation set week_begin_date=if(TO_DAYS(week_begin_date) = TO_DAYS(#{weekBeginDate}), week_begin_date, #{weekBeginDate}), " +
            "week_achievement=if(TO_DAYS(week_begin_date) = TO_DAYS(#{weekBeginDate}), week_achievement + #{addWeekAchievement}, #{addWeekAchievement}) where user_id=#{userId}")
    int updateWeekAchievementNumbers(@Param(value = "userId")int userId, @Param(value = "addWeekAchievement")int addWeekAchievement, @Param(value = "weekBeginDate")Date weekBeginDate);

    /**
     * 本月业绩查询
     * @param userId
     * @return
     */
    @Select("select total_achievement as totalAchievement, date from promotion_user_achievement_record where user_id=#{userId} " +
            "and date >= #{monthStartTime} order by date desc  limit #{start}, #{pageSize}")
    List<PromotionUserAchievementRecordPo> queryMonthAchievements(int userId, Date monthStartTime, int start, int pageSize);

    /**
     * 本月返豆查询
     * @param userId
     * @return
     */
    @Select("select beans, date from promotion_user_feedback_record where user_id=#{userId} and date >= #{monthStartTime} order by date desc limit #{start}, #{pageSize}")
    List<PromotionUserFeedbackRecordPo> queryMonthBeansRecord(int userId, Date monthStartTime, int start, int pageSize);

    /**
     * 查询我的所有直属玩家+直属推广员列表
     * @param userId
     * @returnpromotion_user_infomation
     */
    @Select("select id as promoterId, user_id as userId, if(TO_DAYS(week_begin_date) = TO_DAYS(#{weekBeginDate}), week_achievement, 0) as weekAchievement, " +
            "group_members as groupMembers, promotion_type as promotionType from promotion_user_infomation where parent_user_id = #{userId} " +
            "order by weekAchievement desc, promotion_type desc, user_id asc limit #{start}, #{pageSize}")
    List<PromoterBo> queryMyPromoters(int userId, Date weekBeginDate, int start, int pageSize);

    /**
     * 查询我的直属玩家+直属推广员列表
     * @param userId 用户id
     * @param search 推广员ID或用户昵称
     * @return
     */
    @Select("SELECT t1.id as promoterId, t2.random_num as userId, if(TO_DAYS(week_begin_date) = TO_DAYS(#{weekBeginDate}), t1.week_achievement, 0) as weekAchievement, " +
            "t1.group_members as groupMembers, t2.nike_name as nickname, t1.promotion_type as promotionType FROM promotion_user_infomation t1, user_details_info t2 WHERE parent_user_id = #{userId} " +
            " and t1.user_id = t2.USER_ID and (t2.nike_name like CONCAT('%',#{search},'%')   or t2.random_num like CONCAT('%',#{search},'%')  ) " +
            "order by t1.week_achievement desc limit #{start}, #{pageSize}")
    List<PromoterBo> queryMyPromotersBySearch(@Param("userId") int userId, @Param("search")String search, @Param("weekBeginDate")Date weekBeginDate, @Param("start")int start, @Param("pageSize")int pageSize);

    /**
     * 更新俱乐部人员的推广员身份
     * @param userId
     */
    @Update("update club_members set promotion_type = 1 where user_id = #{userId}")
    void updateClubMemberType(Integer userId);

    void updateClubMemberPromotionNumber(Integer parentUserId);

    void updateClubPromotionNumber(Integer clubId);

    /**
     * 批量查询用户的昵称
     * @param userIds
     * @return
     */
    @Select({"<script>",
            "select user_id as userId, nike_name as nickname, random_num from user_details_info where user_id in",
            "(" ,
            "<foreach collection='userIds' item='item' separator=','>" ,
            "#{item}" ,
            "</foreach>)",
            "</script>" })
    List<UserInfoBo> batchQueryUserNicknames(@Param("userIds") List<String> userIds);

    @Select("select count(0) from promotion_user_infomation where user_id = #{userId} and promotion_type = 1")
    int countPromoter(int userId);

    @Select("select count(0) from promotion_user_infomation where user_id = #{userId}")
    int countPromotionUser(int userId);

    /**
     * 查询用户所加入的俱乐部id
     * @param userId
     * @return
     */
    @Select("select club_id from club_members where user_id = #{userId}")
    Integer countClubMember(int userId);

    /**
     * 查找俱乐部创建者id
     * @param clubId
     * @return
     */
    @Select("select creator from club_record where id = #{clubId}")
    Integer queryClubCreator(Integer clubId);

    @Insert("INSERT INTO promotion_user_infomation(user_id, club_id, invent_code, parent_user_id, promotion_type) " +
            "VALUES (#{clubCreator}, #{clubId}, #{inventCode}, NULL, 1)")
    void insertClubCreatorPromotionUserInfo(int clubCreator, String inventCode, int clubId);

    @Insert("INSERT INTO promotion_user_relations(user_id, parent_user_id, level, promotion_type, club_id) " +
            "VALUES (#{clubCreator}, #{clubCreator}, 0, 1, #{clubId});")
    void insertClubCreatorPromotionUserRelations(int clubCreator, int clubId);


    @Select({" <script>",
            " select * from promotion_user_feedback_record  where ",
            " <if test='nextId != null'>",
            " `id` &lt; #{nextId} and ",
            " </if>",
            " user_id =#{userId} and status = 1 order by id desc limit #{limit}  ",
            " </script>"
    })
    @ResultMap("com.dzpk.crazypoker.promotion.repositories.mysql.autogen.mapper.PromotionUserFeedbackRecordPoMapper.BaseResultMap")
    List<PromotionUserFeedbackRecordPo> selectByUserIdPage(@Param("userId") Integer userId, @Param("nextId") Integer nextId, @Param("limit") Integer limit);

    @Update("UPDATE message_unread SET kdou_num = ifnull(kdou_num, 0)+#{num}, kdou_msg = #{msg} WHERE user_id=#{userId}")
    Integer updateKdouUnreadMsg(@Param(value = "num")Integer num,@Param(value = "msg")String msg,@Param(value = "userId")Integer userId);

    @Update("update promotion_user_infomation set " +
            " today_increase_promotor_number = if(TO_DAYS(today_date) = TO_DAYS(#{today}), today_increase_promotor_number + #{addNum}, #{addNum}), " +
            " today_date=if(TO_DAYS(today_date) = TO_DAYS(#{today}), today_date, #{today})" +
            " where user_id=#{parentUserId}")
    void updateTodayMyPromotorNumber(Integer parentUserId, int addNum, Date today);
}
