package com.dzpk.crazypoker.promotion.repositories.mysql.autogen.mapper;

import com.dzpk.crazypoker.promotion.repositories.mysql.autogen.model.PromotionUserFeedbackRecordPo;
import com.dzpk.crazypoker.promotion.repositories.mysql.autogen.model.PromotionUserFeedbackRecordPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface PromotionUserFeedbackRecordPoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table promotion_user_feedback_record
     *
     * @mbg.generated
     */
    long countByExample(PromotionUserFeedbackRecordPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table promotion_user_feedback_record
     *
     * @mbg.generated
     */
    int deleteByExample(PromotionUserFeedbackRecordPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table promotion_user_feedback_record
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table promotion_user_feedback_record
     *
     * @mbg.generated
     */
    int insert(PromotionUserFeedbackRecordPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table promotion_user_feedback_record
     *
     * @mbg.generated
     */
    int insertSelective(PromotionUserFeedbackRecordPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table promotion_user_feedback_record
     *
     * @mbg.generated
     */
    List<PromotionUserFeedbackRecordPo> selectByExample(PromotionUserFeedbackRecordPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table promotion_user_feedback_record
     *
     * @mbg.generated
     */
    PromotionUserFeedbackRecordPo selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table promotion_user_feedback_record
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") PromotionUserFeedbackRecordPo record, @Param("example") PromotionUserFeedbackRecordPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table promotion_user_feedback_record
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") PromotionUserFeedbackRecordPo record, @Param("example") PromotionUserFeedbackRecordPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table promotion_user_feedback_record
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(PromotionUserFeedbackRecordPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table promotion_user_feedback_record
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(PromotionUserFeedbackRecordPo record);
}