package com.dzpk.crazypoker.promotion.repositories.mysql.autogen.mapper;

import com.dzpk.crazypoker.promotion.repositories.mysql.autogen.model.PromotionUserInfomationPo;
import com.dzpk.crazypoker.promotion.repositories.mysql.autogen.model.PromotionUserInfomationPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface PromotionUserInfomationPoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table promotion_user_infomation
     *
     * @mbg.generated
     */
    long countByExample(PromotionUserInfomationPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table promotion_user_infomation
     *
     * @mbg.generated
     */
    int deleteByExample(PromotionUserInfomationPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table promotion_user_infomation
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table promotion_user_infomation
     *
     * @mbg.generated
     */
    int insert(PromotionUserInfomationPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table promotion_user_infomation
     *
     * @mbg.generated
     */
    int insertSelective(PromotionUserInfomationPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table promotion_user_infomation
     *
     * @mbg.generated
     */
    List<PromotionUserInfomationPo> selectByExample(PromotionUserInfomationPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table promotion_user_infomation
     *
     * @mbg.generated
     */
    PromotionUserInfomationPo selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table promotion_user_infomation
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") PromotionUserInfomationPo record, @Param("example") PromotionUserInfomationPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table promotion_user_infomation
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") PromotionUserInfomationPo record, @Param("example") PromotionUserInfomationPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table promotion_user_infomation
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(PromotionUserInfomationPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table promotion_user_infomation
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(PromotionUserInfomationPo record);
}