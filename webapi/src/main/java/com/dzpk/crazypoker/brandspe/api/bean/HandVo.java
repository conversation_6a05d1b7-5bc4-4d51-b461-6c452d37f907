package com.dzpk.crazypoker.brandspe.api.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@ApiModel(value = "手牌视图")
public class HandVo {
    /**
     * 编号
     */
    @ApiModelProperty(
            required = true,
            position = 1,
            notes = "编号"
    )
    private Integer index;


    @ApiModelProperty(
            required = true,
            position = 2,
            notes = "手牌id"
    )
    private String id;

    @ApiModelProperty(
            required = true,
            position = 3,
            notes = "小盲注"
    )
    private Integer smallBlind;

    @ApiModelProperty(
            required = true,
            position = 4,
            notes = "大盲注"
    )
    private Integer bigBlind;

    @ApiModelProperty(
            required = true,
            position = 5,
            notes = "时间"
    )
    private Long timeStamp;


    @ApiModelProperty(
            required = true,
            position = 6,
            notes = "输赢筹码"
    )
    private Integer win;

    @ApiModelProperty(
            required = true,
            position = 7,
            notes = "牌局类型:61-德州,62-德州必下场,63-德州AOF,91-奥马哈,92-奥马哈必下场,93-奥马哈AOF"
    )
    private Integer roomType;

    @ApiModelProperty(
            required = true,
            position = 8,
            notes = "手牌名称"
    )
    private String handName;


    @ApiModelProperty(
            required = true,
            position = 9,
            notes = "是否收藏:false-没有,true-收藏"
    )
    private Boolean collect;
    @ApiModelProperty(
            required = true,
            position = 10,
            notes = "前注"
    )
    private Integer qianzhu;

    @ApiModelProperty(
            required = true,
            position = 11,
            notes = "房间id"
    )
    private String roomId;

    private int tribeRoomType;
}
