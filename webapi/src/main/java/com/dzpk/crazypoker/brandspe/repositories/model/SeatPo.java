package com.dzpk.crazypoker.brandspe.repositories.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Field;

@NoArgsConstructor
@Data
public class SeatPo {
    @Field("NUMBER")
    private Integer number;
    @Field("WINCHIPS")
    private Integer winChips;
    @Field("CHIPS")
    private Integer chips;
    @Field("ID")
    private Integer id;
    @Field("ICON")
    private String icon;
    @Field("NAME")
    private String name;
}
