package com.dzpk.crazypoker.sns.api.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel(value = "修改用户备注请求")
@Setter
@Getter
public class SnsModRemarkReq {


    @ApiModelProperty(name = "其它用户id.与前端协调考虑改成randomId",
            required = true,
            notes = "其它用户id.与前端协调考虑改成randomId",
            position = 1)
    @NotNull(message = "用户id必填！")
    private String otherRandomId;

    @ApiModelProperty(name = "打法标志",
            notes = "打法标志",
            position = 2)
    @Length(max = 255, message = "remarks should not be greater than 255 characters!")
    private String remark;

    @ApiModelProperty(name = "备注名",
            notes = "备注名",
            position = 3)
    @Length(max = 255, message = "remarkName should not be greater than 255 characters!")
    private String remarkName;

    @ApiModelProperty(name = "标注颜色",
            notes = "标注颜色,传-1无颜色",
            position = 4)
    private Integer remarkColor;
}
