package com.dzpk.crazypoker.oss.model;

import lombok.Data;

import java.io.InputStream;
import java.io.OutputStream;

/**
 * 文件资源
 */
@Data
public class FileResource {

    /**
     * 名称
     */
    private String fileName;

    /**
     * 输入流
     */
    private InputStream inputStream;

    /**
     * 输出流
     */
    private OutputStream outputStream;

    public FileResource() {
    }

    public FileResource(String fileName) {
        this.fileName = fileName;
    }

    public FileResource(String fileName, InputStream inputStream) {
        this.fileName = fileName;
        this.inputStream = inputStream;
    }

    public FileResource(String fileName, OutputStream outputStream) {
        this.fileName = fileName;
        this.outputStream = outputStream;
    }
}
