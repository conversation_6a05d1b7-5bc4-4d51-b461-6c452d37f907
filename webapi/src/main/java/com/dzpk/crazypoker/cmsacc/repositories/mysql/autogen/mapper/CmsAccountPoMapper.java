package com.dzpk.crazypoker.cmsacc.repositories.mysql.autogen.mapper;

import com.dzpk.crazypoker.cmsacc.repositories.mysql.autogen.model.CmsAccountPo;
import com.dzpk.crazypoker.cmsacc.repositories.mysql.autogen.model.CmsAccountPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CmsAccountPoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cms_account
     *
     * @mbg.generated
     */
    long countByExample(CmsAccountPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cms_account
     *
     * @mbg.generated
     */
    int deleteByExample(CmsAccountPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cms_account
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer cmsUid);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cms_account
     *
     * @mbg.generated
     */
    int insert(CmsAccountPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cms_account
     *
     * @mbg.generated
     */
    int insertSelective(CmsAccountPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cms_account
     *
     * @mbg.generated
     */
    List<CmsAccountPo> selectByExample(CmsAccountPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cms_account
     *
     * @mbg.generated
     */
    CmsAccountPo selectByPrimaryKey(Integer cmsUid);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cms_account
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") CmsAccountPo record, @Param("example") CmsAccountPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cms_account
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") CmsAccountPo record, @Param("example") CmsAccountPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cms_account
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(CmsAccountPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cms_account
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(CmsAccountPo record);
}