package com.dzpk.crazypoker.cmsacc.service.bean;

import com.dzpk.crazypoker.cmsacc.config.ECmsAccChangeType;
import lombok.*;

import java.util.Date;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsAccountChangeBo {

    private Long id;

    private Integer cmsUid;

    private ECmsAccChangeType type;

    private Integer changeChip;

    private Integer opId;

    private Integer clubId;

    private String externalId;

    private Date createdTime;
}
