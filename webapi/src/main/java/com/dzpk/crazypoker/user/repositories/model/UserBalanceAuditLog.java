package com.dzpk.crazypoker.user.repositories.model;

import lombok.*;

import java.sql.Timestamp;

@Getter
@Setter
@Builder
@AllArgsConstructor
public class UserBalanceAuditLog {

    /**
     * 主键
     */
    private long id;

    /**
     * 交易UUID
     */
    private String txUuid;

    /**
     * 用户id
     */
    private int userId;

    /**
     * 账变类型
     */
    private int type;

    /**
     * 账变来源, 1-api 2-osm 3-cms 4-room 5-payment
     */
    private int source;

    /**
     * 0-钻石 1-俱乐部币 2-联盟币
     */
    private int balanceType;

    /**
     * 变动前结余
     */
    private int balanceBefore;

    /**
     * 变动金额
     */
    private int balanceChange;

    /**
     * 简短描述
     */
    private String description;

    /**
     * 操作者id (-1=系统)
     */
    private Integer operatorId;

    /**
     * 房间id (如有)
     */
    private Integer roomId;

    /**
     * 俱乐部id (如有)
     */
    private Integer clubId;

    /**
     * 联盟id (如有)
     */
    private Integer tribeId;

    /**
     * 时间戳
     */
    private Timestamp createTime;

    /**
     * 交易对象用户id
     */
    private Integer counterpartyId;

    public static UserBalanceAuditLog.UserBalanceAuditLogBuilder builderFrom(UserBalanceAuditLog log) {
        return UserBalanceAuditLog.builder()
                .id(log.getId())
                .txUuid(log.getTxUuid())
                .userId(log.getUserId())
                .type(log.getType())
                .source(log.getSource())
                .balanceType(log.getBalanceType())
                .balanceBefore(log.getBalanceBefore())
                .balanceChange(log.getBalanceChange())
                .description(log.getDescription())
                .operatorId(log.getOperatorId())
                .roomId(log.getRoomId())
                .clubId(log.getClubId())
                .tribeId(log.getTribeId())
                .createTime(log.getCreateTime())
                .counterpartyId(log.getCounterpartyId());
    }

    @Getter
    public enum Type {
        BRING_IN(2, "房间带入"),
        BRING_OUT(3, "房间带出"),
        ROOM_FEE(4, "房间服务费"),
        INSURANCE_FEE(8, "保险收入"),
        INSURANCE_COMPENSATION(9, "保险赔付"),
        RECHARGE(11, "充值"),
        TRANSFER(13, "转币"),
        AWARD(14, "赠送"),
        XIAFEN(15, "下分"),
        DIAMOND_RECHARGE(18, "钻石充值"),
        DIAMOND_BUY_GOLD(19, "钻石购买金币");

        private final int value;
        private final String desc;

        Type(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public static Type valueOf(int value) {
            for (Type t : Type.values()) {
                if (t.value == value) {
                    return t;
                }
            }
            throw new IllegalArgumentException(String.valueOf(value));
        }
    }

    @Getter
    public enum Source {
        API(1, "api"),
        OSM(2, "osm"),
        CMS(3, "cms"),
        ROOM(4, "room"),
        PAYMENT(5, "payment");

        private final int value;
        private final String desc;

        Source(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    @Getter
    public enum BalanceType {
        DIAMOND(0, "钻石"),
        CLUB_CHIP(1, "俱乐部币"),
        TRIBE_CHIP(2, "联盟币"),
        GOLD(3, "金币"),
        ;

        private final int value;
        private final String desc;

        BalanceType(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }
    }
}
