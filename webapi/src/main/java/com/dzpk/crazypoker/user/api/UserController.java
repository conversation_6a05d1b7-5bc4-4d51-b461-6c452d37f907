package com.dzpk.crazypoker.user.api;

import com.dzpk.crazypoker.club.repositories.mysql.IClubDao;
import com.dzpk.crazypoker.club.repositories.mysql.model.ClubJoinedPo;
import com.dzpk.crazypoker.common.constant.RespCode;
import com.dzpk.crazypoker.common.dozer.BeanUtil;
import com.dzpk.crazypoker.common.web.resp.CommonRespBuilder;
import com.dzpk.crazypoker.common.web.resp.CommonResponse;
import com.dzpk.crazypoker.oss.service.OssService;
import com.dzpk.crazypoker.user.api.bean.*;
import com.dzpk.crazypoker.user.api.req.*;
import com.dzpk.crazypoker.user.repositories.mysql.autogen.mapper.UserDeviceMapper;
import com.dzpk.crazypoker.user.repositories.mysql.autogen.model.UserDevice;
import com.dzpk.crazypoker.user.service.IUserService;
import com.dzpk.crazypoker.user.service.bo.UserDetailsInfoBo;
import com.dzpk.crazypoker.user.service.bo.UserInfoModBo;
import com.dzpk.crazypoker.user.service.bo.UserRegisterBo;
import com.dzpk.crazypoker.vip.constant.EVipLevel;
import com.dzpk.crazypoker.vip.service.IUserVipService;
import com.dzpk.crazypoker.vip.service.bean.UserVipBaseBo;
import com.dzpk.crazypoker.wallet.api.bean.WalletVo;
import com.dzpk.crazypoker.wallet.service.IUserAccountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@RestController
@Slf4j
@RequestMapping(value = "/user/")
@Api(tags = {"用户模块"})
public class UserController {

    @Autowired
    private IUserService iUserService;

    @Autowired
    private BeanUtil beanUtil;

    @Autowired
    private IUserVipService userVipService;

    @Autowired
    private IUserAccountService userAccountService;

    @Autowired
    private IClubDao clubDao;

    @Autowired
    private UserDeviceMapper userDeviceMapper;

    @Autowired
    private OssService ossService;

    /**
     * 判断手机号是否已经被注册
     *
     * @param existByPhoneReq
     * @return
     */
    @ApiOperation(value = "判断手机号是否已经注册",
            notes = "判断手机号是否已经注册:ture-已经注册,false-未注册")
    @RequestMapping(value = "exist_phone", method = RequestMethod.POST)
    public CommonResponse<Boolean> existByPhone(@Valid @RequestBody ExistByPhoneReq existByPhoneReq) {
        return CommonRespBuilder.succeedBuilder().setData(iUserService.existByPhone(existByPhoneReq.getPhone())).build();
    }

    /**
     * 判断手机号是否已经被注册
     *
     * @param existByNickReq
     * @return
     */
    @ApiOperation(value = "判断昵称是否存在",
            notes = "判断昵称是否存在:ture-已经存在,false-未存在")
    @RequestMapping(value = "exist_nick", method = RequestMethod.POST)
    public CommonResponse<Boolean> existByNick(@Valid @RequestBody ExistByNickReq existByNickReq) {
        //校验名称是否包含非法字符
        if (!iUserService.checkNickName(existByNickReq.getNickName())) {
            RespCode respCode = RespCode.USER_NICK_ILLEGAL;
            return CommonRespBuilder.builder().setStatus(respCode.getCode()).setMsg(respCode.getDesc()).build();
        }

        return CommonRespBuilder.succeedBuilder().setData(iUserService.existByNick(existByNickReq.getNickName())).build();
    }

    /**
     * 用户注册
     *
     * @param userRegisterReq
     * @return
     */
    @ApiOperation(value = "用户注册接口",
            notes = "用户注册接口")
    @RequestMapping(value = "register", method = RequestMethod.POST)
    public CommonResponse<UserRegOrModifyPwdVo> register(@Valid @RequestBody UserRegisterReq userRegisterReq) {
        UserRegisterBo userBo = beanUtil.map(userRegisterReq, UserRegisterBo.class);
        UserDetailsInfoBo infoBo = iUserService.register(userBo, userRegisterReq.getCode());


        UserRegOrModifyPwdVo userRegisterVo = beanUtil.map(infoBo, UserRegOrModifyPwdVo.class);
        return CommonRespBuilder.succeedBuilder().setData(userRegisterVo).build();
    }

    @ApiOperation(value = "通过手机号找回密码",
            notes = "通过手机号找回密码")
    @RequestMapping(value = "mod/pwd", method = RequestMethod.POST)
    public CommonResponse<UserRegOrModifyPwdVo> modifyPassword(@Valid @RequestBody UserModifyPwdReq userModifyPwdReq) {
        UserDetailsInfoBo infoBo = iUserService.modifyPassword(userModifyPwdReq.getPhone(), userModifyPwdReq.getPassword(), userModifyPwdReq.getCode());
        UserRegOrModifyPwdVo userRegisterVo = beanUtil.map(infoBo, UserRegOrModifyPwdVo.class);
        return CommonRespBuilder.succeedBuilder().setData(userRegisterVo).build();

    }

    @ApiOperation(value = "获取用户私人信息",
            notes = "获取用户私人信息")
    @RequestMapping(value = "self_info", method = RequestMethod.POST)
    public CommonResponse<UserPrivacyInfoVo> userInfo(@ApiIgnore @RequestAttribute("user_id") Integer userId) {
        UserPrivacyInfoVo userPrivacyInfoVo = beanUtil.map(iUserService.findById(userId), UserPrivacyInfoVo.class);
        WalletVo walletVo = beanUtil.map(userAccountService.findById(userId), WalletVo.class);
        userPrivacyInfoVo.setWallet(walletVo);
        userPrivacyInfoVo.setChip(walletVo.getChip() + walletVo.getNotExtractChip());
        userPrivacyInfoVo.setGold(userAccountService.getUserGold(userId));

        List<ClubJoinedPo> joinedClubs = clubDao.getJoinedClubs(userId);

        joinedClubs.stream().filter(club -> userId.equals(club.getTribeCreator())).forEach(club -> {
            userPrivacyInfoVo.setTribeId(club.getTribeId());
            userPrivacyInfoVo.setTribeOwner(true);
        });

        userPrivacyInfoVo.setClubIntegral(
                joinedClubs.stream().collect(Collectors.toMap(ClubJoinedPo::getId, ClubJoinedPo::getIntegral)));
        userPrivacyInfoVo.setTribeChips(
                joinedClubs.stream().collect(Collectors.toMap(ClubJoinedPo::getId, ClubJoinedPo::getTribeChips)));

        loadVipInfo(userPrivacyInfoVo);
        return CommonRespBuilder.succeedBuilder().setData(userPrivacyInfoVo).build();
    }

    @ApiOperation(value = "修改用户信息",
            notes = "修改用户信息")
    @RequestMapping(value = "mod/user_info", method = RequestMethod.POST)
    public CommonResponse<ModUserInfoVo> modifyUserInfo(
            @ApiIgnore @RequestAttribute("user_id") Integer userId,
            @Valid @RequestBody UserInfoModReq infoModReq) {

        if (!iUserService.checkNickName(infoModReq.getNickName())) {
            RespCode respCode = RespCode.USER_NICK_ILLEGAL;
            return CommonRespBuilder.builder().setStatus(respCode.getCode()).setMsg(respCode.getDesc()).build();
        }
        UserInfoModBo userInfoModBo = beanUtil.map(infoModReq, UserInfoModBo.class);
        userInfoModBo.setUserId(userId);
        userInfoModBo.setNickName(userInfoModBo.getNickName().trim());
        iUserService.modifyUserInfo(userInfoModBo);
        return CommonRespBuilder.succeedBuilder().setData(ModUserInfoVo.builder().presentation(userInfoModBo.getPresentationChip()).build()).build();
    }

    @ApiOperation(value = "获取用户公开信息信息",
            notes = "获取用户公开信息")
    @RequestMapping(value = "public_info", method = RequestMethod.POST)
    public CommonResponse<UserPublicInfoVo> otherUserInfo(@ApiIgnore
                                                          @RequestAttribute("user_id") Integer userId,
                                                          @Valid @RequestBody UserPublicInfoReq req) {
        UserDetailsInfoBo bo = null;
        if (req.getUserId() == null && req.getRandomId() == null) {
            RespCode respCode = RespCode.USER_NOT_EXIST;
            return CommonRespBuilder.builder().setStatus(respCode.getCode()).setMsg(respCode.getDesc()).build();
        }
        /**
         * 逐渐废弃userID查询
         */
        if (req.getUserId() != null && req.getUserId() > 0) {
            bo = iUserService.findById(req.getUserId());
        } else if (req.getRandomId() != null && !req.getRandomId().trim().isEmpty()) {
            bo = iUserService.findByRandomNum(req.getRandomId());
        }

        if (bo == null) {
            RespCode respCode = RespCode.USER_NOT_EXIST;
            return CommonRespBuilder.builder().setStatus(respCode.getCode()).setMsg(respCode.getDesc()).build();
        }
        UserPublicInfoVo publicInfoVo = beanUtil.map(bo, UserPublicInfoVo.class);
        loadVipInfo(publicInfoVo);
        return CommonRespBuilder.succeedBuilder().setData(publicInfoVo).build();
    }


    @ApiOperation(value = "设置支付密码",
            notes = "设置支付密码")
    @RequestMapping(value = "pay_pwd/save", method = RequestMethod.POST)
    public CommonResponse payPwdSave(@ApiIgnore
                                     @RequestAttribute("user_id") Integer userId,
                                     @Valid @RequestBody UserPayPwdReq req) {
        if (judgeContainsStr(req.getPayPwd())){
            iUserService.payPwdSave(userId, req.getPayPwd(), req.getCode());
            return CommonRespBuilder.succeedBuilder().build();
        }else{
            RespCode respCode = RespCode.USER_PAY_PWD_ILLEGAL;
            return CommonRespBuilder.builder().setStatus(respCode.getCode()).setMsg(respCode.getDesc()).build();
        }


    }

    private static boolean judgeContainsStr(String str) {
        if (str.length()>6&&str.length()<20){
            int i=0;
            boolean matches = Pattern.compile(".*[A-Z]+.*").matcher(str).matches();
            boolean matches1 = Pattern.compile(".*[a-z]+.*").matcher(str).matches();
            boolean matches2 = Pattern.compile(".*[0-9]+.*").matcher(str).matches();
            boolean matches3 = Pattern.compile(".*[#@!*]+.*").matcher(str).matches();
            if (matches)i++;

            if (matches1)i++;

            if (matches2)i++;

            if (matches3)i++;
            return i >= 2;
        }
        return false;
    }

    //TODO 考虑废弃返回VIP信息,由VIP模块提供
    private void loadVipInfo(UserInfoVo userInfoVo) {


        UserVipBaseBo userVipBaseBo = userVipService.findUserVipBaseBoByUserId(userInfoVo.getUserId());
        EVipLevel vipLevel = userVipService.findVipLevel(userVipBaseBo);
        boolean isVip = vipLevel.getLevel() > 0 ? true : false;
        userInfoVo.setVip(vipLevel.getLevel());
        userInfoVo.setVipEndDate(isVip ? userVipBaseBo.getEndDate().getTime() : 0);

    }

    @ApiOperation(value = "更新裝置資料",
            notes = "更新裝置資料")
    @RequestMapping(value = "device", method = RequestMethod.POST)
    public CommonResponse userDevice(@ApiIgnore
                                    @RequestAttribute("user_id") Integer userId,
                                    @Valid @RequestBody UserDeviceReq req) { 
        Integer deviceType = req.getDeviceType();
        String deviceToken = req.getDeviceToken();

        UserDevice userDevice = userDeviceMapper.selectByUserId(userId);
        if(userDevice == null){
            userDevice = new UserDevice();
            userDevice.setUserId(userId);
            userDevice.setDeviceType(deviceType);
            userDevice.setDeviceToken(deviceToken);
            userDeviceMapper.insert(userDevice);
        }else{
            String oldToken = userDevice.getDeviceToken();
            if(!oldToken.equals(deviceToken)){
                userDevice.setDeviceType(deviceType);
                userDevice.setDeviceToken(deviceToken);
                userDeviceMapper.updateByPrimaryKey(userDevice);
            }
        }

        return CommonRespBuilder.succeedBuilder().build();
    }

    @ApiOperation(value = "修改用户默认头像",
            notes = "修改用户默认头像")
    @RequestMapping(value = "setUserHead", method = RequestMethod.POST)
    public CommonResponse<UserDetailsInfoBo> updateHead(@ApiIgnore @RequestAttribute("user_id") Integer userId,
                                                        @RequestBody UserInfoModReq infoModReq) {
        log.info("-----修改用户默认头像-----userId: {}", userId);
        if (userId == null || infoModReq == null || StringUtils.isBlank(infoModReq.getHead())) {
            return CommonRespBuilder.builder().setStatus(RespCode.PARAM_INVALID.getCode()).setMsg(RespCode.PARAM_INVALID.getDesc()).build();
        }
        try {
            // 修改用户默认头像
            UserDetailsInfoBo userDetailsInfoBo = iUserService.setUserHead(userId, infoModReq.getHead());
            if (userDetailsInfoBo == null) {
                log.info("-----修改用户默认头像失败-----");
                return CommonRespBuilder.builder().setStatus(RespCode.FAILED.getCode()).setMsg(RespCode.FAILED.getDesc()).build();
            }
            return CommonRespBuilder.builder().setStatus(RespCode.SUCCEED.getCode()).setMsg(RespCode.SUCCEED.getDesc()).setData(userDetailsInfoBo).build();
        } catch (Exception e) {
            e.printStackTrace();
            log.info("-----修改用户默认头像错误-----e: {}", e.getMessage());
            return CommonRespBuilder.builder().setStatus(RespCode.FAILED.getCode()).setMsg(RespCode.FAILED.getDesc()).build();
        }
    }

    @ApiOperation(value = "修改用户自定义头像",
            notes = "修改用户自定头像")
    @RequestMapping(value = "updateHead", method = RequestMethod.POST)
    public CommonResponse<UserDetailsInfoBo> updateHead(@ApiIgnore
                                    @RequestAttribute("user_id") Integer userId,
                                    MultipartFile file) {
        log.info("-----修改用户自定头像-----userId: {}", userId);
        if (userId == null || file.isEmpty()) {
            return CommonRespBuilder.builder().setStatus(RespCode.PARAM_INVALID.getCode()).setMsg(RespCode.PARAM_INVALID.getDesc()).build();
        }
        try {
            log.info("-----上传图片-----");
            String path = ossService.uploadImage(file, null, null, true, userId, 3);
            log.info("-----上传图片完成-----path: {}", path);
            if (StringUtils.isBlank(path)) {
                return CommonRespBuilder.builder().setStatus(RespCode.FAILED.getCode()).setMsg(RespCode.FAILED.getDesc()).build();
            }
            // 修改用户头像
            UserDetailsInfoBo userDetailsInfoBo = iUserService.updateUserHead(userId, path);
            if (userDetailsInfoBo == null) {
                log.info("-----修改用户自定头像失败-----");
                return CommonRespBuilder.builder().setStatus(RespCode.FAILED.getCode()).setMsg(RespCode.FAILED.getDesc()).build();
            }
            return CommonRespBuilder.builder().setStatus(RespCode.SUCCEED.getCode()).setMsg(RespCode.SUCCEED.getDesc()).setData(userDetailsInfoBo).build();
        } catch (Exception e) {
            e.printStackTrace();
            log.info("-----修改用户自定头像错误-----e: {}", e.getMessage());
            return CommonRespBuilder.builder().setStatus(RespCode.FAILED.getCode()).setMsg(RespCode.FAILED.getDesc()).build();
        }
    }

    @ApiOperation(value = "自定义头像",
            notes = "自定头像")
    @RequestMapping(value = "updateAiHead", method = RequestMethod.POST)
    public CommonResponse<UserDetailsInfoBo> updateAiHead(
            @RequestParam("userId") Integer userId,
            List<MultipartFile> files) {
        log.info("-----修改用户自定头像-----userId: {}", userId);
        if (userId == null || files.size() <= 0) {
            return CommonRespBuilder.builder().setStatus(RespCode.PARAM_INVALID.getCode()).setMsg(RespCode.PARAM_INVALID.getDesc()).build();
        }
        try {
            log.info("-----上传图片-----");
            files.forEach(file -> {
                String path = ossService.uploadImage(file, null, null, true, userId, 9);
                log.info("-----上传图片完成-----path: {}", path);
                if (StringUtils.isBlank(path)) {
                    log.info("====== {} ===============> 上传失败", file.getName());
                }
            });
            return CommonRespBuilder.builder().setStatus(RespCode.SUCCEED.getCode()).setMsg(RespCode.SUCCEED.getDesc()).build();
        } catch (Exception e) {
            e.printStackTrace();
            log.info("-----修改用户自定头像错误-----e: {}", e.getMessage());
            return CommonRespBuilder.builder().setStatus(RespCode.FAILED.getCode()).setMsg(RespCode.FAILED.getDesc()).build();
        }
    }
}
