package com.dzpk.crazypoker.user.api.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

@Setter
@Getter
@ApiModel(value = "判断手机号是否已经注册")
public class ExistByPhoneReq {

    @ApiModelProperty(name = "手机号码",
            required = true,
            notes = "手机号码必须加上地区号并用下划线隔开如:86-12345676789",
            position = 1)
    @NotNull(message = "手机号码必填！")
    @Length(min = 6, max = 36, message = "手机号码加区号不能超过6~36个字符！")
    private String phone;
}
