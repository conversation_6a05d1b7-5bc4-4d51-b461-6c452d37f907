package com.dzpk.crazypoker.user.repositories.mysql.autogen.model;

import java.io.Serializable;

public class UserDelta implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_delta.user_id
     *
     * @mbg.generated
     */
    private Integer userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_delta.game_cnt
     *
     * @mbg.generated
     */
    private Integer gameCnt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_delta.total_hand
     *
     * @mbg.generated
     */
    private Integer totalHand;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_delta.pool_rate
     *
     * @mbg.generated
     */
    private Integer poolRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_delta.pool_win_rate
     *
     * @mbg.generated
     */
    private Integer poolWinRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_delta.allin_win_rate
     *
     * @mbg.generated
     */
    private Integer allinWinRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_delta.af_rate
     *
     * @mbg.generated
     */
    private Integer afRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_delta.prf_rate
     *
     * @mbg.generated
     */
    private Integer prfRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_delta.bet3_rate
     *
     * @mbg.generated
     */
    private Integer bet3Rate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_delta.cbet_rate
     *
     * @mbg.generated
     */
    private Integer cbetRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_delta.tanpai_rate
     *
     * @mbg.generated
     */
    private Integer tanpaiRate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table user_delta
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_delta.user_id
     *
     * @return the value of user_delta.user_id
     *
     * @mbg.generated
     */
    public Integer getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_delta.user_id
     *
     * @param userId the value for user_delta.user_id
     *
     * @mbg.generated
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_delta.game_cnt
     *
     * @return the value of user_delta.game_cnt
     *
     * @mbg.generated
     */
    public Integer getGameCnt() {
        return gameCnt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_delta.game_cnt
     *
     * @param gameCnt the value for user_delta.game_cnt
     *
     * @mbg.generated
     */
    public void setGameCnt(Integer gameCnt) {
        this.gameCnt = gameCnt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_delta.total_hand
     *
     * @return the value of user_delta.total_hand
     *
     * @mbg.generated
     */
    public Integer getTotalHand() {
        return totalHand;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_delta.total_hand
     *
     * @param totalHand the value for user_delta.total_hand
     *
     * @mbg.generated
     */
    public void setTotalHand(Integer totalHand) {
        this.totalHand = totalHand;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_delta.pool_rate
     *
     * @return the value of user_delta.pool_rate
     *
     * @mbg.generated
     */
    public Integer getPoolRate() {
        return poolRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_delta.pool_rate
     *
     * @param poolRate the value for user_delta.pool_rate
     *
     * @mbg.generated
     */
    public void setPoolRate(Integer poolRate) {
        this.poolRate = poolRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_delta.pool_win_rate
     *
     * @return the value of user_delta.pool_win_rate
     *
     * @mbg.generated
     */
    public Integer getPoolWinRate() {
        return poolWinRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_delta.pool_win_rate
     *
     * @param poolWinRate the value for user_delta.pool_win_rate
     *
     * @mbg.generated
     */
    public void setPoolWinRate(Integer poolWinRate) {
        this.poolWinRate = poolWinRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_delta.allin_win_rate
     *
     * @return the value of user_delta.allin_win_rate
     *
     * @mbg.generated
     */
    public Integer getAllinWinRate() {
        return allinWinRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_delta.allin_win_rate
     *
     * @param allinWinRate the value for user_delta.allin_win_rate
     *
     * @mbg.generated
     */
    public void setAllinWinRate(Integer allinWinRate) {
        this.allinWinRate = allinWinRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_delta.af_rate
     *
     * @return the value of user_delta.af_rate
     *
     * @mbg.generated
     */
    public Integer getAfRate() {
        return afRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_delta.af_rate
     *
     * @param afRate the value for user_delta.af_rate
     *
     * @mbg.generated
     */
    public void setAfRate(Integer afRate) {
        this.afRate = afRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_delta.prf_rate
     *
     * @return the value of user_delta.prf_rate
     *
     * @mbg.generated
     */
    public Integer getPrfRate() {
        return prfRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_delta.prf_rate
     *
     * @param prfRate the value for user_delta.prf_rate
     *
     * @mbg.generated
     */
    public void setPrfRate(Integer prfRate) {
        this.prfRate = prfRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_delta.bet3_rate
     *
     * @return the value of user_delta.bet3_rate
     *
     * @mbg.generated
     */
    public Integer getBet3Rate() {
        return bet3Rate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_delta.bet3_rate
     *
     * @param bet3Rate the value for user_delta.bet3_rate
     *
     * @mbg.generated
     */
    public void setBet3Rate(Integer bet3Rate) {
        this.bet3Rate = bet3Rate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_delta.cbet_rate
     *
     * @return the value of user_delta.cbet_rate
     *
     * @mbg.generated
     */
    public Integer getCbetRate() {
        return cbetRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_delta.cbet_rate
     *
     * @param cbetRate the value for user_delta.cbet_rate
     *
     * @mbg.generated
     */
    public void setCbetRate(Integer cbetRate) {
        this.cbetRate = cbetRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_delta.tanpai_rate
     *
     * @return the value of user_delta.tanpai_rate
     *
     * @mbg.generated
     */
    public Integer getTanpaiRate() {
        return tanpaiRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_delta.tanpai_rate
     *
     * @param tanpaiRate the value for user_delta.tanpai_rate
     *
     * @mbg.generated
     */
    public void setTanpaiRate(Integer tanpaiRate) {
        this.tanpaiRate = tanpaiRate;
    }
}