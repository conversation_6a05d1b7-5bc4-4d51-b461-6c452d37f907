package com.dzpk.crazypoker.user.repositories.mysql.autogen.mapper;

import com.dzpk.crazypoker.user.repositories.mysql.autogen.model.UserLoginLog;
import com.dzpk.crazypoker.user.repositories.mysql.autogen.model.UserLoginLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface UserLoginLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_login_log
     *
     * @mbg.generated
     */
    long countByExample(UserLoginLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_login_log
     *
     * @mbg.generated
     */
    int deleteByExample(UserLoginLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_login_log
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_login_log
     *
     * @mbg.generated
     */
    int insert(UserLoginLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_login_log
     *
     * @mbg.generated
     */
    int insertSelective(UserLoginLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_login_log
     *
     * @mbg.generated
     */
    List<UserLoginLog> selectByExample(UserLoginLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_login_log
     *
     * @mbg.generated
     */
    UserLoginLog selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_login_log
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") UserLoginLog record, @Param("example") UserLoginLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_login_log
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") UserLoginLog record, @Param("example") UserLoginLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_login_log
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(UserLoginLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_login_log
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(UserLoginLog record);

    UserLoginLog selectOneByUserId(Integer userId);
}