package com.dzpk.crazypoker.user.api.req;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@ApiModel(description = "更新裝置資料")
public class UserDeviceReq {
     /**
     * 设备类型 (1: Android 2: IOS -1:other)
     */
    @ApiModelProperty(name = "裝置type",
            required = true,
            position = 1
        )
    private Integer deviceType;

    /**
     * 裝置的id
     */
    @ApiModelProperty(name = "裝置id",
            required = true,
            position = 2
        )
    private String deviceToken;

}
