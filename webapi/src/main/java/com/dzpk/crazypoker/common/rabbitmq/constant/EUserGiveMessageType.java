package com.dzpk.crazypoker.common.rabbitmq.constant;

import com.dzpk.crazypoker.common.constant.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * OSM - 消息
 *  类型  1、头像修改赠送  2、首充活动 3、天降红包雨活动
 */
@Getter
@AllArgsConstructor
public enum EUserGiveMessageType implements BaseEnum {
    //*************** 联盟消息 1 - 200 ***************
    MODIFY_HEAD_GIVE(1, "头像修改赠送"),
    FIRST_RECHARGE(2, "首充活动"),
    RED_ENVELOPEN_GIVE(3,"天降红包雨活动"),
    APPOINT_KD_ACTIVITY(4,"指定名单摇金豆活动"),
    ;
    private final int code;
    private final String desc;

    public static EUserGiveMessageType valueOf(Integer code) {
        return BaseEnum.valueOf(EUserGiveMessageType.class, code);
    }
}
