package com.dzpk.crazypoker.common.rabbitmq.client.bean.osm;

import com.dzpk.crazypoker.common.rabbitmq.constant.EOsmMessageCode;
import lombok.*;

/**
 * json参数：申请创建联盟
 *
 * <AUTHOR>
 * @see EOsmMessageCode#TRIBE_KICK_OUT_CLUB
 */
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class TribeKickOutParams extends OsmParams {
    /**
     * 俱乐部ID
     */
    private Integer clubId;
    /**
     * 俱乐部名称
     */
    private String clubName;
    /**
     * 联盟ID
     */
    private Integer tribeId;
    /**
     * 联盟名称
     */
    private String tribeName;
}
