package com.dzpk.crazypoker.common.rabbitmq.client.bean.userbalance;

import lombok.*;

import java.io.Serializable;

@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class UserBalanceSyncMessage implements Serializable {

    /**
     * 用户id
     */
    private int userId;

    /**
     * 俱樂部id
     */
    private int clubId;

    /**
     * 余额
     */
    private int balance;

    /**
     * 幣類 (0=鑽石 1=俱樂部 2=聯盟 3=金币)
     */
    private int coinType;

    /**
     * 时间戳
     */
    private long timestamp;

    public interface CoinType {
        int DIAMOND = 0;
        int CLUB_CHIP = 1;
        int TRIBE_CHIP = 2;
        int GOLD = 3;
    }
}
