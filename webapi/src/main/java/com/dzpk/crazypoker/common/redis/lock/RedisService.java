package com.dzpk.crazypoker.common.redis.lock;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dzpk.crazypoker.common.redis.factory.IRedisInstanceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * RedisService
 *
 * <AUTHOR>
 * @since 2025/4/25
 */
@Component
public class RedisService {

    private final IRedisInstanceFactory redisInstanceFactory;

    @Autowired
    public RedisService(IRedisInstanceFactory redisInstanceFactory) {
        this.redisInstanceFactory = redisInstanceFactory;
    }

    public StringRedisTemplate getStringRedisTemplate() {
        return redisInstanceFactory.defaultRedisInstance().getStringTemplate();
    }

    /**
     * 设置字符串值
     * @param key 键
     * @param value 字符串值
     */
    public void set(String key, String value) {
        getStringRedisTemplate().opsForValue().set(key, value);
    }

    /**
     * 设置字符串值并指定过期时间
     * @param key 键
     * @param value 字符串值
     * @param timeout 过期时间
     * @param unit 时间单位
     */
    public void set(String key, String value, long timeout, TimeUnit unit) {
        getStringRedisTemplate().opsForValue().set(key, value, timeout, unit);
    }

    /**
     * 获取字符串值
     * @param key 键
     * @return 字符串值
     */
    public String get(String key) {
        return getStringRedisTemplate().opsForValue().get(key);
    }

    /**
     * 获取JSON对象
     * @param key 键
     * @return JSON对象
     */
    public JSONObject getJson(String key) {
        String json = get(key);
        if (json == null) {
            return null;
        }
        return JSONObject.parseObject(json);
    }

    /**
     * 设置JSON对象
     * @param key 键
     * @param value JSON对象
     */
    public void setJson(String key, JSONObject value) {
        String json = value.toJSONString();
        set(key, json);
    }

    /**
     * 设置对象（自动序列化为JSON）
     * @param key 键
     * @param value 对象值
     * @param <T> 对象类型
     */
    public <T> void setObject(String key, T value) {
        String json = JSONObject.toJSONString(value);
        set(key, json);
    }

    /**
     * 设置对象并指定过期时间
     * @param key 键
     * @param value 对象值
     * @param timeout 过期时间
     * @param unit 时间单位
     * @param <T> 对象类型
     */
    public <T> void setObject(String key, T value, long timeout, TimeUnit unit) {
        String json = JSONObject.toJSONString(value);
        set(key, json, timeout, unit);
    }

    /**
     * 获取对象（自动反序列化）
     * @param key 键
     * @param clazz 对象类型
     * @param <T> 返回类型
     * @return 对象实例
     */
    public <T> T getObject(String key, Class<T> clazz) {
        String json = get(key);
        return JSONObject.parseObject(json, clazz);
    }

    /**
     * 获取复杂类型对象（支持泛型）
     * @param key 键
     * @param typeReference 类型引用（用于处理泛型）
     * @param <T> 返回类型
     * @return 复杂对象实例
     */
    public <T> T getObject(String key, TypeReference<T> typeReference) {
        String json = get(key);
        return JSONObject.parseObject(json, typeReference.getType());
    }

    /**
     * 删除键
     * @param key 键
     * @return 是否删除成功
     */
    public Boolean remove(String key) {
        return getStringRedisTemplate().delete(key);
    }

    /**
     * 设置过期时间
     * @param key 键
     * @param timeout 过期时间
     * @param unit 时间单位
     * @return 是否设置成功
     */
    public Boolean expire(String key, long timeout, TimeUnit unit) {
        return getStringRedisTemplate().expire(key, timeout, unit);
    }

    /**
     * 检查键是否存在
     * @param key 键
     * @return 是否存在
     */
    public Boolean hasKey(String key) {
        return getStringRedisTemplate().hasKey(key);
    }

    /**
     * 原子操作：仅在键不存在时设置值，并设置过期时间
     * @param key 键
     * @param value 字符串值
     * @param timeout 过期时间
     * @param unit 时间单位
     * @return true-设置成功 false-键已存在
     */
    public boolean setIfAbsent(String key, String value, long timeout, TimeUnit unit) {
        ValueOperations<String, String> ops = getStringRedisTemplate().opsForValue();
        Boolean result = ops.setIfAbsent(key, value, timeout, unit);
        return result != null && result;
    }

    /**
     * 原子操作：仅在键不存在时设置对象（自动序列化为JSON）
     * @param key 键
     * @param value 对象值
     * @param timeout 过期时间
     * @param unit 时间单位
     * @return true-设置成功 false-键已存在
     */
    public <T> boolean setIfAbsent(String key, T value, long timeout, TimeUnit unit) {
        String json = JSONObject.toJSONString(value);
        return setIfAbsent(key, json, timeout, unit);
    }
}
