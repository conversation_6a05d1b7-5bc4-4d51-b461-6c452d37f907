package com.dzpk.crazypoker.common.file;

import lombok.extern.slf4j.Slf4j;

import java.nio.file.*;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class DirectoryWatchService {
    /**
     * 【配置文件】所在目录
     * 绝对路径，否则相对工作目录
     */
    private String fileDir;
    private Path monitorRootDir;

    public DirectoryWatchService(String watchedDir){
        if(null == watchedDir || "".equals(watchedDir.trim())){
            throw new NullPointerException("Directory of monitoring required!");
        }
        this.fileDir = watchedDir.trim();
    }

    /**
     * 文件处理器
     * key -> handler
     * key 是文件名
     */
    private Map<String,IFileChangedHandler> handlerMap = new HashMap<>();
    public void addHandler(IFileChangedHandler handler){
        if(null == handler)
            return;
        if(null == handler.fileName() || "".equals(handler.fileName().trim()))
            return;

        this.handlerMap.put(handler.fileName().trim(),handler);
        this.loadExistingFile(handler.fileName());
    }

    /** 文件监控线程 */
    private FileChangedMonitor fileMonitor;
    private Thread monitorThread;
    public void changedFile(Path filePath,EFileWatchEvent event) throws Exception{
        if(null == filePath)
            return;

        String fileName = filePath.getFileName().toString();
        IFileChangedHandler handler = this.handlerMap.get(fileName);
        if(null == handler){
            log.warn("无法找到处理文件的handler：{}",fileName);
            return;
        }

        handler.handle(filePath,event);
    }

    public void initialize() {
        this.monitorRootDir = Paths.get(this.fileDir).toAbsolutePath();

        if(log.isDebugEnabled()){
            log.debug("【配置文件]监控目录 : {}", this.monitorRootDir);
        }

        this.fileMonitor = new FileChangedMonitor(this.monitorRootDir.toString(),this);
        this.monitorThread = new Thread(this.fileMonitor);
        this.monitorThread.setDaemon(true);
        this.monitorThread.start();
    }

    private void loadExistingFile(String fileName){
        if(null == fileName || "".equals(fileName.trim()))
            return;

        Path path;
        try {
            path = this.monitorRootDir.resolve(fileName);
            this.changedFile(path,EFileWatchEvent.ENTRY_CREATE);
        }catch (Exception ex){
            log.warn("加载已存在配置文件失败：{} -> {}",ex.getMessage(),fileName);
            if(log.isDebugEnabled()){
                log.debug("",ex);
            }
        }
    }

    public void preDestroy(){
        if(null != this.fileMonitor) {
            this.fileMonitor.stop();
        }
    }

    /**
     * 【配置文件】监控线程
     */
    @Slf4j
    private static class FileChangedMonitor implements Runnable{
        private String fileDir;
        private WatchService watchService;

        private DirectoryWatchService changeNotify;

        private boolean isStopped = false;
        private Path realPath;

        public void stop() {
            this.isStopped = true;
            try {
                this.watchService.close();
            }catch (Exception ex){
                log.warn("关闭【配置文件】监听器[ {} ]失败：{}",this.fileDir,ex.getMessage());
                if(log.isDebugEnabled()){
                    log.debug("",ex);
                }
            }
        }

        public FileChangedMonitor(String fileDir,DirectoryWatchService changeNotify){
            this.fileDir= fileDir;
            this.changeNotify = changeNotify;

            try {
                this.realPath = Paths.get(this.fileDir);
                this.watchService = FileSystems.getDefault().newWatchService();
                this.realPath.register(this.watchService,StandardWatchEventKinds.ENTRY_CREATE,
                        StandardWatchEventKinds.ENTRY_MODIFY);
                log.info("成功启动【配置文件】监听：Path={} -> {}",
                        this.fileDir,this.realPath.toString());
            }catch (Exception ex){
                String msg = String.format("启动【配置文件】监听失败：Path=%s -> %s",
                        this.fileDir,this.realPath);
                throw new RuntimeException(msg,ex);
            }
        }

        public void run() {
            while (!this.isStopped) {
                // wait for key to be signaled
                WatchKey wk;
                try {
                    if(log.isDebugEnabled()){
                        log.debug("【配置文件】监听器重新进入监控状态：Path={} -> {}",this.fileDir,this.realPath);
                    }
                    wk = this.watchService.take();
                } catch (InterruptedException ex) {
                    log.error(String.format("【配置文件】监听器已被中断，退出监控: Path=%s -> %s",
                            this.fileDir,this.realPath),ex);
                    break;
                }

                if(log.isDebugEnabled()){
                    log.debug("监控到【配置文件】已修改，执行变更事件: Path={} -> {}",this.fileDir,this.realPath);
                }
                for (WatchEvent<?> event : wk.pollEvents()) {
                    // 忽略【OVERFLOW】
                    // 无法处理，因为overflow的文件不确定
                    WatchEvent.Kind<?> kind = event.kind();
                    EFileWatchEvent fileEvent = this.convert(kind);
                    if (null == fileEvent)
                        continue;

                    Path filename = null;
                    Throwable throwEx = null;
                    try {
                        WatchEvent<Path> ev = (WatchEvent<Path>) event;
                        filename = ev.context();
                        //只监控指定的文件
                        //如果被监控的文件创建或修改，则重新加载此文件的内容
                        filename = this.realPath.resolve(filename);
                        this.changeNotify.changedFile(filename,fileEvent);
                    } catch (Exception ex) {
                        throwEx = ex;
                    }finally {
                        if(throwEx != null){ // 失败
                            log.error(String.format("处理配置文件【 %s 】失败 ， 事件 ： %s  -> 忽略本次事件! ",
                                    filename, fileEvent), throwEx);
                        }else{// 成功
                            log.error(String.format("处理配置文件【 %s 】成功 ， 事件 ： %s ",
                                    filename, fileEvent));
                        }
                    }
                }

                //reset,让当前进入重新监控
                //如果reset失败，则意味着监控失效
                if(wk.isValid()) {
                    boolean valid = wk.reset();
                    if (!valid) {
                        log.error("【配置文件】监听器Rest失败，退出监控: Path={} -> {}",
                                this.fileDir,this.realPath);
                        break;
                    }else{
                        log.info("【配置文件】监听器Rest成功: Path={} -> {}",
                                this.fileDir,this.realPath);
                    }
                }
            }
        }

        private EFileWatchEvent convert(WatchEvent.Kind<?> kind){
            EFileWatchEvent event = null;
            if (kind == StandardWatchEventKinds.ENTRY_CREATE)
                event = EFileWatchEvent.ENTRY_CREATE;
            else if(kind == StandardWatchEventKinds.ENTRY_MODIFY)
                event = EFileWatchEvent.ENTRY_MODIFY;
            else if(kind == StandardWatchEventKinds.ENTRY_DELETE)
                event = EFileWatchEvent.ENTRY_DELETE;

            return event;
        }
    }
}
