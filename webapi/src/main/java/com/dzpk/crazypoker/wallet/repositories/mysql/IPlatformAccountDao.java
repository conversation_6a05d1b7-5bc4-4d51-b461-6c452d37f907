package com.dzpk.crazypoker.wallet.repositories.mysql;

import com.dzpk.crazypoker.platformacc.repositories.mysql.autogen.model.PlatformAccountPo;
import com.dzpk.crazypoker.wallet.repositories.mysql.autogen.model.ChipOrderPo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IPlatformAccountDao {

    @Update("update platform_account set chip = chip + #{chip} where code = #{code}")
    void updatePlatformAccount(int code, Integer chip);

    @Select("select code, chip from platform_account where code = #{code} limit 1")
    PlatformAccountPo queryPlatformAccountByCode(int code);
}
