package com.dzpk.crazypoker.wallet.consumer;


import com.dzpk.crazypoker.common.rabbitmq.client.PayCallBackMessage;
import com.dzpk.crazypoker.wallet.service.IUserAccountService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.connection.PublisherCallbackChannel;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import static com.dzpk.crazypoker.common.rabbitmq.config.RabbitMqConfig.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class PayCallBackMessageConsumer {

    @Autowired
    private IUserAccountService userAccountServicel;

    @RabbitListener(concurrency = "10", bindings = @QueueBinding(
            value = @Queue(value = QUEUE_PAY_CALLBACK, durable = "true"),
            exchange = @Exchange(value = EXCHANGE_PAY_CALLBACK, type = ExchangeTypes.TOPIC, durable = "true"),
            key = ROUTINGKEY_PAY_CALLBACK
    ))
    public void consumeCallback(@Payload PayCallBackMessage message, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                                @Header(PublisherCallbackChannel.RETURNED_MESSAGE_CORRELATION_KEY) String msgId, Channel channel) {
        try {
            if (message == null) {
                log.error("payCallback message is null");
                return;
            }
            if (StringUtils.isEmpty(message.getOrderNo())) {
                log.error("payCallback orderNo is null");
                return;
            }

            if(message.getStatus() == null){
                log.error("payCallback status is null");
                return;
            }

            log.info("pay consume Callback: msgId={}, orderNo={}, status={}",
                    msgId, message.getOrderNo(), message.getStatus());

            userAccountServicel.payCallBack(message.getOrderNo(), message.getStatus());
        } catch (Exception e) {
            log.error("pay consume Callback, deliveryTag={}, msgId={}", deliveryTag, msgId, e);
            try {
                channel.basicNack(deliveryTag, false, false);
            } catch (Exception e1) {
                log.warn("pay channel.basicNack failure ", e1);
            }
        }
    }
}
