package com.dzpk.crazypoker.wallet.service.impl;

import com.dzpk.crazypoker.audit.repositories.mysql.IAuditDao;
import com.dzpk.crazypoker.common.dozer.BeanUtil;
import com.dzpk.crazypoker.common.rabbitmq.client.MessageSender;
import com.dzpk.crazypoker.common.rabbitmq.client.bean.audit.AuditOperationRawdata;
import com.dzpk.crazypoker.wallet.repositories.mysql.autogen.mapper.UserAccountLogPoMapper;
import com.dzpk.crazypoker.wallet.repositories.mysql.autogen.mapper.UserAccountPoMapper;
import com.dzpk.crazypoker.wallet.repositories.mysql.autogen.model.UserAccountLogPo;
import com.dzpk.crazypoker.wallet.repositories.mysql.autogen.model.UserAccountLogPoExample;
import com.dzpk.crazypoker.wallet.repositories.mysql.autogen.model.UserAccountPo;
import com.dzpk.crazypoker.wallet.service.IAuditService;
import com.dzpk.crazypoker.wallet.service.IUserAccountService;
import com.dzpk.crazypoker.wallet.service.bean.UserAccountLogBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Created by jayce on 2019/5/5
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AuditServiceImpl implements IAuditService {

    @Autowired
    private BeanUtil beanUtil;

    @Autowired
    private UserAccountLogPoMapper userAccountLogMapper;

    @Autowired
    private UserAccountPoMapper userAccountMapper;

    @Autowired
    private MessageSender messageSender;

    @Autowired
    private IUserAccountService userAccountService;

    @Autowired
    private IAuditDao auditDao;

    @Override
    public boolean auditUserChip(Integer userId) {
        return true;
        //TODO 屏蔽审计代码
//        //1、查询出玩家的可提取金豆，不可提取金豆，上次审计时间
//        UserAccountBo userAccount = this.userAccountService.findById(userId);
//        if(null == userAccount){
//            return false;
//        }
//        //2、查询流水
//        List<UserAccountLogBo> data = queryRecord(userId,userAccount.getAuditTime());
//        if(null == data || data.isEmpty()){//没有流水记录
//            return true;
//        }
//        //3、校验流水
//        Date auditTime = verifyRecord(userAccount.getChip(),userAccount.getNotExtractChip(),data);
//        //4、更新审计时间
//        if(null == auditTime){
//            return false;
//        }
//        boolean status = updateAuditTime(userId,auditTime);
//
//        return status;
    }

    @Override
    public List<UserAccountLogBo> queryRecord(Integer userId,Date lastAuditTime) {
        UserAccountLogPoExample sel = new UserAccountLogPoExample();
        sel.setOrderByClause("created_time desc");
        sel.or().andUserIdEqualTo(userId).andCreatedTimeGreaterThan(lastAuditTime);
        List<UserAccountLogPo> accountLogPoList = this.userAccountLogMapper.selectByExample(sel);
        if(null == accountLogPoList){
            return null;
        }
        return this.beanUtil.map(accountLogPoList,UserAccountLogBo.class);
    }

    @Override
    public Date verifyRecord(Integer currentChip, Integer currentNChip,List<UserAccountLogBo> data) {
        //最后一次时间,用于更新审计时间使用，下次查询必须大于此时间的流水
        Date auditTime = data.get(0).getCreatedTime();
        //目前与上次审计时的可提取余额差值
        Integer lastAuditChip = currentChip - (data.get(data.size() - 1).getCurrentChip() == null ? 0: data.get(data.size() - 1).getCurrentChip());
        //目前与上次审计时的不可提取余额差值
        Integer lastAuditNChip = currentNChip - (data.get(data.size() - 1).getCurrNotExtractChip() == null ? 0 : data.get(data.size() - 1).getCurrNotExtractChip());
        for(UserAccountLogBo d : data){
            lastAuditChip -= (d.getChangeChip() == null ? 0 :d.getChangeChip());
            lastAuditNChip -= (d.getChangeNotExtractChip() == null ? 0 : d.getChangeNotExtractChip());
        }
        if(lastAuditChip != 0 || lastAuditNChip != 0){//不零和
            return null;
        }

        return auditTime;
    }

    @Override
    public boolean updateAuditTime(Integer userId, Date auditTime) {
        boolean status = userAccountService.updateUserAuditTime(userId, auditTime);
        return status;
    }

    /**
     * 审计操作
     *
     * @param userId
     * @param type
     */
    @Override
    public void auditOperation(Integer userId, Integer type) {
        try {
            UserAccountPo userAccountPo = userAccountMapper.selectByPrimaryKey(userId);
            if(userAccountPo != null) {
                AuditOperationRawdata rawdata = new AuditOperationRawdata();
                rawdata.setType(type);
                rawdata.setUserId(userId);
                rawdata.setAuditTime(new Date());
                Integer  userAccount = (userAccountPo.getChip() == null ? 0 :userAccountPo.getChip())
                        + (userAccountPo.getNotExtractChip() == null ? 0 :userAccountPo.getNotExtractChip())
                        + (userAccountPo.getLockChip() == null ? 0 :userAccountPo.getLockChip());
                rawdata.setUserAccount(userAccount);
                messageSender.sendAuditOperationMessage(rawdata);
                log.info("send audit operation mq message. AuditOperationRawdata:{}.", rawdata);
            }
        } catch (Exception e) {
            log.error("audit operation error. userId:{},  type :{}.", userId, type, e);
        }
    }

    /**
     * 插入告警任务表
     *
     * @param orderId
     * @param uid
     * @param tradeAmount
     * @param userAmount
     * @param type        类型。1-商城充值，2-cms渠道充值，3-提豆，4-充值订单金额与到账金额不符
     * @param tradeTime
     */
    @Override
    public void insertAuditWarnTask(String orderId, Integer uid, Integer tradeAmount, Integer userAmount, int type, Date tradeTime) {
        try {
            auditDao.insertAuditWarnTask(orderId, uid, tradeAmount, userAmount, type, tradeTime);
            log.info("insert into audit warn task success. " +
                            "orderId:【{}】, uid:【{}】,tradeAmount:【{}】,userAmount:【{}】,type:【{}】,tradeTime:【{}】"
                    ,orderId, uid, tradeAmount, userAmount, type, tradeTime);
        } catch (Exception e) {
            log.error("insert audit warn task error.", e);
        }
    }
}
