package com.dzpk.crazypoker.wallet.service;

import com.dzpk.crazypoker.wallet.service.bean.UserAccountLogBo;

import java.util.Date;
import java.util.List;

/**
 * 个人自动审计
 * Created by jayce on 2019/5/5
 *
 * <AUTHOR>
 */
public interface IAuditService {

    /**
     * 审计用户个人流水
     * @param userId 用户id
     * @return
     */
    boolean auditUserChip(Integer userId);

    /**
     * 查询流水
     * @param userId 用户id
     * @param lastAuditTime 上次审计时间
     * @return
     */
    List<UserAccountLogBo> queryRecord(Integer userId,Date lastAuditTime);

    /**
     * 校验流水
     * @param currentChip 当前可用金豆余额
     * @param currentNChip 当前不可用余额
     * @param data 数据源
     * @return
     */
    Date verifyRecord(Integer currentChip,Integer currentNChip,List<UserAccountLogBo> data);

    /**
     * 更新审计时间
     * @param userId 用户id
     * @param auditTime 审计时间
     * @return
     */
    boolean updateAuditTime(Integer userId,Date auditTime);

    /**
     * 审计操作
     * @param userId
     * @param type
     */
    void auditOperation(Integer userId, Integer type);


    /**
     * 插入告警任务表
     * @param orderId
     * @param uid
     * @param tradeAmount
     * @param userAmount
     * @param type  类型。1-商城充值，2-cms渠道充值，3-提豆，4-充值订单金额与到账金额不符
     * @param tradeTime
     */
    public void insertAuditWarnTask(String orderId, Integer uid, Integer tradeAmount, Integer userAmount, int type, Date tradeTime);
}
