package com.dzpk.crazypoker.wallet.repositories.mysql.autogen.model;

import java.io.Serializable;

public class PayChannelBankPoKey implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column pay_channel_bank.pay_channel_id
     *
     * @mbg.generated
     */
    private Integer payChannelId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column pay_channel_bank.bank_code
     *
     * @mbg.generated
     */
    private String bankCode;

    private String paymentCode;

    public String getPaymentCode() {
        return paymentCode;
    }

    public void setPaymentCode(String paymentCode) {
        this.paymentCode = paymentCode;
    }

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table pay_channel_bank
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column pay_channel_bank.pay_channel_id
     *
     * @return the value of pay_channel_bank.pay_channel_id
     *
     * @mbg.generated
     */
    public Integer getPayChannelId() {
        return payChannelId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column pay_channel_bank.pay_channel_id
     *
     * @param payChannelId the value for pay_channel_bank.pay_channel_id
     *
     * @mbg.generated
     */
    public void setPayChannelId(Integer payChannelId) {
        this.payChannelId = payChannelId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column pay_channel_bank.bank_code
     *
     * @return the value of pay_channel_bank.bank_code
     *
     * @mbg.generated
     */
    public String getBankCode() {
        return bankCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column pay_channel_bank.bank_code
     *
     * @param bankCode the value for pay_channel_bank.bank_code
     *
     * @mbg.generated
     */
    public void setBankCode(String bankCode) {
        this.bankCode = bankCode == null ? null : bankCode.trim();
    }
}