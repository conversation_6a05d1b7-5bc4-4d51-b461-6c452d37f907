package com.dzpk.crazypoker.wallet.repositories.mysql.autogen.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ChipOrderPoExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table chip_order
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table chip_order
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table chip_order
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table chip_order
     *
     * @mbg.generated
     */
    public ChipOrderPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table chip_order
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table chip_order
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table chip_order
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table chip_order
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table chip_order
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table chip_order
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table chip_order
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table chip_order
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table chip_order
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table chip_order
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table chip_order
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andUidIsNull() {
            addCriterion("uid is null");
            return (Criteria) this;
        }

        public Criteria andUidIsNotNull() {
            addCriterion("uid is not null");
            return (Criteria) this;
        }

        public Criteria andUidEqualTo(Integer value) {
            addCriterion("uid =", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotEqualTo(Integer value) {
            addCriterion("uid <>", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidGreaterThan(Integer value) {
            addCriterion("uid >", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidGreaterThanOrEqualTo(Integer value) {
            addCriterion("uid >=", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidLessThan(Integer value) {
            addCriterion("uid <", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidLessThanOrEqualTo(Integer value) {
            addCriterion("uid <=", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidIn(List<Integer> values) {
            addCriterion("uid in", values, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotIn(List<Integer> values) {
            addCriterion("uid not in", values, "uid");
            return (Criteria) this;
        }

        public Criteria andUidBetween(Integer value1, Integer value2) {
            addCriterion("uid between", value1, value2, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotBetween(Integer value1, Integer value2) {
            addCriterion("uid not between", value1, value2, "uid");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("order_no is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("order_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("order_no =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("order_no <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("order_no >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("order_no >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("order_no <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("order_no <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("order_no like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("order_no not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("order_no in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("order_no not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("order_no between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("order_no not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andEscrowOrderNoIsNull() {
            addCriterion("escrow_order_no is null");
            return (Criteria) this;
        }

        public Criteria andEscrowOrderNoIsNotNull() {
            addCriterion("escrow_order_no is not null");
            return (Criteria) this;
        }

        public Criteria andEscrowOrderNoEqualTo(String value) {
            addCriterion("escrow_order_no =", value, "escrowOrderNo");
            return (Criteria) this;
        }

        public Criteria andEscrowOrderNoNotEqualTo(String value) {
            addCriterion("escrow_order_no <>", value, "escrowOrderNo");
            return (Criteria) this;
        }

        public Criteria andEscrowOrderNoGreaterThan(String value) {
            addCriterion("escrow_order_no >", value, "escrowOrderNo");
            return (Criteria) this;
        }

        public Criteria andEscrowOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("escrow_order_no >=", value, "escrowOrderNo");
            return (Criteria) this;
        }

        public Criteria andEscrowOrderNoLessThan(String value) {
            addCriterion("escrow_order_no <", value, "escrowOrderNo");
            return (Criteria) this;
        }

        public Criteria andEscrowOrderNoLessThanOrEqualTo(String value) {
            addCriterion("escrow_order_no <=", value, "escrowOrderNo");
            return (Criteria) this;
        }

        public Criteria andEscrowOrderNoLike(String value) {
            addCriterion("escrow_order_no like", value, "escrowOrderNo");
            return (Criteria) this;
        }

        public Criteria andEscrowOrderNoNotLike(String value) {
            addCriterion("escrow_order_no not like", value, "escrowOrderNo");
            return (Criteria) this;
        }

        public Criteria andEscrowOrderNoIn(List<String> values) {
            addCriterion("escrow_order_no in", values, "escrowOrderNo");
            return (Criteria) this;
        }

        public Criteria andEscrowOrderNoNotIn(List<String> values) {
            addCriterion("escrow_order_no not in", values, "escrowOrderNo");
            return (Criteria) this;
        }

        public Criteria andEscrowOrderNoBetween(String value1, String value2) {
            addCriterion("escrow_order_no between", value1, value2, "escrowOrderNo");
            return (Criteria) this;
        }

        public Criteria andEscrowOrderNoNotBetween(String value1, String value2) {
            addCriterion("escrow_order_no not between", value1, value2, "escrowOrderNo");
            return (Criteria) this;
        }

        public Criteria andChipNumIsNull() {
            addCriterion("chip_num is null");
            return (Criteria) this;
        }

        public Criteria andChipNumIsNotNull() {
            addCriterion("chip_num is not null");
            return (Criteria) this;
        }

        public Criteria andChipNumEqualTo(Integer value) {
            addCriterion("chip_num =", value, "chipNum");
            return (Criteria) this;
        }

        public Criteria andChipNumNotEqualTo(Integer value) {
            addCriterion("chip_num <>", value, "chipNum");
            return (Criteria) this;
        }

        public Criteria andChipNumGreaterThan(Integer value) {
            addCriterion("chip_num >", value, "chipNum");
            return (Criteria) this;
        }

        public Criteria andChipNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("chip_num >=", value, "chipNum");
            return (Criteria) this;
        }

        public Criteria andChipNumLessThan(Integer value) {
            addCriterion("chip_num <", value, "chipNum");
            return (Criteria) this;
        }

        public Criteria andChipNumLessThanOrEqualTo(Integer value) {
            addCriterion("chip_num <=", value, "chipNum");
            return (Criteria) this;
        }

        public Criteria andChipNumIn(List<Integer> values) {
            addCriterion("chip_num in", values, "chipNum");
            return (Criteria) this;
        }

        public Criteria andChipNumNotIn(List<Integer> values) {
            addCriterion("chip_num not in", values, "chipNum");
            return (Criteria) this;
        }

        public Criteria andChipNumBetween(Integer value1, Integer value2) {
            addCriterion("chip_num between", value1, value2, "chipNum");
            return (Criteria) this;
        }

        public Criteria andChipNumNotBetween(Integer value1, Integer value2) {
            addCriterion("chip_num not between", value1, value2, "chipNum");
            return (Criteria) this;
        }

        public Criteria andAmountIsNull() {
            addCriterion("amount is null");
            return (Criteria) this;
        }

        public Criteria andAmountIsNotNull() {
            addCriterion("amount is not null");
            return (Criteria) this;
        }

        public Criteria andAmountEqualTo(Double value) {
            addCriterion("amount =", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotEqualTo(Double value) {
            addCriterion("amount <>", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThan(Double value) {
            addCriterion("amount >", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThanOrEqualTo(Double value) {
            addCriterion("amount >=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThan(Double value) {
            addCriterion("amount <", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThanOrEqualTo(Double value) {
            addCriterion("amount <=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountIn(List<Double> values) {
            addCriterion("amount in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotIn(List<Double> values) {
            addCriterion("amount not in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountBetween(Double value1, Double value2) {
            addCriterion("amount between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotBetween(Double value1, Double value2) {
            addCriterion("amount not between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andExpStatusIsNull() {
            addCriterion("exp_status is null");
            return (Criteria) this;
        }

        public Criteria andExpStatusIsNotNull() {
            addCriterion("exp_status is not null");
            return (Criteria) this;
        }

        public Criteria andExpStatusEqualTo(Integer value) {
            addCriterion("exp_status =", value, "expStatus");
            return (Criteria) this;
        }

        public Criteria andExpStatusNotEqualTo(Integer value) {
            addCriterion("exp_status <>", value, "expStatus");
            return (Criteria) this;
        }

        public Criteria andExpStatusGreaterThan(Integer value) {
            addCriterion("exp_status >", value, "expStatus");
            return (Criteria) this;
        }

        public Criteria andExpStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("exp_status >=", value, "expStatus");
            return (Criteria) this;
        }

        public Criteria andExpStatusLessThan(Integer value) {
            addCriterion("exp_status <", value, "expStatus");
            return (Criteria) this;
        }

        public Criteria andExpStatusLessThanOrEqualTo(Integer value) {
            addCriterion("exp_status <=", value, "expStatus");
            return (Criteria) this;
        }

        public Criteria andExpStatusIn(List<Integer> values) {
            addCriterion("exp_status in", values, "expStatus");
            return (Criteria) this;
        }

        public Criteria andExpStatusNotIn(List<Integer> values) {
            addCriterion("exp_status not in", values, "expStatus");
            return (Criteria) this;
        }

        public Criteria andExpStatusBetween(Integer value1, Integer value2) {
            addCriterion("exp_status between", value1, value2, "expStatus");
            return (Criteria) this;
        }

        public Criteria andExpStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("exp_status not between", value1, value2, "expStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentWayIsNull() {
            addCriterion("payment_way is null");
            return (Criteria) this;
        }

        public Criteria andPaymentWayIsNotNull() {
            addCriterion("payment_way is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentWayEqualTo(Integer value) {
            addCriterion("payment_way =", value, "paymentWay");
            return (Criteria) this;
        }

        public Criteria andPaymentWayNotEqualTo(Integer value) {
            addCriterion("payment_way <>", value, "paymentWay");
            return (Criteria) this;
        }

        public Criteria andPaymentWayGreaterThan(Integer value) {
            addCriterion("payment_way >", value, "paymentWay");
            return (Criteria) this;
        }

        public Criteria andPaymentWayGreaterThanOrEqualTo(Integer value) {
            addCriterion("payment_way >=", value, "paymentWay");
            return (Criteria) this;
        }

        public Criteria andPaymentWayLessThan(Integer value) {
            addCriterion("payment_way <", value, "paymentWay");
            return (Criteria) this;
        }

        public Criteria andPaymentWayLessThanOrEqualTo(Integer value) {
            addCriterion("payment_way <=", value, "paymentWay");
            return (Criteria) this;
        }

        public Criteria andPaymentWayIn(List<Integer> values) {
            addCriterion("payment_way in", values, "paymentWay");
            return (Criteria) this;
        }

        public Criteria andPaymentWayNotIn(List<Integer> values) {
            addCriterion("payment_way not in", values, "paymentWay");
            return (Criteria) this;
        }

        public Criteria andPaymentWayBetween(Integer value1, Integer value2) {
            addCriterion("payment_way between", value1, value2, "paymentWay");
            return (Criteria) this;
        }

        public Criteria andPaymentWayNotBetween(Integer value1, Integer value2) {
            addCriterion("payment_way not between", value1, value2, "paymentWay");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeIsNull() {
            addCriterion("payment_code is null");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeIsNotNull() {
            addCriterion("payment_code is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeEqualTo(String value) {
            addCriterion("payment_code =", value, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeNotEqualTo(String value) {
            addCriterion("payment_code <>", value, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeGreaterThan(String value) {
            addCriterion("payment_code >", value, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeGreaterThanOrEqualTo(String value) {
            addCriterion("payment_code >=", value, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeLessThan(String value) {
            addCriterion("payment_code <", value, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeLessThanOrEqualTo(String value) {
            addCriterion("payment_code <=", value, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeLike(String value) {
            addCriterion("payment_code like", value, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeNotLike(String value) {
            addCriterion("payment_code not like", value, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeIn(List<String> values) {
            addCriterion("payment_code in", values, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeNotIn(List<String> values) {
            addCriterion("payment_code not in", values, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeBetween(String value1, String value2) {
            addCriterion("payment_code between", value1, value2, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeNotBetween(String value1, String value2) {
            addCriterion("payment_code not between", value1, value2, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andAppTypeIsNull() {
            addCriterion("app_type is null");
            return (Criteria) this;
        }

        public Criteria andAppTypeIsNotNull() {
            addCriterion("app_type is not null");
            return (Criteria) this;
        }

        public Criteria andAppTypeEqualTo(Integer value) {
            addCriterion("app_type =", value, "appType");
            return (Criteria) this;
        }

        public Criteria andAppTypeNotEqualTo(Integer value) {
            addCriterion("app_type <>", value, "appType");
            return (Criteria) this;
        }

        public Criteria andAppTypeGreaterThan(Integer value) {
            addCriterion("app_type >", value, "appType");
            return (Criteria) this;
        }

        public Criteria andAppTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_type >=", value, "appType");
            return (Criteria) this;
        }

        public Criteria andAppTypeLessThan(Integer value) {
            addCriterion("app_type <", value, "appType");
            return (Criteria) this;
        }

        public Criteria andAppTypeLessThanOrEqualTo(Integer value) {
            addCriterion("app_type <=", value, "appType");
            return (Criteria) this;
        }

        public Criteria andAppTypeIn(List<Integer> values) {
            addCriterion("app_type in", values, "appType");
            return (Criteria) this;
        }

        public Criteria andAppTypeNotIn(List<Integer> values) {
            addCriterion("app_type not in", values, "appType");
            return (Criteria) this;
        }

        public Criteria andAppTypeBetween(Integer value1, Integer value2) {
            addCriterion("app_type between", value1, value2, "appType");
            return (Criteria) this;
        }

        public Criteria andAppTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("app_type not between", value1, value2, "appType");
            return (Criteria) this;
        }

        public Criteria andTradeTimeIsNull() {
            addCriterion("trade_time is null");
            return (Criteria) this;
        }

        public Criteria andTradeTimeIsNotNull() {
            addCriterion("trade_time is not null");
            return (Criteria) this;
        }

        public Criteria andTradeTimeEqualTo(Date value) {
            addCriterion("trade_time =", value, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeNotEqualTo(Date value) {
            addCriterion("trade_time <>", value, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeGreaterThan(Date value) {
            addCriterion("trade_time >", value, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("trade_time >=", value, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeLessThan(Date value) {
            addCriterion("trade_time <", value, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeLessThanOrEqualTo(Date value) {
            addCriterion("trade_time <=", value, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeIn(List<Date> values) {
            addCriterion("trade_time in", values, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeNotIn(List<Date> values) {
            addCriterion("trade_time not in", values, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeBetween(Date value1, Date value2) {
            addCriterion("trade_time between", value1, value2, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeNotBetween(Date value1, Date value2) {
            addCriterion("trade_time not between", value1, value2, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andPayChannelIdIsNull() {
            addCriterion("pay_channel_id is null");
            return (Criteria) this;
        }

        public Criteria andPayChannelIdIsNotNull() {
            addCriterion("pay_channel_id is not null");
            return (Criteria) this;
        }

        public Criteria andPayChannelIdEqualTo(Integer value) {
            addCriterion("pay_channel_id =", value, "payChannelId");
            return (Criteria) this;
        }

        public Criteria andPayChannelIdNotEqualTo(Integer value) {
            addCriterion("pay_channel_id <>", value, "payChannelId");
            return (Criteria) this;
        }

        public Criteria andPayChannelIdGreaterThan(Integer value) {
            addCriterion("pay_channel_id >", value, "payChannelId");
            return (Criteria) this;
        }

        public Criteria andPayChannelIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("pay_channel_id >=", value, "payChannelId");
            return (Criteria) this;
        }

        public Criteria andPayChannelIdLessThan(Integer value) {
            addCriterion("pay_channel_id <", value, "payChannelId");
            return (Criteria) this;
        }

        public Criteria andPayChannelIdLessThanOrEqualTo(Integer value) {
            addCriterion("pay_channel_id <=", value, "payChannelId");
            return (Criteria) this;
        }

        public Criteria andPayChannelIdIn(List<Integer> values) {
            addCriterion("pay_channel_id in", values, "payChannelId");
            return (Criteria) this;
        }

        public Criteria andPayChannelIdNotIn(List<Integer> values) {
            addCriterion("pay_channel_id not in", values, "payChannelId");
            return (Criteria) this;
        }

        public Criteria andPayChannelIdBetween(Integer value1, Integer value2) {
            addCriterion("pay_channel_id between", value1, value2, "payChannelId");
            return (Criteria) this;
        }

        public Criteria andPayChannelIdNotBetween(Integer value1, Integer value2) {
            addCriterion("pay_channel_id not between", value1, value2, "payChannelId");
            return (Criteria) this;
        }

        public Criteria andPayChannelCmsIdIsNull() {
            addCriterion("pay_channel_cms_id is null");
            return (Criteria) this;
        }

        public Criteria andPayChannelCmsIdIsNotNull() {
            addCriterion("pay_channel_cms_id is not null");
            return (Criteria) this;
        }

        public Criteria andPayChannelCmsIdEqualTo(Integer value) {
            addCriterion("pay_channel_cms_id =", value, "payChannelCmsId");
            return (Criteria) this;
        }

        public Criteria andPayChannelCmsIdNotEqualTo(Integer value) {
            addCriterion("pay_channel_cms_id <>", value, "payChannelCmsId");
            return (Criteria) this;
        }

        public Criteria andPayChannelCmsIdGreaterThan(Integer value) {
            addCriterion("pay_channel_cms_id >", value, "payChannelCmsId");
            return (Criteria) this;
        }

        public Criteria andPayChannelCmsIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("pay_channel_cms_id >=", value, "payChannelCmsId");
            return (Criteria) this;
        }

        public Criteria andPayChannelCmsIdLessThan(Integer value) {
            addCriterion("pay_channel_cms_id <", value, "payChannelCmsId");
            return (Criteria) this;
        }

        public Criteria andPayChannelCmsIdLessThanOrEqualTo(Integer value) {
            addCriterion("pay_channel_cms_id <=", value, "payChannelCmsId");
            return (Criteria) this;
        }

        public Criteria andPayChannelCmsIdIn(List<Integer> values) {
            addCriterion("pay_channel_cms_id in", values, "payChannelCmsId");
            return (Criteria) this;
        }

        public Criteria andPayChannelCmsIdNotIn(List<Integer> values) {
            addCriterion("pay_channel_cms_id not in", values, "payChannelCmsId");
            return (Criteria) this;
        }

        public Criteria andPayChannelCmsIdBetween(Integer value1, Integer value2) {
            addCriterion("pay_channel_cms_id between", value1, value2, "payChannelCmsId");
            return (Criteria) this;
        }

        public Criteria andPayChannelCmsIdNotBetween(Integer value1, Integer value2) {
            addCriterion("pay_channel_cms_id not between", value1, value2, "payChannelCmsId");
            return (Criteria) this;
        }

        public Criteria andPayerNameIsNull() {
            addCriterion("payer_name is null");
            return (Criteria) this;
        }

        public Criteria andPayerNameIsNotNull() {
            addCriterion("payer_name is not null");
            return (Criteria) this;
        }

        public Criteria andPayerNameEqualTo(String value) {
            addCriterion("payer_name =", value, "payerName");
            return (Criteria) this;
        }

        public Criteria andPayerNameNotEqualTo(String value) {
            addCriterion("payer_name <>", value, "payerName");
            return (Criteria) this;
        }

        public Criteria andPayerNameGreaterThan(String value) {
            addCriterion("payer_name >", value, "payerName");
            return (Criteria) this;
        }

        public Criteria andPayerNameGreaterThanOrEqualTo(String value) {
            addCriterion("payer_name >=", value, "payerName");
            return (Criteria) this;
        }

        public Criteria andPayerNameLessThan(String value) {
            addCriterion("payer_name <", value, "payerName");
            return (Criteria) this;
        }

        public Criteria andPayerNameLessThanOrEqualTo(String value) {
            addCriterion("payer_name <=", value, "payerName");
            return (Criteria) this;
        }

        public Criteria andPayerNameLike(String value) {
            addCriterion("payer_name like", value, "payerName");
            return (Criteria) this;
        }

        public Criteria andPayerNameNotLike(String value) {
            addCriterion("payer_name not like", value, "payerName");
            return (Criteria) this;
        }

        public Criteria andPayerNameIn(List<String> values) {
            addCriterion("payer_name in", values, "payerName");
            return (Criteria) this;
        }

        public Criteria andPayerNameNotIn(List<String> values) {
            addCriterion("payer_name not in", values, "payerName");
            return (Criteria) this;
        }

        public Criteria andPayerNameBetween(String value1, String value2) {
            addCriterion("payer_name between", value1, value2, "payerName");
            return (Criteria) this;
        }

        public Criteria andPayerNameNotBetween(String value1, String value2) {
            addCriterion("payer_name not between", value1, value2, "payerName");
            return (Criteria) this;
        }

        public Criteria andPayerBankNameIsNull() {
            addCriterion("payer_bank_name is null");
            return (Criteria) this;
        }

        public Criteria andPayerBankNameIsNotNull() {
            addCriterion("payer_bank_name is not null");
            return (Criteria) this;
        }

        public Criteria andPayerBankNameEqualTo(String value) {
            addCriterion("payer_bank_name =", value, "payerBankName");
            return (Criteria) this;
        }

        public Criteria andPayerBankNameNotEqualTo(String value) {
            addCriterion("payer_bank_name <>", value, "payerBankName");
            return (Criteria) this;
        }

        public Criteria andPayerBankNameGreaterThan(String value) {
            addCriterion("payer_bank_name >", value, "payerBankName");
            return (Criteria) this;
        }

        public Criteria andPayerBankNameGreaterThanOrEqualTo(String value) {
            addCriterion("payer_bank_name >=", value, "payerBankName");
            return (Criteria) this;
        }

        public Criteria andPayerBankNameLessThan(String value) {
            addCriterion("payer_bank_name <", value, "payerBankName");
            return (Criteria) this;
        }

        public Criteria andPayerBankNameLessThanOrEqualTo(String value) {
            addCriterion("payer_bank_name <=", value, "payerBankName");
            return (Criteria) this;
        }

        public Criteria andPayerBankNameLike(String value) {
            addCriterion("payer_bank_name like", value, "payerBankName");
            return (Criteria) this;
        }

        public Criteria andPayerBankNameNotLike(String value) {
            addCriterion("payer_bank_name not like", value, "payerBankName");
            return (Criteria) this;
        }

        public Criteria andPayerBankNameIn(List<String> values) {
            addCriterion("payer_bank_name in", values, "payerBankName");
            return (Criteria) this;
        }

        public Criteria andPayerBankNameNotIn(List<String> values) {
            addCriterion("payer_bank_name not in", values, "payerBankName");
            return (Criteria) this;
        }

        public Criteria andPayerBankNameBetween(String value1, String value2) {
            addCriterion("payer_bank_name between", value1, value2, "payerBankName");
            return (Criteria) this;
        }

        public Criteria andPayerBankNameNotBetween(String value1, String value2) {
            addCriterion("payer_bank_name not between", value1, value2, "payerBankName");
            return (Criteria) this;
        }

        public Criteria andPayerBankCodeIsNull() {
            addCriterion("payer_bank_code is null");
            return (Criteria) this;
        }

        public Criteria andPayerBankCodeIsNotNull() {
            addCriterion("payer_bank_code is not null");
            return (Criteria) this;
        }

        public Criteria andPayerBankCodeEqualTo(String value) {
            addCriterion("payer_bank_code =", value, "payerBankCode");
            return (Criteria) this;
        }

        public Criteria andPayerBankCodeNotEqualTo(String value) {
            addCriterion("payer_bank_code <>", value, "payerBankCode");
            return (Criteria) this;
        }

        public Criteria andPayerBankCodeGreaterThan(String value) {
            addCriterion("payer_bank_code >", value, "payerBankCode");
            return (Criteria) this;
        }

        public Criteria andPayerBankCodeGreaterThanOrEqualTo(String value) {
            addCriterion("payer_bank_code >=", value, "payerBankCode");
            return (Criteria) this;
        }

        public Criteria andPayerBankCodeLessThan(String value) {
            addCriterion("payer_bank_code <", value, "payerBankCode");
            return (Criteria) this;
        }

        public Criteria andPayerBankCodeLessThanOrEqualTo(String value) {
            addCriterion("payer_bank_code <=", value, "payerBankCode");
            return (Criteria) this;
        }

        public Criteria andPayerBankCodeLike(String value) {
            addCriterion("payer_bank_code like", value, "payerBankCode");
            return (Criteria) this;
        }

        public Criteria andPayerBankCodeNotLike(String value) {
            addCriterion("payer_bank_code not like", value, "payerBankCode");
            return (Criteria) this;
        }

        public Criteria andPayerBankCodeIn(List<String> values) {
            addCriterion("payer_bank_code in", values, "payerBankCode");
            return (Criteria) this;
        }

        public Criteria andPayerBankCodeNotIn(List<String> values) {
            addCriterion("payer_bank_code not in", values, "payerBankCode");
            return (Criteria) this;
        }

        public Criteria andPayerBankCodeBetween(String value1, String value2) {
            addCriterion("payer_bank_code between", value1, value2, "payerBankCode");
            return (Criteria) this;
        }

        public Criteria andPayerBankCodeNotBetween(String value1, String value2) {
            addCriterion("payer_bank_code not between", value1, value2, "payerBankCode");
            return (Criteria) this;
        }

        public Criteria andPayerBankNumberIsNull() {
            addCriterion("payer_bank_number is null");
            return (Criteria) this;
        }

        public Criteria andPayerBankNumberIsNotNull() {
            addCriterion("payer_bank_number is not null");
            return (Criteria) this;
        }

        public Criteria andPayerBankNumberEqualTo(String value) {
            addCriterion("payer_bank_number =", value, "payerBankNumber");
            return (Criteria) this;
        }

        public Criteria andPayerBankNumberNotEqualTo(String value) {
            addCriterion("payer_bank_number <>", value, "payerBankNumber");
            return (Criteria) this;
        }

        public Criteria andPayerBankNumberGreaterThan(String value) {
            addCriterion("payer_bank_number >", value, "payerBankNumber");
            return (Criteria) this;
        }

        public Criteria andPayerBankNumberGreaterThanOrEqualTo(String value) {
            addCriterion("payer_bank_number >=", value, "payerBankNumber");
            return (Criteria) this;
        }

        public Criteria andPayerBankNumberLessThan(String value) {
            addCriterion("payer_bank_number <", value, "payerBankNumber");
            return (Criteria) this;
        }

        public Criteria andPayerBankNumberLessThanOrEqualTo(String value) {
            addCriterion("payer_bank_number <=", value, "payerBankNumber");
            return (Criteria) this;
        }

        public Criteria andPayerBankNumberLike(String value) {
            addCriterion("payer_bank_number like", value, "payerBankNumber");
            return (Criteria) this;
        }

        public Criteria andPayerBankNumberNotLike(String value) {
            addCriterion("payer_bank_number not like", value, "payerBankNumber");
            return (Criteria) this;
        }

        public Criteria andPayerBankNumberIn(List<String> values) {
            addCriterion("payer_bank_number in", values, "payerBankNumber");
            return (Criteria) this;
        }

        public Criteria andPayerBankNumberNotIn(List<String> values) {
            addCriterion("payer_bank_number not in", values, "payerBankNumber");
            return (Criteria) this;
        }

        public Criteria andPayerBankNumberBetween(String value1, String value2) {
            addCriterion("payer_bank_number between", value1, value2, "payerBankNumber");
            return (Criteria) this;
        }

        public Criteria andPayerBankNumberNotBetween(String value1, String value2) {
            addCriterion("payer_bank_number not between", value1, value2, "payerBankNumber");
            return (Criteria) this;
        }

        public Criteria andClientIpIsNull() {
            addCriterion("client_ip is null");
            return (Criteria) this;
        }

        public Criteria andClientIpIsNotNull() {
            addCriterion("client_ip is not null");
            return (Criteria) this;
        }

        public Criteria andClientIpEqualTo(String value) {
            addCriterion("client_ip =", value, "clientIp");
            return (Criteria) this;
        }

        public Criteria andClientIpNotEqualTo(String value) {
            addCriterion("client_ip <>", value, "clientIp");
            return (Criteria) this;
        }

        public Criteria andClientIpGreaterThan(String value) {
            addCriterion("client_ip >", value, "clientIp");
            return (Criteria) this;
        }

        public Criteria andClientIpGreaterThanOrEqualTo(String value) {
            addCriterion("client_ip >=", value, "clientIp");
            return (Criteria) this;
        }

        public Criteria andClientIpLessThan(String value) {
            addCriterion("client_ip <", value, "clientIp");
            return (Criteria) this;
        }

        public Criteria andClientIpLessThanOrEqualTo(String value) {
            addCriterion("client_ip <=", value, "clientIp");
            return (Criteria) this;
        }

        public Criteria andClientIpLike(String value) {
            addCriterion("client_ip like", value, "clientIp");
            return (Criteria) this;
        }

        public Criteria andClientIpNotLike(String value) {
            addCriterion("client_ip not like", value, "clientIp");
            return (Criteria) this;
        }

        public Criteria andClientIpIn(List<String> values) {
            addCriterion("client_ip in", values, "clientIp");
            return (Criteria) this;
        }

        public Criteria andClientIpNotIn(List<String> values) {
            addCriterion("client_ip not in", values, "clientIp");
            return (Criteria) this;
        }

        public Criteria andClientIpBetween(String value1, String value2) {
            addCriterion("client_ip between", value1, value2, "clientIp");
            return (Criteria) this;
        }

        public Criteria andClientIpNotBetween(String value1, String value2) {
            addCriterion("client_ip not between", value1, value2, "clientIp");
            return (Criteria) this;
        }

        public Criteria andManualHandlingFlagIsNull() {
            addCriterion("manual_handling_flag is null");
            return (Criteria) this;
        }

        public Criteria andManualHandlingFlagIsNotNull() {
            addCriterion("manual_handling_flag is not null");
            return (Criteria) this;
        }

        public Criteria andManualHandlingFlagEqualTo(Integer value) {
            addCriterion("manual_handling_flag =", value, "manualHandlingFlag");
            return (Criteria) this;
        }

        public Criteria andManualHandlingFlagNotEqualTo(Integer value) {
            addCriterion("manual_handling_flag <>", value, "manualHandlingFlag");
            return (Criteria) this;
        }

        public Criteria andManualHandlingFlagGreaterThan(Integer value) {
            addCriterion("manual_handling_flag >", value, "manualHandlingFlag");
            return (Criteria) this;
        }

        public Criteria andManualHandlingFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("manual_handling_flag >=", value, "manualHandlingFlag");
            return (Criteria) this;
        }

        public Criteria andManualHandlingFlagLessThan(Integer value) {
            addCriterion("manual_handling_flag <", value, "manualHandlingFlag");
            return (Criteria) this;
        }

        public Criteria andManualHandlingFlagLessThanOrEqualTo(Integer value) {
            addCriterion("manual_handling_flag <=", value, "manualHandlingFlag");
            return (Criteria) this;
        }

        public Criteria andManualHandlingFlagIn(List<Integer> values) {
            addCriterion("manual_handling_flag in", values, "manualHandlingFlag");
            return (Criteria) this;
        }

        public Criteria andManualHandlingFlagNotIn(List<Integer> values) {
            addCriterion("manual_handling_flag not in", values, "manualHandlingFlag");
            return (Criteria) this;
        }

        public Criteria andManualHandlingFlagBetween(Integer value1, Integer value2) {
            addCriterion("manual_handling_flag between", value1, value2, "manualHandlingFlag");
            return (Criteria) this;
        }

        public Criteria andManualHandlingFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("manual_handling_flag not between", value1, value2, "manualHandlingFlag");
            return (Criteria) this;
        }

        public Criteria andManualHandlingOpIdIsNull() {
            addCriterion("manual_handling_op_id is null");
            return (Criteria) this;
        }

        public Criteria andManualHandlingOpIdIsNotNull() {
            addCriterion("manual_handling_op_id is not null");
            return (Criteria) this;
        }

        public Criteria andManualHandlingOpIdEqualTo(Integer value) {
            addCriterion("manual_handling_op_id =", value, "manualHandlingOpId");
            return (Criteria) this;
        }

        public Criteria andManualHandlingOpIdNotEqualTo(Integer value) {
            addCriterion("manual_handling_op_id <>", value, "manualHandlingOpId");
            return (Criteria) this;
        }

        public Criteria andManualHandlingOpIdGreaterThan(Integer value) {
            addCriterion("manual_handling_op_id >", value, "manualHandlingOpId");
            return (Criteria) this;
        }

        public Criteria andManualHandlingOpIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("manual_handling_op_id >=", value, "manualHandlingOpId");
            return (Criteria) this;
        }

        public Criteria andManualHandlingOpIdLessThan(Integer value) {
            addCriterion("manual_handling_op_id <", value, "manualHandlingOpId");
            return (Criteria) this;
        }

        public Criteria andManualHandlingOpIdLessThanOrEqualTo(Integer value) {
            addCriterion("manual_handling_op_id <=", value, "manualHandlingOpId");
            return (Criteria) this;
        }

        public Criteria andManualHandlingOpIdIn(List<Integer> values) {
            addCriterion("manual_handling_op_id in", values, "manualHandlingOpId");
            return (Criteria) this;
        }

        public Criteria andManualHandlingOpIdNotIn(List<Integer> values) {
            addCriterion("manual_handling_op_id not in", values, "manualHandlingOpId");
            return (Criteria) this;
        }

        public Criteria andManualHandlingOpIdBetween(Integer value1, Integer value2) {
            addCriterion("manual_handling_op_id between", value1, value2, "manualHandlingOpId");
            return (Criteria) this;
        }

        public Criteria andManualHandlingOpIdNotBetween(Integer value1, Integer value2) {
            addCriterion("manual_handling_op_id not between", value1, value2, "manualHandlingOpId");
            return (Criteria) this;
        }

        public Criteria andManualHandlingTimeIsNull() {
            addCriterion("manual_handling_time is null");
            return (Criteria) this;
        }

        public Criteria andManualHandlingTimeIsNotNull() {
            addCriterion("manual_handling_time is not null");
            return (Criteria) this;
        }

        public Criteria andManualHandlingTimeEqualTo(Date value) {
            addCriterion("manual_handling_time =", value, "manualHandlingTime");
            return (Criteria) this;
        }

        public Criteria andManualHandlingTimeNotEqualTo(Date value) {
            addCriterion("manual_handling_time <>", value, "manualHandlingTime");
            return (Criteria) this;
        }

        public Criteria andManualHandlingTimeGreaterThan(Date value) {
            addCriterion("manual_handling_time >", value, "manualHandlingTime");
            return (Criteria) this;
        }

        public Criteria andManualHandlingTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("manual_handling_time >=", value, "manualHandlingTime");
            return (Criteria) this;
        }

        public Criteria andManualHandlingTimeLessThan(Date value) {
            addCriterion("manual_handling_time <", value, "manualHandlingTime");
            return (Criteria) this;
        }

        public Criteria andManualHandlingTimeLessThanOrEqualTo(Date value) {
            addCriterion("manual_handling_time <=", value, "manualHandlingTime");
            return (Criteria) this;
        }

        public Criteria andManualHandlingTimeIn(List<Date> values) {
            addCriterion("manual_handling_time in", values, "manualHandlingTime");
            return (Criteria) this;
        }

        public Criteria andManualHandlingTimeNotIn(List<Date> values) {
            addCriterion("manual_handling_time not in", values, "manualHandlingTime");
            return (Criteria) this;
        }

        public Criteria andManualHandlingTimeBetween(Date value1, Date value2) {
            addCriterion("manual_handling_time between", value1, value2, "manualHandlingTime");
            return (Criteria) this;
        }

        public Criteria andManualHandlingTimeNotBetween(Date value1, Date value2) {
            addCriterion("manual_handling_time not between", value1, value2, "manualHandlingTime");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusIsNull() {
            addCriterion("callback_status is null");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusIsNotNull() {
            addCriterion("callback_status is not null");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusEqualTo(Integer value) {
            addCriterion("callback_status =", value, "callbackStatus");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusNotEqualTo(Integer value) {
            addCriterion("callback_status <>", value, "callbackStatus");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusGreaterThan(Integer value) {
            addCriterion("callback_status >", value, "callbackStatus");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("callback_status >=", value, "callbackStatus");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusLessThan(Integer value) {
            addCriterion("callback_status <", value, "callbackStatus");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusLessThanOrEqualTo(Integer value) {
            addCriterion("callback_status <=", value, "callbackStatus");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusIn(List<Integer> values) {
            addCriterion("callback_status in", values, "callbackStatus");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusNotIn(List<Integer> values) {
            addCriterion("callback_status not in", values, "callbackStatus");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusBetween(Integer value1, Integer value2) {
            addCriterion("callback_status between", value1, value2, "callbackStatus");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("callback_status not between", value1, value2, "callbackStatus");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNull() {
            addCriterion("updated_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNotNull() {
            addCriterion("updated_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeEqualTo(Date value) {
            addCriterion("updated_time =", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotEqualTo(Date value) {
            addCriterion("updated_time <>", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThan(Date value) {
            addCriterion("updated_time >", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_time >=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThan(Date value) {
            addCriterion("updated_time <", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThanOrEqualTo(Date value) {
            addCriterion("updated_time <=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIn(List<Date> values) {
            addCriterion("updated_time in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotIn(List<Date> values) {
            addCriterion("updated_time not in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeBetween(Date value1, Date value2) {
            addCriterion("updated_time between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotBetween(Date value1, Date value2) {
            addCriterion("updated_time not between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNull() {
            addCriterion("created_time is null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNotNull() {
            addCriterion("created_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeEqualTo(Date value) {
            addCriterion("created_time =", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotEqualTo(Date value) {
            addCriterion("created_time <>", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThan(Date value) {
            addCriterion("created_time >", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("created_time >=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThan(Date value) {
            addCriterion("created_time <", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThanOrEqualTo(Date value) {
            addCriterion("created_time <=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIn(List<Date> values) {
            addCriterion("created_time in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotIn(List<Date> values) {
            addCriterion("created_time not in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeBetween(Date value1, Date value2) {
            addCriterion("created_time between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotBetween(Date value1, Date value2) {
            addCriterion("created_time not between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andOrderNoLikeInsensitive(String value) {
            addCriterion("upper(order_no) like", value.toUpperCase(), "orderNo");
            return (Criteria) this;
        }

        public Criteria andEscrowOrderNoLikeInsensitive(String value) {
            addCriterion("upper(escrow_order_no) like", value.toUpperCase(), "escrowOrderNo");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeLikeInsensitive(String value) {
            addCriterion("upper(payment_code) like", value.toUpperCase(), "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPayerNameLikeInsensitive(String value) {
            addCriterion("upper(payer_name) like", value.toUpperCase(), "payerName");
            return (Criteria) this;
        }

        public Criteria andPayerBankNameLikeInsensitive(String value) {
            addCriterion("upper(payer_bank_name) like", value.toUpperCase(), "payerBankName");
            return (Criteria) this;
        }

        public Criteria andPayerBankCodeLikeInsensitive(String value) {
            addCriterion("upper(payer_bank_code) like", value.toUpperCase(), "payerBankCode");
            return (Criteria) this;
        }

        public Criteria andPayerBankNumberLikeInsensitive(String value) {
            addCriterion("upper(payer_bank_number) like", value.toUpperCase(), "payerBankNumber");
            return (Criteria) this;
        }

        public Criteria andClientIpLikeInsensitive(String value) {
            addCriterion("upper(client_ip) like", value.toUpperCase(), "clientIp");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table chip_order
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table chip_order
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}