package com.dzpk.crazypoker.wallet.service.bean;

import lombok.*;

import java.io.Serializable;
import java.util.Date;

@Setter
@Getter
public class FlowChipBo implements Serializable {

    private Integer id;


    private String flowChipNo;


    private Integer transferorUid;


    private String transferorRid;


    private String transferorName;


    private Integer transfereeUid;


    private String transfereeRid;


    private String transfereeName;


    private Integer chip;


    private Date createdTime;


    private static final long serialVersionUID = 1L;

}