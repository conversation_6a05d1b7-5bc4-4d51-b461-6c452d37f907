package com.dzpk.crazypoker.wallet.service.transaction;

import com.dzpk.crazypoker.appmessage.send.AppBusinessMessageSender;
import com.dzpk.crazypoker.appmessage.send.bean.ClubGoldRecharge;
import com.dzpk.crazypoker.common.constant.RespCode;
import com.dzpk.crazypoker.common.dozer.BeanUtil;
import com.dzpk.crazypoker.common.rabbitmq.client.MessageSender;
import com.dzpk.crazypoker.common.rabbitmq.client.bean.userbalance.UserBalanceSyncMessage;
import com.dzpk.crazypoker.common.service.InvokedResult;
import com.dzpk.crazypoker.common.service.exception.ServiceException;
import com.dzpk.crazypoker.user.repositories.model.UserBalanceAuditLog;
import com.dzpk.crazypoker.user.repositories.mysql.IUserBalanceAuditDao;
import com.dzpk.crazypoker.wallet.api.bean.RechargePaymentMethodVo;
import com.dzpk.crazypoker.wallet.api.bean.RechargeProductVo;
import com.dzpk.crazypoker.wallet.api.constant.EChipConsume;
import com.dzpk.crazypoker.wallet.api.constant.EChipSource;
import com.dzpk.crazypoker.wallet.api.constant.ETransType;
import com.dzpk.crazypoker.wallet.repositories.mysql.*;
import com.dzpk.crazypoker.wallet.repositories.mysql.autogen.mapper.UserAccountLogPoMapper;
import com.dzpk.crazypoker.wallet.repositories.mysql.autogen.mapper.UserAccountPoMapper;
import com.dzpk.crazypoker.wallet.repositories.mysql.autogen.model.UserAccountLogPo;
import com.dzpk.crazypoker.wallet.repositories.mysql.autogen.model.UserAccountPo;
import com.dzpk.crazypoker.wallet.repositories.mysql.model.PaymentMethodPo;
import com.dzpk.crazypoker.wallet.repositories.mysql.model.PaymentProductPo;
import com.dzpk.crazypoker.wallet.service.IWalletService;
import com.dzpk.crazypoker.wallet.service.bean.ChannelPayBo;
import com.dzpk.crazypoker.wallet.service.bean.ConsumeChipBo;
import com.dzpk.crazypoker.wallet.service.bean.UserAccountLogBo;
import com.dzpk.crazypoker.wallet.service.bean.WithdrawUserAccountLogBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;

@Service
@Slf4j
public class WalletServiceImpl implements IWalletService {

    @Autowired
    private IUserAccountDao userAccountDao;

    @Autowired
    private UserAccountPoMapper userAccountPoMapper;

    @Autowired
    private UserAccountLogPoMapper userAccountLogPoMapper;

    @Autowired
    private BeanUtil beanUtil;
    @Autowired
    private GasDao gasDao;
    @Autowired
    private IWithdrawChipDao withdrawChipDao;

    @Autowired
    private IUserGoldAccountDao userGoldAccountDao;

    @Autowired
    private IUserBalanceAuditDao userBalanceAuditDao;

    @Autowired
    private MessageSender messageSender;

    @Autowired
    private IPaymentDao paymentDao;
    @Autowired
    private AppBusinessMessageSender appBusinessMessageSender;


    /**
     * 消费金币
     * 根据消费类型来决定扣减还是增加,与调用传入正负无关
     * 如果传入ConsumeChipBo为空则返回true,默认不进行消费
     * 如果userId不能为空,如果userId为空会抛空指针异常,调用方请注意
     * 金豆不足,消费失败,则会抛出ServiceException 异常码为: RespCode.USER_CHIP_NOT_ENOUGH
     * 如果业务不需要回滚则在需要捕获异常
     *
     * @param userId 用户id
     * @param bo
     */
    @Override
    @Transactional
    public UserAccountLogBo consumeChip(Integer userId, ConsumeChipBo bo,Integer type) {
        //提豆处理
        UserAccountLogPo po = null;
        if (EChipConsume.WITHDRAW_CHIP.equals(bo.getConsume())) {
            return withdrawChip(userId, bo,type);
        }
        int changeType = bo.getConsume().getChangeType();

        if (changeType == 1) {
            po = onlyAddChip(userId, bo);
        } else if (changeType == 2) {
            po = onlyAddNotExtractChip(userId, bo);
        } else if (changeType == 3) {
            po = onlySubChip(userId, bo);
        } else if (changeType == 4) {
            po = onlySubNotExtractChip(userId, bo);
        } else if (changeType == 5) {
            po = firstSubNotExtractChip(userId, bo);
        }

        return beanUtil.map(po, UserAccountLogBo.class);
    }

    @Override
    @Transactional
    public UserAccountLogBo addChip(int userId, EChipConsume type, int chip, int notExtractChip) {
        UserAccountPo po = userAccountPoMapper.selectByPrimaryKey(userId);
        UserAccountLogPo log = new UserAccountLogPo();
        log.setUserId(userId);
        log.setExternalId(String.valueOf(userId));
        log.setOpId(userId);
        log.setType(type.getValue());
        log.setDesction(type.getDesc());
        log.setChangeSource(EChipSource.API.getValue());
        log.setChangeChip(chip);
        log.setChangeNotExtractChip(notExtractChip);
        if (po == null) {
            log.setCurrentChip(0);
            log.setCurrNotExtractChip(0);
            po = new UserAccountPo();
            po.setUserId(userId);
            po.setChip(chip);
            po.setNotExtractChip(notExtractChip);
            po.setStatus(1);
        } else {
            log.setCurrentChip(po.getChip());
            log.setCurrNotExtractChip(po.getNotExtractChip());
            po.setChip(po.getChip() + chip);
            po.setNotExtractChip(po.getNotExtractChip() + notExtractChip);
        }
        userAccountPoMapper.updateByPrimaryKey(po);
        userAccountLogPoMapper.insert(log);
        return beanUtil.map(log, UserAccountLogBo.class);
    }

    @Override
    public List<ChannelPayBo> getChannelPayBo(Integer languageType) {
        return  gasDao.getChannelPayBo(0, languageType);
    }


    //针对类型为提豆的特殊处理,只能扣可提金豆,需要根据金豆数扣相应的流水

    /**
     *
     * 1.通过更新用户时间戳锁定阻塞更新该用户行
     * 2.根据当前更新插入日志表
     * 3.根据战绩流水计算手续费
     * 4.数据库默认隔离级别应为可重复读,目前所有开发,测试,生产环境都是.不需要指定隔离级别
     * @param userId
     * @param chipBo
     * @return
     */
    @Transactional
    protected WithdrawUserAccountLogBo withdrawChip(Integer userId, ConsumeChipBo chipBo,Integer type) {

        int chip = Math.abs(chipBo.getChip());
        //XXX 暂时这样实现吧
        Date nowDate = new Date();
        UserAccountPo po = new UserAccountPo();
        po.setUserId(userId);
        po.setUpdatedTime(nowDate);
        //锁定阻塞更新用户行,阻塞后,其它事务修改此行会阻塞
        userAccountPoMapper.updateByPrimaryKeySelective(po);

        UserAccountPo selectPo = userAccountPoMapper.selectByPrimaryKey(userId);

        long subPlCount = Math.min((long) chip,selectPo.getPlCount());
        int fee=0;
        if (chip<=selectPo.getPlCount()){
            fee = Double.valueOf(Math.ceil(chip * chipBo.getFeeRate())
            ).intValue();
        }else {
            fee = Double.valueOf(Math.ceil(selectPo.getPlCount() * chipBo.getFeeRate())
            ).intValue();
            fee =fee+Double.valueOf(Math.ceil((chip-selectPo.getPlCount()) * chipBo.getMaxFeeRate())
            ).intValue();
        }
        //两位后自动砍掉
        BigDecimal eth = new BigDecimal(BigInteger.ZERO);
        if (type== ETransType.USDT.getCode()){
            eth = gasDao.getGas(userId,"Hpay").multiply(new BigDecimal(100));
        }
        if ((fee+eth.intValue()) > selectPo.getChip()) {
            //不够扣吖
            RespCode respCode = RespCode.USER_CHIP_NOT_ENOUGH;
            log.error("请求提现金豆数为:{}, 免收手续费金豆数为:{},手续费为:{},当前可提金豆数为:{},当前战绩流水总和为:{}",
                    chip, subPlCount, fee, selectPo.getChip(), selectPo.getPlCount());
            throw new ServiceException(respCode.getCode(), respCode.getDesc());
        }

        long plCount=selectPo.getPlCount()-chip>=0?selectPo.getPlCount()-chip:0;
        UserAccountPo updatePo = new UserAccountPo();
        updatePo.setUserId(userId);
//        updatePo.setChip(selectPo.getChip() - (chip + fee));
        updatePo.setChip(selectPo.getChip() - (chip));
        updatePo.setPlCount(plCount);
        updatePo.setLockChip(selectPo.getLockChip() + (chip));  //提豆锁定金豆
        updatePo.setUpdatedTime(nowDate);

        userAccountPoMapper.updateByPrimaryKeySelective(updatePo);

        UserAccountLogPo userAccountLogPo = new UserAccountLogPo();
        userAccountLogPo.setChangeChip(-(chip));
        userAccountLogPo.setCurrentChip(selectPo.getChip()-chip);
//        userAccountLogPo.setCurrPlCount(selectPo.getPlCount());
        userAccountLogPo.setCurrPlCount(plCount);
//        userAccountLogPo.setChangePlCount(-(int) chip);
        userAccountLogPo.setChangePlCount(-(int) (selectPo.getPlCount()-chip>=0?chip:selectPo.getPlCount()));
        userAccountLogPo.setDesction(chipBo.getDescription());
        userAccountLogPo.setCreatedTime(new Date());
        userAccountLogPo.setExternalId(chipBo.getExternalId());
        userAccountLogPo.setOpId(chipBo.getOpId());
        userAccountLogPo.setType(chipBo.getConsume().getValue());
        userAccountLogPo.setUserId(userId);
        userAccountLogPo.setChangeSource(chipBo.getSource().getValue());

        userAccountLogPoMapper.insertSelective(userAccountLogPo);
        WithdrawUserAccountLogBo bo = beanUtil.map(userAccountLogPo, WithdrawUserAccountLogBo.class);
        bo.setFee(fee);
        bo.setGas(eth.intValue());
        return bo;
    }

    //仅仅增加金币数
    @Transactional
    protected UserAccountLogPo onlyAddChip(Integer userId, ConsumeChipBo chipBo) {
        return onlyAddOrSubChip(userId, chipBo, true);
    }

    //仅仅增加不可用金币数
    @Transactional
    protected UserAccountLogPo onlyAddNotExtractChip(Integer userId, ConsumeChipBo chipBo) {
        return onlyAddOrSubNotExtractChip(userId, chipBo, true);
    }

    //仅仅扣减金币数
    @Transactional
    protected UserAccountLogPo onlySubChip(Integer userId, ConsumeChipBo chipBo) {
        return onlyAddOrSubChip(userId, chipBo, false);
    }


    //仅仅扣减不可用金币数
    @Transactional
    protected UserAccountLogPo onlySubNotExtractChip(Integer userId, ConsumeChipBo chipBo) {
        return onlyAddOrSubNotExtractChip(userId, chipBo, false);
    }

    /**
     *
     * 1.通过更新用户时间戳锁定阻塞更新该用户行
     * 2.计算消耗不可用余额数,可用余额消耗数
     * 3.根据当前更新插入日志表
     * 4.数据库默认隔离级别应为可重复读,目前所有开发,测试,生产环境都是.不需要指定隔离级别
     * @param userId
     * @param chipBo
     * @return
     */

    //优先减少不可用余额
    @Transactional
    protected UserAccountLogPo firstSubNotExtractChip(Integer userId, ConsumeChipBo chipBo) {
        int chip = Math.abs(chipBo.getChip());
        //XXX 暂时这样实现吧
        Date nowDate = new Date();
        UserAccountPo po = new UserAccountPo();
        po.setUserId(userId);
        po.setUpdatedTime(nowDate);
        //锁定阻塞更新用户行,阻塞后,其它事务修改此行会阻塞
        userAccountPoMapper.updateByPrimaryKeySelective(po);

        UserAccountPo selectPo = userAccountPoMapper.selectByPrimaryKey(userId);

        if (chip > (selectPo.getChip() + selectPo.getNotExtractChip())) {
            RespCode respCode = RespCode.USER_CHIP_NOT_ENOUGH;
            log.error("当前消耗可提金豆数为:{},当前拥有可提金豆数为:{},,当前拥有不可提金豆数为:{}",
                    chip, selectPo.getChip(), selectPo.getNotExtractChip());
            throw new ServiceException(respCode.getCode(), respCode.getDesc());
        }

        //扣减的不可提取金币数
        int subNoExtractChip = Math.min(selectPo.getNotExtractChip(), chip);
        //扣减的可提取的金币数
        int subChip = chip - subNoExtractChip;
        //计算使用的不可提取金豆

        UserAccountPo updatePo = new UserAccountPo();
        updatePo.setUserId(userId);
        updatePo.setChip(selectPo.getChip() - subChip);
        updatePo.setNotExtractChip(selectPo.getNotExtractChip() - subNoExtractChip);
        updatePo.setUpdatedTime(nowDate);

        userAccountPoMapper.updateByPrimaryKeySelective(updatePo);

        UserAccountLogPo userAccountLogPo = new UserAccountLogPo();
        userAccountLogPo.setChangeChip(-subChip);
        userAccountLogPo.setCurrentChip(selectPo.getChip());
        userAccountLogPo.setChangeNotExtractChip(-subNoExtractChip);
        userAccountLogPo.setCurrNotExtractChip(selectPo.getNotExtractChip());
        userAccountLogPo.setDesction(chipBo.getDescription());
        userAccountLogPo.setCreatedTime(new Date());
        userAccountLogPo.setExternalId(chipBo.getExternalId());
        userAccountLogPo.setOpId(chipBo.getOpId());
        userAccountLogPo.setType(chipBo.getConsume().getValue());
        userAccountLogPo.setUserId(userId);
        userAccountLogPo.setChangeSource(chipBo.getSource().getValue());

        userAccountLogPoMapper.insertSelective(userAccountLogPo);

        return userAccountLogPo;
    }


    @Transactional
    protected UserAccountLogPo onlyAddOrSubChip(Integer userId, ConsumeChipBo chipBo, boolean isAdd) {

        int chip = isAdd ? Math.abs(chipBo.getChip()) : -Math.abs(chipBo.getChip());

        int result = userAccountDao.incChipByUserIdAndChipGTE0(userId, chip);
        UserAccountPo accountPo = userAccountPoMapper.selectByPrimaryKey(userId);

        if (result == 0) {
            RespCode respCode = RespCode.USER_CHIP_NOT_ENOUGH;
            log.error("当前消耗可提金豆数为:{},当前拥有可提金豆数为:{}", chip, accountPo.getChip());
            throw new ServiceException(respCode.getCode(), respCode.getDesc());
        }

        UserAccountLogPo po = new UserAccountLogPo();
        po.setChangeChip(chip);
        po.setCurrentChip(accountPo.getChip() - chip);
        po.setDesction(chipBo.getDescription());
        po.setCreatedTime(new Date());
        po.setExternalId(chipBo.getExternalId());
        po.setOpId(chipBo.getOpId());
        po.setType(chipBo.getConsume().getValue());
        po.setUserId(userId);
        po.setChangeSource(chipBo.getSource().getValue());
        po.setCurrPlCount(chipBo.getCurrPlCount() == null ? 0L : chipBo.getCurrPlCount());
        po.setChangePlCount(chipBo.getChangePlCount() == null ? 0 : Integer.parseInt(chipBo.getChangePlCount() + ""));
        userAccountLogPoMapper.insertSelective(po);
        return po;
    }

    @Transactional
    protected UserAccountLogPo onlyAddOrSubNotExtractChip(Integer userId, ConsumeChipBo chipBo, boolean isAdd) {
        int chip = isAdd ? Math.abs(chipBo.getChip()) : -Math.abs(chipBo.getChip());
        int result = userAccountDao.incNotExtractChipByUserIdAndChipGTE0(userId, chip);
        UserAccountPo accountPo = userAccountPoMapper.selectByPrimaryKey(userId);

        if (result == 0) {
            RespCode respCode = RespCode.USER_CHIP_NOT_ENOUGH;
            log.error("当前消耗不可提金豆数为:{},当前拥有不可提金豆数为:{}", chip, accountPo.getNotExtractChip());
            throw new ServiceException(respCode.getCode(), respCode.getDesc());
        }

        UserAccountLogPo po = new UserAccountLogPo();
        po.setChangeNotExtractChip(chip);
        po.setCurrNotExtractChip(accountPo.getNotExtractChip() - chip);
        po.setDesction(chipBo.getDescription());
        po.setCreatedTime(new Date());
        po.setExternalId(chipBo.getExternalId());
        po.setOpId(chipBo.getOpId());
        po.setType(chipBo.getConsume().getValue());
        po.setUserId(userId);
        po.setChangeSource(chipBo.getSource().getValue());
        userAccountLogPoMapper.insertSelective(po);
        return po;
    }

    /**
     * 金币充值
     *
     * @param userId
     * @param productId
     * @return
     */
    @Override
    @Transactional
    public InvokedResult<Object> goldRecharge(Integer userId, Integer productId) {
        InvokedResult<Object> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());
        PaymentProductPo product = paymentDao.getPaymentProductById(productId);
        if (product == null) {
            return result;
        }
        int chipNeeded = product.getPrice();
        int goldReceived = product.getChips() + product.getDiscount();
        try {
            UserAccountLogBo chipLog = consumeChip(userId,
                    ConsumeChipBo.builder()
                            .chip(chipNeeded)
                            .consume(EChipConsume.RECHARGE_GOLD)
                            .description(EChipConsume.RECHARGE_GOLD.getDesc())
                            .opId(userId)
                            .source(EChipSource.API)
                            .build(), ETransType.NULL.getCode()
            );

            int balanceBefore = Optional.ofNullable(userGoldAccountDao.getUserGold(userId)).orElse(0);
            userGoldAccountDao.updateUserGold(userId, goldReceived);

            String txUuid = UUID.randomUUID().toString();
            UserBalanceAuditLog rechargeLog = UserBalanceAuditLog.builder()
                    .txUuid(txUuid)
                    .userId(userId)
                    .source(UserBalanceAuditLog.Source.API.getValue())
                    .operatorId(userId)
                    .type(UserBalanceAuditLog.Type.DIAMOND_BUY_GOLD.getValue())
                    .balanceType(UserBalanceAuditLog.BalanceType.GOLD.getValue())
                    .balanceBefore(balanceBefore)
                    .balanceChange(goldReceived)
                    .build();
            userBalanceAuditDao.addUserBalanceAuditLog(rechargeLog);

            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());

            // 通過 res 服務異步通知前端帳戶變化
            messageSender.sendUserBalanceSyncMessage(UserBalanceSyncMessage.builder()
                    .userId(userId)
                    .balance(chipLog.getCurrentChip() - chipNeeded)
                    .coinType(UserBalanceSyncMessage.CoinType.DIAMOND)
                    .timestamp(System.currentTimeMillis())
                    .build());
            messageSender.sendUserBalanceSyncMessage(UserBalanceSyncMessage.builder()
                    .userId(userId)
                    .balance(balanceBefore + goldReceived)
                    .coinType(UserBalanceSyncMessage.CoinType.GOLD)
                    .timestamp(System.currentTimeMillis())
                    .build());

            /**
             * 通知俱樂部金幣充值
             */
            appBusinessMessageSender.notifyClubGoldRecharge(ClubGoldRecharge.builder()
                    .clubOwnerId(userId.longValue())
                    .goldAmount((long) goldReceived)
                    .build());

        } catch (ServiceException e) {
            log.error("金币充值失败", e);
            result.setCode(e.getCode());
            result.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("金币充值异常", e);
        }
        return result;
    }

    @Override
    public List<RechargeProductVo> getRechargeProduct(int userId, Integer platformType) {
        List<PaymentProductPo> result = paymentDao.getPaymentProductByPlatform(platformType);
        return beanUtil.map(result, RechargeProductVo.class);
    }

    @Override
    public List<RechargePaymentMethodVo> getRechargePaymentMethod(int userId, Integer platformType) {
        List<PaymentMethodPo> result = paymentDao.getPaymentMethodByPlatform(platformType);
        return beanUtil.map(result, RechargePaymentMethodVo.class);
    }
}
