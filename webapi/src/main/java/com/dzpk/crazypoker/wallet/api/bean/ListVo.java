package com.dzpk.crazypoker.wallet.api.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@ApiModel(description = "列表查询返回视图")
@Setter
@Getter
public class ListVo<T> {

    @ApiModelProperty(name = "下一个id",
            notes = "下一个id,如果返回为-1,则无更多数据",
            position = 1)
    private Integer nextId;

    @ApiModelProperty(name = "数据视图",
            notes = "数据视图",
            position = 2)
    private List<T> list;
}
