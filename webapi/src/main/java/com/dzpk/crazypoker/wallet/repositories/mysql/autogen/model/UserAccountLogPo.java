package com.dzpk.crazypoker.wallet.repositories.mysql.autogen.model;

import java.io.Serializable;
import java.util.Date;

public class UserAccountLogPo implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_account_log.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_account_log.user_id
     *
     * @mbg.generated
     */
    private Integer userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_account_log.type
     *
     * @mbg.generated
     */
    private Integer type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_account_log.change_source
     *
     * @mbg.generated
     */
    private Integer changeSource;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_account_log.current_chip
     *
     * @mbg.generated
     */
    private Integer currentChip;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_account_log.change_chip
     *
     * @mbg.generated
     */
    private Integer changeChip;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_account_log.curr_not_extract_chip
     *
     * @mbg.generated
     */
    private Integer currNotExtractChip;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_account_log.change_not_extract_chip
     *
     * @mbg.generated
     */
    private Integer changeNotExtractChip;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_account_log.curr_pl_count
     *
     * @mbg.generated
     */
    private Long currPlCount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_account_log.change_pl_count
     *
     * @mbg.generated
     */
    private Integer changePlCount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_account_log.desction
     *
     * @mbg.generated
     */
    private String desction;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_account_log.external_id
     *
     * @mbg.generated
     */
    private String externalId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_account_log.op_id
     *
     * @mbg.generated
     */
    private Integer opId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_account_log.created_time
     *
     * @mbg.generated
     */
    private Date createdTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table user_account_log
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_account_log.id
     *
     * @return the value of user_account_log.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_account_log.id
     *
     * @param id the value for user_account_log.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_account_log.user_id
     *
     * @return the value of user_account_log.user_id
     *
     * @mbg.generated
     */
    public Integer getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_account_log.user_id
     *
     * @param userId the value for user_account_log.user_id
     *
     * @mbg.generated
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_account_log.type
     *
     * @return the value of user_account_log.type
     *
     * @mbg.generated
     */
    public Integer getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_account_log.type
     *
     * @param type the value for user_account_log.type
     *
     * @mbg.generated
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_account_log.change_source
     *
     * @return the value of user_account_log.change_source
     *
     * @mbg.generated
     */
    public Integer getChangeSource() {
        return changeSource;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_account_log.change_source
     *
     * @param changeSource the value for user_account_log.change_source
     *
     * @mbg.generated
     */
    public void setChangeSource(Integer changeSource) {
        this.changeSource = changeSource;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_account_log.current_chip
     *
     * @return the value of user_account_log.current_chip
     *
     * @mbg.generated
     */
    public Integer getCurrentChip() {
        return currentChip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_account_log.current_chip
     *
     * @param currentChip the value for user_account_log.current_chip
     *
     * @mbg.generated
     */
    public void setCurrentChip(Integer currentChip) {
        this.currentChip = currentChip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_account_log.change_chip
     *
     * @return the value of user_account_log.change_chip
     *
     * @mbg.generated
     */
    public Integer getChangeChip() {
        return changeChip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_account_log.change_chip
     *
     * @param changeChip the value for user_account_log.change_chip
     *
     * @mbg.generated
     */
    public void setChangeChip(Integer changeChip) {
        this.changeChip = changeChip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_account_log.curr_not_extract_chip
     *
     * @return the value of user_account_log.curr_not_extract_chip
     *
     * @mbg.generated
     */
    public Integer getCurrNotExtractChip() {
        return currNotExtractChip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_account_log.curr_not_extract_chip
     *
     * @param currNotExtractChip the value for user_account_log.curr_not_extract_chip
     *
     * @mbg.generated
     */
    public void setCurrNotExtractChip(Integer currNotExtractChip) {
        this.currNotExtractChip = currNotExtractChip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_account_log.change_not_extract_chip
     *
     * @return the value of user_account_log.change_not_extract_chip
     *
     * @mbg.generated
     */
    public Integer getChangeNotExtractChip() {
        return changeNotExtractChip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_account_log.change_not_extract_chip
     *
     * @param changeNotExtractChip the value for user_account_log.change_not_extract_chip
     *
     * @mbg.generated
     */
    public void setChangeNotExtractChip(Integer changeNotExtractChip) {
        this.changeNotExtractChip = changeNotExtractChip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_account_log.curr_pl_count
     *
     * @return the value of user_account_log.curr_pl_count
     *
     * @mbg.generated
     */
    public Long getCurrPlCount() {
        return currPlCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_account_log.curr_pl_count
     *
     * @param currPlCount the value for user_account_log.curr_pl_count
     *
     * @mbg.generated
     */
    public void setCurrPlCount(Long currPlCount) {
        this.currPlCount = currPlCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_account_log.change_pl_count
     *
     * @return the value of user_account_log.change_pl_count
     *
     * @mbg.generated
     */
    public Integer getChangePlCount() {
        return changePlCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_account_log.change_pl_count
     *
     * @param changePlCount the value for user_account_log.change_pl_count
     *
     * @mbg.generated
     */
    public void setChangePlCount(Integer changePlCount) {
        this.changePlCount = changePlCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_account_log.desction
     *
     * @return the value of user_account_log.desction
     *
     * @mbg.generated
     */
    public String getDesction() {
        return desction;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_account_log.desction
     *
     * @param desction the value for user_account_log.desction
     *
     * @mbg.generated
     */
    public void setDesction(String desction) {
        this.desction = desction == null ? null : desction.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_account_log.external_id
     *
     * @return the value of user_account_log.external_id
     *
     * @mbg.generated
     */
    public String getExternalId() {
        return externalId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_account_log.external_id
     *
     * @param externalId the value for user_account_log.external_id
     *
     * @mbg.generated
     */
    public void setExternalId(String externalId) {
        this.externalId = externalId == null ? null : externalId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_account_log.op_id
     *
     * @return the value of user_account_log.op_id
     *
     * @mbg.generated
     */
    public Integer getOpId() {
        return opId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_account_log.op_id
     *
     * @param opId the value for user_account_log.op_id
     *
     * @mbg.generated
     */
    public void setOpId(Integer opId) {
        this.opId = opId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_account_log.created_time
     *
     * @return the value of user_account_log.created_time
     *
     * @mbg.generated
     */
    public Date getCreatedTime() {
        return createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_account_log.created_time
     *
     * @param createdTime the value for user_account_log.created_time
     *
     * @mbg.generated
     */
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }
}