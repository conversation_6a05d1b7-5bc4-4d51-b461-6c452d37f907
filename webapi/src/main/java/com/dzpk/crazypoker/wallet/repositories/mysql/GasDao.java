package com.dzpk.crazypoker.wallet.repositories.mysql;

import com.dzpk.crazypoker.wallet.service.bean.ChannelPayBo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface GasDao {
    @Select("select gas from gas_config where coin=" +
            "(select bank_code from account_payee where uid=#{userId} and payment_code=#{type})")
    BigDecimal getGas(@Param("userId")Integer userId,@Param("type")String type);
    @Select("select gas from gas_config where token='usdt'")
    BigDecimal getWithdrawalRate();

    @Select("select rate from rate_config where rate_code=#{code}")
    BigDecimal getRate(@Param("code")String code);

    //是否为俱乐部主
    @Select("select creator from club_record where creator=#{userId}")
    Integer getClubOwner(@Param("userId")Integer userId);

    //充值对应的渠道信息可配置后期后台加入
    @Select("select * from pay_channel_name payChannel inner join pay_trans_type payTrans " +
            "on payChannel.channel_id=payTrans.id where payChannel.state=0 and " +
            "payChannel.language_type=#{languageType}")
    List<ChannelPayBo> getChannelPayBo(@Param("type") Integer type,
                                       @Param("languageType")Integer languageType);
}
