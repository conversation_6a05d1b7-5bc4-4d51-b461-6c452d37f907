package com.dzpk.crazypoker.wallet.api.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@ApiModel(description = "根据下一个时间戳作为参数列表请求查询")
@Setter
@Getter
public class ListByCreateTimeReq {
    @ApiModelProperty(name = "下一个时间戳",
            required = true,
            notes = "下一个时间戳",
            position = 1)
    @NotNull(message = "下一个时间戳")
    private Long nextTime;
}
