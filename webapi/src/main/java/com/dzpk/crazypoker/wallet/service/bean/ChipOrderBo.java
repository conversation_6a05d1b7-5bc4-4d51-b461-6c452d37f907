package com.dzpk.crazypoker.wallet.service.bean;

import lombok.Getter;
import lombok.Setter;
import org.springframework.messaging.handler.annotation.SendTo;

import java.io.Serializable;
import java.util.Date;

@Setter
@Getter
public class ChipOrderBo implements Serializable {

    private Integer id;

    private Integer uid;

    private String orderNo;

    private Integer chipNum;

    private Integer status;

    private Integer payChannelId;

    private Integer payChannelClubId;

    private Date updatedTime;

    private Date createdTime;

    private String payNo;

    private static final long serialVersionUID = 1L;


}