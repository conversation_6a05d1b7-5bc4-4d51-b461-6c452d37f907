package com.dzpk.crazypoker.wallet.repositories.mysql.autogen.model;

import java.io.Serializable;
import java.util.Date;

public class AccountPayeeCheckPo implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column account_payee_check.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column account_payee_check.uid
     *
     * @mbg.generated
     */
    private Integer uid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column account_payee_check.type
     *
     * @mbg.generated
     */
    private Integer type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column account_payee_check.desction
     *
     * @mbg.generated
     */
    private String desction;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column account_payee_check.status
     *
     * @mbg.generated
     */
    private Integer status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column account_payee_check.check_id
     *
     * @mbg.generated
     */
    private Integer checkId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column account_payee_check.check_time
     *
     * @mbg.generated
     */
    private Date checkTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column account_payee_check.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column account_payee_check.created_time
     *
     * @mbg.generated
     */
    private Date createdTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table account_payee_check
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column account_payee_check.id
     *
     * @return the value of account_payee_check.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column account_payee_check.id
     *
     * @param id the value for account_payee_check.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column account_payee_check.uid
     *
     * @return the value of account_payee_check.uid
     *
     * @mbg.generated
     */
    public Integer getUid() {
        return uid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column account_payee_check.uid
     *
     * @param uid the value for account_payee_check.uid
     *
     * @mbg.generated
     */
    public void setUid(Integer uid) {
        this.uid = uid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column account_payee_check.type
     *
     * @return the value of account_payee_check.type
     *
     * @mbg.generated
     */
    public Integer getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column account_payee_check.type
     *
     * @param type the value for account_payee_check.type
     *
     * @mbg.generated
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column account_payee_check.desction
     *
     * @return the value of account_payee_check.desction
     *
     * @mbg.generated
     */
    public String getDesction() {
        return desction;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column account_payee_check.desction
     *
     * @param desction the value for account_payee_check.desction
     *
     * @mbg.generated
     */
    public void setDesction(String desction) {
        this.desction = desction == null ? null : desction.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column account_payee_check.status
     *
     * @return the value of account_payee_check.status
     *
     * @mbg.generated
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column account_payee_check.status
     *
     * @param status the value for account_payee_check.status
     *
     * @mbg.generated
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column account_payee_check.check_id
     *
     * @return the value of account_payee_check.check_id
     *
     * @mbg.generated
     */
    public Integer getCheckId() {
        return checkId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column account_payee_check.check_id
     *
     * @param checkId the value for account_payee_check.check_id
     *
     * @mbg.generated
     */
    public void setCheckId(Integer checkId) {
        this.checkId = checkId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column account_payee_check.check_time
     *
     * @return the value of account_payee_check.check_time
     *
     * @mbg.generated
     */
    public Date getCheckTime() {
        return checkTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column account_payee_check.check_time
     *
     * @param checkTime the value for account_payee_check.check_time
     *
     * @mbg.generated
     */
    public void setCheckTime(Date checkTime) {
        this.checkTime = checkTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column account_payee_check.update_time
     *
     * @return the value of account_payee_check.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column account_payee_check.update_time
     *
     * @param updateTime the value for account_payee_check.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column account_payee_check.created_time
     *
     * @return the value of account_payee_check.created_time
     *
     * @mbg.generated
     */
    public Date getCreatedTime() {
        return createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column account_payee_check.created_time
     *
     * @param createdTime the value for account_payee_check.created_time
     *
     * @mbg.generated
     */
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }
}