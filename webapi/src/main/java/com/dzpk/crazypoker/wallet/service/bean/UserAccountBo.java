package com.dzpk.crazypoker.wallet.service.bean;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Setter
@Getter
public class UserAccountBo implements Serializable {

    private Integer userId;

    private Integer chip;

    private Integer notExtractChip;

    private Long plCount;

    private Integer status;

    private Date updatedTime;

    private Date auditTime;

    private Date createdTime;

    private static final long serialVersionUID = 1L;

}