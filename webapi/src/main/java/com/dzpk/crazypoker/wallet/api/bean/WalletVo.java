package com.dzpk.crazypoker.wallet.api.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@ApiModel(description = "列表查询返回视图")
public class WalletVo {

    @ApiModelProperty(
            name = "可提金豆",
            required = true,
            notes = "可提金豆",
            position = 1
    )
    private Integer chip;

    @ApiModelProperty(
            name = "不可提金豆",
            required = true,
            notes = "不可提金豆",
            position = 2
    )
    private Integer notExtractChip;

    @ApiModelProperty(
            name = "战绩流水总和",
            required = true,
            notes = "战绩流水总和",
            position = 3
    )
    private Long plCount;

    @ApiModelProperty(
            name = "钱包状态",
            required = true,
            notes = "钱包状态:(1-正常,2-停用)",
            position = 2
    )
    private Integer status;

}
