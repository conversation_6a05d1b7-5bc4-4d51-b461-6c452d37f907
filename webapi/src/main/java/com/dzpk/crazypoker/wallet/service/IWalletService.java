package com.dzpk.crazypoker.wallet.service;

import com.dzpk.crazypoker.common.service.InvokedResult;
import com.dzpk.crazypoker.wallet.api.bean.RechargePaymentMethodVo;
import com.dzpk.crazypoker.wallet.api.bean.RechargeProductVo;
import com.dzpk.crazypoker.wallet.api.constant.EChipConsume;
import com.dzpk.crazypoker.wallet.service.bean.ChannelPayBo;
import com.dzpk.crazypoker.wallet.service.bean.ConsumeChipBo;
import com.dzpk.crazypoker.wallet.service.bean.UserAccountLogBo;

import javax.validation.constraints.NotNull;
import java.util.List;

public interface IWalletService {

    /**
     * 消费金币
     * 扣减传入负数,增加金币传入正数
     * 如果传入ConsumeChipBo为空则返回true,默认不进行消费
     * 如果userId不能为空,如果userId为空会抛空指针异常,调用方请注意
     * 金豆不足,消费失败,则会抛出ServiceException 异常码为: RespCode.USER_CHIP_NOT_ENOUGH
     * 如果业务不需要回滚则在需要捕获异常
     *
     * @param userId 用户id
     * @param bo
     */
    UserAccountLogBo consumeChip(Integer userId, ConsumeChipBo bo,Integer type);

    UserAccountLogBo addChip(int userId, EChipConsume type, int chip, int notExtractChip);

    /**
     * 获取对应的支付通道列表
     */
    List<ChannelPayBo> getChannelPayBo(Integer type);

    InvokedResult<Object> goldRecharge(Integer userId, Integer productId);

    List<RechargeProductVo> getRechargeProduct(int userId, @NotNull Integer platformType);

    List<RechargePaymentMethodVo> getRechargePaymentMethod(int userId, @NotNull Integer platformType);
}
