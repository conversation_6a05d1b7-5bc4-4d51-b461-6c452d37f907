package com.dzpk.crazypoker.datastat.api.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@ApiModel(value = "数据统计接口返回基本视图")
public class DataStatBaseVo {
    @ApiModelProperty(name = "牌局",
            notes = "牌局:61-普通局, 71-MTT, 81-SNG 91-奥马哈 51-大菠萝",
            position = 1)
    private Integer roomPath;


    @ApiModelProperty(name = "总战绩",
            notes = "总战绩",
            position = 2)
    private Integer totalEarn;

    //omaha 普通局 AOF特有参数

    /**
     * 入池率 如20%返回20即可，下同
     */
    @ApiModelProperty(name = "入池率", required = true,
            notes = "omaha,普通局,AOF特有参数.入池率 如20%返回20即可",
            position = 3)
    @JsonProperty("VPIP")
    private Integer vpip;


    /**
     * 入池胜率
     */
    @ApiModelProperty(name = "入池胜率", required = true,
            notes = "omaha,普通局,AOF特有参数.入池胜率 如20%返回20即可",
            position = 4)
    @JsonProperty("Wins")
    private Integer wins;
    /**
     * 翻牌前加注率
     */
    @ApiModelProperty(name = "翻牌前加注率", required = true,
            notes = "omaha,普通局,AOF特有参数.翻牌前加注率 如20%返回20即可",
            position = 5)
    @JsonProperty("PRF")
    private Integer prf;


    /**
     * 翻牌前再加注率
     */
    @ApiModelProperty(name = "翻牌前再加注率", required = true,
            notes = "omaha,普通局,AOF特有参数.翻牌前再加注率 如20%返回20即可",
            position = 6)
    @JsonProperty("threeBet")
    private Integer bet3;

    /**
     * 4Flop持续下注率
     */
    @ApiModelProperty(name = "4Flop持续下注率", required = true,
            notes = "omaha,普通局,AOF特有参数.4Flop持续下注率 如20%返回20即可",
            position = 7)
    @JsonProperty("CBet")
    private Integer cbet;

    /**
     * 激进程度
     */
    @ApiModelProperty(name = "激进程度", required = true,
            notes = "omaha,普通局,AOF特有参数.激进程度",
            position = 8)
    @JsonProperty("AF")
    private Integer af;

    /**
     * 摊牌胜率
     */
    @ApiModelProperty(name = "摊牌胜率", required = true,
            notes = "omaha,普通局,AOF特有参数.摊牌胜率.如20%返回20即可",
            position = 9)
    @JsonProperty("WTSD")
    private Integer wtsd;

    /**
     * 全下胜率
     */
    @ApiModelProperty(name = "全下胜率", required = true,
            notes = "omaha,普通局,AOF特有参数.全下胜率.如20%返回20即可",
            position = 10)
    @JsonProperty("Allin_Wins")
    private Integer allinWins;

    /**
     * 总手数
     **/
    @ApiModelProperty(name = "总手数", required = true,
            notes = "omaha,普通局,AOF特有参数.总手数",
            position = 11)
    @JsonProperty("totalHand")
    private Integer totalHand;

    /**
     * 总局数
     **/
    @ApiModelProperty(name = "总局数", required = true,
            notes = "omaha,普通局,AOF特有参数.总局数",
            position = 12)
    @JsonProperty("totalGameCnt")
    private Integer totalGameCnt;

    //omaha 普通局 AOF特有参数结束


    //大菠萝特有参数

    /**
     * 胜率 如20%返回20即可
     */
    @ApiModelProperty(name = "胜率", required = true,
            notes = "大菠萝特有参数.胜率.如20%返回20即可",
            position = 13)
    @JsonProperty("papWins")
    private Integer papWins;

    /**
     * 进范率 如20%返回20即可
     */
    @ApiModelProperty(name = "进范率", required = true,
            notes = "大菠萝特有参数.进范率.如20%返回20即可",
            position = 14)
    @JsonProperty("fantasy")
    private Integer fantasy;

    /**
     * 手牌平均分
     */
    @ApiModelProperty(name = "手牌平均分", required = true,
            notes = "大菠萝特有参数.手牌平均分",
            position = 15)
    @JsonProperty("handAverage")
    private Integer handAverage;

    /**
     * 进范平均分
     */
    @ApiModelProperty(name = "进范平均分", required = true,
            notes = "大菠萝特有参数.进范平均分",
            position = 16)
    @JsonProperty("fantasyAverage")
    private Integer fantasyAverage;


    //大菠萝特有参数结束


    //sng mtt 特有参数

    /**
     * 参赛次数
     */
    @ApiModelProperty(name = "参赛次数", required = true,
            notes = "sng mtt特有参数.参赛次数",
            position = 17)
    @JsonProperty("playTimes")
    private Integer playTimes;

    /**
     * 获奖次数
     */
    @ApiModelProperty(name = "获奖次数", required = true,
            notes = "sng mtt特有参数.获奖次数",
            position = 18)
    @JsonProperty("winTimes")
    private Integer winTimes;

    /**
     * 第一名次数
     */
    @ApiModelProperty(name = "第一名次数", required = true,
            notes = "sng mtt特有参数.第一名次数",
            position = 19)
    @JsonProperty("firstTimes")
    private Integer fristTimes;

    /**
     * 第二名次数
     */
    @ApiModelProperty(name = "第二名次数", required = true,
            notes = "sng mtt特有参数.第二名次数",
            position = 20)
    @JsonProperty("secondTimes")
    private Integer secondTimes;

    /**
     * 第三名次数
     */
    @ApiModelProperty(name = "特有参数", required = true,
            notes = "sng mtt特有参数.第三名次数",
            position = 21)
    @JsonProperty("thirdTimes")
    private Integer thirdTimes;

    @ApiModelProperty(name = "特有参数", required = true,
            notes = "俱乐部牌局服务费",
            position = 22)
    @JsonProperty("clubRoomChargeTotal")
    private Integer clubRoomChargeTotal = 0;

    @ApiModelProperty(name = "特有参数", required = true,
            notes = "俱乐部牌局总盈利值",
            position = 23)
    @JsonProperty("clubRoomPlTotal")
    private Integer clubRoomPlTotal = 0;
}
