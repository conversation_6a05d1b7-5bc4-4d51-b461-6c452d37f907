package com.dzpk.crazypoker.datastat.service.impl;

import com.dzpk.crazypoker.common.utils.NullUtils;
import com.dzpk.crazypoker.datastat.repositories.IOmahaDataProfitDao;
import com.dzpk.crazypoker.datastat.repositories.model.OmahaDataProfitPo;
import com.dzpk.crazypoker.datastat.service.IStatService;
import com.dzpk.crazypoker.datastat.service.bean.DataStatCondition;
import com.dzpk.crazypoker.datastat.service.bean.OmahaDataBo;
import com.dzpk.crazypoker.datastat.service.bean.RoomDataBaseBo;
import com.dzpk.crazypoker.vip.constant.EVipLevel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * omaha数据统计
 */
@Service("IStatService_91")
public class OmahaDataStatImpl implements IStatService {

    @Autowired
    private IOmahaDataProfitDao dataDao;

    @Override
    public RoomDataBaseBo findByCondition(DataStatCondition condition) {
        return loadByCondition(new OmahaDataBo(), condition);
    }

    /**
     * 根据条件加载统计数据
     *
     * @param roomDataBaseBo
     * @param condition
     * @return
     */
    @Override
    public RoomDataBaseBo loadByCondition(RoomDataBaseBo roomDataBaseBo, DataStatCondition condition) {
        OmahaDataBo bo;

        if (roomDataBaseBo == null) {
            bo = new OmahaDataBo();
        } else {
            bo = (OmahaDataBo) roomDataBaseBo;
        }

        OmahaDataProfitPo po = dataDao.selectByUserId(condition.getUserId());
        int timeType = condition.getTimeType();

        bo.setShowAdvance(1);
        bo.setTimeType(timeType);
        if (po == null) {
            loadNull(bo, null);
            return bo;
        }
        if (timeType == 1) {
            loadProfit2BoByAll(bo, po);
        } else if (timeType == 2) {
            loadProfit2BoByMonth(bo, po);
        } else if (timeType == 3) {
            loadProfit2BoByWeek(bo, po);
        }

        return bo;
    }

    /**
     * 破隐成功加载数据
     *
     * @param baseBo
     * @param condition
     */
    @Override
    public void loadByBreakHide(RoomDataBaseBo baseBo, DataStatCondition condition) {
        if (baseBo == null) {
            return;
        }

        OmahaDataBo bo = (OmahaDataBo) baseBo;

        OmahaDataProfitPo po = dataDao.selectByUserId(condition.getUserId());
        loadByEVipLevelPo2Bo(bo, po, condition.getVipLevel());
        loadByImageTypePo2Bo(bo, po, condition.getImageType());
    }

    /**
     * 没有破隐成功加载数据
     *
     * @param baseBo
     * @param condition
     */
    @Override
    public void loadByNoBreakHide(RoomDataBaseBo baseBo, DataStatCondition condition) {
        if (baseBo == null) {
            return;
        }

        OmahaDataBo bo = (OmahaDataBo) baseBo;

        OmahaDataProfitPo po = dataDao.selectByUserId(condition.getUserId());
        loadByEVipLevelPo2Bo(bo, po, condition.getVipLevel());

    }

    /**
     * 加载所有数据统计结果到Bo
     *
     * @param bo
     * @param po
     */
    private void loadProfit2BoByAll(OmahaDataBo bo, OmahaDataProfitPo po) {
        if (po == null) {
            //防止空指针
            po = new OmahaDataProfitPo();
        }

        Integer totalEarn = NullUtils.intNull2Zero(po.getTotalEarn());
        Integer gameCnt = NullUtils.intNull2Zero(po.getGameCnt());

        bo.setVpip(NullUtils.intNull2Zero(po.getPoolRate()));
        bo.setWins(NullUtils.intNull2Zero(po.getPoolWinRate()));
        bo.setTotalHand(NullUtils.intNull2Zero(po.getTotalHand()));
        bo.setTotalGameCnt(gameCnt);
        bo.setAllinWins(NullUtils.intNull2Zero(po.getBringIn()));
        bo.setAveageBringIn(NullUtils.intNull2Zero(po.getBringIn()));
        bo.setAveageEarn(gameCnt > 0 ? totalEarn / gameCnt : 0);
        bo.setTotalEarn(totalEarn-po.getClubRoomPlTotal());
        bo.setClubRoomChargeTotal(po.getClubRoomChargeTotal());
        bo.setClubRoomPlTotal(po.getClubRoomPlTotal());

        bo.setBet3(NullUtils.intNull2Zero(po.getMonthBet3Rate()));
        bo.setPrf(NullUtils.intNull2Zero(po.getPer()));
        bo.setCbet(NullUtils.intNull2Zero(po.getCbetRate()));
        bo.setAf(NullUtils.intNull2Zero(po.getAfRate()));
        bo.setWtsd(NullUtils.intNull2Zero(po.getTanPaiWinRate()));
        bo.setAllinWins(NullUtils.intNull2Zero(po.getAllinWinRate()));
    }

    @Override
    public RoomDataBaseBo genBo() {
        return new OmahaDataBo();
    }

    /**
     * 加载月数据统计结果到Bo
     *
     * @param bo
     * @param po
     */
    private void loadProfit2BoByMonth(OmahaDataBo bo, OmahaDataProfitPo po) {
        if (po == null) {
            //防止空指针
            po = new OmahaDataProfitPo();
        }

        bo.setVpip(NullUtils.intNull2Zero(po.getMonthPoolRate()));
        bo.setWins(NullUtils.intNull2Zero(po.getMonthPoolWinRate()));
        bo.setTotalHand(NullUtils.intNull2Zero(po.getMonthTotalHand()));
        int gameCnt = NullUtils.intNull2Zero(po.getMonthGameCnt());
        int totalEarn = NullUtils.intNull2Zero(po.getMonthTotalEarn());
        bo.setTotalGameCnt(gameCnt);
        bo.setAveageBringIn(NullUtils.intNull2Zero(po.getMonthBringIn()));
        bo.setAveageEarn(gameCnt > 0 ? totalEarn / gameCnt : 0);
        bo.setTotalEarn(totalEarn-po.getClubRoomPlTotal());
        bo.setClubRoomChargeTotal(po.getClubRoomChargeTotal());
        bo.setClubRoomPlTotal(po.getClubRoomPlTotal());

        bo.setBet3(NullUtils.intNull2Zero(po.getMonthBet3Rate()));
        bo.setPrf(NullUtils.intNull2Zero(po.getMonthPer()));
        bo.setCbet(NullUtils.intNull2Zero(po.getMonthCbetRate()));
        bo.setAf(NullUtils.intNull2Zero(po.getMonthAfRate()));
        bo.setWtsd(NullUtils.intNull2Zero(po.getMonthTanPaiWinRate()));
        bo.setAllinWins(NullUtils.intNull2Zero(po.getMonthAllinWinRate()));
    }

    /**
     * 加载周数据统计结果到Bo
     *
     * @param bo
     * @param po
     */
    private void loadProfit2BoByWeek(OmahaDataBo bo, OmahaDataProfitPo po) {
        if (po == null) {
            //防止空指针
            po = new OmahaDataProfitPo();
        }

        int gameCnt = NullUtils.intNull2Zero(po.getWeekGameCnt());
        int totalEarn = NullUtils.intNull2Zero(po.getWeekTotalEarn());

        bo.setVpip(NullUtils.intNull2Zero(po.getWeekPoolRate()));
        bo.setWins(NullUtils.intNull2Zero(po.getWeekPoolWinRate()));
        bo.setTotalHand(NullUtils.intNull2Zero(po.getWeekTotalHand()));
        bo.setTotalGameCnt(gameCnt);
        bo.setAveageBringIn(NullUtils.intNull2Zero(po.getWeekBringIn()));
        bo.setAveageEarn(gameCnt > 0 ? totalEarn / gameCnt : 0);
        bo.setTotalEarn(totalEarn-po.getClubRoomPlTotal());
        bo.setClubRoomChargeTotal(po.getClubRoomChargeTotal());
        bo.setClubRoomPlTotal(po.getClubRoomPlTotal());

        bo.setBet3(NullUtils.intNull2Zero(po.getWeekBet3Rate()));
        bo.setPrf(NullUtils.intNull2Zero(po.getWeekPer()));
        bo.setCbet(NullUtils.intNull2Zero(po.getWeekCbetRate()));
        bo.setAf(NullUtils.intNull2Zero(po.getWeekAfRate()));
        bo.setWtsd(NullUtils.intNull2Zero(po.getWeekTanPaiWinRate()));
        bo.setAllinWins(NullUtils.intNull2Zero(po.getWeekAllinWinRate()));
    }

    private void loadByImageTypePo2Bo(OmahaDataBo bo, OmahaDataProfitPo po, Integer imageType) {
        if (po == null) {
            //防止空指针
            po = new OmahaDataProfitPo();
        }

        if (imageType == 1) {
            bo.setVpip(NullUtils.intNull2Zero(po.getPoolRate()));
            bo.setWins(NullUtils.intNull2Zero(po.getPoolWinRate()));
            bo.setPrf(NullUtils.intNull2Zero(po.getPer()));
            bo.setBet3(NullUtils.intNull2Zero(po.getBet3Rate()));
            bo.setCbet(NullUtils.intNull2Zero(po.getCbetRate()));
            bo.setAf(NullUtils.intNull2Zero(po.getAfRate()));
            bo.setWtsd(NullUtils.intNull2Zero(po.getTanPaiWinRate()));
            bo.setAllinWins(NullUtils.intNull2Zero(po.getAllinWinRate()));

        } else if (imageType == 2) {
            bo.setVpip(NullUtils.intNull2Zero(po.getMonthPoolRate()));
            bo.setWins(NullUtils.intNull2Zero(po.getMonthPoolWinRate()));
            bo.setPrf(NullUtils.intNull2Zero(po.getMonthPer()));
            bo.setCbet(NullUtils.intNull2Zero(po.getMonthCbetRate()));
            bo.setAf(NullUtils.intNull2Zero(po.getMonthAfRate()));
            bo.setWtsd(NullUtils.intNull2Zero(po.getMonthTanPaiWinRate()));
            bo.setAllinWins(NullUtils.intNull2Zero(po.getMonthAllinWinRate()));
        } else if (imageType == 3) {
            bo.setVpip(NullUtils.intNull2Zero(po.getWeekPoolRate()));
            bo.setWins(NullUtils.intNull2Zero(po.getWeekPoolWinRate()));
            bo.setPrf(NullUtils.intNull2Zero(po.getWeekPer()));
            bo.setBet3(NullUtils.intNull2Zero(po.getWeekBet3Rate()));
            bo.setCbet(NullUtils.intNull2Zero(po.getWeekCbetRate()));
            bo.setAf(NullUtils.intNull2Zero(po.getWeekAfRate()));
            bo.setWtsd(NullUtils.intNull2Zero(po.getWeekTanPaiWinRate()));
            bo.setAllinWins(NullUtils.intNull2Zero(po.getWeekAllinWinRate()));
        }
    }

    private void loadByEVipLevelPo2Bo(OmahaDataBo bo, OmahaDataProfitPo po, EVipLevel vipLevel) {
        if (po == null) {
            //防止空指针
            po = new OmahaDataProfitPo();
        }

        if (EVipLevel.LEVEL_1 == vipLevel || EVipLevel.LEVEL_0 == vipLevel) {
            bo.setVpip(NullUtils.intNull2Zero(po.getWeekPoolRate()));
            bo.setWins(NullUtils.intNull2Zero(po.getWeekPoolWinRate()));
            bo.setTotalHand(NullUtils.intNull2Zero(po.getWeekTotalHand()));
            bo.setTotalGameCnt(NullUtils.intNull2Zero(po.getWeekGameCnt()));
        } else if (EVipLevel.LEVEL_2 == vipLevel) {
            bo.setVpip(NullUtils.intNull2Zero(po.getMonthPoolRate()));
            bo.setWins(NullUtils.intNull2Zero(po.getMonthPoolWinRate()));
            bo.setTotalHand(NullUtils.intNull2Zero(po.getMonthTotalHand()));
            bo.setTotalGameCnt(NullUtils.intNull2Zero(po.getMonthGameCnt()));
        } else if (EVipLevel.LEVEL_3 == vipLevel) {
            bo.setWins(NullUtils.intNull2Zero(po.getPoolWinRate()));
            bo.setVpip(NullUtils.intNull2Zero(po.getWeekPoolRate()));
            bo.setTotalHand(NullUtils.intNull2Zero(po.getTotalHand()));
            bo.setTotalGameCnt(NullUtils.intNull2Zero(po.getGameCnt()));
        }
    }


    private void loadNull(OmahaDataBo bo, EVipLevel vipLevel) {

        Integer zero = Integer.valueOf(0);
        bo.setVpip(zero);
        bo.setWins(zero);
        bo.setTotalHand(zero);
        bo.setAllinWins(zero);
        bo.setTotalGameCnt(zero);
        bo.setAveageBringIn(zero);
        bo.setAveageEarn(zero);
        bo.setTotalEarn(zero);

        if (vipLevel == null || vipLevel == EVipLevel.LEVEL_3) {
            bo.setPrf(zero);
            //bo.setBet3(doc.getInteger("bet3_rate", 0));
            bo.setBet3(zero);
            bo.setCbet(zero);
            bo.setWtsd(zero);
            bo.setAf(zero);
            bo.setAllinWins(zero);
        }
    }
}
