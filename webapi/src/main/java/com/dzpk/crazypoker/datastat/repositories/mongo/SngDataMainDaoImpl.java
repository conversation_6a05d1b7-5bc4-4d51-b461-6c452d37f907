package com.dzpk.crazypoker.datastat.repositories.mongo;

import com.dzpk.crazypoker.common.mongo.factory.IMongoInstanceFactory;
import com.dzpk.crazypoker.datastat.repositories.ISngDataMainDao;
import com.dzpk.crazypoker.datastat.repositories.model.SngDataMainPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

@Repository
public class SngDataMainDaoImpl implements ISngDataMainDao {

    private final static String OMAHA_DATA_PROFIT = "sng_data_main";

    @Autowired
    private IMongoInstanceFactory iMongoInstanceFactory;

    /**
     * 根据userId获取数据统计数据
     *
     * @param userId
     * @returnT
     */
    @Override
    public SngDataMainPo selectByUserId(Integer userId) {
        ReactiveMongoTemplate rTemplate = getReactiveMongoTemplate();
        Query query = new Query();
        query.addCriteria(Criteria.where("userId").is(userId));
        Mono<SngDataMainPo> mono = rTemplate.findOne(query, SngDataMainPo.class, OMAHA_DATA_PROFIT);
        return mono.block();
    }

    private ReactiveMongoTemplate getReactiveMongoTemplate() {
        return iMongoInstanceFactory.defaultInstance().getTemplate();
    }
}
