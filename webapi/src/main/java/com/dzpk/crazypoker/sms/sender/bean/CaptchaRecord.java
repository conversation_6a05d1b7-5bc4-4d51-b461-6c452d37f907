package com.dzpk.crazypoker.sms.sender.bean;


import lombok.*;

/**
 * CaptchaRecord
 *
 * <AUTHOR>
 * @since 2025/4/7
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class CaptchaRecord {

    /**
     * 记录 ID
     */
    private Long id;

    /**
     * 电话或邮箱
     */
    private String account;

    /**
     * 类型：0-手机, 1-邮箱
     */
    private Integer accountType;

    /**
     * 验证码
     */
    private String code;

    /**
     * 类型：0-注册账号, 1-修改密码, 2-设置或更改支付密码
     */
    private Integer type;

    /**
     * 发送时间
     */
    private Long requestedAt;

    /**
     * 状态：0-成功, 1-失败
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Long createdAt;
}
