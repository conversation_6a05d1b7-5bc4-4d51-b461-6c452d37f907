package com.dzpk.crazypoker.sms.service.impl;

import com.alibaba.fastjson.JSON;
import com.dzpk.crazypoker.sms.cache.ISmsCache;
import com.dzpk.crazypoker.sms.config.SmsCnUtil;
import com.dzpk.crazypoker.sms.config.SmsCountryProperties;
import com.dzpk.crazypoker.sms.config.SmsInternatinolProperties;
import com.dzpk.crazypoker.sms.config.ValidaateCodeProperties;
import com.dzpk.crazypoker.sms.constant.SmsTemplate;
import com.dzpk.crazypoker.sms.sender.CaptchaRecordSender;
import com.dzpk.crazypoker.sms.sender.bean.CaptchaRecord;
import com.dzpk.crazypoker.sms.service.ISmsService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

@Service
@Slf4j
public class SmsServiceImpl implements ISmsService {

    @Autowired
    private ISmsCache smsCache;

    @Autowired
    private SmsCountryProperties smsCountryProperties;

    @Autowired
    private ValidaateCodeProperties validaateCodeProperties;

    @Autowired
    private SmsInternatinolProperties smsInternatinolProperties;

    @Autowired
    CaptchaRecordSender captchaRecordSender;

    private static final int SMS_CODE_LENGTH = 6;  //短信验证码位数


    @Override
    public boolean checkSmsCode(int sendCodeType, String mobileNo, String smsCode, String countryCode) {
        log.info("验证短信验证码, sendCodeType:{},mobileNo:{},smsCode:{},countryCode:{}", sendCodeType, mobileNo, smsCode, countryCode);
        if (countryCode.equals("EML")) {
            return checkSmsCodeOld(sendCodeType, mobileNo, smsCode, countryCode);
        }
        return checkSmsCodeNew(sendCodeType, mobileNo, smsCode, countryCode);
    }

    private boolean checkSmsCodeOld(int sendCodeType, String mobileNo, String smsCode, String countryCode) {
        try {
            if (StringUtil.isEmpty(mobileNo) || StringUtil.isEmpty(smsCode) || sendCodeType <= 0 || StringUtil.isEmpty(countryCode)) {
                if (log.isWarnEnabled()) {
                    log.error("验证短信验证码失败, 参数不合法");
                }
                return false;
            }

            //交易和国家语言标识无关
            SmsTemplate smsCodeTemplate = getSmsMessage(sendCodeType, 0);
            if (null == smsCodeTemplate) {
                smsCodeTemplate = SmsTemplate.REGISTERZH;
            }

            String existSmsCode = smsCache.getSmsCode(smsCodeTemplate, getFullMobileNo(countryCode, mobileNo));

            if (!StringUtil.isEmpty(smsCode) && !StringUtil.isEmpty(existSmsCode) && smsCode
                    .equals(existSmsCode)) {
                log.info("验证短信验证码成功, sendCodeType:{},mobileNo:{},smsCode:{},countryCode:{}"
                        , sendCodeType, mobileNo, smsCode, countryCode);

                return true;
            } else {
                log.error("验证短信验证码失败, sendCodeType:{},mobileNo:{},smsCode:{},countryCode:{}"
                        , sendCodeType, mobileNo, smsCode, countryCode);
            }
        } catch (Exception e) {
            log.error("验证短信验证码异常,手机号:{},{}", mobileNo, e);
            return false;
        }

        return false;
    }

    private boolean checkSmsCodeNew(int sendCodeType, String mobileNo, String smsCode, String countryCode) {
        String checkUrl = validaateCodeProperties.getSmsCheckUrl() + "mobile=" + countryCode + mobileNo + "&code=" + smsCode;

        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<?, ?> response = mapper.readValue(new URL(checkUrl), Map.class);

            if ("0".equals(response.get("status"))) {
                log.info("验证短信验证码成功, sendCodeType:{},mobileNo:{},smsCode:{},countryCode:{}"
                        , sendCodeType, mobileNo, smsCode, countryCode);
                return true;
            } else {
                log.error("验证短信验证码失败, sendCodeType:{},mobileNo:{},smsCode:{},countryCode:{},response:{}"
                        , sendCodeType, mobileNo, smsCode, countryCode, response);
                return false;
            }
        } catch (Exception e) {
            log.error("验证短信验证码异常,手机号:{},{}", mobileNo, e);
            return false;
        }
    }

    @Override
    public int sendSmsCode(String mobileNo, String countryCode, int sendCodeType, int lang, String ip) {
        log.debug("发送短信验证码, " +
                "mobileNo:{},countryCode:{},sendCodeType:{},reqIp:{}", mobileNo, countryCode, sendCodeType, ip);
        // 生成短信验证码
        String smsCode = genRandomNum(SMS_CODE_LENGTH);
        try {
            if (StringUtil.isEmpty(mobileNo) || sendCodeType <= 0 || StringUtil.isEmpty(countryCode)) {
                if (log.isWarnEnabled()) {
                    log.error("发送短信验证码失败, 参数不合法");
                }
                return -1;
            }
            SmsTemplate smsCodeTemplate = getSmsMessage(sendCodeType, lang);
            if (null == smsCodeTemplate) {
                smsCodeTemplate = SmsTemplate.REGISTERZH;
            }
            boolean isSendOK = doSendSmsCode(countryCode, mobileNo, smsCode);
            if (isSendOK) {
                smsCache.setSmsCode(smsCodeTemplate, getFullMobileNo(countryCode, mobileNo), smsCode);
                log.debug("发送短信验证码成功, " +
                        "mobileNo:{},countryCode:{},smsCode:{}", mobileNo, countryCode, smsCode);
                // 发送新增消息
                captchaRecordSender.sendAddCaptchaRecordMessage(CaptchaRecord.builder()
                        .account("EML".equals(countryCode)? mobileNo : getFullMobileNo(countryCode, mobileNo))
                        .accountType("EML".equals(countryCode) ? 1 : 0)
                        .code(smsCode)
                        .type(sendCodeType)
                        .requestedAt(System.currentTimeMillis())
                        .status(0)
                        .build());
                return 0;
            }
        } catch (Exception e) {
            log.error("发送短信验证码异常,手机号:{},{}", mobileNo, e);
        }
        // 发送新增消息
        captchaRecordSender.sendAddCaptchaRecordMessage(CaptchaRecord.builder()
                .account("EML".equals(countryCode)? mobileNo : getFullMobileNo(countryCode, mobileNo))
                .accountType("EML".equals(countryCode) ? 1 : 0)
                .code(smsCode)
                .type(sendCodeType)
                .requestedAt(System.currentTimeMillis())
                .status(1)
                .build());
        return -1;
    }

    /**
     * 调用253短信平台发送短信
     *
     * @param mobileNo 手机号,国际手机号要加区号
     * @param smsCode  验证码
     * @return
     */
    private boolean doSendSmsCode(String countryCode, String mobileNo, String smsCode) {
        Map<String, Object> parameterMap = new HashMap<>();
        parameterMap.put("code", smsCode);
        String s = JSON.toJSONString(parameterMap);

        log.info("调用253平台发送验证码, mobileNo:{},smsCode:{}", mobileNo, smsCode);
        if ("0".equals(smsCountryProperties.getFlag()) || "0".equals(smsInternatinolProperties.getFlag())) {
            return true;
        }
        String respStr = "";
        try {
            String fullMobileNo = getFullMobileNo(countryCode, mobileNo);
            if ("86".equals(countryCode) || "086".equals(countryCode) || "0086".equals(countryCode) || "+86".equals(countryCode)) {
                if (SmsCnUtil.httpRequest(fullMobileNo, validaateCodeProperties, s, validaateCodeProperties.getTemplate())) {
                    respStr = "true";
                }
            } else {
                //国际账号发送,手机号要加区号
                if (SmsCnUtil.httpRequest(fullMobileNo, validaateCodeProperties, s, validaateCodeProperties.getTemplateInternation())) {
                    respStr = "true";
                }
            }
            if (StringUtil.isEmpty(respStr)) {
                log.error("调用253平台发送验证码失败,253平台返回异常: {}", respStr);
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("调用253平台发送验证码异常, {}", e);
            return false;
        }
    }

    private String getFullMobileNo(String countryCode, String mobileNo) {
        return String.format("+%s %s", countryCode, mobileNo);
    }

    /**
     * 生成n位随机数，该方法仅使用num比较小的情况
     *
     * @param num 需要生成的位数
     */
    private String genRandomNum(int num) {
        Random random = new Random();
        String result = "";
        for (int i = 0; i < num; i++) {
            result += random.nextInt(10);
        }
        return result;
    }

    /**
     * 获取短息模板
     *
     * @param smsCodeType 短信类型  1注册 2找回密码
     * @param lang        语言标识 0-简体中文,1-繁体中文,2-英文
     * @return SmsTemplate
     */
    private SmsTemplate getSmsMessage(int smsCodeType, int lang) {

        return SmsTemplate.getByTypeCode(smsCodeType * 10 + lang);
    }
}
