package com.dzpk.crazypoker.activity.api;

import com.dzpk.crazypoker.activity.api.req.ActivityApplyReq;
import com.dzpk.crazypoker.activity.api.vo.UserRechargeActivityInfoVo;
import com.dzpk.crazypoker.activity.service.IActivityService;
import com.dzpk.crazypoker.activity.service.bean.UserRechargeActivityInfoBo;
import com.dzpk.crazypoker.club.api.req.ClubFundDetailReq;
import com.dzpk.crazypoker.club.api.req.ClubFundHistoryReq;
import com.dzpk.crazypoker.club.api.req.ClubGiveFundReq;
import com.dzpk.crazypoker.club.api.req.ClubRechargeReq;
import com.dzpk.crazypoker.club.api.vo.ClubFundHistoryVo;
import com.dzpk.crazypoker.club.service.bean.ClubFundHistoryBo;
import com.dzpk.crazypoker.common.dozer.BeanUtil;
import com.dzpk.crazypoker.common.service.InvokedResult;
import com.dzpk.crazypoker.common.utils.NetworkUtil;
import com.dzpk.crazypoker.common.web.controller.AbstractController;
import com.dzpk.crazypoker.common.web.resp.CommonResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * Created by jayce on 2019/5/31
 *
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping(
        value = "/activity/",
        method = RequestMethod.POST,
        consumes = "application/json;charset=UTF-8",
        produces = "application/json;charset=UTF-8")
@Api(tags = {"新注册用户Api"})
public class ActivityController extends AbstractController {

    @Autowired
    private IActivityService activityService;

    @Autowired
    private BeanUtil beanUtil;

    @ApiOperation(value = "参加活动",
            notes = "状态码<br/>" +
                    " 0 = 成功，如果需要数据可以从data字段解析<br/>" +
                    "999 = 失败，异常情况等")
    @ResponseBody
    @RequestMapping("kdou/apply")
    public CommonResponse apply(@RequestAttribute("user_id") int userId,
                                         @Validated @RequestBody ActivityApplyReq req,
                                         HttpServletRequest httpReq,
                                         BindingResult paramValidResult) {
        //常规校验
        handleValidatedError(paramValidResult);

        CommonResponse response = new CommonResponse<>();

        InvokedResult result = this.activityService.applyActivity(userId,NetworkUtil.getClientIp(httpReq),req.getI(),req.getM(),req.getS(),req.getG());

        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());

        return response;
    }

    @ApiOperation(value = "获取奖励",
            notes = "状态码<br/>" +
                    " 0 = 成功，如果需要数据可以从data字段解析<br/>" +
                    "999 = 失败，异常情况等")
    @ResponseBody
    @RequestMapping("kdou/gain")
    public CommonResponse gain(@RequestAttribute("user_id") int userId) {

        CommonResponse response = new CommonResponse<>();

        InvokedResult result = this.activityService.gain(userId);

        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());

        return response;
    }

    @ApiOperation(value = "参加首充活动",
            notes = "状态码<br/>" +
                    " 0 = 成功，如果需要数据可以从data字段解析<br/>" +
                    "999 = 失败，异常情况等")
    @ResponseBody
    @RequestMapping("recharge/apply")
    public CommonResponse rechargeActivityApply(@RequestAttribute("user_id") int userId,
                                @Validated @RequestBody ActivityApplyReq req,
                                HttpServletRequest httpReq,
                                BindingResult paramValidResult) {
        //常规校验
        handleValidatedError(paramValidResult);

        CommonResponse response = new CommonResponse<>();

        InvokedResult result = this.activityService.applyRechargeActivity(userId,NetworkUtil.getClientIp(httpReq),req.getI(),req.getM(),req.getS(),req.getG());

        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());

        return response;
    }

    @ApiOperation(value = "首充活动获取奖励",
            notes = "状态码<br/>" +
                    " 0 = 成功，如果需要数据可以从data字段解析<br/>" +
                    "999 = 失败，异常情况等")
    @ResponseBody
    @RequestMapping("recharge/gain")
    public CommonResponse rechargeActivityGain(@RequestAttribute("user_id") int userId) {

        CommonResponse response = new CommonResponse<>();

        InvokedResult result = this.activityService.gainRechargeActivity(userId);

        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());

        return response;
    }

    @ApiOperation(value = "查询自身活动状态",
            notes = "状态码<br/>" +
                    " 0 = 成功，如果需要数据可以从data字段解析<br/>" +
                    "999 = 失败，异常情况等")
    @ResponseBody
    @RequestMapping("recharge/query")
    public CommonResponse<UserRechargeActivityInfoVo> querySelfRechargeActivityStatus(@RequestAttribute("user_id") int userId) {
        CommonResponse<UserRechargeActivityInfoVo> response = new CommonResponse<>();

        InvokedResult<UserRechargeActivityInfoBo> result = this.activityService.querySelfRechargeActivityStatus(userId);

        if(null != result.getData()){
            response.setData(this.beanUtil.map(result.getData(),UserRechargeActivityInfoVo.class));
        }

        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());

        return response;
    }

}
