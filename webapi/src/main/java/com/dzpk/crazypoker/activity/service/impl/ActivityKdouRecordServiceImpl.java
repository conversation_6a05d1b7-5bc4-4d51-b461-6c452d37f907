package com.dzpk.crazypoker.activity.service.impl;

import com.dzpk.crazypoker.activity.repositories.mysql.IActivityKdouRecordDao;
import com.dzpk.crazypoker.activity.repositories.mysql.autogen.model.ActivityKdouRecordPo;
import com.dzpk.crazypoker.activity.service.IActivityKdouRecordService;
import com.dzpk.crazypoker.activity.service.bean.ActivityKdouRecordBo;
import com.dzpk.crazypoker.common.dozer.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ActivityKdouRecordServiceImpl implements IActivityKdouRecordService {
    @Resource
    private IActivityKdouRecordDao activityKdouRecordDao;
    @Autowired
    private BeanUtil beanUtil;

    @Override
    public List<ActivityKdouRecordBo> listByUserId(Integer userId, String level, Integer status, Integer nextId, Integer pageSize) {
        if (nextId == null || nextId < 0) {
            nextId = null;
        }
        List<ActivityKdouRecordPo> pos = activityKdouRecordDao.listByUserId(userId, level, status, nextId, pageSize);
        return beanUtil.map(pos, ActivityKdouRecordBo.class);
    }
}
