package com.dzpk.crazypoker.activity.repositories.mysql;

import com.dzpk.crazypoker.activity.repositories.mysql.autogen.model.ActivityKdouRecordPo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface IActivityKdouRecordDao {

    @Select({"<script>",
            "select r.* from activity_kdou_record r ",
            "<where>",
            " <if test='nextId != null'>",
            " and `id` &lt; #{nextId} ",
            " </if>",
            " and user_id = #{userId} ",
            " <if test='level != null'>",
            " and `level` = #{level} ",
            " </if>",
            " <if test='status != null'>",
            " and `status` = #{status} ",
            " </if>",
            " </where>",
            " order by id desc limit #{limit} ",
            "</script>"})
    @ResultMap("com.dzpk.crazypoker.activity.repositories.mysql.autogen.mapper.ActivityKdouRecordPoMapper.BaseResultMap")
    List<ActivityKdouRecordPo> listByUserId(@Param("userId") Integer userId, @Param("level") String level, @Param("status") Integer status, @Param("nextId") Integer nextId, @Param("limit") Integer limit);
}
