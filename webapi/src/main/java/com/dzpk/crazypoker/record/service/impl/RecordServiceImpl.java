package com.dzpk.crazypoker.record.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.dzpk.crazypoker.club.repositories.mysql.IClubDao;
import com.dzpk.crazypoker.club.repositories.mysql.autogen.mapper.ClubMemberPoMapper;
import com.dzpk.crazypoker.club.repositories.mysql.autogen.model.ClubMemberPo;
import com.dzpk.crazypoker.club.repositories.mysql.autogen.model.ClubMemberPoExample;
import com.dzpk.crazypoker.club.service.IClubService;
import com.dzpk.crazypoker.club.service.bean.ClubRecordBo;
import com.dzpk.crazypoker.common.constant.RespCode;
import com.dzpk.crazypoker.common.mongo.factory.IMongoInstanceFactory;
import com.dzpk.crazypoker.common.service.exception.ServiceException;
import com.dzpk.crazypoker.common.utils.JsonUtil;
import com.dzpk.crazypoker.common.utils.NullUtils;
import com.dzpk.crazypoker.record.api.bean.BaseRecordListVo;
import com.dzpk.crazypoker.record.api.bean.GameRecordVo;
import com.dzpk.crazypoker.record.config.RecordProperties;
import com.dzpk.crazypoker.record.repositories.IGameDetailDao;
import com.dzpk.crazypoker.record.repositories.IGameRecordDao;
import com.dzpk.crazypoker.record.repositories.IMttGameRecordDao;
import com.dzpk.crazypoker.record.repositories.IMttPlayerDetailDao;
import com.dzpk.crazypoker.record.repositories.model.*;
import com.dzpk.crazypoker.record.service.IRecordService;
import com.dzpk.crazypoker.record.service.bean.*;
import com.dzpk.crazypoker.room.repositories.mysql.autogen.mapper.DzRoomPoMapper;
import com.dzpk.crazypoker.room.repositories.mysql.autogen.model.DzRoomPo;
import com.dzpk.crazypoker.room.repositories.mysql.autogen.model.DzRoomPoExample;
import com.dzpk.crazypoker.tribe.repositories.mysql.ITribeDao;
import com.dzpk.crazypoker.tribe.repositories.mysql.autogen.mapper.TribeRecordPoMapper;
import com.dzpk.crazypoker.tribe.repositories.mysql.autogen.model.TribeRecordPo;
import com.dzpk.crazypoker.tribe.service.ITribeService;
import com.dzpk.crazypoker.tribe.service.bean.TribeMemberBo;
import com.dzpk.crazypoker.tribe.service.bean.TribeRecordBo;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class RecordServiceImpl implements IRecordService {

    @Autowired
    private IGameRecordDao gameRecordDao;

    @Autowired
    private IGameDetailDao gameDetailDao;

    @Autowired
    private IMttGameRecordDao mttGameRecordDao;

    @Autowired
    private IMttPlayerDetailDao mttPlayerDetailDao;

    @Autowired
    private IClubService clubService;

    @Autowired
    private ITribeService tribeService;

    @Autowired
    private RecordProperties recordProperties;

    @Autowired
    private DzRoomPoMapper roomDao;


    @Autowired
    private ClubMemberPoMapper clubMemberPoMapper;

    @Autowired
    private IClubDao clubDao;

    @Autowired
    private TribeRecordPoMapper tribeRecordPoMapper;

    @Autowired
    private IMongoInstanceFactory iMongoInstanceFactory;

    @Autowired
    private ITribeDao tribeDao;

    @Value("${game.record.list}")
    private String recordList;

    protected final Logger logger = LoggerFactory.getLogger(RecordServiceImpl.class);

    private final static Integer LIMIT = 10;

    interface GameRecordSource {
        List<GameRecordPo> getGameRecords();
    }

    /**
     *
     * @param userId
     * @param roomPaths
     * @param minTime
     * @param maxTime -1=no upper bound
     * @param clubId optional
     * @param limit 0=no limit
     * @return
     */
    @Override
    public List<GameRecordBo> listGameRecordByUserId(Integer userId, Set<Integer> roomPaths, long minTime, long maxTime,
                                                     Integer clubId, int limit) {
        return listGameRecord(minTime, maxTime, limit, () -> gameRecordDao.selectByUserIdAndBetweenEndTimeDesc(userId, roomPaths, minTime, maxTime, limit, clubId));
    }

    @Override
    public List<GameRecordBo> listGameRecordByTribeId(Integer userId, Set<Integer> roomPaths, long minTime, long maxTime,
                                                      Integer tribeId, int limit) {
        // 联盟创建者或联盟下的俱樂部创建者才有权限查看详情
        if(tribeDao.checkTribeClubPermission(userId, tribeId) <= 0){//身份权限不合法
            throw new ServiceException(RespCode.TRIBE_PERMISSION_ERROR.getCode(), RespCode.TRIBE_PERMISSION_ERROR.getDesc());
        }
        return listGameRecord(minTime, maxTime, limit, () -> gameRecordDao.selectByTribeIdAndBetweenEndTimeDesc(tribeId, roomPaths, minTime, maxTime, limit));
    }

    @Override
    public List<GameRecordBo> listGameRecordByClubId(Integer userId, Set<Integer> roomPaths, long minTime, long maxTime,
                                                      Integer clubId, int limit) {
        // 俱樂部创建者才有权限查看详情
        if(clubDao.checkClubPermission(userId, clubId) <= 0){//身份权限不合法
            throw new ServiceException(RespCode.CLUB_PERMISSION_ERROR.getCode(), RespCode.CLUB_PERMISSION_ERROR.getDesc());
        }
        return listGameRecord(minTime, maxTime, limit, () -> gameRecordDao.selectByClubIdAndBetweenEndTimeDesc(clubId, roomPaths, minTime, maxTime, limit));
    }

    private List<GameRecordBo> listGameRecord(long minTime, long maxTime, int limit, GameRecordSource source) {
        if (minTime > maxTime && maxTime != -1) {
            //超过限制的最大查询时间
            return new ArrayList<>(0);
        }
        if (limit < 0) {
            return new ArrayList<>(0);
        }
        List<GameRecordPo> gameRecordPos = source.getGameRecords();
        if (gameRecordPos.isEmpty()) {
            return new ArrayList<>(0);
        }
        logger.debug("查询数据的牌局长度:{}", gameRecordPos.size());
        for (GameRecordPo gameRecordPo : gameRecordPos) {
            logger.debug("牌局数据: clubId:{},clubName:{},roomName:{}", gameRecordPo.getClubId(), gameRecordPo.getClubName(), gameRecordPo.getRoomName());
        }
        // 得到game_detail根据id,data的一个map
        Map<String, GameDetailPo> id2GdPoMap = gameDetailDao.selectByIds(gameRecordPos.stream()
                .map(GameRecordPo::getRoomId)
                .collect(Collectors.toList()), GameDetailPo.class)
                .stream()
                .collect(Collectors.toMap(GameDetailPo::getObjectId,
                        Function.identity(), (e1, e2) -> e1));
        logger.debug("得到game_detail分组信息:{}", JsonUtil.toJson(id2GdPoMap, true));
        Map<String, Integer> onlySet = new HashMap<>();
        return gameRecordPos.stream().map(e -> po2Bo(e, id2GdPoMap, onlySet)).filter(Objects::nonNull).peek(e -> {
            Integer sums = onlySet.get(e.getRoomId());
            e.setPersonRecord(sums);
        }).collect(Collectors.toList());
    }

    private GameRecordBo po2Bo(GameRecordPo po, Map<String, GameDetailPo> id2GdPoMap, Map<String, Integer> onlySet) {
        if (onlySet.containsKey(po.getRoomId())) {
            Integer sum = onlySet.get(po.getRoomId());
            sum += po.getProfitLose();
            onlySet.put(po.getRoomId(), sum);
            return null;
        }
        GameRecordBo bo = new GameRecordBo();
        // game_detail明细信息-牌局信息
        GameDetailPo gameDetailPo = id2GdPoMap.get(po.getRoomId());
        boolean isInsurance = false;
        boolean jackPotOp = false;
        if (gameDetailPo != null) {
            isInsurance = Integer.valueOf(1).equals(gameDetailPo.getInsurance());
            jackPotOp = Integer.valueOf(1).equals(gameDetailPo.getJackpot());
        }
        bo.setRoomPath(po.getRoomPath());
        bo.setBlind(po.getBlind());
        bo.setGameTime(po.getMaxPlayTime());
        bo.setInsuranceOp(isInsurance);
        bo.setJackPotOp(jackPotOp);
        bo.setBlind(po.getBlind());
        // 需要求和
        bo.setPersonRecord(po.getProfitLose());
        bo.setRoomName(po.getRoomName());
        // 游戏记录的id
        bo.setRoomId(po.getRoomId());
        bo.setStartTime(po.getTime());
        bo.setEndTime(po.getEndTime());

        // 俱樂部/聯盟信息
        try {
            bo.setClubId(gameDetailPo.getRoomClubId());
            bo.setClubName(gameDetailPo.getRoomClubName());
            bo.setTribeId(gameDetailPo.getRoomTribeId());
            bo.setTribeName(gameDetailPo.getRoomTribeName());
        } catch (Exception e) {
            logger.error("获取俱樂部/聯盟信息错误: {}", e.getMessage());
        }

        bo.setTotalHand(gameDetailPo.getTotalHand());

//        DzRoomPoExample roomExample = new DzRoomPoExample();
//        roomExample.or().andRoomIdEqualTo(gameDetailPo.getRoomId());
//        List<DzRoomPo> rooms = roomDao.selectByExample(roomExample);
//
//        if (!rooms.isEmpty()) {
//            int tribeId = rooms.get(0).getTribeId();
//            bo.setTribeId(tribeId);
//            if (tribeId != 0) {
//                TribeRecordPo tribeRecord = tribeRecordPoMapper.selectByPrimaryKey(tribeId);
//                bo.setTribeName(tribeRecord.getTribeName());
//            }
//        }

        onlySet.put(po.getRoomId(), po.getProfitLose());
        return bo;
    }

    /**
     * 超级统计
     *
     * @param userId
     * @param roomIds
     * @return
     */
    @Override
    public GameDetailBo superDetail(int userId, Set<String> roomIds) {
        GameDetailBo gameDetailBo = new GameDetailBo();
        if (CollectionUtils.isEmpty(roomIds)) {
            return gameDetailBo;
        }
        // 通过roomid先查询出这个游戏所有的玩家数据，然后在进行调detail在进行求和
        List<GameDetailPo> allGameDetails = Lists.newArrayList();
        Map<String, GameDetailPo> detailPoMap = new HashMap<>();
        for (String roomId : roomIds) {
            GameDetailPo gameDetailBos = gameDetailDao.selectById(roomId, GameDetailPo.class);
            if (gameDetailBos != null) {
                allGameDetails.add((gameDetailBos));
                detailPoMap.put(roomId, gameDetailBos);
            }
        }
        // 得到所有的list之后进行响应的求和处理
        logger.info("得到了所有的数据，进行求和处理:{}", JsonUtil.toJson(allGameDetails, true));
        // 得到了玩家的数据，然后进行拼接数据
        List<UserGameRecordBo> allUserRecordBos = new ArrayList<>();
        for (GameDetailPo record : allGameDetails) {
            // 总手数
            gameDetailBo.setTotalHand(record.getTotalHand() + gameDetailBo.getTotalHand());
            // 总带入
            gameDetailBo.setTotalBring(record.getAllBring() + gameDetailBo.getTotalBring());
            // 得到 盈利数据
            int totalEarn = 0;
            int totalLose = 0;
            List<UserGameRecordBo> userRecordBos = recordBoList(record);
            if (CollectionUtils.isNotEmpty(userRecordBos)) {
                allUserRecordBos.addAll(userRecordBos);
            }
            for (UserGameRecordBo userRecordBo : userRecordBos) {
                int pl = userRecordBo.getPl();
                if (pl > 0) {
                    totalEarn += pl;
                } else {
                    totalLose += pl;
                }
            }
            // 统计详细数据
            gameDetailBo.setTotalPl(totalEarn + gameDetailBo.getTotalPl());
            gameDetailBo.setTotalLose(totalLose + gameDetailBo.getTotalLose());
            // 保险池
            if (record.getInsurancePool() != null) {
                gameDetailBo.setInsurancePool(record.getInsurancePool() + gameDetailBo.getInsurancePool());
            }
            // 服务费
            if (record.getRoomChargeTotal() != null) {
                gameDetailBo.setRoomChargeTotal(record.getRoomChargeTotal() + gameDetailBo.getRoomChargeTotal());
            }
        }
        //logger.info("得到上面的统计数据,{}", JsonUtil.toJson(gameDetailBo, true));
        // 获取具体玩家列表
        logger.info("所有的玩家数据2:{}", JsonUtil.toJson(allUserRecordBos, true));
        // 直接对用户id进行求和
        Map<Integer, UserGameRecordBo> recordBoMap = new HashMap<>(16);
        for (UserGameRecordBo recordBo : allUserRecordBos) {
            UserGameRecordBo gameRecordPo = recordBoMap.get(recordBo.getUserId());
            if (gameRecordPo == null) {
                recordBoMap.put(recordBo.getUserId(), recordBo);
            } else {
                // 对数据进行求和  "hand" : 2,  "bring" : 20000,"pl" : -19800,"insurance" : 0
                gameRecordPo.setHand(gameRecordPo.getHand() + recordBo.getHand());
                gameRecordPo.setPl(gameRecordPo.getPl() + recordBo.getPl());
                gameRecordPo.setBring(gameRecordPo.getBring() + recordBo.getBring());
                gameRecordPo.setInsurance(gameRecordPo.getInsurance() + recordBo.getBring());
                recordBoMap.put(recordBo.getUserId(), gameRecordPo);
            }
        }
        List<UserGameRecordBo> userRecordList = new ArrayList<>();
        recordBoMap.forEach((userIds, value) -> {
            userRecordList.add(value);
        });
        gameDetailBo.setUserRecords(userRecordList);
        // 继续设置honor数据
        List<HonorPlayerBo> honorPlayerBos = getHonorPlayerBos(gameDetailBo);
        gameDetailBo.setHonors(honorPlayerBos);

        // 补充不必要数
        gameDetailBo.setRoomId(-1);
        gameDetailBo.setRoomName("--");
        gameDetailBo.setRoomPath(-1);
        gameDetailBo.setVpOp(true);
        gameDetailBo.setJpOp(true);
        gameDetailBo.setInsuranceOp(true);
        gameDetailBo.setEndTime(LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")));
        gameDetailBo.setPlayTime(120);
        gameDetailBo.setManager(true);
        gameDetailBo.setBLind("100/200");
        gameDetailBo.setJpRecords(new ArrayList<>(2));
        gameDetailBo.setInsurances(new ArrayList<>(2));
        gameDetailBo.setClubGameRecord(new ArrayList<>(2));
        return gameDetailBo;
    }

    /**
     * 处理honor数据
     *
     * @param gameDetailBo
     * @return
     */
    private List<HonorPlayerBo> getHonorPlayerBos(GameDetailBo gameDetailBo) {
        List<HonorPlayerBo> honorPlayerBos = new ArrayList<>(3);
        if (CollectionUtils.isEmpty(gameDetailBo.getUserRecords())) {
            return honorPlayerBos;
        }
        List<UserGameRecordBo> values = gameDetailBo.getUserRecords();
        for (int i = 0; i < 3; i++) {
            HonorPlayerBo honorPlayerBo = new HonorPlayerBo();
            //1-MVP=赢的最多的，2-大鱼输的最多的，3-土豪-带入最多的
            if (i == 0) {
                UserGameRecordBo userGameRecordBo = values.stream().sorted(Comparator.comparing(UserGameRecordBo::getPl).reversed()).findFirst().orElse(null);
                honorPlayerBo.setLabelType(3);
                honorPlayerBo.setUser(userGameRecordBo);
            } else if (i == 1) {
                UserGameRecordBo userGameRecordBo = values.stream().sorted(Comparator.comparing(UserGameRecordBo::getPl)).findFirst().orElse(null);
                honorPlayerBo.setLabelType(2);
                honorPlayerBo.setUser(userGameRecordBo);
            } else {
                UserGameRecordBo userGameRecordBo = values.stream().sorted(Comparator.comparing(UserGameRecordBo::getBring).reversed()).findFirst().orElse(null);
                honorPlayerBo.setLabelType(1);
                honorPlayerBo.setUser(userGameRecordBo);
            }
            honorPlayerBos.add(honorPlayerBo);
        }
        return honorPlayerBos;
    }


    @Override
    public GameDetailBo detail(int userId, String roomId) {
        GameDetailBo gameDetailBo = new GameDetailBo();
        boolean manager = true;
        List<HonorPlayerBo> honorPlayers = null;
        List<UserGameRecordBo> userRecordBos = null;
        List<ClubGameRecordBo> clubGameRecordBos = null;
        int totalEarn = 0;
        int totalLose = 0;

        boolean vpOp;
        boolean jpOp;
        boolean insuranceOp;

        GameDetailPo po = gameDetailDao.selectById(roomId, GameDetailPo.class);
        Integer trueInt = Integer.valueOf(1);
        vpOp = trueInt.equals(po.getVp());
        jpOp = trueInt.equals(po.getJackpot());
        insuranceOp = trueInt.equals(po.getInsurance());

        userRecordBos = recordBoList(po);

        //统计
        for (UserGameRecordBo userRecordBo : userRecordBos) {
            int pl = userRecordBo.getPl();
            if (pl > 0) {
                totalEarn += pl;
            } else {
                totalLose += pl;
            }
        }

        Map<Integer, UserGameRecordBo> id2BoMap = userRecordBos.stream().collect(Collectors.toMap(
                UserGameRecordBo::getUserId, Function.identity(), (e1, e2) -> e1
        ));
        // 处理个人是俱乐部的什么成员数据
        // 处理判断服务费数据
        boolean normalClubMeeber = false;
        double serviceCharge = 0;
        DzRoomPoExample example = new DzRoomPoExample();
        DzRoomPoExample.Criteria criteria = example.createCriteria();
        criteria.andRoomIdEqualTo(po.getRoomId());
        List<DzRoomPo> dzRoomPos = roomDao.selectByExample(example);
        if (CollectionUtils.isNotEmpty(dzRoomPos)) {
            DzRoomPo dzRoomPo = dzRoomPos.get(0);
            if (dzRoomPo != null) {
                serviceCharge = dzRoomPo.getServiceCharge();
                // 查询当前的人的俱乐部属性
                ClubMemberPoExample poExample = new ClubMemberPoExample();
                ClubMemberPoExample.Criteria clubMembersCriteria = poExample.createCriteria();
                clubMembersCriteria.andClubIdEqualTo(dzRoomPo.getClubId());
                clubMembersCriteria.andUserIdEqualTo(String.valueOf(userId));
                List<ClubMemberPo> clubMemberPos = clubMemberPoMapper.selectByExample(poExample);
                if (CollectionUtils.isNotEmpty(clubMemberPos)) {
                    ClubMemberPo clubMemberPo = clubMemberPos.get(0);
                    if (clubMemberPo != null) {
                        //获取到用你数据
                        if (clubMemberPo.getType() == 2 || clubMemberPo.getType() == 5) {
                            normalClubMeeber = true;
                        }
                    }
                }
            }
        }
        logger.info("服务费数据:{},普通成员:{}", serviceCharge, normalClubMeeber);
        honorPlayers = honorPlayerBoList(po, userRecordBos);
        //俱乐部汇总

        clubGameRecordBos = clubGameRecordBoList(userRecordBos, userId);

        gameDetailBo.setEndTime(po.getEndTime());
        gameDetailBo.setPlayTime(((int) ((po.getEndTime() - po.getTime()) / 1000)));
        gameDetailBo.setBLind(po.getBlind());
        gameDetailBo.setHonors(honorPlayers);
        gameDetailBo.setManager(manager);
        gameDetailBo.setRoomId(po.getRoomId());
        gameDetailBo.setRoomName(po.getRoomName());
        gameDetailBo.setTotalBring(po.getAllBring());
        gameDetailBo.setTotalPl(totalEarn);
        gameDetailBo.setTotalLose(totalLose);
        gameDetailBo.setTotalHand(po.getTotalHand());
        gameDetailBo.setUserRecords(userRecordBos);
        gameDetailBo.setClubGameRecord(clubGameRecordBos);
        gameDetailBo.setVpOp(vpOp);
        gameDetailBo.setJpOp(jpOp);
        gameDetailBo.setInsuranceOp(insuranceOp);
        gameDetailBo.setInsurancePool(NullUtils.intNull2Zero(po.getInsurancePool()));
        gameDetailBo.setRoomPath(po.getRoomPath());
        gameDetailBo.setCompeteModeOn(false);
        //TODO 大菠萝战绩详细待处理,竞技模式字段
        gameDetailBo.setRoomChargeTotal(po.getRoomChargeTotal() != null ? po.getRoomChargeTotal() : 0);
        // 进行处理服务费数据
        if (normalClubMeeber) {
            // 普通成员
            if (serviceCharge != 0) {
                gameDetailBo.setRoomChargeTotalStr(serviceCharge + "%");
            }
        } else {
            // 管理员
            gameDetailBo.setRoomChargeTotalStr(po.getRoomChargeTotal() != null ? String.valueOf(po.getRoomChargeTotal() / 100) : "");
        }
        if (insuranceOp) {
            gameDetailBo.setInsurances(insuranceList(userRecordBos));
        }

        if (jpOp) {
            gameDetailBo.setJpRecords(loadJpList(id2BoMap, po));
        }
        logger.info("detail 得到的数据:{}", JsonUtil.toJson(gameDetailBo, false));
        return gameDetailBo;
    }

    @Override
    public List<MttGameRecordBo> listMttGameRecordByUserId(Integer userId, Long nextTime) {

        // 查询用户是管理员或创建者的同盟
        List<Integer> tribeIds = clubDao.getManageTribe(userId);
        logger.debug("用户是管理员或者创建者的同盟id为：【{}】", tribeIds);

        // 公会的APP端，管理员和创建者只能看到近7天的战绩 普通用户可以查看30天数据
        boolean showWeekData = false;
        for (int tribeId : tribeIds) {
            List<Integer> gameRecordList = getGameRecordList();
            if (gameRecordList != null && gameRecordList.contains(String.valueOf(tribeId))) {
                showWeekData = true;
            }
        }
        Long maxTime = null; // 时间范围查询，最大值
        long now = System.currentTimeMillis();  // 毫秒
        Long minTime = now - 30 * 24 * 60 * 60 * 1000L;  // 时间范围查询，最小值。默认查看前30天数据
        if (showWeekData) {
            minTime = now - 7 * 24 * 60 * 60 * 1000L;   // 管理员和创建者只能看到近7天数据
        }

        // 查询用户参与的mtt牌局id
        if (nextTime != null && nextTime != 0) {
            maxTime = nextTime;
        }
        if (minTime > maxTime) {
            logger.info("listMttGameRecordByUserId minTime greater than maxTime. minTime:{}, maxTime:{}.", minTime, maxTime);
        }
        List<MttPlayerDetailPo> mttPlayerDetailList = mttPlayerDetailDao.selectMttPlayerDetailByUserIdAndTimeLimit(userId, maxTime == null ? null : maxTime / 1000, minTime == null ? null : minTime / 1000, LIMIT);

        // 查询mtt牌局的信息列表
        if (mttPlayerDetailList != null && mttPlayerDetailList.size() != 0) {
            List<Integer> gameIdList = new ArrayList<>(); // 获取gameId集合
            Map<Integer, Integer> rankMap = new HashMap<>();
            for (MttPlayerDetailPo detailPo : mttPlayerDetailList) {
                rankMap.put(detailPo.getGameID(), detailPo.getRank());
                gameIdList.add(detailPo.getGameID());
            }

            List<MttGameRecordPo> gameRecordPoList = mttGameRecordDao.selectByGameIds(gameIdList);
            if (gameRecordPoList != null && gameRecordPoList.size() != 0) {
                List<MttGameRecordBo> mttGameRecordBos = new ArrayList<>();
                for (MttGameRecordPo mttGameRecordPo : gameRecordPoList) {
                    MttGameRecordBo mttGameRecordBo = parseMttBo(mttGameRecordPo);
                    if (mttGameRecordBo != null) {
                        mttGameRecordBo.setRank(rankMap.get(mttGameRecordBo.getGameId()));
                        mttGameRecordBo.setStartTime(mttGameRecordBo.getStartTime() * 1000); // 转化为毫秒
                        mttGameRecordBo.setEndTime(mttGameRecordBo.getEndTime() * 1000); // 转化为毫秒
                        mttGameRecordBos.add(mttGameRecordBo);
                    }
                }
                return mttGameRecordBos;
            }
        }
        return null;
    }

    /**
     * 查询mtt战绩详情
     *
     * @param userId
     * @param gameID
     * @return
     */
    @Override
    public MttGameDetailBo mttDetail(Integer userId, Integer gameID) {
        // 查询
        List<MttPlayerDetailPo> mttPlayerDetailPoList = mttPlayerDetailDao.selectMttPlayerDetailByGameIDOrderByClubIdAndRank(gameID);
        MttGameRecordPo mttGameRecordPo = mttGameRecordDao.selectByGameId(gameID);

        Double myBonus = 0.0;
        if (mttPlayerDetailPoList != null && mttPlayerDetailPoList.size() != 0) {
            // 统计数据
            Map<String, MttClubGameRecordBo> clubMap = new HashMap<>();
            List<MttUserGameRecordBo> honorsList = new ArrayList<>();// 荣耀榜
            List<MttClubGameRecordBo> clubList = new ArrayList<>(); // 俱乐部数据
            List<MttUserGameRecordBo> allUserRecordList = new ArrayList<>(); // 所有人战绩
            for (MttPlayerDetailPo mttPlayerDetailPo : mttPlayerDetailPoList) {
                if (mttPlayerDetailPo.getUserID() == userId) {// 登录用户本人的战绩
                    myBonus = mttPlayerDetailPo.getBonus();
                }
                MttClubGameRecordBo mttClubGameRecordBo = clubMap.get(mttPlayerDetailPo.getClubId());
                if (mttClubGameRecordBo == null) {
                    mttClubGameRecordBo = new MttClubGameRecordBo();
                }
                mttClubGameRecordBo.setClubId(mttPlayerDetailPo.getClubId());
                mttClubGameRecordBo.setClubName(mttPlayerDetailPo.getClubName());
                mttClubGameRecordBo.setTotalPl((mttClubGameRecordBo.getTotalPl() == null ? 0 : mttClubGameRecordBo.getTotalPl()) + mttPlayerDetailPo.getBonus());
                List<MttUserGameRecordBo> mttUserGameRecordBoList = mttClubGameRecordBo.getUserGameRecords();
                if (mttUserGameRecordBoList == null) {
                    mttUserGameRecordBoList = new ArrayList<>();
                }
                MttUserGameRecordBo mttUserGameRecordBo = parseMttUserGameRecordBo(mttPlayerDetailPo);
                if (mttUserGameRecordBo != null) {
                    allUserRecordList.add(mttUserGameRecordBo); // 所有人战绩列表
                    mttUserGameRecordBoList.add(mttUserGameRecordBo);
                    if (mttUserGameRecordBo.getRank() <= 3) {// 荣耀榜
                        honorsList.add(mttUserGameRecordBo);
                    }
                }
                mttClubGameRecordBo.setUserGameRecords(mttUserGameRecordBoList);
                clubMap.put(mttPlayerDetailPo.getClubId(), mttClubGameRecordBo);
            }

            // 俱乐部数据
            for (String key : clubMap.keySet()) {
                MttClubGameRecordBo mttClubGameRecordBo = clubMap.get(key);
                clubList.add(mttClubGameRecordBo);
            }

            // 转换数据
            MttGameDetailBo mttGameDetailBo = new MttGameDetailBo();
            mttGameDetailBo.setGameID(gameID);
            mttGameDetailBo.setMttName(mttGameRecordPo.getMttName());
            mttGameDetailBo.setRoomPath(71);
            mttGameDetailBo.setEndTime(mttGameRecordPo.getEndTime());
            mttGameDetailBo.setPlayTime(mttGameRecordPo.getPlayTime());
            mttGameDetailBo.setTotalHand(mttGameRecordPo.getTotalHand());
            mttGameDetailBo.setTotalBonus(mttGameRecordPo.getTotalBonus());
            mttGameDetailBo.setClubGameRecord(clubList);
            mttGameDetailBo.setHonors(honorsList);
            mttGameDetailBo.setMyBonus(myBonus);
            mttGameDetailBo.setCreator(mttGameRecordPo.getCreatorName());
            mttGameDetailBo.setPariticipants(mttGameRecordPo.getPariticipants());
            // allUserRecordList 按rank排序
            Collections.sort(allUserRecordList, new Comparator<MttUserGameRecordBo>() {
                @Override
                public int compare(MttUserGameRecordBo o1, MttUserGameRecordBo o2) {
                    return o1.getRank().compareTo(o2.getRank());
                }
            });
            mttGameDetailBo.setAllUsersRecords(allUserRecordList);
            return mttGameDetailBo;
        }
        return null;
    }

    @Override
    public void setHands(BaseRecordListVo<GameRecordVo> vo, Integer userId) {

        if (vo == null || ObjectUtils.isEmpty(vo.getList())) {
            return;
        }

        List<String> ids = vo.getList().stream().map(GameRecordVo::getRoomId).collect(Collectors.toList());

        List<ObjectId> roomObjectIds = ids.stream().map(ObjectId::new).collect(Collectors.toList());

        ReactiveMongoTemplate template = iMongoInstanceFactory.defaultInstance().getTemplate();

        // 先从game_detail表中查询出所有的room_id
        List<JSONObject> details = template.find(new Query(Criteria.where("_id").in(roomObjectIds)), JSONObject.class, "game_detail").collectList().block();

        if (ObjectUtils.isEmpty(details)) {
            return;
        }
        logger.info("查询到的game_detail数据:{}", details.size());
        // 转成map
        Map<String, JSONObject> detailMap = details.stream().collect(Collectors.toMap(e -> e.getString("_id"), Function.identity(), (e1, e2) -> e1));

        // 获取所有room_id
        List<Integer> roomIds = details.stream().map(e -> e.getInteger("room_id")).collect(Collectors.toList());

        // 再根据room_id与user_id查询出game_data_room
        Query query = new Query(Criteria.where("room_id").in(roomIds).and("user_id").is(userId));
        List<JSONObject> gameDataRooms = template.find(query, JSONObject.class, "game_data_room").collectList().block();

        if (ObjectUtils.isEmpty(gameDataRooms)) {
            return;
        }
        logger.info("查询到的game_data_room数据:{}", gameDataRooms.size());

        // 转成map
        Map<String, JSONObject> gameDataRoomMap = gameDataRooms.stream().collect(Collectors.toMap(e -> e.getString("room_id"), Function.identity(), (e1, e2) -> e1));

        // 最后回填hands到vo中
        vo.getList().forEach(e -> {
            JSONObject detail = detailMap.get(e.getRoomId());
            if (detail != null) {
                JSONObject gameDataRoom = gameDataRoomMap.get(detail.getString("room_id"));
                logger.info("gameDataRoom:{}", gameDataRoom);
                if (gameDataRoom != null) {
                    String hands = gameDataRoom.getString("hands");
                    if (!ObjectUtils.isEmpty(hands)) {
                        // 逗号切割并转换成List<Integer>
                        List<Integer> handList = Arrays.stream(hands.split(",")).map(Integer::parseInt).collect(Collectors.toList());
                        e.setHands(handList);
                    }
                }
            }
        });
    }

    private MttUserGameRecordBo parseMttUserGameRecordBo(MttPlayerDetailPo mttPlayerDetailPo) {
        if (mttPlayerDetailPo != null) {
            MttUserGameRecordBo mttUserGameRecordBo = new MttUserGameRecordBo();
            mttUserGameRecordBo.setUserId(mttPlayerDetailPo.getUserID());
            mttUserGameRecordBo.setHead(mttPlayerDetailPo.getUserHead());
            mttUserGameRecordBo.setNickName(mttPlayerDetailPo.getUserName());
            mttUserGameRecordBo.setClubId(mttPlayerDetailPo.getClubId());
            mttUserGameRecordBo.setClubName(mttPlayerDetailPo.getClubName());
            mttUserGameRecordBo.setRank(mttPlayerDetailPo.getRank());
            mttUserGameRecordBo.setBonus(mttPlayerDetailPo.getBonus());
            return mttUserGameRecordBo;
        }
        return null;
    }

    private MttGameRecordBo parseMttBo(MttGameRecordPo mttGameRecordPo) {
        if (mttGameRecordPo != null) {
            MttGameRecordBo mttGameRecordBo = new MttGameRecordBo();
            mttGameRecordBo.setGameId(mttGameRecordPo.getGameID());
            mttGameRecordBo.setMttName(mttGameRecordPo.getMttName());
            mttGameRecordBo.setStartTime(mttGameRecordPo.getStartTime());
            mttGameRecordBo.setEndTime(mttGameRecordPo.getEndTime());
            mttGameRecordBo.setPariticipants(mttGameRecordPo.getPariticipants());
            mttGameRecordBo.setRegistationFee(mttGameRecordPo.getEntryFee());
            return mttGameRecordBo;
        }
        return null;
    }


    private List<UserGameRecordBo> recordBoList(GameDetailPo po) {
        if (po == null || po.getUserIdArray().length() == 0) {
            return new ArrayList<>();
        }

        String[] userIdStr = NullUtils.strNull2Empty(po.getUserIdArray()).split(","); //用户id
        String[] brings = NullUtils.strNull2Empty(po.getBringArray()).split(",");    //用户带入
        String[] clubIds = NullUtils.strNull2Empty(po.getClubId()).split(",");     //俱乐部id
        String[] earns = NullUtils.strNull2Empty(po.getPlArray()).split(",");     //盈利
        String[] hands = NullUtils.strNull2Empty(po.getHandArray()).split(",");  //手数
        String[] heads = NullUtils.strNull2Empty(po.getUserHead()).split("@%");   //头像
        String[] nickNames = NullUtils.strNull2Empty(po.getUserNickName()).split("@%"); //昵称
        String[] insurance = NullUtils.strNull2Empty(po.getAllInsurance()).split(",");
        String[] clubNames = NullUtils.strNull2Empty(po.getClubName()).split(",");
        String[] tribeIds = NullUtils.strNull2Empty(po.getTribeIdRepeat()).split(",");
        List<UserGameRecordBo> recordBos = new ArrayList<>(userIdStr.length);


        for (int i = 0; i < userIdStr.length; i++) {
            UserGameRecordBo userRecordBo = new UserGameRecordBo();
            userRecordBo.setUserId(Integer.parseInt(userIdStr[i]));
            userRecordBo.setBring(NullUtils.string2int(brings, i));
            userRecordBo.setClubId(NullUtils.string2int(clubIds, i));
            userRecordBo.setPl(NullUtils.string2int(earns, i));
            userRecordBo.setHand(NullUtils.string2int(hands, i));
            userRecordBo.setHead(NullUtils.arrayIndex(heads, i));
            userRecordBo.setNickName(NullUtils.arrayIndex(nickNames, i));
            userRecordBo.setInsurance(NullUtils.string2int(insurance, i));
            userRecordBo.setTribeId(NullUtils.string2int(tribeIds, i));
            userRecordBo.setClubName(NullUtils.arrayIndex(clubNames, i));
            recordBos.add(userRecordBo);
        }

        return recordBos.stream().sorted((o1, o2) -> o2.getPl().compareTo(o1.getPl())).collect(Collectors.toList());
    }

    private List<HonorPlayerBo> honorPlayerBoList(GameDetailPo po, List<UserGameRecordBo> userRecordBos) {

        List<HonorPlayerBo> honorPlayerBos = new ArrayList<>(3);

        if (po == null || po.getUserNick() == null) {
            return new ArrayList<>(0);
        }
        String[] userNick = po.getUserNick().split("@%");

        Map<String, UserGameRecordBo> nick2UserBo = userRecordBos.stream().collect(Collectors.toMap(UserGameRecordBo::getNickName, Function.identity(), (e1, e2) -> e1));

        for (int i = 0; i < userNick.length; i++) {
            HonorPlayerBo honorPlayerBo = new HonorPlayerBo();

            UserGameRecordBo userRecordBo = nick2UserBo.get(userNick[i]);
            if (userRecordBo != null) {
                honorPlayerBo.setUser(userRecordBo);
            }

            honorPlayerBos.add(honorPlayerBo);
            //目前标签是存储是有序的
            //1-MVP，2-大鱼，3-土豪
            if (i == 0) {
                honorPlayerBo.setLabelType(3);
            } else if (i == 1) {
                honorPlayerBo.setLabelType(1);
            } else {
                honorPlayerBo.setLabelType(2);
            }
        }

        return honorPlayerBos;
    }

    private List<ClubGameRecordBo> clubGameRecordBoList(List<UserGameRecordBo> userRecordBos, Integer userId) {

        if (userRecordBos.size() == 0) {
            return new ArrayList<>(0);
        }

        List<ClubGameRecordBo> clubGameRecordBos = new ArrayList<>();
        Map<Integer, ClubGameRecordBo> clubId2GameBo = new HashMap<>();

        ClubRecordBo club = clubService.findByCreatorId(userId);
        TribeRecordBo tribe = tribeService.findByCreatorId(userId);
        final Integer tribeId = tribe != null ? tribe.getId() : null;
        final Integer clubId = club != null ? club.getId() : null;


        userRecordBos.stream().filter(e -> e.getTribeId().equals(tribeId)
                || e.getClubId().equals(clubId)).forEach(
                e -> {
                    ClubGameRecordBo bo = clubId2GameBo.get(e.getClubId());
                    if (bo == null) {
                        bo = new ClubGameRecordBo();
                        bo.setClubId(e.getClubId());
                        bo.setTotalPl(0);
                        bo.setUserGameRecords(new ArrayList<>());
                        bo.setClubName(e.getClubName());
                        clubId2GameBo.put(bo.getClubId(), bo);
                    }
                    bo.getUserGameRecords().add(e);
                    bo.setTotalPl(bo.getTotalPl() + e.getPl());
                }
        );
        clubGameRecordBos.addAll(clubId2GameBo.values());

        return clubGameRecordBos;
    }

    private List<UserGameRecordBo> insuranceList(List<UserGameRecordBo> userRecordBos) {
        return userRecordBos.stream().filter(e -> e.getInsurance() != 0).collect(Collectors.toList());
    }

    private List<UserJpRecordBo> loadJpList(Map<Integer, UserGameRecordBo> userRecordMap, GameDetailPo po) {
        List<UserJpRecordBo> userJpRecordBos = new ArrayList<>();
        String[] jpUsers = NullUtils.strNull2Empty(po.getJpUserArray()).split(","); //jpuser
        String[] jps = NullUtils.strNull2Empty(po.getJpRewardArray()).split(","); //jp
        String[] jpCardType = NullUtils.strNull2Empty(po.getJpPokerType()).split(",");

        if (jpUsers != null) {
            for (int i = 0; i < jpUsers.length; i++) {
                String userIdStr = jpUsers[i];
                if (userIdStr == null || userIdStr.trim().isEmpty()) {
                    continue;
                }
                UserJpRecordBo bo = new UserJpRecordBo();
                bo.setJpPokerType(NullUtils.string2int(jpCardType, i));
                bo.setJpReward(NullUtils.string2int(jps, i));
                UserGameRecordBo userGameRecordBo = userRecordMap.get(NullUtils.strNull2Zero(jpUsers[i]));
                bo.setUser(userGameRecordBo);
                userJpRecordBos.add(bo);
            }

        }
        return userJpRecordBos;
    }

    /**
     * 根据用户id获取该用户可以查询的俱乐部id
     *
     * @param userId
     * @return
     */
    private Set<Integer> findICanSeeClub(Integer userId) {
        Set<Integer> clubIds = new HashSet<>();
        //1.获取user自己所创建的俱乐部
        ClubRecordBo clubBo = clubService.findByCreatorId(userId);
        if (clubBo != null) {
            clubIds.add(clubBo.getId());
        }

        //获取自己创建的联盟

        TribeRecordBo tribeRecordBo = tribeService.findByCreatorId(userId);

        //自己联盟下的俱乐部信息信息
        if (tribeRecordBo != null) {
            List<TribeMemberBo> tribeMemberBo = tribeService.findTribeMemberByTribeId(tribeRecordBo.getId());
            tribeMemberBo.forEach(e -> clubIds.add(e.getClubId()));
        }

        return clubIds;
    }


    /**
     * 获取当前用户可以运行查询最早的时间,也就是最小的时间,返回-1,则没有下限
     *
     * @return
     * @deprecated
     */
    private long getMinTime(Integer userId) {
        if (recordProperties.getSevenDayLimit().contains(userId)) {
            return System.currentTimeMillis() - 7 * 86400 * 1000L;
        }
        return System.currentTimeMillis() - 30 * 86400 * 1000L;
    }

    private List<Integer> getGameRecordList() {
        try {
            if (recordList != null) {
                String[] records = recordList.split(",");
                List<Integer> list = null;
                if (records != null && records.length != 0) {
                    list = new ArrayList<>();
                    for (String s : records) {
                        list.add(Integer.parseInt(s));
                    }
                    return list;
                }
            }
        } catch (NumberFormatException e) {
            logger.error("init game record list error.", e);
        }
        return null;
    }
}
