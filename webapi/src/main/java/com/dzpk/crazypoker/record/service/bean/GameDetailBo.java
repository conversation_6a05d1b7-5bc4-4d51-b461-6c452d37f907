package com.dzpk.crazypoker.record.service.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 战绩详情主类
 */
@Setter
@Getter
public class GameDetailBo {

    /**
     * 房间id
     */
    private Integer roomId;

    /**
     * 房间
     */
    private String roomName;

    /**
     * 房间类型
     */
    private Integer roomPath;

    /**
     * 大小盲
     */
    private String bLind;
    /**
     * 结束时间
     */
    private Long endTime;
    /**
     * 游戏时间
     */
    private Integer playTime;

    /**
     * 总手数
     */
    private Integer totalHand = 0;

    /**
     * 总带入
     */
    private Integer totalBring = 0;

    /**
     * 赢利总和
     */
    private Integer totalPl = 0;
    /**
     * 俱乐部牌局服务费总和
     */
    private Integer roomChargeTotal = 0;

    /**
     *
     * 服务费总和
     */
    private String roomChargeTotalStr;


    /**
     * 亏损总和
     */
    private Integer totalLose = 0;

    /**
     * 荣耀榜
     */
    private List<HonorPlayerBo> honors;

    /**
     * 玩家数据
     */
    private List<UserGameRecordBo> userRecords;

    /**
     * 俱乐部数据
     */
    private List<ClubGameRecordBo> clubGameRecord;

    /**
     * 是否为管理员 0-否,1-是
     */
    private Boolean manager;

    /**
     * 是否为管理员
     */
    private Boolean vpOp;

    /**
     * 【竞技】模式, false否 true是
     */
    private Boolean competeModeOn;

    /**
     * 是否有彩池 false没有 true有
     */
    private Boolean jpOp;

    /**
     * 是否开启保险 false-没有,true-有
     */
    private Boolean insuranceOp;

    /**
     * 保险池
     */
    private Integer insurancePool = 0;

    /**
     * 保险盈亏列表
     */
    private List<UserGameRecordBo> insurances;

    private List<UserJpRecordBo> jpRecords;

}
