package com.dzpk.crazypoker.record.api;

import com.dzpk.crazypoker.common.constant.RespCode;
import com.dzpk.crazypoker.common.dozer.BeanUtil;
import com.dzpk.crazypoker.common.service.exception.ServiceException;
import com.dzpk.crazypoker.common.utils.JsonUtil;
import com.dzpk.crazypoker.common.web.resp.CommonRespBuilder;
import com.dzpk.crazypoker.common.web.resp.CommonResponse;
import com.dzpk.crazypoker.record.api.bean.*;
import com.dzpk.crazypoker.record.api.req.*;
import com.dzpk.crazypoker.record.service.IRecordService;
import com.dzpk.crazypoker.record.service.bean.GameDetailBo;
import com.dzpk.crazypoker.record.service.bean.GameRecordBo;
import com.dzpk.crazypoker.record.service.bean.MttGameDetailBo;
import com.dzpk.crazypoker.record.service.bean.MttGameRecordBo;
import com.dzpk.crazypoker.room.constant.ERoomPath;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Supplier;

@RestController
@Slf4j
@RequestMapping("/record/")
@Api(tags = {"战绩列表"})
public class RecordController {

    @Autowired
    private BeanUtil beanUtil;

    @Autowired
    private IRecordService recordService;

    interface GameRecordSource {
        List<GameRecordBo> getGameRecords(Set<Integer> roomPath, long sinceTime, long nextTime, int limit);
    }

    @ApiOperation(value = "战绩列表",
            notes = "战绩列表")
    @RequestMapping(value = "my",
            method = RequestMethod.POST,
            produces = "application/json;charset=UTF-8")
    @ResponseBody
    public CommonResponse<BaseRecordListVo<GameRecordVo>> my(
            @ApiIgnore @RequestAttribute("user_id") Integer userId,
            @Valid @RequestBody RecordListReq req
    ) {
        return listRecords(userId, req, (roomPath, sinceTime, nextTime, limit) -> recordService.listGameRecordByUserId(userId, roomPath, sinceTime, nextTime, req.getClubId(), limit));
    }

    @ApiOperation(value = "联盟战绩列表",
            notes = "联盟战绩列表")
    @RequestMapping(value = "tribe",
            method = RequestMethod.POST,
            produces = "application/json;charset=UTF-8")
    @ResponseBody
    public CommonResponse<BaseRecordListVo<GameRecordVo>> tribe(
            @ApiIgnore @RequestAttribute("user_id") Integer userId,
            @Valid @RequestBody RecordListReq req
    ) {
        try {
            return listRecords(0, req, (roomPath, sinceTime, nextTime, limit) -> recordService.listGameRecordByTribeId(userId, roomPath, sinceTime, nextTime, req.getTribeId(), limit));
        } catch (ServiceException e) {
            CommonResponse<BaseRecordListVo<GameRecordVo>> resp = new CommonResponse<>();
            resp.setStatus(e.getCode());
            resp.setMsg(e.getMsg());
            return resp;
        }
    }

    @ApiOperation(value = "俱乐部战绩列表",
            notes = "俱乐部战绩列表")
    @RequestMapping(value = "club",
            method = RequestMethod.POST,
            produces = "application/json;charset=UTF-8")
    @ResponseBody
    public CommonResponse<BaseRecordListVo<GameRecordVo>> club(
            @ApiIgnore @RequestAttribute("user_id") Integer userId,
            @Valid @RequestBody RecordListReq req
    ) {
        try {
            return listRecords(0, req, (roomPath, sinceTime, nextTime, limit) -> recordService.listGameRecordByClubId(userId, roomPath, sinceTime, nextTime, req.getClubId(), limit));
        } catch (ServiceException e) {
            CommonResponse<BaseRecordListVo<GameRecordVo>> resp = new CommonResponse<>();
            resp.setStatus(e.getCode());
            resp.setMsg(e.getMsg());
            return resp;
        }
    }

    /**
     *
     * @param userId >0=回填手牌手数, 0=沒有手數資訊
     * @param req
     * @param source
     * @return
     */
    private CommonResponse<BaseRecordListVo<GameRecordVo>> listRecords(int userId, RecordListReq req, GameRecordSource source) {
        BaseRecordListVo<GameRecordVo> vo = new BaseRecordListVo<>();
        vo.setNextTime(-1L);
        Long sinceTime = req.getSinceTime();
        if (sinceTime == null || sinceTime < 0) {
            // 默认查询最近7天的数据
            sinceTime = System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000L;
        }
        Long nextTime = req.getNextTime();
        if (nextTime == null) {
            // 默认最大查询时间为当前时间
            nextTime = System.currentTimeMillis();
        }
        Integer limit = req.getLimit();
        if (limit == null || limit <= 0) {
            // 默认每次查询10条数据 (上拉更新用)
            limit = 10;
        }
        int pageSize = limit;
        Integer page = req.getPage();
        if (page != null && page > 0) {
            // 分页查询, 需要所有資料
            limit = 0;
        }
        Set<Integer> roomPath = searchType2RoomPath(req.getSearchType());
        log.info("战绩记录：roomPath:{}", JsonUtil.toJson(roomPath, false));
        if (roomPath == null) {
            CommonResponse<BaseRecordListVo<GameRecordVo>> commonResponse = new CommonResponse<>();
            commonResponse.setStatus(RespCode.VIP_MISS_PARAM.getCode());
            commonResponse.setMsg(RespCode.VIP_MISS_PARAM.getDesc());
            return commonResponse;
        }
        List<GameRecordBo> bos = source.getGameRecords(roomPath, sinceTime, nextTime, limit);
        if (bos.isEmpty()) {
            vo.setList(new ArrayList<>(0));
        } else if (limit == 0) { // 分页查询
            int totalPages = (bos.size() + pageSize - 1) / pageSize;
            vo.setTotalPages(totalPages);
            vo.setTotalRows(bos.size());
            vo.setList(beanUtil.map(bos, GameRecordVo.class).subList((page - 1) * pageSize, Math.min(page * pageSize, bos.size())));
        } else { // 上拉更新
            vo.setList(beanUtil.map(bos, GameRecordVo.class));
            vo.setNextTime(bos.stream().mapToLong(e -> {
                if (e.getEndTime() == null) {
                    return -1L;
                }
                return e.getEndTime();
            }).min().orElse(-1L));
        }

        if (userId != 0) {
            // 回填手牌手数
            recordService.setHands(vo, userId);
        }

        return CommonRespBuilder.succeedBuilder().setData(vo).build();
    }

    @ApiOperation(value = "战绩详情",
            notes = "战绩详情")
    @RequestMapping(value = "detail",
            method = RequestMethod.POST,
            produces = "application/json;charset=UTF-8")
    @ResponseBody
    public CommonResponse<GameDetailVo> detail(
            @ApiIgnore @RequestAttribute("user_id") Integer userId,
            @Valid @RequestBody RecordDetailReq req
    ) {
        GameDetailBo bo = recordService.detail(userId, req.getRoomId());

        return CommonRespBuilder.succeedBuilder().setData(bo2GameDetailVo(bo)).build();
    }


    @ApiOperation(value = "战绩详情-超级统计",
            notes = "战绩详情-超级统计")
    @RequestMapping(value = "superDetail",
            method = RequestMethod.POST,
            produces = "application/json;charset=UTF-8")
    @ResponseBody
    public CommonResponse<GameDetailVo> superDetail(
            @ApiIgnore @RequestAttribute("user_id") Integer userId,
            @Valid @RequestBody RecordSuperDetailReq req
    ) {
        GameDetailBo bo = recordService.superDetail(userId, req.getRoomIds());
        return CommonRespBuilder.succeedBuilder().setData(bo2GameDetailVo(bo)).build();
    }

    @ApiOperation(value = "mtt战绩列表",
            notes = "mtt战绩列表")
    @RequestMapping(value = "mtt_list",
            method = RequestMethod.POST,
            produces = "application/json;charset=UTF-8")
    @ResponseBody
    public CommonResponse<BaseRecordListVo<MttGameRecordVo>> mttList(
            @ApiIgnore @RequestAttribute("user_id") Integer userId,
            @Valid @RequestBody MttRecordListReq req
    ) {
        BaseRecordListVo vo = new BaseRecordListVo();

        if (req.getNextTime() == null || req.getNextTime() < 0) {
            vo.setList(new ArrayList(0));
            vo.setNextTime(-1L);
        }

        List<MttGameRecordBo> bos = recordService.listMttGameRecordByUserId(userId, req.getNextTime());

        if (bos == null || bos.size() == 0) {
            vo.setList(bos);
            vo.setNextTime(-1L);
        } else {
            vo.setList(beanUtil.map(bos, MttGameRecordVo.class));
            vo.setNextTime(bos.stream().mapToLong(e -> {
                if (e.getEndTime() == null) {
                    return -1L;
                }
                return e.getEndTime();
            }).min().orElse(-1L));
        }

        return CommonRespBuilder.succeedBuilder().setData(vo).build();
    }


    @ApiOperation(value = "mtt战绩详情",
            notes = "mtt战绩详情")
    @RequestMapping(value = "mtt_detail",
            method = RequestMethod.POST,
            produces = "application/json;charset=UTF-8")
    @ResponseBody
    public CommonResponse<MttGameDetailVo> detail(
            @ApiIgnore @RequestAttribute("user_id") Integer userId,
            @Valid @RequestBody MttRecordDetailReq req
    ) {
        MttGameDetailBo bo = recordService.mttDetail(userId, req.getGameID());

        return CommonRespBuilder.succeedBuilder().setData(bo2MttGameDetailVo(bo)).build();
    }

    private MttGameDetailVo bo2MttGameDetailVo(MttGameDetailBo bo) {
        MttGameDetailVo gameDetailVo = beanUtil.map(bo, MttGameDetailVo.class);
        return gameDetailVo;
    }


    private GameDetailVo bo2GameDetailVo(GameDetailBo bo) {
        GameDetailVo gameDetailVo = beanUtil.map(bo, GameDetailVo.class);
        return gameDetailVo;
    }


    private Set<Integer> searchType2RoomPath(Integer searchType) {
        if (searchType == null) {
            return null;
        }

        int type = searchType; //解包;

        Set<Integer> roomPath = new HashSet<>();
        //:1-德州,2-Omaha,3-AOF,4-必下场

        if (type == 1) {
            roomPath.add(ERoomPath.dzpk.value());
            return roomPath;
        }
        if (type == 2) {
            roomPath.add(ERoomPath.omaha.value());
            return roomPath;
        }

        if (type == 3) {
            roomPath.add(ERoomPath.dzpkAof.value());
            roomPath.add(ERoomPath.omahaAof.value());
            roomPath.add(ERoomPath.shortCardAof.value());
            return roomPath;
        }

        if (searchType == 4) {
            roomPath.add(ERoomPath.shortCard.value());
            return roomPath;
        }

        return null;

    }

}
