package com.dzpk.crazypoker.record.repositories.model;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Field;

@Setter
@Getter
public class GameRecordPo {

    @Field("user_id")
    private Integer userId;

    @Field("room_id")
    private String roomId;

    @Field("room_path")
    private Integer roomPath;

    @Field("room_name")
    private String roomName;

    @Field("blind")
    private String blind;

    @Field("max_play_time")
    private Integer maxPlayTime;

    @Field("time")
    private Long time;

    @Field("end_time")
    private Long endTime;

    @Field("profit_lose")
    private Integer profitLose;

    @Field("owner_name")
    private String ownerName;

    @Field("owner_head")
    private String ownerHead;

    @Field("qianzhu")
    private Integer qianzhu;

    @Field("insurance")
    private Integer insurance;

    @Field("status")
    private Integer status;

    @Field("club_name")
    private String clubName;

    @Field("type")
    private Integer type;

    @Field("club_id")
    private Integer clubId;

    @Field("jackpot_real")
    private Integer jackpotReal;

    @Field("jackpot")
    private Integer jackpot;

    @Field("jackpot_pl_fee")
    private Integer jackpotPlFee;

    @Field("jackpot_pl")
    private Integer jackpotPl;


}
