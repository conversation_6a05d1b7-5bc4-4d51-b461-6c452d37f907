package com.dzpk.crazypoker.record.repositories;

import com.dzpk.crazypoker.record.repositories.model.MttGameRecordPo;

import java.util.List;

public interface IMttGameRecordDao {

    /**
     * 根据牌局id查询mtt
     * @param gameIds
     * @return
     */
    List<MttGameRecordPo> selectByGameIds(List<Integer> gameIds);


    /**
     * 根据牌局id查询
     * @param gameID
     * @return
     */
    MttGameRecordPo selectByGameId(Integer gameID);
}
