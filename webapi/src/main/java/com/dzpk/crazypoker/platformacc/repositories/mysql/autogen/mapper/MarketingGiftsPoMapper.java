package com.dzpk.crazypoker.platformacc.repositories.mysql.autogen.mapper;

import com.dzpk.crazypoker.platformacc.repositories.mysql.autogen.model.MarketingGiftsPo;
import com.dzpk.crazypoker.platformacc.repositories.mysql.autogen.model.MarketingGiftsPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MarketingGiftsPoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table marketing_gifts
     *
     * @mbg.generated
     */
    long countByExample(MarketingGiftsPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table marketing_gifts
     *
     * @mbg.generated
     */
    int deleteByExample(MarketingGiftsPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table marketing_gifts
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table marketing_gifts
     *
     * @mbg.generated
     */
    int insert(MarketingGiftsPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table marketing_gifts
     *
     * @mbg.generated
     */
    int insertSelective(MarketingGiftsPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table marketing_gifts
     *
     * @mbg.generated
     */
    List<MarketingGiftsPo> selectByExample(MarketingGiftsPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table marketing_gifts
     *
     * @mbg.generated
     */
    MarketingGiftsPo selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table marketing_gifts
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") MarketingGiftsPo record, @Param("example") MarketingGiftsPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table marketing_gifts
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") MarketingGiftsPo record, @Param("example") MarketingGiftsPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table marketing_gifts
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MarketingGiftsPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table marketing_gifts
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MarketingGiftsPo record);
}