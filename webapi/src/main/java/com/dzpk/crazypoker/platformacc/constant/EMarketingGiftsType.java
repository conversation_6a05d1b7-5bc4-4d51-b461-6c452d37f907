package com.dzpk.crazypoker.platformacc.constant;

import lombok.Getter;

@Getter
public enum EMarketingGiftsType {

    MOD_HEAD_PRESENTED(1, "头像赠送"),
    REG_ACTIVITY(2,"注册活动赠送"),
    F_RECHARGE(3,"首充活动赠送"),
    APPOINT_KD_ACTIVITY(4,"指定名单摇金豆活动赠送"),
    ;


    private int value;
    private String desc;

    EMarketingGiftsType(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static EMarketingGiftsType valueOfCode(int value) {
        for (EMarketingGiftsType temp : EMarketingGiftsType.values()) {
            if (temp.value == value) {
                return temp;
            }
        }

        return null;
    }
}
