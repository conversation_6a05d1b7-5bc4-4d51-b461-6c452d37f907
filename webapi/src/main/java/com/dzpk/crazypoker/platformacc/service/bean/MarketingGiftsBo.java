package com.dzpk.crazypoker.platformacc.service.bean;

import com.dzpk.crazypoker.platformacc.constant.EMarketingGiftsType;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Setter
@Getter
public class MarketingGiftsBo implements Serializable {

    private Integer id;

    private Integer userId;

    private EMarketingGiftsType type;

    private Integer chip;


}