package com.dzpk.crazypoker.banner.constant;

import com.dzpk.crazypoker.common.constant.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BannerProductEnum implements BaseEnum {
    /**
     * 简体中文：0
     */
    CRAZY_POKER(0, "CRAZY_POKER"),
    ;

    private int code;
    private String desc;

    public static BannerProductEnum valueOf(Integer code) {
        return BaseEnum.valueOf(BannerProductEnum.class, code);
    }
}
