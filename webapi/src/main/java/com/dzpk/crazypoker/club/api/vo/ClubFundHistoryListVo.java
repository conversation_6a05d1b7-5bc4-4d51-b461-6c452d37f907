package com.dzpk.crazypoker.club.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by jayce on 2019/3/25
 *
 * <AUTHOR>
 */
@Setter
@Getter
@ApiModel(value = "俱乐部基金-基金历史记录列表项")
public class ClubFundHistoryListVo {

    @ApiModelProperty(name = "被操作人",
            position = 0,
            notes = "被操作玩家昵称")
    private String userName;

    @ApiModelProperty(name = "操作人昵称",
            position = 1,
            notes = "操作人昵称，一般是管理员")
    private String operatorName;

    @ApiModelProperty(name = "操作时间",
            position = 2,
            notes = "该操作时间，毫秒")
    private Long time;

    @ApiModelProperty(name = "基金变动数额",
            position = 3,
            notes = "操作导致的基金变动数额")
    private Integer amount;

    @ApiModelProperty(name = "记录类型",
            position = 4,
            notes = "记录类型<br/>" +
                    "1 = 发放基金到用户可提账户中<br/>" +
                    "2 = 充值基金<br/>" +
                    "3 = 俱乐部牌局收益<br/>" +
                    "4 = 战绩流水返佣<br/>"+
                    "5 = 平台代充支付渠道充豆<br/>"+
                    "7 = 分享赚金豆返豆<br/>"+
                    "8 = 联盟牌局收益<br/>"+
                    "9 = 发放基金到用户不可提账户中<br/>"+
                    "10 = 俱乐部保险返利收益/支出<br/>"+
                    "11 = 联盟保险返利收益/支出<br/>"+
                    "12 = 俱乐部彩金返利收益<br/>"+
                    "13 = 联盟彩金返利收益<br/>"+
                    "14 = 俱乐部返佣收益<br/>"+
                    "15 = 联盟返佣收益<br/>"+
                    "16 = 发放俱乐部币<br/>"+
                    "17 = 回收俱乐部币<br/>"
    )
    private Integer type;

    @ApiModelProperty(name = "被操作人随机id",
            position = 5,
            notes = "被操作玩家的用户随机id")
    private String userId;

    @ApiModelProperty(name = "操作人随机id",
            position = 6,
            notes = "操作管理员的随机id")
    private String operatorId;

}
