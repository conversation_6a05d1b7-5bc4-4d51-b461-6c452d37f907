package com.dzpk.crazypoker.club.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by jayce on 2019/4/1
 *
 * <AUTHOR>
 */
@Setter
@Getter
@ApiModel(value = "俱乐部基金-历史记录")
public class ClubFundHistoryVo {

    @ApiModelProperty(name = "加载方向",
            position = 0,
            notes = "此次查询的加载方向")
    private Integer direction;

    @ApiModelProperty(name = "历史记录列表",
            position = 1,
            notes = "历史记录列表")
    private List<ClubFundHistoryListVo> list;

}
