package com.dzpk.crazypoker.club.msgservice.bean;

import com.dzpk.crazypoker.common.rabbitmq.constant.EMessageCode;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class MoneyMsgBo extends BaseMsgBo {
    /**
     * 参数列表
     */
    private String[] params;

    @Builder
    public MoneyMsgBo(String msgId, EMessageCode type, Integer senderId, Integer reciverId, String header, String[] params) {
        super(msgId, type, senderId, reciverId, header);
        this.params = params;
    }
}
