package com.dzpk.crazypoker.club.service.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * Created by jayce on 2019/3/6
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class ClubRecordBo {

    private Integer id;

    private Integer randomId;

    private String name;

    private String header;

    private String description;

    private String creator;

    private Integer clubMembers;

    private Integer upperLimit;

    private Date createTime;

    private Integer onlyChief;

    private String areaId;

    private Integer promoterNumbers;

    //0正常  1关闭
    private Short clubStatus;

    private Integer fund;

    private Integer rechargeFeeRate;

    private Integer initialCredit;

    private Integer creditStatus;

    private Integer totalRecharge;

    private Integer totalInsure;

    private Integer tribeStatus;

    private Integer tribeCount;

    private Integer ratio;

    private Date ratioTime;

    private Integer officailClub;

    private Short frozen;

    private Integer profit;

    private Integer transType;

    private Long modTransTypeTime;

    private Boolean payChannelFlag;

    private Boolean openRebate;

    private Double rebateRatio;

    private Long rebateSum;

    private Integer chip;

    private Integer modifyNameTimes;

    // 是否使用自定义头像 0否 1是
    private Integer useCustom;
    // 自定义头像
    private String customUrl;
}
