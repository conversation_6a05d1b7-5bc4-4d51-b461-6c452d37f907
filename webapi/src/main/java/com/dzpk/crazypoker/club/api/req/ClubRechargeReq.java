package com.dzpk.crazypoker.club.api.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * Created by jayce on 2019/3/5
 *
 * <AUTHOR>
 */
@Setter
@Getter
@ApiModel(value = "俱乐部-基金充值")
public class ClubRechargeReq {

    @ApiModelProperty(name = "俱乐部id",
            required = true,
            position = 0,
            notes = "俱乐部id<br/>" +
                    "必填")
    @NotNull(message = "clubId can't null")
    @Min(value = 1,message = "clubId can't less than 1")
    private Integer clubId;

    @ApiModelProperty(name = "充值金额",
            required = true,
            position = 1,
            notes = "充值基金数额<br/>" +
                    "必填")
    @NotNull(message = "fund can't null")
    @Min(value = 1,message = "fund can't less than 1")
    private Integer fund;

}
