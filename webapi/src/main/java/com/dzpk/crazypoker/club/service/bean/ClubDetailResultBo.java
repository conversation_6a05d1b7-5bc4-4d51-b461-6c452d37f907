package com.dzpk.crazypoker.club.service.bean;

import lombok.Getter;
import lombok.Setter;
import org.dozer.Mapping;

import java.util.Date;
import java.util.List;

/**
 * Created by jayce on 2019/2/28
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class ClubDetailResultBo {

    private int id;

    private int randomId;

    private String clubName;

    private int creatorId;

    private String clubHeader;

    private int areaId;

    private String description;

    private int type;

    private List<String> userHeads;

    private int clubMembers;

    private int membersLimit;

    private Integer transType;

    private Integer payChannelFlag;

    private Long modTransTypeTime;

    private Integer clubStatus;

    private List<ClubContactInfoBo> contactList;

    private Integer roomCount;
    private int chip;

    private Integer tribeId;
    private String tribeName;
    private Integer tribeClubStatus;
    private boolean tribeOwner;
    private Integer tribeRandomId;

    private int rechargeFeeRate;

    private Date createTime;

    private String creator;

    private long membersChipTotal;

    private Integer modifyNameTimes;

    private boolean canTransferChip;

    // 是否使用自定义头像 0否 1是
    private Integer useCustom;
    // 自定义头像
    private String customUrl;
}
