package com.dzpk.crazypoker.club.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by jayce on 2019/3/5
 *
 * <AUTHOR>
 */
@Data
public class ClubHotListVo {

    @ApiModelProperty(name = "俱乐部隐性id",
            position = 1,
            notes = "俱乐部隐性id")
    @JsonProperty("clubId")
    private Integer id;

    @ApiModelProperty(name = "俱乐部随机id",
            position = 2,
            notes = "俱乐部随机id")
    private Integer randomId;

    @ApiModelProperty(name = "俱乐部名称",
            position = 3,
            notes = "俱乐部名称")
    @JsonProperty("clubName")
    private String name;

    @ApiModelProperty(name = "俱乐部头像文件地址",
            position = 4,
            notes = "俱乐部头像文件地址")
    @JsonProperty("clubHeader")
    private String header;

    @ApiModelProperty(name = "俱乐部当前人数",
            position = 5,
            notes = "俱乐部当前人数")
    @JsonProperty("memberNum")
    private Integer clubMembers;

    @ApiModelProperty(name = "俱乐部地区id",
            position = 6,
            notes = "俱乐部地区id")
    @JsonProperty("clubAreaId")
    private String areaId;

    @ApiModelProperty(name = "俱乐部人数上限",
            position = 7,
            notes = "俱乐部人数上限")
    @JsonProperty("memberLimit")
    private Integer upperLimit;

    /**
     * 1-創建者 2-普通成员 3-非成员 4-管理员 5-待审核
     */
    @ApiModelProperty(name = "俱乐部成员类型",
            position = 8,
            notes = "1-創建者 2-普通成员 3-非成员 4-管理员 5-待审核")
    private int type;

    @ApiModelProperty(name = "联盟id",
            position = 9,
            notes = "联盟id")
    private int tribeId;

    @ApiModelProperty(name = "联盟名称",
            position = 10,
            notes = "联盟名称")
    private String tribeName;

    @ApiModelProperty(name = "联盟头像",
            position = 11,
            notes = "联盟头像")
    private String tribeHead;

    private Integer useCustom;

    private String customUrl;

    private Integer tribeUseCustom;

    private String tribeCustomUrl;
}
