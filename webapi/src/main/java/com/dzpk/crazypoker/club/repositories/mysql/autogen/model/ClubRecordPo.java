package com.dzpk.crazypoker.club.repositories.mysql.autogen.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ClubRecordPo implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.random_id
     *
     * @mbg.generated
     */
    private Integer randomId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.name
     *
     * @mbg.generated
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.header
     *
     * @mbg.generated
     */
    private String header;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.description
     *
     * @mbg.generated
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.creator
     *
     * @mbg.generated
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.club_status
     *
     * @mbg.generated
     */
    private Short clubStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.club_members
     *
     * @mbg.generated
     */
    private Integer clubMembers;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.upper_limit
     *
     * @mbg.generated
     */
    private Integer upperLimit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.only_chief
     *
     * @mbg.generated
     */
    private Integer onlyChief;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.area_id
     *
     * @mbg.generated
     */
    private String areaId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.fund
     *
     * @mbg.generated
     */
    private Integer fund;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.initial_credit
     *
     * @mbg.generated
     */
    private Integer initialCredit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.credit_status
     *
     * @mbg.generated
     */
    private Integer creditStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.total_insure
     *
     * @mbg.generated
     */
    private Integer totalInsure;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.tribe_status
     *
     * @mbg.generated
     */
    private Integer tribeStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.tribe_count
     *
     * @mbg.generated
     */
    private Integer tribeCount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.ratio
     *
     * @mbg.generated
     */
    private Integer ratio;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.ratio_time
     *
     * @mbg.generated
     */
    private Date ratioTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.officail_club
     *
     * @mbg.generated
     */
    private Integer officailClub;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.frozen
     *
     * @mbg.generated
     */
    private Short frozen;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.profit
     *
     * @mbg.generated
     */
    private Integer profit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.trans_type
     *
     * @mbg.generated
     */
    private Integer transType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.mod_trans_type_time
     *
     * @mbg.generated
     */
    private Long modTransTypeTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.pay_channel_flag
     *
     * @mbg.generated
     */
    private Boolean payChannelFlag;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.open_rebate
     *
     * @mbg.generated
     */
    private Boolean openRebate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.rebate_ratio
     *
     * @mbg.generated
     */
    private Double rebateRatio;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.rebate_sum
     *
     * @mbg.generated
     */
    private Long rebateSum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.room_profit
     *
     * @mbg.generated
     */
    private BigDecimal roomProfit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.club_room_charge
     *
     * @mbg.generated
     */
    private Integer clubRoomCharge;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.club_room_insure_total
     *
     * @mbg.generated
     */
    private Integer clubRoomInsureTotal;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.tribe_profit
     *
     * @mbg.generated
     */
    private Integer tribeProfit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.chip
     *
     * @mbg.generated
     */
    private Integer chip;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.recharge_fee_rate
     *
     * @mbg.generated
     */
    private Integer rechargeFeeRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.insurance_loss_limit
     *
     * @mbg.generated
     */
    private Integer insuranceLossLimit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.win_rebate_rate
     *
     * @mbg.generated
     */
    private Double winRebateRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.lose_rebate_rate
     *
     * @mbg.generated
     */
    private Double loseRebateRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.insurance_fee_rate
     *
     * @mbg.generated
     */
    private Double insuranceFeeRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.status_update_time
     *
     * @mbg.generated
     */
    private Date statusUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.insurance_ratio
     *
     * @mbg.generated
     */
    private Double insuranceRatio;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column club_record.MODIFY_NAME_TIMES
     *
     * @mbg.generated
     */
    private Integer modifyNameTimes;

    private Integer useCustom;

    private String customUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table club_record
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.id
     *
     * @return the value of club_record.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.id
     *
     * @param id the value for club_record.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.random_id
     *
     * @return the value of club_record.random_id
     *
     * @mbg.generated
     */
    public Integer getRandomId() {
        return randomId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.random_id
     *
     * @param randomId the value for club_record.random_id
     *
     * @mbg.generated
     */
    public void setRandomId(Integer randomId) {
        this.randomId = randomId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.name
     *
     * @return the value of club_record.name
     *
     * @mbg.generated
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.name
     *
     * @param name the value for club_record.name
     *
     * @mbg.generated
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.header
     *
     * @return the value of club_record.header
     *
     * @mbg.generated
     */
    public String getHeader() {
        return header;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.header
     *
     * @param header the value for club_record.header
     *
     * @mbg.generated
     */
    public void setHeader(String header) {
        this.header = header == null ? null : header.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.description
     *
     * @return the value of club_record.description
     *
     * @mbg.generated
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.description
     *
     * @param description the value for club_record.description
     *
     * @mbg.generated
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.creator
     *
     * @return the value of club_record.creator
     *
     * @mbg.generated
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.creator
     *
     * @param creator the value for club_record.creator
     *
     * @mbg.generated
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.club_status
     *
     * @return the value of club_record.club_status
     *
     * @mbg.generated
     */
    public Short getClubStatus() {
        return clubStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.club_status
     *
     * @param clubStatus the value for club_record.club_status
     *
     * @mbg.generated
     */
    public void setClubStatus(Short clubStatus) {
        this.clubStatus = clubStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.club_members
     *
     * @return the value of club_record.club_members
     *
     * @mbg.generated
     */
    public Integer getClubMembers() {
        return clubMembers;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.club_members
     *
     * @param clubMembers the value for club_record.club_members
     *
     * @mbg.generated
     */
    public void setClubMembers(Integer clubMembers) {
        this.clubMembers = clubMembers;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.upper_limit
     *
     * @return the value of club_record.upper_limit
     *
     * @mbg.generated
     */
    public Integer getUpperLimit() {
        return upperLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.upper_limit
     *
     * @param upperLimit the value for club_record.upper_limit
     *
     * @mbg.generated
     */
    public void setUpperLimit(Integer upperLimit) {
        this.upperLimit = upperLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.create_time
     *
     * @return the value of club_record.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.create_time
     *
     * @param createTime the value for club_record.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.only_chief
     *
     * @return the value of club_record.only_chief
     *
     * @mbg.generated
     */
    public Integer getOnlyChief() {
        return onlyChief;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.only_chief
     *
     * @param onlyChief the value for club_record.only_chief
     *
     * @mbg.generated
     */
    public void setOnlyChief(Integer onlyChief) {
        this.onlyChief = onlyChief;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.area_id
     *
     * @return the value of club_record.area_id
     *
     * @mbg.generated
     */
    public String getAreaId() {
        return areaId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.area_id
     *
     * @param areaId the value for club_record.area_id
     *
     * @mbg.generated
     */
    public void setAreaId(String areaId) {
        this.areaId = areaId == null ? null : areaId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.fund
     *
     * @return the value of club_record.fund
     *
     * @mbg.generated
     */
    public Integer getFund() {
        return fund;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.fund
     *
     * @param fund the value for club_record.fund
     *
     * @mbg.generated
     */
    public void setFund(Integer fund) {
        this.fund = fund;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.initial_credit
     *
     * @return the value of club_record.initial_credit
     *
     * @mbg.generated
     */
    public Integer getInitialCredit() {
        return initialCredit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.initial_credit
     *
     * @param initialCredit the value for club_record.initial_credit
     *
     * @mbg.generated
     */
    public void setInitialCredit(Integer initialCredit) {
        this.initialCredit = initialCredit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.credit_status
     *
     * @return the value of club_record.credit_status
     *
     * @mbg.generated
     */
    public Integer getCreditStatus() {
        return creditStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.credit_status
     *
     * @param creditStatus the value for club_record.credit_status
     *
     * @mbg.generated
     */
    public void setCreditStatus(Integer creditStatus) {
        this.creditStatus = creditStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.total_insure
     *
     * @return the value of club_record.total_insure
     *
     * @mbg.generated
     */
    public Integer getTotalInsure() {
        return totalInsure;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.total_insure
     *
     * @param totalInsure the value for club_record.total_insure
     *
     * @mbg.generated
     */
    public void setTotalInsure(Integer totalInsure) {
        this.totalInsure = totalInsure;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.tribe_status
     *
     * @return the value of club_record.tribe_status
     *
     * @mbg.generated
     */
    public Integer getTribeStatus() {
        return tribeStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.tribe_status
     *
     * @param tribeStatus the value for club_record.tribe_status
     *
     * @mbg.generated
     */
    public void setTribeStatus(Integer tribeStatus) {
        this.tribeStatus = tribeStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.tribe_count
     *
     * @return the value of club_record.tribe_count
     *
     * @mbg.generated
     */
    public Integer getTribeCount() {
        return tribeCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.tribe_count
     *
     * @param tribeCount the value for club_record.tribe_count
     *
     * @mbg.generated
     */
    public void setTribeCount(Integer tribeCount) {
        this.tribeCount = tribeCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.ratio
     *
     * @return the value of club_record.ratio
     *
     * @mbg.generated
     */
    public Integer getRatio() {
        return ratio;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.ratio
     *
     * @param ratio the value for club_record.ratio
     *
     * @mbg.generated
     */
    public void setRatio(Integer ratio) {
        this.ratio = ratio;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.ratio_time
     *
     * @return the value of club_record.ratio_time
     *
     * @mbg.generated
     */
    public Date getRatioTime() {
        return ratioTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.ratio_time
     *
     * @param ratioTime the value for club_record.ratio_time
     *
     * @mbg.generated
     */
    public void setRatioTime(Date ratioTime) {
        this.ratioTime = ratioTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.officail_club
     *
     * @return the value of club_record.officail_club
     *
     * @mbg.generated
     */
    public Integer getOfficailClub() {
        return officailClub;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.officail_club
     *
     * @param officailClub the value for club_record.officail_club
     *
     * @mbg.generated
     */
    public void setOfficailClub(Integer officailClub) {
        this.officailClub = officailClub;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.frozen
     *
     * @return the value of club_record.frozen
     *
     * @mbg.generated
     */
    public Short getFrozen() {
        return frozen;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.frozen
     *
     * @param frozen the value for club_record.frozen
     *
     * @mbg.generated
     */
    public void setFrozen(Short frozen) {
        this.frozen = frozen;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.profit
     *
     * @return the value of club_record.profit
     *
     * @mbg.generated
     */
    public Integer getProfit() {
        return profit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.profit
     *
     * @param profit the value for club_record.profit
     *
     * @mbg.generated
     */
    public void setProfit(Integer profit) {
        this.profit = profit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.trans_type
     *
     * @return the value of club_record.trans_type
     *
     * @mbg.generated
     */
    public Integer getTransType() {
        return transType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.trans_type
     *
     * @param transType the value for club_record.trans_type
     *
     * @mbg.generated
     */
    public void setTransType(Integer transType) {
        this.transType = transType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.mod_trans_type_time
     *
     * @return the value of club_record.mod_trans_type_time
     *
     * @mbg.generated
     */
    public Long getModTransTypeTime() {
        return modTransTypeTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.mod_trans_type_time
     *
     * @param modTransTypeTime the value for club_record.mod_trans_type_time
     *
     * @mbg.generated
     */
    public void setModTransTypeTime(Long modTransTypeTime) {
        this.modTransTypeTime = modTransTypeTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.pay_channel_flag
     *
     * @return the value of club_record.pay_channel_flag
     *
     * @mbg.generated
     */
    public Boolean getPayChannelFlag() {
        return payChannelFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.pay_channel_flag
     *
     * @param payChannelFlag the value for club_record.pay_channel_flag
     *
     * @mbg.generated
     */
    public void setPayChannelFlag(Boolean payChannelFlag) {
        this.payChannelFlag = payChannelFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.open_rebate
     *
     * @return the value of club_record.open_rebate
     *
     * @mbg.generated
     */
    public Boolean getOpenRebate() {
        return openRebate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.open_rebate
     *
     * @param openRebate the value for club_record.open_rebate
     *
     * @mbg.generated
     */
    public void setOpenRebate(Boolean openRebate) {
        this.openRebate = openRebate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.rebate_ratio
     *
     * @return the value of club_record.rebate_ratio
     *
     * @mbg.generated
     */
    public Double getRebateRatio() {
        return rebateRatio;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.rebate_ratio
     *
     * @param rebateRatio the value for club_record.rebate_ratio
     *
     * @mbg.generated
     */
    public void setRebateRatio(Double rebateRatio) {
        this.rebateRatio = rebateRatio;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.rebate_sum
     *
     * @return the value of club_record.rebate_sum
     *
     * @mbg.generated
     */
    public Long getRebateSum() {
        return rebateSum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.rebate_sum
     *
     * @param rebateSum the value for club_record.rebate_sum
     *
     * @mbg.generated
     */
    public void setRebateSum(Long rebateSum) {
        this.rebateSum = rebateSum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.room_profit
     *
     * @return the value of club_record.room_profit
     *
     * @mbg.generated
     */
    public BigDecimal getRoomProfit() {
        return roomProfit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.room_profit
     *
     * @param roomProfit the value for club_record.room_profit
     *
     * @mbg.generated
     */
    public void setRoomProfit(BigDecimal roomProfit) {
        this.roomProfit = roomProfit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.club_room_charge
     *
     * @return the value of club_record.club_room_charge
     *
     * @mbg.generated
     */
    public Integer getClubRoomCharge() {
        return clubRoomCharge;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.club_room_charge
     *
     * @param clubRoomCharge the value for club_record.club_room_charge
     *
     * @mbg.generated
     */
    public void setClubRoomCharge(Integer clubRoomCharge) {
        this.clubRoomCharge = clubRoomCharge;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.club_room_insure_total
     *
     * @return the value of club_record.club_room_insure_total
     *
     * @mbg.generated
     */
    public Integer getClubRoomInsureTotal() {
        return clubRoomInsureTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.club_room_insure_total
     *
     * @param clubRoomInsureTotal the value for club_record.club_room_insure_total
     *
     * @mbg.generated
     */
    public void setClubRoomInsureTotal(Integer clubRoomInsureTotal) {
        this.clubRoomInsureTotal = clubRoomInsureTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.tribe_profit
     *
     * @return the value of club_record.tribe_profit
     *
     * @mbg.generated
     */
    public Integer getTribeProfit() {
        return tribeProfit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.tribe_profit
     *
     * @param tribeProfit the value for club_record.tribe_profit
     *
     * @mbg.generated
     */
    public void setTribeProfit(Integer tribeProfit) {
        this.tribeProfit = tribeProfit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.chip
     *
     * @return the value of club_record.chip
     *
     * @mbg.generated
     */
    public Integer getChip() {
        return chip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.chip
     *
     * @param chip the value for club_record.chip
     *
     * @mbg.generated
     */
    public void setChip(Integer chip) {
        this.chip = chip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.recharge_fee_rate
     *
     * @return the value of club_record.recharge_fee_rate
     *
     * @mbg.generated
     */
    public Integer getRechargeFeeRate() {
        return rechargeFeeRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.recharge_fee_rate
     *
     * @param rechargeFeeRate the value for club_record.recharge_fee_rate
     *
     * @mbg.generated
     */
    public void setRechargeFeeRate(Integer rechargeFeeRate) {
        this.rechargeFeeRate = rechargeFeeRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.insurance_loss_limit
     *
     * @return the value of club_record.insurance_loss_limit
     *
     * @mbg.generated
     */
    public Integer getInsuranceLossLimit() {
        return insuranceLossLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.insurance_loss_limit
     *
     * @param insuranceLossLimit the value for club_record.insurance_loss_limit
     *
     * @mbg.generated
     */
    public void setInsuranceLossLimit(Integer insuranceLossLimit) {
        this.insuranceLossLimit = insuranceLossLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.win_rebate_rate
     *
     * @return the value of club_record.win_rebate_rate
     *
     * @mbg.generated
     */
    public Double getWinRebateRate() {
        return winRebateRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.win_rebate_rate
     *
     * @param winRebateRate the value for club_record.win_rebate_rate
     *
     * @mbg.generated
     */
    public void setWinRebateRate(Double winRebateRate) {
        this.winRebateRate = winRebateRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.lose_rebate_rate
     *
     * @return the value of club_record.lose_rebate_rate
     *
     * @mbg.generated
     */
    public Double getLoseRebateRate() {
        return loseRebateRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.lose_rebate_rate
     *
     * @param loseRebateRate the value for club_record.lose_rebate_rate
     *
     * @mbg.generated
     */
    public void setLoseRebateRate(Double loseRebateRate) {
        this.loseRebateRate = loseRebateRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.insurance_fee_rate
     *
     * @return the value of club_record.insurance_fee_rate
     *
     * @mbg.generated
     */
    public Double getInsuranceFeeRate() {
        return insuranceFeeRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.insurance_fee_rate
     *
     * @param insuranceFeeRate the value for club_record.insurance_fee_rate
     *
     * @mbg.generated
     */
    public void setInsuranceFeeRate(Double insuranceFeeRate) {
        this.insuranceFeeRate = insuranceFeeRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.status_update_time
     *
     * @return the value of club_record.status_update_time
     *
     * @mbg.generated
     */
    public Date getStatusUpdateTime() {
        return statusUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.status_update_time
     *
     * @param statusUpdateTime the value for club_record.status_update_time
     *
     * @mbg.generated
     */
    public void setStatusUpdateTime(Date statusUpdateTime) {
        this.statusUpdateTime = statusUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.insurance_ratio
     *
     * @return the value of club_record.insurance_ratio
     *
     * @mbg.generated
     */
    public Double getInsuranceRatio() {
        return insuranceRatio;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.insurance_ratio
     *
     * @param insuranceRatio the value for club_record.insurance_ratio
     *
     * @mbg.generated
     */
    public void setInsuranceRatio(Double insuranceRatio) {
        this.insuranceRatio = insuranceRatio;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column club_record.MODIFY_NAME_TIMES
     *
     * @return the value of club_record.MODIFY_NAME_TIMES
     *
     * @mbg.generated
     */
    public Integer getModifyNameTimes() {
        return modifyNameTimes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column club_record.MODIFY_NAME_TIMES
     *
     * @param modifyNameTimes the value for club_record.MODIFY_NAME_TIMES
     *
     * @mbg.generated
     */
    public void setModifyNameTimes(Integer modifyNameTimes) {
        this.modifyNameTimes = modifyNameTimes;
    }

    public Integer getUseCustom() {
        return useCustom;
    }

    public void setUseCustom(Integer useCustom) {
        this.useCustom = useCustom;
    }

    public String getCustomUrl() {
        return customUrl;
    }

    public void setCustomUrl(String customUrl) {
        this.customUrl = customUrl;
    }
}