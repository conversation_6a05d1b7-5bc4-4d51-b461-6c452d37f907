package com.dzpk.crazypoker.club.repositories.mysql.autogen.model;

import java.io.Serializable;
import java.util.Date;

public class ClubMessageRequest implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column message_club_request.msg_id
     *
     * @mbg.generated
     */
    private String msgId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column message_club_request.club_id
     *
     * @mbg.generated
     */
    private String clubId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column message_club_request.user_id
     *
     * @mbg.generated
     */
    private String userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column message_club_request.param
     *
     * @mbg.generated
     */
    private String param;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column message_club_request.type
     *
     * @mbg.generated
     */
    private Integer type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column message_club_request.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table message_club_request
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column message_club_request.msg_id
     *
     * @return the value of message_club_request.msg_id
     *
     * @mbg.generated
     */
    public String getMsgId() {
        return msgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column message_club_request.msg_id
     *
     * @param msgId the value for message_club_request.msg_id
     *
     * @mbg.generated
     */
    public void setMsgId(String msgId) {
        this.msgId = msgId == null ? null : msgId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column message_club_request.club_id
     *
     * @return the value of message_club_request.club_id
     *
     * @mbg.generated
     */
    public String getClubId() {
        return clubId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column message_club_request.club_id
     *
     * @param clubId the value for message_club_request.club_id
     *
     * @mbg.generated
     */
    public void setClubId(String clubId) {
        this.clubId = clubId == null ? null : clubId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column message_club_request.user_id
     *
     * @return the value of message_club_request.user_id
     *
     * @mbg.generated
     */
    public String getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column message_club_request.user_id
     *
     * @param userId the value for message_club_request.user_id
     *
     * @mbg.generated
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column message_club_request.param
     *
     * @return the value of message_club_request.param
     *
     * @mbg.generated
     */
    public String getParam() {
        return param;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column message_club_request.param
     *
     * @param param the value for message_club_request.param
     *
     * @mbg.generated
     */
    public void setParam(String param) {
        this.param = param == null ? null : param.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column message_club_request.type
     *
     * @return the value of message_club_request.type
     *
     * @mbg.generated
     */
    public Integer getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column message_club_request.type
     *
     * @param type the value for message_club_request.type
     *
     * @mbg.generated
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column message_club_request.create_time
     *
     * @return the value of message_club_request.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column message_club_request.create_time
     *
     * @param createTime the value for message_club_request.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}