package com.dzpk.crazypoker.club.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by jayce on 2019/4/16
 *
 * <AUTHOR>
 */
@Setter
@Getter
@ApiModel(value = "俱乐部-俱乐部联系方式详情")
public class ClubContactInfoVo {

    @ApiModelProperty(name = "联系方式",
            position = 0,
            notes = "联系方式" +
                    "1 = 微信<br/>" +
                    "2 = telegram<br/>" +
                    "3 = skype<br/>" +
                    "4 = sugram")
    private Integer type;

    @ApiModelProperty(name = "联系方式对应账号",
            position = 1,
            notes = "联系方式对应账号")
    private String content;

}
