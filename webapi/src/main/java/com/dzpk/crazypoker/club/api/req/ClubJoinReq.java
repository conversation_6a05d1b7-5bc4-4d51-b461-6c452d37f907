package com.dzpk.crazypoker.club.api.req;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel(value = "俱乐部-申请加入俱乐部")
public class ClubJoinReq extends CommClubReq {

    @ApiModelProperty(name = "俱乐部id",
            required = true,
            position = 0,
            notes = "俱乐部id<br/>" +
                    "必填")
    @NotNull(message = "俱乐部id不能为空!")
    @NotEmpty(message = "俱乐部id不能为空!")
    private String clubId;

    @ApiModelProperty(name = "玩家ip",
            position = 1,
            notes = "玩家ip")
    private String ip;

    @ApiModelProperty(name = "玩家裝置",
            position = 2,
            notes = "玩家裝置")
    private String device;

    @ApiModelProperty(name = "目標玩家id",
            position = 3,
            notes = "目標玩家id")
    private String targetId;

    @ApiModelProperty(
            name = "是否强制",
            position = 4,
            notes = "是否强制")
    private Integer force;


}
