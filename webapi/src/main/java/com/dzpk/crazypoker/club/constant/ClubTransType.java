package com.dzpk.crazypoker.club.constant;

import lombok.Getter;

/**
 * 俱乐部交易模式
 */

@Getter
public enum ClubTransType {

    /**
     * 俱乐部
     */
    CLUB(0),
    PLATFORM(1),
    ;


    ClubTransType(int value) {
        this.value = value;
    }

    private int value;

    public static ClubTransType valueOfCode(int value) {
        for (ClubTransType e : ClubTransType.values()) {
            if (e.value == value) {
                return e;
            }
        }

        return null;
    }


}
