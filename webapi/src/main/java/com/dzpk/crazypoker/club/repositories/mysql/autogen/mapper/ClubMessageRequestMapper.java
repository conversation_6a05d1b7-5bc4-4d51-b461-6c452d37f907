package com.dzpk.crazypoker.club.repositories.mysql.autogen.mapper;

import com.dzpk.crazypoker.club.repositories.mysql.autogen.model.ClubMessageRequest;
import com.dzpk.crazypoker.club.repositories.mysql.autogen.model.ClubMessageRequestExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ClubMessageRequestMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_club_request
     *
     * @mbg.generated
     */
    long countByExample(ClubMessageRequestExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_club_request
     *
     * @mbg.generated
     */
    int deleteByExample(ClubMessageRequestExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_club_request
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String msgId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_club_request
     *
     * @mbg.generated
     */
    int insert(ClubMessageRequest record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_club_request
     *
     * @mbg.generated
     */
    int insertSelective(ClubMessageRequest record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_club_request
     *
     * @mbg.generated
     */
    List<ClubMessageRequest> selectByExample(ClubMessageRequestExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_club_request
     *
     * @mbg.generated
     */
    ClubMessageRequest selectByPrimaryKey(String msgId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_club_request
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") ClubMessageRequest record, @Param("example") ClubMessageRequestExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_club_request
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") ClubMessageRequest record, @Param("example") ClubMessageRequestExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_club_request
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(ClubMessageRequest record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_club_request
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(ClubMessageRequest record);
}