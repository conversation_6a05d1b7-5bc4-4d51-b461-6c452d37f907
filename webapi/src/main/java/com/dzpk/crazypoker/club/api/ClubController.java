package com.dzpk.crazypoker.club.api;

import com.alibaba.fastjson.JSONObject;
import com.dzpk.crazypoker.appmessage.send.AppBusinessMessageSender;
import com.dzpk.crazypoker.appmessage.send.bean.LeaveClub;
import com.dzpk.crazypoker.club.api.req.*;
import com.dzpk.crazypoker.club.api.vo.*;
import com.dzpk.crazypoker.club.service.IClubService;
import com.dzpk.crazypoker.club.service.bean.*;
import com.dzpk.crazypoker.common.constant.RespCode;
import com.dzpk.crazypoker.common.dozer.BeanUtil;
import com.dzpk.crazypoker.common.service.InvokedResult;
import com.dzpk.crazypoker.common.utils.JsonUtil;
import com.dzpk.crazypoker.common.web.controller.AbstractController;
import com.dzpk.crazypoker.common.web.resp.CommonPagedResponse;
import com.dzpk.crazypoker.common.web.resp.CommonRespBuilder;
import com.dzpk.crazypoker.common.web.resp.CommonResponse;
import com.dzpk.crazypoker.common.web.config.CmsProperties;
import com.dzpk.crazypoker.oss.service.OssService;
import com.google.gson.Gson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by jayce on 2019/2/25
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(
        value = "/club/",
        method = RequestMethod.POST)
@Api(tags = {"俱乐部Api"})
public class ClubController extends AbstractController {

    @Autowired
    private CmsProperties cmsProperties;


    @Resource
    AppBusinessMessageSender appBusinessMessageSender;

    @Autowired
    private OssService ossService;



    private Map<String, String> postHttpClient(String path, String jsonParam) {
        String baseUrl = cmsProperties.getUrl();

        String url = baseUrl + path;
        RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        headers.setContentType(type);
        HttpEntity<String> entity = new HttpEntity<>(jsonParam, headers);
        String response = restTemplate.postForObject(url, entity, String.class);
        // use gson to parse the response to a map
        Gson gson = new Gson();
        Map<String, String> map = gson.fromJson(response, Map.class);

        return map;
    }

    /**
     * 调用cms接口
     * @param uri
     * @param params
     * @return
     */
    private JSONObject cmsJsonPostApi(String uri, JSONObject params) {
        String baseUrl = cmsProperties.getUrl();
        String url = baseUrl + uri;
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        headers.setContentType(type);
        HttpEntity<String> entity = new HttpEntity<>(params.toString(), headers);
        String response = restTemplate.postForObject(url, entity, String.class);
        return JSONObject.parseObject(response);
    }

    @Autowired
    private IClubService clubService;

    @Autowired
    private BeanUtil beanUtil;

    @ApiOperation(value = "创建俱乐部",
            notes = "状态码<br/>" +
                    " 0 = 成功，如果需要数据可以从data字段解析<br/>" +
                    "999 = 失败，异常情况等<br/>" +
                    "1200 = 关键参数错误<br/>" +
                    "1202 = 俱乐部申请待处理<br/>" +
                    "1203 = 已加入俱乐部<br/>" +
                    "1204 = 俱乐部重名<br/>")
    @ResponseBody
    @RequestMapping("create")
    public CommonResponse<Object> create(@RequestAttribute("user_id") int userId,
                                         @Validated @RequestBody ClubCreationReq request,
                                         BindingResult paramValidResult) {
        //常规校验
        handleValidatedError(paramValidResult);

        CommonResponse<Object> response = new CommonResponse<>();

        ClubCreationBo clubCreationBo = this.beanUtil.map(request, ClubCreationBo.class);

        InvokedResult result = clubService.create(userId, clubCreationBo);
        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());

        return response;
    }

    @ApiOperation(value = "加入俱乐部",
            notes = "状态码<br/>" +
                    " 0 = 成功，如果需要数据可以从data字段解析<br/>" +
                    "999 = 失败，异常情况等<br/>" +
                    "1202 = 俱乐部申请待处理<br/>" +
                    "1203 = 已加入俱乐部<br/>" +
                    "1205 = 目标俱乐部不存在<br/>" +
                    "1206 = 俱乐部人数上限<br/>")
    @ResponseBody
    @RequestMapping("join")
    public CommonResponse<Object> applyJoin(@RequestAttribute("user_id") int userId,
                                            @Validated @RequestBody ClubJoinReq request,
                                            BindingResult paramValidResult) {
        //常规校验
        handleValidatedError(paramValidResult);
        
        CommonResponse<Object> response = new CommonResponse<>();

        InvokedResult result = clubService.join(userId, request.getClubId(), request.getDevice(), 1);//最后参数用于标识客户端调用
        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());

        return response;
    }


    @ApiOperation(value = "退出俱乐部",
            notes = "状态码<br/>" +
                    " 0 = 成功，如果需要数据可以从data字段解析<br/>"
             )
    @ResponseBody
    @RequestMapping("exit")
    public CommonResponse<Object> applyExit(@RequestAttribute("user_id") int userId,
                                            @Validated @RequestBody ClubJoinReq request,
                                            BindingResult paramValidResult) {
        //常规校验
        handleValidatedError(paramValidResult);
        JSONObject params = new JSONObject();
        params.put("userId", userId);
        params.put("clubRandomId", request.getClubId());
        params.put("force", request.getForce());
        JSONObject cmsResponse = cmsJsonPostApi("exit/member", params);
        CommonResponse<Object> response = new CommonResponse<>();
        response.setStatus(cmsResponse.getInteger("status"));
        response.setMsg(cmsResponse.getString("message"));
        return  response;
    }


    @ApiOperation(value = "刪除俱乐部",
            notes = "状态码<br/>" +
                    " 0 = 成功，如果需要数据可以从data字段解析<br/>"
    )
    @ResponseBody
    @RequestMapping("delete")
    public CommonResponse<Object> applyDelete(@RequestAttribute("user_id") int userId,
                                            @Validated @RequestBody ClubJoinReq request,
                                            BindingResult paramValidResult) {
        //常规校验
        handleValidatedError(paramValidResult);


        Map<String, String> data = new HashMap<>();
        data.put("user_id", String.valueOf(userId));
        data.put("club_id", String.valueOf(request.getClubId()));

        // Convert the data map to JSON
        String jsonParam = JsonUtil.toJson(data, false);
        Map<String, String> resp = postHttpClient("game/deleteClub", jsonParam);
        // if resp.error, set Msg to resp.error and status to 999
        // else set status to 0

        CommonResponse<Object> response = new CommonResponse<>();
        if (resp.containsKey("error")) {
            response.setStatus(999);
            response.setMsg(resp.get("error"));
        } else {
            response.setStatus(0);
            response.setMsg("ok");
        }


//        response.setStatus(0);
//        response.setMsg("ok");
        return  response;
    }

    @ApiOperation(value = "成員踢出俱乐部",
            notes = "状态码<br/>" +
                    " 0 = 成功，如果需要数据可以从data字段解析<br/>"
    )
    @ResponseBody
    @RequestMapping("kick")
    public CommonResponse<Object> applyKick(@RequestAttribute("user_id") int userId,
                                              @Validated @RequestBody ClubJoinReq request,
                                              BindingResult paramValidResult) {
        //常规校验
        handleValidatedError(paramValidResult);
        JSONObject params = new JSONObject();
        params.put("userId", request.getTargetId());
        params.put("clubId", request.getClubId());
        JSONObject cmsResponse = cmsJsonPostApi("kick/member", params);
        CommonResponse<Object> response = new CommonResponse<>();
        response.setStatus(cmsResponse.getInteger("status"));
        response.setMsg(cmsResponse.getString("message"));
        return  response;
    }


    @ApiOperation(value = "退出聯盟",
            notes = "状态码<br/>" +
                    " 0 = 成功，如果需要数据可以从data字段解析<br/>"
    )
    @ResponseBody
    @RequestMapping("exitTribe")
    public CommonResponse<Object> applyExitTribe(@RequestAttribute("user_id") int userId,
                                            @Validated @RequestBody ClubJoinReq request,
                                            BindingResult paramValidResult) {
        //常规校验
        handleValidatedError(paramValidResult);

        Map<String, String> data = new HashMap<>();
        data.put("user_id", String.valueOf(userId));
        data.put("club_id", String.valueOf(request.getClubId()));

        // Convert the data map to JSON
        String jsonParam = JsonUtil.toJson(data, false);
        postHttpClient("game/exitTribe", jsonParam);

        CommonResponse<Object> response = new CommonResponse<>();
        response.setStatus(0);
        response.setMsg("ok");


        return  response;
    }


    @ApiOperation(value = "刪除聯盟",
            notes = "状态码<br/>" +
                    " 0 = 成功，如果需要数据可以从data字段解析<br/>"
    )
    @ResponseBody
    @RequestMapping("deleteTribe")
    public CommonResponse<Object> applyDeleteTribe(@RequestAttribute("user_id") int userId,
                                              @Validated @RequestBody ClubJoinReq request,
                                              BindingResult paramValidResult) {
        //常规校验
        handleValidatedError(paramValidResult);


        Map<String, String> data = new HashMap<>();
        data.put("user_id", String.valueOf(userId));
        data.put("club_id", String.valueOf(request.getClubId()));

        // Convert the data map to JSON
        String jsonParam = JsonUtil.toJson(data, false);
        Map<String, String> resp = postHttpClient("game/deleteTribe", jsonParam);
        // if resp.error, set Msg to resp.error and status to 999
        // else set status to 0

        CommonResponse<Object> response = new CommonResponse<>();
        if (resp.containsKey("error")) {
            response.setStatus(999);
            response.setMsg(resp.get("error"));
        } else {
            response.setStatus(0);
            response.setMsg("ok");
        }


//        response.setStatus(0);
//        response.setMsg("ok");
        return  response;
    }


    @ApiOperation(value = "成員踢出俱乐部",
            notes = "状态码<br/>" +
                    " 0 = 成功，如果需要数据可以从data字段解析<br/>"
    )
    @ResponseBody
    @RequestMapping("kickClub")
    public CommonResponse<Object> applyKickClub(@RequestAttribute("user_id") int userId,
                                            @Validated @RequestBody ClubJoinReq request,
                                            BindingResult paramValidResult) {
        //常规校验
        handleValidatedError(paramValidResult);


        Map<String, String> data = new HashMap<>();
        data.put("user_id", String.valueOf(userId));
        data.put("club_id", String.valueOf(request.getClubId()));
        data.put("target_id", String.valueOf(request.getTargetId()));

        // Convert the data map to JSON
        String jsonParam = JsonUtil.toJson(data, false);
        Map<String, String> resp = postHttpClient("game/kickClub", jsonParam);
        // if resp.error, set Msg to resp.error and status to 999
        // else set status to 0

        CommonResponse<Object> response = new CommonResponse<>();
        if (resp.containsKey("error")) {
            response.setStatus(999);
            response.setMsg(resp.get("error"));
        } else {
            response.setStatus(0);
            response.setMsg("ok");
        }


//        response.setStatus(0);
//        response.setMsg("ok");
        return  response;
    }

    @ResponseBody
    @RequestMapping("check")
    public CommonResponse<Object> check(@RequestAttribute("user_id") int userId,
                                        @Validated @RequestBody ClubCheckReq request,
                                        BindingResult paramValidResult) {
        //常规校验
        handleValidatedError(paramValidResult);
        CommonResponse<Object> response = new CommonResponse<>();
        InvokedResult result = clubService.check(userId,
                Integer.parseInt(request.getClubId()),
                Integer.parseInt(request.getUserId()),
                Integer.parseInt(request.getCheckStatus()));
        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());
        return response;
    }

    @ResponseBody
    @RequestMapping("setAdmin")
    public CommonResponse<Object> setAdmin(@RequestAttribute("user_id") int userId,
                                           @Validated @RequestBody ClubAdminReq request,
                                           BindingResult paramValidResult) {
        //常规校验
        handleValidatedError(paramValidResult);
        CommonResponse<Object> response = new CommonResponse<>();
        InvokedResult result = clubService.setAdmin(Integer.parseInt(request.getClubId()), Integer.parseInt(request.getUserId()), Integer.parseInt(request.getType()));
        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());
        return response;
    }

    @ApiOperation(value = "俱乐部列表",
            notes = "状态码<br/>" +
                    " 0 = 成功，如果需要数据可以从data字段解析<br/>" +
                    "999 = 失败，异常情况等")
    @ResponseBody
    @RequestMapping("list")
    public CommonResponse<ClubListDetailVo> getJoinClubList(@RequestAttribute("user_id") int userId) {

        CommonResponse<ClubListDetailVo> response = new CommonResponse<>();

        InvokedResult<ClubListDetailBo> result = clubService.getJoinClubList(userId);
        if (null != result.getData()) {
            response.setData(this.beanUtil.map(result.getData(), ClubListDetailVo.class));
        }
        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());

        return response;
    }

    @ApiOperation(value = "搜索俱乐部",
            notes = "原先的地区查找和关键字查找合并为一个接口<br/>" +
                    "状态码<br/>" +
                    " 0 = 成功，如果需要数据可以从data字段解析<br/>" +
                    "999 = 失败，异常情况等<br/>" +
                    "1207 = 俱乐部查找无结果(此情况不解析data字段数据)")
    @ResponseBody
    @RequestMapping("search")
    public CommonResponse<List<ClubSearchVo>> searchById(@RequestAttribute("user_id") int userId,
                                                         @Validated @RequestBody ClubSearchReq request,
                                                         BindingResult paramValidResult) {
        //常规校验
        handleValidatedError(paramValidResult);

        CommonResponse<List<ClubSearchVo>> response = new CommonResponse<>();
        ClubSearchBo clubSearchBo = this.beanUtil.map(request, ClubSearchBo.class);
        InvokedResult<List<ClubSearchResultBo>> result = clubService.search(userId, clubSearchBo);
        if (null != result.getData()) {
            response.setData(this.beanUtil.map(result.getData(), ClubSearchVo.class));
        }
        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());

        return response;
    }

    @ApiOperation(value = "查看俱乐部详情",
            notes = "状态码<br/>" +
                    " 0 = 成功，如果需要数据可以从data字段解析<br/>" +
                    "999 = 失败，异常情况等<br/>" +
                    "1205 = 俱乐部不存在")
    @ResponseBody
    @RequestMapping("view")
    public CommonResponse<ClubDetailVo> getClubDetail(@RequestAttribute("user_id") int userId,
                                                      @Validated @RequestBody ClubDetailReq request,
                                                      BindingResult paramValidResult) {
        //常规校验
        // handleValidatedError(paramValidResult);

        CommonResponse<ClubDetailVo> response = new CommonResponse<>();
        InvokedResult<ClubDetailResultBo> result = clubService.getClubDetail(userId, request.getClubId());

        if (null != result.getData()) {
            response.setData(this.beanUtil.map(result.getData(), ClubDetailVo.class));
        }


        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());

        return response;
    }

    @ApiOperation(value = "查看个人俱乐部积分",
            notes = "状态码<br/>" +
                    " 0 = 成功，如果需要数据可以从data字段解析<br/>" +
                    "999 = 失败，异常情况等<br/>" +
                    "1205 = 俱乐部不存在")
    @ResponseBody
    @RequestMapping("querySelfIntegral")
    public CommonResponse<Integer> querySelfIntegral(@RequestAttribute("user_id") int userId,
                                                     @Validated @RequestBody ClubDetailReq request,
                                                     BindingResult paramValidResult) {
        //常规校验
        CommonResponse<Integer> response = new CommonResponse<>();
        InvokedResult<Integer> result = clubService.queryIntegral(userId, request.getClubId());
        response.setData(result.getData());
        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());
        return response;
    }


    @ApiOperation(value = "个人操作申请或者扣减积分",
            notes = "状态码<br/>" +
                    " 0 = 成功，如果需要数据可以从data字段解析<br/>" +
                    "999 = 失败，异常情况等<br/>" +
                    "1205 = 俱乐部不存在")
    @ResponseBody
    @RequestMapping("selfApplyIntegral")
    public CommonResponse<Integer> selfApplyIntegral(@RequestAttribute("user_id") int userId,
                                                     @Validated @RequestBody ClubIntegralReq request,
                                                     BindingResult paramValidResult) {
        //常规校验
        CommonResponse<Integer> response = new CommonResponse<>();
        log.info("操作个人积分:{}", JsonUtil.toJson(request, true));
        if (request.getType() == 1 || request.getType() == 2) {
            if (request.getIntegral() <= 0) {
                response.setData(request.getIntegral());
                response.setStatus(3000);
                response.setMsg("积分不合法，必须大于0");
                return response;
            }
            InvokedResult<Integer> result = clubService.selfApplyIntegral(userId, request.getClubId(), request.getIntegral(), request.getType());
            response.setData(request.getIntegral());
            response.setStatus(result.getCode());
            response.setMsg(result.getMsg());
        } else {
            response.setData(request.getIntegral());
            response.setStatus(3000);
            response.setMsg("积分类型不合法");
            return response;
        }
        return response;
    }

    @ResponseBody
    @RequestMapping("handle")
    public CommonResponse handleMsgRecord(@RequestAttribute(value = "user_id") int userId,
                                          @Validated @RequestBody MessageHandleReq request,
                                          BindingResult paramValidResult) {
        CommonResponse response = new CommonResponse<>();
        if (clubService.handleMessage(request.getMessageId(), request.getHandleType()) == 1) {
            log.info("handleMsgRecord===>msgId=" + request.getMessageId());
            response.setStatus(RespCode.SUCCEED.getCode());
            response.setMsg(RespCode.SUCCEED.getDesc());

        } else {
            response.setStatus(RespCode.FAILED.getCode());
            response.setMsg(RespCode.FAILED.getDesc());
        }
        return response;
    }

    @ResponseBody
    @RequestMapping("grantGold")
    public CommonResponse<Object> grantGold(@RequestAttribute("user_id") int userId,
                                                @Validated @RequestBody ClubGoldReq request,
                                                BindingResult paramValidResult) {
        //常规校验
        handleValidatedError(paramValidResult);

        CommonResponse<Object> response = new CommonResponse<>();
        InvokedResult<Object> result = clubService.grantGold(userId, request.getClubId(), request.getUserId(), request.getAmount());
        response.setMsg(result.getMsg());
        response.setStatus(result.getCode());

        return response;
    }

    @ResponseBody
    @RequestMapping("grantIntegral")
    public CommonResponse<Object> grantIntegral(@RequestAttribute("user_id") int userId,
                                         @Validated @RequestBody ClubIntegralReq request,
                                         BindingResult paramValidResult) {
        //常规校验
        handleValidatedError(paramValidResult);

        CommonResponse<Object> response = new CommonResponse<>();
        InvokedResult result = clubService.grantIntegral(userId, request.getClubId(), request.getUserId(), request.getIntegral());
        response.setMsg(result.getMsg());
        response.setStatus(result.getCode());

        return response;
    }

    @ResponseBody
    @RequestMapping("retrieveIntegral")
    public CommonResponse<Object> retrieveIntegral(@RequestAttribute("user_id") int userId,
                                                @Validated @RequestBody ClubIntegralReq request,
                                                BindingResult paramValidResult) {
        //常规校验
        handleValidatedError(paramValidResult);

        CommonResponse<Object> response = new CommonResponse<>();
        InvokedResult result = clubService.retrieveIntegral(userId, request.getClubId(), request.getUserId(), request.getIntegral());
        response.setMsg(result.getMsg());
        response.setStatus(result.getCode());

        return response;
    }


    @ApiOperation(value = "修改信息",
            notes = "状态码<br/>" +
                    " 0 = 成功，如果需要数据可以从data字段解析<br/>" +
                    "999 = 失败，异常情况等<br/>" +
                    "1201 = 没有俱乐部操作权限<br/>" +
                    "1204 = 俱乐部重名<br/>" +
                    "1205 = 目标俱乐部不存在")
    @ResponseBody
    @RequestMapping("modify")
    public CommonResponse<Object> modify(@RequestAttribute("user_id") int userId,
                                         @Validated @RequestBody ClubModifyReq request,
                                         BindingResult paramValidResult) {

        //常规校验
        handleValidatedError(paramValidResult);

        CommonResponse<Object> response = new CommonResponse<>();
        InvokedResult result = clubService.modify(userId, request.getClubId(), request.getContent(), request.getType());
        response.setMsg(result.getMsg());
        response.setStatus(result.getCode());

        return response;
    }

    @ApiOperation(value = "推荐俱乐部列表",
            notes = "状态码<br/>" +
                    " 0 = 成功，如果需要数据可以从data字段解析<br/>" +
                    "999 = 失败，异常情况等<br/>")
    @ResponseBody
    @RequestMapping("hotClubs")
    public CommonResponse<ClubHotVo> getHotClubs(@RequestAttribute("user_id") int userId,
                                                 @Validated @RequestBody ClubHotReq request,
                                                 BindingResult paramValidResult) {

        //常规校验
        handleValidatedError(paramValidResult);

        CommonResponse<ClubHotVo> response = new CommonResponse<>();
        InvokedResult<ClubHotBo> result = clubService.getHotClubs(userId, request.getReqType());
        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());
        if (null != result.getData()) {
            response.setData(this.beanUtil.map(result.getData(), ClubHotVo.class));
        }

        return response;
    }

    @ApiOperation(value = "俱乐部成员列表",
            notes = "状态码<br/>" +
                    " 0 = 成功，如果需要数据可以从data字段解析<br/>" +
                    "999 = 失败，异常情况等<br/>" +
                    "1201 = 没有俱乐部操作权限<br/>" +
                    "1205 = 目标俱乐部不存在<br/>")
    @ResponseBody
    @RequestMapping("mlist")
    public CommonResponse<List<ClubMemberListVo>> getClubMembersList(@RequestAttribute("user_id") int userId,
                                                                     @Validated @RequestBody ClubMemberListReq request,
                                                                     BindingResult paramValidResult) {
        //常规校验
        handleValidatedError(paramValidResult);

        CommonResponse<List<ClubMemberListVo>> response = new CommonResponse<>();
        InvokedResult<List<ClubMemberResultBo>> result = clubService.getClubMembersList(userId, request.getClubId());
        if (null != result.getData()) {
            response.setData(this.beanUtil.map(result.getData(), ClubMemberListVo.class));
        }
        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());

        return response;
    }

    @ApiOperation(value = "修改俱乐部联系方式",
            notes = "状态码<br/>" +
                    " 0 = 成功，如果需要数据可以从data字段解析<br/>" +
                    "999 = 失败，异常情况等<br/>" +
                    "1201 = 没有俱乐部操作权限")
    @ResponseBody
    @RequestMapping("modify-contact")
    public CommonResponse<Object> modifyContact(@RequestAttribute("user_id") int userId,
                                                @Validated @RequestBody ClubModifyContactReq request,
                                                BindingResult paramValidResult) {

        //常规校验
        handleValidatedError(paramValidResult);

        CommonResponse<Object> response = new CommonResponse<>();
        InvokedResult result = clubService.modifyContact(userId, request.getClubId(), request.getFirstType(), request.getFirstContact(), request.getSecondType(), request.getSecondContact());
        response.setMsg(result.getMsg());
        response.setStatus(result.getCode());

        return response;
    }


    @ApiOperation(value = "交易模式修改",
            notes = "交易模式修改")
    @ResponseBody
    @RequestMapping("mod_trans")
    public CommonResponse<Object> modifyTransType(@RequestAttribute("user_id") int userId
            , @Validated @RequestBody ModTransReq req) {

        clubService.modifyTransType(userId, req.getTransType());
        return CommonRespBuilder.succeedBuilder().build();
    }

    /**
     * 发放
     */
    @ResponseBody
    @RequestMapping("grant")
    public CommonResponse<Object> grant(@RequestAttribute("user_id") int userId,@RequestBody ClubChipReq request) {
        CommonResponse<Object> response = new CommonResponse<>();
        InvokedResult<Object> result = clubService.grant(userId,request);
        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());
        return response;
    }

    /**
     * 回收
     */
    @ResponseBody
    @RequestMapping("retrieve")
    public CommonResponse<Object> retrieve(@RequestAttribute("user_id") int userId,@RequestBody ClubChipReq request) {
        CommonResponse<Object> response = new CommonResponse<>();
        InvokedResult<Object> result = clubService.retrieve(userId,request);
        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());
        return response;
    }

    /**
     * 用戶申請发放/回收
     */
    @ResponseBody
    @RequestMapping("chip/request")
    public CommonResponse<Object> userChipRequest(@RequestAttribute("user_id") int userId,@RequestBody ClubChipReq request) {
        CommonResponse<Object> response = new CommonResponse<>();
        InvokedResult<Object> result = clubService.userChipRequest(userId,request);
        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());
        return response;
    }

    /**
     * 處理用戶申請
     */
    @ResponseBody
    @RequestMapping("chip/request/handle")
    public CommonResponse<Object> handleUserChipRequest(@RequestAttribute("user_id") int userId,@RequestBody MessageHandleReq request) {
        CommonResponse<Object> response = new CommonResponse<>();
        InvokedResult<Object> result = clubService.handleUserChipRequest(userId,request.getMessageId(),request.getHandleType());
        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());
        return response;
    }

    /**
     * 用戶間轉幣
     */
    @ResponseBody
    @RequestMapping("chip/p2p/transfer")
    public CommonResponse<Object> transferUserChip(@RequestAttribute("user_id") int userId,@RequestBody ClubChipReq request) {
        CommonResponse<Object> response = new CommonResponse<>();
        InvokedResult<Object> result = clubService.transferUserChip(userId,request);
        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());
        return response;
    }

    /**
     * 用戶間轉幣記錄
     */
    @ResponseBody
    @RequestMapping("chip/p2p/log")
    public CommonPagedResponse<ClubChipTransferLog> transferUserChipLog(@RequestAttribute("user_id") int userId,@RequestBody ClubChipReq request) {
        CommonPagedResponse<ClubChipTransferLog> response = new CommonPagedResponse<>();
        InvokedResult<List<ClubChipTransferLog>> result = clubService.transferUserChipLog(userId,request);
        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());
        response.setData(result.getData(), ClubChipTransferLog::getId);
        return response;
    }

    /**
     * 用戶最近轉幣對象
     */
    @ResponseBody
    @RequestMapping("chip/p2p/recipients")
    public CommonResponse<List<ClubChipTransferRecipient>> transferUserChipRecipients(@RequestAttribute("user_id") int userId,@RequestBody ClubChipReq request) {
        CommonResponse<List<ClubChipTransferRecipient>> response = new CommonResponse<>();
        InvokedResult<List<ClubChipTransferRecipient>> result = clubService.transferUserChipRecipients(userId,request);
        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());
        response.setData(result.getData());
        return response;
    }

    @ResponseBody
    @RequestMapping("chipLog")
    public CommonPagedResponse<ClubChipLog> chipLog(@RequestAttribute("user_id") int userId, @RequestBody ClubChipReq request) {
        CommonPagedResponse<ClubChipLog> response = new CommonPagedResponse<>();
        response.setStatus(RespCode.SUCCEED.getCode());
        response.setMsg(RespCode.SUCCEED.getDesc());
        InvokedResult<List<ClubChipLog>> result = clubService.chipLog(userId,request);
        if (result.getData() == null) {
            response.setStatus(result.getCode());
            response.setMsg(result.getMsg());
        } else {
            response.setData(result.getData(), log -> (long) log.getId());
        }
        return response;
    }

    /**
     * 俱樂部金幣记录 (俱樂部主錢包)
     * @param userId
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("goldLog")
    public CommonPagedResponse<ClubGoldLog> goldLog(@RequestAttribute("user_id") int userId, @RequestBody ClubGoldLogReq request) {
        CommonPagedResponse<ClubGoldLog> response = new CommonPagedResponse<>();
        response.setStatus(RespCode.SUCCEED.getCode());
        response.setMsg(RespCode.SUCCEED.getDesc());
        InvokedResult<List<ClubGoldLog>> result = clubService.goldLog(userId,request);
        if (result.getData() == null) {
            response.setStatus(result.getCode());
            response.setMsg(result.getMsg());
        } else {
            response.setData(result.getData(), ClubGoldLog::getId);
        }
        return response;
    }

    @ResponseBody
    @RequestMapping("userChip")
    public CommonResponse<Object> findUserChip(@RequestAttribute("user_id") int userId,@RequestBody ClubChipReq request) {
        CommonResponse<Object> response = new CommonResponse<>();
        InvokedResult result = clubService.findUserChip(userId, request);
        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());
        response.setData(result.getData());
        return response;
    }
    @ResponseBody
    @RequestMapping("userMyChip")
    public CommonResponse<Object> findUserMyChip(@RequestAttribute("user_id") int userId,@RequestBody ClubChipReq request) {
        CommonResponse<Object> response = new CommonResponse<>();
        InvokedResult result = clubService.findUserMyChip(userId, request);
        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());
        response.setData(result.getData());
        return response;
    }
    @ResponseBody
    @RequestMapping("clubChip")
    public CommonResponse<Object> findClubChip(@RequestAttribute("user_id") int userId,@RequestBody ClubChipReq request) {
        CommonResponse<Object> response = new CommonResponse<>();
        InvokedResult result = clubService.findClubChip(userId, request);
        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());
        response.setData(result.getData());
        return response;
    }

    @ApiOperation(value = "设置俱乐部默认头像",
            notes = "设置俱乐部默认头像")
    @RequestMapping("setClubHead")
    public CommonResponse<ClubDetailVo> setClubHead(@RequestAttribute("user_id") Integer userId,
                                                       @RequestBody ClubHeadReq request) {
        log.info("-----设置俱乐部默认头像-----req:{}", JSONObject.toJSONString(request));
        CommonResponse<ClubDetailVo> response = new CommonResponse<>();
        if (userId == null || request == null || request.getClubId() == null || StringUtils.isBlank(request.getHead())) {
            return CommonRespBuilder.builder().setStatus(RespCode.PARAM_INVALID.getCode()).setMsg(RespCode.PARAM_INVALID.getDesc()).build();
        }
        try {
            // 修改俱乐部头像
            InvokedResult result = clubService.setClubHead(userId, request.getClubId(), request.getHead());
            response.setMsg(result.getMsg());
            log.info("-----设置俱乐部默认头像-----msg:{}", result.getMsg());
            response.setStatus(result.getCode());
            if (result.getData() != null) {
                response.setData(this.beanUtil.map(result.getData(), ClubDetailVo.class));
                response.setStatus(RespCode.SUCCEED.getCode());
                response.setMsg(RespCode.SUCCEED.getDesc());
                return response;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("设置俱乐部默认头像错误--- e: {}", e.getMessage());
        }
        response.setStatus(RespCode.FAILED.getCode());
        response.setMsg(RespCode.FAILED.getDesc());

        return response;
    }

    @ApiOperation(value = "修改俱乐部头像",
            notes = "修改俱乐部头像")
    @RequestMapping("updateClubHead")
    public CommonResponse<ClubDetailVo> updateClubHead(@RequestAttribute("user_id") Integer userId,
                                                       @RequestParam("clubId") String clubId,
                                                       @RequestParam("file") MultipartFile file) {
        log.info("-----修改俱乐部头像-----clubId: {}", clubId);
        CommonResponse<ClubDetailVo> response = new CommonResponse<>();
        if (userId == null || file.isEmpty()) {
            return CommonRespBuilder.builder().setStatus(RespCode.PARAM_INVALID.getCode()).setMsg(RespCode.PARAM_INVALID.getDesc()).build();
        }
        try {
            log.info("-----上传图片-----");
            String path = ossService.uploadImage(file, null, null, true, userId, 4);
            log.info("-----上传图片完成-----path: {}", path);
            if (StringUtils.isBlank(path)) {
                return CommonRespBuilder.builder().setStatus(RespCode.FAILED.getCode()).setMsg(RespCode.FAILED.getDesc()).build();
            }
            // 修改俱乐部头像
            InvokedResult result = clubService.updateClubCustomHead(userId, Integer.parseInt(clubId), path);
            response.setMsg(result.getMsg());
            log.info("-----修改俱乐部头像-----msg:{}", result.getMsg());
            response.setStatus(result.getCode());
            if (result.getData() != null) {
                response.setData(this.beanUtil.map(result.getData(), ClubDetailVo.class));
                response.setStatus(RespCode.SUCCEED.getCode());
                response.setMsg(RespCode.SUCCEED.getDesc());
                return response;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("修改俱乐部自定义头像错误--- e: {}", e.getMessage());
        }
        response.setStatus(RespCode.FAILED.getCode());
        response.setMsg(RespCode.FAILED.getDesc());

        return response;
    }
}
