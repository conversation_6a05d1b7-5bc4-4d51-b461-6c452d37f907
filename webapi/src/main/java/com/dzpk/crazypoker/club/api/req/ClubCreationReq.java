package com.dzpk.crazypoker.club.api.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel(value = "俱乐部-新建俱乐部")
public class ClubCreationReq extends CommClubReq {

    @ApiModelProperty(name = "俱乐部名称",
            position = 0,
            required = true,
            notes = "俱乐部名称<br/>" +
                    "必填")
    @NotNull(message = "name required!")
    @NotEmpty(message = "name can't not empty")
    @Length(max = 60, message = "name should not be greater than 60 characters!")
    private String clubName;

    @ApiModelProperty(name = "俱乐部头像文件地址",
            position = 1,
            notes = "俱乐部头像文件地址<br/>" +
                    "目前版本可以传 -1的字符串")
    @Length(max = 60, message = "header should not be greater than 60 characters!")
    private String clubHead;

    @ApiModelProperty(name = "俱乐部简介",
            position = 2,
            notes = "俱乐部简介<br/>" +
                    "非必填")
    @JsonProperty("desc")
    @Length(max = 300, message = "header should not be greater than 300 characters!")
    private String desc;

    @ApiModelProperty(name = "俱乐部地区id",
            position = 3,
            notes = "俱乐部地区id<br/>" +
                    "非必填")
    @Length(max = 60, message = "area_id should not be greater than 60 characters!")
    private String areaId;

    //以下五个字段至少需要填一项
    @ApiModelProperty(name = "联系人手机号码",
            position = 4,
            notes = "联系人手机号码<br/>" +
                    "非必填")
    @Length(max = 40)
    private String phone;//手机号码

    @ApiModelProperty(name = "联系人手机号码",
            position = 5,
            notes = "联系人手机号码<br/>" +
                    "非必填")
    @Length(max = 60)
    private String wechat;//微信号码

    @ApiModelProperty(name = "telegram联系",
            position = 6,
            notes = "telegram联系<br/>" +
                    "非必填")
    @Length(max = 60)
    private String telegram;

    @ApiModelProperty(name = "电子邮箱地址",
            position = 7,
            notes = "电子邮箱地址<br/>" +
                    "非必填")
    @Length(max = 60)
    private String email;//邮箱

    @ApiModelProperty(name = "留言",
            position = 8,
            notes = "留言<br/>" +
                    "非必填")
    @JsonProperty("message")
    @Length(max = 60)
    private String messageStr;//留言

    @ApiModelProperty(name = "俱乐部是否使用自定义头像 0否 1是",
            position = 9,
            notes = "俱乐部是否使用自定义头像 0否 1是<")
    private Integer useCustom;

    @ApiModelProperty(name = "俱乐部自定义头像",
            position = 10,
            notes = "俱乐部自定义头像")
    private String customUrl;

}
