package com.dzpk.crazypoker.gamehistory.repositories.mongo;

import com.dzpk.crazypoker.common.mongo.factory.IMongoInstanceFactory;
import com.dzpk.crazypoker.gamehistory.repositories.ICardScoreDao;
import com.dzpk.crazypoker.gamehistory.repositories.model.CardScore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class CardScoreDaoImpl implements ICardScoreDao {

    private static final String COLLECTION_NAME = "card_score";

    @Autowired
    private IMongoInstanceFactory iMongoInstanceFactory;

    private ReactiveMongoTemplate getReactiveMongoTemplate() {
        return iMongoInstanceFactory.defaultInstance().getTemplate();
    }

    @Override
    public List<CardScore> selectCardScore(int roomId, int version) {
        ReactiveMongoTemplate rTemplate = getReactiveMongoTemplate();
        Query query = new Query();
        query.addCriteria(Criteria.where("room_id").is(roomId).and("version").is(version));
        return rTemplate.find(query, CardScore.class, COLLECTION_NAME).collectList().block();
    }
}
