package com.dzpk.crazypoker.announcement.api.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 公告管理
 */
@Getter
@Setter
public class AnnouncementManagement {

    @ApiModelProperty(name = "公告图片",
            required = true,
            position = 0,
            notes = "公告图片")
    @JsonProperty("banner")
    private String banner;

    @ApiModelProperty(name = "排序",
            required = true,
            position = 1,
            notes = "排序")
    @JsonProperty("sortIndex")
    private Integer sortIndex;

    @ApiModelProperty(name = "维护状态",
            required = true,
            position = 2,
            notes = "状态：0停用,1启用,2删除")
    @JsonProperty("status")
    private Integer status;

    @ApiModelProperty(name = "id",
            required = true,
            position = 3,
            notes = "id")
    @JsonProperty("id")
    private Long id;

    @ApiModelProperty(name = "公告名称",
            required = true,
            position = 4,
            notes = "公告名称")
    @JsonProperty("announcementName")
    private String announcementName;

    @ApiModelProperty(name = "公告内容",
            required = true,
            position = 5,
            notes = "公告内容")
    @JsonProperty("content")
    private String content;

    @ApiModelProperty(name = "内容类型",
            required = true,
            position = 6,
            notes = "内容类型")
    @JsonProperty("contentType")
    private Integer contentType;

    @ApiModelProperty(name = "链接",
            required = true,
            position = 7,
            notes = "链接")
    @JsonProperty("linkUrl")
    private String linkUrl;

    @ApiModelProperty(name = "创建时间",
            required = true,
            position = 8,
            notes = "创建时间")
    @JsonProperty("createTime")
    private Date createdAt;

    @ApiModelProperty(name = "更新时间",
            required = true,
            position = 9,
            notes = "更新时间")
    @JsonProperty("createTime")
    private Date updatedAt;

    @ApiModelProperty(name = "操作人ID",
            required = true,
            position = 10,
            notes = "操作人ID")
    @JsonProperty("operatorId")
    private Long operatorId;

    /** 内容类型 1：文本内容 */
    public static final int TYPE_CONTENT = 1;
    /** 内容类型 2：链接 */
    public static final int TYPE_LINK = 2;
}
