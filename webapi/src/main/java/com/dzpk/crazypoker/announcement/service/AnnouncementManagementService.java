package com.dzpk.crazypoker.announcement.service;

import com.dzpk.crazypoker.announcement.api.resp.AnnouncementManagementResp;

import java.util.List;

/**
 * 公告管理
 *
 * <AUTHOR>
 */
public interface AnnouncementManagementService {

    /**
     * 查询公告列表
     * @param page
     * @param size
     */
    List<AnnouncementManagementResp> configList(Integer page, Integer size);

    /**
     * 根据id查询公告内容
     * @param id
     */
    AnnouncementManagementResp queryAnnouncementManagementById(Long id);
}
