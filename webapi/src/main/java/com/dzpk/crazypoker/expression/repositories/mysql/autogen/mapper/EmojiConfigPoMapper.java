package com.dzpk.crazypoker.expression.repositories.mysql.autogen.mapper;

import com.dzpk.crazypoker.expression.repositories.mysql.autogen.model.EmojiConfigPo;
import com.dzpk.crazypoker.expression.repositories.mysql.autogen.model.EmojiConfigPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface EmojiConfigPoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table emoji_config
     *
     * @mbg.generated
     */
    long countByExample(EmojiConfigPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table emoji_config
     *
     * @mbg.generated
     */
    int deleteByExample(EmojiConfigPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table emoji_config
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table emoji_config
     *
     * @mbg.generated
     */
    int insert(EmojiConfigPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table emoji_config
     *
     * @mbg.generated
     */
    int insertSelective(EmojiConfigPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table emoji_config
     *
     * @mbg.generated
     */
    List<EmojiConfigPo> selectByExample(EmojiConfigPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table emoji_config
     *
     * @mbg.generated
     */
    EmojiConfigPo selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table emoji_config
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") EmojiConfigPo record, @Param("example") EmojiConfigPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table emoji_config
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") EmojiConfigPo record, @Param("example") EmojiConfigPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table emoji_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(EmojiConfigPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table emoji_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(EmojiConfigPo record);
}