package com.dzpk.crazypoker.expression.service.impl;

import com.dzpk.crazypoker.common.constant.RespCode;
import com.dzpk.crazypoker.common.service.InvokedResult;
import com.dzpk.crazypoker.common.service.exception.ServiceException;
import com.dzpk.crazypoker.expression.repositories.mysql.autogen.mapper.EmojiConfigPoMapper;
import com.dzpk.crazypoker.expression.repositories.mysql.autogen.model.EmojiConfigPo;
import com.dzpk.crazypoker.expression.repositories.mysql.autogen.model.EmojiConfigPoExample;
import com.dzpk.crazypoker.expression.service.IExpressionService;
import com.dzpk.crazypoker.expression.service.bo.UseEmojiBo;
import com.dzpk.crazypoker.platformacc.constant.EPlatAcc;
import com.dzpk.crazypoker.platformacc.constant.EPlatAccChangeType;
import com.dzpk.crazypoker.platformacc.constant.EPlatChipSource;
import com.dzpk.crazypoker.platformacc.service.IPlatformAccountService;
import com.dzpk.crazypoker.platformacc.service.bean.PlatformAccountBo;
import com.dzpk.crazypoker.wallet.api.constant.EChipConsume;
import com.dzpk.crazypoker.wallet.api.constant.EChipSource;
import com.dzpk.crazypoker.wallet.api.constant.ETransType;
import com.dzpk.crazypoker.wallet.service.IUserAccountService;
import com.dzpk.crazypoker.wallet.service.IWalletService;
import com.dzpk.crazypoker.wallet.service.bean.ConsumeChipBo;
import com.dzpk.crazypoker.wallet.service.bean.UserAccountBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by jayce on 2019/5/11
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ExpressionServiceImpl implements IExpressionService {

    @Autowired
    private EmojiConfigPoMapper emojiConfigDao;

    @Autowired
    private IWalletService walletService;

    @Autowired
    private IPlatformAccountService platformAccountService;

    @Autowired
    private IUserAccountService userAccountService;

    //emoji缓存配置表 5分钟加载一次
    private final Map<String,Integer> emojiConfigMap = new HashMap<>();
    //上次加载时间
    private long lastInitConfigTime = -1;
    //配置失效时间
    private final long INVALID_TIME = 5 * 60 * 1000;

    @Override
    public InvokedResult<UseEmojiBo> useEmoji(Integer userId, String emojiKey) {
        InvokedResult<UseEmojiBo> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        UseEmojiBo data = new UseEmojiBo();

        try {
            //1/检查key是否有对应表情
            boolean status = checkCanUse(emojiKey);
            if (!status) {
                return result;
            }

            //2、扣除相应费用，客户端的费用只做显示用，实际扣费使用后台配置
            walletService.consumeChip(userId, ConsumeChipBo.builder()
                    .chip(-this.emojiConfigMap.get(emojiKey))
                    .consume(EChipConsume.USE_EMOJI)
                    .description("表情花费=>" + emojiKey)
                    .opId(userId)
                    .source(EChipSource.API)
                    .build(), ETransType.NULL.getCode());

            //3、将扣费计入系统仓
            PlatformAccountBo platformAccountBo = new PlatformAccountBo();
            platformAccountBo.setChip(this.emojiConfigMap.get(emojiKey));
            platformAccountBo.setDesction(EPlatAccChangeType.USE_EMOJI.getDesc());
            platformAccountBo.setExternalId(String.valueOf(userId));
            platformAccountBo.setOpId(userId);
            platformAccountBo.setPlatformacc(EPlatAcc.SYS_ACCOUNT);
            platformAccountBo.setSource(EPlatChipSource.API);
            platformAccountBo.setType(EPlatAccChangeType.USE_EMOJI);
            this.platformAccountService.changeChip(platformAccountBo);

            //4、返回用户剩余金豆
            UserAccountBo userInfo = this.userAccountService.findById(userId);
            if (null != userInfo) {
                data.setChip(userInfo.getChip() + userInfo.getNotExtractChip());
            }
        } catch (ServiceException e) {
            log.error(e.getMessage(),e);
            result.setCode(e.getCode());
            result.setMsg(e.getMsg());
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return result;
        }
        result.setData(data);
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());

        return result;
    }

    private boolean checkCanUse(String emojiKey){
        if(this.lastInitConfigTime == -1 || (System.currentTimeMillis() - this.lastInitConfigTime) > INVALID_TIME){
            initConfig();
            this.lastInitConfigTime = System.currentTimeMillis();
        }
        return this.emojiConfigMap.containsKey(emojiKey);
    }

    private void initConfig(){
        //加载配置
        EmojiConfigPoExample sel = new EmojiConfigPoExample();
        sel.or().andStatusEqualTo(1);
        List<EmojiConfigPo> emojiConfigList = this.emojiConfigDao.selectByExample(sel);
        if(null != emojiConfigList && !emojiConfigList.isEmpty()){
            emojiConfigList.forEach(d -> emojiConfigMap.put(d.getName(),d.getAmount()));
        }
    }
}
