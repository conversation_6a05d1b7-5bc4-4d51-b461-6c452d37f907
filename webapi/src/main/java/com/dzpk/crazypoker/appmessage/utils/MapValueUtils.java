package com.dzpk.crazypoker.appmessage.utils;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.Map;

/**
 * MapValueUtils
 * <AUTHOR>
 */
public class MapValueUtils {

    /**
     * 获取Map中指定键对应的String值。
     *
     * @param map Map对象
     * @param key 键
     * @return 键对应的值，如果键不存在或值为null，则返回null
     */
    public static String getString(Map<String, Object> map, String key) {
        return (String) get(map, key);
    }

    /**
     * 获取Map中指定键对应的Long值。
     *
     * @param map Map对象
     * @param key 键
     * @return 键对应的值，如果键不存在或值不是Long类型，则返回null
     */
    public static Long getLong(Map<String, Object> map, String key) {
        return getNumber(map, key, Long.class);
    }

    /**
     * 获取Map中指定键对应的BigInteger值。
     *
     * @param map Map对象
     * @param key 键
     * @return 键对应的值，如果键不存在或值不是BigInteger类型，则返回null
     */
    public static BigInteger getBigInteger(Map<String, Object> map, String key) {
        // 先getLong 如果不为空则转换为BigInteger
        Long value = getLong(map, key);
        if (value != null) {
            return BigInteger.valueOf(value);
        }
        return getNumber(map, key, BigInteger.class);
    }

    /**
     * 获取Map中指定键对应的Integer值。
     *
     * @param map Map对象
     * @param key 键
     * @return 键对应的值，如果键不存在或值不是Integer类型，则返回null
     */
    public static Integer getInteger(Map<String, Object> map, String key) {
        return getNumber(map, key, Integer.class);
    }

    /**
     * 获取Map中指定键对应的Boolean值。
     *
     * @param map Map对象
     * @param key 键
     * @return 键对应的值，如果键不存在或值不是Boolean类型，则返回null
     */
    public static Boolean getBoolean(Map<String, Object> map, String key) {
        return (Boolean) get(map, key);
    }

    /**
     * 获取Map中指定键对应的Double值。
     *
     * @param map Map对象
     * @param key 键
     * @return 键对应的值，如果键不存在或值不是Double类型，则返回null
     */
    public static Double getDouble(Map<String, Object> map, String key) {
        return getNumber(map, key, Double.class);
    }

    /**
     * 获取Map中指定键对应的Float值。
     *
     * @param map Map对象
     * @param key 键
     * @return 键对应的值，如果键不存在或值不是Float类型，则返回null
     */
    public static Float getFloat(Map<String, Object> map, String key) {
        return getNumber(map, key, Float.class);
    }

    /**
     * 获取Map中指定键对应的Short值。
     *
     * @param map Map对象
     * @param key 键
     * @return 键对应的值，如果键不存在或值不是Short类型，则返回null
     */
    public static Short getShort(Map<String, Object> map, String key) {
        return getNumber(map, key, Short.class);
    }

    /**
     * 获取Map中指定键对应的Byte值。
     *
     * @param map Map对象
     * @param key 键
     * @return 键对应的值，如果键不存在或值不是Byte类型，则返回null
     */
    public static Byte getByte(Map<String, Object> map, String key) {
        return getNumber(map, key, Byte.class);
    }

    /**
     * 获取Map中指定键对应的Character值。
     *
     * @param map Map对象
     * @param key 键
     * @return 键对应的值，如果键不存在或值不是Character类型，则返回null
     */
    public static Character getCharacter(Map<String, Object> map, String key) {
        return (Character) get(map, key);
    }

    /**
     * 从Map中获取指定键的值，并尝试转换为指定的Number类型。
     *
     * @param map Map对象
     * @param key 键
     * @param numberClass Number类型类对象，例如Integer.class
     * @return 键对应的值，如果键不存在或值不是Number类型，则返回null
     */
    private static <T extends Number> T getNumber(Map<String, Object> map, String key, Class<T> numberClass) {
        Object value = get(map, key);
        if (value instanceof Number) {
            Number number = (Number) value;
            if (numberClass.isInstance(number)) {
                return numberClass.cast(number);
            }
        }
        return null;
    }

    /**
     * 获取时间戳long类型
     * @param map Map对象
     * @param key 键
     * @return 键对应的值，如果键不存在或值不是Timestamp类型，则返回null
     */
    public static Long getTimestampLong(Map<String, Object> map, String key) {
        // 将时间戳转换为Long类型
        Timestamp timestamp = (Timestamp) map.get(key);
        if (timestamp == null) {
            return null;
        }
        return timestamp.getTime();
    }

    /**
     * 从Map中获取指定键的值。
     *
     * @param map Map对象
     * @param key 键
     * @return 键对应的值，如果键不存在，则返回null
     */
    private static Object get(Map<String, Object> map, String key) {
        return map != null ? map.get(key) : null;
    }
}
