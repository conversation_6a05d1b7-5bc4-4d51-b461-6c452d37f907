package com.dzpk.crazypoker.appmessage.dao;

import com.dzpk.crazypoker.appmessage.bean.UserRoomTierBean;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * AppBusinessMessageDao
 *
 * <AUTHOR>
 * @date 2024/11/18
 */
@Mapper
public interface AppBusinessMessageDao {

    /**
     *  查询俱乐部
     * @param id
     * @return Map
     */
    @Select("select id as clubId, name as clubN<PERSON>, creator as clubOwnerId from club_record where id = #{id}")
    Map<String, Object> getClubRecordAsMap(Integer id);

    /**
     * 根据USER_ID列表查询用户详情信息，结果以Map列表形式返回
     *
     * @param userIds 用户ID列表，逗号分隔的字符串形式
     * @return 包含USER_ID和nike_name的Map列表
     */
    @Select({"<script>",
            "select user_id as userId, nike_name as userName from user_details_info where user_id in",
            "(" ,
            "<foreach collection='userIds' item='item' separator=','>" ,
            "#{item}" ,
            "</foreach>)",
            "</script>" })
    List<Map<String, Object>> getUserDetailsAsMapList(@Param("userIds") List<String> userIds);


    /**
     * 根据发起者ID和状态查询消息
     * @param initiatorId 发起者ID
     * @param businessStatus 状态
     * @return
     */
    @Select("select * from app_message_business_record where initiator_id = #{initiatorId} AND business_status = #{businessStatus} AND business_code = #{businessCode}")
    List<Map<String, Object>> getBusinessRecordByInitiatorIdAndStatus(@Param("initiatorId") Long initiatorId, @Param("businessStatus") Integer businessStatus, @Param("businessCode") String businessCode);

    /**
     * 查询业务记录详情
     * @param operatorId
     * @param recordId
     * @return
     */
    @Select("SELECT " +
            "AMR.message_id, " +
            "AMR.notice_id, " +
            "AMR.category_code, " +
            "AMR.render_type, " +
            "AMR.params, " +
            "AMR.content, " +
            "AMR.tpl_code, " +
            "AMR.sender_id, " +
            "AMR.created_at, " +
            "AMRCR.user_id, " +
            "AMRCR.is_read, " +
            "AMRCR.read_at, " +
            "AMR.record_id, " +
            "AMBR.business_code, " +
            "AMBR.business_name, " +
            "AMBR.business_data, " +
            "AMBR.business_status, " +
            "AMBR.operator_id, " +
            "AMBR.initiator_id, " +
            "AMBR.operated_at " +
            "FROM app_message_receiver AMRCR " +
            "LEFT JOIN app_message_record AMR ON AMR.message_id = AMRCR.message_id " +
            "LEFT JOIN app_message_business_record AMBR ON AMR.record_id = AMBR.record_id " +
            "WHERE AMBR.operator_id = #{operatorId} " +
            "AND AMR.record_id = #{recordId}")
    Map<String, Object> getBusinessMessageDetail(@Param("operatorId") Long operatorId, @Param("recordId") Long recordId);


    /**
     * 更新业务状态
     * @param recordId
     * @param businessStatus
     */
    @Update("update app_message_business_record set business_status = #{businessStatus} where record_id = #{recordId}")
    void updateBusinessStatus(@Param("recordId") Long recordId, @Param("businessStatus") Integer businessStatus);

    /**
     * 插入联盟成员
     * @param tribeId
     * @param clubId
     * @param type
     */
    @Insert("INSERT INTO tribe_members (tribe_id, club_id, type) VALUES (#{tribeId}, #{clubId}, #{type})")
    void insertTribeMember(@Param("tribeId") Integer tribeId, @Param("clubId") Integer clubId, @Param("type") Integer type);

    /**
     * 删除联盟成员
     * @param clubId
     * @param tribeId
     */
    @Delete("DELETE FROM message_tribe_request WHERE club_id = #{clubId} AND tribe_id = #{tribeId}")
    void deleteMessageTribeRequest(@Param("clubId") Integer clubId, @Param("tribeId") Integer tribeId);

    /**
     * 查询用户长ID
     * @param userId
     * @return
     */
    @Select("SELECT random_num FROM user_details_info WHERE USER_ID = #{userId}")
    String selectRandomNumByUserId(@Param("userId") Integer userId);


    @Select("SELECT tribe_id FROM tribe_members WHERE club_id = #{clubId} LIMIT 1")
    Integer getTribeIdByClubId(@Param("clubId") Integer clubId);

    @Select("SELECT id FROM user_room_tier WHERE user_id = #{userId} AND tribe_id = #{tribeId} LIMIT 1")
    Long findUserTribeRoomTier(@Param("userId") Integer userId, @Param("tribeId") Integer tribeId);

    @Insert("INSERT INTO user_room_tier (user_id, tribe_id, tier_id) VALUES (#{userId}, #{tribeId}, #{tierId})")
    void insertUserTribeRoomTier(@Param("userId") Integer userId, @Param("tribeId") Integer tribeId, @Param("tierId") Integer tierId);

    @Select("SELECT id FROM room_tier WHERE manageable = 1 ORDER BY value ASC LIMIT 1")
    Integer getTribeMinTier();

    @Select("SELECT id FROM room_tier WHERE manageable = 1 ORDER BY value DESC LIMIT 1")
    Integer getTribeMaxTier();

    @Select("SELECT id FROM room_tier WHERE manageable = 0 ORDER BY value ASC LIMIT 1")
    Integer getDefaultMinTier();

    @Select("SELECT user_id FROM club_members WHERE club_id = #{clubId}")
    List<Long> findClubMemberIds(@Param("clubId") Integer clubId);

    @Select("select urt.user_id,urt.tier_id,urt.tribe_id,rt.tier_name,rt.value " +
            "from user_room_tier urt " +
            "left join room_tier rt on urt.tier_id = rt.id " +
            "where urt.user_id = #{userId} AND urt.tribe_id in (0,#{tribeId}) ")
    @Results({
            @Result(property = "userId", column = "user_id"),
            @Result(property = "tierId", column = "tier_id"),
            @Result(property = "tribeId", column = "tribe_id"),
            @Result(property = "tierName", column = "tier_name")
    })
    List<UserRoomTierBean> findUserRoomTier(@Param("userId") Integer userId, @Param("tribeId") Integer tribeId);

    @Select("SELECT CASE WHEN tribe_room_type = 1 AND club_room_type = 1 THEN 1 ELSE 0 END AS is_union_room FROM group_room WHERE room_id = #{roomId}")
    Integer isTribeRoom(@Param("roomId") Integer roomId);

    @Select("select rt.value from room_tier rt where id = (select gr.tier_id from group_room gr where gr.room_id = #{roomId})")
    Integer getRoomTierValue(@Param("roomId") Integer roomId);

    @Select("select gr.tier_entry_limit from group_room gr where gr.room_id = #{roomId}")
    Integer getRoomTierEntryLimit(@Param("roomId") Integer roomId);


    @Insert("INSERT INTO user_room_tier (user_id, tribe_id, tier_id) VALUES (#{userId}, 0, 1)")
    void insertUserDefaultRoomTier(@Param("userId") Integer userId);

    @Insert({
            "insert into tribe_user_payment_activity_tier (tribe_id,club_id,user_id,tpa_tier_id) ",
            "values (#{tribeId},#{clubId},#{userId},(SELECT id FROM crazy_poker.tribe_payment_activity_tier WHERE tribe_id = #{tribeId} AND is_use = 1 AND is_lock = 0 AND manageable = 0 LIMIT 1))"
    })
    void insertUserDefaultPaymentActivityTier(
            @Param("tribeId") Integer tribeId,
            @Param("clubId") Integer clubId,
            @Param("userId") Integer userId
    );

    @Update("UPDATE crazy_poker.tribe_members m " +
            "JOIN crazy_poker.tribe_club_tier t ON m.tribe_id = t.tribe_id " +
            "SET m.tribe_club_tier_id = t.id " +
            "WHERE t.tier_name = '未分层' AND t.manageable = 0 AND m.club_id = #{clubId} ")
    void updateClubMembersToDefaultTier(@Param("clubId") Integer clubId);
}
