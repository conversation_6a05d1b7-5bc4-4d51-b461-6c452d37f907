package com.dzpk.crazypoker.task.maintain;

import com.dzpk.crazypoker.common.redis.config.RedisKeyService;
import com.dzpk.crazypoker.common.redis.factory.IRedisInstanceFactory;
import com.dzpk.crazypoker.system.api.vo.MaintainMessageVo;
import com.dzpk.crazypoker.system.service.IMaintainMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


/**
 * Description:
 * <p>
 * Created by <PERSON><PERSON> on 2019/5/6 10:16
 */
@Slf4j
@Component
public class MaintainMessageTask {

    @Autowired
    private IMaintainMessageService maintainMessageService;

    @Autowired
    private IRedisInstanceFactory redisInstanceFactory;

    /**
     * 每30s刷新一次维护内容
     */
    @Scheduled(cron="*/30 * * * * ?")
    public void refreshMaintainMessage() {
        MaintainMessageVo maintainMessageVo = maintainMessageService.queryMaintainMessageFromDatabase();
        if (maintainMessageVo == null) {
            // cache a placeholder to avoid querying the database every request
            maintainMessageVo = new MaintainMessageVo();
            maintainMessageVo.setStartTime(0L);
            maintainMessageVo.setEndTime(0L);
        }
        RedisTemplate redisTemplate = redisInstanceFactory.defaultRedisInstance().getTemplate();
        String key = RedisKeyService.getMaintainMessageKey();
        redisTemplate.opsForValue().set(key, maintainMessageVo);
        log.info("refresh maintain message success, key:{}, maintainMessageVo:{}.", key, maintainMessageVo);
    }
}
