package com.dzpk.crazypoker.function.service.impl;

import com.dzpk.crazypoker.common.constant.FunctionCode;
import com.dzpk.crazypoker.function.repositories.mysql.autogen.IFunctionDao;
import com.dzpk.crazypoker.function.service.IFunctionService;
import com.dzpk.crazypoker.function.service.bean.FunctionBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class FunctionService implements IFunctionService {

    @Autowired
    private IFunctionDao iFunctionDao;

    /**
     * 查询功能开关情况
     *
     * @return
     */
    @Override
    public List<FunctionBo> queryFunctionOpen() {
        List<FunctionBo> functionBos = iFunctionDao.queryFunctionOpen();
        if(functionBos != null) {
            for(FunctionBo bo : functionBos) {
                bo.setFunctionCode(FunctionCode.queryCodeByName(bo.getFunction()));
            }
        }
        return functionBos;
    }

}
