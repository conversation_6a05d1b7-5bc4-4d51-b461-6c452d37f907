package com.dzpk.crazypoker.room.service.bean;

import com.dzpk.crazypoker.common.validators.AnyIntRange;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class DzOmahaCreationBo extends CommCreationBo {
    private int qianzhu;
    private int insuranceOn;
    private int straddleOn;
    private int muckOn;
    private int jackpotOn;
    private int vpOn;
    private int aheadLeaveOn;
    private int gameMinTime;
    private int clubId;
    private String lableName;
    private String logoUrl;

    /**
     * 是否自动开局
     * 0否 1是
     */
    private Byte autoStart;

    /**
     * 自动开局要求人数
     * 2 ~ 9
     */
    private Integer autoPlayerCount;

    /**
     * 自动开房计划开启
     */
    private int autoPlanOn;

    /**
     * 空位自动开桌配置
     */
    private int emptySeatConfig;

    /**
     * 每天计划开始时间
     */
    private Date startTime;

    /**
     * 每天计划结束时间
     */
    private Date endTime;

    /**
     *  空桌自动解散
     */
    private Integer autoDismiss;


    /**
     * 满多少桌后停止开桌
     */
    private Integer roomsLimit;
    /**
     * 延迟看牌
     */
    private Integer delay;
    /**
     * 是否俱乐部房间
     */
    private int clubRoomType;
    private int tribeRoomType;
    /**
     *请求带入
     */
    private Integer  requestToBringIn;

    /**
     * 服务费
     */
    private Double fee;
    /**
     * 每局抽取服务费的最大值
     */
    private Integer maxFeeChip;
    /**
     * 最低入池率
     */
    private Integer minVpip;

    /**
     * 創建消費
     */
    private Integer chipSpent;

    /**
     * 牌局分层
     */
    private Integer tierId;

    /**
     * 分层入场限制
     */
    private Integer tierEntryLimit;

}
