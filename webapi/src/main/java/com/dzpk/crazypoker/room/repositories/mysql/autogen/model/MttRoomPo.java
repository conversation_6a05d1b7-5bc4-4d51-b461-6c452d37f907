package com.dzpk.crazypoker.room.repositories.mysql.autogen.model;

import java.io.Serializable;
import java.math.BigDecimal;

public class MttRoomPo implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.game_id
     *
     * @mbg.generated
     */
    private Integer gameId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.creator_id
     *
     * @mbg.generated
     */
    private Integer creatorId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.creator_name
     *
     * @mbg.generated
     */
    private String creatorName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.mtt_name
     *
     * @mbg.generated
     */
    private String mttName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.is_official
     *
     * @mbg.generated
     */
    private Byte isOfficial;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.start_time
     *
     * @mbg.generated
     */
    private Integer startTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.create_time
     *
     * @mbg.generated
     */
    private Integer createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.upper_limit
     *
     * @mbg.generated
     */
    private Integer upperLimit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.participants
     *
     * @mbg.generated
     */
    private Integer participants;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.mtt_type
     *
     * @mbg.generated
     */
    private Integer mttType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.initial_chip
     *
     * @mbg.generated
     */
    private Integer initialChip;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.update_cycle
     *
     * @mbg.generated
     */
    private Integer updateCycle;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.bind_type
     *
     * @mbg.generated
     */
    private Byte bindType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.bonus_type
     *
     * @mbg.generated
     */
    private Byte bonusType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.allow_rebuy
     *
     * @mbg.generated
     */
    private Byte allowRebuy;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.max_update_level
     *
     * @mbg.generated
     */
    private Integer maxUpdateLevel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.rebuy_times
     *
     * @mbg.generated
     */
    private Integer rebuyTimes;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.allow_append
     *
     * @mbg.generated
     */
    private Byte allowAppend;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.voucher
     *
     * @mbg.generated
     */
    private Integer voucher;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.initial_score
     *
     * @mbg.generated
     */
    private Integer initialScore;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.lower_limit
     *
     * @mbg.generated
     */
    private Integer lowerLimit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.entry_time
     *
     * @mbg.generated
     */
    private Integer entryTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.show_time
     *
     * @mbg.generated
     */
    private Integer showTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.game_type
     *
     * @mbg.generated
     */
    private Integer gameType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.game_icon
     *
     * @mbg.generated
     */
    private String gameIcon;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.service_fee
     *
     * @mbg.generated
     */
    private Integer serviceFee;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.allow_delay
     *
     * @mbg.generated
     */
    private Byte allowDelay;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.max_delay_level
     *
     * @mbg.generated
     */
    private Integer maxDelayLevel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.advanced_entry
     *
     * @mbg.generated
     */
    private Integer advancedEntry;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.prize_name
     *
     * @mbg.generated
     */
    private String prizeName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.prize_icon
     *
     * @mbg.generated
     */
    private String prizeIcon;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.is_virtual
     *
     * @mbg.generated
     */
    private Byte isVirtual;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.initial_pool
     *
     * @mbg.generated
     */
    private Integer initialPool;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.ip
     *
     * @mbg.generated
     */
    private Short ip;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.gps
     *
     * @mbg.generated
     */
    private Short gps;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.sb_chip
     *
     * @mbg.generated
     */
    private BigDecimal sbChip;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.hunter_match
     *
     * @mbg.generated
     */
    private Integer hunterMatch;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.hunter_percent
     *
     * @mbg.generated
     */
    private Integer hunterPercent;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.canShow
     *
     * @mbg.generated
     */
    private Integer canshow;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.logo_url
     *
     * @mbg.generated
     */
    private String logoUrl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.op_time
     *
     * @mbg.generated
     */
    private Integer opTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.server_id
     *
     * @mbg.generated
     */
    private String serverId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.access_ip
     *
     * @mbg.generated
     */
    private String accessIp;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.access_port
     *
     * @mbg.generated
     */
    private Integer accessPort;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.under_control
     *
     * @mbg.generated
     */
    private Byte underControl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.charge
     *
     * @mbg.generated
     */
    private Integer charge;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.source
     *
     * @mbg.generated
     */
    private Short source;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.source_id
     *
     * @mbg.generated
     */
    private Integer sourceId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.tribe_id
     *
     * @mbg.generated
     */
    private Integer tribeId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.charge_percent
     *
     * @mbg.generated
     */
    private Integer chargePercent;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtt_record.registation_fee
     *
     * @mbg.generated
     */
    private Integer registationFee;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table mtt_record
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.id
     *
     * @return the value of mtt_record.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.id
     *
     * @param id the value for mtt_record.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.game_id
     *
     * @return the value of mtt_record.game_id
     *
     * @mbg.generated
     */
    public Integer getGameId() {
        return gameId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.game_id
     *
     * @param gameId the value for mtt_record.game_id
     *
     * @mbg.generated
     */
    public void setGameId(Integer gameId) {
        this.gameId = gameId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.creator_id
     *
     * @return the value of mtt_record.creator_id
     *
     * @mbg.generated
     */
    public Integer getCreatorId() {
        return creatorId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.creator_id
     *
     * @param creatorId the value for mtt_record.creator_id
     *
     * @mbg.generated
     */
    public void setCreatorId(Integer creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.creator_name
     *
     * @return the value of mtt_record.creator_name
     *
     * @mbg.generated
     */
    public String getCreatorName() {
        return creatorName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.creator_name
     *
     * @param creatorName the value for mtt_record.creator_name
     *
     * @mbg.generated
     */
    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName == null ? null : creatorName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.mtt_name
     *
     * @return the value of mtt_record.mtt_name
     *
     * @mbg.generated
     */
    public String getMttName() {
        return mttName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.mtt_name
     *
     * @param mttName the value for mtt_record.mtt_name
     *
     * @mbg.generated
     */
    public void setMttName(String mttName) {
        this.mttName = mttName == null ? null : mttName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.is_official
     *
     * @return the value of mtt_record.is_official
     *
     * @mbg.generated
     */
    public Byte getIsOfficial() {
        return isOfficial;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.is_official
     *
     * @param isOfficial the value for mtt_record.is_official
     *
     * @mbg.generated
     */
    public void setIsOfficial(Byte isOfficial) {
        this.isOfficial = isOfficial;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.start_time
     *
     * @return the value of mtt_record.start_time
     *
     * @mbg.generated
     */
    public Integer getStartTime() {
        return startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.start_time
     *
     * @param startTime the value for mtt_record.start_time
     *
     * @mbg.generated
     */
    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.create_time
     *
     * @return the value of mtt_record.create_time
     *
     * @mbg.generated
     */
    public Integer getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.create_time
     *
     * @param createTime the value for mtt_record.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Integer createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.status
     *
     * @return the value of mtt_record.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.status
     *
     * @param status the value for mtt_record.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.upper_limit
     *
     * @return the value of mtt_record.upper_limit
     *
     * @mbg.generated
     */
    public Integer getUpperLimit() {
        return upperLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.upper_limit
     *
     * @param upperLimit the value for mtt_record.upper_limit
     *
     * @mbg.generated
     */
    public void setUpperLimit(Integer upperLimit) {
        this.upperLimit = upperLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.participants
     *
     * @return the value of mtt_record.participants
     *
     * @mbg.generated
     */
    public Integer getParticipants() {
        return participants;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.participants
     *
     * @param participants the value for mtt_record.participants
     *
     * @mbg.generated
     */
    public void setParticipants(Integer participants) {
        this.participants = participants;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.mtt_type
     *
     * @return the value of mtt_record.mtt_type
     *
     * @mbg.generated
     */
    public Integer getMttType() {
        return mttType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.mtt_type
     *
     * @param mttType the value for mtt_record.mtt_type
     *
     * @mbg.generated
     */
    public void setMttType(Integer mttType) {
        this.mttType = mttType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.initial_chip
     *
     * @return the value of mtt_record.initial_chip
     *
     * @mbg.generated
     */
    public Integer getInitialChip() {
        return initialChip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.initial_chip
     *
     * @param initialChip the value for mtt_record.initial_chip
     *
     * @mbg.generated
     */
    public void setInitialChip(Integer initialChip) {
        this.initialChip = initialChip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.update_cycle
     *
     * @return the value of mtt_record.update_cycle
     *
     * @mbg.generated
     */
    public Integer getUpdateCycle() {
        return updateCycle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.update_cycle
     *
     * @param updateCycle the value for mtt_record.update_cycle
     *
     * @mbg.generated
     */
    public void setUpdateCycle(Integer updateCycle) {
        this.updateCycle = updateCycle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.bind_type
     *
     * @return the value of mtt_record.bind_type
     *
     * @mbg.generated
     */
    public Byte getBindType() {
        return bindType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.bind_type
     *
     * @param bindType the value for mtt_record.bind_type
     *
     * @mbg.generated
     */
    public void setBindType(Byte bindType) {
        this.bindType = bindType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.bonus_type
     *
     * @return the value of mtt_record.bonus_type
     *
     * @mbg.generated
     */
    public Byte getBonusType() {
        return bonusType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.bonus_type
     *
     * @param bonusType the value for mtt_record.bonus_type
     *
     * @mbg.generated
     */
    public void setBonusType(Byte bonusType) {
        this.bonusType = bonusType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.allow_rebuy
     *
     * @return the value of mtt_record.allow_rebuy
     *
     * @mbg.generated
     */
    public Byte getAllowRebuy() {
        return allowRebuy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.allow_rebuy
     *
     * @param allowRebuy the value for mtt_record.allow_rebuy
     *
     * @mbg.generated
     */
    public void setAllowRebuy(Byte allowRebuy) {
        this.allowRebuy = allowRebuy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.max_update_level
     *
     * @return the value of mtt_record.max_update_level
     *
     * @mbg.generated
     */
    public Integer getMaxUpdateLevel() {
        return maxUpdateLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.max_update_level
     *
     * @param maxUpdateLevel the value for mtt_record.max_update_level
     *
     * @mbg.generated
     */
    public void setMaxUpdateLevel(Integer maxUpdateLevel) {
        this.maxUpdateLevel = maxUpdateLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.rebuy_times
     *
     * @return the value of mtt_record.rebuy_times
     *
     * @mbg.generated
     */
    public Integer getRebuyTimes() {
        return rebuyTimes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.rebuy_times
     *
     * @param rebuyTimes the value for mtt_record.rebuy_times
     *
     * @mbg.generated
     */
    public void setRebuyTimes(Integer rebuyTimes) {
        this.rebuyTimes = rebuyTimes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.allow_append
     *
     * @return the value of mtt_record.allow_append
     *
     * @mbg.generated
     */
    public Byte getAllowAppend() {
        return allowAppend;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.allow_append
     *
     * @param allowAppend the value for mtt_record.allow_append
     *
     * @mbg.generated
     */
    public void setAllowAppend(Byte allowAppend) {
        this.allowAppend = allowAppend;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.voucher
     *
     * @return the value of mtt_record.voucher
     *
     * @mbg.generated
     */
    public Integer getVoucher() {
        return voucher;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.voucher
     *
     * @param voucher the value for mtt_record.voucher
     *
     * @mbg.generated
     */
    public void setVoucher(Integer voucher) {
        this.voucher = voucher;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.initial_score
     *
     * @return the value of mtt_record.initial_score
     *
     * @mbg.generated
     */
    public Integer getInitialScore() {
        return initialScore;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.initial_score
     *
     * @param initialScore the value for mtt_record.initial_score
     *
     * @mbg.generated
     */
    public void setInitialScore(Integer initialScore) {
        this.initialScore = initialScore;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.lower_limit
     *
     * @return the value of mtt_record.lower_limit
     *
     * @mbg.generated
     */
    public Integer getLowerLimit() {
        return lowerLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.lower_limit
     *
     * @param lowerLimit the value for mtt_record.lower_limit
     *
     * @mbg.generated
     */
    public void setLowerLimit(Integer lowerLimit) {
        this.lowerLimit = lowerLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.entry_time
     *
     * @return the value of mtt_record.entry_time
     *
     * @mbg.generated
     */
    public Integer getEntryTime() {
        return entryTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.entry_time
     *
     * @param entryTime the value for mtt_record.entry_time
     *
     * @mbg.generated
     */
    public void setEntryTime(Integer entryTime) {
        this.entryTime = entryTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.show_time
     *
     * @return the value of mtt_record.show_time
     *
     * @mbg.generated
     */
    public Integer getShowTime() {
        return showTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.show_time
     *
     * @param showTime the value for mtt_record.show_time
     *
     * @mbg.generated
     */
    public void setShowTime(Integer showTime) {
        this.showTime = showTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.game_type
     *
     * @return the value of mtt_record.game_type
     *
     * @mbg.generated
     */
    public Integer getGameType() {
        return gameType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.game_type
     *
     * @param gameType the value for mtt_record.game_type
     *
     * @mbg.generated
     */
    public void setGameType(Integer gameType) {
        this.gameType = gameType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.game_icon
     *
     * @return the value of mtt_record.game_icon
     *
     * @mbg.generated
     */
    public String getGameIcon() {
        return gameIcon;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.game_icon
     *
     * @param gameIcon the value for mtt_record.game_icon
     *
     * @mbg.generated
     */
    public void setGameIcon(String gameIcon) {
        this.gameIcon = gameIcon == null ? null : gameIcon.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.service_fee
     *
     * @return the value of mtt_record.service_fee
     *
     * @mbg.generated
     */
    public Integer getServiceFee() {
        return serviceFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.service_fee
     *
     * @param serviceFee the value for mtt_record.service_fee
     *
     * @mbg.generated
     */
    public void setServiceFee(Integer serviceFee) {
        this.serviceFee = serviceFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.allow_delay
     *
     * @return the value of mtt_record.allow_delay
     *
     * @mbg.generated
     */
    public Byte getAllowDelay() {
        return allowDelay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.allow_delay
     *
     * @param allowDelay the value for mtt_record.allow_delay
     *
     * @mbg.generated
     */
    public void setAllowDelay(Byte allowDelay) {
        this.allowDelay = allowDelay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.max_delay_level
     *
     * @return the value of mtt_record.max_delay_level
     *
     * @mbg.generated
     */
    public Integer getMaxDelayLevel() {
        return maxDelayLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.max_delay_level
     *
     * @param maxDelayLevel the value for mtt_record.max_delay_level
     *
     * @mbg.generated
     */
    public void setMaxDelayLevel(Integer maxDelayLevel) {
        this.maxDelayLevel = maxDelayLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.advanced_entry
     *
     * @return the value of mtt_record.advanced_entry
     *
     * @mbg.generated
     */
    public Integer getAdvancedEntry() {
        return advancedEntry;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.advanced_entry
     *
     * @param advancedEntry the value for mtt_record.advanced_entry
     *
     * @mbg.generated
     */
    public void setAdvancedEntry(Integer advancedEntry) {
        this.advancedEntry = advancedEntry;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.prize_name
     *
     * @return the value of mtt_record.prize_name
     *
     * @mbg.generated
     */
    public String getPrizeName() {
        return prizeName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.prize_name
     *
     * @param prizeName the value for mtt_record.prize_name
     *
     * @mbg.generated
     */
    public void setPrizeName(String prizeName) {
        this.prizeName = prizeName == null ? null : prizeName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.prize_icon
     *
     * @return the value of mtt_record.prize_icon
     *
     * @mbg.generated
     */
    public String getPrizeIcon() {
        return prizeIcon;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.prize_icon
     *
     * @param prizeIcon the value for mtt_record.prize_icon
     *
     * @mbg.generated
     */
    public void setPrizeIcon(String prizeIcon) {
        this.prizeIcon = prizeIcon == null ? null : prizeIcon.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.is_virtual
     *
     * @return the value of mtt_record.is_virtual
     *
     * @mbg.generated
     */
    public Byte getIsVirtual() {
        return isVirtual;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.is_virtual
     *
     * @param isVirtual the value for mtt_record.is_virtual
     *
     * @mbg.generated
     */
    public void setIsVirtual(Byte isVirtual) {
        this.isVirtual = isVirtual;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.initial_pool
     *
     * @return the value of mtt_record.initial_pool
     *
     * @mbg.generated
     */
    public Integer getInitialPool() {
        return initialPool;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.initial_pool
     *
     * @param initialPool the value for mtt_record.initial_pool
     *
     * @mbg.generated
     */
    public void setInitialPool(Integer initialPool) {
        this.initialPool = initialPool;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.ip
     *
     * @return the value of mtt_record.ip
     *
     * @mbg.generated
     */
    public Short getIp() {
        return ip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.ip
     *
     * @param ip the value for mtt_record.ip
     *
     * @mbg.generated
     */
    public void setIp(Short ip) {
        this.ip = ip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.gps
     *
     * @return the value of mtt_record.gps
     *
     * @mbg.generated
     */
    public Short getGps() {
        return gps;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.gps
     *
     * @param gps the value for mtt_record.gps
     *
     * @mbg.generated
     */
    public void setGps(Short gps) {
        this.gps = gps;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.sb_chip
     *
     * @return the value of mtt_record.sb_chip
     *
     * @mbg.generated
     */
    public BigDecimal getSbChip() {
        return sbChip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.sb_chip
     *
     * @param sbChip the value for mtt_record.sb_chip
     *
     * @mbg.generated
     */
    public void setSbChip(BigDecimal sbChip) {
        this.sbChip = sbChip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.hunter_match
     *
     * @return the value of mtt_record.hunter_match
     *
     * @mbg.generated
     */
    public Integer getHunterMatch() {
        return hunterMatch;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.hunter_match
     *
     * @param hunterMatch the value for mtt_record.hunter_match
     *
     * @mbg.generated
     */
    public void setHunterMatch(Integer hunterMatch) {
        this.hunterMatch = hunterMatch;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.hunter_percent
     *
     * @return the value of mtt_record.hunter_percent
     *
     * @mbg.generated
     */
    public Integer getHunterPercent() {
        return hunterPercent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.hunter_percent
     *
     * @param hunterPercent the value for mtt_record.hunter_percent
     *
     * @mbg.generated
     */
    public void setHunterPercent(Integer hunterPercent) {
        this.hunterPercent = hunterPercent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.canShow
     *
     * @return the value of mtt_record.canShow
     *
     * @mbg.generated
     */
    public Integer getCanshow() {
        return canshow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.canShow
     *
     * @param canshow the value for mtt_record.canShow
     *
     * @mbg.generated
     */
    public void setCanshow(Integer canshow) {
        this.canshow = canshow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.logo_url
     *
     * @return the value of mtt_record.logo_url
     *
     * @mbg.generated
     */
    public String getLogoUrl() {
        return logoUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.logo_url
     *
     * @param logoUrl the value for mtt_record.logo_url
     *
     * @mbg.generated
     */
    public void setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl == null ? null : logoUrl.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.op_time
     *
     * @return the value of mtt_record.op_time
     *
     * @mbg.generated
     */
    public Integer getOpTime() {
        return opTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.op_time
     *
     * @param opTime the value for mtt_record.op_time
     *
     * @mbg.generated
     */
    public void setOpTime(Integer opTime) {
        this.opTime = opTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.server_id
     *
     * @return the value of mtt_record.server_id
     *
     * @mbg.generated
     */
    public String getServerId() {
        return serverId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.server_id
     *
     * @param serverId the value for mtt_record.server_id
     *
     * @mbg.generated
     */
    public void setServerId(String serverId) {
        this.serverId = serverId == null ? null : serverId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.access_ip
     *
     * @return the value of mtt_record.access_ip
     *
     * @mbg.generated
     */
    public String getAccessIp() {
        return accessIp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.access_ip
     *
     * @param accessIp the value for mtt_record.access_ip
     *
     * @mbg.generated
     */
    public void setAccessIp(String accessIp) {
        this.accessIp = accessIp == null ? null : accessIp.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.access_port
     *
     * @return the value of mtt_record.access_port
     *
     * @mbg.generated
     */
    public Integer getAccessPort() {
        return accessPort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.access_port
     *
     * @param accessPort the value for mtt_record.access_port
     *
     * @mbg.generated
     */
    public void setAccessPort(Integer accessPort) {
        this.accessPort = accessPort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.under_control
     *
     * @return the value of mtt_record.under_control
     *
     * @mbg.generated
     */
    public Byte getUnderControl() {
        return underControl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.under_control
     *
     * @param underControl the value for mtt_record.under_control
     *
     * @mbg.generated
     */
    public void setUnderControl(Byte underControl) {
        this.underControl = underControl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.charge
     *
     * @return the value of mtt_record.charge
     *
     * @mbg.generated
     */
    public Integer getCharge() {
        return charge;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.charge
     *
     * @param charge the value for mtt_record.charge
     *
     * @mbg.generated
     */
    public void setCharge(Integer charge) {
        this.charge = charge;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.source
     *
     * @return the value of mtt_record.source
     *
     * @mbg.generated
     */
    public Short getSource() {
        return source;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.source
     *
     * @param source the value for mtt_record.source
     *
     * @mbg.generated
     */
    public void setSource(Short source) {
        this.source = source;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.source_id
     *
     * @return the value of mtt_record.source_id
     *
     * @mbg.generated
     */
    public Integer getSourceId() {
        return sourceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.source_id
     *
     * @param sourceId the value for mtt_record.source_id
     *
     * @mbg.generated
     */
    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.tribe_id
     *
     * @return the value of mtt_record.tribe_id
     *
     * @mbg.generated
     */
    public Integer getTribeId() {
        return tribeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.tribe_id
     *
     * @param tribeId the value for mtt_record.tribe_id
     *
     * @mbg.generated
     */
    public void setTribeId(Integer tribeId) {
        this.tribeId = tribeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.charge_percent
     *
     * @return the value of mtt_record.charge_percent
     *
     * @mbg.generated
     */
    public Integer getChargePercent() {
        return chargePercent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.charge_percent
     *
     * @param chargePercent the value for mtt_record.charge_percent
     *
     * @mbg.generated
     */
    public void setChargePercent(Integer chargePercent) {
        this.chargePercent = chargePercent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtt_record.registation_fee
     *
     * @return the value of mtt_record.registation_fee
     *
     * @mbg.generated
     */
    public Integer getRegistationFee() {
        return registationFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtt_record.registation_fee
     *
     * @param registationFee the value for mtt_record.registation_fee
     *
     * @mbg.generated
     */
    public void setRegistationFee(Integer registationFee) {
        this.registationFee = registationFee;
    }
}