package com.dzpk.crazypoker.room.repositories.mysql.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 0:close 1:create 2:pending 3:playing 4:played  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GroupRoomPo {
    /**
     * 房间id
     */
    @ApiModelProperty("房间id")
    private Integer id;

    /**
     * 保险模式（1打开0关闭）
     */
    @ApiModelProperty("保险模式（1打开0关闭）")
    private Byte insurance;

    /**
     * 牌局名称
     */
    @ApiModelProperty("牌局名称")
    private String name;

    /**
     * 前注
     */
    @ApiModelProperty("前注")
    private Integer qianzhu;

    /**
     * 小盲注筹码
     */
    @ApiModelProperty("小盲注筹码")
    private Integer sbChip;

    /**
     * 带入的筹码
     */
    @ApiModelProperty("带入的筹码")
    private Integer inChip;

    /**
     * 比赛时长
     */
    @ApiModelProperty("比赛时长")
    private Integer maxPlayTime;

    /**
     * 牌桌人数
     */
    @ApiModelProperty("牌桌人数")
    private Byte playerCount;

    /**
     * 房间状态：0:close 1:create（创建） 2:pending（野局创建完成） 3:playing(进入房间) 4:played（牌局内点了开局）
     */
    @ApiModelProperty("房间状态：0:close 1:create（创建） 2:pending（野局创建完成） 3:playing(进入房间) 4:played（牌局内点了开局）")
    private Integer status;

    /**
     * 房间创建人
     */
    @ApiModelProperty("房间创建人")
    private Integer creator;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Integer createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    /**
     * 游戏开始时间
     */
    @ApiModelProperty("游戏开始时间")
    private LocalDateTime startTime;

    /**
     * 最小带入倍数
     */
    @ApiModelProperty("最小带入倍数")
    private Integer minRate;

    /**
     * 最大带入倍数
     */
    @ApiModelProperty("最大带入倍数")
    private Integer maxRate;

    /**
     * 是否暂停
     */
    @ApiModelProperty("是否暂停")
    private Byte pause;

    /**
     * 高级功能扣除砖石数 0表示没开启高级功能
     */
    @ApiModelProperty("高级功能扣除砖石数 0表示没开启高级功能")
    private Integer idou;

    /**
     * 房间id
     */
    @ApiModelProperty("房间id")
    private Integer roomId;

    /**
     * ip控制 0关 1开
     */
    @ApiModelProperty("ip控制 0关 1开")
    private Byte ip;

    /**
     * 是否gps限制 0关 1开
     */
    @ApiModelProperty("是否gps限制 0关 1开")
    private Byte limitGps;

    /**
     * straddle
     */
    @ApiModelProperty("straddle")
    private Byte straddle;

    /**
     * 最短打牌时间
     */
    @ApiModelProperty("最短打牌时间")
    private Integer minPlayTime;

    /**
     * 牌局类型：
            
            61 = 德州
            62 = 德州-必下场
            63 = 德州-AOF
            91 = 奥马哈
            92 = 奥马哈-必下场
            93 = 奥马哈-AOF
            51 = 大菠萝
            81 = SNG
            71 = MTT
     */
    @ApiModelProperty("牌局类型： 61 = 德州 62 = 德州-必下场 63 = 德州-AOF 91 = 奥马哈 92 = 奥马哈-必下场 93 = 奥马哈-AOF 51 = 大菠萝 81 = SNG 71 = MTT")
    private Integer roomPath;

    /**
     * 普通0 奥马哈1
     */
    @ApiModelProperty("普通0 奥马哈1")
    private Integer roomType;

    /**
     * 盖牌开关
     */
    @ApiModelProperty("盖牌开关")
    private Integer muckSwitch;

    /**
     * jackpotOn
     */
    @ApiModelProperty("jackpotOn")
    private Boolean jackpotOn;

    /**
     * 特殊玩法，暂奥玛哈使用，0为普通玩法、1奥马哈血战模式
     */
    @ApiModelProperty("特殊玩法，暂奥玛哈使用，0为普通玩法、1奥马哈血战模式")
    private Integer roomMode;

    /**
     * 提前离桌是否开启 0否1开启
     */
    @ApiModelProperty("提前离桌是否开启 0否1开启")
    private Integer leaveTable;

    /**
     * 统计入池率 0 不统计
     */
    @ApiModelProperty("统计入池率 0 不统计")
    private Byte vp;

    /**
     * logoUrl
     */
    @ApiModelProperty("logoUrl")
    private String logoUrl;

    /**
     * 思考时间
     */
    @ApiModelProperty("思考时间")
    private Integer opTime;

    /**
     * serverId
     */
    @ApiModelProperty("serverId")
    private String serverId;

    /**
     * accessIp
     */
    @ApiModelProperty("accessIp")
    private String accessIp;

    /**
     * accessPort
     */
    @ApiModelProperty("accessPort")
    private Integer accessPort;

    /**
     * 是否控制带入：0控制，1不控制
     */
    @ApiModelProperty("是否控制带入：0控制，1不控制")
    private Integer control;

    /**
     * 需要支付的记录费
     */
    @ApiModelProperty("需要支付的记录费")
    private Integer charge;

    /**
     * 建房来源(0:快速创建；1:群创建；2:俱乐部创建)
     */
    @ApiModelProperty("建房来源(0:快速创建；1:群创建；2:俱乐部创建)")
    private Integer source;

    /**
     * 佣金（抽水百分比）
     */
    @ApiModelProperty("佣金（抽水百分比）")
    private Byte commission;

    /**
     * 建房来源id
     */
    @ApiModelProperty("建房来源id")
    private Integer sourceId;

    /**
     * 部落id
     */
    @ApiModelProperty("部落id")
    private Integer tribeId;

    /**
     * 是否定向公会房间，用于差异收费
     */
    @ApiModelProperty("是否定向公会房间，用于差异收费")
    private Integer orientation;

    /**
     * 信用分控制0关闭，1-开启
     */
    @ApiModelProperty("信用分控制0关闭，1-开启")
    private Integer creditControl;

    /**
     * 是否自动开局 0否 1是
     */
    @ApiModelProperty("是否自动开局 0否 1是")
    private Byte autoStart;

    /**
     * 自动开局的人数
     */
    @ApiModelProperty("自动开局的人数")
    private Integer autoPlayerCount;

    /**
     * 延迟看牌
     */
    @ApiModelProperty("延迟看牌")
    private Integer delay;
    /**
     * 是否俱乐部房间
     */
    private Integer clubRoomType;
    /**
     * 是否联盟0否1是
     */
    private Integer tribeRoomType;
    private Integer clubId;

    /**
     *是否请求带入0：false，1：false requestToBringIn
     */
    private Integer requestToBringIn;

    /**
     * 服务费
     */
    private Double serviceCharge;

    /**
     * 每局抽取服务费的最大值
     */
    private Integer maxFeeChip;

    /**
     * 最低入池率
     */
    private Integer minVpip;

    /**
     * 創建消費
     */
    private Integer chipSpent;

    private Integer tierId;

    private Integer tierValue;


}