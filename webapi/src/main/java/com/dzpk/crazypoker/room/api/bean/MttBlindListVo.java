package com.dzpk.crazypoker.room.api.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * Created by jayce on 2019/10/14
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@ApiModel(value = "mtt比赛盲注列表信息")
public class MttBlindListVo {

    @ApiModelProperty(notes = "级别")
    private Integer level;

    @ApiModelProperty(notes = "升盲时间")
    private Integer time;

    @ApiModelProperty(notes = "对应小盲")
    private Integer sb;

    @ApiModelProperty(notes = "对应前注")
    private Integer ante;

}
