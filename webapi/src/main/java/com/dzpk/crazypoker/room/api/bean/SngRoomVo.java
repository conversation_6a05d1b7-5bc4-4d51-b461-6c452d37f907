package com.dzpk.crazypoker.room.api.bean;

import com.dzpk.crazypoker.common.validators.AnyIntRange;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@ApiModel(description="创建牌局&进入牌局-SNG专用")
public class SngRoomVo extends CommRoomVo {
    @ApiModelProperty(
            required = false,
            position = 12,
            allowableValues="0,1",
            notes="muck开关:0 = 关闭 ,1 = 开启"
    )
    @JsonProperty("muckon")
    @AnyIntRange(value = {0,1})
    private Integer muckOn;
}
