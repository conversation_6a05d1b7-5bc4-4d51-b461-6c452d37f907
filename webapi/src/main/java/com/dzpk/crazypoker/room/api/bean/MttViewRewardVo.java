package com.dzpk.crazypoker.room.api.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;

/**
 * Created by jayce on 2019/10/14
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@ApiModel(value = "mtt比赛奖励")
public class MttViewRewardVo {

    @ApiModelProperty(notes = "奖励列表")
    private List<MttRewardListVo> currMttRewardList;

    @ApiModelProperty(notes = "下一个奖励排名")
    private Integer nextRewardNum;

    @ApiModelProperty(notes = "下一个奖励")
    private String nextReward;

    @ApiModelProperty(notes = "奖励人数")
    private Integer rewardNumber;

    @ApiModelProperty(notes = "总玩家数")
    private Integer totalPlayerNum;

    @ApiModelProperty(notes = "奖励类型？")
    private Integer bonusType;

    @ApiModelProperty(notes = "固定奖池")
    private Integer initialPool;

    @ApiModelProperty(notes = "总奖池")
    private Integer totalChips;

    @ApiModelProperty(notes = "玩家在比赛中处于的状态")
    private Integer gameStatus;

    @ApiModelProperty(notes = "客户端ip")
    private String clientIp;

    @ApiModelProperty(notes = "MTT服务器所在的IP")
    private String mttIp;

    @ApiModelProperty(notes = "MTT对应的MTT服务所在port")
    private String mttPort;

}
