package com.dzpk.crazypoker.room.repositories.mysql.autogen.model;

import java.io.Serializable;
import java.util.Date;

public class SngRoomPo implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.insurance
     *
     * @mbg.generated
     */
    private Byte insurance;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.name
     *
     * @mbg.generated
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.in_chip
     *
     * @mbg.generated
     */
    private Integer inChip;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.init_chip
     *
     * @mbg.generated
     */
    private Integer initChip;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.player_count
     *
     * @mbg.generated
     */
    private Byte playerCount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.status
     *
     * @mbg.generated
     */
    private Integer status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.creator
     *
     * @mbg.generated
     */
    private Integer creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.up_blind_time
     *
     * @mbg.generated
     */
    private Integer upBlindTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.create_time
     *
     * @mbg.generated
     */
    private Integer createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.start_time
     *
     * @mbg.generated
     */
    private Date startTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.pause
     *
     * @mbg.generated
     */
    private Byte pause;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.room_id
     *
     * @mbg.generated
     */
    private Integer roomId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.ip
     *
     * @mbg.generated
     */
    private Byte ip;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.limit_gps
     *
     * @mbg.generated
     */
    private Byte limitGps;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.type
     *
     * @mbg.generated
     */
    private Byte type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.muck_switch
     *
     * @mbg.generated
     */
    private Integer muckSwitch;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.logo_url
     *
     * @mbg.generated
     */
    private String logoUrl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.op_time
     *
     * @mbg.generated
     */
    private Integer opTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.server_id
     *
     * @mbg.generated
     */
    private String serverId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.access_ip
     *
     * @mbg.generated
     */
    private String accessIp;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.access_port
     *
     * @mbg.generated
     */
    private Integer accessPort;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.control
     *
     * @mbg.generated
     */
    private Integer control;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.charge
     *
     * @mbg.generated
     */
    private Integer charge;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.source
     *
     * @mbg.generated
     */
    private Integer source;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.source_id
     *
     * @mbg.generated
     */
    private Integer sourceId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.tribe_id
     *
     * @mbg.generated
     */
    private Integer tribeId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sng_room.credit_control
     *
     * @mbg.generated
     */
    private Integer creditControl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table sng_room
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.id
     *
     * @return the value of sng_room.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.id
     *
     * @param id the value for sng_room.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.insurance
     *
     * @return the value of sng_room.insurance
     *
     * @mbg.generated
     */
    public Byte getInsurance() {
        return insurance;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.insurance
     *
     * @param insurance the value for sng_room.insurance
     *
     * @mbg.generated
     */
    public void setInsurance(Byte insurance) {
        this.insurance = insurance;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.name
     *
     * @return the value of sng_room.name
     *
     * @mbg.generated
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.name
     *
     * @param name the value for sng_room.name
     *
     * @mbg.generated
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.in_chip
     *
     * @return the value of sng_room.in_chip
     *
     * @mbg.generated
     */
    public Integer getInChip() {
        return inChip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.in_chip
     *
     * @param inChip the value for sng_room.in_chip
     *
     * @mbg.generated
     */
    public void setInChip(Integer inChip) {
        this.inChip = inChip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.init_chip
     *
     * @return the value of sng_room.init_chip
     *
     * @mbg.generated
     */
    public Integer getInitChip() {
        return initChip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.init_chip
     *
     * @param initChip the value for sng_room.init_chip
     *
     * @mbg.generated
     */
    public void setInitChip(Integer initChip) {
        this.initChip = initChip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.player_count
     *
     * @return the value of sng_room.player_count
     *
     * @mbg.generated
     */
    public Byte getPlayerCount() {
        return playerCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.player_count
     *
     * @param playerCount the value for sng_room.player_count
     *
     * @mbg.generated
     */
    public void setPlayerCount(Byte playerCount) {
        this.playerCount = playerCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.status
     *
     * @return the value of sng_room.status
     *
     * @mbg.generated
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.status
     *
     * @param status the value for sng_room.status
     *
     * @mbg.generated
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.creator
     *
     * @return the value of sng_room.creator
     *
     * @mbg.generated
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.creator
     *
     * @param creator the value for sng_room.creator
     *
     * @mbg.generated
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.up_blind_time
     *
     * @return the value of sng_room.up_blind_time
     *
     * @mbg.generated
     */
    public Integer getUpBlindTime() {
        return upBlindTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.up_blind_time
     *
     * @param upBlindTime the value for sng_room.up_blind_time
     *
     * @mbg.generated
     */
    public void setUpBlindTime(Integer upBlindTime) {
        this.upBlindTime = upBlindTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.create_time
     *
     * @return the value of sng_room.create_time
     *
     * @mbg.generated
     */
    public Integer getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.create_time
     *
     * @param createTime the value for sng_room.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Integer createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.update_time
     *
     * @return the value of sng_room.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.update_time
     *
     * @param updateTime the value for sng_room.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.start_time
     *
     * @return the value of sng_room.start_time
     *
     * @mbg.generated
     */
    public Date getStartTime() {
        return startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.start_time
     *
     * @param startTime the value for sng_room.start_time
     *
     * @mbg.generated
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.pause
     *
     * @return the value of sng_room.pause
     *
     * @mbg.generated
     */
    public Byte getPause() {
        return pause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.pause
     *
     * @param pause the value for sng_room.pause
     *
     * @mbg.generated
     */
    public void setPause(Byte pause) {
        this.pause = pause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.room_id
     *
     * @return the value of sng_room.room_id
     *
     * @mbg.generated
     */
    public Integer getRoomId() {
        return roomId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.room_id
     *
     * @param roomId the value for sng_room.room_id
     *
     * @mbg.generated
     */
    public void setRoomId(Integer roomId) {
        this.roomId = roomId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.ip
     *
     * @return the value of sng_room.ip
     *
     * @mbg.generated
     */
    public Byte getIp() {
        return ip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.ip
     *
     * @param ip the value for sng_room.ip
     *
     * @mbg.generated
     */
    public void setIp(Byte ip) {
        this.ip = ip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.limit_gps
     *
     * @return the value of sng_room.limit_gps
     *
     * @mbg.generated
     */
    public Byte getLimitGps() {
        return limitGps;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.limit_gps
     *
     * @param limitGps the value for sng_room.limit_gps
     *
     * @mbg.generated
     */
    public void setLimitGps(Byte limitGps) {
        this.limitGps = limitGps;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.type
     *
     * @return the value of sng_room.type
     *
     * @mbg.generated
     */
    public Byte getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.type
     *
     * @param type the value for sng_room.type
     *
     * @mbg.generated
     */
    public void setType(Byte type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.muck_switch
     *
     * @return the value of sng_room.muck_switch
     *
     * @mbg.generated
     */
    public Integer getMuckSwitch() {
        return muckSwitch;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.muck_switch
     *
     * @param muckSwitch the value for sng_room.muck_switch
     *
     * @mbg.generated
     */
    public void setMuckSwitch(Integer muckSwitch) {
        this.muckSwitch = muckSwitch;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.logo_url
     *
     * @return the value of sng_room.logo_url
     *
     * @mbg.generated
     */
    public String getLogoUrl() {
        return logoUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.logo_url
     *
     * @param logoUrl the value for sng_room.logo_url
     *
     * @mbg.generated
     */
    public void setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl == null ? null : logoUrl.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.op_time
     *
     * @return the value of sng_room.op_time
     *
     * @mbg.generated
     */
    public Integer getOpTime() {
        return opTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.op_time
     *
     * @param opTime the value for sng_room.op_time
     *
     * @mbg.generated
     */
    public void setOpTime(Integer opTime) {
        this.opTime = opTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.server_id
     *
     * @return the value of sng_room.server_id
     *
     * @mbg.generated
     */
    public String getServerId() {
        return serverId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.server_id
     *
     * @param serverId the value for sng_room.server_id
     *
     * @mbg.generated
     */
    public void setServerId(String serverId) {
        this.serverId = serverId == null ? null : serverId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.access_ip
     *
     * @return the value of sng_room.access_ip
     *
     * @mbg.generated
     */
    public String getAccessIp() {
        return accessIp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.access_ip
     *
     * @param accessIp the value for sng_room.access_ip
     *
     * @mbg.generated
     */
    public void setAccessIp(String accessIp) {
        this.accessIp = accessIp == null ? null : accessIp.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.access_port
     *
     * @return the value of sng_room.access_port
     *
     * @mbg.generated
     */
    public Integer getAccessPort() {
        return accessPort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.access_port
     *
     * @param accessPort the value for sng_room.access_port
     *
     * @mbg.generated
     */
    public void setAccessPort(Integer accessPort) {
        this.accessPort = accessPort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.control
     *
     * @return the value of sng_room.control
     *
     * @mbg.generated
     */
    public Integer getControl() {
        return control;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.control
     *
     * @param control the value for sng_room.control
     *
     * @mbg.generated
     */
    public void setControl(Integer control) {
        this.control = control;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.charge
     *
     * @return the value of sng_room.charge
     *
     * @mbg.generated
     */
    public Integer getCharge() {
        return charge;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.charge
     *
     * @param charge the value for sng_room.charge
     *
     * @mbg.generated
     */
    public void setCharge(Integer charge) {
        this.charge = charge;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.source
     *
     * @return the value of sng_room.source
     *
     * @mbg.generated
     */
    public Integer getSource() {
        return source;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.source
     *
     * @param source the value for sng_room.source
     *
     * @mbg.generated
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.source_id
     *
     * @return the value of sng_room.source_id
     *
     * @mbg.generated
     */
    public Integer getSourceId() {
        return sourceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.source_id
     *
     * @param sourceId the value for sng_room.source_id
     *
     * @mbg.generated
     */
    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.tribe_id
     *
     * @return the value of sng_room.tribe_id
     *
     * @mbg.generated
     */
    public Integer getTribeId() {
        return tribeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.tribe_id
     *
     * @param tribeId the value for sng_room.tribe_id
     *
     * @mbg.generated
     */
    public void setTribeId(Integer tribeId) {
        this.tribeId = tribeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sng_room.credit_control
     *
     * @return the value of sng_room.credit_control
     *
     * @mbg.generated
     */
    public Integer getCreditControl() {
        return creditControl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sng_room.credit_control
     *
     * @param creditControl the value for sng_room.credit_control
     *
     * @mbg.generated
     */
    public void setCreditControl(Integer creditControl) {
        this.creditControl = creditControl;
    }
}