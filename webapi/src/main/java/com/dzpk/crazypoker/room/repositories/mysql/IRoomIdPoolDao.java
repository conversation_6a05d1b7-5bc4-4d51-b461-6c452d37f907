package com.dzpk.crazypoker.room.repositories.mysql;

import com.dzpk.crazypoker.room.repositories.mysql.model.RoomIdPoolPo;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IRoomIdPoolDao {
    @Select({"select * from room_id_pool ",
            "where id >= #{fromId} and id <= #{toId} ",
            "and remain/(max-min+1) >= 1-#{threshold} ",
            "order by remain desc limit #{topN}"})
    List<RoomIdPoolPo> selectTopNFromLeastUsed(int fromId, int toId, int topN, double threshold);

    @Select("select * from room_id_pool where id = #{id} for update")
    RoomIdPoolPo selectForUpdate(int id);

    @Update("update room_id_pool set remain = remain - 1 where id = #{id}")
    void consume(int id);
}
