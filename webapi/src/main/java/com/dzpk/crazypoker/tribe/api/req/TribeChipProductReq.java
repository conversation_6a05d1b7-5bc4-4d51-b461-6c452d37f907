package com.dzpk.crazypoker.tribe.api.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Setter
@Getter
@ApiModel(value = "联盟-请求联盟充值包")
public class TribeChipProductReq {

    @ApiModelProperty(name = "平台类型",
            position = 0,
            required = true,
            notes = "1-Android 2-IOS")
    @NotNull(message = "缺少平台类型 !")
    private Integer platformType;

}
