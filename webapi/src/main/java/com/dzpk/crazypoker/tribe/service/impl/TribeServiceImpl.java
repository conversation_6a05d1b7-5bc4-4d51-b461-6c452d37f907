package com.dzpk.crazypoker.tribe.service.impl;

import com.dzpk.crazypoker.appmessage.send.AppBusinessMessageSender;
import com.dzpk.crazypoker.appmessage.send.bean.JoinTribe;
import com.dzpk.crazypoker.appmessage.send.bean.SendOrRecycleTribeChip;
import com.dzpk.crazypoker.appmessage.send.bean.SendOrRecycleTribeClubChip;
import com.dzpk.crazypoker.appmessage.send.bean.TribeChipRecharge;
import com.dzpk.crazypoker.appmessage.utils.AppMessageConstants;
import com.dzpk.crazypoker.club.api.vo.ClubChipLog;
import com.dzpk.crazypoker.club.repositories.mysql.IClubDao;
import com.dzpk.crazypoker.club.repositories.mysql.autogen.mapper.ClubRecordPoMapper;
import com.dzpk.crazypoker.club.repositories.mysql.autogen.model.ClubRecordPo;
import com.dzpk.crazypoker.club.repositories.mysql.autogen.model.ClubRecordPoExample;
import com.dzpk.crazypoker.club.service.IClubService;
import com.dzpk.crazypoker.club.service.bean.ClubRecordBo;
import com.dzpk.crazypoker.common.constant.RespCode;
import com.dzpk.crazypoker.common.dozer.BeanUtil;
import com.dzpk.crazypoker.common.rabbitmq.client.MessageSender;
import com.dzpk.crazypoker.common.rabbitmq.client.bean.CmsMessage;
import com.dzpk.crazypoker.common.rabbitmq.client.bean.OsmMessage;
import com.dzpk.crazypoker.common.rabbitmq.client.bean.TribeMessage;
import com.dzpk.crazypoker.common.rabbitmq.client.bean.cms.TribeJoinApplyParams;
import com.dzpk.crazypoker.common.rabbitmq.client.bean.osm.TribeCreateApplyParams;
import com.dzpk.crazypoker.common.rabbitmq.client.bean.userbalance.UserBalanceSyncMessage;
import com.dzpk.crazypoker.common.rabbitmq.constant.*;
import com.dzpk.crazypoker.common.service.InvokedResult;
import com.dzpk.crazypoker.common.service.exception.ServiceException;
import com.dzpk.crazypoker.common.utils.JsonUtil;
import com.dzpk.crazypoker.oss.service.OssService;
import com.dzpk.crazypoker.tribe.api.req.TribeClubReq;
import com.dzpk.crazypoker.tribe.api.vo.TribeChipConfig;
import com.dzpk.crazypoker.tribe.api.vo.TribeChipLog;
import com.dzpk.crazypoker.tribe.api.vo.TribeChipProductVo;
import com.dzpk.crazypoker.tribe.api.vo.TribeConfig;
import com.dzpk.crazypoker.tribe.constant.ETribeRequestMessageCode;
import com.dzpk.crazypoker.tribe.repositories.mysql.ITribeDao;
import com.dzpk.crazypoker.tribe.repositories.mysql.autogen.mapper.TribeMemberPoMapper;
import com.dzpk.crazypoker.tribe.repositories.mysql.autogen.mapper.TribeRecordPoMapper;
import com.dzpk.crazypoker.tribe.repositories.mysql.autogen.model.*;
import com.dzpk.crazypoker.tribe.repositories.mysql.model.TribeCreateRequestPo;
import com.dzpk.crazypoker.tribe.repositories.mysql.model.TribeJoinOrCreatedPo;
import com.dzpk.crazypoker.tribe.repositories.mysql.model.TribeJoinRequestPo;
import com.dzpk.crazypoker.tribe.repositories.mysql.model.TribeStatusPo;
import com.dzpk.crazypoker.tribe.service.ITribeService;
import com.dzpk.crazypoker.tribe.service.bean.*;
import com.dzpk.crazypoker.tribe.service.transaction.ITribeTransaction;
import com.dzpk.crazypoker.user.service.IUserService;
import com.dzpk.crazypoker.user.service.bo.UserDetailsInfoBo;
import com.dzpk.crazypoker.wallet.api.constant.EChipConsume;
import com.dzpk.crazypoker.wallet.api.constant.EChipConsumeFunction;
import com.dzpk.crazypoker.wallet.api.constant.EChipSource;
import com.dzpk.crazypoker.wallet.api.constant.ETransType;
import com.dzpk.crazypoker.wallet.repositories.mysql.IPlatformConsumptionConfigDao;
import com.dzpk.crazypoker.wallet.repositories.mysql.autogen.mapper.UserAccountPoMapper;
import com.dzpk.crazypoker.wallet.repositories.mysql.autogen.model.UserAccountPo;
import com.dzpk.crazypoker.wallet.repositories.mysql.model.PlatformConsumptionConfigPo;
import com.dzpk.crazypoker.wallet.service.IUserAccountService;
import com.dzpk.crazypoker.wallet.service.IWalletService;
import com.dzpk.crazypoker.wallet.service.bean.ConsumeChipBo;
import com.dzpk.crazypoker.wallet.service.bean.UserAccountBo;
import com.dzpk.crazypoker.tribe.repositories.mysql.autogen.mapper.TribeActivityMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by jayce on 2019/2/25
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TribeServiceImpl implements ITribeService {

    @Autowired
    private ITribeDao tribeDao;

    @Autowired
    private BeanUtil beanUtil;

    @Autowired
    private IClubService clubService;
    @Resource
    private ClubRecordPoMapper clubRecordDao;

    @Autowired
    private IUserService userService;
    @Autowired
    private IUserAccountService accountService;

    @Autowired
    private ITribeTransaction tribeTransaction;

    @Autowired
    private TribeRecordPoMapper tribeRecordPoMapper;

    @Autowired
    private TribeMemberPoMapper tribeMemberPoMapper;

    @Autowired
    private MessageSender messageSender;

    @Autowired
    private IWalletService walletService;

    @Autowired
    private IClubDao clubDao;

    @Autowired
    private IPlatformConsumptionConfigDao platformConsumptionConfigDao;

    @Autowired
    private UserAccountPoMapper userAccountPoMapper;

    @Autowired
    private TribeActivityMapper tribeActivityMapper;

    private List<TribeConfig> list = new ArrayList<>();
    private List<TribeChipConfig> chipList = new ArrayList<>();

    @Value("${tribe.levelConfig}")
    private String tribeConfig;

    @Value("${tribe.chipConfig}")
    private String tribeChipConfig;
    @Autowired
    private AppBusinessMessageSender appBusinessMessageSender;

    @Autowired
    private OssService ossService;

    /**
     * 联盟等级兑换比例
     * @return
     */
    @Override
    public Map getConfigAll(int userId, int tribeId){

        //获取联盟数据
        TribeRecordPo tribe = tribeRecordPoMapper.selectByPrimaryKey(tribeId);
        TribeDetailBo tribeDetail = this.beanUtil.map(tribe,TribeDetailBo.class);
        UserAccountBo byId = accountService.findById(userId);
        tribeDetail.setChip((byId.getChip()+byId.getNotExtractChip()));
        Map<String,Object> retMap = new HashMap<>();
        retMap.put("list",getConfigAll());
        retMap.put("detail",tribeDetail);
        return retMap;
    }

    private List<TribeConfig> getConfigAll(){
        if(list.size()==0){
            String[] split = tribeConfig.split(";");
            for (String s : split) {
                String[] sp = s.split(",");
                TribeConfig config = new TribeConfig();
                config.setTribeLevel(Integer.parseInt(sp[0]));
                config.setClubNum(Integer.parseInt(sp[1]));
                config.setChip(Integer.parseInt(sp[2]));
                list.add(config);
            }
        }
        return list;
    }

    /**
     * 联盟币兑换比例
     * @return
     */

    public List<TribeChipConfig> getChipAll(){
        if(chipList.size()==0){
            String[] split = tribeChipConfig.split(";");
            for (String s : split) {
                String[] sp = s.split(",");
                TribeChipConfig config = new TribeChipConfig();
                config.setTribeChip(Integer.parseInt(sp[0]));
                config.setChip(Integer.parseInt(sp[1]));
                chipList.add(config);
            }
        }
        return chipList;
    }
    @Override
    public Map getChipAll(int userid){
        UserAccountBo byId = accountService.findById(userid);
        Map<String,Object> retMap = new HashMap<>();
        retMap.put("list",getChipAll());
        retMap.put("userChip",(byId.getChip()+byId.getNotExtractChip()));
        return retMap;
    }

    @Override
    public List<TribeChipProductVo> getTribeChipProduct(int userId, int platformType) {
        List<TribeChipProductPo> result = tribeDao.getTribeChipProductByPlatform(platformType);
        return beanUtil.map(result, TribeChipProductVo.class);
    }

    @Override
    public InvokedResult create(int userId, int clubId, String tribeName, String phone, String wechat, String telegram, String email, String message, String head, Integer useCustom, String customUrl) {
        InvokedResult result = new InvokedResult();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        try {
            if (phone.isEmpty() && wechat.isEmpty() && telegram.isEmpty() && email.isEmpty() && message.isEmpty()) {//必要参数错误
                result.setCode(RespCode.TRIBE_PARAM_ERROR.getCode());
                result.setMsg(RespCode.TRIBE_PARAM_ERROR.getDesc());
                return result;
            }

            ClubRecordBo club = clubService.getClub(clubId);

            if(null == club){//主机不存在
                result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
                result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
                return result;
            }

            if(Integer.parseInt(club.getCreator()) != userId){//无权限操作
                result.setCode(RespCode.CLUB_PERMISSION_ERROR.getCode());
                result.setMsg(RespCode.CLUB_PERMISSION_ERROR.getDesc());
                return result;
            }

            //判断是否有创建请求
            if(tribeDao.getCreateTribeRequestNums(userId) > 0){
                result.setCode(RespCode.TRIBE_CREATING.getCode());
                result.setMsg(RespCode.TRIBE_CREATING.getDesc());
                return result;
            }

            List<TribeJoinOrCreatedPo> tribeList = tribeDao.getJoinAndCreatedTribe(clubId);
            if(null != tribeList && tribeList.size() > 0){//俱乐部已经创建了联盟
                result.setCode(RespCode.TRIBE_HAS.getCode());
                result.setMsg(RespCode.TRIBE_HAS.getDesc());
                return result;
            }

            tribeName = tribeName.trim();
            // 判断是否已存在该同盟
            int tribeNums = tribeDao.checkNameInTribe(tribeName);
            if(tribeNums > 0){//同盟名称冲突
                result.setCode(RespCode.TRIBE_NAME_EXIST.getCode());
                result.setMsg(RespCode.TRIBE_NAME_EXIST.getDesc());
                return result;
            }
            List<TribeConfig> configAll = getConfigAll();
            int clubCount = 0;
            for (TribeConfig config : configAll) {
                if(config.getTribeLevel().equals(1)){
                    clubCount = config.getClubNum();
                    break;
                }
            }

            //扣除鑽石
            EChipConsumeFunction consumeFunction = EChipConsumeFunction.CREATE_TRIBE;
            PlatformConsumptionConfigPo configPo = platformConsumptionConfigDao.findById(consumeFunction.getId());
            UserAccountPo userAccountPo = userAccountPoMapper.selectByPrimaryKey(userId);
            Integer cp = userAccountPo.getChip();
            Integer fee = configPo.getQuantity() * 100;

            if(fee != 0 && fee > cp){
                log.error("用户：{}，钻石数量不够，无法创建俱乐部！", userId);
                result.setCode(RespCode.USER_CHIP_ILLEGAL.getCode());
                result.setMsg(RespCode.USER_CHIP_ILLEGAL.getDesc());
                return result;
            }

            walletService.consumeChip(userId, ConsumeChipBo.builder()
                        .chip(fee)
                        .consume(consumeFunction.getConsumeType())
                        .description(consumeFunction.getConsumeType().getDesc())
                        .opId(userId)
                        .source(EChipSource.API)
                        .build(), ETransType.NULL.getCode());

            //生成审批请求
            TribeCreateRequestPo tribeCreateRequestPo = TribeCreateRequestPo.builder().tribeName(tribeName).clubCount(clubCount+"").
                        tribeHead(head).useCustom(useCustom).customUrl(customUrl).phone(phone).wechat(wechat).telegram(telegram).email(email).message(message).fee(fee.toString()).build();

            String insertMsgId = tribeTransaction.addTribeRequest("",String.valueOf(userId),String.valueOf(clubId),
                    JsonUtil.toJson(tribeCreateRequestPo,false),ETribeRequestMessageCode.TRIBE_CREATE.getCode());
            if(insertMsgId.isEmpty()){
                return result;
            }
            //通知osm
            messageSender.sendOsmMessage(OsmMessage.builder().senderId(String.valueOf(userId)).type(EOsmMessageCode.TRIBE_CREATE_APPLY.getCode())
                    .params(JsonUtil.toJson(TribeCreateApplyParams.builder().applyMsgId(insertMsgId).clubId(clubId).clubName(club.getName()).tribeName(tribeName).build(),false)).build());


        } catch (Exception e) {
            log.error("create error", e);
        }
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());

        return result;
    }

    @Override
    public InvokedResult<TribeMineBo> getJoinAndCreatedTribe(int userId, int clubId) {
        InvokedResult<TribeMineBo> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        try {

            ClubRecordBo club = clubService.getClub(clubId);

            if(null == club){//主机不存在
                result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
                result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
                return result;
            }

            if(Integer.parseInt(club.getCreator()) != userId){//无权限操作
                result.setCode(RespCode.CLUB_PERMISSION_ERROR.getCode());
                result.setMsg(RespCode.CLUB_PERMISSION_ERROR.getDesc());
                return result;
            }

            TribeMineBo data = new TribeMineBo();
            data.setCanCreateTribe(1);//可以创建联盟

            //判断是否有创建请求
            if(tribeDao.getCreateTribeRequestNums(userId) > 0){
                data.setCanCreateTribe(0);//不可以点击创建或加入
                data.setHasCreateTribeRequest(true);
            }

            //是否有申请加入联盟或者创建联盟的请求
            if(tribeDao.getJoinTribeRequestByClubId(clubId) > 0){
                data.setCanCreateTribe(0);//不可以点击创建或加入
            }

            List<TribeJoinOrCreatedPo> tribeList = tribeDao.getJoinAndCreatedTribe(clubId);

            if(null != tribeList && tribeList.size() > 0){
                data.setCanCreateTribe(0);//不可以点击创建或加入
                //返回加入的联盟
                TribeMineBo temp = this.beanUtil.map(tribeList.get(0),TribeMineBo.class);
                data.setRandomId(temp.getRandomId());
                data.setTribeId(temp.getTribeId());
                data.setTribeName(temp.getTribeName());
                data.setTribeHead(temp.getTribeHead());
            }
            result.setData(data);

            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());

        } catch (Exception e) {
            log.error("get group error", e);
        }

        return result;
    }

    @Override
    public InvokedResult<TribeDetailBo> getTribeDetail(int userId, int tribeId) {
        InvokedResult<TribeDetailBo> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        // 联盟创建者或联盟下的俱樂部创建者才有权限查看详情
        if(tribeDao.checkTribeClubPermission(userId, tribeId) <= 0){//身份权限不合法
            result.setCode(RespCode.TRIBE_PERMISSION_ERROR.getCode());
            result.setMsg(RespCode.TRIBE_PERMISSION_ERROR.getDesc());
            return result;
        }

        TribeRecordPo tribe = tribeRecordPoMapper.selectByPrimaryKey(tribeId);
        TribeDetailBo tribeDetail = this.beanUtil.map(tribe,TribeDetailBo.class);

        UserDetailsInfoBo userBo = userService.findById(Integer.parseInt(tribeDetail.getCreator()));
//        UserAccountBo byId = accountService.findById(userId);
//        tribeDetail.setChip((byId.getChip()+byId.getNotExtractChip())/100);
        String name = userBo.getNickName();
        tribeDetail.setCreator(name);//取出来的是id，换成名称
        tribeDetail.setClubChipTotal(tribeDao.getClubChipTotal(tribeId));

        result.setData(tribeDetail);
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());
        return result;
    }

    @Override
    public InvokedResult<List<TribeMemberListBo>> getTribeMemberList(int userId, int tribeId) {
        InvokedResult<List<TribeMemberListBo>> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        // 联盟创建者或联盟下的俱樂部创建者才有权限查看详情
        if(tribeDao.checkTribeClubPermission(userId, tribeId) <= 0){//身份权限不合法
            result.setCode(RespCode.TRIBE_PERMISSION_ERROR.getCode());
            result.setMsg(RespCode.TRIBE_PERMISSION_ERROR.getDesc());
            return result;
        }

        // 加载俱乐部返回
        TribeMemberPoExample example = new TribeMemberPoExample();
        example.or().andTribeIdEqualTo(tribeId);
        List<TribeMemberPo> tribeMemberDataList = tribeMemberPoMapper.selectByExample(example);
        if(null == tribeMemberDataList || tribeMemberDataList.isEmpty()){
            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());
            return result;
        }

        List<TribeMemberBo> tribeMemberList = this.beanUtil.map(tribeMemberDataList,TribeMemberBo.class);
        Set<Integer> clubIdList = tribeMemberList.stream().map(TribeMemberBo::getClubId).collect(Collectors.toSet());
        Map<Integer, Short> tribeMemberStatusMap = tribeMemberList.stream().collect(Collectors.toMap(TribeMemberBo::getClubId,TribeMemberBo::getStatus));
//        Map<Integer,Integer> tribeMemberTypeMap = tribeMemberList.stream().collect(Collectors.toMap(TribeMemberBo::getClubId,TribeMemberBo::getType));
        List<TribeMemberListBo> clubList = this.beanUtil.map(clubService.getClubs(clubIdList),TribeMemberListBo.class);
        Set<Integer> creatorIdList = clubList.stream().map(TribeMemberListBo::getCreator).map(Integer::parseInt).collect(Collectors.toSet());
        Map<Integer, String> creatorMap = userService.findByIds(creatorIdList).stream().collect(Collectors.toMap(UserDetailsInfoBo::getUserId, UserDetailsInfoBo::getNickName));
        Map<Integer, Long> clubMembersChipTotal = clubService.getClubMembersChipTotal(clubIdList);
        //app 不显示踢出和退出联盟的功能
        for(TribeMemberListBo d : clubList){
            d.setClubPleNum(d.getClubMembers() + "/" + d.getUpperLimit());
//            d.setStatus(tribeMemberStatusMap.get(d.getId()) == 1 ? 0 : 1);//0正常 1转移中
            d.setStatus(tribeMemberStatusMap.get(d.getId()).intValue());
            d.setCreator(creatorMap.get(Integer.parseInt(d.getCreator()))); // translate user id to name
            Long chipTotal = clubMembersChipTotal.get(d.getId());
            d.setClubMembersChipTotal(chipTotal != null ? chipTotal : 0);
//            int memberType = tribeMemberTypeMap.get(d.getId());
//            if(memberType == 1){//是否显示踢出按钮
//                d.setShowKick(0);
//            }else if (memberType == 2){
//                d.setShowKick(1);
//            }
        }
        result.setData(clubList);
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());

        return result;
    }

    @Override
    public InvokedResult removeMember(int userId,int kickClubId,int tribeId) {
        InvokedResult result = new InvokedResult();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        if(tribeDao.checkTribePermission(userId, tribeId) <= 0){//身份权限不合法
            result.setCode(RespCode.TRIBE_PERMISSION_ERROR.getCode());
            result.setMsg(RespCode.TRIBE_PERMISSION_ERROR.getDesc());
            return result;
        }

        TribeMemberPoExample tmExample = new TribeMemberPoExample();
        tmExample.or().andClubIdEqualTo(kickClubId).andTribeIdEqualTo(tribeId);
        List<TribeMemberPo> tmList = tribeMemberPoMapper.selectByExample(tmExample);
        if(null == tmList || tmList.size() <= 0){//俱乐部不存在于目标联盟中
            result.setCode(RespCode.TRIBE_NOT_EXIST_CLUB.getCode());
            result.setMsg(RespCode.TRIBE_NOT_EXIST_CLUB.getDesc());
            return result;
        }

        //踢出俱乐部
        TribeRecordPoExample trExample = new TribeRecordPoExample();
        trExample.or().andIdEqualTo(tribeId);
        TribeRecordPo tribe = tribeRecordPoMapper.selectByExample(trExample).get(0);
        if(tribe.getClubId() == kickClubId){//盟主不能踢出自己
            result.setCode(RespCode.TRIBE_REMOVE_LOGIC_ERROR.getCode());
            result.setMsg(RespCode.TRIBE_REMOVE_LOGIC_ERROR.getDesc());
            return result;
        }

        //内部实现出错会抛异常回滚，下面可以不用判断返回值
        tribeTransaction.removeTribeMember(kickClubId,tribeId);
        // 通知玩家 被踢出的俱乐部创建者 和 运营方
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());

        return result;
    }

    @Override
    public InvokedResult<List<TribeSearchBo>> searchTribe(int userId,int clubId, String key) {
        InvokedResult<List<TribeSearchBo>> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        //校验俱乐部身份合法
        boolean checkStatus = clubService.checkClubPermisson(userId, clubId);
        if(!checkStatus){
            result.setCode(RespCode.CLUB_PERMISSION_ERROR.getCode());
            result.setMsg(RespCode.CLUB_PERMISSION_ERROR.getDesc());
            return result;
        }

        //查找对应id的联盟
        List<TribeSearchBo> resultList = new ArrayList<>();
        TribeRecordPo tribeRecordPo = tribeDao.getTribeByRandomId(key,"");
        if(null != tribeRecordPo){
            resultList.add(this.beanUtil.map(tribeRecordPo,TribeSearchBo.class));
            resultList.forEach(t -> {
                int applications = tribeDao.getJoinTribeRequestByTribeId(t.getId(), clubId);
                t.setApplying(applications > 0);
            });
        }

        result.setData(resultList);
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());

        return result;
    }

    @Override
    public InvokedResult applyJoinTribe(int userId, int clubId, int tribeId) {
        InvokedResult result = new InvokedResult();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        // 1、确认俱乐部权限
        boolean checkStatus = clubService.checkClubPermisson(userId, clubId);
        if(!checkStatus){
            result.setCode(RespCode.CLUB_PERMISSION_ERROR.getCode());
            result.setMsg(RespCode.CLUB_PERMISSION_ERROR.getDesc());
            return result;
        }

        //2、确认是否有加入的联盟
        boolean hasTribe = checkClubHasTribe(clubId);
        if(hasTribe){
            result.setCode(RespCode.TRIBE_HAS.getCode());
            result.setMsg(RespCode.TRIBE_HAS.getDesc());
            return result;
        }

        //判断是否有创建请求
        if(tribeDao.getCreateTribeRequestNums(userId) > 0){
            result.setCode(RespCode.TRIBE_CREATING.getCode());
            result.setMsg(RespCode.TRIBE_CREATING.getDesc());
            return result;
        }

        //是否有向同个联盟申请的请求
        if(tribeDao.getJoinTribeRequestByTribeId(tribeId, clubId) > 0){
            result.setCode(RespCode.TRIBE_HAS_REQUEST.getCode());
            result.setMsg(RespCode.TRIBE_HAS_REQUEST.getDesc());
            return result;
        }


        TribeRecordPo tribe = tribeDao.getTribeByTribeId(tribeId);
        ClubRecordBo club = clubService.getClub(clubId);
        if(null != tribe && null != club){
            // 1、新建申请消息
            TribeJoinRequestPo tribeJoinRequestPo = TribeJoinRequestPo.builder().tribeName(tribe.getTribeName()).clubName(club.getName()).build();
            String insertMsgId = tribeTransaction.addTribeRequest(String.valueOf(tribeId),String.valueOf(userId),String.valueOf(clubId),JsonUtil.toJson(tribeJoinRequestPo,false),ETribeRequestMessageCode.TRIBE_JOIN_REQUEST.getCode());
            if(insertMsgId.isEmpty()){
                return result;
            }
            //2、发到mq队列通知审批方
            List<String> reciverIds = new ArrayList<>();
            reciverIds.add(tribe.getCreator());
            TribeMessage tribeMessage = TribeMessage.builder().reciverUserIds(reciverIds).content(club.getName()).remark(tribe.getTribeName()).
                    type(EMessageCode.TRIBE_JOIN_REQUEST.getCode()).pushChannel(EMessageChannelCode.ALL.getCode()).build();
            String mqMsgId = messageSender.sendTribeMessage(tribeMessage);
            log.info(String.format("applyJoinTribe => insertMsgId:%s => mqMsgId:%s ,msg=>%s",insertMsgId,mqMsgId,tribeMessage.toString()));
            //3、通知cms
            messageSender.sendCmsMessage(CmsMessage.builder().senderId(String.valueOf(userId)).type(ECmsMessageCode.TRIBE_JOIN_APPLY.getCode())
                    .params(JsonUtil.toJson(TribeJoinApplyParams.builder().applyMsgId(insertMsgId).clubId(clubId).clubName(club.getName()).
                            clubRandomId(String.valueOf(club.getRandomId())).tribeId(tribe.getId()).tribeName(tribe.getTribeName()).build(),false)).build());

            // 20241128 新增发送app消息
            appBusinessMessageSender.notifyJoinTribeApply(JoinTribe.builder()
                    .clubId(Long.valueOf(club.getId()))
                    .clubName(club.getName())
                    .clubOwnerId(Long.valueOf(club.getCreator()))
                    .tribeId(Long.valueOf(tribe.getId()))
                    .tribeName(tribe.getTribeName())
                    .tribeOwnerId(Long.valueOf(tribe.getCreator()))
                    .build());

            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());
        }

        return result;
    }

    @Override
    public Integer updateTribeMemberCount(int num, int clubId) {
        return tribeTransaction.updateTribeMemberCount(num, clubId);
    }

    /**
     * 根据创建者id获取联盟信息
     *
     * @param creatorId
     * @return
     */
    @Override
    public TribeRecordBo findByCreatorId(Integer creatorId) {
        if (creatorId == null) {
            return null;
        }
        TribeRecordPoExample example = new TribeRecordPoExample();

        example.or().andCreatorEqualTo(creatorId.toString());

        List<TribeRecordPo> tribeRecordPos = tribeRecordPoMapper.selectByExample(example);

        return tribeRecordPos.size() == 0 ? null : beanUtil.map(tribeRecordPos.get(0), TribeRecordBo.class);
    }

    /**
     * 根据俱联盟id,查找成员信息
     *
     * @param tribeId
     * @return
     */
    @Override
    public List<TribeMemberBo> findTribeMemberByTribeId(Integer tribeId) {
        if (tribeId == null) {
            return new ArrayList<>(0);
        }

        TribeMemberPoExample example = new TribeMemberPoExample();

        example.or().andTribeIdEqualTo(tribeId);
        List<TribeMemberPo> list = tribeMemberPoMapper.selectByExample(example);
        return beanUtil.map(list, TribeMemberBo.class);
    }

    /**
     * 根据俱乐部id获取俱乐部所处联盟状态，以及俱乐部在联盟中状态
     * @param clubId 俱乐部id
     * @return null则是俱乐部没有归属联盟
     */
    @Override
    public TribeStatusBo findTribeStatusByClubId(Integer clubId) {
        TribeStatusPo tribeStatusPo = tribeDao.findTribeStatusByClubId(clubId);
        TribeStatusBo tribeStatusBo = null;
        if(null != tribeStatusPo){
            tribeStatusBo = this.beanUtil.map(tribeStatusPo,TribeStatusBo.class);
        }
        return tribeStatusBo;
    }

    @Override
    public boolean checkClubHasTribe(Integer clubId) {
        List<TribeJoinOrCreatedPo> tribeList = tribeDao.getJoinAndCreatedTribe(clubId);

        if(null == tribeList || (tribeList != null && tribeList.size() > 0)){//已经有加入的联盟
            return true;
        }
        return false;
    }

    @Transactional
    @Override
    public InvokedResult<Object> modify(int userId, int tribeId, String content, int modifyType) {
        InvokedResult<Object> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());
        try {
            // check if tribe exists
            TribeRecordPo tribeRecord = tribeDao.getTribeByTribeId(tribeId);
            if (null == tribeRecord) {
                result.setCode(RespCode.TRIBE_NOT_EXIST.getCode());
                result.setMsg(RespCode.TRIBE_NOT_EXIST.getDesc());
                return result;
            }

            // check if user is the tribe creator
            if(tribeDao.checkTribePermission(userId, tribeId) <= 0){//身份权限不合法
                result.setCode(RespCode.TRIBE_PERMISSION_ERROR.getCode());
                result.setMsg(RespCode.TRIBE_PERMISSION_ERROR.getDesc());
                return result;
            }

            if (modifyType == 1) {
                // check if tribe name already exists
                String newName = content.trim();
                if(tribeDao.checkNameInTribe(newName) > 0){//同盟名称冲突
                    result.setCode(RespCode.TRIBE_NAME_EXIST.getCode());
                    result.setMsg(RespCode.TRIBE_NAME_EXIST.getDesc());
                    return result;
                }

                //判断是否为第一次修改
                // if(tribeRecord.getModifyNameTimes() > 0 ){
                    // debit user balance
                    EChipConsumeFunction consumeFunction = EChipConsumeFunction.RENAME_TRIBE;
                    PlatformConsumptionConfigPo configPo = platformConsumptionConfigDao.findById(consumeFunction.getId());
                    UserAccountPo userAccountPo = userAccountPoMapper.selectByPrimaryKey(userId);
                    Integer cp = userAccountPo.getChip();
                    Integer fee = configPo.getQuantity() * 100;
        
                    if(fee != 0 && fee > cp){
                        log.error("用户：{}，钻石数量不够，聯盟無法法名！", userId);
                        result.setCode(RespCode.USER_CHIP_ILLEGAL.getCode());
                        result.setMsg(RespCode.USER_CHIP_ILLEGAL.getDesc());
                        return result;
                    }

                    walletService.consumeChip(userId, ConsumeChipBo.builder()
                            .chip(fee)
                            .consume(consumeFunction.getConsumeType())
                            .description(consumeFunction.getConsumeType().getDesc())
                            .opId(userId)
                            .source(EChipSource.API)
                            .build(), ETransType.NULL.getCode());
                // }

                // update tribe info
                if (0 < tribeDao.updateTribeName(tribeId, newName)) {
                    log.info("联盟改名成功, tribeId={} oldName={} newName={}", tribeId, tribeRecord.getTribeName(), newName);
                }
            }

            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());
        } catch (ServiceException ex) {
            result.setCode(ex.getCode());
            result.setMsg(ex.getMsg());
        }
        return result;
    }

    @Transactional
    @Override
    public InvokedResult<Object> setTribeHead(int userId, int tribeId, String head) {
        InvokedResult<Object> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());
        try {
            // check if tribe exists
            TribeRecordPo tribeRecord = tribeDao.getTribeByTribeId(tribeId);
            if (null == tribeRecord) {
                result.setCode(RespCode.TRIBE_NOT_EXIST.getCode());
                result.setMsg(RespCode.TRIBE_NOT_EXIST.getDesc());
                return result;
            }

            // check if user is the tribe creator
            if(tribeDao.checkTribePermission(userId, tribeId) <= 0){//身份权限不合法
                result.setCode(RespCode.TRIBE_PERMISSION_ERROR.getCode());
                result.setMsg(RespCode.TRIBE_PERMISSION_ERROR.getDesc());
                return result;
            }
            if (tribeRecord.getUseCustom().equals(1)) {
                if (!StringUtils.isEmpty(tribeRecord.getCustomUrl())) {
                    // 删除旧资源
//                    ossService.deleteFile(tribeRecord.getCustomUrl());
                }
            }
            // update tribe info
            if (0 < tribeDao.setTribeHead(tribeId, head)) {
                log.info("联盟设置头像成功, tribeId={} oldHead={} head={}", tribeId, tribeRecord.getHead(), head);
                tribeRecord.setHead(head);
                TribeDetailBo tribeDetail = this.beanUtil.map(tribeRecord,TribeDetailBo.class);
                result.setData(tribeDetail);
                result.setCode(RespCode.SUCCEED.getCode());
                result.setMsg(RespCode.SUCCEED.getDesc());
            }
        } catch (ServiceException ex) {
            result.setCode(ex.getCode());
            result.setMsg(ex.getMsg());
        }
        return result;
    }

    @Override
    public InvokedResult<TribeDetailBo> updateTribeCustomHead(int userId, int tribeId, String head) {
        InvokedResult<TribeDetailBo> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());
        try {
            // check if tribe exists
            TribeRecordPo tribeRecord = tribeDao.getTribeByTribeId(tribeId);
            if (null == tribeRecord) {
                result.setCode(RespCode.TRIBE_NOT_EXIST.getCode());
                result.setMsg(RespCode.TRIBE_NOT_EXIST.getDesc());
                return result;
            }

            // check if user is the tribe creator
            if(tribeDao.checkTribePermission(userId, tribeId) <= 0){//身份权限不合法
                result.setCode(RespCode.TRIBE_PERMISSION_ERROR.getCode());
                result.setMsg(RespCode.TRIBE_PERMISSION_ERROR.getDesc());
                return result;
            }
            if (tribeRecord.getUseCustom().equals(1)) {
                if (!StringUtils.isEmpty(tribeRecord.getCustomUrl())) {
                    // 删除旧资源
//                    ossService.deleteFile(tribeRecord.getCustomUrl());
                }
            }
            // update tribe info
            if (0 < tribeDao.setTribeCustomHead(tribeId, head)) {
                log.info("联盟修改头像成功, tribeId={} oldHead={} head={}", tribeId, tribeRecord.getHead(), head);
                tribeRecord.setHead(head);
                TribeDetailBo tribeDetail = this.beanUtil.map(tribeRecord,TribeDetailBo.class);
                result.setData(tribeDetail);
                result.setCode(RespCode.SUCCEED.getCode());
                result.setMsg(RespCode.SUCCEED.getDesc());
            }
        } catch (ServiceException ex) {
            result.setCode(ex.getCode());
            result.setMsg(ex.getMsg());
        }
        return result;
    }

    @Transactional
    @Override
    public InvokedResult upgrade(int userId, TribeClubReq request) {
        InvokedResult result = new InvokedResult();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());
        // 联盟创建者才有权限查看详情
        if(tribeDao.checkTribePermission(userId, request.getTribeId()) <= 0){//身份权限不合法
            result.setCode(RespCode.TRIBE_PERMISSION_ERROR.getCode());
            result.setMsg(RespCode.TRIBE_PERMISSION_ERROR.getDesc());
            return result;
        }
        TribeRecordPo tribe = tribeRecordPoMapper.selectByPrimaryKey(request.getTribeId());

        int level = tribe.getLevel();//当前等级

        if(level>= request.getLevel()){
            result.setCode(RespCode.TRIBE_CANNOT_REPEAT_UPGRADE.getCode());
            result.setMsg(RespCode.TRIBE_CANNOT_REPEAT_UPGRADE.getDesc());
            return result;
        }

        int levelNew = 0;
        int chipNew = 0;
        int count = 0;
        List<TribeConfig> configAll = getConfigAll();
        for (TribeConfig config : configAll) {
            if(config.getTribeLevel()> level && config.getTribeLevel() <= request.getLevel()){
                levelNew = config.getTribeLevel();
                chipNew += config.getChip();
                count = config.getClubNum();
            }
        }
        if(levelNew  == 0){
            result.setCode(RespCode.TRIBE_ALREADY_MAX_LEVEL.getCode());
            result.setMsg(RespCode.TRIBE_ALREADY_MAX_LEVEL.getDesc());
            return result;
        }
        UserAccountBo byId = accountService.findById(userId);
        int userChip = (byId.getChip()+byId.getNotExtractChip())/100;
        if(userChip< chipNew){
            result.setCode(RespCode.TRIBE_CHIP_REQUEST.getCode());
            result.setMsg(RespCode.TRIBE_CHIP_REQUEST.getDesc());
            return result;
        }

        walletService.consumeChip(userId,
                ConsumeChipBo.builder()
                        .chip(chipNew * 100)
                        .consume(EChipConsume.TRIBE_LEVEL)
                        .description("升级联盟")
                        .opId(userId)
                        .source(EChipSource.API)
                        .build(), ETransType.NULL.getCode()
        );
        TribeRecordPo po = new TribeRecordPo();
        po.setId(tribe.getId());
        po.setLevel(levelNew);
        po.setClubLimit(count);
        tribeRecordPoMapper.updateByPrimaryKeySelective(po);
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());
        return result;
    }

    @Transactional
    @Override
    public InvokedResult<Object> exchange(int userId, TribeClubReq request) {
        InvokedResult<Object> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());
        // 联盟创建者才有权限查看详情
        if(tribeDao.checkTribePermission(userId, request.getTribeId()) <= 0){//身份权限不合法
            result.setCode(RespCode.TRIBE_PERMISSION_ERROR.getCode());
            result.setMsg(RespCode.TRIBE_PERMISSION_ERROR.getDesc());
            return result;
        }

        TribeChipProductPo product = tribeDao.getTribeChipProductById(request.getProductId());
        if(product == null){
            return result;
        }
        int chipNeeded = product.getPrice();
        int tribeChip = product.getChips() + product.getDiscount();

        UserAccountBo userAccount = accountService.findById(userId);
        int userChip = userAccount.getChip() + userAccount.getNotExtractChip();
        if(userChip < chipNeeded){
            result.setCode(RespCode.TRIBE_CHIP_REQUEST.getCode());
            result.setMsg(RespCode.TRIBE_CHIP_REQUEST.getDesc());
            return result;
        }

        int feeRate = tribeDao.getRechargeFeeRate(request.getTribeId());
        if (feeRate < 0) throw new ServiceException(RespCode.FAILED.getCode(), RespCode.FAILED.getDesc());

        int chipSpent = (int) Math.round(chipNeeded * (1.0 + feeRate / 100.0));

        walletService.consumeChip(userId,
                ConsumeChipBo.builder()
                        .chip(chipSpent)
                        .consume(EChipConsume.TRIBE_RECHARGE_FUND)
                        .description(String.format("联盟币兑换 (%d%% 费率)", feeRate))
                        .opId(userId)
                        .source(EChipSource.API)
                        .build(), ETransType.NULL.getCode()
        );

        TribeRecordPo tribe = tribeRecordPoMapper.selectByPrimaryKey(request.getTribeId());
        TribeRecordPo po = new TribeRecordPo();
        po.setId(tribe.getId());
        po.setChip(tribeChip + tribe.getChip());
        tribeRecordPoMapper.updateByPrimaryKeySelective(po);
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());

        //充值记录
        TribeChipLog log = new TribeChipLog();
        log.setTribeId(request.getTribeId());
        log.setType(TribeChipLog.Type.RECHARGE.getValue());
        log.setChip(tribeChip);
        log.setChipBefore(tribe.getChip());
        log.setCreateTime(new Date());
//        log.setOtherId();
        tribeDao.insertLog(log);

        // 通過 res 服務異步通知前端帳戶變化
        messageSender.sendUserBalanceSyncMessage(UserBalanceSyncMessage.builder()
                .userId(userId)
                .balance(userChip - chipSpent)
                .coinType(UserBalanceSyncMessage.CoinType.DIAMOND)
                .timestamp(System.currentTimeMillis())
                .build());

        // 发送app消息
        appBusinessMessageSender.notifyTribeChipRecharge(TribeChipRecharge.builder()
                .tribeId(tribe.getId().longValue())
                .tribeName(tribe.getTribeName())
                .tribeOwnerId(Long.valueOf(tribe.getCreator()))
                .tribeCoinAmount(tribeChip)
                .build());

        return result;
    }

    @Transactional
    @Override
    public InvokedResult grant(int userId, TribeClubReq request) {

        InvokedResult result = new InvokedResult();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());
        // 联盟创建者才有权限查看详情
        if(tribeDao.checkTribePermission(userId, request.getTribeId()) <= 0){//身份权限不合法
            result.setCode(RespCode.TRIBE_PERMISSION_ERROR.getCode());
            result.setMsg(RespCode.TRIBE_PERMISSION_ERROR.getDesc());
            return result;
        }

        TribeRecordPoExample example = new TribeRecordPoExample();
        example.or().andCreatorEqualTo(userId+"");
        List<TribeRecordPo> tribes = tribeRecordPoMapper.selectByExample(example);
        if(tribes.size()==0){
            return result;
        }
        TribeRecordPo tribe = tribes.get(0);
        int tribeChipRequested = request.getTribeChip();
        if(tribe.getChip() < tribeChipRequested){
            result.setCode(RespCode.TRIBE_CHIP_NOT_ENOUGH_FOR_GRANT.getCode());
            result.setMsg(RespCode.TRIBE_CHIP_NOT_ENOUGH_FOR_GRANT.getDesc());
            return result;
        }
        ClubRecordPoExample exampleC = new ClubRecordPoExample();
        exampleC.or().andRandomIdEqualTo(request.getClubId());
        List<ClubRecordPo> clubRecordPoList = clubRecordDao.selectByExample(exampleC);
        if(clubRecordPoList.size()==0){
            result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
            result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
            return result;
        }
        ClubRecordPo clubRecordPo = clubRecordPoList.get(0);

        TribeMemberPoExample poExample = new TribeMemberPoExample();
        poExample.or().andTribeIdEqualTo(tribe.getId()).andClubIdEqualTo(clubRecordPo.getId());

        List<TribeMemberPo> tribeMemberPos = tribeMemberPoMapper.selectByExample(poExample);
        if(tribeMemberPos.size()==0){
            result.setCode(RespCode.TRIBE_CLUB_NOT_MEMBER.getCode());
            result.setMsg(RespCode.TRIBE_CLUB_NOT_MEMBER.getDesc());
            return result;
        }


        ClubRecordPo recordPo = new ClubRecordPo();
        recordPo.setChip(clubRecordPo.getChip() + tribeChipRequested);
        recordPo.setId(clubRecordPo.getId());
        clubRecordDao.updateByPrimaryKeySelective(recordPo);

        TribeRecordPo po = new TribeRecordPo();
        po.setId(tribe.getId());
        po.setChip(tribe.getChip() - tribeChipRequested);
        tribeRecordPoMapper.updateByPrimaryKeySelective(po);

        //发放记录
        TribeChipLog log = new TribeChipLog();
        log.setTribeId(request.getTribeId());
        log.setType(TribeChipLog.Type.GRANT.getValue());
        log.setChip(-tribeChipRequested);
        log.setChipBefore(tribe.getChip());
        log.setCreateTime(new Date());
        log.setOtherId(clubRecordPo.getRandomId()+"");
        log.setClubRandomId(clubRecordPo.getRandomId()+"");
        log.setClubId(clubRecordPo.getId());
        log.setOperatorId(-1);
        tribeDao.insertLog(log);

        //俱樂部充值記錄
        ClubChipLog clog = new ClubChipLog();
        clog.setClubId(clubRecordPo.getId());
        clog.setType(ClubChipLog.Type.RECHARGE.getValue());
        clog.setChip(tribeChipRequested);
        clog.setChipBefore(clubRecordPo.getChip());
        clog.setCreateTime(new Date());
        clog.setOtherId(tribe.getRandomId()+"");
        clog.setTribeRandomId(tribe.getRandomId()+"");
        clubDao.insertLog(clog);

        // 发送app消息
        appBusinessMessageSender.notifySendOrRecycleClubTribeChip(SendOrRecycleTribeClubChip.builder()
                .tplCode(AppMessageConstants.TplCode.WALLET0012)
                .clubName(clubRecordPo.getName())
                .clubId(clubRecordPo.getId().longValue())
                .clubOwnerId(Long.valueOf(clubRecordPo.getCreator()))
                .tribeId(tribe.getId().longValue())
                .tribeName(tribe.getTribeName())
                .tribeOwnerId(Long.valueOf(tribe.getCreator()))
                .tribeCoinAmount(tribeChipRequested)
                .build());

        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());
        return result;
    }

    @Override
    public InvokedResult retrieve(int userId, TribeClubReq request) {
        InvokedResult result = new InvokedResult();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());
        // 联盟创建者才有权限查看详情
        if(tribeDao.checkTribePermission(userId, request.getTribeId()) <= 0){//身份权限不合法
            result.setCode(RespCode.TRIBE_PERMISSION_ERROR.getCode());
            result.setMsg(RespCode.TRIBE_PERMISSION_ERROR.getDesc());
            return result;
        }

        TribeRecordPoExample example = new TribeRecordPoExample();
        example.or().andCreatorEqualTo(userId+"");
        List<TribeRecordPo> tribes = tribeRecordPoMapper.selectByExample(example);
        if(tribes.size()==0){
            return result;
        }
        ClubRecordPoExample exampleC = new ClubRecordPoExample();
        exampleC.or().andRandomIdEqualTo(request.getClubId());

        List<ClubRecordPo> clubRecordPoList = clubRecordDao.selectByExample(exampleC);
        ClubRecordPo clubRecordPo = clubRecordPoList.get(0);

        TribeRecordPo tribe = tribes.get(0);

        TribeMemberPoExample poExample = new TribeMemberPoExample();
        poExample.or().andTribeIdEqualTo(tribe.getId()).andClubIdEqualTo(clubRecordPo.getId());
        List<TribeMemberPo> tribeMemberPos = tribeMemberPoMapper.selectByExample(poExample);
        if(tribeMemberPos.size()==0){
            result.setCode(RespCode.TRIBE_CLUB_NOT_MEMBER.getCode());
            result.setMsg(RespCode.TRIBE_CLUB_NOT_MEMBER.getDesc());
            return result;
        }

        int tribeChipRequested = request.getTribeChip();
        if(clubRecordPo.getChip() < tribeChipRequested){
            result.setCode(RespCode.TRIBE_CHIP_NOT_ENOUGH_FOR_RETRIEVE.getCode());
            result.setMsg(RespCode.TRIBE_CHIP_NOT_ENOUGH_FOR_RETRIEVE.getDesc());
            return result;
        }
        ClubRecordPo recordPo = new ClubRecordPo();
        recordPo.setChip(clubRecordPo.getChip() - tribeChipRequested);
        recordPo.setId(clubRecordPo.getId());
        clubRecordDao.updateByPrimaryKeySelective(recordPo);

        TribeRecordPo po = new TribeRecordPo();
        po.setId(tribe.getId());
        po.setChip(tribe.getChip() + tribeChipRequested);
        tribeRecordPoMapper.updateByPrimaryKeySelective(po);

        //回收记录
        TribeChipLog log = new TribeChipLog();
        log.setTribeId(request.getTribeId());
        log.setType(TribeChipLog.Type.RECYCLE.getValue());
        log.setChip(tribeChipRequested);
        log.setChipBefore(tribe.getChip());
        log.setCreateTime(new Date());
        log.setOtherId(clubRecordPo.getRandomId()+"");
        log.setClubRandomId(clubRecordPo.getRandomId()+"");
        log.setClubId(clubRecordPo.getId());
        log.setOperatorId(-1);
        tribeDao.insertLog(log);
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());

        //回收俱樂部記錄
        ClubChipLog clog = new ClubChipLog();
        clog.setClubId(clubRecordPo.getId());
        clog.setType(ClubChipLog.Type.TRIBE_RECYCLE.getValue());
        clog.setChip(-tribeChipRequested);
        clog.setChipBefore(clubRecordPo.getChip());
        clog.setCreateTime(new Date());
        clog.setOtherId(tribe.getRandomId()+"");
        clog.setTribeRandomId(tribe.getRandomId()+"");
        clubDao.insertLog(clog);


        // 发送app消息
        appBusinessMessageSender.notifySendOrRecycleClubTribeChip(SendOrRecycleTribeClubChip.builder()
                .tplCode(AppMessageConstants.TplCode.WALLET0013)
                .clubName(clubRecordPo.getName())
                .clubId(clubRecordPo.getId().longValue())
                .clubOwnerId(Long.valueOf(clubRecordPo.getCreator()))
                .tribeId(tribe.getId().longValue())
                .tribeName(tribe.getTribeName())
                .tribeOwnerId(Long.valueOf(tribe.getCreator()))
                .tribeCoinAmount(tribeChipRequested)
                .build());

        return result;
    }

    @Override
    public List<TribeChipLog> chipLog(int userId, TribeClubReq request) {
        TribeRecordPoExample example = new TribeRecordPoExample();
        example.or().andCreatorEqualTo(userId+"");
        List<TribeRecordPo> tribes = tribeRecordPoMapper.selectByExample(example);

        List<TribeChipLog> tribeChipLog = tribeDao.findTribeChipLog(tribes.get(0).getId(), request.getType());

        return tribeChipLog;
    }

    @Override
    public InvokedResult<Integer> findClubChip(int userId, TribeClubReq request) {

        ClubRecordPoExample exampleC = new ClubRecordPoExample();
        exampleC.or().andRandomIdEqualTo(request.getClubId());

        List<ClubRecordPo> clubRecordPoList = clubRecordDao.selectByExample(exampleC);
        InvokedResult<Integer> result = new InvokedResult<>();
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());

        if (clubRecordPoList == null || clubRecordPoList.isEmpty()) {
            result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
            result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
            return result;
        }

        ClubRecordPo clubRecordPo = clubRecordPoList.get(0);
        result.setData(clubRecordPo.getChip());
        return result;
    }

    @Override
    public InvokedResult<List<TribeActivityPo>> getTribeActivityList(int userId) {
        InvokedResult<List<TribeActivityPo>> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        List<TribeActivityPo> tribeActivityList = tribeDao.getTribeActivityList(userId);
        if(null == tribeActivityList || tribeActivityList.isEmpty()){
            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());
            return result;
        }

        // 过滤出用户允许的活动
        tribeActivityList = filterAllowedActivities(tribeActivityList, userId);

        result.setData(tribeActivityList);
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());

        return result;
    }

    // 过滤出用户允许的活动id
    List<TribeActivityPo> filterAllowedActivities(List<TribeActivityPo> tribeActivityList, Integer userId) {
        List<UserClubTribe> userClubTribes = tribeDao.findUserClubTribe(userId);
        List<Integer> allowedActivityIds = allowedActivityIds(userClubTribes);
        // 根据允许的ID过滤活动列表
        if (allowedActivityIds != null && !allowedActivityIds.isEmpty()) {
            return tribeActivityList.stream()
                    .filter(activity -> allowedActivityIds.contains(activity.getId()))
                    .collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }


    /**
     * 查询允许的活动id
     * @param userClubTribes
     * @return
     */
    List<Integer> allowedActivityIds(List<UserClubTribe> userClubTribes) {
        List<Integer> allowedActivityIds = new ArrayList<>();

        for (UserClubTribe userClubTribe : userClubTribes) {
            // 先查用户的活动支付层级
            Integer tpaTierId = tribeDao.findTribeUserPaymentActivityTier(userClubTribe.getTribeId(), userClubTribe.getClubId(), userClubTribe.getUserId());
            if (tpaTierId != null) {
                // 查询允许的活动id
                List<Integer> activityIdsByTpaTierId = tribeDao.findActivityIdsByTpaTierId(tpaTierId);
                if (activityIdsByTpaTierId != null && !activityIdsByTpaTierId.isEmpty()) {
                    allowedActivityIds.addAll(activityIdsByTpaTierId);
                }
            }

            // 再查询俱乐部的层级
            Integer tribeClubTierId = userClubTribe.getTribeClubTierId();
            if (tribeClubTierId != null) {
                // 查询俱乐部允许的活动id
                List<Integer> activityIdsByTribeClubTierId = tribeDao.findActivityIdsByTribeClubTierId(tribeClubTierId);
                if (activityIdsByTribeClubTierId != null && !activityIdsByTribeClubTierId.isEmpty()) {
                    allowedActivityIds.addAll(activityIdsByTribeClubTierId);
                }
            }
            
        }
        return allowedActivityIds;
    }


}
