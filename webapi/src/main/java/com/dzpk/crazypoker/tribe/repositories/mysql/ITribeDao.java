package com.dzpk.crazypoker.tribe.repositories.mysql;

import com.dzpk.crazypoker.club.api.vo.ClubChipLog;
import com.dzpk.crazypoker.tribe.api.vo.TribeChipLog;
import com.dzpk.crazypoker.tribe.repositories.mysql.autogen.model.TribeActivityPo;
import com.dzpk.crazypoker.tribe.repositories.mysql.autogen.model.TribeChipProductPo;
import com.dzpk.crazypoker.tribe.repositories.mysql.autogen.model.TribeRecordPo;
import com.dzpk.crazypoker.tribe.repositories.mysql.autogen.model.UserClubTribe;
import com.dzpk.crazypoker.tribe.repositories.mysql.model.TribeJoinOrCreatedPo;
import com.dzpk.crazypoker.tribe.repositories.mysql.model.TribeStatusPo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by jayce on 2019/2/25
 *
 * <AUTHOR>
 */
@Repository
public interface ITribeDao {

    @Select("SELECT COUNT(*) FROM message_tribe_request WHERE user_id=#{userId} AND type = 1")
    int getCreateTribeRequestNums(@Param(value = "userId")int userId);

    @Select("SELECT COUNT(*) FROM message_tribe_request WHERE club_id =#{clubId} AND tribe_id=#{tribeId} AND type in(1,2)")
    int getJoinTribeRequestByTribeId(@Param(value = "tribeId")int tribeId,@Param(value = "clubId")int clubId);

    @Select("SELECT COUNT(*) FROM message_tribe_request WHERE club_id =#{clubId} AND type in(1,2)")
    int getJoinTribeRequestByClubId(@Param(value = "clubId")int clubId);

    @Insert("INSERT INTO message_tribe_request (tribe_id,user_id,msg_id,club_id,param,type,create_time)" +
            " VALUES(#{tribeId},#{userId},#{msgId},#{clubId},#{param},#{type},NOW())")
    int addTribeRequest(@Param("tribeId")String tribeId,@Param("userId")String userId,@Param("msgId")String msgId,
                        @Param("clubId")String clubId,@Param("param")String param, @Param("type")Integer type);

    // 查找社区名字个数
    @Select("select count(*) from tribe_record where tribe_name=#{tribeName}")
    int checkNameInTribe(@Param(value = "tribeName") String tribeName);

    @Select("SELECT id,random_id as randomId,tribe_name as tribeName,creator as creator, head, use_custom useCustom, custom_url customUrl FROM tribe_record WHERE id=#{tribeId}")
    TribeRecordPo getTribeByTribeId(@Param(value = "tribeId")int tribeId);

    @Select("SELECT id,random_id as randomId,tribe_name as tribeName, club_count as clubCount, club_limit as clubLimit, head, use_custom useCustom, custom_url customUrl FROM tribe_record WHERE random_id=#{randomId} and tribe_name not like #{notLikeName}")
    TribeRecordPo getTribeByRandomId(@Param(value = "randomId")String randomId,@Param(value = "notLikeName")String notLikeName);

    @Select("SELECT COUNT(*) FROM tribe_record tr WHERE tr.id=#{tribeId} AND tr.creator=#{userId}")
    int checkTribePermission(@Param(value = "userId")int userId,@Param(value = "tribeId")int tribeId);

    @Select({
            "SELECT COUNT(*) FROM tribe_record tr",
            "inner join tribe_members tm on tm.tribe_id = tr.id",
            "inner join club_record cr on cr.id = tm.club_id",
            "WHERE tr.id=#{tribeId} AND #{userId} IN (tr.creator, cr.creator)"
    })
    int checkTribeClubPermission(@Param(value = "userId")int userId,@Param(value = "tribeId")int tribeId);

    @Select("SELECT COUNT(*) FROM tribe_members WHERE club_id=#{clubId}")
    int getJoinTribeNums(@Param(value = "clubId")int clubId);

    @Select("SELECT tr.id as tribeId, tr.random_id as randomId,tr.tribe_name as tribeName, tr.head as tribeHead, tr.use_custom useCustom, tr.custom_url customUrl " +
            "FROM tribe_members as tm,tribe_record as tr WHERE tm.club_id = #{clubId} AND tm.tribe_id = tr.id AND tm.status = 1")
    List<TribeJoinOrCreatedPo> getJoinAndCreatedTribe(@Param(value = "clubId")int clubId);

    @Update("UPDATE tribe_record SET tribe_name=#{tribeName} WHERE id=#{tribeId}")
    int updateTribeName(int tribeId, String tribeName);

    @Update("UPDATE tribe_record SET head=#{head}, use_custom = 0, custom_url = null WHERE id=#{tribeId}")
    int setTribeHead(int tribeId, String head);

    @Update("UPDATE tribe_record SET custom_url=#{customUrl}, use_custom=1 WHERE id=#{tribeId}")
    int setTribeCustomHead(int tribeId, String customUrl);

    // 更新社区加入同盟的个数
    @Update("update club_record set tribe_count=(case when cast(tribe_count as signed)+#{tribeCount}>0 then tribe_count+#{tribeCount} else 0 end) where id=#{clubId}")
    int updateTribeCount(@Param(value = "clubId") int clubId, @Param(value = "tribeCount") int tribeCount);

    // 更新同盟成员数 (社区人数变化时这里也要变化)
    @Update("update tribe_record set club_count=(case when cast(club_count as signed)+#{clubNum}>=0 then club_count+#{clubNum} else 0 end), " +
            "member_count=(case when cast(member_count as signed)+#{memberCount}>=0 then member_count+#{memberCount} else 0 end) where id=#{tribeId}")
    int updateTribeMembers(@Param(value = "tribeId") int tribeId, @Param(value = "clubNum") int clubNum, @Param(value = "memberCount") int memberCount);

    // 更新同盟成员数 (社区人数变化时需要更新该社区加入的所有同盟的总成员数)
    @Update("update tribe_record set member_count=member_count+#{num} where id in (select tribe_id from tribe_members WHERE club_id =#{clubId})")
    int updateTribesMemberCount(@Param(value = "num") int num,@Param(value = "clubId") int clubId);

    //TODO 如果tm 出现多条记录  这条语句查出来的数据会有业务冲突
    @Select("SELECT tm.tribe_id as tribeId,tm.status,tr.tribe_status as tribeStatus FROM tribe_members tm,tribe_record tr" +
            " WHERE tm.club_id = #{clubId} AND tr.id = tm.tribe_id")
    TribeStatusPo findTribeStatusByClubId(@Param(value = "clubId")Integer clubId);

    @Insert("insert into tribe_chip_log (tribe_id,type,chip,chip_before,create_time,other_id,room_id,club_random_id, club_id, user_id, remark, operator_id) " +
            "values ( #{tribeId},#{type},#{chip},#{chipBefore},#{createTime},#{otherId},#{roomId},#{clubRandomId},#{clubId},#{userId},#{remark},#{operatorId})")
    void insertLog(TribeChipLog log);

    @Select("select id,tribe_id tribeId,chip,chip_before chipBefore,type,other_id otherId,create_time createTime,room_id roomId,club_random_id clubRandomId from tribe_chip_log where tribe_id = #{tribeId} and type =#{type} order by create_time desc")
    List<TribeChipLog> findTribeChipLog(@Param("tribeId") int tribeId, @Param("type") int type);

    /**
     * 获取同盟的充值费率
     * @param tribeId
     * @return
     */
    @Select("select recharge_fee_rate from tribe_record where id=#{tribeId}")
    Integer getRechargeFeeRate(@Param(value = "tribeId") int tribeId);

    /**
     * 获取同盟的筹码餘額
     * @param tribeId
     * @return
     */
    @Select("select chip from tribe_record where id=#{tribeId}")
    Integer getTribeChip(@Param(value = "tribeId") int tribeId);

    /**
     * 获取同盟的保险賠付限额
     * @param tribeId
     * @return
     */
    @Select("select insurance_loss_limit from tribe_record where id=#{tribeId}")
    Integer getTribeInsuranceLossLimit(@Param(value = "tribeId") int tribeId);

    @Select({"select id,club_id as clubId,type,chip,chip_before as chipBefore,create_time as createTime,other_id as otherId ",
            "from club_chip_log where club_id = #{clubId} and id < ifnull(#{nextPage}, id+1) order by id desc limit #{limit}"})
    List<ClubChipLog> findClubChipLog(int clubId, int limit, Integer nextPage);

    /**
     * 获取同盟成员的筹码总额
     * @param tribeId
     * @return
     */
    @Select({"select sum(cr.chip) from club_record cr ",
            "inner join tribe_members tm on cr.id = tm.club_id",
            "where tm.tribe_id = #{tribeId} and tm.status = 1"})
    int getClubChipTotal(@Param(value = "tribeId") int tribeId);

    /**
     * 联盟币充值包
     * @param platformType
     * @return
     */
    @Select({"SELECT id,cast(price*100 as decimal) as price,discount,idou_num as chips,client_types as clientTypes",
            "FROM yunying.payment_product",
            "where status=1 and balance_type=2 and currency_type=1",
            "and find_in_set( #{platformType}, client_types ) > 0",
            "order by sort_no DESC, price"})
    List<TribeChipProductPo> getTribeChipProductByPlatform(int platformType);

    @Select({"SELECT id,cast(price*100 as decimal) as price,discount,idou_num as chips,client_types as clientTypes",
            "FROM yunying.payment_product",
            "where status=1 and id = #{id}"})
    TribeChipProductPo getTribeChipProductById(int id);

    @Select({"SELECT DISTINCT",
	"ta.id,",
	"ta.tribe_id AS tribeId,",
        "tr.random_id AS tribeRandomId,",
	"tr.tribe_name AS tribeName,",
	"tr.head AS tribeHead,",
	"ta.name,",
	"ta.seq,",
	"ta.url,",
	"ta.banner,",
	"ta.description,",
	"ta.activity_start_time AS activityStartTime,",
	"ta.activity_end_time AS activityEndTime,",
	"ta.display_start_time AS displayStartTime,",
	"ta.display_end_time AS displayEndTime",
        "FROM",
	"crazy_poker.tribe_record tr",
	"LEFT JOIN yunying.tribe_activity ta ON ta.tribe_id = tr.id",
	"LEFT JOIN crazy_poker.tribe_members tm ON tm.tribe_id = tr.id",
	"LEFT JOIN crazy_poker.club_members cm ON cm.club_id = tm.club_id",
        "WHERE",
	"cm.user_id = #{userId} AND ta.status = 1 AND ta.display_end_time > NOW()",
        "ORDER BY ta.seq ASC"})
    List<TribeActivityPo> getTribeActivityList(int userId);

    @Select("select club_status from tribe_members where club_id = #{clubId} and status = 1")
    int getTribeMembersClubStatus(int clubId);

    @Select("select cm.club_id as clubId, cm.user_id as userId, tm.tribe_id as tribeId, tm.tribe_club_tier_id as tribeClubTierId from club_members cm join tribe_members tm on cm.club_id = tm.club_id where cm.user_id = #{userId}")
    List<UserClubTribe> findUserClubTribe(@Param("userId") Integer userId);

    @Select("select tpa_tier_id from tribe_user_payment_activity_tier where tribe_id = #{tribeId} and user_id = #{userId} and club_id = #{clubId} limit 1")
    Integer findTribeUserPaymentActivityTier(@Param("tribeId") Integer tribeId, @Param("userId") Integer userId, @Param("clubId") Integer clubId);

    @Select("select tribe_club_tier_id from tribe_members where tribe_id = #{tribeId} and club_id = #{clubId} limit 1")
    Integer findTribeActivityClubTier(@Param("tribeId") Integer tribeId, @Param("clubId") Integer clubId);

    @Select("select activity_id from tribe_user_payment_activity_tier where tpa_tier_id = #{tpaTierId} ")
    List<Integer> findActivityIdsByTpaTierId(@Param("tpaTierId") Integer tpaTierId);

    @Select("select activity_id from tribe_activity_club_tier where club_tier_id = #{tribeClubTierId} ")
    List<Integer> findActivityIdsByTribeClubTierId(@Param("tribeClubTierId") Integer tribeClubTierId);

}
