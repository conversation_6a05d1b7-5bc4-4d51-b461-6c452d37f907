package com.dzpk.crazypoker.tribe.api.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Setter
@Getter
@ApiModel(value = "联盟-创建联盟")
public class TribeCreationReq {

    @ApiModelProperty(name = "俱乐部id",
            position = 0,
            required = true,
            notes = "此俱乐部将成为联盟主机，拥有联盟最高权限<br/>" +
                    "必填")
    @NotNull(message = "clubId required!")
    @Min(value = 1,message = "clubId required!")
    private Integer clubId = 0;

    @ApiModelProperty(name = "联盟名称",
            position = 1,
            required = true,
            notes = "联盟名称，用于展示<br/>" +
                    "必填")
    @NotEmpty(message = "tribeName can't empty!")
    @NotNull(message = "tribeName can't null!")
    @Length(max = 60,message = "tribeName can't more than 60 char")
    private String tribeName = "";

    //以下五个字段至少需要填一项
    @ApiModelProperty(name = "联系人手机号码",
            position = 2,
            notes = "联系人手机号码<br/>" +
                    "非必填")
    @Length(max = 40)
    private String phone;//手机号码

    @ApiModelProperty(name = "联系人手机号码",
            position = 3,
            notes = "联系人手机号码<br/>" +
                    "非必填")
    @Length(max = 60)
    private String wechat;//微信号码

    @ApiModelProperty(name = "telegram联系",
            position = 4,
            notes = "telegram联系<br/>" +
                    "非必填")
    @Length(max = 60)
    private String telegram;

    @ApiModelProperty(name = "电子邮箱地址",
            position = 5,
            notes = "电子邮箱地址<br/>" +
                    "非必填")
    @Length(max = 60)
    private String email;//邮箱

    @ApiModelProperty(name = "留言",
            position = 6,
            notes = "留言<br/>" +
                    "非必填")
    @JsonProperty("message")
    @Length(max = 60)
    private String messageStr;//留言

    @ApiModelProperty(name = "联盟头像",
            position = 7,
            required = true,
            notes = "联盟头像")
    @NotEmpty(message = "head can't empty!")
    @NotNull(message = "head can't null!")
    @Length(max = 60,message = "head can't more than 60 char")
    private String head;

    @ApiModelProperty(name = "联盟是否使用自定义头像 0否 1是",
            position = 8,
            required = true,
            notes = "联盟是否使用自定义头像 0否 1是")
    private Integer useCustom;

    @ApiModelProperty(name = "联盟自定义头像",
            position = 9,
            required = true,
            notes = "联盟自定义头像")
    private String customUrl;

}
