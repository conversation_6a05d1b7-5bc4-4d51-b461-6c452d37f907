package com.dzpk.crazypoker.usertask.api.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotNull;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "用户任务列表请求")
public class UserTaskListReq {

    /**
     * 设备类型
     */
    @ApiModelProperty(
            name = "设备类型",
            notes = "设备类型1-android 2-ios",
            position = 2
    )
    @NotNull
    @JsonProperty("cli_type")
    private Integer deviceType;
}
