package com.dzpk.crazypoker.usertask.repositories.mysql.autogen.model;

import java.io.Serializable;
import java.util.Date;

public class UserTaskPo implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_task.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_task.user_id
     *
     * @mbg.generated
     */
    private Integer userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_task.game_task_id
     *
     * @mbg.generated
     */
    private Integer gameTaskId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_task.task_code
     *
     * @mbg.generated
     */
    private String taskCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_task.status
     *
     * @mbg.generated
     */
    private Integer status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_task.finish_time
     *
     * @mbg.generated
     */
    private Date finishTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_task.accepting_time
     *
     * @mbg.generated
     */
    private Date acceptingTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_task.device_type
     *
     * @mbg.generated
     */
    private Integer deviceType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_task.chip
     *
     * @mbg.generated
     */
    private Integer chip;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_task.created_time
     *
     * @mbg.generated
     */
    private Date createdTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_task.updated_time
     *
     * @mbg.generated
     */
    private Date updatedTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table user_task
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_task.id
     *
     * @return the value of user_task.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_task.id
     *
     * @param id the value for user_task.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_task.user_id
     *
     * @return the value of user_task.user_id
     *
     * @mbg.generated
     */
    public Integer getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_task.user_id
     *
     * @param userId the value for user_task.user_id
     *
     * @mbg.generated
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_task.game_task_id
     *
     * @return the value of user_task.game_task_id
     *
     * @mbg.generated
     */
    public Integer getGameTaskId() {
        return gameTaskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_task.game_task_id
     *
     * @param gameTaskId the value for user_task.game_task_id
     *
     * @mbg.generated
     */
    public void setGameTaskId(Integer gameTaskId) {
        this.gameTaskId = gameTaskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_task.task_code
     *
     * @return the value of user_task.task_code
     *
     * @mbg.generated
     */
    public String getTaskCode() {
        return taskCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_task.task_code
     *
     * @param taskCode the value for user_task.task_code
     *
     * @mbg.generated
     */
    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode == null ? null : taskCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_task.status
     *
     * @return the value of user_task.status
     *
     * @mbg.generated
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_task.status
     *
     * @param status the value for user_task.status
     *
     * @mbg.generated
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_task.finish_time
     *
     * @return the value of user_task.finish_time
     *
     * @mbg.generated
     */
    public Date getFinishTime() {
        return finishTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_task.finish_time
     *
     * @param finishTime the value for user_task.finish_time
     *
     * @mbg.generated
     */
    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_task.accepting_time
     *
     * @return the value of user_task.accepting_time
     *
     * @mbg.generated
     */
    public Date getAcceptingTime() {
        return acceptingTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_task.accepting_time
     *
     * @param acceptingTime the value for user_task.accepting_time
     *
     * @mbg.generated
     */
    public void setAcceptingTime(Date acceptingTime) {
        this.acceptingTime = acceptingTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_task.device_type
     *
     * @return the value of user_task.device_type
     *
     * @mbg.generated
     */
    public Integer getDeviceType() {
        return deviceType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_task.device_type
     *
     * @param deviceType the value for user_task.device_type
     *
     * @mbg.generated
     */
    public void setDeviceType(Integer deviceType) {
        this.deviceType = deviceType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_task.chip
     *
     * @return the value of user_task.chip
     *
     * @mbg.generated
     */
    public Integer getChip() {
        return chip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_task.chip
     *
     * @param chip the value for user_task.chip
     *
     * @mbg.generated
     */
    public void setChip(Integer chip) {
        this.chip = chip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_task.created_time
     *
     * @return the value of user_task.created_time
     *
     * @mbg.generated
     */
    public Date getCreatedTime() {
        return createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_task.created_time
     *
     * @param createdTime the value for user_task.created_time
     *
     * @mbg.generated
     */
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_task.updated_time
     *
     * @return the value of user_task.updated_time
     *
     * @mbg.generated
     */
    public Date getUpdatedTime() {
        return updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_task.updated_time
     *
     * @param updatedTime the value for user_task.updated_time
     *
     * @mbg.generated
     */
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }
}