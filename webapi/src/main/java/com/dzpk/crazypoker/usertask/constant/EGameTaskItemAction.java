package com.dzpk.crazypoker.usertask.constant;

import lombok.Getter;

/**
 * 任务action类型
 */
@Getter
public enum EGameTaskItemAction {
    MOD_NIKE_NAME(1,2),
    MOD_HEAD(2,2),
    SAVE_WEB_SITE(3,1),

    ;

    private int value;
    /**
     * 客户端/服务端标志
     */
    private int csFlag;

    EGameTaskItemAction(int value, int csFlag) {
        this.value = value;
        this.csFlag = csFlag;
    }

    public static EGameTaskItemAction valueOfCode(int value) {
        for (EGameTaskItemAction temp : EGameTaskItemAction.values()) {
            if (temp.value == value) {
                return temp;
            }
        }

        return null;
    }

}
