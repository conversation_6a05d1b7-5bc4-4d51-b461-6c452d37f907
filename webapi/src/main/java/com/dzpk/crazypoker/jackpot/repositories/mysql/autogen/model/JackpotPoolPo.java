package com.dzpk.crazypoker.jackpot.repositories.mysql.autogen.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class JackpotPoolPo implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jackpot_pool.jackpot_id
     *
     * @mbg.generated
     */
    private Integer jackpotId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jackpot_pool.jackpot_type
     *
     * @mbg.generated
     */
    private Integer jackpotType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jackpot_pool.fund
     *
     * @mbg.generated
     */
    private BigDecimal fund;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jackpot_pool.created_by
     *
     * @mbg.generated
     */
    private Integer createdBy;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jackpot_pool.created_time
     *
     * @mbg.generated
     */
    private Date createdTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jackpot_pool.updated_by
     *
     * @mbg.generated
     */
    private Integer updatedBy;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jackpot_pool.updated_time
     *
     * @mbg.generated
     */
    private Date updatedTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jackpot_pool.version
     *
     * @mbg.generated
     */
    private Integer version;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jackpot_pool.status
     *
     * @mbg.generated
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table jackpot_pool
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jackpot_pool.jackpot_id
     *
     * @return the value of jackpot_pool.jackpot_id
     *
     * @mbg.generated
     */
    public Integer getJackpotId() {
        return jackpotId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jackpot_pool.jackpot_id
     *
     * @param jackpotId the value for jackpot_pool.jackpot_id
     *
     * @mbg.generated
     */
    public void setJackpotId(Integer jackpotId) {
        this.jackpotId = jackpotId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jackpot_pool.jackpot_type
     *
     * @return the value of jackpot_pool.jackpot_type
     *
     * @mbg.generated
     */
    public Integer getJackpotType() {
        return jackpotType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jackpot_pool.jackpot_type
     *
     * @param jackpotType the value for jackpot_pool.jackpot_type
     *
     * @mbg.generated
     */
    public void setJackpotType(Integer jackpotType) {
        this.jackpotType = jackpotType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jackpot_pool.fund
     *
     * @return the value of jackpot_pool.fund
     *
     * @mbg.generated
     */
    public BigDecimal getFund() {
        return fund;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jackpot_pool.fund
     *
     * @param fund the value for jackpot_pool.fund
     *
     * @mbg.generated
     */
    public void setFund(BigDecimal fund) {
        this.fund = fund;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jackpot_pool.created_by
     *
     * @return the value of jackpot_pool.created_by
     *
     * @mbg.generated
     */
    public Integer getCreatedBy() {
        return createdBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jackpot_pool.created_by
     *
     * @param createdBy the value for jackpot_pool.created_by
     *
     * @mbg.generated
     */
    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jackpot_pool.created_time
     *
     * @return the value of jackpot_pool.created_time
     *
     * @mbg.generated
     */
    public Date getCreatedTime() {
        return createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jackpot_pool.created_time
     *
     * @param createdTime the value for jackpot_pool.created_time
     *
     * @mbg.generated
     */
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jackpot_pool.updated_by
     *
     * @return the value of jackpot_pool.updated_by
     *
     * @mbg.generated
     */
    public Integer getUpdatedBy() {
        return updatedBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jackpot_pool.updated_by
     *
     * @param updatedBy the value for jackpot_pool.updated_by
     *
     * @mbg.generated
     */
    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jackpot_pool.updated_time
     *
     * @return the value of jackpot_pool.updated_time
     *
     * @mbg.generated
     */
    public Date getUpdatedTime() {
        return updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jackpot_pool.updated_time
     *
     * @param updatedTime the value for jackpot_pool.updated_time
     *
     * @mbg.generated
     */
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jackpot_pool.version
     *
     * @return the value of jackpot_pool.version
     *
     * @mbg.generated
     */
    public Integer getVersion() {
        return version;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jackpot_pool.version
     *
     * @param version the value for jackpot_pool.version
     *
     * @mbg.generated
     */
    public void setVersion(Integer version) {
        this.version = version;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jackpot_pool.status
     *
     * @return the value of jackpot_pool.status
     *
     * @mbg.generated
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jackpot_pool.status
     *
     * @param status the value for jackpot_pool.status
     *
     * @mbg.generated
     */
    public void setStatus(Integer status) {
        this.status = status;
    }
}