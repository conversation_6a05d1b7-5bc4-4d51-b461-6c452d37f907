<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dzpk.crazypoker.room.repositories.mysql.autogen.mapper.RoomJackpotSettionPoMapper">
  <resultMap id="BaseResultMap" type="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.RoomJackpotSettionPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="room_id" jdbcType="INTEGER" property="roomId" />
    <result column="room_path" jdbcType="INTEGER" property="roomPath" />
    <result column="jackpot_id" jdbcType="INTEGER" property="jackpotId" />
    <result column="blind_code" jdbcType="INTEGER" property="blindCode" />
    <result column="blind_name" jdbcType="VARCHAR" property="blindName" />
    <result column="royalFlush_ratio" jdbcType="DECIMAL" property="royalflushRatio" />
    <result column="straightFlush_ratio" jdbcType="DECIMAL" property="straightflushRatio" />
    <result column="fourOfAKind_ratio" jdbcType="DECIMAL" property="fourofakindRatio" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, room_id, room_path, jackpot_id, blind_code, blind_name, royalFlush_ratio, straightFlush_ratio, 
    fourOfAKind_ratio
  </sql>
  <select id="selectByExample" parameterType="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.RoomJackpotSettionPoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from room_jackpot_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from room_jackpot_setting
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from room_jackpot_setting
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.RoomJackpotSettionPoExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from room_jackpot_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.RoomJackpotSettionPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into room_jackpot_setting (id, room_id, room_path, 
      jackpot_id, blind_code, blind_name, 
      royalFlush_ratio, straightFlush_ratio, fourOfAKind_ratio
      )
    values (#{id,jdbcType=INTEGER}, #{roomId,jdbcType=INTEGER}, #{roomPath,jdbcType=INTEGER}, 
      #{jackpotId,jdbcType=INTEGER}, #{blindCode,jdbcType=INTEGER}, #{blindName,jdbcType=VARCHAR}, 
      #{royalflushRatio,jdbcType=DECIMAL}, #{straightflushRatio,jdbcType=DECIMAL}, #{fourofakindRatio,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.RoomJackpotSettionPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into room_jackpot_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="roomId != null">
        room_id,
      </if>
      <if test="roomPath != null">
        room_path,
      </if>
      <if test="jackpotId != null">
        jackpot_id,
      </if>
      <if test="blindCode != null">
        blind_code,
      </if>
      <if test="blindName != null">
        blind_name,
      </if>
      <if test="royalflushRatio != null">
        royalFlush_ratio,
      </if>
      <if test="straightflushRatio != null">
        straightFlush_ratio,
      </if>
      <if test="fourofakindRatio != null">
        fourOfAKind_ratio,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="roomId != null">
        #{roomId,jdbcType=INTEGER},
      </if>
      <if test="roomPath != null">
        #{roomPath,jdbcType=INTEGER},
      </if>
      <if test="jackpotId != null">
        #{jackpotId,jdbcType=INTEGER},
      </if>
      <if test="blindCode != null">
        #{blindCode,jdbcType=INTEGER},
      </if>
      <if test="blindName != null">
        #{blindName,jdbcType=VARCHAR},
      </if>
      <if test="royalflushRatio != null">
        #{royalflushRatio,jdbcType=DECIMAL},
      </if>
      <if test="straightflushRatio != null">
        #{straightflushRatio,jdbcType=DECIMAL},
      </if>
      <if test="fourofakindRatio != null">
        #{fourofakindRatio,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.RoomJackpotSettionPoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from room_jackpot_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update room_jackpot_setting
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.roomId != null">
        room_id = #{record.roomId,jdbcType=INTEGER},
      </if>
      <if test="record.roomPath != null">
        room_path = #{record.roomPath,jdbcType=INTEGER},
      </if>
      <if test="record.jackpotId != null">
        jackpot_id = #{record.jackpotId,jdbcType=INTEGER},
      </if>
      <if test="record.blindCode != null">
        blind_code = #{record.blindCode,jdbcType=INTEGER},
      </if>
      <if test="record.blindName != null">
        blind_name = #{record.blindName,jdbcType=VARCHAR},
      </if>
      <if test="record.royalflushRatio != null">
        royalFlush_ratio = #{record.royalflushRatio,jdbcType=DECIMAL},
      </if>
      <if test="record.straightflushRatio != null">
        straightFlush_ratio = #{record.straightflushRatio,jdbcType=DECIMAL},
      </if>
      <if test="record.fourofakindRatio != null">
        fourOfAKind_ratio = #{record.fourofakindRatio,jdbcType=DECIMAL},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update room_jackpot_setting
    set id = #{record.id,jdbcType=INTEGER},
      room_id = #{record.roomId,jdbcType=INTEGER},
      room_path = #{record.roomPath,jdbcType=INTEGER},
      jackpot_id = #{record.jackpotId,jdbcType=INTEGER},
      blind_code = #{record.blindCode,jdbcType=INTEGER},
      blind_name = #{record.blindName,jdbcType=VARCHAR},
      royalFlush_ratio = #{record.royalflushRatio,jdbcType=DECIMAL},
      straightFlush_ratio = #{record.straightflushRatio,jdbcType=DECIMAL},
      fourOfAKind_ratio = #{record.fourofakindRatio,jdbcType=DECIMAL}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.RoomJackpotSettionPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update room_jackpot_setting
    <set>
      <if test="roomId != null">
        room_id = #{roomId,jdbcType=INTEGER},
      </if>
      <if test="roomPath != null">
        room_path = #{roomPath,jdbcType=INTEGER},
      </if>
      <if test="jackpotId != null">
        jackpot_id = #{jackpotId,jdbcType=INTEGER},
      </if>
      <if test="blindCode != null">
        blind_code = #{blindCode,jdbcType=INTEGER},
      </if>
      <if test="blindName != null">
        blind_name = #{blindName,jdbcType=VARCHAR},
      </if>
      <if test="royalflushRatio != null">
        royalFlush_ratio = #{royalflushRatio,jdbcType=DECIMAL},
      </if>
      <if test="straightflushRatio != null">
        straightFlush_ratio = #{straightflushRatio,jdbcType=DECIMAL},
      </if>
      <if test="fourofakindRatio != null">
        fourOfAKind_ratio = #{fourofakindRatio,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.RoomJackpotSettionPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update room_jackpot_setting
    set room_id = #{roomId,jdbcType=INTEGER},
      room_path = #{roomPath,jdbcType=INTEGER},
      jackpot_id = #{jackpotId,jdbcType=INTEGER},
      blind_code = #{blindCode,jdbcType=INTEGER},
      blind_name = #{blindName,jdbcType=VARCHAR},
      royalFlush_ratio = #{royalflushRatio,jdbcType=DECIMAL},
      straightFlush_ratio = #{straightflushRatio,jdbcType=DECIMAL},
      fourOfAKind_ratio = #{fourofakindRatio,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>