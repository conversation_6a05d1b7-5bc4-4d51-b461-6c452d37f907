<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dzpk.crazypoker.room.repositories.mysql.autogen.mapper.MttRoomPoMapper">
  <resultMap id="BaseResultMap" type="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.MttRoomPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="game_id" jdbcType="INTEGER" property="gameId" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="mtt_name" jdbcType="VARCHAR" property="mttName" />
    <result column="is_official" jdbcType="TINYINT" property="isOfficial" />
    <result column="start_time" jdbcType="INTEGER" property="startTime" />
    <result column="create_time" jdbcType="INTEGER" property="createTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="upper_limit" jdbcType="INTEGER" property="upperLimit" />
    <result column="participants" jdbcType="INTEGER" property="participants" />
    <result column="mtt_type" jdbcType="INTEGER" property="mttType" />
    <result column="initial_chip" jdbcType="INTEGER" property="initialChip" />
    <result column="update_cycle" jdbcType="INTEGER" property="updateCycle" />
    <result column="bind_type" jdbcType="TINYINT" property="bindType" />
    <result column="bonus_type" jdbcType="TINYINT" property="bonusType" />
    <result column="allow_rebuy" jdbcType="TINYINT" property="allowRebuy" />
    <result column="max_update_level" jdbcType="INTEGER" property="maxUpdateLevel" />
    <result column="rebuy_times" jdbcType="INTEGER" property="rebuyTimes" />
    <result column="allow_append" jdbcType="TINYINT" property="allowAppend" />
    <result column="voucher" jdbcType="INTEGER" property="voucher" />
    <result column="initial_score" jdbcType="INTEGER" property="initialScore" />
    <result column="lower_limit" jdbcType="INTEGER" property="lowerLimit" />
    <result column="entry_time" jdbcType="INTEGER" property="entryTime" />
    <result column="show_time" jdbcType="INTEGER" property="showTime" />
    <result column="game_type" jdbcType="INTEGER" property="gameType" />
    <result column="game_icon" jdbcType="VARCHAR" property="gameIcon" />
    <result column="service_fee" jdbcType="INTEGER" property="serviceFee" />
    <result column="allow_delay" jdbcType="TINYINT" property="allowDelay" />
    <result column="max_delay_level" jdbcType="INTEGER" property="maxDelayLevel" />
    <result column="advanced_entry" jdbcType="INTEGER" property="advancedEntry" />
    <result column="prize_name" jdbcType="VARCHAR" property="prizeName" />
    <result column="prize_icon" jdbcType="VARCHAR" property="prizeIcon" />
    <result column="is_virtual" jdbcType="TINYINT" property="isVirtual" />
    <result column="initial_pool" jdbcType="INTEGER" property="initialPool" />
    <result column="ip" jdbcType="SMALLINT" property="ip" />
    <result column="gps" jdbcType="SMALLINT" property="gps" />
    <result column="sb_chip" jdbcType="DECIMAL" property="sbChip" />
    <result column="hunter_match" jdbcType="INTEGER" property="hunterMatch" />
    <result column="hunter_percent" jdbcType="INTEGER" property="hunterPercent" />
    <result column="canShow" jdbcType="INTEGER" property="canshow" />
    <result column="logo_url" jdbcType="VARCHAR" property="logoUrl" />
    <result column="op_time" jdbcType="INTEGER" property="opTime" />
    <result column="server_id" jdbcType="VARCHAR" property="serverId" />
    <result column="access_ip" jdbcType="VARCHAR" property="accessIp" />
    <result column="access_port" jdbcType="INTEGER" property="accessPort" />
    <result column="under_control" jdbcType="TINYINT" property="underControl" />
    <result column="charge" jdbcType="INTEGER" property="charge" />
    <result column="source" jdbcType="SMALLINT" property="source" />
    <result column="source_id" jdbcType="INTEGER" property="sourceId" />
    <result column="tribe_id" jdbcType="INTEGER" property="tribeId" />
    <result column="charge_percent" jdbcType="INTEGER" property="chargePercent" />
    <result column="registation_fee" jdbcType="INTEGER" property="registationFee" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, game_id, creator_id, creator_name, mtt_name, is_official, start_time, create_time, 
    status, upper_limit, participants, mtt_type, initial_chip, update_cycle, bind_type, 
    bonus_type, allow_rebuy, max_update_level, rebuy_times, allow_append, voucher, initial_score, 
    lower_limit, entry_time, show_time, game_type, game_icon, service_fee, allow_delay, 
    max_delay_level, advanced_entry, prize_name, prize_icon, is_virtual, initial_pool, 
    ip, gps, sb_chip, hunter_match, hunter_percent, canShow, logo_url, op_time, server_id, 
    access_ip, access_port, under_control, charge, source, source_id, tribe_id, charge_percent, 
    registation_fee
  </sql>
  <select id="selectByExample" parameterType="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.MttRoomPoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mtt_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from mtt_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from mtt_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.MttRoomPoExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from mtt_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.MttRoomPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into mtt_record (id, game_id, creator_id, 
      creator_name, mtt_name, is_official, 
      start_time, create_time, status, 
      upper_limit, participants, mtt_type, 
      initial_chip, update_cycle, bind_type, 
      bonus_type, allow_rebuy, max_update_level, 
      rebuy_times, allow_append, voucher, 
      initial_score, lower_limit, entry_time, 
      show_time, game_type, game_icon, 
      service_fee, allow_delay, max_delay_level, 
      advanced_entry, prize_name, prize_icon, 
      is_virtual, initial_pool, ip, 
      gps, sb_chip, hunter_match, 
      hunter_percent, canShow, logo_url, 
      op_time, server_id, access_ip, 
      access_port, under_control, charge, 
      source, source_id, tribe_id, 
      charge_percent, registation_fee)
    values (#{id,jdbcType=INTEGER}, #{gameId,jdbcType=INTEGER}, #{creatorId,jdbcType=INTEGER}, 
      #{creatorName,jdbcType=VARCHAR}, #{mttName,jdbcType=VARCHAR}, #{isOfficial,jdbcType=TINYINT}, 
      #{startTime,jdbcType=INTEGER}, #{createTime,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, 
      #{upperLimit,jdbcType=INTEGER}, #{participants,jdbcType=INTEGER}, #{mttType,jdbcType=INTEGER}, 
      #{initialChip,jdbcType=INTEGER}, #{updateCycle,jdbcType=INTEGER}, #{bindType,jdbcType=TINYINT}, 
      #{bonusType,jdbcType=TINYINT}, #{allowRebuy,jdbcType=TINYINT}, #{maxUpdateLevel,jdbcType=INTEGER}, 
      #{rebuyTimes,jdbcType=INTEGER}, #{allowAppend,jdbcType=TINYINT}, #{voucher,jdbcType=INTEGER}, 
      #{initialScore,jdbcType=INTEGER}, #{lowerLimit,jdbcType=INTEGER}, #{entryTime,jdbcType=INTEGER}, 
      #{showTime,jdbcType=INTEGER}, #{gameType,jdbcType=INTEGER}, #{gameIcon,jdbcType=VARCHAR}, 
      #{serviceFee,jdbcType=INTEGER}, #{allowDelay,jdbcType=TINYINT}, #{maxDelayLevel,jdbcType=INTEGER}, 
      #{advancedEntry,jdbcType=INTEGER}, #{prizeName,jdbcType=VARCHAR}, #{prizeIcon,jdbcType=VARCHAR}, 
      #{isVirtual,jdbcType=TINYINT}, #{initialPool,jdbcType=INTEGER}, #{ip,jdbcType=SMALLINT}, 
      #{gps,jdbcType=SMALLINT}, #{sbChip,jdbcType=DECIMAL}, #{hunterMatch,jdbcType=INTEGER}, 
      #{hunterPercent,jdbcType=INTEGER}, #{canshow,jdbcType=INTEGER}, #{logoUrl,jdbcType=VARCHAR}, 
      #{opTime,jdbcType=INTEGER}, #{serverId,jdbcType=VARCHAR}, #{accessIp,jdbcType=VARCHAR}, 
      #{accessPort,jdbcType=INTEGER}, #{underControl,jdbcType=TINYINT}, #{charge,jdbcType=INTEGER}, 
      #{source,jdbcType=SMALLINT}, #{sourceId,jdbcType=INTEGER}, #{tribeId,jdbcType=INTEGER}, 
      #{chargePercent,jdbcType=INTEGER}, #{registationFee,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.MttRoomPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into mtt_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="gameId != null">
        game_id,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="creatorName != null">
        creator_name,
      </if>
      <if test="mttName != null">
        mtt_name,
      </if>
      <if test="isOfficial != null">
        is_official,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="upperLimit != null">
        upper_limit,
      </if>
      <if test="participants != null">
        participants,
      </if>
      <if test="mttType != null">
        mtt_type,
      </if>
      <if test="initialChip != null">
        initial_chip,
      </if>
      <if test="updateCycle != null">
        update_cycle,
      </if>
      <if test="bindType != null">
        bind_type,
      </if>
      <if test="bonusType != null">
        bonus_type,
      </if>
      <if test="allowRebuy != null">
        allow_rebuy,
      </if>
      <if test="maxUpdateLevel != null">
        max_update_level,
      </if>
      <if test="rebuyTimes != null">
        rebuy_times,
      </if>
      <if test="allowAppend != null">
        allow_append,
      </if>
      <if test="voucher != null">
        voucher,
      </if>
      <if test="initialScore != null">
        initial_score,
      </if>
      <if test="lowerLimit != null">
        lower_limit,
      </if>
      <if test="entryTime != null">
        entry_time,
      </if>
      <if test="showTime != null">
        show_time,
      </if>
      <if test="gameType != null">
        game_type,
      </if>
      <if test="gameIcon != null">
        game_icon,
      </if>
      <if test="serviceFee != null">
        service_fee,
      </if>
      <if test="allowDelay != null">
        allow_delay,
      </if>
      <if test="maxDelayLevel != null">
        max_delay_level,
      </if>
      <if test="advancedEntry != null">
        advanced_entry,
      </if>
      <if test="prizeName != null">
        prize_name,
      </if>
      <if test="prizeIcon != null">
        prize_icon,
      </if>
      <if test="isVirtual != null">
        is_virtual,
      </if>
      <if test="initialPool != null">
        initial_pool,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="gps != null">
        gps,
      </if>
      <if test="sbChip != null">
        sb_chip,
      </if>
      <if test="hunterMatch != null">
        hunter_match,
      </if>
      <if test="hunterPercent != null">
        hunter_percent,
      </if>
      <if test="canshow != null">
        canShow,
      </if>
      <if test="logoUrl != null">
        logo_url,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="serverId != null">
        server_id,
      </if>
      <if test="accessIp != null">
        access_ip,
      </if>
      <if test="accessPort != null">
        access_port,
      </if>
      <if test="underControl != null">
        under_control,
      </if>
      <if test="charge != null">
        charge,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="tribeId != null">
        tribe_id,
      </if>
      <if test="chargePercent != null">
        charge_percent,
      </if>
      <if test="registationFee != null">
        registation_fee,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="gameId != null">
        #{gameId,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="mttName != null">
        #{mttName,jdbcType=VARCHAR},
      </if>
      <if test="isOfficial != null">
        #{isOfficial,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="upperLimit != null">
        #{upperLimit,jdbcType=INTEGER},
      </if>
      <if test="participants != null">
        #{participants,jdbcType=INTEGER},
      </if>
      <if test="mttType != null">
        #{mttType,jdbcType=INTEGER},
      </if>
      <if test="initialChip != null">
        #{initialChip,jdbcType=INTEGER},
      </if>
      <if test="updateCycle != null">
        #{updateCycle,jdbcType=INTEGER},
      </if>
      <if test="bindType != null">
        #{bindType,jdbcType=TINYINT},
      </if>
      <if test="bonusType != null">
        #{bonusType,jdbcType=TINYINT},
      </if>
      <if test="allowRebuy != null">
        #{allowRebuy,jdbcType=TINYINT},
      </if>
      <if test="maxUpdateLevel != null">
        #{maxUpdateLevel,jdbcType=INTEGER},
      </if>
      <if test="rebuyTimes != null">
        #{rebuyTimes,jdbcType=INTEGER},
      </if>
      <if test="allowAppend != null">
        #{allowAppend,jdbcType=TINYINT},
      </if>
      <if test="voucher != null">
        #{voucher,jdbcType=INTEGER},
      </if>
      <if test="initialScore != null">
        #{initialScore,jdbcType=INTEGER},
      </if>
      <if test="lowerLimit != null">
        #{lowerLimit,jdbcType=INTEGER},
      </if>
      <if test="entryTime != null">
        #{entryTime,jdbcType=INTEGER},
      </if>
      <if test="showTime != null">
        #{showTime,jdbcType=INTEGER},
      </if>
      <if test="gameType != null">
        #{gameType,jdbcType=INTEGER},
      </if>
      <if test="gameIcon != null">
        #{gameIcon,jdbcType=VARCHAR},
      </if>
      <if test="serviceFee != null">
        #{serviceFee,jdbcType=INTEGER},
      </if>
      <if test="allowDelay != null">
        #{allowDelay,jdbcType=TINYINT},
      </if>
      <if test="maxDelayLevel != null">
        #{maxDelayLevel,jdbcType=INTEGER},
      </if>
      <if test="advancedEntry != null">
        #{advancedEntry,jdbcType=INTEGER},
      </if>
      <if test="prizeName != null">
        #{prizeName,jdbcType=VARCHAR},
      </if>
      <if test="prizeIcon != null">
        #{prizeIcon,jdbcType=VARCHAR},
      </if>
      <if test="isVirtual != null">
        #{isVirtual,jdbcType=TINYINT},
      </if>
      <if test="initialPool != null">
        #{initialPool,jdbcType=INTEGER},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=SMALLINT},
      </if>
      <if test="gps != null">
        #{gps,jdbcType=SMALLINT},
      </if>
      <if test="sbChip != null">
        #{sbChip,jdbcType=DECIMAL},
      </if>
      <if test="hunterMatch != null">
        #{hunterMatch,jdbcType=INTEGER},
      </if>
      <if test="hunterPercent != null">
        #{hunterPercent,jdbcType=INTEGER},
      </if>
      <if test="canshow != null">
        #{canshow,jdbcType=INTEGER},
      </if>
      <if test="logoUrl != null">
        #{logoUrl,jdbcType=VARCHAR},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=INTEGER},
      </if>
      <if test="serverId != null">
        #{serverId,jdbcType=VARCHAR},
      </if>
      <if test="accessIp != null">
        #{accessIp,jdbcType=VARCHAR},
      </if>
      <if test="accessPort != null">
        #{accessPort,jdbcType=INTEGER},
      </if>
      <if test="underControl != null">
        #{underControl,jdbcType=TINYINT},
      </if>
      <if test="charge != null">
        #{charge,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=SMALLINT},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=INTEGER},
      </if>
      <if test="tribeId != null">
        #{tribeId,jdbcType=INTEGER},
      </if>
      <if test="chargePercent != null">
        #{chargePercent,jdbcType=INTEGER},
      </if>
      <if test="registationFee != null">
        #{registationFee,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.MttRoomPoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from mtt_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update mtt_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.gameId != null">
        game_id = #{record.gameId,jdbcType=INTEGER},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=INTEGER},
      </if>
      <if test="record.creatorName != null">
        creator_name = #{record.creatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.mttName != null">
        mtt_name = #{record.mttName,jdbcType=VARCHAR},
      </if>
      <if test="record.isOfficial != null">
        is_official = #{record.isOfficial,jdbcType=TINYINT},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.upperLimit != null">
        upper_limit = #{record.upperLimit,jdbcType=INTEGER},
      </if>
      <if test="record.participants != null">
        participants = #{record.participants,jdbcType=INTEGER},
      </if>
      <if test="record.mttType != null">
        mtt_type = #{record.mttType,jdbcType=INTEGER},
      </if>
      <if test="record.initialChip != null">
        initial_chip = #{record.initialChip,jdbcType=INTEGER},
      </if>
      <if test="record.updateCycle != null">
        update_cycle = #{record.updateCycle,jdbcType=INTEGER},
      </if>
      <if test="record.bindType != null">
        bind_type = #{record.bindType,jdbcType=TINYINT},
      </if>
      <if test="record.bonusType != null">
        bonus_type = #{record.bonusType,jdbcType=TINYINT},
      </if>
      <if test="record.allowRebuy != null">
        allow_rebuy = #{record.allowRebuy,jdbcType=TINYINT},
      </if>
      <if test="record.maxUpdateLevel != null">
        max_update_level = #{record.maxUpdateLevel,jdbcType=INTEGER},
      </if>
      <if test="record.rebuyTimes != null">
        rebuy_times = #{record.rebuyTimes,jdbcType=INTEGER},
      </if>
      <if test="record.allowAppend != null">
        allow_append = #{record.allowAppend,jdbcType=TINYINT},
      </if>
      <if test="record.voucher != null">
        voucher = #{record.voucher,jdbcType=INTEGER},
      </if>
      <if test="record.initialScore != null">
        initial_score = #{record.initialScore,jdbcType=INTEGER},
      </if>
      <if test="record.lowerLimit != null">
        lower_limit = #{record.lowerLimit,jdbcType=INTEGER},
      </if>
      <if test="record.entryTime != null">
        entry_time = #{record.entryTime,jdbcType=INTEGER},
      </if>
      <if test="record.showTime != null">
        show_time = #{record.showTime,jdbcType=INTEGER},
      </if>
      <if test="record.gameType != null">
        game_type = #{record.gameType,jdbcType=INTEGER},
      </if>
      <if test="record.gameIcon != null">
        game_icon = #{record.gameIcon,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceFee != null">
        service_fee = #{record.serviceFee,jdbcType=INTEGER},
      </if>
      <if test="record.allowDelay != null">
        allow_delay = #{record.allowDelay,jdbcType=TINYINT},
      </if>
      <if test="record.maxDelayLevel != null">
        max_delay_level = #{record.maxDelayLevel,jdbcType=INTEGER},
      </if>
      <if test="record.advancedEntry != null">
        advanced_entry = #{record.advancedEntry,jdbcType=INTEGER},
      </if>
      <if test="record.prizeName != null">
        prize_name = #{record.prizeName,jdbcType=VARCHAR},
      </if>
      <if test="record.prizeIcon != null">
        prize_icon = #{record.prizeIcon,jdbcType=VARCHAR},
      </if>
      <if test="record.isVirtual != null">
        is_virtual = #{record.isVirtual,jdbcType=TINYINT},
      </if>
      <if test="record.initialPool != null">
        initial_pool = #{record.initialPool,jdbcType=INTEGER},
      </if>
      <if test="record.ip != null">
        ip = #{record.ip,jdbcType=SMALLINT},
      </if>
      <if test="record.gps != null">
        gps = #{record.gps,jdbcType=SMALLINT},
      </if>
      <if test="record.sbChip != null">
        sb_chip = #{record.sbChip,jdbcType=DECIMAL},
      </if>
      <if test="record.hunterMatch != null">
        hunter_match = #{record.hunterMatch,jdbcType=INTEGER},
      </if>
      <if test="record.hunterPercent != null">
        hunter_percent = #{record.hunterPercent,jdbcType=INTEGER},
      </if>
      <if test="record.canshow != null">
        canShow = #{record.canshow,jdbcType=INTEGER},
      </if>
      <if test="record.logoUrl != null">
        logo_url = #{record.logoUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=INTEGER},
      </if>
      <if test="record.serverId != null">
        server_id = #{record.serverId,jdbcType=VARCHAR},
      </if>
      <if test="record.accessIp != null">
        access_ip = #{record.accessIp,jdbcType=VARCHAR},
      </if>
      <if test="record.accessPort != null">
        access_port = #{record.accessPort,jdbcType=INTEGER},
      </if>
      <if test="record.underControl != null">
        under_control = #{record.underControl,jdbcType=TINYINT},
      </if>
      <if test="record.charge != null">
        charge = #{record.charge,jdbcType=INTEGER},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=SMALLINT},
      </if>
      <if test="record.sourceId != null">
        source_id = #{record.sourceId,jdbcType=INTEGER},
      </if>
      <if test="record.tribeId != null">
        tribe_id = #{record.tribeId,jdbcType=INTEGER},
      </if>
      <if test="record.chargePercent != null">
        charge_percent = #{record.chargePercent,jdbcType=INTEGER},
      </if>
      <if test="record.registationFee != null">
        registation_fee = #{record.registationFee,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update mtt_record
    set id = #{record.id,jdbcType=INTEGER},
      game_id = #{record.gameId,jdbcType=INTEGER},
      creator_id = #{record.creatorId,jdbcType=INTEGER},
      creator_name = #{record.creatorName,jdbcType=VARCHAR},
      mtt_name = #{record.mttName,jdbcType=VARCHAR},
      is_official = #{record.isOfficial,jdbcType=TINYINT},
      start_time = #{record.startTime,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=INTEGER},
      status = #{record.status,jdbcType=TINYINT},
      upper_limit = #{record.upperLimit,jdbcType=INTEGER},
      participants = #{record.participants,jdbcType=INTEGER},
      mtt_type = #{record.mttType,jdbcType=INTEGER},
      initial_chip = #{record.initialChip,jdbcType=INTEGER},
      update_cycle = #{record.updateCycle,jdbcType=INTEGER},
      bind_type = #{record.bindType,jdbcType=TINYINT},
      bonus_type = #{record.bonusType,jdbcType=TINYINT},
      allow_rebuy = #{record.allowRebuy,jdbcType=TINYINT},
      max_update_level = #{record.maxUpdateLevel,jdbcType=INTEGER},
      rebuy_times = #{record.rebuyTimes,jdbcType=INTEGER},
      allow_append = #{record.allowAppend,jdbcType=TINYINT},
      voucher = #{record.voucher,jdbcType=INTEGER},
      initial_score = #{record.initialScore,jdbcType=INTEGER},
      lower_limit = #{record.lowerLimit,jdbcType=INTEGER},
      entry_time = #{record.entryTime,jdbcType=INTEGER},
      show_time = #{record.showTime,jdbcType=INTEGER},
      game_type = #{record.gameType,jdbcType=INTEGER},
      game_icon = #{record.gameIcon,jdbcType=VARCHAR},
      service_fee = #{record.serviceFee,jdbcType=INTEGER},
      allow_delay = #{record.allowDelay,jdbcType=TINYINT},
      max_delay_level = #{record.maxDelayLevel,jdbcType=INTEGER},
      advanced_entry = #{record.advancedEntry,jdbcType=INTEGER},
      prize_name = #{record.prizeName,jdbcType=VARCHAR},
      prize_icon = #{record.prizeIcon,jdbcType=VARCHAR},
      is_virtual = #{record.isVirtual,jdbcType=TINYINT},
      initial_pool = #{record.initialPool,jdbcType=INTEGER},
      ip = #{record.ip,jdbcType=SMALLINT},
      gps = #{record.gps,jdbcType=SMALLINT},
      sb_chip = #{record.sbChip,jdbcType=DECIMAL},
      hunter_match = #{record.hunterMatch,jdbcType=INTEGER},
      hunter_percent = #{record.hunterPercent,jdbcType=INTEGER},
      canShow = #{record.canshow,jdbcType=INTEGER},
      logo_url = #{record.logoUrl,jdbcType=VARCHAR},
      op_time = #{record.opTime,jdbcType=INTEGER},
      server_id = #{record.serverId,jdbcType=VARCHAR},
      access_ip = #{record.accessIp,jdbcType=VARCHAR},
      access_port = #{record.accessPort,jdbcType=INTEGER},
      under_control = #{record.underControl,jdbcType=TINYINT},
      charge = #{record.charge,jdbcType=INTEGER},
      source = #{record.source,jdbcType=SMALLINT},
      source_id = #{record.sourceId,jdbcType=INTEGER},
      tribe_id = #{record.tribeId,jdbcType=INTEGER},
      charge_percent = #{record.chargePercent,jdbcType=INTEGER},
      registation_fee = #{record.registationFee,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.MttRoomPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update mtt_record
    <set>
      <if test="gameId != null">
        game_id = #{gameId,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="mttName != null">
        mtt_name = #{mttName,jdbcType=VARCHAR},
      </if>
      <if test="isOfficial != null">
        is_official = #{isOfficial,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="upperLimit != null">
        upper_limit = #{upperLimit,jdbcType=INTEGER},
      </if>
      <if test="participants != null">
        participants = #{participants,jdbcType=INTEGER},
      </if>
      <if test="mttType != null">
        mtt_type = #{mttType,jdbcType=INTEGER},
      </if>
      <if test="initialChip != null">
        initial_chip = #{initialChip,jdbcType=INTEGER},
      </if>
      <if test="updateCycle != null">
        update_cycle = #{updateCycle,jdbcType=INTEGER},
      </if>
      <if test="bindType != null">
        bind_type = #{bindType,jdbcType=TINYINT},
      </if>
      <if test="bonusType != null">
        bonus_type = #{bonusType,jdbcType=TINYINT},
      </if>
      <if test="allowRebuy != null">
        allow_rebuy = #{allowRebuy,jdbcType=TINYINT},
      </if>
      <if test="maxUpdateLevel != null">
        max_update_level = #{maxUpdateLevel,jdbcType=INTEGER},
      </if>
      <if test="rebuyTimes != null">
        rebuy_times = #{rebuyTimes,jdbcType=INTEGER},
      </if>
      <if test="allowAppend != null">
        allow_append = #{allowAppend,jdbcType=TINYINT},
      </if>
      <if test="voucher != null">
        voucher = #{voucher,jdbcType=INTEGER},
      </if>
      <if test="initialScore != null">
        initial_score = #{initialScore,jdbcType=INTEGER},
      </if>
      <if test="lowerLimit != null">
        lower_limit = #{lowerLimit,jdbcType=INTEGER},
      </if>
      <if test="entryTime != null">
        entry_time = #{entryTime,jdbcType=INTEGER},
      </if>
      <if test="showTime != null">
        show_time = #{showTime,jdbcType=INTEGER},
      </if>
      <if test="gameType != null">
        game_type = #{gameType,jdbcType=INTEGER},
      </if>
      <if test="gameIcon != null">
        game_icon = #{gameIcon,jdbcType=VARCHAR},
      </if>
      <if test="serviceFee != null">
        service_fee = #{serviceFee,jdbcType=INTEGER},
      </if>
      <if test="allowDelay != null">
        allow_delay = #{allowDelay,jdbcType=TINYINT},
      </if>
      <if test="maxDelayLevel != null">
        max_delay_level = #{maxDelayLevel,jdbcType=INTEGER},
      </if>
      <if test="advancedEntry != null">
        advanced_entry = #{advancedEntry,jdbcType=INTEGER},
      </if>
      <if test="prizeName != null">
        prize_name = #{prizeName,jdbcType=VARCHAR},
      </if>
      <if test="prizeIcon != null">
        prize_icon = #{prizeIcon,jdbcType=VARCHAR},
      </if>
      <if test="isVirtual != null">
        is_virtual = #{isVirtual,jdbcType=TINYINT},
      </if>
      <if test="initialPool != null">
        initial_pool = #{initialPool,jdbcType=INTEGER},
      </if>
      <if test="ip != null">
        ip = #{ip,jdbcType=SMALLINT},
      </if>
      <if test="gps != null">
        gps = #{gps,jdbcType=SMALLINT},
      </if>
      <if test="sbChip != null">
        sb_chip = #{sbChip,jdbcType=DECIMAL},
      </if>
      <if test="hunterMatch != null">
        hunter_match = #{hunterMatch,jdbcType=INTEGER},
      </if>
      <if test="hunterPercent != null">
        hunter_percent = #{hunterPercent,jdbcType=INTEGER},
      </if>
      <if test="canshow != null">
        canShow = #{canshow,jdbcType=INTEGER},
      </if>
      <if test="logoUrl != null">
        logo_url = #{logoUrl,jdbcType=VARCHAR},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=INTEGER},
      </if>
      <if test="serverId != null">
        server_id = #{serverId,jdbcType=VARCHAR},
      </if>
      <if test="accessIp != null">
        access_ip = #{accessIp,jdbcType=VARCHAR},
      </if>
      <if test="accessPort != null">
        access_port = #{accessPort,jdbcType=INTEGER},
      </if>
      <if test="underControl != null">
        under_control = #{underControl,jdbcType=TINYINT},
      </if>
      <if test="charge != null">
        charge = #{charge,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=SMALLINT},
      </if>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=INTEGER},
      </if>
      <if test="tribeId != null">
        tribe_id = #{tribeId,jdbcType=INTEGER},
      </if>
      <if test="chargePercent != null">
        charge_percent = #{chargePercent,jdbcType=INTEGER},
      </if>
      <if test="registationFee != null">
        registation_fee = #{registationFee,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.MttRoomPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update mtt_record
    set game_id = #{gameId,jdbcType=INTEGER},
      creator_id = #{creatorId,jdbcType=INTEGER},
      creator_name = #{creatorName,jdbcType=VARCHAR},
      mtt_name = #{mttName,jdbcType=VARCHAR},
      is_official = #{isOfficial,jdbcType=TINYINT},
      start_time = #{startTime,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      upper_limit = #{upperLimit,jdbcType=INTEGER},
      participants = #{participants,jdbcType=INTEGER},
      mtt_type = #{mttType,jdbcType=INTEGER},
      initial_chip = #{initialChip,jdbcType=INTEGER},
      update_cycle = #{updateCycle,jdbcType=INTEGER},
      bind_type = #{bindType,jdbcType=TINYINT},
      bonus_type = #{bonusType,jdbcType=TINYINT},
      allow_rebuy = #{allowRebuy,jdbcType=TINYINT},
      max_update_level = #{maxUpdateLevel,jdbcType=INTEGER},
      rebuy_times = #{rebuyTimes,jdbcType=INTEGER},
      allow_append = #{allowAppend,jdbcType=TINYINT},
      voucher = #{voucher,jdbcType=INTEGER},
      initial_score = #{initialScore,jdbcType=INTEGER},
      lower_limit = #{lowerLimit,jdbcType=INTEGER},
      entry_time = #{entryTime,jdbcType=INTEGER},
      show_time = #{showTime,jdbcType=INTEGER},
      game_type = #{gameType,jdbcType=INTEGER},
      game_icon = #{gameIcon,jdbcType=VARCHAR},
      service_fee = #{serviceFee,jdbcType=INTEGER},
      allow_delay = #{allowDelay,jdbcType=TINYINT},
      max_delay_level = #{maxDelayLevel,jdbcType=INTEGER},
      advanced_entry = #{advancedEntry,jdbcType=INTEGER},
      prize_name = #{prizeName,jdbcType=VARCHAR},
      prize_icon = #{prizeIcon,jdbcType=VARCHAR},
      is_virtual = #{isVirtual,jdbcType=TINYINT},
      initial_pool = #{initialPool,jdbcType=INTEGER},
      ip = #{ip,jdbcType=SMALLINT},
      gps = #{gps,jdbcType=SMALLINT},
      sb_chip = #{sbChip,jdbcType=DECIMAL},
      hunter_match = #{hunterMatch,jdbcType=INTEGER},
      hunter_percent = #{hunterPercent,jdbcType=INTEGER},
      canShow = #{canshow,jdbcType=INTEGER},
      logo_url = #{logoUrl,jdbcType=VARCHAR},
      op_time = #{opTime,jdbcType=INTEGER},
      server_id = #{serverId,jdbcType=VARCHAR},
      access_ip = #{accessIp,jdbcType=VARCHAR},
      access_port = #{accessPort,jdbcType=INTEGER},
      under_control = #{underControl,jdbcType=TINYINT},
      charge = #{charge,jdbcType=INTEGER},
      source = #{source,jdbcType=SMALLINT},
      source_id = #{sourceId,jdbcType=INTEGER},
      tribe_id = #{tribeId,jdbcType=INTEGER},
      charge_percent = #{chargePercent,jdbcType=INTEGER},
      registation_fee = #{registationFee,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>