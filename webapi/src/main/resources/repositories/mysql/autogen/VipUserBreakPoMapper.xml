<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dzpk.crazypoker.vip.repositories.mysql.autogen.mapper.VipUserBreakPoMapper">
  <resultMap id="BaseResultMap" type="com.dzpk.crazypoker.vip.repositories.mysql.autogen.model.VipUserBreakPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="user_id" jdbcType="INTEGER" property="userId" />
    <id column="break_user_id" jdbcType="INTEGER" property="breakUserId" />
    <result column="break_on" jdbcType="BIT" property="breakOn" />
    <result column="created_by" jdbcType="INTEGER" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_by" jdbcType="INTEGER" property="updatedBy" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    user_id, break_user_id, break_on, created_by, created_time, updated_by, updated_time
  </sql>
  <select id="selectByExample" parameterType="com.dzpk.crazypoker.vip.repositories.mysql.autogen.model.VipUserBreakPoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from vip_user_break
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="com.dzpk.crazypoker.vip.repositories.mysql.autogen.model.VipUserBreakPoKey" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from vip_user_break
    where user_id = #{userId,jdbcType=INTEGER}
      and break_user_id = #{breakUserId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.dzpk.crazypoker.vip.repositories.mysql.autogen.model.VipUserBreakPoKey">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from vip_user_break
    where user_id = #{userId,jdbcType=INTEGER}
      and break_user_id = #{breakUserId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.dzpk.crazypoker.vip.repositories.mysql.autogen.model.VipUserBreakPoExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from vip_user_break
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.dzpk.crazypoker.vip.repositories.mysql.autogen.model.VipUserBreakPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into vip_user_break (user_id, break_user_id, break_on, 
      created_by, created_time, updated_by, 
      updated_time)
    values (#{userId,jdbcType=INTEGER}, #{breakUserId,jdbcType=INTEGER}, #{breakOn,jdbcType=BIT}, 
      #{createdBy,jdbcType=INTEGER}, #{createdTime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=INTEGER}, 
      #{updatedTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.dzpk.crazypoker.vip.repositories.mysql.autogen.model.VipUserBreakPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into vip_user_break
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="breakUserId != null">
        break_user_id,
      </if>
      <if test="breakOn != null">
        break_on,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="breakUserId != null">
        #{breakUserId,jdbcType=INTEGER},
      </if>
      <if test="breakOn != null">
        #{breakOn,jdbcType=BIT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=INTEGER},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.dzpk.crazypoker.vip.repositories.mysql.autogen.model.VipUserBreakPoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from vip_user_break
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vip_user_break
    <set>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.breakUserId != null">
        break_user_id = #{record.breakUserId,jdbcType=INTEGER},
      </if>
      <if test="record.breakOn != null">
        break_on = #{record.breakOn,jdbcType=BIT},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=INTEGER},
      </if>
      <if test="record.createdTime != null">
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=INTEGER},
      </if>
      <if test="record.updatedTime != null">
        updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vip_user_break
    set user_id = #{record.userId,jdbcType=INTEGER},
      break_user_id = #{record.breakUserId,jdbcType=INTEGER},
      break_on = #{record.breakOn,jdbcType=BIT},
      created_by = #{record.createdBy,jdbcType=INTEGER},
      created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=INTEGER},
      updated_time = #{record.updatedTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.dzpk.crazypoker.vip.repositories.mysql.autogen.model.VipUserBreakPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vip_user_break
    <set>
      <if test="breakOn != null">
        break_on = #{breakOn,jdbcType=BIT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=INTEGER},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where user_id = #{userId,jdbcType=INTEGER}
      and break_user_id = #{breakUserId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dzpk.crazypoker.vip.repositories.mysql.autogen.model.VipUserBreakPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update vip_user_break
    set break_on = #{breakOn,jdbcType=BIT},
      created_by = #{createdBy,jdbcType=INTEGER},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=INTEGER},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP}
    where user_id = #{userId,jdbcType=INTEGER}
      and break_user_id = #{breakUserId,jdbcType=INTEGER}
  </update>
</mapper>