<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dzpk.crazypoker.room.repositories.mysql.autogen.mapper.RoomCreationPlanConfigPoMapper">
  <resultMap id="BaseResultMap" type="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.RoomCreationPlanConfigPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="room_path" jdbcType="INTEGER" property="roomPath" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="player_count" jdbcType="INTEGER" property="playerCount" />
    <result column="auto_player_count" jdbcType="INTEGER" property="autoPlayerCount" />
    <result column="sb_chip" jdbcType="INTEGER" property="sbChip" />
    <result column="in_chip" jdbcType="INTEGER" property="inChip" />
    <result column="max_play_time" jdbcType="INTEGER" property="maxPlayTime" />
    <result column="op_time" jdbcType="INTEGER" property="opTime" />
    <result column="min_rate" jdbcType="INTEGER" property="minRate" />
    <result column="max_rate" jdbcType="INTEGER" property="maxRate" />
    <result column="qianzhu" jdbcType="INTEGER" property="qianzhu" />
    <result column="min_play_time" jdbcType="INTEGER" property="minPlayTime" />
    <result column="straddle" jdbcType="INTEGER" property="straddle" />
    <result column="ip" jdbcType="INTEGER" property="ip" />
    <result column="leave_table" jdbcType="INTEGER" property="leaveTable" />
    <result column="vp" jdbcType="INTEGER" property="vp" />
    <result column="muck_switch" jdbcType="INTEGER" property="muckSwitch" />
    <result column="insurance" jdbcType="INTEGER" property="insurance" />
    <result column="limit_gps" jdbcType="INTEGER" property="limitGps" />
    <result column="room_mode" jdbcType="INTEGER" property="roomMode" />
    <result column="jackpot_on" jdbcType="INTEGER" property="jackpotOn" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="empty_seat_config" jdbcType="INTEGER" property="emptySeatConfig" />
    <result column="start_time" jdbcType="TIME" property="startTime" />
    <result column="end_time" jdbcType="TIME" property="endTime" />
    <result column="empty_seat_sum" jdbcType="INTEGER" property="emptySeatSum" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="INTEGER" property="createBy" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="INTEGER" property="updateBy" />
    <result column="auto_dismiss" jdbcType="INTEGER" property="autoDismiss" />
    <result column="rooms_limit" jdbcType="INTEGER" property="roomsLimit" />
    <result column="lable_name" jdbcType="VARCHAR" property="lableName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, room_path, name, player_count, auto_player_count, sb_chip, in_chip, max_play_time, 
    op_time, min_rate, max_rate, qianzhu, min_play_time, straddle, ip, leave_table, vp, 
    muck_switch, insurance, limit_gps, room_mode, jackpot_on, status, empty_seat_config, 
    start_time, end_time, empty_seat_sum, create_time, create_by, creator_name, update_time, 
    update_by, auto_dismiss, rooms_limit, lable_name
  </sql>
  <select id="selectByExample" parameterType="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.RoomCreationPlanConfigPoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from room_creation_plan_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from room_creation_plan_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from room_creation_plan_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.RoomCreationPlanConfigPoExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from room_creation_plan_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.RoomCreationPlanConfigPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into room_creation_plan_config (id, room_path, name, 
      player_count, auto_player_count, sb_chip, 
      in_chip, max_play_time, op_time, 
      min_rate, max_rate, qianzhu, 
      min_play_time, straddle, ip, 
      leave_table, vp, muck_switch, 
      insurance, limit_gps, room_mode, 
      jackpot_on, status, empty_seat_config, 
      start_time, end_time, empty_seat_sum, 
      create_time, create_by, creator_name, 
      update_time, update_by, auto_dismiss, 
      rooms_limit, lable_name)
    values (#{id,jdbcType=INTEGER}, #{roomPath,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, 
      #{playerCount,jdbcType=INTEGER}, #{autoPlayerCount,jdbcType=INTEGER}, #{sbChip,jdbcType=INTEGER}, 
      #{inChip,jdbcType=INTEGER}, #{maxPlayTime,jdbcType=INTEGER}, #{opTime,jdbcType=INTEGER}, 
      #{minRate,jdbcType=INTEGER}, #{maxRate,jdbcType=INTEGER}, #{qianzhu,jdbcType=INTEGER}, 
      #{minPlayTime,jdbcType=INTEGER}, #{straddle,jdbcType=INTEGER}, #{ip,jdbcType=INTEGER}, 
      #{leaveTable,jdbcType=INTEGER}, #{vp,jdbcType=INTEGER}, #{muckSwitch,jdbcType=INTEGER}, 
      #{insurance,jdbcType=INTEGER}, #{limitGps,jdbcType=INTEGER}, #{roomMode,jdbcType=INTEGER}, 
      #{jackpotOn,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{emptySeatConfig,jdbcType=INTEGER}, 
      #{startTime,jdbcType=TIME}, #{endTime,jdbcType=TIME}, #{emptySeatSum,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=INTEGER}, #{autoDismiss,jdbcType=INTEGER}, 
      #{roomsLimit,jdbcType=INTEGER}, #{lableName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.RoomCreationPlanConfigPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into room_creation_plan_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="roomPath != null">
        room_path,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="playerCount != null">
        player_count,
      </if>
      <if test="autoPlayerCount != null">
        auto_player_count,
      </if>
      <if test="sbChip != null">
        sb_chip,
      </if>
      <if test="inChip != null">
        in_chip,
      </if>
      <if test="maxPlayTime != null">
        max_play_time,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="minRate != null">
        min_rate,
      </if>
      <if test="maxRate != null">
        max_rate,
      </if>
      <if test="qianzhu != null">
        qianzhu,
      </if>
      <if test="minPlayTime != null">
        min_play_time,
      </if>
      <if test="straddle != null">
        straddle,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="leaveTable != null">
        leave_table,
      </if>
      <if test="vp != null">
        vp,
      </if>
      <if test="muckSwitch != null">
        muck_switch,
      </if>
      <if test="insurance != null">
        insurance,
      </if>
      <if test="limitGps != null">
        limit_gps,
      </if>
      <if test="roomMode != null">
        room_mode,
      </if>
      <if test="jackpotOn != null">
        jackpot_on,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="emptySeatConfig != null">
        empty_seat_config,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="emptySeatSum != null">
        empty_seat_sum,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="creatorName != null">
        creator_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="autoDismiss != null">
        auto_dismiss,
      </if>
      <if test="roomsLimit != null">
        rooms_limit,
      </if>
      <if test="lableName != null">
        lable_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="roomPath != null">
        #{roomPath,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="playerCount != null">
        #{playerCount,jdbcType=INTEGER},
      </if>
      <if test="autoPlayerCount != null">
        #{autoPlayerCount,jdbcType=INTEGER},
      </if>
      <if test="sbChip != null">
        #{sbChip,jdbcType=INTEGER},
      </if>
      <if test="inChip != null">
        #{inChip,jdbcType=INTEGER},
      </if>
      <if test="maxPlayTime != null">
        #{maxPlayTime,jdbcType=INTEGER},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=INTEGER},
      </if>
      <if test="minRate != null">
        #{minRate,jdbcType=INTEGER},
      </if>
      <if test="maxRate != null">
        #{maxRate,jdbcType=INTEGER},
      </if>
      <if test="qianzhu != null">
        #{qianzhu,jdbcType=INTEGER},
      </if>
      <if test="minPlayTime != null">
        #{minPlayTime,jdbcType=INTEGER},
      </if>
      <if test="straddle != null">
        #{straddle,jdbcType=INTEGER},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=INTEGER},
      </if>
      <if test="leaveTable != null">
        #{leaveTable,jdbcType=INTEGER},
      </if>
      <if test="vp != null">
        #{vp,jdbcType=INTEGER},
      </if>
      <if test="muckSwitch != null">
        #{muckSwitch,jdbcType=INTEGER},
      </if>
      <if test="insurance != null">
        #{insurance,jdbcType=INTEGER},
      </if>
      <if test="limitGps != null">
        #{limitGps,jdbcType=INTEGER},
      </if>
      <if test="roomMode != null">
        #{roomMode,jdbcType=INTEGER},
      </if>
      <if test="jackpotOn != null">
        #{jackpotOn,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="emptySeatConfig != null">
        #{emptySeatConfig,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIME},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIME},
      </if>
      <if test="emptySeatSum != null">
        #{emptySeatSum,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=INTEGER},
      </if>
      <if test="autoDismiss != null">
        #{autoDismiss,jdbcType=INTEGER},
      </if>
      <if test="roomsLimit != null">
        #{roomsLimit,jdbcType=INTEGER},
      </if>
      <if test="lableName != null">
        #{lableName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.RoomCreationPlanConfigPoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from room_creation_plan_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update room_creation_plan_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.roomPath != null">
        room_path = #{record.roomPath,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.playerCount != null">
        player_count = #{record.playerCount,jdbcType=INTEGER},
      </if>
      <if test="record.autoPlayerCount != null">
        auto_player_count = #{record.autoPlayerCount,jdbcType=INTEGER},
      </if>
      <if test="record.sbChip != null">
        sb_chip = #{record.sbChip,jdbcType=INTEGER},
      </if>
      <if test="record.inChip != null">
        in_chip = #{record.inChip,jdbcType=INTEGER},
      </if>
      <if test="record.maxPlayTime != null">
        max_play_time = #{record.maxPlayTime,jdbcType=INTEGER},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=INTEGER},
      </if>
      <if test="record.minRate != null">
        min_rate = #{record.minRate,jdbcType=INTEGER},
      </if>
      <if test="record.maxRate != null">
        max_rate = #{record.maxRate,jdbcType=INTEGER},
      </if>
      <if test="record.qianzhu != null">
        qianzhu = #{record.qianzhu,jdbcType=INTEGER},
      </if>
      <if test="record.minPlayTime != null">
        min_play_time = #{record.minPlayTime,jdbcType=INTEGER},
      </if>
      <if test="record.straddle != null">
        straddle = #{record.straddle,jdbcType=INTEGER},
      </if>
      <if test="record.ip != null">
        ip = #{record.ip,jdbcType=INTEGER},
      </if>
      <if test="record.leaveTable != null">
        leave_table = #{record.leaveTable,jdbcType=INTEGER},
      </if>
      <if test="record.vp != null">
        vp = #{record.vp,jdbcType=INTEGER},
      </if>
      <if test="record.muckSwitch != null">
        muck_switch = #{record.muckSwitch,jdbcType=INTEGER},
      </if>
      <if test="record.insurance != null">
        insurance = #{record.insurance,jdbcType=INTEGER},
      </if>
      <if test="record.limitGps != null">
        limit_gps = #{record.limitGps,jdbcType=INTEGER},
      </if>
      <if test="record.roomMode != null">
        room_mode = #{record.roomMode,jdbcType=INTEGER},
      </if>
      <if test="record.jackpotOn != null">
        jackpot_on = #{record.jackpotOn,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.emptySeatConfig != null">
        empty_seat_config = #{record.emptySeatConfig,jdbcType=INTEGER},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=TIME},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIME},
      </if>
      <if test="record.emptySeatSum != null">
        empty_seat_sum = #{record.emptySeatSum,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=INTEGER},
      </if>
      <if test="record.creatorName != null">
        creator_name = #{record.creatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=INTEGER},
      </if>
      <if test="record.autoDismiss != null">
        auto_dismiss = #{record.autoDismiss,jdbcType=INTEGER},
      </if>
      <if test="record.roomsLimit != null">
        rooms_limit = #{record.roomsLimit,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update room_creation_plan_config
    set id = #{record.id,jdbcType=INTEGER},
      room_path = #{record.roomPath,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      player_count = #{record.playerCount,jdbcType=INTEGER},
      auto_player_count = #{record.autoPlayerCount,jdbcType=INTEGER},
      sb_chip = #{record.sbChip,jdbcType=INTEGER},
      in_chip = #{record.inChip,jdbcType=INTEGER},
      max_play_time = #{record.maxPlayTime,jdbcType=INTEGER},
      op_time = #{record.opTime,jdbcType=INTEGER},
      min_rate = #{record.minRate,jdbcType=INTEGER},
      max_rate = #{record.maxRate,jdbcType=INTEGER},
      qianzhu = #{record.qianzhu,jdbcType=INTEGER},
      min_play_time = #{record.minPlayTime,jdbcType=INTEGER},
      straddle = #{record.straddle,jdbcType=INTEGER},
      ip = #{record.ip,jdbcType=INTEGER},
      leave_table = #{record.leaveTable,jdbcType=INTEGER},
      vp = #{record.vp,jdbcType=INTEGER},
      muck_switch = #{record.muckSwitch,jdbcType=INTEGER},
      insurance = #{record.insurance,jdbcType=INTEGER},
      limit_gps = #{record.limitGps,jdbcType=INTEGER},
      room_mode = #{record.roomMode,jdbcType=INTEGER},
      jackpot_on = #{record.jackpotOn,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      empty_seat_config = #{record.emptySeatConfig,jdbcType=INTEGER},
      start_time = #{record.startTime,jdbcType=TIME},
      end_time = #{record.endTime,jdbcType=TIME},
      empty_seat_sum = #{record.emptySeatSum,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_by = #{record.createBy,jdbcType=INTEGER},
      creator_name = #{record.creatorName,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=INTEGER},
      auto_dismiss = #{record.autoDismiss,jdbcType=INTEGER},
      rooms_limit = #{record.roomsLimit,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.RoomCreationPlanConfigPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update room_creation_plan_config
    <set>
      <if test="roomPath != null">
        room_path = #{roomPath,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="playerCount != null">
        player_count = #{playerCount,jdbcType=INTEGER},
      </if>
      <if test="autoPlayerCount != null">
        auto_player_count = #{autoPlayerCount,jdbcType=INTEGER},
      </if>
      <if test="sbChip != null">
        sb_chip = #{sbChip,jdbcType=INTEGER},
      </if>
      <if test="inChip != null">
        in_chip = #{inChip,jdbcType=INTEGER},
      </if>
      <if test="maxPlayTime != null">
        max_play_time = #{maxPlayTime,jdbcType=INTEGER},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=INTEGER},
      </if>
      <if test="minRate != null">
        min_rate = #{minRate,jdbcType=INTEGER},
      </if>
      <if test="maxRate != null">
        max_rate = #{maxRate,jdbcType=INTEGER},
      </if>
      <if test="qianzhu != null">
        qianzhu = #{qianzhu,jdbcType=INTEGER},
      </if>
      <if test="minPlayTime != null">
        min_play_time = #{minPlayTime,jdbcType=INTEGER},
      </if>
      <if test="straddle != null">
        straddle = #{straddle,jdbcType=INTEGER},
      </if>
      <if test="ip != null">
        ip = #{ip,jdbcType=INTEGER},
      </if>
      <if test="leaveTable != null">
        leave_table = #{leaveTable,jdbcType=INTEGER},
      </if>
      <if test="vp != null">
        vp = #{vp,jdbcType=INTEGER},
      </if>
      <if test="muckSwitch != null">
        muck_switch = #{muckSwitch,jdbcType=INTEGER},
      </if>
      <if test="insurance != null">
        insurance = #{insurance,jdbcType=INTEGER},
      </if>
      <if test="limitGps != null">
        limit_gps = #{limitGps,jdbcType=INTEGER},
      </if>
      <if test="roomMode != null">
        room_mode = #{roomMode,jdbcType=INTEGER},
      </if>
      <if test="jackpotOn != null">
        jackpot_on = #{jackpotOn,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="emptySeatConfig != null">
        empty_seat_config = #{emptySeatConfig,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIME},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIME},
      </if>
      <if test="emptySeatSum != null">
        empty_seat_sum = #{emptySeatSum,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=INTEGER},
      </if>
      <if test="autoDismiss != null">
        auto_dismiss = #{autoDismiss,jdbcType=INTEGER},
      </if>
      <if test="roomsLimit != null">
        rooms_limit = #{roomsLimit,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dzpk.crazypoker.room.repositories.mysql.autogen.model.RoomCreationPlanConfigPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update room_creation_plan_config
    set room_path = #{roomPath,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      player_count = #{playerCount,jdbcType=INTEGER},
      auto_player_count = #{autoPlayerCount,jdbcType=INTEGER},
      sb_chip = #{sbChip,jdbcType=INTEGER},
      in_chip = #{inChip,jdbcType=INTEGER},
      max_play_time = #{maxPlayTime,jdbcType=INTEGER},
      op_time = #{opTime,jdbcType=INTEGER},
      min_rate = #{minRate,jdbcType=INTEGER},
      max_rate = #{maxRate,jdbcType=INTEGER},
      qianzhu = #{qianzhu,jdbcType=INTEGER},
      min_play_time = #{minPlayTime,jdbcType=INTEGER},
      straddle = #{straddle,jdbcType=INTEGER},
      ip = #{ip,jdbcType=INTEGER},
      leave_table = #{leaveTable,jdbcType=INTEGER},
      vp = #{vp,jdbcType=INTEGER},
      muck_switch = #{muckSwitch,jdbcType=INTEGER},
      insurance = #{insurance,jdbcType=INTEGER},
      limit_gps = #{limitGps,jdbcType=INTEGER},
      room_mode = #{roomMode,jdbcType=INTEGER},
      jackpot_on = #{jackpotOn,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      empty_seat_config = #{emptySeatConfig,jdbcType=INTEGER},
      start_time = #{startTime,jdbcType=TIME},
      end_time = #{endTime,jdbcType=TIME},
      empty_seat_sum = #{emptySeatSum,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=INTEGER},
      creator_name = #{creatorName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=INTEGER},
      auto_dismiss = #{autoDismiss,jdbcType=INTEGER},
      rooms_limit = #{roomsLimit,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>