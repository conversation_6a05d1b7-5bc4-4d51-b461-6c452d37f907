<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dzpk.crazypoker.vip.repositories.mysql.autogen.mapper.UserVipPoMapper">
  <resultMap id="BaseResultMap" type="com.dzpk.crazypoker.vip.repositories.mysql.autogen.model.UserVipPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="vip_code" jdbcType="INTEGER" property="vipCode" />
    <result column="card_type" jdbcType="TINYINT" property="cardType" />
    <result column="card_period" jdbcType="INTEGER" property="cardPeriod" />
    <result column="open_fee" jdbcType="INTEGER" property="openFee" />
    <result column="start_date" jdbcType="TIMESTAMP" property="startDate" />
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
    <result column="created_by" jdbcType="INTEGER" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_by" jdbcType="INTEGER" property="updatedBy" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    user_id, vip_code, card_type, card_period, open_fee, start_date, end_date, created_by, 
    created_time, updated_by, updated_time
  </sql>
  <select id="selectByExample" parameterType="com.dzpk.crazypoker.vip.repositories.mysql.autogen.model.UserVipPoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user_vip
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from user_vip
    where user_id = #{userId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from user_vip
    where user_id = #{userId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.dzpk.crazypoker.vip.repositories.mysql.autogen.model.UserVipPoExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from user_vip
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.dzpk.crazypoker.vip.repositories.mysql.autogen.model.UserVipPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into user_vip (user_id, vip_code, card_type, 
      card_period, open_fee, start_date, 
      end_date, created_by, created_time, 
      updated_by, updated_time)
    values (#{userId,jdbcType=INTEGER}, #{vipCode,jdbcType=INTEGER}, #{cardType,jdbcType=TINYINT}, 
      #{cardPeriod,jdbcType=INTEGER}, #{openFee,jdbcType=INTEGER}, #{startDate,jdbcType=TIMESTAMP}, 
      #{endDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=INTEGER}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{updatedBy,jdbcType=INTEGER}, #{updatedTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.dzpk.crazypoker.vip.repositories.mysql.autogen.model.UserVipPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into user_vip
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="vipCode != null">
        vip_code,
      </if>
      <if test="cardType != null">
        card_type,
      </if>
      <if test="cardPeriod != null">
        card_period,
      </if>
      <if test="openFee != null">
        open_fee,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="vipCode != null">
        #{vipCode,jdbcType=INTEGER},
      </if>
      <if test="cardType != null">
        #{cardType,jdbcType=TINYINT},
      </if>
      <if test="cardPeriod != null">
        #{cardPeriod,jdbcType=INTEGER},
      </if>
      <if test="openFee != null">
        #{openFee,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=INTEGER},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.dzpk.crazypoker.vip.repositories.mysql.autogen.model.UserVipPoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from user_vip
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_vip
    <set>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.vipCode != null">
        vip_code = #{record.vipCode,jdbcType=INTEGER},
      </if>
      <if test="record.cardType != null">
        card_type = #{record.cardType,jdbcType=TINYINT},
      </if>
      <if test="record.cardPeriod != null">
        card_period = #{record.cardPeriod,jdbcType=INTEGER},
      </if>
      <if test="record.openFee != null">
        open_fee = #{record.openFee,jdbcType=INTEGER},
      </if>
      <if test="record.startDate != null">
        start_date = #{record.startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endDate != null">
        end_date = #{record.endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=INTEGER},
      </if>
      <if test="record.createdTime != null">
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=INTEGER},
      </if>
      <if test="record.updatedTime != null">
        updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_vip
    set user_id = #{record.userId,jdbcType=INTEGER},
      vip_code = #{record.vipCode,jdbcType=INTEGER},
      card_type = #{record.cardType,jdbcType=TINYINT},
      card_period = #{record.cardPeriod,jdbcType=INTEGER},
      open_fee = #{record.openFee,jdbcType=INTEGER},
      start_date = #{record.startDate,jdbcType=TIMESTAMP},
      end_date = #{record.endDate,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=INTEGER},
      created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=INTEGER},
      updated_time = #{record.updatedTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.dzpk.crazypoker.vip.repositories.mysql.autogen.model.UserVipPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_vip
    <set>
      <if test="vipCode != null">
        vip_code = #{vipCode,jdbcType=INTEGER},
      </if>
      <if test="cardType != null">
        card_type = #{cardType,jdbcType=TINYINT},
      </if>
      <if test="cardPeriod != null">
        card_period = #{cardPeriod,jdbcType=INTEGER},
      </if>
      <if test="openFee != null">
        open_fee = #{openFee,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=INTEGER},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where user_id = #{userId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dzpk.crazypoker.vip.repositories.mysql.autogen.model.UserVipPo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_vip
    set vip_code = #{vipCode,jdbcType=INTEGER},
      card_type = #{cardType,jdbcType=TINYINT},
      card_period = #{cardPeriod,jdbcType=INTEGER},
      open_fee = #{openFee,jdbcType=INTEGER},
      start_date = #{startDate,jdbcType=TIMESTAMP},
      end_date = #{endDate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=INTEGER},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=INTEGER},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP}
    where user_id = #{userId,jdbcType=INTEGER}
  </update>
</mapper>