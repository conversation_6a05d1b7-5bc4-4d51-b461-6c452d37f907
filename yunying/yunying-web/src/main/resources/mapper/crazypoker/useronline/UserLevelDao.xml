<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.useronline.dao.UserLevelDao">
    <!-- 在这里写自定义的sql，autogen 目录下的文件不要手动修改 -->
    <insert id="insertOrUpdate">
        insert into user_level (user_id, level_code, is_auto, created_time, created_by, updated_time, updated_by)
            value (#{userId}, #{levelCode}, 0, now(), #{operatorId}, now(), #{operatorId})
        on duplicate key update level_code   = #{levelCode},
                                updated_time = now(),
                                updated_by   = #{operatorId}
    </insert>
</mapper>