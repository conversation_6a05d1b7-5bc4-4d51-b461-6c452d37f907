<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.useronline.dao.UserJoinRoomDao">
  <resultMap id="BaseResultMap" type="com.allinpokers.yunying.useronline.entity.UserJoinRoom">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="user_id" jdbcType="INTEGER" property="userId" />
    <id column="room_id" jdbcType="INTEGER" property="roomId" />
    <result column="room_path" jdbcType="INTEGER" property="roomPath" />
    <result column="is_close" jdbcType="BIT" property="isClose" />
    <result column="on_seat" jdbcType="BIT" property="onSeat" />
    <result column="has_bringin" jdbcType="BIT" property="hasBringin" />
    <result column="is_trust" jdbcType="BIT" property="isTrust" />
    <result column="is_at" jdbcType="BIT" property="isAt" />
    <result column="bringin_longitude" jdbcType="DOUBLE" property="bringinLongitude" />
    <result column="bringin_latitude" jdbcType="DOUBLE" property="bringinLatitude" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="updated_type" jdbcType="TINYINT" property="updatedType" />
    <result column="club_id" jdbcType="INTEGER" property="clubId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    user_id, room_id, room_path, is_close, on_seat, has_bringin, is_trust, is_at, bringin_longitude, 
    bringin_latitude, updated_time, updated_type, club_id
  </sql>
  <select id="selectByExample" parameterType="com.allinpokers.yunying.useronline.entity.example.UserJoinRoomExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user_join_room
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="com.allinpokers.yunying.useronline.entity.key.UserJoinRoomKey" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from user_join_room
    where user_id = #{userId,jdbcType=INTEGER}
      and room_id = #{roomId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.allinpokers.yunying.useronline.entity.key.UserJoinRoomKey">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from user_join_room
    where user_id = #{userId,jdbcType=INTEGER}
      and room_id = #{roomId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.allinpokers.yunying.useronline.entity.example.UserJoinRoomExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from user_join_room
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.allinpokers.yunying.useronline.entity.UserJoinRoom">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into user_join_room (user_id, room_id, room_path, 
      is_close, on_seat, has_bringin, is_trust, 
      is_at, bringin_longitude, bringin_latitude, 
      updated_time, updated_type, club_id)
    values (#{userId,jdbcType=INTEGER}, #{roomId,jdbcType=INTEGER}, #{roomPath,jdbcType=INTEGER},
      #{isClose,jdbcType=BIT}, #{onSeat,jdbcType=BIT}, #{hasBringin,jdbcType=BIT}, #{isTrust,jdbcType=BIT}, 
      #{isAt,jdbcType=BIT}, #{bringinLongitude,jdbcType=DOUBLE}, #{bringinLatitude,jdbcType=DOUBLE}, 
      #{updatedTime,jdbcType=TIMESTAMP}, #{updatedType,jdbcType=TINYINT}, #{clubId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.allinpokers.yunying.useronline.entity.UserJoinRoom">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into user_join_room
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="roomId != null">
        room_id,
      </if>
      <if test="roomPath != null">
        room_path,
      </if>
      <if test="isClose != null">
        is_close,
      </if>
      <if test="onSeat != null">
        on_seat,
      </if>
      <if test="hasBringin != null">
        has_bringin,
      </if>
      <if test="isTrust != null">
        is_trust,
      </if>
      <if test="isAt != null">
        is_at,
      </if>
      <if test="bringinLongitude != null">
        bringin_longitude,
      </if>
      <if test="bringinLatitude != null">
        bringin_latitude,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
      <if test="updatedType != null">
        updated_type,
      </if>
      <if test="clubId != null">
        club_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="roomId != null">
        #{roomId,jdbcType=INTEGER},
      </if>
      <if test="roomPath != null">
        #{roomPath,jdbcType=INTEGER},
      </if>
      <if test="isClose != null">
        #{isClose,jdbcType=BIT},
      </if>
      <if test="onSeat != null">
        #{onSeat,jdbcType=BIT},
      </if>
      <if test="hasBringin != null">
        #{hasBringin,jdbcType=BIT},
      </if>
      <if test="isTrust != null">
        #{isTrust,jdbcType=BIT},
      </if>
      <if test="isAt != null">
        #{isAt,jdbcType=BIT},
      </if>
      <if test="bringinLongitude != null">
        #{bringinLongitude,jdbcType=DOUBLE},
      </if>
      <if test="bringinLatitude != null">
        #{bringinLatitude,jdbcType=DOUBLE},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedType != null">
        #{updatedType,jdbcType=TINYINT},
      </if>
      <if test="clubId != null">
        #{clubId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.allinpokers.yunying.useronline.entity.example.UserJoinRoomExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from user_join_room
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_join_room
    <set>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.roomId != null">
        room_id = #{record.roomId,jdbcType=INTEGER},
      </if>
      <if test="record.roomPath != null">
        room_path = #{record.roomPath,jdbcType=INTEGER},
      </if>
      <if test="record.isClose != null">
        is_close = #{record.isClose,jdbcType=BIT},
      </if>
      <if test="record.onSeat != null">
        on_seat = #{record.onSeat,jdbcType=BIT},
      </if>
      <if test="record.hasBringin != null">
        has_bringin = #{record.hasBringin,jdbcType=BIT},
      </if>
      <if test="record.isTrust != null">
        is_trust = #{record.isTrust,jdbcType=BIT},
      </if>
      <if test="record.isAt != null">
        is_at = #{record.isAt,jdbcType=BIT},
      </if>
      <if test="record.bringinLongitude != null">
        bringin_longitude = #{record.bringinLongitude,jdbcType=DOUBLE},
      </if>
      <if test="record.bringinLatitude != null">
        bringin_latitude = #{record.bringinLatitude,jdbcType=DOUBLE},
      </if>
      <if test="record.updatedTime != null">
        updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedType != null">
        updated_type = #{record.updatedType,jdbcType=TINYINT},
      </if>
      <if test="record.clubId != null">
        club_id = #{record.clubId,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_join_room
    set user_id = #{record.userId,jdbcType=INTEGER},
      room_id = #{record.roomId,jdbcType=INTEGER},
      room_path = #{record.roomPath,jdbcType=INTEGER},
      is_close = #{record.isClose,jdbcType=BIT},
      on_seat = #{record.onSeat,jdbcType=BIT},
      has_bringin = #{record.hasBringin,jdbcType=BIT},
      is_trust = #{record.isTrust,jdbcType=BIT},
      is_at = #{record.isAt,jdbcType=BIT},
      bringin_longitude = #{record.bringinLongitude,jdbcType=DOUBLE},
      bringin_latitude = #{record.bringinLatitude,jdbcType=DOUBLE},
      updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      updated_type = #{record.updatedType,jdbcType=TINYINT},
      club_id = #{record.clubId,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.allinpokers.yunying.useronline.entity.UserJoinRoom">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_join_room
    <set>
      <if test="roomPath != null">
        room_path = #{roomPath,jdbcType=INTEGER},
      </if>
      <if test="isClose != null">
        is_close = #{isClose,jdbcType=BIT},
      </if>
      <if test="onSeat != null">
        on_seat = #{onSeat,jdbcType=BIT},
      </if>
      <if test="hasBringin != null">
        has_bringin = #{hasBringin,jdbcType=BIT},
      </if>
      <if test="isTrust != null">
        is_trust = #{isTrust,jdbcType=BIT},
      </if>
      <if test="isAt != null">
        is_at = #{isAt,jdbcType=BIT},
      </if>
      <if test="bringinLongitude != null">
        bringin_longitude = #{bringinLongitude,jdbcType=DOUBLE},
      </if>
      <if test="bringinLatitude != null">
        bringin_latitude = #{bringinLatitude,jdbcType=DOUBLE},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedType != null">
        updated_type = #{updatedType,jdbcType=TINYINT},
      </if>
      <if test="clubId != null">
        club_id = #{clubId,jdbcType=INTEGER},
      </if>
    </set>
    where user_id = #{userId,jdbcType=INTEGER}
      and room_id = #{roomId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.allinpokers.yunying.useronline.entity.UserJoinRoom">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_join_room
    set room_path = #{roomPath,jdbcType=INTEGER},
      is_close = #{isClose,jdbcType=BIT},
      on_seat = #{onSeat,jdbcType=BIT},
      has_bringin = #{hasBringin,jdbcType=BIT},
      is_trust = #{isTrust,jdbcType=BIT},
      is_at = #{isAt,jdbcType=BIT},
      bringin_longitude = #{bringinLongitude,jdbcType=DOUBLE},
      bringin_latitude = #{bringinLatitude,jdbcType=DOUBLE},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      updated_type = #{updatedType,jdbcType=TINYINT},
      club_id = #{clubId,jdbcType=INTEGER}
    where user_id = #{userId,jdbcType=INTEGER}
      and room_id = #{roomId,jdbcType=INTEGER}
  </update>
</mapper>