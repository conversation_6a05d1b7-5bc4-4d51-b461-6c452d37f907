<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.ClubMembersDao">
  <resultMap id="BaseResultMap" type="com.allinpokers.yunying.entity.crazypoker.ClubMembers">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="club_id" jdbcType="INTEGER" property="clubId" />
    <id column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="invite_id" jdbcType="VARCHAR" property="inviteId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="current_credit" jdbcType="INTEGER" property="currentCredit" />
    <result column="initial_credit" jdbcType="INTEGER" property="initialCredit" />
    <result column="credit_status" jdbcType="INTEGER" property="creditStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="clear_request_status" jdbcType="INTEGER" property="clearRequestStatus" />
    <result column="ratio" jdbcType="INTEGER" property="ratio" />
    <result column="ratioStatus" jdbcType="INTEGER" property="ratiostatus" />
    <result column="push_status" jdbcType="INTEGER" property="pushStatus" />
    <result column="promotion_type" jdbcType="INTEGER" property="promotionType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    club_id, user_id, invite_id, type, current_credit, initial_credit, credit_status, 
    create_time, clear_request_status, ratio, ratioStatus, push_status, promotion_type
  </sql>
  <select id="selectByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.ClubMembersExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from club_members
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from club_members
    where club_id = #{clubId,jdbcType=INTEGER}
      and user_id = #{userId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from club_members
    where club_id = #{clubId,jdbcType=INTEGER}
      and user_id = #{userId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.ClubMembersExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from club_members
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.allinpokers.yunying.entity.crazypoker.ClubMembers">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into club_members (club_id, user_id, invite_id, 
      type, current_credit, initial_credit, 
      credit_status, create_time, clear_request_status, 
      ratio, ratioStatus, push_status, 
      promotion_type)
    values (#{clubId,jdbcType=INTEGER}, #{userId,jdbcType=VARCHAR}, #{inviteId,jdbcType=VARCHAR}, 
      #{type,jdbcType=INTEGER}, #{currentCredit,jdbcType=INTEGER}, #{initialCredit,jdbcType=INTEGER}, 
      #{creditStatus,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{clearRequestStatus,jdbcType=INTEGER}, 
      #{ratio,jdbcType=INTEGER}, #{ratiostatus,jdbcType=INTEGER}, #{pushStatus,jdbcType=INTEGER}, 
      #{promotionType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.allinpokers.yunying.entity.crazypoker.ClubMembers">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into club_members
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="clubId != null">
        club_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="inviteId != null">
        invite_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="currentCredit != null">
        current_credit,
      </if>
      <if test="initialCredit != null">
        initial_credit,
      </if>
      <if test="creditStatus != null">
        credit_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="clearRequestStatus != null">
        clear_request_status,
      </if>
      <if test="ratio != null">
        ratio,
      </if>
      <if test="ratiostatus != null">
        ratioStatus,
      </if>
      <if test="pushStatus != null">
        push_status,
      </if>
      <if test="promotionType != null">
        promotion_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="clubId != null">
        #{clubId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="inviteId != null">
        #{inviteId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="currentCredit != null">
        #{currentCredit,jdbcType=INTEGER},
      </if>
      <if test="initialCredit != null">
        #{initialCredit,jdbcType=INTEGER},
      </if>
      <if test="creditStatus != null">
        #{creditStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="clearRequestStatus != null">
        #{clearRequestStatus,jdbcType=INTEGER},
      </if>
      <if test="ratio != null">
        #{ratio,jdbcType=INTEGER},
      </if>
      <if test="ratiostatus != null">
        #{ratiostatus,jdbcType=INTEGER},
      </if>
      <if test="pushStatus != null">
        #{pushStatus,jdbcType=INTEGER},
      </if>
      <if test="promotionType != null">
        #{promotionType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.ClubMembersExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from club_members
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update club_members
    <set>
      <if test="record.clubId != null">
        club_id = #{record.clubId,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.inviteId != null">
        invite_id = #{record.inviteId,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.currentCredit != null">
        current_credit = #{record.currentCredit,jdbcType=INTEGER},
      </if>
      <if test="record.initialCredit != null">
        initial_credit = #{record.initialCredit,jdbcType=INTEGER},
      </if>
      <if test="record.creditStatus != null">
        credit_status = #{record.creditStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.clearRequestStatus != null">
        clear_request_status = #{record.clearRequestStatus,jdbcType=INTEGER},
      </if>
      <if test="record.ratio != null">
        ratio = #{record.ratio,jdbcType=INTEGER},
      </if>
      <if test="record.ratiostatus != null">
        ratioStatus = #{record.ratiostatus,jdbcType=INTEGER},
      </if>
      <if test="record.pushStatus != null">
        push_status = #{record.pushStatus,jdbcType=INTEGER},
      </if>
      <if test="record.promotionType != null">
        promotion_type = #{record.promotionType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update club_members
    set club_id = #{record.clubId,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=VARCHAR},
      invite_id = #{record.inviteId,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=INTEGER},
      current_credit = #{record.currentCredit,jdbcType=INTEGER},
      initial_credit = #{record.initialCredit,jdbcType=INTEGER},
      credit_status = #{record.creditStatus,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      clear_request_status = #{record.clearRequestStatus,jdbcType=INTEGER},
      ratio = #{record.ratio,jdbcType=INTEGER},
      ratioStatus = #{record.ratiostatus,jdbcType=INTEGER},
      push_status = #{record.pushStatus,jdbcType=INTEGER},
      promotion_type = #{record.promotionType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.allinpokers.yunying.entity.crazypoker.ClubMembers">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update club_members
    <set>
      <if test="inviteId != null">
        invite_id = #{inviteId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="currentCredit != null">
        current_credit = #{currentCredit,jdbcType=INTEGER},
      </if>
      <if test="initialCredit != null">
        initial_credit = #{initialCredit,jdbcType=INTEGER},
      </if>
      <if test="creditStatus != null">
        credit_status = #{creditStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="clearRequestStatus != null">
        clear_request_status = #{clearRequestStatus,jdbcType=INTEGER},
      </if>
      <if test="ratio != null">
        ratio = #{ratio,jdbcType=INTEGER},
      </if>
      <if test="ratiostatus != null">
        ratioStatus = #{ratiostatus,jdbcType=INTEGER},
      </if>
      <if test="pushStatus != null">
        push_status = #{pushStatus,jdbcType=INTEGER},
      </if>
      <if test="promotionType != null">
        promotion_type = #{promotionType,jdbcType=INTEGER},
      </if>
    </set>
    where club_id = #{clubId,jdbcType=INTEGER}
      and user_id = #{userId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.allinpokers.yunying.entity.crazypoker.ClubMembers">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update club_members
    set invite_id = #{inviteId,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      current_credit = #{currentCredit,jdbcType=INTEGER},
      initial_credit = #{initialCredit,jdbcType=INTEGER},
      credit_status = #{creditStatus,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      clear_request_status = #{clearRequestStatus,jdbcType=INTEGER},
      ratio = #{ratio,jdbcType=INTEGER},
      ratioStatus = #{ratiostatus,jdbcType=INTEGER},
      push_status = #{pushStatus,jdbcType=INTEGER},
      promotion_type = #{promotionType,jdbcType=INTEGER}
    where club_id = #{clubId,jdbcType=INTEGER}
      and user_id = #{userId,jdbcType=VARCHAR}
  </update>
</mapper>