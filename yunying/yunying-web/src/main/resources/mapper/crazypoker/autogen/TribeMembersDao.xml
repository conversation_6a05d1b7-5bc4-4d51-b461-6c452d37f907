<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.TribeMembersDao">
  <resultMap id="BaseResultMap" type="com.allinpokers.yunying.entity.crazypoker.TribeMembers">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="club_id" jdbcType="INTEGER" property="clubId" />
    <id column="tribe_id" jdbcType="INTEGER" property="tribeId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="club_credit" jdbcType="INTEGER" property="clubCredit" />
    <result column="init_credit" jdbcType="INTEGER" property="initCredit" />
    <result column="pl" jdbcType="INTEGER" property="pl" />
    <result column="pause" jdbcType="SMALLINT" property="pause" />
    <result column="status" jdbcType="SMALLINT" property="status" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    club_id, tribe_id, type, create_time, club_credit, init_credit, pl, pause, status
  </sql>
  <select id="selectByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.TribeMembersExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tribe_members
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="com.allinpokers.yunying.entity.crazypoker.key.TribeMembersKey" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tribe_members
    where club_id = #{clubId,jdbcType=INTEGER}
      and tribe_id = #{tribeId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.allinpokers.yunying.entity.crazypoker.key.TribeMembersKey">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tribe_members
    where club_id = #{clubId,jdbcType=INTEGER}
      and tribe_id = #{tribeId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.TribeMembersExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tribe_members
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.allinpokers.yunying.entity.crazypoker.TribeMembers">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into tribe_members (club_id, tribe_id, type, 
      create_time, club_credit, init_credit, 
      pl, pause, status
      )
    values (#{clubId,jdbcType=INTEGER}, #{tribeId,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{clubCredit,jdbcType=INTEGER}, #{initCredit,jdbcType=INTEGER}, 
      #{pl,jdbcType=INTEGER}, #{pause,jdbcType=SMALLINT}, #{status,jdbcType=SMALLINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.allinpokers.yunying.entity.crazypoker.TribeMembers">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into tribe_members
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="clubId != null">
        club_id,
      </if>
      <if test="tribeId != null">
        tribe_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="clubCredit != null">
        club_credit,
      </if>
      <if test="initCredit != null">
        init_credit,
      </if>
      <if test="pl != null">
        pl,
      </if>
      <if test="pause != null">
        pause,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="clubId != null">
        #{clubId,jdbcType=INTEGER},
      </if>
      <if test="tribeId != null">
        #{tribeId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="clubCredit != null">
        #{clubCredit,jdbcType=INTEGER},
      </if>
      <if test="initCredit != null">
        #{initCredit,jdbcType=INTEGER},
      </if>
      <if test="pl != null">
        #{pl,jdbcType=INTEGER},
      </if>
      <if test="pause != null">
        #{pause,jdbcType=SMALLINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.TribeMembersExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from tribe_members
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tribe_members
    <set>
      <if test="record.clubId != null">
        club_id = #{record.clubId,jdbcType=INTEGER},
      </if>
      <if test="record.tribeId != null">
        tribe_id = #{record.tribeId,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.clubCredit != null">
        club_credit = #{record.clubCredit,jdbcType=INTEGER},
      </if>
      <if test="record.initCredit != null">
        init_credit = #{record.initCredit,jdbcType=INTEGER},
      </if>
      <if test="record.pl != null">
        pl = #{record.pl,jdbcType=INTEGER},
      </if>
      <if test="record.pause != null">
        pause = #{record.pause,jdbcType=SMALLINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=SMALLINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tribe_members
    set club_id = #{record.clubId,jdbcType=INTEGER},
      tribe_id = #{record.tribeId,jdbcType=INTEGER},
      type = #{record.type,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      club_credit = #{record.clubCredit,jdbcType=INTEGER},
      init_credit = #{record.initCredit,jdbcType=INTEGER},
      pl = #{record.pl,jdbcType=INTEGER},
      pause = #{record.pause,jdbcType=SMALLINT},
      status = #{record.status,jdbcType=SMALLINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.allinpokers.yunying.entity.crazypoker.TribeMembers">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tribe_members
    <set>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="clubCredit != null">
        club_credit = #{clubCredit,jdbcType=INTEGER},
      </if>
      <if test="initCredit != null">
        init_credit = #{initCredit,jdbcType=INTEGER},
      </if>
      <if test="pl != null">
        pl = #{pl,jdbcType=INTEGER},
      </if>
      <if test="pause != null">
        pause = #{pause,jdbcType=SMALLINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=SMALLINT},
      </if>
    </set>
    where club_id = #{clubId,jdbcType=INTEGER}
      and tribe_id = #{tribeId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.allinpokers.yunying.entity.crazypoker.TribeMembers">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tribe_members
    set type = #{type,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      club_credit = #{clubCredit,jdbcType=INTEGER},
      init_credit = #{initCredit,jdbcType=INTEGER},
      pl = #{pl,jdbcType=INTEGER},
      pause = #{pause,jdbcType=SMALLINT},
      status = #{status,jdbcType=SMALLINT}
    where club_id = #{clubId,jdbcType=INTEGER}
      and tribe_id = #{tribeId,jdbcType=INTEGER}
  </update>
</mapper>