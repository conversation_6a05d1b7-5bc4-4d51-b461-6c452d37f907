<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.ClubRecordDao">
  <resultMap id="BaseResultMap" type="com.allinpokers.yunying.entity.crazypoker.ClubRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="random_id" jdbcType="INTEGER" property="randomId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="header" jdbcType="VARCHAR" property="header" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="club_status" jdbcType="SMALLINT" property="clubStatus" />
    <result column="club_members" jdbcType="INTEGER" property="clubMembers" />
    <result column="upper_limit" jdbcType="INTEGER" property="upperLimit" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="only_chief" jdbcType="INTEGER" property="onlyChief" />
    <result column="area_id" jdbcType="VARCHAR" property="areaId" />
    <result column="fund" jdbcType="INTEGER" property="fund" />
    <result column="initial_credit" jdbcType="INTEGER" property="initialCredit" />
    <result column="credit_status" jdbcType="INTEGER" property="creditStatus" />
    <result column="total_insure" jdbcType="INTEGER" property="totalInsure" />
    <result column="tribe_status" jdbcType="INTEGER" property="tribeStatus" />
    <result column="tribe_count" jdbcType="INTEGER" property="tribeCount" />
    <result column="ratio" jdbcType="INTEGER" property="ratio" />
    <result column="ratio_time" jdbcType="TIMESTAMP" property="ratioTime" />
    <result column="officail_club" jdbcType="INTEGER" property="officailClub" />
    <result column="frozen" jdbcType="SMALLINT" property="frozen" />
    <result column="profit" jdbcType="INTEGER" property="profit" />
    <result column="trans_type" jdbcType="INTEGER" property="transType" />
    <result column="mod_trans_type_time" jdbcType="BIGINT" property="modTransTypeTime" />
    <result column="pay_channel_flag" jdbcType="TINYINT" property="payChannelFlag" />
    <result column="open_rebate" jdbcType="TINYINT" property="openRebate" />
    <result column="rebate_ratio" jdbcType="DOUBLE" property="rebateRatio" />
    <result column="rebate_sum" jdbcType="BIGINT" property="rebateSum" />
    <result column="room_profit" jdbcType="DECIMAL" property="roomProfit" />
    <result column="club_room_charge" jdbcType="INTEGER" property="clubRoomCharge" />
    <result column="club_room_insure_total" jdbcType="INTEGER" property="clubRoomInsureTotal" />
    <result column="tribe_profit" jdbcType="INTEGER" property="tribeProfit" />
    <result column="chip" jdbcType="INTEGER" property="chip" />
    <result column="recharge_fee_rate" jdbcType="INTEGER" property="rechargeFeeRate" />
    <result column="insurance_loss_limit" jdbcType="INTEGER" property="insuranceLossLimit" />
    <result column="use_custom" jdbcType="INTEGER" property="useCustom" />
    <result column="custom_url" jdbcType="VARCHAR" property="customUrl" />
    <result column="total_amount" jdbcType="INTEGER" property="totalAmount" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, random_id, name, header, description, creator, club_status, club_members, upper_limit, 
    create_time, only_chief, area_id, fund, initial_credit, credit_status, total_insure, 
    tribe_status, tribe_count, ratio, ratio_time, officail_club, frozen, profit, trans_type, 
    mod_trans_type_time, pay_channel_flag, open_rebate, rebate_ratio, rebate_sum, room_profit, 
    club_room_charge, club_room_insure_total, tribe_profit, chip, recharge_fee_rate, 
    insurance_loss_limit, use_custom, custom_url, total_amount
  </sql>
  <select id="selectByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.ClubRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from club_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from club_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from club_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.ClubRecordExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from club_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.allinpokers.yunying.entity.crazypoker.ClubRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into club_record (random_id, name, header, 
      description, creator, club_status, 
      club_members, upper_limit, create_time, 
      only_chief, area_id, fund, 
      initial_credit, credit_status, total_insure, 
      tribe_status, tribe_count, ratio, 
      ratio_time, officail_club, frozen, 
      profit, trans_type, mod_trans_type_time, 
      pay_channel_flag, open_rebate, rebate_ratio, 
      rebate_sum, room_profit, club_room_charge, 
      club_room_insure_total, tribe_profit, chip, 
      recharge_fee_rate, insurance_loss_limit, use_custom, custom_url)
    values (#{randomId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{header,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{clubStatus,jdbcType=SMALLINT}, 
      #{clubMembers,jdbcType=INTEGER}, #{upperLimit,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{onlyChief,jdbcType=INTEGER}, #{areaId,jdbcType=VARCHAR}, #{fund,jdbcType=INTEGER}, 
      #{initialCredit,jdbcType=INTEGER}, #{creditStatus,jdbcType=INTEGER}, #{totalInsure,jdbcType=INTEGER}, 
      #{tribeStatus,jdbcType=INTEGER}, #{tribeCount,jdbcType=INTEGER}, #{ratio,jdbcType=INTEGER}, 
      #{ratioTime,jdbcType=TIMESTAMP}, #{officailClub,jdbcType=INTEGER}, #{frozen,jdbcType=SMALLINT}, 
      #{profit,jdbcType=INTEGER}, #{transType,jdbcType=INTEGER}, #{modTransTypeTime,jdbcType=BIGINT}, 
      #{payChannelFlag,jdbcType=TINYINT}, #{openRebate,jdbcType=TINYINT}, #{rebateRatio,jdbcType=DOUBLE}, 
      #{rebateSum,jdbcType=BIGINT}, #{roomProfit,jdbcType=DECIMAL}, #{clubRoomCharge,jdbcType=INTEGER}, 
      #{clubRoomInsureTotal,jdbcType=INTEGER}, #{tribeProfit,jdbcType=INTEGER}, #{chip,jdbcType=INTEGER}, 
      #{rechargeFeeRate,jdbcType=INTEGER}, #{insuranceLossLimit,jdbcType=INTEGER}, #{useCustom,jdbcType=INTEGER}, #{customUrl,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.allinpokers.yunying.entity.crazypoker.ClubRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into club_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="randomId != null">
        random_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="header != null">
        header,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="clubStatus != null">
        club_status,
      </if>
      <if test="clubMembers != null">
        club_members,
      </if>
      <if test="upperLimit != null">
        upper_limit,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="onlyChief != null">
        only_chief,
      </if>
      <if test="areaId != null">
        area_id,
      </if>
      <if test="fund != null">
        fund,
      </if>
      <if test="initialCredit != null">
        initial_credit,
      </if>
      <if test="creditStatus != null">
        credit_status,
      </if>
      <if test="totalInsure != null">
        total_insure,
      </if>
      <if test="tribeStatus != null">
        tribe_status,
      </if>
      <if test="tribeCount != null">
        tribe_count,
      </if>
      <if test="ratio != null">
        ratio,
      </if>
      <if test="ratioTime != null">
        ratio_time,
      </if>
      <if test="officailClub != null">
        officail_club,
      </if>
      <if test="frozen != null">
        frozen,
      </if>
      <if test="profit != null">
        profit,
      </if>
      <if test="transType != null">
        trans_type,
      </if>
      <if test="modTransTypeTime != null">
        mod_trans_type_time,
      </if>
      <if test="payChannelFlag != null">
        pay_channel_flag,
      </if>
      <if test="openRebate != null">
        open_rebate,
      </if>
      <if test="rebateRatio != null">
        rebate_ratio,
      </if>
      <if test="rebateSum != null">
        rebate_sum,
      </if>
      <if test="roomProfit != null">
        room_profit,
      </if>
      <if test="clubRoomCharge != null">
        club_room_charge,
      </if>
      <if test="clubRoomInsureTotal != null">
        club_room_insure_total,
      </if>
      <if test="tribeProfit != null">
        tribe_profit,
      </if>
      <if test="chip != null">
        chip,
      </if>
      <if test="rechargeFeeRate != null">
        recharge_fee_rate,
      </if>
      <if test="insuranceLossLimit != null">
        insurance_loss_limit,
      </if>
      <if test="useCustom != null">
        use_custom,
      </if>
      <if test="customUrl != null">
        custom_url,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="randomId != null">
        #{randomId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="header != null">
        #{header,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="clubStatus != null">
        #{clubStatus,jdbcType=SMALLINT},
      </if>
      <if test="clubMembers != null">
        #{clubMembers,jdbcType=INTEGER},
      </if>
      <if test="upperLimit != null">
        #{upperLimit,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="onlyChief != null">
        #{onlyChief,jdbcType=INTEGER},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=VARCHAR},
      </if>
      <if test="fund != null">
        #{fund,jdbcType=INTEGER},
      </if>
      <if test="initialCredit != null">
        #{initialCredit,jdbcType=INTEGER},
      </if>
      <if test="creditStatus != null">
        #{creditStatus,jdbcType=INTEGER},
      </if>
      <if test="totalInsure != null">
        #{totalInsure,jdbcType=INTEGER},
      </if>
      <if test="tribeStatus != null">
        #{tribeStatus,jdbcType=INTEGER},
      </if>
      <if test="tribeCount != null">
        #{tribeCount,jdbcType=INTEGER},
      </if>
      <if test="ratio != null">
        #{ratio,jdbcType=INTEGER},
      </if>
      <if test="ratioTime != null">
        #{ratioTime,jdbcType=TIMESTAMP},
      </if>
      <if test="officailClub != null">
        #{officailClub,jdbcType=INTEGER},
      </if>
      <if test="frozen != null">
        #{frozen,jdbcType=SMALLINT},
      </if>
      <if test="profit != null">
        #{profit,jdbcType=INTEGER},
      </if>
      <if test="transType != null">
        #{transType,jdbcType=INTEGER},
      </if>
      <if test="modTransTypeTime != null">
        #{modTransTypeTime,jdbcType=BIGINT},
      </if>
      <if test="payChannelFlag != null">
        #{payChannelFlag,jdbcType=TINYINT},
      </if>
      <if test="openRebate != null">
        #{openRebate,jdbcType=TINYINT},
      </if>
      <if test="rebateRatio != null">
        #{rebateRatio,jdbcType=DOUBLE},
      </if>
      <if test="rebateSum != null">
        #{rebateSum,jdbcType=BIGINT},
      </if>
      <if test="roomProfit != null">
        #{roomProfit,jdbcType=DECIMAL},
      </if>
      <if test="clubRoomCharge != null">
        #{clubRoomCharge,jdbcType=INTEGER},
      </if>
      <if test="clubRoomInsureTotal != null">
        #{clubRoomInsureTotal,jdbcType=INTEGER},
      </if>
      <if test="tribeProfit != null">
        #{tribeProfit,jdbcType=INTEGER},
      </if>
      <if test="chip != null">
        #{chip,jdbcType=INTEGER},
      </if>
      <if test="rechargeFeeRate != null">
        #{rechargeFeeRate,jdbcType=INTEGER},
      </if>
      <if test="insuranceLossLimit != null">
        #{insuranceLossLimit,jdbcType=INTEGER},
      </if>
      <if test="useCustom != null">
        #{useCustom,jdbcType=INTEGER},
      </if>
      <if test="customUrl != null">
        #{customUrl,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.ClubRecordExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from club_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update club_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.randomId != null">
        random_id = #{record.randomId,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.header != null">
        header = #{record.header,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.clubStatus != null">
        club_status = #{record.clubStatus,jdbcType=SMALLINT},
      </if>
      <if test="record.clubMembers != null">
        club_members = #{record.clubMembers,jdbcType=INTEGER},
      </if>
      <if test="record.upperLimit != null">
        upper_limit = #{record.upperLimit,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.onlyChief != null">
        only_chief = #{record.onlyChief,jdbcType=INTEGER},
      </if>
      <if test="record.areaId != null">
        area_id = #{record.areaId,jdbcType=VARCHAR},
      </if>
      <if test="record.fund != null">
        fund = #{record.fund,jdbcType=INTEGER},
      </if>
      <if test="record.initialCredit != null">
        initial_credit = #{record.initialCredit,jdbcType=INTEGER},
      </if>
      <if test="record.creditStatus != null">
        credit_status = #{record.creditStatus,jdbcType=INTEGER},
      </if>
      <if test="record.totalInsure != null">
        total_insure = #{record.totalInsure,jdbcType=INTEGER},
      </if>
      <if test="record.tribeStatus != null">
        tribe_status = #{record.tribeStatus,jdbcType=INTEGER},
      </if>
      <if test="record.tribeCount != null">
        tribe_count = #{record.tribeCount,jdbcType=INTEGER},
      </if>
      <if test="record.ratio != null">
        ratio = #{record.ratio,jdbcType=INTEGER},
      </if>
      <if test="record.ratioTime != null">
        ratio_time = #{record.ratioTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.officailClub != null">
        officail_club = #{record.officailClub,jdbcType=INTEGER},
      </if>
      <if test="record.frozen != null">
        frozen = #{record.frozen,jdbcType=SMALLINT},
      </if>
      <if test="record.profit != null">
        profit = #{record.profit,jdbcType=INTEGER},
      </if>
      <if test="record.transType != null">
        trans_type = #{record.transType,jdbcType=INTEGER},
      </if>
      <if test="record.modTransTypeTime != null">
        mod_trans_type_time = #{record.modTransTypeTime,jdbcType=BIGINT},
      </if>
      <if test="record.payChannelFlag != null">
        pay_channel_flag = #{record.payChannelFlag,jdbcType=TINYINT},
      </if>
      <if test="record.openRebate != null">
        open_rebate = #{record.openRebate,jdbcType=TINYINT},
      </if>
      <if test="record.rebateRatio != null">
        rebate_ratio = #{record.rebateRatio,jdbcType=DOUBLE},
      </if>
      <if test="record.rebateSum != null">
        rebate_sum = #{record.rebateSum,jdbcType=BIGINT},
      </if>
      <if test="record.roomProfit != null">
        room_profit = #{record.roomProfit,jdbcType=DECIMAL},
      </if>
      <if test="record.clubRoomCharge != null">
        club_room_charge = #{record.clubRoomCharge,jdbcType=INTEGER},
      </if>
      <if test="record.clubRoomInsureTotal != null">
        club_room_insure_total = #{record.clubRoomInsureTotal,jdbcType=INTEGER},
      </if>
      <if test="record.tribeProfit != null">
        tribe_profit = #{record.tribeProfit,jdbcType=INTEGER},
      </if>
      <if test="record.chip != null">
        chip = #{record.chip,jdbcType=INTEGER},
      </if>
      <if test="record.rechargeFeeRate != null">
        recharge_fee_rate = #{record.rechargeFeeRate,jdbcType=INTEGER},
      </if>
      <if test="record.insuranceLossLimit != null">
        insurance_loss_limit = #{record.insuranceLossLimit,jdbcType=INTEGER},
      </if>
      <if test="record.useCustom != null">
        use_custom = #{record.useCustom,jdbcType=INTEGER},
      </if>
      <if test="record.customUrl != null">
        custom_url = #{record.customUrl,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update club_record
    set id = #{record.id,jdbcType=INTEGER},
      random_id = #{record.randomId,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      header = #{record.header,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      club_status = #{record.clubStatus,jdbcType=SMALLINT},
      club_members = #{record.clubMembers,jdbcType=INTEGER},
      upper_limit = #{record.upperLimit,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      only_chief = #{record.onlyChief,jdbcType=INTEGER},
      area_id = #{record.areaId,jdbcType=VARCHAR},
      fund = #{record.fund,jdbcType=INTEGER},
      initial_credit = #{record.initialCredit,jdbcType=INTEGER},
      credit_status = #{record.creditStatus,jdbcType=INTEGER},
      total_insure = #{record.totalInsure,jdbcType=INTEGER},
      tribe_status = #{record.tribeStatus,jdbcType=INTEGER},
      tribe_count = #{record.tribeCount,jdbcType=INTEGER},
      ratio = #{record.ratio,jdbcType=INTEGER},
      ratio_time = #{record.ratioTime,jdbcType=TIMESTAMP},
      officail_club = #{record.officailClub,jdbcType=INTEGER},
      frozen = #{record.frozen,jdbcType=SMALLINT},
      profit = #{record.profit,jdbcType=INTEGER},
      trans_type = #{record.transType,jdbcType=INTEGER},
      mod_trans_type_time = #{record.modTransTypeTime,jdbcType=BIGINT},
      pay_channel_flag = #{record.payChannelFlag,jdbcType=TINYINT},
      open_rebate = #{record.openRebate,jdbcType=TINYINT},
      rebate_ratio = #{record.rebateRatio,jdbcType=DOUBLE},
      rebate_sum = #{record.rebateSum,jdbcType=BIGINT},
      room_profit = #{record.roomProfit,jdbcType=DECIMAL},
      club_room_charge = #{record.clubRoomCharge,jdbcType=INTEGER},
      club_room_insure_total = #{record.clubRoomInsureTotal,jdbcType=INTEGER},
      tribe_profit = #{record.tribeProfit,jdbcType=INTEGER},
      chip = #{record.chip,jdbcType=INTEGER},
      recharge_fee_rate = #{record.rechargeFeeRate,jdbcType=INTEGER},
      insurance_loss_limit = #{record.insuranceLossLimit,jdbcType=INTEGER},
      use_custom = #{record.useCustom,jdbcType=INTEGER},
      custom_url = #{record.customUrl,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.allinpokers.yunying.entity.crazypoker.ClubRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update club_record
    <set>
      <if test="randomId != null">
        random_id = #{randomId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="header != null">
        header = #{header,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="clubStatus != null">
        club_status = #{clubStatus,jdbcType=SMALLINT},
      </if>
      <if test="clubMembers != null">
        club_members = #{clubMembers,jdbcType=INTEGER},
      </if>
      <if test="upperLimit != null">
        upper_limit = #{upperLimit,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="onlyChief != null">
        only_chief = #{onlyChief,jdbcType=INTEGER},
      </if>
      <if test="areaId != null">
        area_id = #{areaId,jdbcType=VARCHAR},
      </if>
      <if test="fund != null">
        fund = #{fund,jdbcType=INTEGER},
      </if>
      <if test="initialCredit != null">
        initial_credit = #{initialCredit,jdbcType=INTEGER},
      </if>
      <if test="creditStatus != null">
        credit_status = #{creditStatus,jdbcType=INTEGER},
      </if>
      <if test="totalInsure != null">
        total_insure = #{totalInsure,jdbcType=INTEGER},
      </if>
      <if test="tribeStatus != null">
        tribe_status = #{tribeStatus,jdbcType=INTEGER},
      </if>
      <if test="tribeCount != null">
        tribe_count = #{tribeCount,jdbcType=INTEGER},
      </if>
      <if test="ratio != null">
        ratio = #{ratio,jdbcType=INTEGER},
      </if>
      <if test="ratioTime != null">
        ratio_time = #{ratioTime,jdbcType=TIMESTAMP},
      </if>
      <if test="officailClub != null">
        officail_club = #{officailClub,jdbcType=INTEGER},
      </if>
      <if test="frozen != null">
        frozen = #{frozen,jdbcType=SMALLINT},
      </if>
      <if test="profit != null">
        profit = #{profit,jdbcType=INTEGER},
      </if>
      <if test="transType != null">
        trans_type = #{transType,jdbcType=INTEGER},
      </if>
      <if test="modTransTypeTime != null">
        mod_trans_type_time = #{modTransTypeTime,jdbcType=BIGINT},
      </if>
      <if test="payChannelFlag != null">
        pay_channel_flag = #{payChannelFlag,jdbcType=TINYINT},
      </if>
      <if test="openRebate != null">
        open_rebate = #{openRebate,jdbcType=TINYINT},
      </if>
      <if test="rebateRatio != null">
        rebate_ratio = #{rebateRatio,jdbcType=DOUBLE},
      </if>
      <if test="rebateSum != null">
        rebate_sum = #{rebateSum,jdbcType=BIGINT},
      </if>
      <if test="roomProfit != null">
        room_profit = #{roomProfit,jdbcType=DECIMAL},
      </if>
      <if test="clubRoomCharge != null">
        club_room_charge = #{clubRoomCharge,jdbcType=INTEGER},
      </if>
      <if test="clubRoomInsureTotal != null">
        club_room_insure_total = #{clubRoomInsureTotal,jdbcType=INTEGER},
      </if>
      <if test="tribeProfit != null">
        tribe_profit = #{tribeProfit,jdbcType=INTEGER},
      </if>
      <if test="chip != null">
        chip = #{chip,jdbcType=INTEGER},
      </if>
      <if test="rechargeFeeRate != null">
        recharge_fee_rate = #{rechargeFeeRate,jdbcType=INTEGER},
      </if>
      <if test="insuranceLossLimit != null">
        insurance_loss_limit = #{insuranceLossLimit,jdbcType=INTEGER},
      </if>
      <if test="useCustom != null">
        use_custom = #{useCustom,jdbcType=INTEGER},
      </if>
      <if test="customUrl != null">
        custom_url = #{customUrl,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.allinpokers.yunying.entity.crazypoker.ClubRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update club_record
    set random_id = #{randomId,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      header = #{header,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      club_status = #{clubStatus,jdbcType=SMALLINT},
      club_members = #{clubMembers,jdbcType=INTEGER},
      upper_limit = #{upperLimit,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      only_chief = #{onlyChief,jdbcType=INTEGER},
      area_id = #{areaId,jdbcType=VARCHAR},
      fund = #{fund,jdbcType=INTEGER},
      initial_credit = #{initialCredit,jdbcType=INTEGER},
      credit_status = #{creditStatus,jdbcType=INTEGER},
      total_insure = #{totalInsure,jdbcType=INTEGER},
      tribe_status = #{tribeStatus,jdbcType=INTEGER},
      tribe_count = #{tribeCount,jdbcType=INTEGER},
      ratio = #{ratio,jdbcType=INTEGER},
      ratio_time = #{ratioTime,jdbcType=TIMESTAMP},
      officail_club = #{officailClub,jdbcType=INTEGER},
      frozen = #{frozen,jdbcType=SMALLINT},
      profit = #{profit,jdbcType=INTEGER},
      trans_type = #{transType,jdbcType=INTEGER},
      mod_trans_type_time = #{modTransTypeTime,jdbcType=BIGINT},
      pay_channel_flag = #{payChannelFlag,jdbcType=TINYINT},
      open_rebate = #{openRebate,jdbcType=TINYINT},
      rebate_ratio = #{rebateRatio,jdbcType=DOUBLE},
      rebate_sum = #{rebateSum,jdbcType=BIGINT},
      room_profit = #{roomProfit,jdbcType=DECIMAL},
      club_room_charge = #{clubRoomCharge,jdbcType=INTEGER},
      club_room_insure_total = #{clubRoomInsureTotal,jdbcType=INTEGER},
      tribe_profit = #{tribeProfit,jdbcType=INTEGER},
      chip = #{chip,jdbcType=INTEGER},
      recharge_fee_rate = #{rechargeFeeRate,jdbcType=INTEGER},
      insurance_loss_limit = #{insuranceLossLimit,jdbcType=INTEGER},
      use_custom = #{useCustom,jdbcType=INTEGER},
      custom_url = #{customUrl,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>