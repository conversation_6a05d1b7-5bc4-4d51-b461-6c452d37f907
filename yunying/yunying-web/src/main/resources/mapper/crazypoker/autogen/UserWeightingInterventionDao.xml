<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.UserWeightingInterventionDao">
  <resultMap id="BaseResultMap" type="com.allinpokers.yunying.entity.crazypoker.UserWeightingIntervention">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="room_id" jdbcType="INTEGER" property="roomId" />
    <result column="room_name" jdbcType="VARCHAR" property="roomName" />
    <result column="room_path" jdbcType="INTEGER" property="roomPath" />
    <result column="room_no" jdbcType="INTEGER" property="roomNo" />
    <result column="room_hand_no" jdbcType="INTEGER" property="roomHandNo" />
    <result column="user_hand_no" jdbcType="INTEGER" property="userHandNo" />
    <result column="weighting_type" jdbcType="INTEGER" property="weightingType" />
    <result column="intervention_type" jdbcType="INTEGER" property="interventionType" />
    <result column="intervention_num" jdbcType="INTEGER" property="interventionNum" />
    <result column="intervention_time" jdbcType="TIMESTAMP" property="interventionTime" />
    <result column="intervention_pocers" jdbcType="VARCHAR" property="interventionPocers" />
    <result column="hand_pl" jdbcType="DECIMAL" property="handPl" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, user_id, room_id, room_name, room_path, room_no, room_hand_no, user_hand_no, 
    weighting_type, intervention_type, intervention_num, intervention_time, intervention_pocers, 
    hand_pl, created_time
  </sql>
  <select id="selectByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.UserWeightingInterventionExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user_weighting_intervention
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from user_weighting_intervention
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from user_weighting_intervention
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.UserWeightingInterventionExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from user_weighting_intervention
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.allinpokers.yunying.entity.crazypoker.UserWeightingIntervention">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into user_weighting_intervention (id, user_id, room_id, 
      room_name, room_path, room_no, 
      room_hand_no, user_hand_no, weighting_type, 
      intervention_type, intervention_num, intervention_time, 
      intervention_pocers, hand_pl, created_time
      )
    values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=INTEGER}, #{roomId,jdbcType=INTEGER}, 
      #{roomName,jdbcType=VARCHAR}, #{roomPath,jdbcType=INTEGER}, #{roomNo,jdbcType=INTEGER}, 
      #{roomHandNo,jdbcType=INTEGER}, #{userHandNo,jdbcType=INTEGER}, #{weightingType,jdbcType=INTEGER}, 
      #{interventionType,jdbcType=INTEGER}, #{interventionNum,jdbcType=INTEGER}, #{interventionTime,jdbcType=TIMESTAMP}, 
      #{interventionPocers,jdbcType=VARCHAR}, #{handPl,jdbcType=DECIMAL}, #{createdTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.allinpokers.yunying.entity.crazypoker.UserWeightingIntervention">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into user_weighting_intervention
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="roomId != null">
        room_id,
      </if>
      <if test="roomName != null">
        room_name,
      </if>
      <if test="roomPath != null">
        room_path,
      </if>
      <if test="roomNo != null">
        room_no,
      </if>
      <if test="roomHandNo != null">
        room_hand_no,
      </if>
      <if test="userHandNo != null">
        user_hand_no,
      </if>
      <if test="weightingType != null">
        weighting_type,
      </if>
      <if test="interventionType != null">
        intervention_type,
      </if>
      <if test="interventionNum != null">
        intervention_num,
      </if>
      <if test="interventionTime != null">
        intervention_time,
      </if>
      <if test="interventionPocers != null">
        intervention_pocers,
      </if>
      <if test="handPl != null">
        hand_pl,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="roomId != null">
        #{roomId,jdbcType=INTEGER},
      </if>
      <if test="roomName != null">
        #{roomName,jdbcType=VARCHAR},
      </if>
      <if test="roomPath != null">
        #{roomPath,jdbcType=INTEGER},
      </if>
      <if test="roomNo != null">
        #{roomNo,jdbcType=INTEGER},
      </if>
      <if test="roomHandNo != null">
        #{roomHandNo,jdbcType=INTEGER},
      </if>
      <if test="userHandNo != null">
        #{userHandNo,jdbcType=INTEGER},
      </if>
      <if test="weightingType != null">
        #{weightingType,jdbcType=INTEGER},
      </if>
      <if test="interventionType != null">
        #{interventionType,jdbcType=INTEGER},
      </if>
      <if test="interventionNum != null">
        #{interventionNum,jdbcType=INTEGER},
      </if>
      <if test="interventionTime != null">
        #{interventionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="interventionPocers != null">
        #{interventionPocers,jdbcType=VARCHAR},
      </if>
      <if test="handPl != null">
        #{handPl,jdbcType=DECIMAL},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.UserWeightingInterventionExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from user_weighting_intervention
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_weighting_intervention
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.roomId != null">
        room_id = #{record.roomId,jdbcType=INTEGER},
      </if>
      <if test="record.roomName != null">
        room_name = #{record.roomName,jdbcType=VARCHAR},
      </if>
      <if test="record.roomPath != null">
        room_path = #{record.roomPath,jdbcType=INTEGER},
      </if>
      <if test="record.roomNo != null">
        room_no = #{record.roomNo,jdbcType=INTEGER},
      </if>
      <if test="record.roomHandNo != null">
        room_hand_no = #{record.roomHandNo,jdbcType=INTEGER},
      </if>
      <if test="record.userHandNo != null">
        user_hand_no = #{record.userHandNo,jdbcType=INTEGER},
      </if>
      <if test="record.weightingType != null">
        weighting_type = #{record.weightingType,jdbcType=INTEGER},
      </if>
      <if test="record.interventionType != null">
        intervention_type = #{record.interventionType,jdbcType=INTEGER},
      </if>
      <if test="record.interventionNum != null">
        intervention_num = #{record.interventionNum,jdbcType=INTEGER},
      </if>
      <if test="record.interventionTime != null">
        intervention_time = #{record.interventionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.interventionPocers != null">
        intervention_pocers = #{record.interventionPocers,jdbcType=VARCHAR},
      </if>
      <if test="record.handPl != null">
        hand_pl = #{record.handPl,jdbcType=DECIMAL},
      </if>
      <if test="record.createdTime != null">
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_weighting_intervention
    set id = #{record.id,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=INTEGER},
      room_id = #{record.roomId,jdbcType=INTEGER},
      room_name = #{record.roomName,jdbcType=VARCHAR},
      room_path = #{record.roomPath,jdbcType=INTEGER},
      room_no = #{record.roomNo,jdbcType=INTEGER},
      room_hand_no = #{record.roomHandNo,jdbcType=INTEGER},
      user_hand_no = #{record.userHandNo,jdbcType=INTEGER},
      weighting_type = #{record.weightingType,jdbcType=INTEGER},
      intervention_type = #{record.interventionType,jdbcType=INTEGER},
      intervention_num = #{record.interventionNum,jdbcType=INTEGER},
      intervention_time = #{record.interventionTime,jdbcType=TIMESTAMP},
      intervention_pocers = #{record.interventionPocers,jdbcType=VARCHAR},
      hand_pl = #{record.handPl,jdbcType=DECIMAL},
      created_time = #{record.createdTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.allinpokers.yunying.entity.crazypoker.UserWeightingIntervention">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_weighting_intervention
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="roomId != null">
        room_id = #{roomId,jdbcType=INTEGER},
      </if>
      <if test="roomName != null">
        room_name = #{roomName,jdbcType=VARCHAR},
      </if>
      <if test="roomPath != null">
        room_path = #{roomPath,jdbcType=INTEGER},
      </if>
      <if test="roomNo != null">
        room_no = #{roomNo,jdbcType=INTEGER},
      </if>
      <if test="roomHandNo != null">
        room_hand_no = #{roomHandNo,jdbcType=INTEGER},
      </if>
      <if test="userHandNo != null">
        user_hand_no = #{userHandNo,jdbcType=INTEGER},
      </if>
      <if test="weightingType != null">
        weighting_type = #{weightingType,jdbcType=INTEGER},
      </if>
      <if test="interventionType != null">
        intervention_type = #{interventionType,jdbcType=INTEGER},
      </if>
      <if test="interventionNum != null">
        intervention_num = #{interventionNum,jdbcType=INTEGER},
      </if>
      <if test="interventionTime != null">
        intervention_time = #{interventionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="interventionPocers != null">
        intervention_pocers = #{interventionPocers,jdbcType=VARCHAR},
      </if>
      <if test="handPl != null">
        hand_pl = #{handPl,jdbcType=DECIMAL},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.allinpokers.yunying.entity.crazypoker.UserWeightingIntervention">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_weighting_intervention
    set user_id = #{userId,jdbcType=INTEGER},
      room_id = #{roomId,jdbcType=INTEGER},
      room_name = #{roomName,jdbcType=VARCHAR},
      room_path = #{roomPath,jdbcType=INTEGER},
      room_no = #{roomNo,jdbcType=INTEGER},
      room_hand_no = #{roomHandNo,jdbcType=INTEGER},
      user_hand_no = #{userHandNo,jdbcType=INTEGER},
      weighting_type = #{weightingType,jdbcType=INTEGER},
      intervention_type = #{interventionType,jdbcType=INTEGER},
      intervention_num = #{interventionNum,jdbcType=INTEGER},
      intervention_time = #{interventionTime,jdbcType=TIMESTAMP},
      intervention_pocers = #{interventionPocers,jdbcType=VARCHAR},
      hand_pl = #{handPl,jdbcType=DECIMAL},
      created_time = #{createdTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>