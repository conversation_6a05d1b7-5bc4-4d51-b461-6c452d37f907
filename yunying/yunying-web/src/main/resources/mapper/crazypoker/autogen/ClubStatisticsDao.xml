<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.assignment.dao.ClubStatisticsDao">
  <resultMap id="BaseResultMap" type="com.allinpokers.yunying.assignment.dao.model.ClubStatistics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="club_id" jdbcType="INTEGER" property="clubId" />
    <result column="at_num" jdbcType="INTEGER" property="atNum" />
    <result column="at_op_num" jdbcType="INTEGER" property="atOpNum" />
    <result column="mt_num" jdbcType="INTEGER" property="mtNum" />
    <result column="mt_op_num" jdbcType="INTEGER" property="mtOpNum" />
    <result column="club_show_id" jdbcType="INTEGER" property="clubShowId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    club_id, at_num, at_op_num, mt_num, mt_op_num, club_show_id
  </sql>
  <select id="selectByExample" parameterType="com.allinpokers.yunying.assignment.dao.model.example.ClubStatisticsExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from at_club_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from at_club_statistics
    where club_id = #{clubId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from at_club_statistics
    where club_id = #{clubId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.allinpokers.yunying.assignment.dao.model.example.ClubStatisticsExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from at_club_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.allinpokers.yunying.assignment.dao.model.ClubStatistics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into at_club_statistics (club_id, at_num, at_op_num, 
      mt_num, mt_op_num, club_show_id
      )
    values (#{clubId,jdbcType=INTEGER}, #{atNum,jdbcType=INTEGER}, #{atOpNum,jdbcType=INTEGER}, 
      #{mtNum,jdbcType=INTEGER}, #{mtOpNum,jdbcType=INTEGER}, #{clubShowId,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.allinpokers.yunying.assignment.dao.model.ClubStatistics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into at_club_statistics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="clubId != null">
        club_id,
      </if>
      <if test="atNum != null">
        at_num,
      </if>
      <if test="atOpNum != null">
        at_op_num,
      </if>
      <if test="mtNum != null">
        mt_num,
      </if>
      <if test="mtOpNum != null">
        mt_op_num,
      </if>
      <if test="clubShowId != null">
        club_show_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="clubId != null">
        #{clubId,jdbcType=INTEGER},
      </if>
      <if test="atNum != null">
        #{atNum,jdbcType=INTEGER},
      </if>
      <if test="atOpNum != null">
        #{atOpNum,jdbcType=INTEGER},
      </if>
      <if test="mtNum != null">
        #{mtNum,jdbcType=INTEGER},
      </if>
      <if test="mtOpNum != null">
        #{mtOpNum,jdbcType=INTEGER},
      </if>
      <if test="clubShowId != null">
        #{clubShowId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.allinpokers.yunying.assignment.dao.model.example.ClubStatisticsExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from at_club_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update at_club_statistics
    <set>
      <if test="record.clubId != null">
        club_id = #{record.clubId,jdbcType=INTEGER},
      </if>
      <if test="record.atNum != null">
        at_num = #{record.atNum,jdbcType=INTEGER},
      </if>
      <if test="record.atOpNum != null">
        at_op_num = #{record.atOpNum,jdbcType=INTEGER},
      </if>
      <if test="record.mtNum != null">
        mt_num = #{record.mtNum,jdbcType=INTEGER},
      </if>
      <if test="record.mtOpNum != null">
        mt_op_num = #{record.mtOpNum,jdbcType=INTEGER},
      </if>
      <if test="record.clubShowId != null">
        club_show_id = #{record.clubShowId,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update at_club_statistics
    set club_id = #{record.clubId,jdbcType=INTEGER},
      at_num = #{record.atNum,jdbcType=INTEGER},
      at_op_num = #{record.atOpNum,jdbcType=INTEGER},
      mt_num = #{record.mtNum,jdbcType=INTEGER},
      mt_op_num = #{record.mtOpNum,jdbcType=INTEGER},
      club_show_id = #{record.clubShowId,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.allinpokers.yunying.assignment.dao.model.ClubStatistics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update at_club_statistics
    <set>
      <if test="atNum != null">
        at_num = #{atNum,jdbcType=INTEGER},
      </if>
      <if test="atOpNum != null">
        at_op_num = #{atOpNum,jdbcType=INTEGER},
      </if>
      <if test="mtNum != null">
        mt_num = #{mtNum,jdbcType=INTEGER},
      </if>
      <if test="mtOpNum != null">
        mt_op_num = #{mtOpNum,jdbcType=INTEGER},
      </if>
      <if test="clubShowId != null">
        club_show_id = #{clubShowId,jdbcType=INTEGER},
      </if>
    </set>
    where club_id = #{clubId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.allinpokers.yunying.assignment.dao.model.ClubStatistics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update at_club_statistics
    set at_num = #{atNum,jdbcType=INTEGER},
      at_op_num = #{atOpNum,jdbcType=INTEGER},
      mt_num = #{mtNum,jdbcType=INTEGER},
      mt_op_num = #{mtOpNum,jdbcType=INTEGER},
      club_show_id = #{clubShowId,jdbcType=INTEGER}
    where club_id = #{clubId,jdbcType=INTEGER}
  </update>
</mapper>