<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.UserBalanceAuditLogDao">
  <resultMap id="BaseResultMap" type="com.allinpokers.yunying.entity.crazypoker.UserBalanceAuditLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="tx_uuid" jdbcType="VARCHAR" property="txUuid" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="source" jdbcType="TINYINT" property="source" />
    <result column="balance_type" jdbcType="TINYINT" property="balanceType" />
    <result column="balance_before" jdbcType="INTEGER" property="balanceBefore" />
    <result column="balance_change" jdbcType="INTEGER" property="balanceChange" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="room_id" jdbcType="INTEGER" property="roomId" />
    <result column="club_id" jdbcType="INTEGER" property="clubId" />
    <result column="tribe_id" jdbcType="INTEGER" property="tribeId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, tx_uuid, user_id, type, source, balance_type, balance_before, balance_change, 
    description, operator_id, room_id, club_id, tribe_id, create_time
  </sql>
  <select id="selectByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.UserBalanceAuditLogExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user_balance_audit_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from user_balance_audit_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from user_balance_audit_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.UserBalanceAuditLogExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from user_balance_audit_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.allinpokers.yunying.entity.crazypoker.UserBalanceAuditLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user_balance_audit_log (tx_uuid, user_id, type, 
      source, balance_type, balance_before, 
      balance_change, description, operator_id, 
      room_id, club_id, tribe_id, 
      create_time)
    values (#{txUuid,jdbcType=VARCHAR}, #{userId,jdbcType=INTEGER}, #{type,jdbcType=TINYINT}, 
      #{source,jdbcType=TINYINT}, #{balanceType,jdbcType=TINYINT}, #{balanceBefore,jdbcType=INTEGER}, 
      #{balanceChange,jdbcType=INTEGER}, #{description,jdbcType=VARCHAR}, #{operatorId,jdbcType=INTEGER}, 
      #{roomId,jdbcType=INTEGER}, #{clubId,jdbcType=INTEGER}, #{tribeId,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.allinpokers.yunying.entity.crazypoker.UserBalanceAuditLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user_balance_audit_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="txUuid != null">
        tx_uuid,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="balanceType != null">
        balance_type,
      </if>
      <if test="balanceBefore != null">
        balance_before,
      </if>
      <if test="balanceChange != null">
        balance_change,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="roomId != null">
        room_id,
      </if>
      <if test="clubId != null">
        club_id,
      </if>
      <if test="tribeId != null">
        tribe_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="txUuid != null">
        #{txUuid,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="balanceType != null">
        #{balanceType,jdbcType=TINYINT},
      </if>
      <if test="balanceBefore != null">
        #{balanceBefore,jdbcType=INTEGER},
      </if>
      <if test="balanceChange != null">
        #{balanceChange,jdbcType=INTEGER},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="roomId != null">
        #{roomId,jdbcType=INTEGER},
      </if>
      <if test="clubId != null">
        #{clubId,jdbcType=INTEGER},
      </if>
      <if test="tribeId != null">
        #{tribeId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.UserBalanceAuditLogExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from user_balance_audit_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_balance_audit_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.txUuid != null">
        tx_uuid = #{record.txUuid,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=TINYINT},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=TINYINT},
      </if>
      <if test="record.balanceType != null">
        balance_type = #{record.balanceType,jdbcType=TINYINT},
      </if>
      <if test="record.balanceBefore != null">
        balance_before = #{record.balanceBefore,jdbcType=INTEGER},
      </if>
      <if test="record.balanceChange != null">
        balance_change = #{record.balanceChange,jdbcType=INTEGER},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=INTEGER},
      </if>
      <if test="record.roomId != null">
        room_id = #{record.roomId,jdbcType=INTEGER},
      </if>
      <if test="record.clubId != null">
        club_id = #{record.clubId,jdbcType=INTEGER},
      </if>
      <if test="record.tribeId != null">
        tribe_id = #{record.tribeId,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_balance_audit_log
    set id = #{record.id,jdbcType=INTEGER},
      tx_uuid = #{record.txUuid,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=INTEGER},
      type = #{record.type,jdbcType=TINYINT},
      source = #{record.source,jdbcType=TINYINT},
      balance_type = #{record.balanceType,jdbcType=TINYINT},
      balance_before = #{record.balanceBefore,jdbcType=INTEGER},
      balance_change = #{record.balanceChange,jdbcType=INTEGER},
      description = #{record.description,jdbcType=VARCHAR},
      operator_id = #{record.operatorId,jdbcType=INTEGER},
      room_id = #{record.roomId,jdbcType=INTEGER},
      club_id = #{record.clubId,jdbcType=INTEGER},
      tribe_id = #{record.tribeId,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.allinpokers.yunying.entity.crazypoker.UserBalanceAuditLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_balance_audit_log
    <set>
      <if test="txUuid != null">
        tx_uuid = #{txUuid,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=TINYINT},
      </if>
      <if test="balanceType != null">
        balance_type = #{balanceType,jdbcType=TINYINT},
      </if>
      <if test="balanceBefore != null">
        balance_before = #{balanceBefore,jdbcType=INTEGER},
      </if>
      <if test="balanceChange != null">
        balance_change = #{balanceChange,jdbcType=INTEGER},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="roomId != null">
        room_id = #{roomId,jdbcType=INTEGER},
      </if>
      <if test="clubId != null">
        club_id = #{clubId,jdbcType=INTEGER},
      </if>
      <if test="tribeId != null">
        tribe_id = #{tribeId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.allinpokers.yunying.entity.crazypoker.UserBalanceAuditLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_balance_audit_log
    set tx_uuid = #{txUuid,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=INTEGER},
      type = #{type,jdbcType=TINYINT},
      source = #{source,jdbcType=TINYINT},
      balance_type = #{balanceType,jdbcType=TINYINT},
      balance_before = #{balanceBefore,jdbcType=INTEGER},
      balance_change = #{balanceChange,jdbcType=INTEGER},
      description = #{description,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=INTEGER},
      room_id = #{roomId,jdbcType=INTEGER},
      club_id = #{clubId,jdbcType=INTEGER},
      tribe_id = #{tribeId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>