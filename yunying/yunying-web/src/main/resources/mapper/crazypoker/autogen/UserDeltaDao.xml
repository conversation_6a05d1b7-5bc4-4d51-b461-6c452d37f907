<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.UserDeltaDao">
  <resultMap id="BaseResultMap" type="com.allinpokers.yunying.entity.crazypoker.UserDelta">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="game_cnt" jdbcType="INTEGER" property="gameCnt" />
    <result column="total_hand" jdbcType="INTEGER" property="totalHand" />
    <result column="pool_rate" jdbcType="INTEGER" property="poolRate" />
    <result column="pool_win_rate" jdbcType="INTEGER" property="poolWinRate" />
    <result column="allin_win_rate" jdbcType="INTEGER" property="allinWinRate" />
    <result column="af_rate" jdbcType="INTEGER" property="afRate" />
    <result column="prf_rate" jdbcType="INTEGER" property="prfRate" />
    <result column="bet3_rate" jdbcType="INTEGER" property="bet3Rate" />
    <result column="cbet_rate" jdbcType="INTEGER" property="cbetRate" />
    <result column="tanpai_rate" jdbcType="INTEGER" property="tanpaiRate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    user_id, game_cnt, total_hand, pool_rate, pool_win_rate, allin_win_rate, af_rate, 
    prf_rate, bet3_rate, cbet_rate, tanpai_rate
  </sql>
  <select id="selectByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.UserDeltaExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user_delta
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from user_delta
    where user_id = #{userId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from user_delta
    where user_id = #{userId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.UserDeltaExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from user_delta
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.allinpokers.yunying.entity.crazypoker.UserDelta">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into user_delta (user_id, game_cnt, total_hand, 
      pool_rate, pool_win_rate, allin_win_rate, 
      af_rate, prf_rate, bet3_rate, 
      cbet_rate, tanpai_rate)
    values (#{userId,jdbcType=INTEGER}, #{gameCnt,jdbcType=INTEGER}, #{totalHand,jdbcType=INTEGER}, 
      #{poolRate,jdbcType=INTEGER}, #{poolWinRate,jdbcType=INTEGER}, #{allinWinRate,jdbcType=INTEGER}, 
      #{afRate,jdbcType=INTEGER}, #{prfRate,jdbcType=INTEGER}, #{bet3Rate,jdbcType=INTEGER}, 
      #{cbetRate,jdbcType=INTEGER}, #{tanpaiRate,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.allinpokers.yunying.entity.crazypoker.UserDelta">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into user_delta
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="gameCnt != null">
        game_cnt,
      </if>
      <if test="totalHand != null">
        total_hand,
      </if>
      <if test="poolRate != null">
        pool_rate,
      </if>
      <if test="poolWinRate != null">
        pool_win_rate,
      </if>
      <if test="allinWinRate != null">
        allin_win_rate,
      </if>
      <if test="afRate != null">
        af_rate,
      </if>
      <if test="prfRate != null">
        prf_rate,
      </if>
      <if test="bet3Rate != null">
        bet3_rate,
      </if>
      <if test="cbetRate != null">
        cbet_rate,
      </if>
      <if test="tanpaiRate != null">
        tanpai_rate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="gameCnt != null">
        #{gameCnt,jdbcType=INTEGER},
      </if>
      <if test="totalHand != null">
        #{totalHand,jdbcType=INTEGER},
      </if>
      <if test="poolRate != null">
        #{poolRate,jdbcType=INTEGER},
      </if>
      <if test="poolWinRate != null">
        #{poolWinRate,jdbcType=INTEGER},
      </if>
      <if test="allinWinRate != null">
        #{allinWinRate,jdbcType=INTEGER},
      </if>
      <if test="afRate != null">
        #{afRate,jdbcType=INTEGER},
      </if>
      <if test="prfRate != null">
        #{prfRate,jdbcType=INTEGER},
      </if>
      <if test="bet3Rate != null">
        #{bet3Rate,jdbcType=INTEGER},
      </if>
      <if test="cbetRate != null">
        #{cbetRate,jdbcType=INTEGER},
      </if>
      <if test="tanpaiRate != null">
        #{tanpaiRate,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.UserDeltaExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from user_delta
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_delta
    <set>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.gameCnt != null">
        game_cnt = #{record.gameCnt,jdbcType=INTEGER},
      </if>
      <if test="record.totalHand != null">
        total_hand = #{record.totalHand,jdbcType=INTEGER},
      </if>
      <if test="record.poolRate != null">
        pool_rate = #{record.poolRate,jdbcType=INTEGER},
      </if>
      <if test="record.poolWinRate != null">
        pool_win_rate = #{record.poolWinRate,jdbcType=INTEGER},
      </if>
      <if test="record.allinWinRate != null">
        allin_win_rate = #{record.allinWinRate,jdbcType=INTEGER},
      </if>
      <if test="record.afRate != null">
        af_rate = #{record.afRate,jdbcType=INTEGER},
      </if>
      <if test="record.prfRate != null">
        prf_rate = #{record.prfRate,jdbcType=INTEGER},
      </if>
      <if test="record.bet3Rate != null">
        bet3_rate = #{record.bet3Rate,jdbcType=INTEGER},
      </if>
      <if test="record.cbetRate != null">
        cbet_rate = #{record.cbetRate,jdbcType=INTEGER},
      </if>
      <if test="record.tanpaiRate != null">
        tanpai_rate = #{record.tanpaiRate,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_delta
    set user_id = #{record.userId,jdbcType=INTEGER},
      game_cnt = #{record.gameCnt,jdbcType=INTEGER},
      total_hand = #{record.totalHand,jdbcType=INTEGER},
      pool_rate = #{record.poolRate,jdbcType=INTEGER},
      pool_win_rate = #{record.poolWinRate,jdbcType=INTEGER},
      allin_win_rate = #{record.allinWinRate,jdbcType=INTEGER},
      af_rate = #{record.afRate,jdbcType=INTEGER},
      prf_rate = #{record.prfRate,jdbcType=INTEGER},
      bet3_rate = #{record.bet3Rate,jdbcType=INTEGER},
      cbet_rate = #{record.cbetRate,jdbcType=INTEGER},
      tanpai_rate = #{record.tanpaiRate,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.allinpokers.yunying.entity.crazypoker.UserDelta">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_delta
    <set>
      <if test="gameCnt != null">
        game_cnt = #{gameCnt,jdbcType=INTEGER},
      </if>
      <if test="totalHand != null">
        total_hand = #{totalHand,jdbcType=INTEGER},
      </if>
      <if test="poolRate != null">
        pool_rate = #{poolRate,jdbcType=INTEGER},
      </if>
      <if test="poolWinRate != null">
        pool_win_rate = #{poolWinRate,jdbcType=INTEGER},
      </if>
      <if test="allinWinRate != null">
        allin_win_rate = #{allinWinRate,jdbcType=INTEGER},
      </if>
      <if test="afRate != null">
        af_rate = #{afRate,jdbcType=INTEGER},
      </if>
      <if test="prfRate != null">
        prf_rate = #{prfRate,jdbcType=INTEGER},
      </if>
      <if test="bet3Rate != null">
        bet3_rate = #{bet3Rate,jdbcType=INTEGER},
      </if>
      <if test="cbetRate != null">
        cbet_rate = #{cbetRate,jdbcType=INTEGER},
      </if>
      <if test="tanpaiRate != null">
        tanpai_rate = #{tanpaiRate,jdbcType=INTEGER},
      </if>
    </set>
    where user_id = #{userId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.allinpokers.yunying.entity.crazypoker.UserDelta">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_delta
    set game_cnt = #{gameCnt,jdbcType=INTEGER},
      total_hand = #{totalHand,jdbcType=INTEGER},
      pool_rate = #{poolRate,jdbcType=INTEGER},
      pool_win_rate = #{poolWinRate,jdbcType=INTEGER},
      allin_win_rate = #{allinWinRate,jdbcType=INTEGER},
      af_rate = #{afRate,jdbcType=INTEGER},
      prf_rate = #{prfRate,jdbcType=INTEGER},
      bet3_rate = #{bet3Rate,jdbcType=INTEGER},
      cbet_rate = #{cbetRate,jdbcType=INTEGER},
      tanpai_rate = #{tanpaiRate,jdbcType=INTEGER}
    where user_id = #{userId,jdbcType=INTEGER}
  </update>
</mapper>