<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.PayChannelDao">
  <resultMap id="BaseResultMap" type="com.allinpokers.yunying.entity.crazypoker.PayChannel">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="cms_uid" jdbcType="INTEGER" property="cmsUid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="open_rebate" jdbcType="BIT" property="openRebate" />
    <result column="rebate_ratio" jdbcType="DOUBLE" property="rebateRatio" />
    <result column="rebate_sum" jdbcType="BIGINT" property="rebateSum" />
    <result column="club_count" jdbcType="INTEGER" property="clubCount" />
    <result column="payment_code" jdbcType="VARCHAR" property="paymentCode" />
    <result column="allin_partner_id" jdbcType="INTEGER" property="allinPartnerId" />
    <result column="allin_partner_key" jdbcType="VARCHAR" property="allinPartnerKey" />
    <result column="contact_type" jdbcType="INTEGER" property="contactType" />
    <result column="contact_account" jdbcType="VARCHAR" property="contactAccount" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, cms_uid, name, type, open_rebate, rebate_ratio, rebate_sum, club_count, payment_code, 
    allin_partner_id, allin_partner_key, contact_type, contact_account, status, creator_id, 
    created_time, updater_id, updated_time
  </sql>
  <select id="selectByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.PayChannelExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pay_channel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from pay_channel
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from pay_channel
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.PayChannelExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from pay_channel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.allinpokers.yunying.entity.crazypoker.PayChannel">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pay_channel (cms_uid, name, type, 
      open_rebate, rebate_ratio, rebate_sum, 
      club_count, payment_code, allin_partner_id, 
      allin_partner_key, contact_type, contact_account, 
      status, creator_id, created_time, 
      updater_id, updated_time)
    values (#{cmsUid,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, 
      #{openRebate,jdbcType=BIT}, #{rebateRatio,jdbcType=DOUBLE}, #{rebateSum,jdbcType=BIGINT}, 
      #{clubCount,jdbcType=INTEGER}, #{paymentCode,jdbcType=VARCHAR}, #{allinPartnerId,jdbcType=INTEGER}, 
      #{allinPartnerKey,jdbcType=VARCHAR}, #{contactType,jdbcType=INTEGER}, #{contactAccount,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{creatorId,jdbcType=INTEGER}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{updaterId,jdbcType=INTEGER}, #{updatedTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.allinpokers.yunying.entity.crazypoker.PayChannel">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pay_channel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="cmsUid != null">
        cms_uid,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="openRebate != null">
        open_rebate,
      </if>
      <if test="rebateRatio != null">
        rebate_ratio,
      </if>
      <if test="rebateSum != null">
        rebate_sum,
      </if>
      <if test="clubCount != null">
        club_count,
      </if>
      <if test="paymentCode != null">
        payment_code,
      </if>
      <if test="allinPartnerId != null">
        allin_partner_id,
      </if>
      <if test="allinPartnerKey != null">
        allin_partner_key,
      </if>
      <if test="contactType != null">
        contact_type,
      </if>
      <if test="contactAccount != null">
        contact_account,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="cmsUid != null">
        #{cmsUid,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="openRebate != null">
        #{openRebate,jdbcType=BIT},
      </if>
      <if test="rebateRatio != null">
        #{rebateRatio,jdbcType=DOUBLE},
      </if>
      <if test="rebateSum != null">
        #{rebateSum,jdbcType=BIGINT},
      </if>
      <if test="clubCount != null">
        #{clubCount,jdbcType=INTEGER},
      </if>
      <if test="paymentCode != null">
        #{paymentCode,jdbcType=VARCHAR},
      </if>
      <if test="allinPartnerId != null">
        #{allinPartnerId,jdbcType=INTEGER},
      </if>
      <if test="allinPartnerKey != null">
        #{allinPartnerKey,jdbcType=VARCHAR},
      </if>
      <if test="contactType != null">
        #{contactType,jdbcType=INTEGER},
      </if>
      <if test="contactAccount != null">
        #{contactAccount,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.PayChannelExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from pay_channel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update pay_channel
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.cmsUid != null">
        cms_uid = #{record.cmsUid,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.openRebate != null">
        open_rebate = #{record.openRebate,jdbcType=BIT},
      </if>
      <if test="record.rebateRatio != null">
        rebate_ratio = #{record.rebateRatio,jdbcType=DOUBLE},
      </if>
      <if test="record.rebateSum != null">
        rebate_sum = #{record.rebateSum,jdbcType=BIGINT},
      </if>
      <if test="record.clubCount != null">
        club_count = #{record.clubCount,jdbcType=INTEGER},
      </if>
      <if test="record.paymentCode != null">
        payment_code = #{record.paymentCode,jdbcType=VARCHAR},
      </if>
      <if test="record.allinPartnerId != null">
        allin_partner_id = #{record.allinPartnerId,jdbcType=INTEGER},
      </if>
      <if test="record.allinPartnerKey != null">
        allin_partner_key = #{record.allinPartnerKey,jdbcType=VARCHAR},
      </if>
      <if test="record.contactType != null">
        contact_type = #{record.contactType,jdbcType=INTEGER},
      </if>
      <if test="record.contactAccount != null">
        contact_account = #{record.contactAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=INTEGER},
      </if>
      <if test="record.createdTime != null">
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updaterId != null">
        updater_id = #{record.updaterId,jdbcType=INTEGER},
      </if>
      <if test="record.updatedTime != null">
        updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update pay_channel
    set id = #{record.id,jdbcType=INTEGER},
      cms_uid = #{record.cmsUid,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=INTEGER},
      open_rebate = #{record.openRebate,jdbcType=BIT},
      rebate_ratio = #{record.rebateRatio,jdbcType=DOUBLE},
      rebate_sum = #{record.rebateSum,jdbcType=BIGINT},
      club_count = #{record.clubCount,jdbcType=INTEGER},
      payment_code = #{record.paymentCode,jdbcType=VARCHAR},
      allin_partner_id = #{record.allinPartnerId,jdbcType=INTEGER},
      allin_partner_key = #{record.allinPartnerKey,jdbcType=VARCHAR},
      contact_type = #{record.contactType,jdbcType=INTEGER},
      contact_account = #{record.contactAccount,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      creator_id = #{record.creatorId,jdbcType=INTEGER},
      created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      updater_id = #{record.updaterId,jdbcType=INTEGER},
      updated_time = #{record.updatedTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.allinpokers.yunying.entity.crazypoker.PayChannel">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update pay_channel
    <set>
      <if test="cmsUid != null">
        cms_uid = #{cmsUid,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="openRebate != null">
        open_rebate = #{openRebate,jdbcType=BIT},
      </if>
      <if test="rebateRatio != null">
        rebate_ratio = #{rebateRatio,jdbcType=DOUBLE},
      </if>
      <if test="rebateSum != null">
        rebate_sum = #{rebateSum,jdbcType=BIGINT},
      </if>
      <if test="clubCount != null">
        club_count = #{clubCount,jdbcType=INTEGER},
      </if>
      <if test="paymentCode != null">
        payment_code = #{paymentCode,jdbcType=VARCHAR},
      </if>
      <if test="allinPartnerId != null">
        allin_partner_id = #{allinPartnerId,jdbcType=INTEGER},
      </if>
      <if test="allinPartnerKey != null">
        allin_partner_key = #{allinPartnerKey,jdbcType=VARCHAR},
      </if>
      <if test="contactType != null">
        contact_type = #{contactType,jdbcType=INTEGER},
      </if>
      <if test="contactAccount != null">
        contact_account = #{contactAccount,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.allinpokers.yunying.entity.crazypoker.PayChannel">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update pay_channel
    set cms_uid = #{cmsUid,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      open_rebate = #{openRebate,jdbcType=BIT},
      rebate_ratio = #{rebateRatio,jdbcType=DOUBLE},
      rebate_sum = #{rebateSum,jdbcType=BIGINT},
      club_count = #{clubCount,jdbcType=INTEGER},
      payment_code = #{paymentCode,jdbcType=VARCHAR},
      allin_partner_id = #{allinPartnerId,jdbcType=INTEGER},
      allin_partner_key = #{allinPartnerKey,jdbcType=VARCHAR},
      contact_type = #{contactType,jdbcType=INTEGER},
      contact_account = #{contactAccount,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      creator_id = #{creatorId,jdbcType=INTEGER},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updater_id = #{updaterId,jdbcType=INTEGER},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>