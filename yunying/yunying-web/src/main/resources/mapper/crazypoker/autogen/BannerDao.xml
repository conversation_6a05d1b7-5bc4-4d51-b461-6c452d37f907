<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.BannerDao">
  <resultMap id="BaseResultMap" type="com.allinpokers.yunying.entity.crazypoker.Banner">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="product" jdbcType="TINYINT" property="product" />
    <result column="lang" jdbcType="VARCHAR" property="lang" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="image_url" jdbcType="VARCHAR" property="imageUrl" />
    <result column="redirect_url" jdbcType="VARCHAR" property="redirectUrl" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="created_on" jdbcType="TIMESTAMP" property="createdOn" />
    <result column="updated_on" jdbcType="TIMESTAMP" property="updatedOn" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, product, lang, status, type, image_url, redirect_url, description, created_on, 
    updated_on
  </sql>
  <select id="selectByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.BannerExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from banner
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from banner
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from banner
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.BannerExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from banner
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.allinpokers.yunying.entity.crazypoker.Banner">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into banner (product, lang, status, 
      type, image_url, redirect_url, 
      description, created_on, updated_on
      )
    values (#{product,jdbcType=TINYINT}, #{lang,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{type,jdbcType=TINYINT}, #{imageUrl,jdbcType=VARCHAR}, #{redirectUrl,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{createdOn,jdbcType=TIMESTAMP}, #{updatedOn,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.allinpokers.yunying.entity.crazypoker.Banner">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into banner
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="product != null">
        product,
      </if>
      <if test="lang != null">
        lang,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="imageUrl != null">
        image_url,
      </if>
      <if test="redirectUrl != null">
        redirect_url,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="createdOn != null">
        created_on,
      </if>
      <if test="updatedOn != null">
        updated_on,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="product != null">
        #{product,jdbcType=TINYINT},
      </if>
      <if test="lang != null">
        #{lang,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="imageUrl != null">
        #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="redirectUrl != null">
        #{redirectUrl,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="createdOn != null">
        #{createdOn,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedOn != null">
        #{updatedOn,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.BannerExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from banner
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update banner
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.product != null">
        product = #{record.product,jdbcType=TINYINT},
      </if>
      <if test="record.lang != null">
        lang = #{record.lang,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=TINYINT},
      </if>
      <if test="record.imageUrl != null">
        image_url = #{record.imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.redirectUrl != null">
        redirect_url = #{record.redirectUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.createdOn != null">
        created_on = #{record.createdOn,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedOn != null">
        updated_on = #{record.updatedOn,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update banner
    set id = #{record.id,jdbcType=INTEGER},
      product = #{record.product,jdbcType=TINYINT},
      lang = #{record.lang,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      type = #{record.type,jdbcType=TINYINT},
      image_url = #{record.imageUrl,jdbcType=VARCHAR},
      redirect_url = #{record.redirectUrl,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      created_on = #{record.createdOn,jdbcType=TIMESTAMP},
      updated_on = #{record.updatedOn,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.allinpokers.yunying.entity.crazypoker.Banner">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update banner
    <set>
      <if test="product != null">
        product = #{product,jdbcType=TINYINT},
      </if>
      <if test="lang != null">
        lang = #{lang,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="imageUrl != null">
        image_url = #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="redirectUrl != null">
        redirect_url = #{redirectUrl,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="createdOn != null">
        created_on = #{createdOn,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedOn != null">
        updated_on = #{updatedOn,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.allinpokers.yunying.entity.crazypoker.Banner">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update banner
    set product = #{product,jdbcType=TINYINT},
      lang = #{lang,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      type = #{type,jdbcType=TINYINT},
      image_url = #{imageUrl,jdbcType=VARCHAR},
      redirect_url = #{redirectUrl,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      created_on = #{createdOn,jdbcType=TIMESTAMP},
      updated_on = #{updatedOn,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>