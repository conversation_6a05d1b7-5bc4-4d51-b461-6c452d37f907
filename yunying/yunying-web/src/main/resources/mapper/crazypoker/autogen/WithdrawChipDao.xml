<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.WithdrawChipDao">
  <resultMap id="BaseResultMap" type="com.allinpokers.yunying.entity.crazypoker.WithdrawChip">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="uid" jdbcType="INTEGER" property="uid" />
    <result column="withdraw_no" jdbcType="VARCHAR" property="withdrawNo" />
    <result column="chip" jdbcType="INTEGER" property="chip" />
    <result column="amount" jdbcType="DOUBLE" property="amount" />
    <result column="fee" jdbcType="INTEGER" property="fee" />
    <result column="gas" jdbcType="INTEGER" property="gas" />
    <result column="pl_deduction" jdbcType="INTEGER" property="plDeduction" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="trans_type" jdbcType="INTEGER" property="transType" />
    <result column="payment_code" jdbcType="VARCHAR" property="paymentCode" />
    <result column="payee_account" jdbcType="VARCHAR" property="payeeAccount" />
    <result column="pay_channel_id" jdbcType="INTEGER" property="payChannelId" />
    <result column="pay_channel_cms_id" jdbcType="INTEGER" property="payChannelCmsId" />
    <result column="pay_no" jdbcType="VARCHAR" property="payNo" />
    <result column="escrow_payment_id" jdbcType="INTEGER" property="escrowPaymentId" />
    <result column="check_time" jdbcType="TIMESTAMP" property="checkTime" />
    <result column="trade_time" jdbcType="TIMESTAMP" property="tradeTime" />
    <result column="check_id" jdbcType="INTEGER" property="checkId" />
    <result column="check_type" jdbcType="VARCHAR" property="checkType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, uid, withdraw_no, chip, amount,gas, fee, pl_deduction, status, trans_type, payment_code,
    payee_account, pay_channel_id, pay_channel_cms_id, pay_no, escrow_payment_id, check_time, 
    trade_time, check_id, check_type, remark, updated_time, created_time
  </sql>
  <select id="selectByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.WithdrawChipExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from withdraw_chip
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from withdraw_chip
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from withdraw_chip
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.WithdrawChipExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from withdraw_chip
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.allinpokers.yunying.entity.crazypoker.WithdrawChip">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into withdraw_chip (uid, withdraw_no, chip, 
      amount, fee, pl_deduction, 
      status, trans_type, payment_code, 
      payee_account, pay_channel_id, pay_channel_cms_id, 
      pay_no, escrow_payment_id, check_time, 
      trade_time, check_id, check_type, 
      remark, updated_time, created_time
      )
    values (#{uid,jdbcType=INTEGER}, #{withdrawNo,jdbcType=VARCHAR}, #{chip,jdbcType=INTEGER}, 
      #{amount,jdbcType=DOUBLE}, #{fee,jdbcType=INTEGER}, #{plDeduction,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{transType,jdbcType=INTEGER}, #{paymentCode,jdbcType=VARCHAR}, 
      #{payeeAccount,jdbcType=VARCHAR}, #{payChannelId,jdbcType=INTEGER}, #{payChannelCmsId,jdbcType=INTEGER}, 
      #{payNo,jdbcType=VARCHAR}, #{escrowPaymentId,jdbcType=INTEGER}, #{checkTime,jdbcType=TIMESTAMP}, 
      #{tradeTime,jdbcType=TIMESTAMP}, #{checkId,jdbcType=INTEGER}, #{checkType,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{updatedTime,jdbcType=TIMESTAMP}, #{createdTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.allinpokers.yunying.entity.crazypoker.WithdrawChip">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into withdraw_chip
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="uid != null">
        uid,
      </if>
      <if test="withdrawNo != null">
        withdraw_no,
      </if>
      <if test="chip != null">
        chip,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="fee != null">
        fee,
      </if>
      <if test="plDeduction != null">
        pl_deduction,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="transType != null">
        trans_type,
      </if>
      <if test="paymentCode != null">
        payment_code,
      </if>
      <if test="payeeAccount != null">
        payee_account,
      </if>
      <if test="payChannelId != null">
        pay_channel_id,
      </if>
      <if test="payChannelCmsId != null">
        pay_channel_cms_id,
      </if>
      <if test="payNo != null">
        pay_no,
      </if>
      <if test="escrowPaymentId != null">
        escrow_payment_id,
      </if>
      <if test="checkTime != null">
        check_time,
      </if>
      <if test="tradeTime != null">
        trade_time,
      </if>
      <if test="checkId != null">
        check_id,
      </if>
      <if test="checkType != null">
        check_type,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="uid != null">
        #{uid,jdbcType=INTEGER},
      </if>
      <if test="withdrawNo != null">
        #{withdrawNo,jdbcType=VARCHAR},
      </if>
      <if test="chip != null">
        #{chip,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DOUBLE},
      </if>
      <if test="fee != null">
        #{fee,jdbcType=INTEGER},
      </if>
      <if test="plDeduction != null">
        #{plDeduction,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="transType != null">
        #{transType,jdbcType=INTEGER},
      </if>
      <if test="paymentCode != null">
        #{paymentCode,jdbcType=VARCHAR},
      </if>
      <if test="payeeAccount != null">
        #{payeeAccount,jdbcType=VARCHAR},
      </if>
      <if test="payChannelId != null">
        #{payChannelId,jdbcType=INTEGER},
      </if>
      <if test="payChannelCmsId != null">
        #{payChannelCmsId,jdbcType=INTEGER},
      </if>
      <if test="payNo != null">
        #{payNo,jdbcType=VARCHAR},
      </if>
      <if test="escrowPaymentId != null">
        #{escrowPaymentId,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeTime != null">
        #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkId != null">
        #{checkId,jdbcType=INTEGER},
      </if>
      <if test="checkType != null">
        #{checkType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.WithdrawChipExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from withdraw_chip
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update withdraw_chip
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.uid != null">
        uid = #{record.uid,jdbcType=INTEGER},
      </if>
      <if test="record.withdrawNo != null">
        withdraw_no = #{record.withdrawNo,jdbcType=VARCHAR},
      </if>
      <if test="record.chip != null">
        chip = #{record.chip,jdbcType=INTEGER},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=DOUBLE},
      </if>
      <if test="record.fee != null">
        fee = #{record.fee,jdbcType=INTEGER},
      </if>
      <if test="record.plDeduction != null">
        pl_deduction = #{record.plDeduction,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.transType != null">
        trans_type = #{record.transType,jdbcType=INTEGER},
      </if>
      <if test="record.paymentCode != null">
        payment_code = #{record.paymentCode,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeAccount != null">
        payee_account = #{record.payeeAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.payChannelId != null">
        pay_channel_id = #{record.payChannelId,jdbcType=INTEGER},
      </if>
      <if test="record.payChannelCmsId != null">
        pay_channel_cms_id = #{record.payChannelCmsId,jdbcType=INTEGER},
      </if>
      <if test="record.payNo != null">
        pay_no = #{record.payNo,jdbcType=VARCHAR},
      </if>
      <if test="record.escrowPaymentId != null">
        escrow_payment_id = #{record.escrowPaymentId,jdbcType=INTEGER},
      </if>
      <if test="record.checkTime != null">
        check_time = #{record.checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tradeTime != null">
        trade_time = #{record.tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.checkId != null">
        check_id = #{record.checkId,jdbcType=INTEGER},
      </if>
      <if test="record.checkType != null">
        check_type = #{record.checkType,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedTime != null">
        updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdTime != null">
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update withdraw_chip
    set id = #{record.id,jdbcType=INTEGER},
      uid = #{record.uid,jdbcType=INTEGER},
      withdraw_no = #{record.withdrawNo,jdbcType=VARCHAR},
      chip = #{record.chip,jdbcType=INTEGER},
      amount = #{record.amount,jdbcType=DOUBLE},
      fee = #{record.fee,jdbcType=INTEGER},
      pl_deduction = #{record.plDeduction,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      trans_type = #{record.transType,jdbcType=INTEGER},
      payment_code = #{record.paymentCode,jdbcType=VARCHAR},
      payee_account = #{record.payeeAccount,jdbcType=VARCHAR},
      pay_channel_id = #{record.payChannelId,jdbcType=INTEGER},
      pay_channel_cms_id = #{record.payChannelCmsId,jdbcType=INTEGER},
      pay_no = #{record.payNo,jdbcType=VARCHAR},
      escrow_payment_id = #{record.escrowPaymentId,jdbcType=INTEGER},
      check_time = #{record.checkTime,jdbcType=TIMESTAMP},
      trade_time = #{record.tradeTime,jdbcType=TIMESTAMP},
      check_id = #{record.checkId,jdbcType=INTEGER},
      check_type = #{record.checkType,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      created_time = #{record.createdTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.allinpokers.yunying.entity.crazypoker.WithdrawChip">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update withdraw_chip
    <set>
      <if test="uid != null">
        uid = #{uid,jdbcType=INTEGER},
      </if>
      <if test="withdrawNo != null">
        withdraw_no = #{withdrawNo,jdbcType=VARCHAR},
      </if>
      <if test="chip != null">
        chip = #{chip,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DOUBLE},
      </if>
      <if test="fee != null">
        fee = #{fee,jdbcType=INTEGER},
      </if>
      <if test="plDeduction != null">
        pl_deduction = #{plDeduction,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="transType != null">
        trans_type = #{transType,jdbcType=INTEGER},
      </if>
      <if test="paymentCode != null">
        payment_code = #{paymentCode,jdbcType=VARCHAR},
      </if>
      <if test="payeeAccount != null">
        payee_account = #{payeeAccount,jdbcType=VARCHAR},
      </if>
      <if test="payChannelId != null">
        pay_channel_id = #{payChannelId,jdbcType=INTEGER},
      </if>
      <if test="payChannelCmsId != null">
        pay_channel_cms_id = #{payChannelCmsId,jdbcType=INTEGER},
      </if>
      <if test="payNo != null">
        pay_no = #{payNo,jdbcType=VARCHAR},
      </if>
      <if test="escrowPaymentId != null">
        escrow_payment_id = #{escrowPaymentId,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        check_time = #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeTime != null">
        trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkId != null">
        check_id = #{checkId,jdbcType=INTEGER},
      </if>
      <if test="checkType != null">
        check_type = #{checkType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.allinpokers.yunying.entity.crazypoker.WithdrawChip">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update withdraw_chip
    set uid = #{uid,jdbcType=INTEGER},
      withdraw_no = #{withdrawNo,jdbcType=VARCHAR},
      chip = #{chip,jdbcType=INTEGER},
      amount = #{amount,jdbcType=DOUBLE},
      fee = #{fee,jdbcType=INTEGER},
      pl_deduction = #{plDeduction,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      trans_type = #{transType,jdbcType=INTEGER},
      payment_code = #{paymentCode,jdbcType=VARCHAR},
      payee_account = #{payeeAccount,jdbcType=VARCHAR},
      pay_channel_id = #{payChannelId,jdbcType=INTEGER},
      pay_channel_cms_id = #{payChannelCmsId,jdbcType=INTEGER},
      pay_no = #{payNo,jdbcType=VARCHAR},
      escrow_payment_id = #{escrowPaymentId,jdbcType=INTEGER},
      check_time = #{checkTime,jdbcType=TIMESTAMP},
      trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      check_id = #{checkId,jdbcType=INTEGER},
      check_type = #{checkType,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      created_time = #{createdTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>