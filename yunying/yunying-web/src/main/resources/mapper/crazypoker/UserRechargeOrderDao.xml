<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.UserRechargeOrderDao">
    <!-- 在这里写自定义的sql，autogen 目录下的文件不要手动修改 -->

    <select id="sumTotalOrderPrice" resultType="com.allinpokers.yunying.services.model.UserRechargeOrderSum">
        select ifnull(sum(order_price), 0) as totalOrderPrice,
               count(*) as totalCount,
               ifnull(sum(if(`status` = 1, 1, 0)), 0) as totalSuccess,
               ifnull(sum(fee), 0) as totalFee
        from user_recharge_order
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
</mapper>