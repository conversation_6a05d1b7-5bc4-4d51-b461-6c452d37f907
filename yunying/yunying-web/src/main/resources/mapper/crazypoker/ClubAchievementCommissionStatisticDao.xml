<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.ClubAchievementCommissionStatisticDao">
    <!-- 在这里写自定义的sql，autogen 目录下的文件不要手动修改 -->

    <select id="sumClubBeans" resultType="long">
        select ifnull(sum(beans), 0)
        from club_achievement_commission_statistic
        where club_id = #{clubId}
          and date between #{startDate} and #{endDate}
    </select>
</mapper>