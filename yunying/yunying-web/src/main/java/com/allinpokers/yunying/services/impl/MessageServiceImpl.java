package com.allinpokers.yunying.services.impl;

import com.allinpokers.yunying.appmessage.send.AppBusinessMessageSender;
import com.allinpokers.yunying.appmessage.send.bean.ClubCreation;
import com.allinpokers.yunying.dao.crazypoker.MessageClubRecordDao;
import com.allinpokers.yunying.dao.crazypoker.MessageClubRequestDao;
import com.allinpokers.yunying.dao.crazypoker.MessageTribeRecordDao;
import com.allinpokers.yunying.entity.crazypoker.*;
import com.allinpokers.yunying.enu.EClubRequestMessageCode;
import com.allinpokers.yunying.enu.ETribeMembersType;
import com.allinpokers.yunying.enu.ResponseCodeEnum;
import com.allinpokers.yunying.exception.UserException;
import com.allinpokers.yunying.model.request.message.ApproveClubChangePayReq;
import com.allinpokers.yunying.model.request.message.ApproveClubCreateReq;
import com.allinpokers.yunying.model.request.message.MessageListReq;
import com.allinpokers.yunying.model.request.message.OperationMessageReq;
import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.model.response.PageBean;
import com.allinpokers.yunying.model.response.message.MessageClubChangePay;
import com.allinpokers.yunying.model.response.message.MessageClubCreate;
import com.allinpokers.yunying.model.response.message.MessageClubCreateList;
import com.allinpokers.yunying.model.response.message.MessageClubCreateApprovalResult;
import com.allinpokers.yunying.model.response.message.param.ClubCreateRequestPo;
import com.allinpokers.yunying.entity.crazypoker.UserAccount;
import com.allinpokers.yunying.entity.crazypoker.UserAccountLog;
import com.allinpokers.yunying.services.UserAccountService;
import com.allinpokers.yunying.rabbitmq.client.MessageSender;
import com.allinpokers.yunying.rabbitmq.client.bean.ClubMessage;
import com.allinpokers.yunying.rabbitmq.client.bean.TribeMessage;
import com.allinpokers.yunying.rabbitmq.constant.EMessageChannelCode;
import com.allinpokers.yunying.rabbitmq.constant.EMessageCode;
import com.allinpokers.yunying.enu.EUserAccountLogType;
import com.allinpokers.yunying.services.*;
import com.allinpokers.yunying.tier.dao.TierDao;
import com.allinpokers.yunying.util.JsonUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 消息 service
 *
 * <AUTHOR>
 */
@Service("messageService")
@Slf4j
public class MessageServiceImpl implements MessageService {
    @Resource
    private MessageClubRequestDao messageClubRequestDao;
    @Resource
    private MessageClubRecordDao messageClubRecordDao;
    @Resource
    private UserDetailsInfoService userDetailsInfoService;
    @Resource
    private TribeMembersService tribeMembersService;
    @Resource
    private TribeRecordService tribeRecordService;
    @Resource
    private MessageSender messageSender;
    @Resource
    private ClubRecordService clubRecordService;
    @Resource
    private ClubMembersService clubMembersService;
    @Resource
    private MessageUnreadService messageUnreadService;
    @Resource
    private PromotionUserInformationService promotionUserInformationService;
    @Resource
    private MessageTribeRecordDao messageTribeRecordDao;
    @Resource
    private PayChannelService payChannelService;
    @Resource
    private ClubPayChannelServiceImpl clubPayChannelService;
    @Resource
    private UserAccountService userAccountService;

    @Resource
    private AppBusinessMessageSender appBusinessMessageSender;

    @Override
    public PageBean<MessageClubChangePay> listClubChangePayMessages(MessageListReq req) {
        PageHelper.startPage(req.getPage(), req.getSize());
        Page<MessageClubRequest> page = messageClubRequestDao.findByTypeCode(EClubRequestMessageCode.CHAGNE_PAY);
        if (page.isEmpty()) {
            return PageBean.empty();
        }
        //查询用户昵称
        Set<Integer> userIdSet = page.stream().map(msg -> Integer.valueOf(msg.getUserId())).collect(Collectors.toSet());
        Map<Integer, UserDetailsInfo> userMap = userDetailsInfoService.findUsersMapByUserIds(userIdSet);

        //转换成messageInfo
        List<MessageClubChangePay> beanList = new ArrayList<>();
        page.forEach(message -> {
            UserDetailsInfo user = userMap.getOrDefault(Integer.valueOf(message.getUserId()), new UserDetailsInfo());
            String nickname = user.getNikeName();
            //联系方式
            String contact = getContact(user);
            MessageClubChangePay info = MessageClubChangePay.builder()
                    .msgId(message.getMsgId())
                    .userId(message.getUserId())
                    .nickname(nickname)
                    .contact(contact)
                    .createTime(message.getCreateTime())
                    .build();
            beanList.add(info);
        });

        return PageBean.of(page.getTotal(), req.getPage(), req.getSize(), beanList);
    }

    private String getContact(UserDetailsInfo user) {
        if (!StringUtils.isEmpty(user.getPhone())) {
            return user.getPhone();
        }
        return "";
    }

    @Transactional(transactionManager = "crazyPokerTransactionManager", rollbackFor = RuntimeException.class)
    @Override
    public CommonRespon approveClubChangePayMessage(ApproveClubChangePayReq req) {
        MessageClubRequest message = messageClubRequestDao.selectByPrimaryKey(req.getMsgId());
        if (message == null) {
            return CommonRespon.failure(ResponseCodeEnum.DATA_EMPT);
        }
        if (!message.getType().equals(EClubRequestMessageCode.CHAGNE_PAY.getCode())) {
            return CommonRespon.failure(ResponseCodeEnum.MESSAGE_CANT_SUPPORT_MESSAGE_TYPE);
        }
        // TODO: 2019/3/25 交易方式
        return CommonRespon.failure(ResponseCodeEnum.DATA_EMPT);
    }

    @Override
    public CommonRespon rejectClubChangePayMessage(OperationMessageReq req) {
        MessageClubRequest message = messageClubRequestDao.selectByPrimaryKey(req.getMsgId());
        if (message == null) {
            return CommonRespon.failure(ResponseCodeEnum.DATA_EMPT);
        }
        if (!message.getType().equals(EClubRequestMessageCode.CHAGNE_PAY.getCode())) {
            return CommonRespon.failure(ResponseCodeEnum.MESSAGE_CANT_SUPPORT_MESSAGE_TYPE);
        }

        //0. 删除消息请求表对应的数据
        int deleteRow = messageClubRequestDao.deleteByPrimaryKey(message.getMsgId());
        if (deleteRow <= 0) {
            //没有删除成功，记录不存在，不需要往下执行，直接返回
            throw new UserException(ResponseCodeEnum.DATA_EMPT);
        }

        //1. 保存相关的记录表数据
        Integer clubId = Integer.valueOf(message.getClubId());
        ClubRecord clubRecord = clubRecordService.findByClubId(clubId);
        EMessageCode messageCode = EMessageCode.CLUB_CHANGE_PAY_F;
        MessageClubRecord record = makeRecord(message, messageCode, req.getOperatorId(), clubRecord.getName());
        messageClubRecordDao.insert(record);
        messageUnreadService.addUnread(record);

        //3. 推送一条拒绝创建联盟的消息到mq
        sendRabbitMq(record, EMessageChannelCode.TIM.getCode());
        return CommonRespon.success();
    }

    private MessageClubRecord makeRecord(MessageClubRequest message, EMessageCode messageCode, String senderId, String content) {
        return MessageClubRecord.builder()
                .msgId(message.getMsgId())
                .clubId(message.getClubId())
                .senderId(senderId)
                .reciverId(message.getUserId())
                .header("-1")
                .title(messageCode.getDesc())
                .content(content)
                .remark("")
                .type(messageCode.getCode())
                .msgStatus(0)
                .createTime(LocalDateTime.now())
                .build();
    }

    @Override
    public PageBean<MessageClubCreateList> listClubCreateMessages(MessageListReq req) {
        PageHelper.startPage(req.getPage(), req.getSize());
        Page<MessageClubRequest> page = messageClubRequestDao.findByTypeCode(EClubRequestMessageCode.CREATE);
        if (page.isEmpty()) {
            return PageBean.empty();
        }
        //查询用户昵称
        Set<Integer> userIdSet = page.stream().map(msg -> Integer.valueOf(msg.getUserId())).collect(Collectors.toSet());
        Map<Integer, UserDetailsInfo> userMap = userDetailsInfoService.findUsersMapByUserIds(userIdSet);

        //转换成messageInfo
        List<MessageClubCreateList> beanList = new ArrayList<>();
        page.forEach(message -> {
            UserDetailsInfo user = userMap.getOrDefault(Integer.valueOf(message.getUserId()), new UserDetailsInfo());
            String nickname = user.getNikeName();
            String randomNum = user.getRandomNum();
            //联系方式
            ClubCreateRequestPo param = JsonUtils.read(message.getParam(), ClubCreateRequestPo.class);
            String contact = getContact(param);

            MessageClubCreateList info = MessageClubCreateList.builder()
                    .msgId(message.getMsgId())
                    .userId(message.getUserId())
                    .randomNum(randomNum)
                    .nickname(nickname)
                    .clubName(param.getClubName())
                    .clubHead(param.getClubHead())
                    .useCustom(param.getUseCustom())
                    .customUrl(param.getCustomUrl())
                    .clubArea(param.getClubArea())
                    .clubDesc(param.getClubDesc())
                    .contact(contact)
                    .message(param.getMessage())
                    .createTime(message.getCreateTime())
                    .build();
            beanList.add(info);
        });

        return PageBean.of(page.getTotal(), req.getPage(), req.getSize(), beanList);
    }

    private String getContact(ClubCreateRequestPo param) {
        if (!StringUtils.isEmpty(param.getPhone())) {
            return param.getPhone();
        }
        if (!StringUtils.isEmpty(param.getWechat())) {
            return param.getWechat();
        }
        if (!StringUtils.isEmpty(param.getTelegram())) {
            return param.getTelegram();
        }
        if (!StringUtils.isEmpty(param.getEmail())) {
            return param.getEmail();
        }
        return "";
    }

    private CommonRespon<MessageClubCreateApprovalResult> checkClubRandomId(ApproveClubCreateReq req) {
        //判断俱乐部随机号是否存在
        if (req.getRandomId() ==null) {
            return CommonRespon.failure(ResponseCodeEnum.CLUB_ID_FAIL_1);
        }

        // 检查格式不能以0开头，且要4-6位数字
        String reg = "^[1-9]\\d{3,5}$";
        if (!req.getRandomId().toString().matches(reg)) {
            return CommonRespon.failure(ResponseCodeEnum.CLUB_ID_FAIL_2);
        }

        // 检查是否已经存在
        if (clubRecordService.findByRandomId(req.getRandomId()) != null) {
            return CommonRespon.failure(ResponseCodeEnum.CLUB_ID_FAIL_3);
        }

        return null;
    }

    @Transactional(transactionManager = "crazyPokerTransactionManager", rollbackFor = RuntimeException.class)
    @Override
    public CommonRespon<MessageClubCreateApprovalResult> approveClubCreateMessage(ApproveClubCreateReq req) {

        // 检查ID
        CommonRespon<MessageClubCreateApprovalResult> checkRandomIdResult = checkClubRandomId(req);
        if (checkRandomIdResult != null) {
            return checkRandomIdResult;
        }

        //设置占成率和俱乐部上限的默认值
        int maxProfit = 99;
        if (req.getProfit() == null) {
            req.setProfit(maxProfit);
        }
        if (req.getMemberUpperLimit() == null) {
            req.setMemberUpperLimit(5000);
        }

        MessageClubRequest message = messageClubRequestDao.selectByPrimaryKey(req.getMsgId());
        //校验
        if (message == null) {
            return CommonRespon.failure(ResponseCodeEnum.DATA_EMPT);
        }
        if (!message.getType().equals(EClubRequestMessageCode.CREATE.getCode())) {
            return CommonRespon.failure(ResponseCodeEnum.MESSAGE_CANT_SUPPORT_MESSAGE_TYPE);
        }

        //这里可以使用参数配置
        if (req.getProfit() < 1 || req.getProfit() > maxProfit) {
            return CommonRespon.failure(ResponseCodeEnum.MESSAGE_TRIBE_PROFIT_OUT_OF_RANGE);
        }
        if (req.getMemberUpperLimit() < 1 || req.getMemberUpperLimit() > 5000) {
            return CommonRespon.failure(ResponseCodeEnum.MESSAGE_CLUB_UPPER_LIMIT_OUT_OF_RANGE);
        }


        //判断代充渠道设置
        // PayChannel payChannel = payChannelService.selectPayChannelById(req.getPayChannelId());
        // if (payChannel == null || !payChannel.getStatus().equals(0)) {
        //     return CommonRespon.failure(ResponseCodeEnum.MESSAGE_JOIN_CLUB_PAY_CHANNEL_NOT_LEGAL);
        // }


        //判断联盟人数是否超出限制
        boolean hasTribeId = !StringUtils.isEmpty(req.getTribeId());
        TribeRecord tribeRecord = null;
        if (hasTribeId) {
            Integer tribeId = Integer.valueOf(req.getTribeId());
            tribeRecord = tribeRecordService.findByTribeId(tribeId);
            if (tribeRecord.getClubLimit() <= tribeRecord.getClubCount()) {
                return CommonRespon.failure(ResponseCodeEnum.MESSAGE_CLUB_IS_BEYOND_TRIBE_LIMIT);
            }
        }
        //判断俱乐部的名称是否重复
        ClubCreateRequestPo param = JsonUtils.read(message.getParam(), ClubCreateRequestPo.class);
        ClubRecord clubRecord = clubRecordService.findByClubName(param.getClubName());
        if (clubRecord != null) {
            return CommonRespon.failure(ResponseCodeEnum.MESSAGE_EXIST_SAME_TRIBE_NAME_CANT_CREATE);
        }

        //0. 删除消息请求表对应的数据
        int deleteRow = messageClubRequestDao.deleteByPrimaryKey(message.getMsgId());
        if (deleteRow <= 0) {
            //没有删除成功，记录不存在，不需要往下执行，直接返回
            throw new UserException(ResponseCodeEnum.DATA_EMPT);
        }

        //写俱乐部
        clubRecord = clubRecordService.insertByApproveCreateMessage(message, param, req);
        //写俱乐部成员
        clubMembersService.insertByApproveCreateMessage(clubRecord);

        // 添加到代充渠道
        // this.addClubToPayChannel(req.getOperatorId(), clubRecord.getId(), payChannel);

        if (hasTribeId) {
            //写联盟成员
            Integer tribeId = Integer.valueOf(req.getTribeId());
            tribeMembersService.insertByDefault(tribeId, clubRecord.getId(), ETribeMembersType.MEMBER);
            tribeRecordService.addClubCountMemberCount(tribeId, 1, clubRecord.getClubMembers());

            // 如果是指定加入联盟，则插入层级数据
            insertClubUserTier(clubRecord.getId());

        }
        //写PromotionUserInformation
        promotionUserInformationService.insertByApproveCreateMessage(clubRecord);

        /* ********：注释原有的消息推送代码 <AUTHOR>
        //2. 记录消息记录表
        EMessageCode messageCode = EMessageCode.CLUB_CREATE_RESULT_S;
        MessageClubRecord record = makeRecord(message, messageCode, req.getOperatorId(), param.getClubName());
        messageUnreadService.addUnread(record);
        messageClubRecordDao.insert(record);

        //5. 保存 推送一条俱乐部变动的消息到消息记录表中
        MessageTribeRecord noticeRecord = null;
        List<String> receiverIds = new ArrayList<>();
        if (hasTribeId) {
            List<ClubRecord> clubRecords = clubRecordService.findByTribeId(Integer.parseInt(req.getTribeId()));
            ClubRecord finalClubRecord = clubRecord;
            clubRecords = clubRecords.stream().filter(club -> !club.getId().equals(finalClubRecord.getId())).collect(Collectors.toList());
            messageCode = EMessageCode.TRIBE_JOIN_NEW_CLUB;
            for (ClubRecord club : clubRecords) {
                noticeRecord = MessageTribeRecord.builder()
                        .msgId(message.getMsgId())
                        .tribeId(req.getTribeId())
                        .senderId(club.getCreator())
                        .reciverId(club.getCreator())
                        .header("-1")
                        .title(messageCode.getDesc())
                        .content(param.getClubName())
                        .remark(tribeRecord.getTribeName())
                        .type(messageCode.getCode())
                        .msgStatus(0)
                        .createTime(LocalDateTime.now())
                        .build();
                receiverIds.add(noticeRecord.getReciverId());
                messageTribeRecordDao.insert(noticeRecord);
                messageUnreadService.addUnread(noticeRecord);
            }
        }

        //4. 推送一条同意创建的消息到mq
        sendRabbitMq(record, EMessageChannelCode.TIM.getCode());
        //5. 推送一条俱乐部变动的消息到mq（给该联盟下所有俱乐部）
        if (!receiverIds.isEmpty()) {
            sendRabbitMq(noticeRecord, EMessageChannelCode.TIM.getCode(), receiverIds);
        }
         */

        // 启用新的消息推送代码
        ClubCreation clubCreation = ClubCreation.builder()
                .clubName(clubRecord.getName())
                .clubId(Long.valueOf(clubRecord.getId()))
                .userId(Long.valueOf(message.getUserId()))
                .build();
        // 判断是否有联盟ID，有联盟ID则给联盟主发送消息
        if (hasTribeId) {
            clubCreation.setTribeId(Long.valueOf(tribeRecord.getId()));
            clubCreation.setTribeOwnerId(Long.valueOf(tribeRecord.getCreator()));
            clubCreation.setTribeName(tribeRecord.getTribeName());
        }
        // 推送消息
        appBusinessMessageSender.notifyClubCreationSuccessful(clubCreation);
        return CommonRespon.success(new MessageClubCreateApprovalResult(clubRecord.getId()));
    }

    /**
     * 把俱乐部添加到代充渠道
     *
     * @param operatorId
     * @param clubId
     * @param payChannel
     */
    private void addClubToPayChannel(String operatorId, Integer clubId, PayChannel payChannel) {
        operatorId = operatorId.replaceAll("YY-", "");
        //添加club_pay 记录
        this.clubPayChannelService.insertClubPayChannelRecord(clubId, payChannel, operatorId);
        //添加日志
        this.clubPayChannelService.insertClubPayChannelRecordLog(clubId, payChannel, operatorId);
    }

    @Transactional(transactionManager = "crazyPokerTransactionManager", rollbackFor = RuntimeException.class)
    @Override
    public CommonRespon rejectClubCreateMessage(OperationMessageReq req, Integer id) {
        MessageClubRequest message = messageClubRequestDao.selectByPrimaryKey(req.getMsgId());
        if (message == null) {
            return CommonRespon.failure(ResponseCodeEnum.DATA_EMPT);
        }
        if (!message.getType().equals(EClubRequestMessageCode.CREATE.getCode())) {
            return CommonRespon.failure(ResponseCodeEnum.MESSAGE_CANT_SUPPORT_MESSAGE_TYPE);
        }
        String userId = message.getUserId();

        //0. 删除消息请求表对应的数据
        int deleteRow = messageClubRequestDao.deleteByPrimaryKey(message.getMsgId());
        if (deleteRow <= 0) {
            //没有删除成功，记录不存在，不需要往下执行，直接返回
            throw new UserException(ResponseCodeEnum.DATA_EMPT);
        }


        //1. 保存相关的记录表数据
        ClubCreateRequestPo param = JsonUtils.read(message.getParam(), ClubCreateRequestPo.class);
          /*
        ********：注释原有的消息推送代码 <AUTHOR>
        EMessageCode messageCode = EMessageCode.CLUB_CREATE_RESULT_F;
        MessageClubRecord record = makeRecord(message, messageCode, req.getOperatorId(), param.getClubName());
        messageClubRecordDao.insert(record);
        messageUnreadService.addUnread(record);
        */
        //返還創建時扣除的鑽石
        Integer amount = Integer.parseInt(param.getFee());
                
        // UserAccount
        UserAccount userAccount = userAccountService.selectByPrimaryKey(Integer.parseInt(userId));
        Integer currentExtractChip = userAccount.getChip() ;
        Integer currentNotExtractChip = userAccount.getNotExtractChip();
                
        userAccountService.addExtractChip(Integer.parseInt(userId), amount);
                
        //add UserAccountLog
        UserAccountLog userAccountLog = UserAccountLog.builder()
                        .userId(Integer.parseInt(userId))
                        .type(UserAccountLog.Type.RETURN_CREATE_CLUB.getValue())
                        .changeSource(1)
                        .currentChip(currentExtractChip)
                        .changeChip(amount)
                        .currNotExtractChip(currentNotExtractChip)
                        .changeNotExtractChip(0)
                        .currPlCount(userAccount.getPlCount())
                        .changePlCount(0)
                        .desction("返还创建俱乐部时扣除的钻石")
                        .externalId("")
                        .opId(id)
                        .createdTime(LocalDateTime.now())
                        .build();
        userAccountService.addLog(userAccountLog);

        //3. 推送一条拒绝创建联盟的消息到mq
        /*
        ********：注释原有的消息推送代码 <AUTHOR>
        sendRabbitMq(record, EMessageChannelCode.TIM.getCode());
        */

        // 启用新的消息推送代码
        appBusinessMessageSender.notifyClubCreationFailed(ClubCreation.builder()
                .clubName(param.getClubName())
                .userId(Long.valueOf(userId))
                .build());

        return CommonRespon.success();
    }

    private void sendRabbitMq(MessageClubRecord record, int pushChannel) {
        sendRabbitMq(record, pushChannel, Collections.singletonList(record.getReciverId()));
    }

    private void sendRabbitMq(MessageClubRecord record, int pushChannel, List<String> receiverIds) {
        ClubMessage clubMessage = ClubMessage.builder()
                .clubId(record.getClubId())
                .senderId(record.getSenderId())
                .reciverUserIds(receiverIds)
                .header(record.getHeader())
                .title(record.getTitle())
                .content(record.getContent())
                .remark(record.getRemark())
                .type(record.getType())
                .msgStatus(record.getMsgStatus())
                .pushChannel(pushChannel)
                .build();
        String msgId = messageSender.sendClubMessage(clubMessage);
        log.info("send club message to rabbitmq, msgId={}", msgId);
    }

    private void sendRabbitMq(MessageTribeRecord record, int pushChannel, List<String> receiverIds) {
        TribeMessage tribeMessage = TribeMessage.builder()
                .tribeId(record.getTribeId())
                .senderId(record.getSenderId())
                .reciverUserIds(receiverIds)
                .header(record.getHeader())
                .title(record.getTitle())
                .content(record.getContent())
                .remark(record.getRemark())
                .type(record.getType())
                .msgStatus(record.getMsgStatus())
                .pushChannel(pushChannel)
                .build();
        String msgId = messageSender.sendTribeMessage(tribeMessage);
        log.info("send tribe message to rabbitmq, msgId={}", msgId);
    }

    private void sendRabbitMq(MessageTribeRecord record, int pushChannel) {
        sendRabbitMq(record, pushChannel, Collections.singletonList(record.getReciverId()));
    }

    @Resource
    TierDao tierDao;

    /**
     * 插入俱乐部用户层级数据
     * @param clubId
     */
    public void insertClubUserTier(Integer clubId) {

        // 查询底下所有用户
        List<Long> clubMemberIds = tierDao.findClubMemberIds(clubId);
        if (CollectionUtils.isEmpty(clubMemberIds)) {
            log.warn("俱乐部没有成员，clubId:{}", clubId);
            return;
        }
        // 将俱乐部设置为未分层
        tierDao.updateClubMembersToDefaultTier(clubId);
        // 批量插入
        for (Long userId : clubMemberIds) {
            joinClubSaveTier(userId.intValue(), clubId);
        }
    }
    /**
     * 加入俱乐部时保存用户的联盟层级数据
     * @param userId 用户ID
     * @param clubId 俱乐部ID
     */
    public void joinClubSaveTier(Integer userId, Integer clubId) {
        Integer tribeId = tierDao.getTribeIdByClubId(clubId);
        // 如果没有加入俱乐部则不处理
        if (tribeId == null) {
            log.warn("俱乐部未加入联盟，userId:{}, clubId:{}", userId, clubId);
            return;
        }
        Long userTribeRoomTierId = tierDao.findUserTribeRoomTier(userId, tribeId);
        // 如果有值，则不处理，如果没值，则插入
        if (userTribeRoomTierId == null || userTribeRoomTierId == 0) {
            Integer tribeMinTierId = tierDao.getTribeMinTier();
            tierDao.insertUserTribeRoomTier(userId, tribeId, tribeMinTierId);
            tierDao.insertUserDefaultPaymentActivityTier(
                    tribeId,
                    clubId,
                    userId
            );
        }
    }
}
