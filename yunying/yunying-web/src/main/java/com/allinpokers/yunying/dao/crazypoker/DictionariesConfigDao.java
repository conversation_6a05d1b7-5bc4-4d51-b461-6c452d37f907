package com.allinpokers.yunying.dao.crazypoker;

import com.allinpokers.yunying.entity.crazypoker.DictionariesConfig;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;

@Mapper
@Repository
public interface DictionariesConfigDao {
    @Select("select * from dictionaries_config where code=#{code}")
    DictionariesConfig selectOneByCode(Integer code);

    @Insert("UPDATE dictionaries_config set number=#{number} where code=#{code}")
    int updateConfig(BigDecimal number,Integer code);

}
