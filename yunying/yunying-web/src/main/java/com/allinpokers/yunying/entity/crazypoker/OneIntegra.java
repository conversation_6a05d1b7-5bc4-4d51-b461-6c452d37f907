package com.allinpokers.yunying.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
@Data
public class OneIntegra {
    @ApiModelProperty(value = "用户id")
    private int userId;
    @ApiModelProperty(value = "用户昵称")
    private String userName;
    @ApiModelProperty(value = "盲注")
    private int mangzhu;
    @ApiModelProperty(value = "房间id")
    private int roomId;
    @ApiModelProperty(value = "房间名称")
    private String roomName;
    @ApiModelProperty(value = "玩家下注")
    private int xiazhu;
    @ApiModelProperty(value = "玩家输赢")
    private int integral;
    @ApiModelProperty(value = "保险赔付")
    private int insuranceExpenditure;
    @ApiModelProperty(value = "第一次投入保险")
    private int insuranceProfitOne;
    @ApiModelProperty(value = "第二次投入保险")
    private int insuranceProfitTow;
    @ApiModelProperty(value = "俱乐部保险获利")
    private int clubInsurance;
    @ApiModelProperty(value = "联盟保险获利")
    private int tribeInsurance;
    @ApiModelProperty(value = "服务费")
    private int fee;
    @ApiModelProperty(value = "俱乐部获取服务费返利")
    private int feeClub;
    @ApiModelProperty(value = "联盟获取服务费返利")
    private int feeTribe;
    @ApiModelProperty(value = "本手中彩值")
    private int jp;
    @ApiModelProperty(value = "彩金服务费")
    private int jpFee;
    @ApiModelProperty(value = "俱乐部获取彩金服务费")
    private int jpFeeClub;
    @ApiModelProperty(value = "联盟获取彩金服务费")
    private int jpFeeTribe;
    @ApiModelProperty(value = "贡献彩池的金额")
    private  int contribution;
    @ApiModelProperty(value = "贡献的彩金投入池")
    private int sign;
    @ApiModelProperty(value = "手数")
    private int version;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "俱乐部输赢")
    private int clubWin;
    @ApiModelProperty(value = "联盟输赢")
    private int rebateWin;
    @ApiModelProperty(value = "平台输赢")
    private int adminWin;
    @ApiModelProperty(value = "抽水")
    private int taxChip;
    @ApiModelProperty(value = "俱乐部房间类型")
    private int clubRoomType;
}
