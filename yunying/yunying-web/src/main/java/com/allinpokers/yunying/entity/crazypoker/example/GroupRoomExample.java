package com.allinpokers.yunying.entity.crazypoker.example;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class GroupRoomExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table group_room
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table group_room
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table group_room
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table group_room
     *
     * @mbg.generated
     */
    public GroupRoomExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table group_room
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table group_room
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table group_room
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table group_room
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table group_room
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table group_room
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table group_room
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table group_room
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table group_room
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table group_room
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table group_room
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andInsuranceIsNull() {
            addCriterion("insurance is null");
            return (Criteria) this;
        }

        public Criteria andInsuranceIsNotNull() {
            addCriterion("insurance is not null");
            return (Criteria) this;
        }

        public Criteria andInsuranceEqualTo(Byte value) {
            addCriterion("insurance =", value, "insurance");
            return (Criteria) this;
        }

        public Criteria andInsuranceNotEqualTo(Byte value) {
            addCriterion("insurance <>", value, "insurance");
            return (Criteria) this;
        }

        public Criteria andInsuranceGreaterThan(Byte value) {
            addCriterion("insurance >", value, "insurance");
            return (Criteria) this;
        }

        public Criteria andInsuranceGreaterThanOrEqualTo(Byte value) {
            addCriterion("insurance >=", value, "insurance");
            return (Criteria) this;
        }

        public Criteria andInsuranceLessThan(Byte value) {
            addCriterion("insurance <", value, "insurance");
            return (Criteria) this;
        }

        public Criteria andInsuranceLessThanOrEqualTo(Byte value) {
            addCriterion("insurance <=", value, "insurance");
            return (Criteria) this;
        }

        public Criteria andInsuranceIn(List<Byte> values) {
            addCriterion("insurance in", values, "insurance");
            return (Criteria) this;
        }

        public Criteria andInsuranceNotIn(List<Byte> values) {
            addCriterion("insurance not in", values, "insurance");
            return (Criteria) this;
        }

        public Criteria andInsuranceBetween(Byte value1, Byte value2) {
            addCriterion("insurance between", value1, value2, "insurance");
            return (Criteria) this;
        }

        public Criteria andInsuranceNotBetween(Byte value1, Byte value2) {
            addCriterion("insurance not between", value1, value2, "insurance");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andQianzhuIsNull() {
            addCriterion("qianzhu is null");
            return (Criteria) this;
        }

        public Criteria andQianzhuIsNotNull() {
            addCriterion("qianzhu is not null");
            return (Criteria) this;
        }

        public Criteria andQianzhuEqualTo(Integer value) {
            addCriterion("qianzhu =", value, "qianzhu");
            return (Criteria) this;
        }

        public Criteria andQianzhuNotEqualTo(Integer value) {
            addCriterion("qianzhu <>", value, "qianzhu");
            return (Criteria) this;
        }

        public Criteria andQianzhuGreaterThan(Integer value) {
            addCriterion("qianzhu >", value, "qianzhu");
            return (Criteria) this;
        }

        public Criteria andQianzhuGreaterThanOrEqualTo(Integer value) {
            addCriterion("qianzhu >=", value, "qianzhu");
            return (Criteria) this;
        }

        public Criteria andQianzhuLessThan(Integer value) {
            addCriterion("qianzhu <", value, "qianzhu");
            return (Criteria) this;
        }

        public Criteria andQianzhuLessThanOrEqualTo(Integer value) {
            addCriterion("qianzhu <=", value, "qianzhu");
            return (Criteria) this;
        }

        public Criteria andQianzhuIn(List<Integer> values) {
            addCriterion("qianzhu in", values, "qianzhu");
            return (Criteria) this;
        }

        public Criteria andQianzhuNotIn(List<Integer> values) {
            addCriterion("qianzhu not in", values, "qianzhu");
            return (Criteria) this;
        }

        public Criteria andQianzhuBetween(Integer value1, Integer value2) {
            addCriterion("qianzhu between", value1, value2, "qianzhu");
            return (Criteria) this;
        }

        public Criteria andQianzhuNotBetween(Integer value1, Integer value2) {
            addCriterion("qianzhu not between", value1, value2, "qianzhu");
            return (Criteria) this;
        }

        public Criteria andSbChipIsNull() {
            addCriterion("sb_chip is null");
            return (Criteria) this;
        }

        public Criteria andSbChipIsNotNull() {
            addCriterion("sb_chip is not null");
            return (Criteria) this;
        }

        public Criteria andSbChipEqualTo(Integer value) {
            addCriterion("sb_chip =", value, "sbChip");
            return (Criteria) this;
        }

        public Criteria andSbChipNotEqualTo(Integer value) {
            addCriterion("sb_chip <>", value, "sbChip");
            return (Criteria) this;
        }

        public Criteria andSbChipGreaterThan(Integer value) {
            addCriterion("sb_chip >", value, "sbChip");
            return (Criteria) this;
        }

        public Criteria andSbChipGreaterThanOrEqualTo(Integer value) {
            addCriterion("sb_chip >=", value, "sbChip");
            return (Criteria) this;
        }

        public Criteria andSbChipLessThan(Integer value) {
            addCriterion("sb_chip <", value, "sbChip");
            return (Criteria) this;
        }

        public Criteria andSbChipLessThanOrEqualTo(Integer value) {
            addCriterion("sb_chip <=", value, "sbChip");
            return (Criteria) this;
        }

        public Criteria andSbChipIn(List<Integer> values) {
            addCriterion("sb_chip in", values, "sbChip");
            return (Criteria) this;
        }

        public Criteria andSbChipNotIn(List<Integer> values) {
            addCriterion("sb_chip not in", values, "sbChip");
            return (Criteria) this;
        }

        public Criteria andSbChipBetween(Integer value1, Integer value2) {
            addCriterion("sb_chip between", value1, value2, "sbChip");
            return (Criteria) this;
        }

        public Criteria andSbChipNotBetween(Integer value1, Integer value2) {
            addCriterion("sb_chip not between", value1, value2, "sbChip");
            return (Criteria) this;
        }

        public Criteria andInChipIsNull() {
            addCriterion("in_chip is null");
            return (Criteria) this;
        }

        public Criteria andInChipIsNotNull() {
            addCriterion("in_chip is not null");
            return (Criteria) this;
        }

        public Criteria andInChipEqualTo(Integer value) {
            addCriterion("in_chip =", value, "inChip");
            return (Criteria) this;
        }

        public Criteria andInChipNotEqualTo(Integer value) {
            addCriterion("in_chip <>", value, "inChip");
            return (Criteria) this;
        }

        public Criteria andInChipGreaterThan(Integer value) {
            addCriterion("in_chip >", value, "inChip");
            return (Criteria) this;
        }

        public Criteria andInChipGreaterThanOrEqualTo(Integer value) {
            addCriterion("in_chip >=", value, "inChip");
            return (Criteria) this;
        }

        public Criteria andInChipLessThan(Integer value) {
            addCriterion("in_chip <", value, "inChip");
            return (Criteria) this;
        }

        public Criteria andInChipLessThanOrEqualTo(Integer value) {
            addCriterion("in_chip <=", value, "inChip");
            return (Criteria) this;
        }

        public Criteria andInChipIn(List<Integer> values) {
            addCriterion("in_chip in", values, "inChip");
            return (Criteria) this;
        }

        public Criteria andInChipNotIn(List<Integer> values) {
            addCriterion("in_chip not in", values, "inChip");
            return (Criteria) this;
        }

        public Criteria andInChipBetween(Integer value1, Integer value2) {
            addCriterion("in_chip between", value1, value2, "inChip");
            return (Criteria) this;
        }

        public Criteria andInChipNotBetween(Integer value1, Integer value2) {
            addCriterion("in_chip not between", value1, value2, "inChip");
            return (Criteria) this;
        }

        public Criteria andMaxPlayTimeIsNull() {
            addCriterion("max_play_time is null");
            return (Criteria) this;
        }

        public Criteria andMaxPlayTimeIsNotNull() {
            addCriterion("max_play_time is not null");
            return (Criteria) this;
        }

        public Criteria andMaxPlayTimeEqualTo(Integer value) {
            addCriterion("max_play_time =", value, "maxPlayTime");
            return (Criteria) this;
        }

        public Criteria andMaxPlayTimeNotEqualTo(Integer value) {
            addCriterion("max_play_time <>", value, "maxPlayTime");
            return (Criteria) this;
        }

        public Criteria andMaxPlayTimeGreaterThan(Integer value) {
            addCriterion("max_play_time >", value, "maxPlayTime");
            return (Criteria) this;
        }

        public Criteria andMaxPlayTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("max_play_time >=", value, "maxPlayTime");
            return (Criteria) this;
        }

        public Criteria andMaxPlayTimeLessThan(Integer value) {
            addCriterion("max_play_time <", value, "maxPlayTime");
            return (Criteria) this;
        }

        public Criteria andMaxPlayTimeLessThanOrEqualTo(Integer value) {
            addCriterion("max_play_time <=", value, "maxPlayTime");
            return (Criteria) this;
        }

        public Criteria andMaxPlayTimeIn(List<Integer> values) {
            addCriterion("max_play_time in", values, "maxPlayTime");
            return (Criteria) this;
        }

        public Criteria andMaxPlayTimeNotIn(List<Integer> values) {
            addCriterion("max_play_time not in", values, "maxPlayTime");
            return (Criteria) this;
        }

        public Criteria andMaxPlayTimeBetween(Integer value1, Integer value2) {
            addCriterion("max_play_time between", value1, value2, "maxPlayTime");
            return (Criteria) this;
        }

        public Criteria andMaxPlayTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("max_play_time not between", value1, value2, "maxPlayTime");
            return (Criteria) this;
        }

        public Criteria andPlayerCountIsNull() {
            addCriterion("player_count is null");
            return (Criteria) this;
        }

        public Criteria andPlayerCountIsNotNull() {
            addCriterion("player_count is not null");
            return (Criteria) this;
        }

        public Criteria andPlayerCountEqualTo(Byte value) {
            addCriterion("player_count =", value, "playerCount");
            return (Criteria) this;
        }

        public Criteria andPlayerCountNotEqualTo(Byte value) {
            addCriterion("player_count <>", value, "playerCount");
            return (Criteria) this;
        }

        public Criteria andPlayerCountGreaterThan(Byte value) {
            addCriterion("player_count >", value, "playerCount");
            return (Criteria) this;
        }

        public Criteria andPlayerCountGreaterThanOrEqualTo(Byte value) {
            addCriterion("player_count >=", value, "playerCount");
            return (Criteria) this;
        }

        public Criteria andPlayerCountLessThan(Byte value) {
            addCriterion("player_count <", value, "playerCount");
            return (Criteria) this;
        }

        public Criteria andPlayerCountLessThanOrEqualTo(Byte value) {
            addCriterion("player_count <=", value, "playerCount");
            return (Criteria) this;
        }

        public Criteria andPlayerCountIn(List<Byte> values) {
            addCriterion("player_count in", values, "playerCount");
            return (Criteria) this;
        }

        public Criteria andPlayerCountNotIn(List<Byte> values) {
            addCriterion("player_count not in", values, "playerCount");
            return (Criteria) this;
        }

        public Criteria andPlayerCountBetween(Byte value1, Byte value2) {
            addCriterion("player_count between", value1, value2, "playerCount");
            return (Criteria) this;
        }

        public Criteria andPlayerCountNotBetween(Byte value1, Byte value2) {
            addCriterion("player_count not between", value1, value2, "playerCount");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(Integer value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(Integer value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(Integer value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(Integer value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(Integer value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<Integer> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<Integer> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(Integer value1, Integer value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(Integer value1, Integer value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Integer value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Integer value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Integer value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Integer value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Integer value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Integer> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Integer> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Integer value1, Integer value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(LocalDateTime value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(LocalDateTime value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(LocalDateTime value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(LocalDateTime value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<LocalDateTime> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<LocalDateTime> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andMinRateIsNull() {
            addCriterion("min_rate is null");
            return (Criteria) this;
        }

        public Criteria andMinRateIsNotNull() {
            addCriterion("min_rate is not null");
            return (Criteria) this;
        }

        public Criteria andMinRateEqualTo(Integer value) {
            addCriterion("min_rate =", value, "minRate");
            return (Criteria) this;
        }

        public Criteria andMinRateNotEqualTo(Integer value) {
            addCriterion("min_rate <>", value, "minRate");
            return (Criteria) this;
        }

        public Criteria andMinRateGreaterThan(Integer value) {
            addCriterion("min_rate >", value, "minRate");
            return (Criteria) this;
        }

        public Criteria andMinRateGreaterThanOrEqualTo(Integer value) {
            addCriterion("min_rate >=", value, "minRate");
            return (Criteria) this;
        }

        public Criteria andMinRateLessThan(Integer value) {
            addCriterion("min_rate <", value, "minRate");
            return (Criteria) this;
        }

        public Criteria andMinRateLessThanOrEqualTo(Integer value) {
            addCriterion("min_rate <=", value, "minRate");
            return (Criteria) this;
        }

        public Criteria andMinRateIn(List<Integer> values) {
            addCriterion("min_rate in", values, "minRate");
            return (Criteria) this;
        }

        public Criteria andMinRateNotIn(List<Integer> values) {
            addCriterion("min_rate not in", values, "minRate");
            return (Criteria) this;
        }

        public Criteria andMinRateBetween(Integer value1, Integer value2) {
            addCriterion("min_rate between", value1, value2, "minRate");
            return (Criteria) this;
        }

        public Criteria andMinRateNotBetween(Integer value1, Integer value2) {
            addCriterion("min_rate not between", value1, value2, "minRate");
            return (Criteria) this;
        }

        public Criteria andMaxRateIsNull() {
            addCriterion("max_rate is null");
            return (Criteria) this;
        }

        public Criteria andMaxRateIsNotNull() {
            addCriterion("max_rate is not null");
            return (Criteria) this;
        }

        public Criteria andMaxRateEqualTo(Integer value) {
            addCriterion("max_rate =", value, "maxRate");
            return (Criteria) this;
        }

        public Criteria andMaxRateNotEqualTo(Integer value) {
            addCriterion("max_rate <>", value, "maxRate");
            return (Criteria) this;
        }

        public Criteria andMaxRateGreaterThan(Integer value) {
            addCriterion("max_rate >", value, "maxRate");
            return (Criteria) this;
        }

        public Criteria andMaxRateGreaterThanOrEqualTo(Integer value) {
            addCriterion("max_rate >=", value, "maxRate");
            return (Criteria) this;
        }

        public Criteria andMaxRateLessThan(Integer value) {
            addCriterion("max_rate <", value, "maxRate");
            return (Criteria) this;
        }

        public Criteria andMaxRateLessThanOrEqualTo(Integer value) {
            addCriterion("max_rate <=", value, "maxRate");
            return (Criteria) this;
        }

        public Criteria andMaxRateIn(List<Integer> values) {
            addCriterion("max_rate in", values, "maxRate");
            return (Criteria) this;
        }

        public Criteria andMaxRateNotIn(List<Integer> values) {
            addCriterion("max_rate not in", values, "maxRate");
            return (Criteria) this;
        }

        public Criteria andMaxRateBetween(Integer value1, Integer value2) {
            addCriterion("max_rate between", value1, value2, "maxRate");
            return (Criteria) this;
        }

        public Criteria andMaxRateNotBetween(Integer value1, Integer value2) {
            addCriterion("max_rate not between", value1, value2, "maxRate");
            return (Criteria) this;
        }

        public Criteria andPauseIsNull() {
            addCriterion("pause is null");
            return (Criteria) this;
        }

        public Criteria andPauseIsNotNull() {
            addCriterion("pause is not null");
            return (Criteria) this;
        }

        public Criteria andPauseEqualTo(Byte value) {
            addCriterion("pause =", value, "pause");
            return (Criteria) this;
        }

        public Criteria andPauseNotEqualTo(Byte value) {
            addCriterion("pause <>", value, "pause");
            return (Criteria) this;
        }

        public Criteria andPauseGreaterThan(Byte value) {
            addCriterion("pause >", value, "pause");
            return (Criteria) this;
        }

        public Criteria andPauseGreaterThanOrEqualTo(Byte value) {
            addCriterion("pause >=", value, "pause");
            return (Criteria) this;
        }

        public Criteria andPauseLessThan(Byte value) {
            addCriterion("pause <", value, "pause");
            return (Criteria) this;
        }

        public Criteria andPauseLessThanOrEqualTo(Byte value) {
            addCriterion("pause <=", value, "pause");
            return (Criteria) this;
        }

        public Criteria andPauseIn(List<Byte> values) {
            addCriterion("pause in", values, "pause");
            return (Criteria) this;
        }

        public Criteria andPauseNotIn(List<Byte> values) {
            addCriterion("pause not in", values, "pause");
            return (Criteria) this;
        }

        public Criteria andPauseBetween(Byte value1, Byte value2) {
            addCriterion("pause between", value1, value2, "pause");
            return (Criteria) this;
        }

        public Criteria andPauseNotBetween(Byte value1, Byte value2) {
            addCriterion("pause not between", value1, value2, "pause");
            return (Criteria) this;
        }

        public Criteria andIdouIsNull() {
            addCriterion("idou is null");
            return (Criteria) this;
        }

        public Criteria andIdouIsNotNull() {
            addCriterion("idou is not null");
            return (Criteria) this;
        }

        public Criteria andIdouEqualTo(Integer value) {
            addCriterion("idou =", value, "idou");
            return (Criteria) this;
        }

        public Criteria andIdouNotEqualTo(Integer value) {
            addCriterion("idou <>", value, "idou");
            return (Criteria) this;
        }

        public Criteria andIdouGreaterThan(Integer value) {
            addCriterion("idou >", value, "idou");
            return (Criteria) this;
        }

        public Criteria andIdouGreaterThanOrEqualTo(Integer value) {
            addCriterion("idou >=", value, "idou");
            return (Criteria) this;
        }

        public Criteria andIdouLessThan(Integer value) {
            addCriterion("idou <", value, "idou");
            return (Criteria) this;
        }

        public Criteria andIdouLessThanOrEqualTo(Integer value) {
            addCriterion("idou <=", value, "idou");
            return (Criteria) this;
        }

        public Criteria andIdouIn(List<Integer> values) {
            addCriterion("idou in", values, "idou");
            return (Criteria) this;
        }

        public Criteria andIdouNotIn(List<Integer> values) {
            addCriterion("idou not in", values, "idou");
            return (Criteria) this;
        }

        public Criteria andIdouBetween(Integer value1, Integer value2) {
            addCriterion("idou between", value1, value2, "idou");
            return (Criteria) this;
        }

        public Criteria andIdouNotBetween(Integer value1, Integer value2) {
            addCriterion("idou not between", value1, value2, "idou");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNull() {
            addCriterion("room_id is null");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNotNull() {
            addCriterion("room_id is not null");
            return (Criteria) this;
        }

        public Criteria andRoomIdEqualTo(Integer value) {
            addCriterion("room_id =", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotEqualTo(Integer value) {
            addCriterion("room_id <>", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThan(Integer value) {
            addCriterion("room_id >", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("room_id >=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThan(Integer value) {
            addCriterion("room_id <", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThanOrEqualTo(Integer value) {
            addCriterion("room_id <=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdIn(List<Integer> values) {
            addCriterion("room_id in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotIn(List<Integer> values) {
            addCriterion("room_id not in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdBetween(Integer value1, Integer value2) {
            addCriterion("room_id between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotBetween(Integer value1, Integer value2) {
            addCriterion("room_id not between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andIpIsNull() {
            addCriterion("ip is null");
            return (Criteria) this;
        }

        public Criteria andIpIsNotNull() {
            addCriterion("ip is not null");
            return (Criteria) this;
        }

        public Criteria andIpEqualTo(Byte value) {
            addCriterion("ip =", value, "ip");
            return (Criteria) this;
        }

        public Criteria andIpNotEqualTo(Byte value) {
            addCriterion("ip <>", value, "ip");
            return (Criteria) this;
        }

        public Criteria andIpGreaterThan(Byte value) {
            addCriterion("ip >", value, "ip");
            return (Criteria) this;
        }

        public Criteria andIpGreaterThanOrEqualTo(Byte value) {
            addCriterion("ip >=", value, "ip");
            return (Criteria) this;
        }

        public Criteria andIpLessThan(Byte value) {
            addCriterion("ip <", value, "ip");
            return (Criteria) this;
        }

        public Criteria andIpLessThanOrEqualTo(Byte value) {
            addCriterion("ip <=", value, "ip");
            return (Criteria) this;
        }

        public Criteria andIpIn(List<Byte> values) {
            addCriterion("ip in", values, "ip");
            return (Criteria) this;
        }

        public Criteria andIpNotIn(List<Byte> values) {
            addCriterion("ip not in", values, "ip");
            return (Criteria) this;
        }

        public Criteria andIpBetween(Byte value1, Byte value2) {
            addCriterion("ip between", value1, value2, "ip");
            return (Criteria) this;
        }

        public Criteria andIpNotBetween(Byte value1, Byte value2) {
            addCriterion("ip not between", value1, value2, "ip");
            return (Criteria) this;
        }

        public Criteria andLimitGpsIsNull() {
            addCriterion("limit_gps is null");
            return (Criteria) this;
        }

        public Criteria andLimitGpsIsNotNull() {
            addCriterion("limit_gps is not null");
            return (Criteria) this;
        }

        public Criteria andLimitGpsEqualTo(Byte value) {
            addCriterion("limit_gps =", value, "limitGps");
            return (Criteria) this;
        }

        public Criteria andLimitGpsNotEqualTo(Byte value) {
            addCriterion("limit_gps <>", value, "limitGps");
            return (Criteria) this;
        }

        public Criteria andLimitGpsGreaterThan(Byte value) {
            addCriterion("limit_gps >", value, "limitGps");
            return (Criteria) this;
        }

        public Criteria andLimitGpsGreaterThanOrEqualTo(Byte value) {
            addCriterion("limit_gps >=", value, "limitGps");
            return (Criteria) this;
        }

        public Criteria andLimitGpsLessThan(Byte value) {
            addCriterion("limit_gps <", value, "limitGps");
            return (Criteria) this;
        }

        public Criteria andLimitGpsLessThanOrEqualTo(Byte value) {
            addCriterion("limit_gps <=", value, "limitGps");
            return (Criteria) this;
        }

        public Criteria andLimitGpsIn(List<Byte> values) {
            addCriterion("limit_gps in", values, "limitGps");
            return (Criteria) this;
        }

        public Criteria andLimitGpsNotIn(List<Byte> values) {
            addCriterion("limit_gps not in", values, "limitGps");
            return (Criteria) this;
        }

        public Criteria andLimitGpsBetween(Byte value1, Byte value2) {
            addCriterion("limit_gps between", value1, value2, "limitGps");
            return (Criteria) this;
        }

        public Criteria andLimitGpsNotBetween(Byte value1, Byte value2) {
            addCriterion("limit_gps not between", value1, value2, "limitGps");
            return (Criteria) this;
        }

        public Criteria andStraddleIsNull() {
            addCriterion("straddle is null");
            return (Criteria) this;
        }

        public Criteria andStraddleIsNotNull() {
            addCriterion("straddle is not null");
            return (Criteria) this;
        }

        public Criteria andStraddleEqualTo(Byte value) {
            addCriterion("straddle =", value, "straddle");
            return (Criteria) this;
        }

        public Criteria andStraddleNotEqualTo(Byte value) {
            addCriterion("straddle <>", value, "straddle");
            return (Criteria) this;
        }

        public Criteria andStraddleGreaterThan(Byte value) {
            addCriterion("straddle >", value, "straddle");
            return (Criteria) this;
        }

        public Criteria andStraddleGreaterThanOrEqualTo(Byte value) {
            addCriterion("straddle >=", value, "straddle");
            return (Criteria) this;
        }

        public Criteria andStraddleLessThan(Byte value) {
            addCriterion("straddle <", value, "straddle");
            return (Criteria) this;
        }

        public Criteria andStraddleLessThanOrEqualTo(Byte value) {
            addCriterion("straddle <=", value, "straddle");
            return (Criteria) this;
        }

        public Criteria andStraddleIn(List<Byte> values) {
            addCriterion("straddle in", values, "straddle");
            return (Criteria) this;
        }

        public Criteria andStraddleNotIn(List<Byte> values) {
            addCriterion("straddle not in", values, "straddle");
            return (Criteria) this;
        }

        public Criteria andStraddleBetween(Byte value1, Byte value2) {
            addCriterion("straddle between", value1, value2, "straddle");
            return (Criteria) this;
        }

        public Criteria andStraddleNotBetween(Byte value1, Byte value2) {
            addCriterion("straddle not between", value1, value2, "straddle");
            return (Criteria) this;
        }

        public Criteria andMinPlayTimeIsNull() {
            addCriterion("min_play_time is null");
            return (Criteria) this;
        }

        public Criteria andMinPlayTimeIsNotNull() {
            addCriterion("min_play_time is not null");
            return (Criteria) this;
        }

        public Criteria andMinPlayTimeEqualTo(Integer value) {
            addCriterion("min_play_time =", value, "minPlayTime");
            return (Criteria) this;
        }

        public Criteria andMinPlayTimeNotEqualTo(Integer value) {
            addCriterion("min_play_time <>", value, "minPlayTime");
            return (Criteria) this;
        }

        public Criteria andMinPlayTimeGreaterThan(Integer value) {
            addCriterion("min_play_time >", value, "minPlayTime");
            return (Criteria) this;
        }

        public Criteria andMinPlayTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("min_play_time >=", value, "minPlayTime");
            return (Criteria) this;
        }

        public Criteria andMinPlayTimeLessThan(Integer value) {
            addCriterion("min_play_time <", value, "minPlayTime");
            return (Criteria) this;
        }

        public Criteria andMinPlayTimeLessThanOrEqualTo(Integer value) {
            addCriterion("min_play_time <=", value, "minPlayTime");
            return (Criteria) this;
        }

        public Criteria andMinPlayTimeIn(List<Integer> values) {
            addCriterion("min_play_time in", values, "minPlayTime");
            return (Criteria) this;
        }

        public Criteria andMinPlayTimeNotIn(List<Integer> values) {
            addCriterion("min_play_time not in", values, "minPlayTime");
            return (Criteria) this;
        }

        public Criteria andMinPlayTimeBetween(Integer value1, Integer value2) {
            addCriterion("min_play_time between", value1, value2, "minPlayTime");
            return (Criteria) this;
        }

        public Criteria andMinPlayTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("min_play_time not between", value1, value2, "minPlayTime");
            return (Criteria) this;
        }

        public Criteria andRoomPathIsNull() {
            addCriterion("room_path is null");
            return (Criteria) this;
        }

        public Criteria andRoomPathIsNotNull() {
            addCriterion("room_path is not null");
            return (Criteria) this;
        }

        public Criteria andRoomPathEqualTo(Integer value) {
            addCriterion("room_path =", value, "roomPath");
            return (Criteria) this;
        }

        public Criteria andRoomPathNotEqualTo(Integer value) {
            addCriterion("room_path <>", value, "roomPath");
            return (Criteria) this;
        }

        public Criteria andRoomPathGreaterThan(Integer value) {
            addCriterion("room_path >", value, "roomPath");
            return (Criteria) this;
        }

        public Criteria andRoomPathGreaterThanOrEqualTo(Integer value) {
            addCriterion("room_path >=", value, "roomPath");
            return (Criteria) this;
        }

        public Criteria andRoomPathLessThan(Integer value) {
            addCriterion("room_path <", value, "roomPath");
            return (Criteria) this;
        }

        public Criteria andRoomPathLessThanOrEqualTo(Integer value) {
            addCriterion("room_path <=", value, "roomPath");
            return (Criteria) this;
        }

        public Criteria andRoomPathIn(List<Integer> values) {
            addCriterion("room_path in", values, "roomPath");
            return (Criteria) this;
        }

        public Criteria andRoomPathNotIn(List<Integer> values) {
            addCriterion("room_path not in", values, "roomPath");
            return (Criteria) this;
        }

        public Criteria andRoomPathBetween(Integer value1, Integer value2) {
            addCriterion("room_path between", value1, value2, "roomPath");
            return (Criteria) this;
        }

        public Criteria andRoomPathNotBetween(Integer value1, Integer value2) {
            addCriterion("room_path not between", value1, value2, "roomPath");
            return (Criteria) this;
        }

        public Criteria andRoomTypeIsNull() {
            addCriterion("room_type is null");
            return (Criteria) this;
        }

        public Criteria andRoomTypeIsNotNull() {
            addCriterion("room_type is not null");
            return (Criteria) this;
        }

        public Criteria andRoomTypeEqualTo(Integer value) {
            addCriterion("room_type =", value, "roomType");
            return (Criteria) this;
        }

        public Criteria andRoomTypeNotEqualTo(Integer value) {
            addCriterion("room_type <>", value, "roomType");
            return (Criteria) this;
        }

        public Criteria andRoomTypeGreaterThan(Integer value) {
            addCriterion("room_type >", value, "roomType");
            return (Criteria) this;
        }

        public Criteria andRoomTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("room_type >=", value, "roomType");
            return (Criteria) this;
        }

        public Criteria andRoomTypeLessThan(Integer value) {
            addCriterion("room_type <", value, "roomType");
            return (Criteria) this;
        }

        public Criteria andRoomTypeLessThanOrEqualTo(Integer value) {
            addCriterion("room_type <=", value, "roomType");
            return (Criteria) this;
        }

        public Criteria andRoomTypeIn(List<Integer> values) {
            addCriterion("room_type in", values, "roomType");
            return (Criteria) this;
        }

        public Criteria andRoomTypeNotIn(List<Integer> values) {
            addCriterion("room_type not in", values, "roomType");
            return (Criteria) this;
        }

        public Criteria andRoomTypeBetween(Integer value1, Integer value2) {
            addCriterion("room_type between", value1, value2, "roomType");
            return (Criteria) this;
        }

        public Criteria andRoomTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("room_type not between", value1, value2, "roomType");
            return (Criteria) this;
        }

        public Criteria andMuckSwitchIsNull() {
            addCriterion("muck_switch is null");
            return (Criteria) this;
        }

        public Criteria andMuckSwitchIsNotNull() {
            addCriterion("muck_switch is not null");
            return (Criteria) this;
        }

        public Criteria andMuckSwitchEqualTo(Integer value) {
            addCriterion("muck_switch =", value, "muckSwitch");
            return (Criteria) this;
        }

        public Criteria andMuckSwitchNotEqualTo(Integer value) {
            addCriterion("muck_switch <>", value, "muckSwitch");
            return (Criteria) this;
        }

        public Criteria andMuckSwitchGreaterThan(Integer value) {
            addCriterion("muck_switch >", value, "muckSwitch");
            return (Criteria) this;
        }

        public Criteria andMuckSwitchGreaterThanOrEqualTo(Integer value) {
            addCriterion("muck_switch >=", value, "muckSwitch");
            return (Criteria) this;
        }

        public Criteria andMuckSwitchLessThan(Integer value) {
            addCriterion("muck_switch <", value, "muckSwitch");
            return (Criteria) this;
        }

        public Criteria andMuckSwitchLessThanOrEqualTo(Integer value) {
            addCriterion("muck_switch <=", value, "muckSwitch");
            return (Criteria) this;
        }

        public Criteria andMuckSwitchIn(List<Integer> values) {
            addCriterion("muck_switch in", values, "muckSwitch");
            return (Criteria) this;
        }

        public Criteria andMuckSwitchNotIn(List<Integer> values) {
            addCriterion("muck_switch not in", values, "muckSwitch");
            return (Criteria) this;
        }

        public Criteria andMuckSwitchBetween(Integer value1, Integer value2) {
            addCriterion("muck_switch between", value1, value2, "muckSwitch");
            return (Criteria) this;
        }

        public Criteria andMuckSwitchNotBetween(Integer value1, Integer value2) {
            addCriterion("muck_switch not between", value1, value2, "muckSwitch");
            return (Criteria) this;
        }

        public Criteria andJackpotOnIsNull() {
            addCriterion("jackpot_on is null");
            return (Criteria) this;
        }

        public Criteria andJackpotOnIsNotNull() {
            addCriterion("jackpot_on is not null");
            return (Criteria) this;
        }

        public Criteria andJackpotOnEqualTo(Boolean value) {
            addCriterion("jackpot_on =", value, "jackpotOn");
            return (Criteria) this;
        }

        public Criteria andJackpotOnNotEqualTo(Boolean value) {
            addCriterion("jackpot_on <>", value, "jackpotOn");
            return (Criteria) this;
        }

        public Criteria andJackpotOnGreaterThan(Boolean value) {
            addCriterion("jackpot_on >", value, "jackpotOn");
            return (Criteria) this;
        }

        public Criteria andJackpotOnGreaterThanOrEqualTo(Boolean value) {
            addCriterion("jackpot_on >=", value, "jackpotOn");
            return (Criteria) this;
        }

        public Criteria andJackpotOnLessThan(Boolean value) {
            addCriterion("jackpot_on <", value, "jackpotOn");
            return (Criteria) this;
        }

        public Criteria andJackpotOnLessThanOrEqualTo(Boolean value) {
            addCriterion("jackpot_on <=", value, "jackpotOn");
            return (Criteria) this;
        }

        public Criteria andJackpotOnIn(List<Boolean> values) {
            addCriterion("jackpot_on in", values, "jackpotOn");
            return (Criteria) this;
        }

        public Criteria andJackpotOnNotIn(List<Boolean> values) {
            addCriterion("jackpot_on not in", values, "jackpotOn");
            return (Criteria) this;
        }

        public Criteria andJackpotOnBetween(Boolean value1, Boolean value2) {
            addCriterion("jackpot_on between", value1, value2, "jackpotOn");
            return (Criteria) this;
        }

        public Criteria andJackpotOnNotBetween(Boolean value1, Boolean value2) {
            addCriterion("jackpot_on not between", value1, value2, "jackpotOn");
            return (Criteria) this;
        }

        public Criteria andRoomModeIsNull() {
            addCriterion("room_mode is null");
            return (Criteria) this;
        }

        public Criteria andRoomModeIsNotNull() {
            addCriterion("room_mode is not null");
            return (Criteria) this;
        }

        public Criteria andRoomModeEqualTo(Integer value) {
            addCriterion("room_mode =", value, "roomMode");
            return (Criteria) this;
        }

        public Criteria andRoomModeNotEqualTo(Integer value) {
            addCriterion("room_mode <>", value, "roomMode");
            return (Criteria) this;
        }

        public Criteria andRoomModeGreaterThan(Integer value) {
            addCriterion("room_mode >", value, "roomMode");
            return (Criteria) this;
        }

        public Criteria andRoomModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("room_mode >=", value, "roomMode");
            return (Criteria) this;
        }

        public Criteria andRoomModeLessThan(Integer value) {
            addCriterion("room_mode <", value, "roomMode");
            return (Criteria) this;
        }

        public Criteria andRoomModeLessThanOrEqualTo(Integer value) {
            addCriterion("room_mode <=", value, "roomMode");
            return (Criteria) this;
        }

        public Criteria andRoomModeIn(List<Integer> values) {
            addCriterion("room_mode in", values, "roomMode");
            return (Criteria) this;
        }

        public Criteria andRoomModeNotIn(List<Integer> values) {
            addCriterion("room_mode not in", values, "roomMode");
            return (Criteria) this;
        }

        public Criteria andRoomModeBetween(Integer value1, Integer value2) {
            addCriterion("room_mode between", value1, value2, "roomMode");
            return (Criteria) this;
        }

        public Criteria andRoomModeNotBetween(Integer value1, Integer value2) {
            addCriterion("room_mode not between", value1, value2, "roomMode");
            return (Criteria) this;
        }

        public Criteria andLeaveTableIsNull() {
            addCriterion("leave_table is null");
            return (Criteria) this;
        }

        public Criteria andLeaveTableIsNotNull() {
            addCriterion("leave_table is not null");
            return (Criteria) this;
        }

        public Criteria andLeaveTableEqualTo(Integer value) {
            addCriterion("leave_table =", value, "leaveTable");
            return (Criteria) this;
        }

        public Criteria andLeaveTableNotEqualTo(Integer value) {
            addCriterion("leave_table <>", value, "leaveTable");
            return (Criteria) this;
        }

        public Criteria andLeaveTableGreaterThan(Integer value) {
            addCriterion("leave_table >", value, "leaveTable");
            return (Criteria) this;
        }

        public Criteria andLeaveTableGreaterThanOrEqualTo(Integer value) {
            addCriterion("leave_table >=", value, "leaveTable");
            return (Criteria) this;
        }

        public Criteria andLeaveTableLessThan(Integer value) {
            addCriterion("leave_table <", value, "leaveTable");
            return (Criteria) this;
        }

        public Criteria andLeaveTableLessThanOrEqualTo(Integer value) {
            addCriterion("leave_table <=", value, "leaveTable");
            return (Criteria) this;
        }

        public Criteria andLeaveTableIn(List<Integer> values) {
            addCriterion("leave_table in", values, "leaveTable");
            return (Criteria) this;
        }

        public Criteria andLeaveTableNotIn(List<Integer> values) {
            addCriterion("leave_table not in", values, "leaveTable");
            return (Criteria) this;
        }

        public Criteria andLeaveTableBetween(Integer value1, Integer value2) {
            addCriterion("leave_table between", value1, value2, "leaveTable");
            return (Criteria) this;
        }

        public Criteria andLeaveTableNotBetween(Integer value1, Integer value2) {
            addCriterion("leave_table not between", value1, value2, "leaveTable");
            return (Criteria) this;
        }

        public Criteria andVpIsNull() {
            addCriterion("vp is null");
            return (Criteria) this;
        }

        public Criteria andVpIsNotNull() {
            addCriterion("vp is not null");
            return (Criteria) this;
        }

        public Criteria andVpEqualTo(Byte value) {
            addCriterion("vp =", value, "vp");
            return (Criteria) this;
        }

        public Criteria andVpNotEqualTo(Byte value) {
            addCriterion("vp <>", value, "vp");
            return (Criteria) this;
        }

        public Criteria andVpGreaterThan(Byte value) {
            addCriterion("vp >", value, "vp");
            return (Criteria) this;
        }

        public Criteria andVpGreaterThanOrEqualTo(Byte value) {
            addCriterion("vp >=", value, "vp");
            return (Criteria) this;
        }

        public Criteria andVpLessThan(Byte value) {
            addCriterion("vp <", value, "vp");
            return (Criteria) this;
        }

        public Criteria andVpLessThanOrEqualTo(Byte value) {
            addCriterion("vp <=", value, "vp");
            return (Criteria) this;
        }

        public Criteria andVpIn(List<Byte> values) {
            addCriterion("vp in", values, "vp");
            return (Criteria) this;
        }

        public Criteria andVpNotIn(List<Byte> values) {
            addCriterion("vp not in", values, "vp");
            return (Criteria) this;
        }

        public Criteria andVpBetween(Byte value1, Byte value2) {
            addCriterion("vp between", value1, value2, "vp");
            return (Criteria) this;
        }

        public Criteria andVpNotBetween(Byte value1, Byte value2) {
            addCriterion("vp not between", value1, value2, "vp");
            return (Criteria) this;
        }

        public Criteria andLogoUrlIsNull() {
            addCriterion("logo_url is null");
            return (Criteria) this;
        }

        public Criteria andLogoUrlIsNotNull() {
            addCriterion("logo_url is not null");
            return (Criteria) this;
        }

        public Criteria andLogoUrlEqualTo(String value) {
            addCriterion("logo_url =", value, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andLogoUrlNotEqualTo(String value) {
            addCriterion("logo_url <>", value, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andLogoUrlGreaterThan(String value) {
            addCriterion("logo_url >", value, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andLogoUrlGreaterThanOrEqualTo(String value) {
            addCriterion("logo_url >=", value, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andLogoUrlLessThan(String value) {
            addCriterion("logo_url <", value, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andLogoUrlLessThanOrEqualTo(String value) {
            addCriterion("logo_url <=", value, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andLogoUrlLike(String value) {
            addCriterion("logo_url like", value, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andLogoUrlNotLike(String value) {
            addCriterion("logo_url not like", value, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andLogoUrlIn(List<String> values) {
            addCriterion("logo_url in", values, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andLogoUrlNotIn(List<String> values) {
            addCriterion("logo_url not in", values, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andLogoUrlBetween(String value1, String value2) {
            addCriterion("logo_url between", value1, value2, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andLogoUrlNotBetween(String value1, String value2) {
            addCriterion("logo_url not between", value1, value2, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andOpTimeIsNull() {
            addCriterion("op_time is null");
            return (Criteria) this;
        }

        public Criteria andOpTimeIsNotNull() {
            addCriterion("op_time is not null");
            return (Criteria) this;
        }

        public Criteria andOpTimeEqualTo(Integer value) {
            addCriterion("op_time =", value, "opTime");
            return (Criteria) this;
        }

        public Criteria andOpTimeNotEqualTo(Integer value) {
            addCriterion("op_time <>", value, "opTime");
            return (Criteria) this;
        }

        public Criteria andOpTimeGreaterThan(Integer value) {
            addCriterion("op_time >", value, "opTime");
            return (Criteria) this;
        }

        public Criteria andOpTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("op_time >=", value, "opTime");
            return (Criteria) this;
        }

        public Criteria andOpTimeLessThan(Integer value) {
            addCriterion("op_time <", value, "opTime");
            return (Criteria) this;
        }

        public Criteria andOpTimeLessThanOrEqualTo(Integer value) {
            addCriterion("op_time <=", value, "opTime");
            return (Criteria) this;
        }

        public Criteria andOpTimeIn(List<Integer> values) {
            addCriterion("op_time in", values, "opTime");
            return (Criteria) this;
        }

        public Criteria andOpTimeNotIn(List<Integer> values) {
            addCriterion("op_time not in", values, "opTime");
            return (Criteria) this;
        }

        public Criteria andOpTimeBetween(Integer value1, Integer value2) {
            addCriterion("op_time between", value1, value2, "opTime");
            return (Criteria) this;
        }

        public Criteria andOpTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("op_time not between", value1, value2, "opTime");
            return (Criteria) this;
        }

        public Criteria andServerIdIsNull() {
            addCriterion("server_id is null");
            return (Criteria) this;
        }

        public Criteria andServerIdIsNotNull() {
            addCriterion("server_id is not null");
            return (Criteria) this;
        }

        public Criteria andServerIdEqualTo(String value) {
            addCriterion("server_id =", value, "serverId");
            return (Criteria) this;
        }

        public Criteria andServerIdNotEqualTo(String value) {
            addCriterion("server_id <>", value, "serverId");
            return (Criteria) this;
        }

        public Criteria andServerIdGreaterThan(String value) {
            addCriterion("server_id >", value, "serverId");
            return (Criteria) this;
        }

        public Criteria andServerIdGreaterThanOrEqualTo(String value) {
            addCriterion("server_id >=", value, "serverId");
            return (Criteria) this;
        }

        public Criteria andServerIdLessThan(String value) {
            addCriterion("server_id <", value, "serverId");
            return (Criteria) this;
        }

        public Criteria andServerIdLessThanOrEqualTo(String value) {
            addCriterion("server_id <=", value, "serverId");
            return (Criteria) this;
        }

        public Criteria andServerIdLike(String value) {
            addCriterion("server_id like", value, "serverId");
            return (Criteria) this;
        }

        public Criteria andServerIdNotLike(String value) {
            addCriterion("server_id not like", value, "serverId");
            return (Criteria) this;
        }

        public Criteria andServerIdIn(List<String> values) {
            addCriterion("server_id in", values, "serverId");
            return (Criteria) this;
        }

        public Criteria andServerIdNotIn(List<String> values) {
            addCriterion("server_id not in", values, "serverId");
            return (Criteria) this;
        }

        public Criteria andServerIdBetween(String value1, String value2) {
            addCriterion("server_id between", value1, value2, "serverId");
            return (Criteria) this;
        }

        public Criteria andServerIdNotBetween(String value1, String value2) {
            addCriterion("server_id not between", value1, value2, "serverId");
            return (Criteria) this;
        }

        public Criteria andAccessIpIsNull() {
            addCriterion("access_ip is null");
            return (Criteria) this;
        }

        public Criteria andAccessIpIsNotNull() {
            addCriterion("access_ip is not null");
            return (Criteria) this;
        }

        public Criteria andAccessIpEqualTo(String value) {
            addCriterion("access_ip =", value, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessIpNotEqualTo(String value) {
            addCriterion("access_ip <>", value, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessIpGreaterThan(String value) {
            addCriterion("access_ip >", value, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessIpGreaterThanOrEqualTo(String value) {
            addCriterion("access_ip >=", value, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessIpLessThan(String value) {
            addCriterion("access_ip <", value, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessIpLessThanOrEqualTo(String value) {
            addCriterion("access_ip <=", value, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessIpLike(String value) {
            addCriterion("access_ip like", value, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessIpNotLike(String value) {
            addCriterion("access_ip not like", value, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessIpIn(List<String> values) {
            addCriterion("access_ip in", values, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessIpNotIn(List<String> values) {
            addCriterion("access_ip not in", values, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessIpBetween(String value1, String value2) {
            addCriterion("access_ip between", value1, value2, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessIpNotBetween(String value1, String value2) {
            addCriterion("access_ip not between", value1, value2, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessPortIsNull() {
            addCriterion("access_port is null");
            return (Criteria) this;
        }

        public Criteria andAccessPortIsNotNull() {
            addCriterion("access_port is not null");
            return (Criteria) this;
        }

        public Criteria andAccessPortEqualTo(Integer value) {
            addCriterion("access_port =", value, "accessPort");
            return (Criteria) this;
        }

        public Criteria andAccessPortNotEqualTo(Integer value) {
            addCriterion("access_port <>", value, "accessPort");
            return (Criteria) this;
        }

        public Criteria andAccessPortGreaterThan(Integer value) {
            addCriterion("access_port >", value, "accessPort");
            return (Criteria) this;
        }

        public Criteria andAccessPortGreaterThanOrEqualTo(Integer value) {
            addCriterion("access_port >=", value, "accessPort");
            return (Criteria) this;
        }

        public Criteria andAccessPortLessThan(Integer value) {
            addCriterion("access_port <", value, "accessPort");
            return (Criteria) this;
        }

        public Criteria andAccessPortLessThanOrEqualTo(Integer value) {
            addCriterion("access_port <=", value, "accessPort");
            return (Criteria) this;
        }

        public Criteria andAccessPortIn(List<Integer> values) {
            addCriterion("access_port in", values, "accessPort");
            return (Criteria) this;
        }

        public Criteria andAccessPortNotIn(List<Integer> values) {
            addCriterion("access_port not in", values, "accessPort");
            return (Criteria) this;
        }

        public Criteria andAccessPortBetween(Integer value1, Integer value2) {
            addCriterion("access_port between", value1, value2, "accessPort");
            return (Criteria) this;
        }

        public Criteria andAccessPortNotBetween(Integer value1, Integer value2) {
            addCriterion("access_port not between", value1, value2, "accessPort");
            return (Criteria) this;
        }

        public Criteria andControlIsNull() {
            addCriterion("control is null");
            return (Criteria) this;
        }

        public Criteria andControlIsNotNull() {
            addCriterion("control is not null");
            return (Criteria) this;
        }

        public Criteria andControlEqualTo(Integer value) {
            addCriterion("control =", value, "control");
            return (Criteria) this;
        }

        public Criteria andControlNotEqualTo(Integer value) {
            addCriterion("control <>", value, "control");
            return (Criteria) this;
        }

        public Criteria andControlGreaterThan(Integer value) {
            addCriterion("control >", value, "control");
            return (Criteria) this;
        }

        public Criteria andControlGreaterThanOrEqualTo(Integer value) {
            addCriterion("control >=", value, "control");
            return (Criteria) this;
        }

        public Criteria andControlLessThan(Integer value) {
            addCriterion("control <", value, "control");
            return (Criteria) this;
        }

        public Criteria andControlLessThanOrEqualTo(Integer value) {
            addCriterion("control <=", value, "control");
            return (Criteria) this;
        }

        public Criteria andControlIn(List<Integer> values) {
            addCriterion("control in", values, "control");
            return (Criteria) this;
        }

        public Criteria andControlNotIn(List<Integer> values) {
            addCriterion("control not in", values, "control");
            return (Criteria) this;
        }

        public Criteria andControlBetween(Integer value1, Integer value2) {
            addCriterion("control between", value1, value2, "control");
            return (Criteria) this;
        }

        public Criteria andControlNotBetween(Integer value1, Integer value2) {
            addCriterion("control not between", value1, value2, "control");
            return (Criteria) this;
        }

        public Criteria andChargeIsNull() {
            addCriterion("charge is null");
            return (Criteria) this;
        }

        public Criteria andChargeIsNotNull() {
            addCriterion("charge is not null");
            return (Criteria) this;
        }

        public Criteria andChargeEqualTo(Integer value) {
            addCriterion("charge =", value, "charge");
            return (Criteria) this;
        }

        public Criteria andChargeNotEqualTo(Integer value) {
            addCriterion("charge <>", value, "charge");
            return (Criteria) this;
        }

        public Criteria andChargeGreaterThan(Integer value) {
            addCriterion("charge >", value, "charge");
            return (Criteria) this;
        }

        public Criteria andChargeGreaterThanOrEqualTo(Integer value) {
            addCriterion("charge >=", value, "charge");
            return (Criteria) this;
        }

        public Criteria andChargeLessThan(Integer value) {
            addCriterion("charge <", value, "charge");
            return (Criteria) this;
        }

        public Criteria andChargeLessThanOrEqualTo(Integer value) {
            addCriterion("charge <=", value, "charge");
            return (Criteria) this;
        }

        public Criteria andChargeIn(List<Integer> values) {
            addCriterion("charge in", values, "charge");
            return (Criteria) this;
        }

        public Criteria andChargeNotIn(List<Integer> values) {
            addCriterion("charge not in", values, "charge");
            return (Criteria) this;
        }

        public Criteria andChargeBetween(Integer value1, Integer value2) {
            addCriterion("charge between", value1, value2, "charge");
            return (Criteria) this;
        }

        public Criteria andChargeNotBetween(Integer value1, Integer value2) {
            addCriterion("charge not between", value1, value2, "charge");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("source is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("source is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(Integer value) {
            addCriterion("source =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(Integer value) {
            addCriterion("source <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(Integer value) {
            addCriterion("source >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("source >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(Integer value) {
            addCriterion("source <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(Integer value) {
            addCriterion("source <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<Integer> values) {
            addCriterion("source in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<Integer> values) {
            addCriterion("source not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(Integer value1, Integer value2) {
            addCriterion("source between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("source not between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andCommissionIsNull() {
            addCriterion("commission is null");
            return (Criteria) this;
        }

        public Criteria andCommissionIsNotNull() {
            addCriterion("commission is not null");
            return (Criteria) this;
        }

        public Criteria andCommissionEqualTo(Byte value) {
            addCriterion("commission =", value, "commission");
            return (Criteria) this;
        }

        public Criteria andCommissionNotEqualTo(Byte value) {
            addCriterion("commission <>", value, "commission");
            return (Criteria) this;
        }

        public Criteria andCommissionGreaterThan(Byte value) {
            addCriterion("commission >", value, "commission");
            return (Criteria) this;
        }

        public Criteria andCommissionGreaterThanOrEqualTo(Byte value) {
            addCriterion("commission >=", value, "commission");
            return (Criteria) this;
        }

        public Criteria andCommissionLessThan(Byte value) {
            addCriterion("commission <", value, "commission");
            return (Criteria) this;
        }

        public Criteria andCommissionLessThanOrEqualTo(Byte value) {
            addCriterion("commission <=", value, "commission");
            return (Criteria) this;
        }

        public Criteria andCommissionIn(List<Byte> values) {
            addCriterion("commission in", values, "commission");
            return (Criteria) this;
        }

        public Criteria andCommissionNotIn(List<Byte> values) {
            addCriterion("commission not in", values, "commission");
            return (Criteria) this;
        }

        public Criteria andCommissionBetween(Byte value1, Byte value2) {
            addCriterion("commission between", value1, value2, "commission");
            return (Criteria) this;
        }

        public Criteria andCommissionNotBetween(Byte value1, Byte value2) {
            addCriterion("commission not between", value1, value2, "commission");
            return (Criteria) this;
        }

        public Criteria andSourceIdIsNull() {
            addCriterion("source_id is null");
            return (Criteria) this;
        }

        public Criteria andSourceIdIsNotNull() {
            addCriterion("source_id is not null");
            return (Criteria) this;
        }

        public Criteria andSourceIdEqualTo(Integer value) {
            addCriterion("source_id =", value, "sourceId");
            return (Criteria) this;
        }

        public Criteria andSourceIdNotEqualTo(Integer value) {
            addCriterion("source_id <>", value, "sourceId");
            return (Criteria) this;
        }

        public Criteria andSourceIdGreaterThan(Integer value) {
            addCriterion("source_id >", value, "sourceId");
            return (Criteria) this;
        }

        public Criteria andSourceIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("source_id >=", value, "sourceId");
            return (Criteria) this;
        }

        public Criteria andSourceIdLessThan(Integer value) {
            addCriterion("source_id <", value, "sourceId");
            return (Criteria) this;
        }

        public Criteria andSourceIdLessThanOrEqualTo(Integer value) {
            addCriterion("source_id <=", value, "sourceId");
            return (Criteria) this;
        }

        public Criteria andSourceIdIn(List<Integer> values) {
            addCriterion("source_id in", values, "sourceId");
            return (Criteria) this;
        }

        public Criteria andSourceIdNotIn(List<Integer> values) {
            addCriterion("source_id not in", values, "sourceId");
            return (Criteria) this;
        }

        public Criteria andSourceIdBetween(Integer value1, Integer value2) {
            addCriterion("source_id between", value1, value2, "sourceId");
            return (Criteria) this;
        }

        public Criteria andSourceIdNotBetween(Integer value1, Integer value2) {
            addCriterion("source_id not between", value1, value2, "sourceId");
            return (Criteria) this;
        }

        public Criteria andTribeIdIsNull() {
            addCriterion("tribe_id is null");
            return (Criteria) this;
        }

        public Criteria andTribeIdIsNotNull() {
            addCriterion("tribe_id is not null");
            return (Criteria) this;
        }

        public Criteria andTribeIdEqualTo(Integer value) {
            addCriterion("tribe_id =", value, "tribeId");
            return (Criteria) this;
        }

        public Criteria andTribeIdNotEqualTo(Integer value) {
            addCriterion("tribe_id <>", value, "tribeId");
            return (Criteria) this;
        }

        public Criteria andTribeIdGreaterThan(Integer value) {
            addCriterion("tribe_id >", value, "tribeId");
            return (Criteria) this;
        }

        public Criteria andTribeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("tribe_id >=", value, "tribeId");
            return (Criteria) this;
        }

        public Criteria andTribeIdLessThan(Integer value) {
            addCriterion("tribe_id <", value, "tribeId");
            return (Criteria) this;
        }

        public Criteria andTribeIdLessThanOrEqualTo(Integer value) {
            addCriterion("tribe_id <=", value, "tribeId");
            return (Criteria) this;
        }

        public Criteria andTribeIdIn(List<Integer> values) {
            addCriterion("tribe_id in", values, "tribeId");
            return (Criteria) this;
        }

        public Criteria andTribeIdNotIn(List<Integer> values) {
            addCriterion("tribe_id not in", values, "tribeId");
            return (Criteria) this;
        }

        public Criteria andTribeIdBetween(Integer value1, Integer value2) {
            addCriterion("tribe_id between", value1, value2, "tribeId");
            return (Criteria) this;
        }

        public Criteria andTribeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("tribe_id not between", value1, value2, "tribeId");
            return (Criteria) this;
        }

        public Criteria andOrientationIsNull() {
            addCriterion("orientation is null");
            return (Criteria) this;
        }

        public Criteria andOrientationIsNotNull() {
            addCriterion("orientation is not null");
            return (Criteria) this;
        }

        public Criteria andOrientationEqualTo(Integer value) {
            addCriterion("orientation =", value, "orientation");
            return (Criteria) this;
        }

        public Criteria andOrientationNotEqualTo(Integer value) {
            addCriterion("orientation <>", value, "orientation");
            return (Criteria) this;
        }

        public Criteria andOrientationGreaterThan(Integer value) {
            addCriterion("orientation >", value, "orientation");
            return (Criteria) this;
        }

        public Criteria andOrientationGreaterThanOrEqualTo(Integer value) {
            addCriterion("orientation >=", value, "orientation");
            return (Criteria) this;
        }

        public Criteria andOrientationLessThan(Integer value) {
            addCriterion("orientation <", value, "orientation");
            return (Criteria) this;
        }

        public Criteria andOrientationLessThanOrEqualTo(Integer value) {
            addCriterion("orientation <=", value, "orientation");
            return (Criteria) this;
        }

        public Criteria andOrientationIn(List<Integer> values) {
            addCriterion("orientation in", values, "orientation");
            return (Criteria) this;
        }

        public Criteria andOrientationNotIn(List<Integer> values) {
            addCriterion("orientation not in", values, "orientation");
            return (Criteria) this;
        }

        public Criteria andOrientationBetween(Integer value1, Integer value2) {
            addCriterion("orientation between", value1, value2, "orientation");
            return (Criteria) this;
        }

        public Criteria andOrientationNotBetween(Integer value1, Integer value2) {
            addCriterion("orientation not between", value1, value2, "orientation");
            return (Criteria) this;
        }

        public Criteria andCreditControlIsNull() {
            addCriterion("credit_control is null");
            return (Criteria) this;
        }

        public Criteria andCreditControlIsNotNull() {
            addCriterion("credit_control is not null");
            return (Criteria) this;
        }

        public Criteria andCreditControlEqualTo(Integer value) {
            addCriterion("credit_control =", value, "creditControl");
            return (Criteria) this;
        }

        public Criteria andCreditControlNotEqualTo(Integer value) {
            addCriterion("credit_control <>", value, "creditControl");
            return (Criteria) this;
        }

        public Criteria andCreditControlGreaterThan(Integer value) {
            addCriterion("credit_control >", value, "creditControl");
            return (Criteria) this;
        }

        public Criteria andCreditControlGreaterThanOrEqualTo(Integer value) {
            addCriterion("credit_control >=", value, "creditControl");
            return (Criteria) this;
        }

        public Criteria andCreditControlLessThan(Integer value) {
            addCriterion("credit_control <", value, "creditControl");
            return (Criteria) this;
        }

        public Criteria andCreditControlLessThanOrEqualTo(Integer value) {
            addCriterion("credit_control <=", value, "creditControl");
            return (Criteria) this;
        }

        public Criteria andCreditControlIn(List<Integer> values) {
            addCriterion("credit_control in", values, "creditControl");
            return (Criteria) this;
        }

        public Criteria andCreditControlNotIn(List<Integer> values) {
            addCriterion("credit_control not in", values, "creditControl");
            return (Criteria) this;
        }

        public Criteria andCreditControlBetween(Integer value1, Integer value2) {
            addCriterion("credit_control between", value1, value2, "creditControl");
            return (Criteria) this;
        }

        public Criteria andCreditControlNotBetween(Integer value1, Integer value2) {
            addCriterion("credit_control not between", value1, value2, "creditControl");
            return (Criteria) this;
        }

        public Criteria andAutoStartIsNull() {
            addCriterion("auto_start is null");
            return (Criteria) this;
        }

        public Criteria andAutoStartIsNotNull() {
            addCriterion("auto_start is not null");
            return (Criteria) this;
        }

        public Criteria andAutoStartEqualTo(Byte value) {
            addCriterion("auto_start =", value, "autoStart");
            return (Criteria) this;
        }

        public Criteria andAutoStartNotEqualTo(Byte value) {
            addCriterion("auto_start <>", value, "autoStart");
            return (Criteria) this;
        }

        public Criteria andAutoStartGreaterThan(Byte value) {
            addCriterion("auto_start >", value, "autoStart");
            return (Criteria) this;
        }

        public Criteria andAutoStartGreaterThanOrEqualTo(Byte value) {
            addCriterion("auto_start >=", value, "autoStart");
            return (Criteria) this;
        }

        public Criteria andAutoStartLessThan(Byte value) {
            addCriterion("auto_start <", value, "autoStart");
            return (Criteria) this;
        }

        public Criteria andAutoStartLessThanOrEqualTo(Byte value) {
            addCriterion("auto_start <=", value, "autoStart");
            return (Criteria) this;
        }

        public Criteria andAutoStartIn(List<Byte> values) {
            addCriterion("auto_start in", values, "autoStart");
            return (Criteria) this;
        }

        public Criteria andAutoStartNotIn(List<Byte> values) {
            addCriterion("auto_start not in", values, "autoStart");
            return (Criteria) this;
        }

        public Criteria andAutoStartBetween(Byte value1, Byte value2) {
            addCriterion("auto_start between", value1, value2, "autoStart");
            return (Criteria) this;
        }

        public Criteria andAutoStartNotBetween(Byte value1, Byte value2) {
            addCriterion("auto_start not between", value1, value2, "autoStart");
            return (Criteria) this;
        }

        public Criteria andAutoPlayerCountIsNull() {
            addCriterion("auto_player_count is null");
            return (Criteria) this;
        }

        public Criteria andAutoPlayerCountIsNotNull() {
            addCriterion("auto_player_count is not null");
            return (Criteria) this;
        }

        public Criteria andAutoPlayerCountEqualTo(Integer value) {
            addCriterion("auto_player_count =", value, "autoPlayerCount");
            return (Criteria) this;
        }

        public Criteria andAutoPlayerCountNotEqualTo(Integer value) {
            addCriterion("auto_player_count <>", value, "autoPlayerCount");
            return (Criteria) this;
        }

        public Criteria andAutoPlayerCountGreaterThan(Integer value) {
            addCriterion("auto_player_count >", value, "autoPlayerCount");
            return (Criteria) this;
        }

        public Criteria andAutoPlayerCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("auto_player_count >=", value, "autoPlayerCount");
            return (Criteria) this;
        }

        public Criteria andAutoPlayerCountLessThan(Integer value) {
            addCriterion("auto_player_count <", value, "autoPlayerCount");
            return (Criteria) this;
        }

        public Criteria andAutoPlayerCountLessThanOrEqualTo(Integer value) {
            addCriterion("auto_player_count <=", value, "autoPlayerCount");
            return (Criteria) this;
        }

        public Criteria andAutoPlayerCountIn(List<Integer> values) {
            addCriterion("auto_player_count in", values, "autoPlayerCount");
            return (Criteria) this;
        }

        public Criteria andAutoPlayerCountNotIn(List<Integer> values) {
            addCriterion("auto_player_count not in", values, "autoPlayerCount");
            return (Criteria) this;
        }

        public Criteria andAutoPlayerCountBetween(Integer value1, Integer value2) {
            addCriterion("auto_player_count between", value1, value2, "autoPlayerCount");
            return (Criteria) this;
        }

        public Criteria andAutoPlayerCountNotBetween(Integer value1, Integer value2) {
            addCriterion("auto_player_count not between", value1, value2, "autoPlayerCount");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table group_room
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table group_room
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}