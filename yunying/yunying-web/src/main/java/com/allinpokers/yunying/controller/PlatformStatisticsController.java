package com.allinpokers.yunying.controller;

import com.allinpokers.yunying.entity.crazypoker.*;
import com.allinpokers.yunying.model.request.insurerReq.InsurerUserReq;
import com.allinpokers.yunying.model.request.platformReq.PlatformReq;
import com.allinpokers.yunying.model.request.statistics.PlatformStatisticsReq;
import com.allinpokers.yunying.model.request.userfee.UserFeeReq;
import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.mongodb.model.JPContributionTotal;
import com.allinpokers.yunying.mongodb.model.PlatformTotal;
import com.allinpokers.yunying.services.PlatformStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@Api(tags = "OSM报表模块")
@RestController
@RequestMapping("/data/statistics")
public class PlatformStatisticsController {

    @Autowired
    private PlatformStatisticsService platformStatisticsService;

    /**
     * 战绩流水返佣记录列表
     *
     * @return resp
     */
    @ApiOperation(value = "查询osm报表数据")
    @PostMapping("/query")
    public CommonRespon<PlatformTotal> queryPlatformStatistics(@RequestBody(required = false) PlatformStatisticsReq req) {
        return platformStatisticsService.queryPlatformStatistics(req);
    }

    @ApiOperation(value = "用户详情列表输赢积分")
    @PostMapping("/userOneIntegral")
    public CommonRespon<OneIntegraTotal> queryOneIntegral(@RequestBody(required = false) InsurerUserReq req) {
        return platformStatisticsService.queryUserIntegralOne(req);
    }


}
