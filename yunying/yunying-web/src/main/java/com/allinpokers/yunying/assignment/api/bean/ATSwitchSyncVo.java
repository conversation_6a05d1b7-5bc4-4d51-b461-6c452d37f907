package com.allinpokers.yunying.assignment.api.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ATSwitchSyncVo {
    @ApiModelProperty("自动模式开关，有效值：<br/>" +
                    " on  = 开启<br/>" +
                    " off = 关闭")
    private String statusOfAuto;

    @ApiModelProperty("手动模式开关，有效值：<br/>" +
                    " on  = 开启<br/>" +
                    " off = 关闭")
    private String statusOfManual;

    @ApiModelProperty("爆牌模式开关，有效值：<br/>" +
            " on  = 开启<br/>" +
            " off = 关闭")
    @JsonProperty("statusOfET")
    private String statusOfExplosionTable;
}
