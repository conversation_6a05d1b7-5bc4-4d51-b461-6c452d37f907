package com.allinpokers.yunying.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 提豆申请表  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WithdrawChip {
    /**
     * id
     */
    @ApiModelProperty("id")
    private Integer id;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private Integer uid;

    /**
     * 提豆单号
     */
    @ApiModelProperty("提豆单号")
    private String withdrawNo;

    /**
     * 提豆数量
     */
    @ApiModelProperty("提豆数量")
    private Integer chip;

    /**
     * 金额
     */
    @ApiModelProperty("金额")
    private Double amount;

    /**
     * 金豆数
     */
    @ApiModelProperty("金豆数")
    private Integer fee;
    /**
     * gas费
     */
    @ApiModelProperty("gas费")
    private Integer gas;
    /**
     * 战绩流水抵扣的金豆数
     */
    @ApiModelProperty("战绩流水抵扣的金豆数")
    private Integer plDeduction;

    /**
     * 状态(0-审核中,1-打款中,2-提现成功,3-提现失败,4-审核失败)
     */
    @ApiModelProperty("状态(0-审核中,1-打款中,2-提现成功,3-提现失败,4-审核失败)")
    private Integer status;

    /**
     * 交易方式(1-银行卡)
     */
    @ApiModelProperty("交易方式(1-银行卡)")
    private Integer transType;

    /**
     * sdkCode
     */
    @ApiModelProperty("sdkCode")
    private String paymentCode;

    /**
     * 收款账户信息
     */
    @ApiModelProperty("收款账户信息")
    private String payeeAccount;

    /**
     * 渠道id
     */
    @ApiModelProperty("渠道id")
    private Integer payChannelId;

    /**
     * cmsId
     */
    @ApiModelProperty("cmsId")
    private Integer payChannelCmsId;

    /**
     * 支付流水号
     */
    @ApiModelProperty("支付流水号")
    private String payNo;

    /**
     * 关联pay_channel_escrow_payment表id,用于获取sdk配置等
     */
    @ApiModelProperty("关联pay_channel_escrow_payment表id,用于获取sdk配置等")
    private Integer escrowPaymentId;

    /**
     * checkTime
     */
    @ApiModelProperty("checkTime")
    private LocalDateTime checkTime;

    /**
     * 第三方交易时间（回调）
     */
    @ApiModelProperty("第三方交易时间（回调）")
    private LocalDateTime tradeTime;

    /**
     * 审核人id
     */
    @ApiModelProperty("审核人id")
    private Integer checkId;

    /**
     * 审核来源：OSM=OSM系统用户，CMS=CMS系统用户
     */
    @ApiModelProperty("审核来源：OSM=OSM系统用户，CMS=CMS系统用户")
    private String checkType;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updatedTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createdTime;
}