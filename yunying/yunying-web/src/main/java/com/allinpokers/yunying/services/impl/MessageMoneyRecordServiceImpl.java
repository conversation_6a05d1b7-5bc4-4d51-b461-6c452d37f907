package com.allinpokers.yunying.services.impl;

import com.allinpokers.yunying.dao.crazypoker.MessageMoneyRecordDao;
import com.allinpokers.yunying.entity.crazypoker.MessageMoneyRecord;
import com.allinpokers.yunying.rabbitmq.client.MessageSender;
import com.allinpokers.yunying.rabbitmq.client.bean.MoneyMessage;
import com.allinpokers.yunying.rabbitmq.constant.EMessageChannelCode;
import com.allinpokers.yunying.rabbitmq.constant.EMessageCode;
import com.allinpokers.yunying.services.MessageMoneyRecordService;
import com.allinpokers.yunying.services.MessageUnreadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service("messageMoneyRecordService")
@Slf4j
public class MessageMoneyRecordServiceImpl implements MessageMoneyRecordService {
    @Resource
    private MessageSender messageSender;
    @Resource
    private MessageUnreadService messageUnreadService;
    @Resource
    private MessageMoneyRecordDao messageMoneyRecordDao;

    @Transactional(transactionManager = "crazyPokerTransactionManager", rollbackFor = RuntimeException.class)
    @Override
    public void sendMessageNotParams(EMessageCode messageCode, String senderId, Collection<String> receiverIds) {
        sendMessage(messageCode, senderId, receiverIds);
    }

    @Transactional(transactionManager = "crazyPokerTransactionManager", rollbackFor = RuntimeException.class)
    @Override
    public void sendMessage(EMessageCode messageCode, String senderId, Collection<String> receiverIds, String... params) {
        if (receiverIds.isEmpty()) {
            return;
        }
        String content = params.length > 0 ? params[0] : "";
        String remark = params.length > 1 ? params[1] : "";
        String title = params.length > 2 ? params[2] : "";
        List<MessageMoneyRecord> records = new ArrayList<>();
        MessageMoneyRecord record = null;
        for (String reciverId : receiverIds) {
            record = MessageMoneyRecord.builder()
                    .msgId(UUID.randomUUID().toString())
                    .senderId(senderId)
                    .reciverId(reciverId)
                    .header("-1")
                    .title(title)
                    .content(content)
                    .remark(remark)
                    .type(messageCode.getCode())
                    .msgStatus(0)
                    .createTime(LocalDateTime.now())
                    .build();
            records.add(record);
        }
        messageMoneyRecordDao.batchInsert(records);
        messageUnreadService.addUnread(record);

        //3. 推送消息到mq
        sendRabbitMq(record, EMessageChannelCode.TIM.getCode(), new ArrayList<>(receiverIds));
    }

    private void sendRabbitMq(MessageMoneyRecord record, int pushChannel, List<String> receiverIds) {
        MoneyMessage moneyMessage = MoneyMessage.builder()
                .senderId(record.getSenderId())
                .reciverUserIds(receiverIds)
                .header(record.getHeader())
                .title(record.getTitle())
                .content(record.getContent())
                .remark(record.getRemark())
                .type(record.getType())
                .msgStatus(record.getMsgStatus())
                .pushChannel(pushChannel)
                .build();
        String msgId = messageSender.sendMoneyMessage(moneyMessage);
        log.info("send money message to rabbitmq, record.msgId={}, msgId={}", record.getMsgId(), msgId);
    }
}
