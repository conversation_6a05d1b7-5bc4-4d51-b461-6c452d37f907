package com.allinpokers.yunying.model.request.user;

import com.allinpokers.yunying.permission.security.UserInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MsgReceiveUserAddReq {
    @ApiModelProperty("用户id--显性id")
    private String userId;

    /**
     * 操作的用户
     */
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private UserInfo operatorUser;
}
