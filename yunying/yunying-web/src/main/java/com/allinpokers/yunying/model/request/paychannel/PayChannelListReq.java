package com.allinpokers.yunying.model.request.paychannel;

import com.allinpokers.yunying.model.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel(value = "支付渠道列表查询")
@Data
public class PayChannelListReq extends PageReq {

    @ApiModelProperty(value = "查询搜索内容")
    private String search;
}
