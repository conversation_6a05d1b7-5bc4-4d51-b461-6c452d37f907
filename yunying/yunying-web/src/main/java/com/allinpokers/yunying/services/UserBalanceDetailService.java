package com.allinpokers.yunying.services;


import com.allinpokers.yunying.model.request.balance.UserBalanceDetailQuery;
import com.allinpokers.yunying.model.response.balance.UserBalanceDetail;
import com.github.pagehelper.PageInfo;

import javax.servlet.http.HttpServletResponse;

/**
 * UserBalanceDetailService
 *
 * <AUTHOR>
 * @date 2024/12/13
 */
public interface UserBalanceDetailService {

    interface BalanceType {
        int DIAMOND = 1;
        int TRIBE = 2;
        int GOLD = 3;
    }


    /**
     * 查询用户余额明细
     * @param queryParams
     * @return
     */
    PageInfo<UserBalanceDetail> findUserBalanceDetail(UserBalanceDetailQuery queryParams);


    /**
     * 导出用户余额明细
     * @param queryParams
     * @param response
     */
    void export(UserBalanceDetailQuery queryParams, HttpServletResponse response);

}
