package com.allinpokers.yunying.rabbitmq.client.bean.eventtrack;


import lombok.*;

import java.io.Serializable;

@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class UserDataMessage implements Serializable {

    /**
     * 消息类型
     * 1. 带入消息
     * 2. 成功坐下消息
     * 3. 成功离开座位消息
     * 4. 取消托管消息
     * 5. 托管消息
     * 6. 牌局结算消息
     * 7. 玩家账户金豆变动消息
     * 8. 赠送玩家消息
     * 9. 玩家登录设备信息更新
     * 10. 用户名单/分值
     * 11. 玩家绑定/解绑银行卡
     */
    private Integer msgType;

    private String message;  //消息体
}
