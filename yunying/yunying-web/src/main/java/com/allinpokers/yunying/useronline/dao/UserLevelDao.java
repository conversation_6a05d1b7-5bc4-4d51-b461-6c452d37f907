package com.allinpokers.yunying.useronline.dao;

import com.allinpokers.yunying.dao.BaseDao;
import com.allinpokers.yunying.useronline.entity.UserLevel;
import com.allinpokers.yunying.useronline.entity.example.UserLevelExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户层级名单  Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserLevelDao extends BaseDao<UserLevel, UserLevelExample, Integer> {

	/**
	 * 插入或更新
	 *
	 * @param userId
	 * @param levelCode
	 * @param operatorId
	 * @return
	 */
	int insertOrUpdate(@Param("userId") Integer userId, @Param("levelCode") Integer levelCode, @Param("operatorId") Integer operatorId);
}