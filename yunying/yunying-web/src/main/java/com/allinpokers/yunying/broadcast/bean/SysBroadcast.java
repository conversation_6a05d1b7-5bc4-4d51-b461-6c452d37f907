package com.allinpokers.yunying.broadcast.bean;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * SysBroadcast
 *
 * <AUTHOR>
 * @date 2025/2/12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SysBroadcast {

    // id
    private Long id;

    // 广播方式:0-按次数广播,1-按时间段广播
    private Integer broadcastMode;

    // 广播内容
    private String broadcastContent;

    // 广播次数
    private Integer broadcastCount;

    // 广播间隔
    private Integer broadcastInterval;

    // 广播时间
    private Timestamp broadcastTime;

    // 广播开始时间
    private Timestamp broadcastStartTime;

    // 广播结束时间
    private Timestamp broadcastEndTime;

    // 广播范围:0-全服广播,1-牌局广播
    private Integer broadcastRange;

    // 广播牌局类型,61-德州扑克
    private String broadcastGameType;

    // 广播状态：0-未开始,1-进行中,2-已结束,3-失败
    private Integer broadcastStatus;

    // 创建时间
    private Timestamp createdAt;

    // 创建人
    private Long createdBy;

    // 创建人
    private String createdByUsername;

}
