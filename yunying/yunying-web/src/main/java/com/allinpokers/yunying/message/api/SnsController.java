package com.allinpokers.yunying.message.api;

import com.allinpokers.yunying.rabbitmq.client.MessageSender;
import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.message.api.req.SnsReq;
import com.allinpokers.yunying.entity.crazypoker.UserDetailsInfo;
import com.allinpokers.yunying.services.UserDetailsInfoService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.allinpokers.yunying.rabbitmq.constant.EMessageCode;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

@RestController
@Slf4j
@RequestMapping(value = "/sns/", method = RequestMethod.POST)
@Api(tags = {"sns模块"})
public class SnsController {
    @Resource
    private UserDetailsInfoService userDetailsInfoService;

    @Autowired
    private MessageSender messageSender;

    /**
     * 發送sns給全部玩家
     *
     * @return
     */
    @ApiOperation(value = "發送sns給全部玩家",
            notes = "發送sns給全部玩家")
    @RequestMapping(value = "sendSns", method = RequestMethod.POST)
    public CommonRespon sendSns(@Valid @RequestBody SnsReq req) {

        Map<String,Object> data = new HashMap<>();

        String title = req.getTitle();
        String message = req.getMessage();
        String randomId = req.getUserId();
        Integer userId = 0;
        if(randomId != null && !randomId.isEmpty()){
            UserDetailsInfo user = userDetailsInfoService.findUserByUserRamdomId(randomId);
            userId = user.getUserId();
        }

        data.put("type",EMessageCode.SNS_CUSTOM.getCode());
        data.put("userId",userId);
        data.put("title",title);
        data.put("message",message);

        messageSender.sendSns(data);

        return CommonRespon.success();
    }

}
