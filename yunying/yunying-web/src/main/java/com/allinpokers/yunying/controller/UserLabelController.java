package com.allinpokers.yunying.controller;

import com.allinpokers.yunying.entity.crazypoker.UserLabelConfig;
import com.allinpokers.yunying.model.request.PageReq;
import com.allinpokers.yunying.model.request.userlabel.AddUserLabelConfigReq;
import com.allinpokers.yunying.model.request.userlabel.AddUserLabelReq;
import com.allinpokers.yunying.model.request.userlabel.DeleteUserLabelConfigReq;
import com.allinpokers.yunying.model.request.userlabel.UpdateUserLabelConfigReq;
import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.services.UserLabelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "用户标签")
@RestController
@RequestMapping("/user/label")
public class UserLabelController {
	@Resource
	private UserLabelService userLabelService;

	@ApiOperation(value = "标签列表")
	@PostMapping("/listConfigs")
	public CommonRespon<List<UserLabelConfig>> listConfigs() {
		List<UserLabelConfig> page = userLabelService.listConfigs();
		return CommonRespon.success(page);
	}

	@ApiOperation(value = "新增标签")
	@PostMapping("/addConfig")
	public CommonRespon addConfig(@Valid @RequestBody AddUserLabelConfigReq req) {
		return userLabelService.addConfig(req.getName());
	}

	@ApiOperation(value = "更新标签名称")
	@PostMapping("/updateConfig")
	public CommonRespon updateConfig(@Valid @RequestBody UpdateUserLabelConfigReq req) {
		return userLabelService.updateConfig(req.getId(), req.getName());
	}

	@ApiOperation(value = "删除标签配置")
	@PostMapping("/deleteConfig")
	public CommonRespon deleteConfig(@RequestBody DeleteUserLabelConfigReq req) {
		userLabelService.deleteConfig(req.getIdList());
		return CommonRespon.success();
	}

	@ApiOperation(value = "给用户添加标签")
	@PostMapping("/add")
	public CommonRespon addLabel(@RequestBody AddUserLabelReq req) {
		userLabelService.addLabel(req.getUserId(), req.getLabelConfigId());
		return CommonRespon.success();
	}

	@ApiOperation(value = "给用户取消标签")
	@PostMapping("/delete")
	public CommonRespon deleteLabel(@RequestBody AddUserLabelReq req) {
		userLabelService.deleteLabel(req.getUserId(), req.getLabelConfigId());
		return CommonRespon.success();
	}
}
