package com.allinpokers.yunying.util;

import java.util.Calendar;
import java.util.Date;

public class DateUtils {
    /**
     * 根据当前系统时间获取指定偏移天数的日期
     *
     * @param startDate   偏移计算的起点时间，可选，null时则表示当天
     * @param offsetDays  偏移天数，0则表示不偏移
     *
     * @return Date ，时间部分重置为00:00:00
     */
    public static Date getDateWithoutTime(Date startDate , int offsetDays){
        Calendar calendar = Calendar.getInstance();
        if (null != startDate) {
            calendar.setTime(startDate);
        }

        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        if (offsetDays != 0) {
            calendar.add(Calendar.DAY_OF_MONTH, offsetDays);
        }

        return calendar.getTime();
    }

    /**
     * 清零Time
     * 并且增加或减少X天
     *
     * @param refDate 参考日期，可选，默认以当前系统时间
     * @param days    增加或减少X天
     * @return
     */
    public static Date getDateWithZeroTime(Date refDate, int days) {
        return getDateWithoutTime(refDate,days);
    }

    /**
     * 计算两个日期之间的天数
     *
     * @param startDate 开始日期，必填
     * @param endDate   结束日期，null，则取当前系统时间
     * @return
     */
    public static int getDiffDayIgnoreTime(Date startDate, Date endDate) {
        int days = 0;
        if (null == startDate)
            return days;

        startDate = getDateWithZeroTime(startDate, 0);
        if (null == endDate)
            endDate = getDateWithZeroTime(null, 0);

        long diffMills = endDate.getTime() - startDate.getTime();
        long diffDays = diffMills / 1000 / 60 / 60 / 24;

        return (int) (diffDays + 1);
    }

    public static int getSecondIgnoreTime(Date date) {
        int seconds = 0;
        if (null == date)
            return seconds;

        Date temp = getDateWithZeroTime(date, 0);
        seconds = (int) (temp.getTime() / 1000);
        return seconds;
    }

    /**
     * 查询本周周一
     * @param date
     * @return
     */
    public static Date getThisWeekMonday(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        // 获得当前日期是一个星期的第几天
        int dayWeek = cal.get(Calendar.DAY_OF_WEEK);
        if (1 == dayWeek) {
            cal.add(Calendar.DAY_OF_MONTH, -1);
        }
        // 设置一个星期的第一天，按中国的习惯一个星期的第一天是星期一
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        // 获得当前日期是一个星期的第几天
        int day = cal.get(Calendar.DAY_OF_WEEK);
        // 根据日历的规则，给当前日期减去星期几与一个星期第一天的差值
        cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - day);
        return cal.getTime();
    }

    /**
     * 查询本周周一
     * @param date
     * @return
     */
    public static Date getMondayOfWeek(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        // 获得当前日期是一个星期的第几天
        int dayWeek = cal.get(Calendar.DAY_OF_WEEK);
        if (1 == dayWeek) {
            dayWeek = 8;
        }

        int addDays = 2-dayWeek;
        System.out.println(String.valueOf(dayWeek) + " -> 2 - day_of_week : "+String.valueOf(addDays));

        // 根据日历的规则，给当前日期减去星期几与一个星期第一天的差值
        if(addDays!=0)
            cal.add(Calendar.DAY_OF_MONTH, addDays);

        return cal.getTime();
    }

    public static Date getDateCalculation(Date date, int days, int hours, int min, int seconds) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DAY_OF_MONTH, days);
        c.add(Calendar.HOUR_OF_DAY, hours);
        c.add(Calendar.MINUTE, min);
        c.add(Calendar.SECOND, seconds);
        return c.getTime();
    }

    public static void main(String[] args){

        //最近7天
        Date endDateOfnearest7Day = getDateWithoutTime(null,-1);
        Date startDateOfnearest7Day = getDateWithoutTime(endDateOfnearest7Day,-6);

        // 上一个7天
        Date endDateOfLast7Day = getDateWithoutTime(startDateOfnearest7Day,-1);
        Date startDateOfLast7Day = getDateWithoutTime(endDateOfLast7Day,-6);

        System.out.println("上一个7天时间范围：");
        System.out.println("   开始时间："+startDateOfLast7Day);
        System.out.println("   结束时间："+endDateOfLast7Day);

        System.out.println("最近7天时间范围：");
        System.out.println("   开始时间："+startDateOfnearest7Day);
        System.out.println("   结束时间："+endDateOfnearest7Day);
    }

    /**
     * 获得当月1号零时零分零秒
     * @return
     */
    public static Date getMonthStartDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }
}
