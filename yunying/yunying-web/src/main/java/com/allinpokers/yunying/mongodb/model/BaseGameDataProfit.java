package com.allinpokers.yunying.mongodb.model;


import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

/**
 * BaseGameDataProfit
 *
 * <AUTHOR>
 * @since 2025/3/30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "base_game_data_profit")
public class BaseGameDataProfit implements Serializable {

    @Id
    private String id;

    @Field("at_id")
    @ExcelProperty("机器人ID")
    private String atId;

    @Field("user_random_num")
    @ExcelProperty("用户ID")
    private String userRandomNum;

    @Field("nickname")
    @ExcelProperty("用户名")
    private String nickname;

    @Field("allin_hand")
    @ExcelProperty("allin_hand")
    private Integer allinHand;

    @Field("allin_win_hand")
    @ExcelProperty("allin_win_hand")
    private Integer allinWinHand;

    @Field("tbet_hand")
    @ExcelProperty("tbet_hand")
    private Integer tbetHand;

    @Field("bring_in")
    @ExcelProperty("bring_in")
    private Integer bringIn;

    @Field("call_hand")
    @ExcelProperty("call_hand")
    private Integer callHand;

    @Field("cbet_hand")
    @ExcelProperty("cbet_hand")
    private Integer cbetHand;

    @Field("game_cnt")
    @ExcelProperty("game_cnt")
    private Integer gameCnt;

    @Field("gold_bring_in")
    @ExcelProperty("gold_bring_in")
    private Integer goldBringIn;

    @Field("gold_earn")
    @ExcelProperty("gold_earn")
    private Integer goldEarn;

    @Field("gold_game_cnt")
    @ExcelProperty("gold_game_cnt")
    private Integer goldGameCnt;

    @Field("month_allin_hand")
    @ExcelProperty("month_allin_hand")
    private Integer monthAllinHand;

    @Field("month_allin_win_hand")
    @ExcelProperty("month_allin_win_hand")
    private Integer monthAllinWinHand;

    @Field("month_tbet_hand")
    @ExcelProperty("month_tbet_hand")
    private Integer monthTbetHand;

    @Field("month_bring_in")
    @ExcelProperty("month_bring_in")
    private Integer monthBringIn;

    @Field("month_call_hand")
    @ExcelProperty("month_call_hand")
    private Integer monthCallHand;

    @Field("month_cbet_hand")
    @ExcelProperty("month_cbet_hand")
    private Integer monthCbetHand;

    @Field("month_game_cnt")
    @ExcelProperty("month_game_cnt")
    private Integer monthGameCnt;


    @Field("month_gold_bring_in")
    @ExcelProperty("month_gold_bring_in")
    private Integer monthGoldBringIn;

    @Field("month_gold_earn")
    @ExcelProperty("month_gold_earn")
    private Integer monthGoldEarn;

    @Field("month_gold_game_cnt")
    @ExcelProperty("month_gold_game_cnt")
    private Integer monthGoldGameCnt;

    @Field("month_pfr_hand")
    @ExcelProperty("month_pfr_hand")
    private Integer monthPfrHand;

    @Field("month_pool_hand")
    @ExcelProperty("month_pool_hand")
    private Integer monthPoolHand;

    @Field("month_pool_win_hand")
    @ExcelProperty("month_pool_win_hand")
    private Integer monthPoolWinHand;

    @Field("month_raise_hand")
    @ExcelProperty("month_raise_hand")
    private Integer monthRaiseHand;

    @Field("month_showdown_hand")
    @ExcelProperty("month_showdown_hand")
    private Integer monthShowdownHand;

    @Field("month_showdown_win_hand")
    @ExcelProperty("month_showdown_win_hand")
    private Integer monthShowdownWinHand;

    @Field("month_total_earn")
    @ExcelProperty("month_total_earn")
    private Integer monthTotalEarn;

    @Field("month_total_hand")
    @ExcelProperty("month_total_hand")
    private Integer monthTotalHand;

    @Field("month_win_hand")
    @ExcelProperty("month_win_hand")
    private Integer monthWinHand;

    @Field("pfr_hand")
    @ExcelProperty("pfr_hand")
    private Integer pfrHand;

    @Field("pool_hand")
    @ExcelProperty("pool_hand")
    private Integer poolHand;

    @Field("pool_win_hand")
    @ExcelProperty("pool_win_hand")
    private Integer poolWinHand;

    @Field("raise_hand")
    @ExcelProperty("raise_hand")
    private Integer raiseHand;

    @Field("showdown_hand")
    @ExcelProperty("showdown_hand")
    private Integer showdownHand;

    @Field("showdown_win_hand")
    @ExcelProperty("showdown_win_hand")
    private Integer showdownWinHand;

    @Field("total_earn")
    @ExcelProperty("total_earn")
    private Integer totalEarn;

    @Field("total_hand")
    @ExcelProperty("total_hand")
    private Integer totalHand;

    @Field("week_allin_hand")
    @ExcelProperty("week_allin_hand")
    private Integer weekAllinHand;

    @Field("week_allin_win_hand")
    @ExcelProperty("week_allin_win_hand")
    private Integer weekAllinWinHand;


    @Field("week_tbet_hand")
    @ExcelProperty("week_tbet_hand")
    private Integer weekTbetHand;

    @Field("week_bring_in")
    @ExcelProperty("week_bring_in")
    private Integer weekBringIn;

    @Field("week_call_hand")
    @ExcelProperty("week_call_hand")
    private Integer weekCallHand;

    @Field("week_cbet_hand")
    @ExcelProperty("week_cbet_hand")
    private Integer weekCbetHand;

    @Field("week_game_cnt")
    @ExcelProperty("week_game_cnt")
    private Integer weekGameCnt;


    @Field("week_gold_bring_in")
    @ExcelProperty("week_gold_bring_in")
    private Integer weekGoldBringIn;

    @Field("week_gold_earn")
    @ExcelProperty("week_gold_earn")
    private Integer weekGoldEarn;

    @Field("week_gold_game_cnt")
    @ExcelProperty("week_gold_game_cnt")
    private Integer weekGoldGameCnt;

    @Field("week_pfr_hand")
    @ExcelProperty("week_pfr_hand")
    private Integer weekPfrHand;

    @Field("week_pool_hand")
    @ExcelProperty("week_pool_hand")
    private Integer weekPoolHand;

    @Field("week_pool_win_hand")
    @ExcelProperty("week_pool_win_hand")
    private Integer weekPoolWinHand;

    @Field("week_raise_hand")
    @ExcelProperty("week_raise_hand")
    private Integer weekRaiseHand;

    @Field("week_showdown_hand")
    @ExcelProperty("week_showdown_hand")
    private Integer weekShowdownHand;

    @Field("week_showdown_win_hand")
    @ExcelProperty("week_showdown_win_hand")
    private Integer weekShowdownWinHand;

    @Field("week_total_earn")
    @ExcelProperty("week_total_earn")
    private Integer weekTotalEarn;

    @Field("week_total_hand")
    @ExcelProperty("week_total_hand")
    private Integer weekTotalHand;

    @Field("week_win_hand")
    @ExcelProperty("week_win_hand")
    private Integer weekWinHand;

    @Field("win_hand")
    @ExcelProperty("win_hand")
    private Integer winHand;

}
