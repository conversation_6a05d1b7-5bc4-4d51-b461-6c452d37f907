package com.allinpokers.yunying.entity.crazypoker.example;

import java.util.ArrayList;
import java.util.List;

public class AllianceRelationsExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table alliance_relations
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table alliance_relations
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table alliance_relations
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alliance_relations
     *
     * @mbg.generated
     */
    public AllianceRelationsExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alliance_relations
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alliance_relations
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alliance_relations
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alliance_relations
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alliance_relations
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alliance_relations
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alliance_relations
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alliance_relations
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alliance_relations
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alliance_relations
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table alliance_relations
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andMasterTribeIdIsNull() {
            addCriterion("master_tribe_id is null");
            return (Criteria) this;
        }

        public Criteria andMasterTribeIdIsNotNull() {
            addCriterion("master_tribe_id is not null");
            return (Criteria) this;
        }

        public Criteria andMasterTribeIdEqualTo(Integer value) {
            addCriterion("master_tribe_id =", value, "masterTribeId");
            return (Criteria) this;
        }

        public Criteria andMasterTribeIdNotEqualTo(Integer value) {
            addCriterion("master_tribe_id <>", value, "masterTribeId");
            return (Criteria) this;
        }

        public Criteria andMasterTribeIdGreaterThan(Integer value) {
            addCriterion("master_tribe_id >", value, "masterTribeId");
            return (Criteria) this;
        }

        public Criteria andMasterTribeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("master_tribe_id >=", value, "masterTribeId");
            return (Criteria) this;
        }

        public Criteria andMasterTribeIdLessThan(Integer value) {
            addCriterion("master_tribe_id <", value, "masterTribeId");
            return (Criteria) this;
        }

        public Criteria andMasterTribeIdLessThanOrEqualTo(Integer value) {
            addCriterion("master_tribe_id <=", value, "masterTribeId");
            return (Criteria) this;
        }

        public Criteria andMasterTribeIdIn(List<Integer> values) {
            addCriterion("master_tribe_id in", values, "masterTribeId");
            return (Criteria) this;
        }

        public Criteria andMasterTribeIdNotIn(List<Integer> values) {
            addCriterion("master_tribe_id not in", values, "masterTribeId");
            return (Criteria) this;
        }

        public Criteria andMasterTribeIdBetween(Integer value1, Integer value2) {
            addCriterion("master_tribe_id between", value1, value2, "masterTribeId");
            return (Criteria) this;
        }

        public Criteria andMasterTribeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("master_tribe_id not between", value1, value2, "masterTribeId");
            return (Criteria) this;
        }

        public Criteria andSlaveTribeIdIsNull() {
            addCriterion("slave_tribe_id is null");
            return (Criteria) this;
        }

        public Criteria andSlaveTribeIdIsNotNull() {
            addCriterion("slave_tribe_id is not null");
            return (Criteria) this;
        }

        public Criteria andSlaveTribeIdEqualTo(Integer value) {
            addCriterion("slave_tribe_id =", value, "slaveTribeId");
            return (Criteria) this;
        }

        public Criteria andSlaveTribeIdNotEqualTo(Integer value) {
            addCriterion("slave_tribe_id <>", value, "slaveTribeId");
            return (Criteria) this;
        }

        public Criteria andSlaveTribeIdGreaterThan(Integer value) {
            addCriterion("slave_tribe_id >", value, "slaveTribeId");
            return (Criteria) this;
        }

        public Criteria andSlaveTribeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("slave_tribe_id >=", value, "slaveTribeId");
            return (Criteria) this;
        }

        public Criteria andSlaveTribeIdLessThan(Integer value) {
            addCriterion("slave_tribe_id <", value, "slaveTribeId");
            return (Criteria) this;
        }

        public Criteria andSlaveTribeIdLessThanOrEqualTo(Integer value) {
            addCriterion("slave_tribe_id <=", value, "slaveTribeId");
            return (Criteria) this;
        }

        public Criteria andSlaveTribeIdIn(List<Integer> values) {
            addCriterion("slave_tribe_id in", values, "slaveTribeId");
            return (Criteria) this;
        }

        public Criteria andSlaveTribeIdNotIn(List<Integer> values) {
            addCriterion("slave_tribe_id not in", values, "slaveTribeId");
            return (Criteria) this;
        }

        public Criteria andSlaveTribeIdBetween(Integer value1, Integer value2) {
            addCriterion("slave_tribe_id between", value1, value2, "slaveTribeId");
            return (Criteria) this;
        }

        public Criteria andSlaveTribeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("slave_tribe_id not between", value1, value2, "slaveTribeId");
            return (Criteria) this;
        }

        public Criteria andAllianceStatusIsNull() {
            addCriterion("alliance_status is null");
            return (Criteria) this;
        }

        public Criteria andAllianceStatusIsNotNull() {
            addCriterion("alliance_status is not null");
            return (Criteria) this;
        }

        public Criteria andAllianceStatusEqualTo(Integer value) {
            addCriterion("alliance_status =", value, "allianceStatus");
            return (Criteria) this;
        }

        public Criteria andAllianceStatusNotEqualTo(Integer value) {
            addCriterion("alliance_status <>", value, "allianceStatus");
            return (Criteria) this;
        }

        public Criteria andAllianceStatusGreaterThan(Integer value) {
            addCriterion("alliance_status >", value, "allianceStatus");
            return (Criteria) this;
        }

        public Criteria andAllianceStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("alliance_status >=", value, "allianceStatus");
            return (Criteria) this;
        }

        public Criteria andAllianceStatusLessThan(Integer value) {
            addCriterion("alliance_status <", value, "allianceStatus");
            return (Criteria) this;
        }

        public Criteria andAllianceStatusLessThanOrEqualTo(Integer value) {
            addCriterion("alliance_status <=", value, "allianceStatus");
            return (Criteria) this;
        }

        public Criteria andAllianceStatusIn(List<Integer> values) {
            addCriterion("alliance_status in", values, "allianceStatus");
            return (Criteria) this;
        }

        public Criteria andAllianceStatusNotIn(List<Integer> values) {
            addCriterion("alliance_status not in", values, "allianceStatus");
            return (Criteria) this;
        }

        public Criteria andAllianceStatusBetween(Integer value1, Integer value2) {
            addCriterion("alliance_status between", value1, value2, "allianceStatus");
            return (Criteria) this;
        }

        public Criteria andAllianceStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("alliance_status not between", value1, value2, "allianceStatus");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Integer value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Integer value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Integer value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Integer value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Integer value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Integer> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Integer> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Integer value1, Integer value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andBreakupTimeIsNull() {
            addCriterion("breakup_time is null");
            return (Criteria) this;
        }

        public Criteria andBreakupTimeIsNotNull() {
            addCriterion("breakup_time is not null");
            return (Criteria) this;
        }

        public Criteria andBreakupTimeEqualTo(Integer value) {
            addCriterion("breakup_time =", value, "breakupTime");
            return (Criteria) this;
        }

        public Criteria andBreakupTimeNotEqualTo(Integer value) {
            addCriterion("breakup_time <>", value, "breakupTime");
            return (Criteria) this;
        }

        public Criteria andBreakupTimeGreaterThan(Integer value) {
            addCriterion("breakup_time >", value, "breakupTime");
            return (Criteria) this;
        }

        public Criteria andBreakupTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("breakup_time >=", value, "breakupTime");
            return (Criteria) this;
        }

        public Criteria andBreakupTimeLessThan(Integer value) {
            addCriterion("breakup_time <", value, "breakupTime");
            return (Criteria) this;
        }

        public Criteria andBreakupTimeLessThanOrEqualTo(Integer value) {
            addCriterion("breakup_time <=", value, "breakupTime");
            return (Criteria) this;
        }

        public Criteria andBreakupTimeIn(List<Integer> values) {
            addCriterion("breakup_time in", values, "breakupTime");
            return (Criteria) this;
        }

        public Criteria andBreakupTimeNotIn(List<Integer> values) {
            addCriterion("breakup_time not in", values, "breakupTime");
            return (Criteria) this;
        }

        public Criteria andBreakupTimeBetween(Integer value1, Integer value2) {
            addCriterion("breakup_time between", value1, value2, "breakupTime");
            return (Criteria) this;
        }

        public Criteria andBreakupTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("breakup_time not between", value1, value2, "breakupTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table alliance_relations
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table alliance_relations
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}