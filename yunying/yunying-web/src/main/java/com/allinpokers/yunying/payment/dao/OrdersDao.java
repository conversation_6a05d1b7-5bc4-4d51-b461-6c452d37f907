package com.allinpokers.yunying.payment.dao;

import com.allinpokers.yunying.dao.BaseDao;
import com.allinpokers.yunying.payment.dao.model.Orders;
import com.allinpokers.yunying.payment.dao.model.OrdersWithBLOBs;
import com.allinpokers.yunying.services.model.OrderInfoWithUserANDPayment;
import com.allinpokers.yunying.payment.dao.model.example.OrdersExample;
import com.allinpokers.yunying.services.model.OrderInfoWithUserANDPayment;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.time.LocalDate;

/**
 * OrdersDao  Mapper
 * 
 * <AUTHOR>
 */
@Mapper
public interface OrdersDao extends BaseDao<Orders, OrdersExample, Integer> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table orders
     *
     * @mbg.generated
     */
    List<OrdersWithBLOBs> selectByExampleWithBLOBs(OrdersExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table orders
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") OrdersWithBLOBs record, @Param("example") OrdersExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table orders
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(OrdersWithBLOBs record);

    /**
     * 查询所有
     * @return
     */
    @Select("<script>" +
        "SELECT o.id," +
        "o.order_id AS orderId, " +
        "o.status, " +
        "o.source, " +
        "o.created_at AS createdAt, " +
        "o.updated_at AS updatedAt, " +
        "udi.USER_ID AS userId, " +
        "udi.random_num AS userRandomId, " +
        "udi.nike_name AS userName, " +
        "au.id AS reissueId, " +
        "au.username AS reissueName, " +
        "tp.name_json AS paymentNameJson, " +
        "pp.idou_num AS amount, " +
        "pp.discount AS discount " +
        "FROM yunying.orders o " +
        "LEFT JOIN crazy_poker.user_details_info udi ON udi.USER_ID = o.user_id " +
        "LEFT JOIN yunying.auth_user au ON au.id = o.locked_by " +
        "LEFT JOIN yunying.tribe_payment tp ON tp.id = o.payment_id " +
        "LEFT JOIN yunying.payment_product pp ON pp.id = o.product_id " +
        "<where> " +
        "o.type = 'diamond' " +
        "<if test='startDate != null'>" +
            "AND o.created_at &gt;= #{startDate} " +
        "</if> " +  
        "<if test='endDate != null'>" +
            "AND o.created_at &lt;= #{endDate} " +
        "</if> " +  
        "<if test='value != null and value != \"\"'>" +  
            "AND (udi.random_num LIKE CONCAT('%', #{value}, '%') OR o.order_id LIKE CONCAT('%', #{value}, '%')) " +
        "</if> " +  
        "<if test='source != null and source != \"\"'>" +
            "<choose>" +
                "<when test='source == \"app\"'>" +
                    "AND (o.source IS NULL OR o.source = '')" +
                "</when>" +
                "<otherwise>" +
                    "AND o.source = #{source}" +
                "</otherwise>" +
            "</choose>" +
        "</if> " +  
        "<if test='payment != null and payment != \"\"'>" +
            "AND o.payment_id = #{payment} " +
        "</if> " +  
        "<if test='status != null'>" +
            "AND o.status = #{status} " +
        "</if> " +  
        "</where> " +
        "ORDER BY o.created_at DESC " +
        "</script>")
    List<OrderInfoWithUserANDPayment> queryOrderInfos(@Param(value = "value") String value, @Param(value = "source") String source, @Param(value = "payment") String paymentChannel, @Param(value = "status") Integer status, @Param(value = "startDate") LocalDateTime startDate, @Param(value = "endDate") LocalDateTime endDate);
    
}