package com.allinpokers.yunying.services.impl;

import com.allinpokers.yunying.dao.crazypoker.*;
import com.allinpokers.yunying.entity.crazypoker.*;
import com.allinpokers.yunying.entity.crazypoker.example.PromotionUserInformationExample;
import com.allinpokers.yunying.entity.crazypoker.example.PromotionUserRelationsExample;
import com.allinpokers.yunying.permission.security.SpringSecurityUtils;
import com.allinpokers.yunying.rabbitmq.client.MessageSender;
import com.allinpokers.yunying.rabbitmq.client.bean.ClubMessage;
import com.allinpokers.yunying.rabbitmq.client.bean.MessageUnreadDetailBo;
import com.allinpokers.yunying.rabbitmq.constant.EMessageChannelCode;
import com.allinpokers.yunying.rabbitmq.constant.EMessageCode;
import com.allinpokers.yunying.services.JoinClubTransactionService;
import com.allinpokers.yunying.tier.bean.MemberRoomTier;
import com.allinpokers.yunying.tier.bean.MemberRoomTierQuery;
import com.allinpokers.yunying.tier.dao.TierDao;
import com.allinpokers.yunying.util.DateUtil;
import com.allinpokers.yunying.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Service
@Slf4j
public class JoinClubTransactionServiceImpl implements JoinClubTransactionService {

    @Autowired
    private JoinClubDao joinClubDao;

    @Autowired
    private MessageSender messageSender;

    @Autowired
    private ClubMembersDao clubMembersDao;

    @Autowired
    private PromotionUserRelationsDao promotionUserRelationsDao;

    @Autowired
    private PromotionUserInformationDao promotionUserInformationDao;

    @Autowired
    private TribeMembersDao tribeMembersDao;

    @Autowired
    private TierDao tierDao;

    @Transactional(transactionManager = "crazyPokerTransactionManager", rollbackFor = Exception.class)
    @Override
    public Boolean joinClub(Integer clubId, Integer userId, Integer promoterId) {
        ClubMembers clubMember = new ClubMembers();
        clubMember.setClubId(clubId);
        clubMember.setUserId(String.valueOf(userId));
        clubMember.setType(2);//普通成员
        clubMembersDao.insertSelective(clubMember);
        joinClubDao.updateClubMembers(clubId,1);
        joinClubDao.updateTribesMemberCount(1,clubId);
        this.joinClubPromotion(userId, promoterId, clubId);

        // createClubTier
        createClubTier(clubId, userId);

        return false;
    }

    /**
     * 创建俱乐部分层
     * @param clubId 俱乐部id
     * @param userId 用户id
     */
    private void createClubTier(Integer clubId, Integer userId) {
        // 查询俱乐部，看看是否有联盟
        Integer tribeId = tribeMembersDao.getTribeIdByClubId(clubId);
        if (tribeId != null) {
            MemberRoomTier userTier = tierDao.findUserTier(MemberRoomTierQuery.builder()
                    .userId(userId)
                    .tribeId(tribeId)
                    .build());
            if (userTier == null) {
                // 如果用户在联盟中没有层级，则创建一个默认层级
                tierDao.insertTribeDefaultRoomTier(MemberRoomTierQuery.builder()
                        .userId(userId)
                        .tribeId(tribeId)
                        .build());

                tierDao.insertUserDefaultPaymentActivityTier(
                        tribeId,
                        clubId,
                        userId
                );
            }
        }
    }

    private void joinClubPromotion(Integer userId, Integer promoterId, Integer clubId) {
        try {
            PromotionUserInformationExample poExample = new PromotionUserInformationExample();
            poExample.createCriteria().andUserIdEqualTo(promoterId).andClubIdEqualTo(clubId);
            List<PromotionUserInformation> parentPos = promotionUserInformationDao.selectByExample(poExample);
            if(parentPos != null && parentPos.size() != 0) {
                PromotionUserInformation po = parentPos.get(0);
                bindPromotion(userId, po);
            }
        } catch (Exception e) {
            log.error("Join club promotion error.", e);
            throw new RuntimeException("bind club promotion error.");
        }
    }

    /**
     * 分享模块绑定上下级
     * @param userId 绑定人员
     * @param parentPo 绑定的上级
     */
    @Transactional
    public void bindPromotion(int userId, PromotionUserInformation parentPo) {
        Date today = new Date();
        Integer createBy = SpringSecurityUtils.getUserId().intValue();

        // 插入上下级关系
        joinClubDao.insertPromotionUser(userId, parentPo.getUserId(), parentPo.getClubId(), today, DateUtil.getThisWeekMonday(today), createBy);

        // 更新父级数据
        joinClubDao.updatePromotionUserNumbers(parentPo.getUserId(), 1, 0);
        joinClubDao.updateTodayPromotionUserNumbers(parentPo.getUserId(), 1, new Date());

        insertPromotionUserRelations(userId, parentPo.getUserId(), parentPo.getClubId());
    }

    /**
     * 插入分享用户关系表
     */
    private void insertPromotionUserRelations(Integer userId, Integer parentUserId, Integer clubId) {
        PromotionUserRelationsExample example = new PromotionUserRelationsExample();
        example.createCriteria().andUserIdEqualTo(parentUserId).andClubIdEqualTo(clubId);
        List<PromotionUserRelations> relationsPos = promotionUserRelationsDao.selectByExample(example);
        if(relationsPos != null) {
            for(PromotionUserRelations relationsPo : relationsPos) {
                joinClubDao.insertPromotionUserRelation(userId, relationsPo.getParentUserId(), (relationsPo.getLevel() + 1), clubId, 0);
            }
        }
        //  创建自己的树
        joinClubDao.insertPromotionUserRelation(userId, userId, 0, clubId, 0);
    }

    @Transactional
    @Override
    public void clubMembersLimitOperation(ClubRecord clubRecord) {
        // 发送消息通知俱乐部创建者
        createClubMessageRecord("",clubRecord.getCreator(), clubRecord.getCreator(),"","","","", EMessageCode.CLUB_MEMBER_LIMIT_NOTIFY.getCode(),0);

        //2、插入未读消息数量   msg的参数 需要json序列化的数据  取出返回是json反序列化出去的
        MessageUnreadDetailBo messageBo = MessageUnreadDetailBo.builder().time(new Date().getTime()).type(EMessageCode.CLUB_MEMBER_LIMIT_NOTIFY.getCode()).content("").remark("").build();
        joinClubDao.updateClubUnreadMsg(1, JsonUtils.write(messageBo),Integer.parseInt(clubRecord.getCreator()));

        List<String> reciverIds = new ArrayList<>();
        reciverIds.add(clubRecord.getCreator());
        messageSender.sendClubMessage(ClubMessage.builder().reciverUserIds(reciverIds).type(EMessageCode.CLUB_MEMBER_LIMIT_NOTIFY.getCode()).pushChannel(EMessageChannelCode.TIM.getCode()).build());
    }

    //俱乐部消息
    public void createClubMessageRecord(String clubId, String senderId, String reciverId, String header, String title, String content, String remark, Integer type, Integer msgStatus) {
        String msgId = UUID.randomUUID().toString();//消息id
        joinClubDao.createClubMessageRecord(msgId,clubId,senderId,reciverId,header,title,content,remark,type,msgStatus);
    }

}
