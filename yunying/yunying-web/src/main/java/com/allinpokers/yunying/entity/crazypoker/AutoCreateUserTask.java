package com.allinpokers.yunying.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 一键创建用户任务表  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AutoCreateUserTask {
    /**
     * id
     */
    @ApiModelProperty("id")
    private Integer id;

    /**
     * 目标创建用户的数量
     */
    @ApiModelProperty("目标创建用户的数量")
    private Integer targetNumber;

    /**
     * 实际生成用户的数量
     */
    @ApiModelProperty("实际生成用户的数量")
    private Integer actualNumber;

    /**
     * 密码
     */
    @ApiModelProperty("密码")
    private String password;

    /**
     * 提交时间
     */
    @ApiModelProperty("提交时间")
    private LocalDateTime submitTime;

    /**
     * 完成时间
     */
    @ApiModelProperty("完成时间")
    private LocalDateTime finishTime;

    /**
     * 0等待处理 1 处理中 2完成
     */
    @ApiModelProperty("0等待处理 1 处理中 2完成")
    private Integer status;
}