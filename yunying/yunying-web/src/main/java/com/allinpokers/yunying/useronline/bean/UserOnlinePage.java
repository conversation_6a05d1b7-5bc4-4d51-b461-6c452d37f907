package com.allinpokers.yunying.useronline.bean;

import com.allinpokers.yunying.model.response.PageBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class UserOnlinePage extends PageBean<UserOnline> {

    @ApiModelProperty("在线玩家总数")
    private Integer totalOnlineCount;

    @ApiModelProperty("绿名单")
    private Integer greenListCount;

    @ApiModelProperty("橙名单")
    private Integer orangeListCount;

    @ApiModelProperty("白名单")
    private Integer whiteListCount;

    @ApiModelProperty("灰名单")
    private Integer grayListCount;

    @ApiModelProperty("黑名单")
    private Integer blackListCount;

    public UserOnlinePage(UserOnlineCount onlineCount) {
        this.totalOnlineCount = onlineCount.getTotalOnlineCount();
        this.greenListCount = onlineCount.getGreenListCount();
        this.orangeListCount = onlineCount.getOrangeListCount();
        this.whiteListCount = onlineCount.getWhiteListCount();
        this.grayListCount = onlineCount.getGrayListCount();
        this.blackListCount = onlineCount.getBlackListCount();
    }
}
