package com.allinpokers.yunying.network.service;

import com.allinpokers.yunying.network.dao.model.NetworkConfig;
import com.allinpokers.yunying.network.dao.model.NetworkSwitchLog;
import com.allinpokers.yunying.network.service.bean.NetworkInfoSyncVo;

import java.util.List;
import java.util.Map;

public interface INetworkService {
    /** 线路标识符 */
    int NETWORK_CHINA=1;
    int NETWORK_ABROAD=2;

    /**
     * 返回当前所有可用的网络配置
     * @return
     */
    List<NetworkConfig> queryAll();

    /**
     * 保存数据
     *
     * @param inlandMap
     * @param abroadMap
     */
    void update(Map<NetworkConfig,NetworkSwitchLog> inlandMap,
                Map<NetworkConfig,NetworkSwitchLog> abroadMap);

    /**
     * 推送切换线路信息
     * @param machineType
     * @param channel
     */
    void pushSwitchNotifly(Integer machineType,Integer channel);

    NetworkInfoSyncVo copyTo(NetworkConfig config);

    NetworkConfig getEnableConfig(Map<Integer, List<NetworkConfig>> ds, int networkCode);

    NetworkConfig getConfigById(Map<Integer, List<NetworkConfig>> ds,int networkCode,int id);

    String genContent(NetworkInfoSyncVo mainConfig, List<NetworkInfoSyncVo> list);

    String genPushContent();
}
