package com.allinpokers.yunying.services.model;

import lombok.*;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class ManualRechargeOneUserChipParams {

    /**
     * 用户隐性ID
     */
    private Integer userId;

    /**
     * 金豆类型，1可提，2不可提
     */
    private Integer chipType;

    /**
     * 充豆类型：
     * 1：手工收费
     * 2：手工扣豆
     * 3：赠送
     * 4：系统红包
     * 5：手工补单
     * 6：营销-牌桌金豆红包
     * 7：BUG赔付
     * 8：手动提现
     */
    private Integer rechargeType;

    /**
     * 充豆状态：按充豆类型区分
     * 手工收费/手工扣豆：1已收，2未收
     * 手工提现：1已付，2未付
     */
    private Integer rechargeStatus;

    /**
     * 补单订单号，充豆类型是手工补单
     */
    private String replenishmentNo;

    /**
     * 充豆的金豆数量
     */
    private Integer rechargeChip;

    /**
     * 申请人
     */
    private String applicant;

    /**
     * 审批人
     */
    private String approver;

    /**
     * 备注
     */
    private String remark;
}
