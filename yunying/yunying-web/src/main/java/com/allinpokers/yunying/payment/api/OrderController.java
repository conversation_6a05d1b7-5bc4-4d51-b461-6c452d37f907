package com.allinpokers.yunying.payment.api;

import com.allinpokers.yunying.annotation.ApiResponseEnum;
import com.allinpokers.yunying.enu.ResponseCodeEnum;
import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.payment.constant.EChannel;
import com.allinpokers.yunying.payment.dao.model.PlatformRechargeOrder;
import com.allinpokers.yunying.payment.dao.model.TribePayment;
import com.allinpokers.yunying.payment.service.IOrderService;
import com.allinpokers.yunying.payment.api.req.ListOrderReq;
import com.allinpokers.yunying.services.model.OrderInfo;
import com.allinpokers.yunying.permission.security.UserInfo;
import com.allinpokers.yunying.permission.security.SpringSecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.allinpokers.yunying.model.request.PageReq;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.allinpokers.yunying.model.response.PageBean;

import java.math.BigDecimal;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.time.LocalDate;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import static com.allinpokers.yunying.enu.ResponseCodeEnum.*;

@Api(tags = "商城-平台充值记录")
@RestController
@RequestMapping("/payment/order")
@Slf4j
public class OrderController {
    /** 服务实例 */
    @Autowired
    private IOrderService orderService;

    @ApiOperation(value = "平台充值记录列表")
    @PostMapping("/list")
    public CommonRespon<PageBean<OrderInfo>> listOrder(@RequestBody(required = false) ListOrderReq req) {
        String value = req.getValue();
        String source = req.getSource();
        String paymentChannel = req.getPaymentChannel();
        Integer status = req.getStatus();
        LocalDate startDate = req.getStartDate();
        LocalDate endDate = req.getEndDate();

        Map<String, Object> result = this.orderService.countOrderInfos(value, source, paymentChannel, status, startDate, endDate);
        Integer count = (Integer) result.get("count");
        BigDecimal total = (BigDecimal) result.get("total");
        
        PageHelper.startPage(req.getPage(), req.getSize());
        List<OrderInfo> dataLst = this.orderService.queryOrderInfos(value, source, paymentChannel, status, startDate, endDate);
        for (OrderInfo item : dataLst) {
            item.setTotal(total);
        }
        PageInfo<OrderInfo> pageInfo = new PageInfo<>(dataLst);
        PageBean<OrderInfo> data = new PageBean();

        return CommonRespon.success(data.setTotal(count).setLastPage(pageInfo.isIsLastPage())
                .setPages(pageInfo.getPages()).setBeanList(dataLst));
    }

    @ApiOperation(value = "商品详情")
    @PostMapping("/reissue/{id}")
    public CommonRespon reissueOrder(@PathVariable int id) {
        UserInfo operationUser = SpringSecurityUtils.getUserInfo();
        this.orderService.reissue(operationUser.getId(),id);

        return CommonRespon.success();
    }

    @ApiOperation(value = "導出")
    @PostMapping("/export")
    public void export(
            @ApiParam(value = "token", required = true) @RequestParam String token,
            @ApiParam(value = "search") @RequestParam(required = false) String value,
            @ApiParam(value = "购买來源") @RequestParam(required = false) String source,
            @ApiParam(value = "支付渠道") @RequestParam(required = false) String paymentChannel,
            @ApiParam(value = "狀態") @RequestParam(required = false) Integer status,
            @ApiParam(value = "开始时间，yyyy-MM-dd", example = "2017-04-08", required = true)
            @RequestParam LocalDate startDate,
            @ApiParam(value = "结束时间，yyyy-MM-dd", example = "2019-05-08", required = true)
            @RequestParam LocalDate endDate,
            HttpServletResponse response) throws IOException {

        this.orderService.exportPlatformRechargeOrder(value, source, paymentChannel, status, startDate, endDate, response);

    }

    @ApiOperation(value = "paymentList")
    @PostMapping("/getPaymentList")
    public CommonRespon<List<TribePayment>> getPaymentList() {
        List<TribePayment> dataList = this.orderService.queryTribePayment();
        return CommonRespon.success(dataList);
    }
    
}
