package com.allinpokers.yunying.mongodb.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * <AUTHOR>
 */
@Data
@Document("game_request_record")
public class GameRequestRecord {
    @Id
    private String id;
    @Field("room_id")
    private String roomId;
    @Field("room_path")
    private String roomPath;
    @Field("room_name")
    private String roomName;
    @Field("user_id")
    private Integer userId;
    @Field("owner_id")
    private Integer ownerId;
    @Field("nick_name")
    private String nickName;
    @Field("take_in")
    private Integer takeIn;
    @Field("msg_id")
    private String msgId;
    @Field("type")
    private Integer type;
    @Field("sb")
    private Integer sb;
    @Field("bb")
    private Integer bb;
    @Field("start_time")
    private Long startTime;
    @Field("time")
    private Long time;
    @Field("status")
    private Integer status;
    @Field("ignore_bring")
    private Integer ignoreBring;
    @Field("club_id")
    private Integer clubId;
    @Field("head")
    private String head;
    @Field("room_type")
    private Integer roomType;
    @Field("club_type")
    private Integer clubType;
    @Field("tribe_id")
    private Integer tribeId;
    @Field("tribe_name")
    private String tribeName;
    @Field("entry_fee")
    private Integer entryFee;
    @Field("random_num")
    private String randomNum;
    @Field("control")
    private Integer control;

}
