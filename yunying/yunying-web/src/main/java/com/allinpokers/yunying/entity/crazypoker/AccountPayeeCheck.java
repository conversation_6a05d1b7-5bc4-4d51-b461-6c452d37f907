package com.allinpokers.yunying.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 提豆信息审核表  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AccountPayeeCheck {
    /**
     * id
     */
    @ApiModelProperty("id")
    private Integer id;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private Integer uid;

    /**
     * 审核类型1-银行卡,2-支付宝
     */
    @ApiModelProperty("审核类型1-银行卡,2-支付宝")
    private Integer type;

    /**
     * 审核内容json字符串,定义审核的审核内容
     *             
     */
    @ApiModelProperty("审核内容json字符串,定义审核的审核内容<br/>            ")
    private String desction;

    /**
     * 状态(0-审核中,1-审核成功,2-审核失败)
     */
    @ApiModelProperty("状态(0-审核中,1-审核成功,2-审核失败)")
    private Integer status;

    /**
     * 审核人id
     */
    @ApiModelProperty("审核人id")
    private Integer checkId;

    /**
     * 审核时间
     */
    @ApiModelProperty("审核时间")
    private LocalDateTime checkTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createdTime;
}