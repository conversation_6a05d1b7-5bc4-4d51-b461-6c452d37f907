package com.allinpokers.yunying.model.response.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@ApiModel
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageClubCreate {
    @ApiModelProperty("消息UUID")
    private String msgId;
    @ApiModelProperty("用户ID")
    private String userId;
    @ApiModelProperty("用户昵称")
    private String nickname;
    @ApiModelProperty("俱乐部名称")
    private String clubName;
    @ApiModelProperty("俱乐部头像")
    private String clubHead;
    @ApiModelProperty("俱乐部所属地区ID")
    private String clubArea;
    @ApiModelProperty("俱乐部简介")
    private String clubDesc;
    @ApiModelProperty("联系方式")
    private String contact;
    @ApiModelProperty("留言板")
    private String message;
    @ApiModelProperty("申请时间")
    private LocalDateTime createTime;
}
