package com.allinpokers.yunying.model.request.cmsaccount;

import com.allinpokers.yunying.model.request.PageReq;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(value = "cms渠道充豆提豆列表查询")
@Data
public class CmsAccountFlowReq  extends PageReq {

    @ApiModelProperty(value = "搜索内容")
    String search;

    @ApiModelProperty(value = "cms账户id")
    Integer cmsId;

    @ApiModelProperty(value = "查询类型，0-充豆，1-提豆。默认0充豆")
    Integer type;

    @ApiModelProperty(value = "查询条件：开始时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm")
    Date startTime;

    @ApiModelProperty(value = "查询条件：开始时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm")
    Date endTime;
}
