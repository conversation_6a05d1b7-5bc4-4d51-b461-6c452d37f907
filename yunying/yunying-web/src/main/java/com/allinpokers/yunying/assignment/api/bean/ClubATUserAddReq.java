package com.allinpokers.yunying.assignment.api.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalTime;

@Getter
@Setter
@ApiModel("AT分发-AT用户批量增加")
public class ClubATUserAddReq extends ClubMTUserAddReq {
    @ApiModelProperty("游戏类型, 修改/删除时可选，必填<br/>" +
            "有效取值范围：<br/>" +
            "  61=德州<br/>" +
            "  63=德州-AOF<br/>")
    private Integer roomPath;

    @ApiModelProperty("小盲注，修改/删除时可选，增加时必填。")
    private Integer smallblind;

    @ApiModelProperty("状态,删除时，可选；否则，必填。<br/>" +
                    "有效取值范围：<br/>" +
                    "    on  = 启用<br/>" +
                    "    off = 终止")
    private String status;

    @ApiModelProperty("时间范围-开始时间，修改/删除时可选，增加时必填。<br/>" +
                    "格式：HH:mm")
    @JsonFormat(pattern="HH:mm")
    private LocalTime startTime;

    @ApiModelProperty("时间范围-结束时间，修改/删除时可选，增加时必填。<br/>" +
                    "格式：HH:mm")
    @JsonFormat(pattern="HH:mm")
    private LocalTime endTime;
}
