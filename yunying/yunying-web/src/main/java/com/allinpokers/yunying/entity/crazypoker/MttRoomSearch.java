package com.allinpokers.yunying.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MttRoomSearch  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MttRoomSearch {
    /**
     * 自增id
     */
    @ApiModelProperty("自增id")
    private Long id;

    /**
     * 比赛id
     */
    @ApiModelProperty("比赛id")
    private Integer matchId;

    /**
     * 比赛名称
     */
    @ApiModelProperty("比赛名称")
    private String matchName;

    /**
     * 比赛图片地址
     */
    @ApiModelProperty("比赛图片地址")
    private String logoUrl;

    /**
     * 报名费
     */
    @ApiModelProperty("报名费")
    private Integer entryFee;

    /**
     * 报名券代号，不需要则是0
     */
    @ApiModelProperty("报名券代号，不需要则是0")
    private Integer voucher;

    /**
     * 参赛上限
     */
    @ApiModelProperty("参赛上限")
    private Integer upperLimit;

    /**
     * 比赛开赛时间
     */
    @ApiModelProperty("比赛开赛时间")
    private LocalDateTime startTime;

    /**
     * 比赛类别 1=积分赛 2=实物赛
     */
    @ApiModelProperty("比赛类别 1=积分赛 2=实物赛")
    private Integer mttType;

    /**
     * 比赛状态 0=未开始，1=开始，2=结束，3=解散
     */
    @ApiModelProperty("比赛状态 0=未开始，1=开始，2=结束，3=解散")
    private Integer status;

    /**
     * 由谁创建
     */
    @ApiModelProperty("由谁创建")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createdTime;

    /**
     * 服务器编号
     */
    @ApiModelProperty("服务器编号")
    private String serverId;

    /**
     * 服务器ip
     */
    @ApiModelProperty("服务器ip")
    private String accessIp;

    /**
     * 服务器端口号
     */
    @ApiModelProperty("服务器端口号")
    private Integer accessPort;

    @ApiModelProperty("ai的数量")
    private Integer aiCount;
}