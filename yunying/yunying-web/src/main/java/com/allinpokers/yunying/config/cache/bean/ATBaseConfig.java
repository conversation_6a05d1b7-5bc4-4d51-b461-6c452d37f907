package com.allinpokers.yunying.config.cache.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ATBaseConfig {
    /**
     * 应用下发时的同步到牌局的监控目录
     */
    private String syncDir;
    /**
     * AT用户的同步文件名
     * 基于上面目录的相对路径
     */
    private String syncAtUserFile;
    /**
     * MT用户的同步文件名
     * 基于上面目录的相对路径
     */
    private String syncMtUserFile;
    /**
     * 开关配置的同步文件名
     * 基于上面目录的相对路径
     */
    private String syncSwitchFile;

    /**
     * 爆桌时间区间配置的同步文件名
     * 基于上面目录的相对路径
     */
    private String syncExplosionTableFile;
}
