package com.allinpokers.yunying.model.request.manualrecharge;

import com.allinpokers.yunying.model.request.PageReq;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@ApiModel
public class ManualRechargeReq extends PageReq {

    @ApiModelProperty(value = "开始时间，yyyy-MM-dd HH:mm", example = "2017-04-08 00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间，yyyy-MM-dd HH:mm", example = "2020-05-08 00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime endTime;

}
