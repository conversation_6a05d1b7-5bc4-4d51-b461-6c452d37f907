package com.allinpokers.yunying.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户收款账户信息表  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AccountPayee {
    /**
     * id
     */
    @ApiModelProperty("id")
    private Integer id;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private Integer uid;

    /**
     * 银行户名
     */
    @ApiModelProperty("银行户名")
    private String bankAccName;

    /**
     * 银行卡号
     */
    @ApiModelProperty("银行卡号")
    private String bankAccNo;

    /**
     * 银行名称
     */
    @ApiModelProperty("银行名称")
    private String bankName;

    /**
     * bankCode
     */
    @ApiModelProperty("bankCode")
    private String bankCode;

    /**
     * 开户行
     */
    @ApiModelProperty("开户行")
    private String bankOfDeposit;

    /**
     * 开户所在省
     */
    @ApiModelProperty("开户所在省")
    private String bankLocProvince;

    /**
     * 开户所在市
     */
    @ApiModelProperty("开户所在市")
    private String bankLocCity;

    /**
     * 支付宝实名制名称
     */
    @ApiModelProperty("支付宝实名制名称")
    private String alipayRealName;

    /**
     * 支付宝账号
     */
    @ApiModelProperty("支付宝账号")
    private String alipayAcc;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updatedTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createdTime;
}