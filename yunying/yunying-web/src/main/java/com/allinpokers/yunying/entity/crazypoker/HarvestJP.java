package com.allinpokers.yunying.entity.crazypoker;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;

@Setter
@Getter
public class HarvestJP {
    //房间名称
    private String roomName;
    //用户
    private String userName;
    //中彩的彩金
    private int bet;
    //彩金服务费
    private int rewardFee;
    //用户的俱乐部Id没有为0
    private int cludId;
    //用户的联盟Id没有为0
    private int tribeId;
    //是否为机器人0:false ; 1:true
    private int AIstate;
    //房间类型
    private int roomPath;
    //小盲
    private int mangzhu;
    //彩金中奖俱乐部的分成
    private int cludeProportion;
    //彩金中奖联盟的分成
    private int tribeProportion;
    //牌型
    private int paixing;

    private Timestamp creatTime;

}

