package com.allinpokers.yunying.model.request.jp;

import com.allinpokers.yunying.model.request.gameDetail.BaseStatsReq;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "玩家列表jp 记录")
public class ClubRoomUserListJPReq extends BaseStatsReq {

    @ApiModelProperty(hidden = true)
    @JsonIgnore
    List<Integer> roomPaths;

    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Integer tribeId;
    /**
     * 俱乐部id  对应
     **/
    @ApiModelProperty(value = "俱乐部id")
    private Integer clubId;


    /**
     * 房间id 对应
     **/
    @ApiModelProperty(value = "房间id")
    private Integer roomId;


    @ApiModelProperty("牌局类型 所有=all  61 = 德州   91 = 奥马哈  11=必下场  22=AOF ")
    private String roomPath = "all";
}
