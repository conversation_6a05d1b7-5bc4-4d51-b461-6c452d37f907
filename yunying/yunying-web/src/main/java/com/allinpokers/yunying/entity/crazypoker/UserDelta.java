package com.allinpokers.yunying.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * UserDelta  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserDelta {
    /**
     * userId
     */
    @ApiModelProperty("userId")
    private Integer userId;

    /**
     * gameCnt
     */
    @ApiModelProperty("gameCnt")
    private Integer gameCnt;

    /**
     * totalHand
     */
    @ApiModelProperty("totalHand")
    private Integer totalHand;

    /**
     * poolRate
     */
    @ApiModelProperty("poolRate")
    private Integer poolRate;

    /**
     * poolWinRate
     */
    @ApiModelProperty("poolWinRate")
    private Integer poolWinRate;

    /**
     * allinWinRate
     */
    @ApiModelProperty("allinWinRate")
    private Integer allinWinRate;

    /**
     * afRate
     */
    @ApiModelProperty("afRate")
    private Integer afRate;

    /**
     * prfRate
     */
    @ApiModelProperty("prfRate")
    private Integer prfRate;

    /**
     * bet3Rate
     */
    @ApiModelProperty("bet3Rate")
    private Integer bet3Rate;

    /**
     * cbetRate
     */
    @ApiModelProperty("cbetRate")
    private Integer cbetRate;

    /**
     * tanpaiRate
     */
    @ApiModelProperty("tanpaiRate")
    private Integer tanpaiRate;
}