package com.allinpokers.yunying.services.impl;

import com.allinpokers.yunying.config.cache.IHotConfigCache;
import com.allinpokers.yunying.config.cache.bean.RoomInfoWhitelistConfig;
import com.allinpokers.yunying.dao.crazypoker.UserDetailsInfoDao;
import com.allinpokers.yunying.entity.crazypoker.*;
import com.allinpokers.yunying.enu.RoomPathTypeEnum;
import com.allinpokers.yunying.model.response.PageBean;
import com.allinpokers.yunying.model.response.gamedetail.UserTotalStat;
import com.allinpokers.yunying.mongodb.dao.AddRequestInfoDao;
import com.allinpokers.yunying.mongodb.dao.GameRequestRecordDao;
import com.allinpokers.yunying.mongodb.dao.SelectClubDao;
import com.allinpokers.yunying.mongodb.model.AddRequestInfo;
import com.allinpokers.yunying.mongodb.model.GameRequestRecord;
import com.allinpokers.yunying.permission.security.UserInfo;
import com.allinpokers.yunying.services.ClubRecordService;
import com.allinpokers.yunying.services.DataManagerService;
import com.allinpokers.yunying.services.GameDetailService;
import com.allinpokers.yunying.services.UserDetailsInfoService;
import com.allinpokers.yunying.services.model.*;
import com.allinpokers.yunying.util.DateUtil;
import com.allinpokers.yunying.util.RamdomUtil;
import com.allinpokers.yunying.util.RandomUtils;
import com.allinpokers.yunying.util.excel.ExcelModel;
import com.allinpokers.yunying.util.excel.ExcelRow;
import com.allinpokers.yunying.util.excel.ExcelSheet;
import com.allinpokers.yunying.util.excel.ExcelUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 数据管理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DataManageServiceImpl implements DataManagerService {

    @Autowired
    private AddRequestInfoDao addRequestInfoDao;

    @Autowired
    private GameRequestRecordDao gameRequestRecordDao;

    @Autowired
    private SelectClubDao selectClubDao;

    @Autowired
    private UserDetailsInfoDao userDetailsInfoDao;

    @Autowired
    private UserDetailsInfoService userService;

    @Autowired
    private MongoTemplate mongoTemplate;
    @Resource
    private ClubRecordService clubRecordService;
    @Resource
    private GameDetailService gameDetailService;
    @Resource(name = "hotConfigCacheImpl")
    private IHotConfigCache configCache;


    @Override
    public List<PlayerIP> queryPlayerIpsByTimeRange(Date startTime, Date endTime) {
        // TODO 黑名单过滤
        List<AddRequestInfo> addRequestInfos = addRequestInfoDao.findByTimeBetween(startTime.getTime(), endTime.getTime());
        List<PlayerIP> playerIPS = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        if (addRequestInfos != null) {
            for (AddRequestInfo addRequestInfo : addRequestInfos) {
                String userIds = map.get(addRequestInfo.getIp());
                if (userIds == null) {
                    userIds = addRequestInfo.getUserId() + "";
                } else {
                    userIds = userIds + "," + addRequestInfo.getUserId();
                }
                map.put(addRequestInfo.getIp(), userIds);
            }

            if (map.size() != 0) {
                for (String key : map.keySet()) {
                    PlayerIP playerIP = new PlayerIP();
                    playerIP.setIp(key);
                    playerIP.setUserId(map.get(key));
                    playerIPS.add(playerIP);
                }
            }
        }
        return playerIPS;
    }

    @Override
    public List<SelectClubInfo> querySelectClub(String user, Date startTime, Date endTime) {
//        List<SelectClub> selectClubs = new ArrayList<>();
        List<SelectClubInfo> selectClubInfos = new ArrayList<>();
        if (user != null && !user.equals("")) {
            // 查询用户id
            Integer userId = userDetailsInfoDao.selectByRandomIdOrUserName(user);
            if (userId != null && !user.equals("")) {
                // 查询带入信息
                List<GameRequestRecord> gameRequestRecords = gameRequestRecordDao.findByUserIdAndTimeBetween(userId, startTime.getTime(), endTime.getTime());
                if(gameRequestRecords != null) {
                    for(GameRequestRecord gameRequestRecord : gameRequestRecords) {
                        SelectClubInfo selectClubInfo = new SelectClubInfo();
                        selectClubInfo.setClubId(userDetailsInfoDao.selectRandomIdByClubId(gameRequestRecord.getClubId()));
                        if(gameRequestRecord.getOwnerId() != null) {
                            selectClubInfo.setOwnerId(userDetailsInfoDao.selectRandomIdByUserId(gameRequestRecord.getOwnerId()).longValue());
                        }
                        selectClubInfo.setRoomId(gameRequestRecord.getRoomId());
                        selectClubInfo.setRoomName(gameRequestRecord.getRoomName());
                        selectClubInfo.setRoomPath(RoomPathTypeEnum.getRoomName(Integer.parseInt(gameRequestRecord.getRoomPath())).getDesc());
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:SS");
                        selectClubInfo.setTime(sdf.format(new Date(gameRequestRecord.getTime())));
                        selectClubInfo.setUserId(userDetailsInfoDao.selectRandomIdByUserId(gameRequestRecord.getUserId()).longValue());
                        selectClubInfo.setTakeIn(gameRequestRecord.getTakeIn());
                        selectClubInfos.add(selectClubInfo);
                    }
                }
            }
        }
        return selectClubInfos;
    }

    @Override
    public void exportTakeinIps(Integer days, HttpServletResponse response) throws IOException {
        if (days <= 0) {
            days = 3;
        }
        Date startTime = DateUtil.calDateWithoutTime(0 - days);
        List<PlayerIP> playerIPS = queryPlayerIpsByTimeRange(startTime, new Date());

        ExcelSheet sheet = new ExcelSheet("带入ip信息");
        List<ExcelModel> models = new ArrayList<>();
        sheet.setModels(models);

        List<ExcelRow> rows = new ArrayList<>();

        String[] titles = new String[]{"IP", "玩家ID"};

        if (playerIPS != null && playerIPS.size() != 0) {
            for (int i = 0; i < playerIPS.size(); i++) {
                PlayerIP playerIP = playerIPS.get(i);
                ExcelRow row = new ExcelRow()
                        .add(playerIP.getIp())
                        .add(playerIP.getUserId());
                rows.add(row);
            }
        }

        ExcelModel model = ExcelModel.builder()
                .titles(Lists.newArrayList(titles))
                .rows(rows)
                .afterBlankLine(1)
                .build();
        models.add(model);

        export(sheet, response);
    }

    @Override
    public PageBean<PlayerGps> queryUserGps(@NotNull List<String> userIdList, @NotNull Integer pattern, @NotNull Integer page, @NotNull Integer size) {
        List<PlayerGps> dataList = new ArrayList<>();

        List<Integer> userIdIntList = new ArrayList<>();
        Map<Integer, String> randomIdMap = new HashMap<>();
        if (pattern == 0) {//userid查询
            userIdIntList = userIdList.stream().map(Integer::parseInt).collect(Collectors.toList());
            List<UserDetailsInfo> userByUserRamdomIds = this.userService.findUsersByUserIds(userIdIntList);
            if (null != userByUserRamdomIds && userByUserRamdomIds.size() > 0) {
                randomIdMap = userByUserRamdomIds.stream().collect(Collectors.toMap(UserDetailsInfo::getUserId, UserDetailsInfo::getRandomNum));
            }
        } else if (pattern == 1) {//随机id查询
            //先查询用户id
            List<UserDetailsInfo> userByUserRamdomIds = this.userService.findUserByUserRamdomIds(userIdList);
            if (null != userByUserRamdomIds && userByUserRamdomIds.size() > 0) {
                userIdIntList = userByUserRamdomIds.stream().map(UserDetailsInfo::getUserId).collect(Collectors.toList());
                randomIdMap = userByUserRamdomIds.stream().collect(Collectors.toMap(UserDetailsInfo::getUserId, UserDetailsInfo::getRandomNum));
            }
        }
        if (userIdIntList.size() > 0) {
            List<AddRequestInfo> addRequestInfos = addRequestInfoDao.findByUserIdIn(new HashSet<>(userIdIntList));
            if (null != addRequestInfos) {
                List<Integer> userWhitelist = getUserWhitelist();
                boolean hasWhiteList = userWhitelist != null && !userWhitelist.isEmpty();
                String today = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
                for (AddRequestInfo d : addRequestInfos) {
                    //设置用户的userId是用户的显性的id
                    PlayerGps playerGps = PlayerGps.builder().userId(randomIdMap.getOrDefault(d.getUserId(), "")).
                            ip(d.getIp()).imei(d.getImei()).
                            time(LocalDateTime.ofEpochSecond((d.getTime() / 1000), 0, ZoneOffset.ofHours(8))).build();
                    if (hasWhiteList && userWhitelist.contains(d.getUserId())) {
                        String randomSeed = d.getUserId() + today;
                        String randomGps = RandomUtils.nextGps(randomSeed);
                        playerGps.setGps(randomGps);
                    } else {
                        playerGps.setGps(d.getGps());
                    }
                    dataList.add(playerGps);
                }
            }
        }

        return PageBean.of(dataList.size(), page, size, dataList.subList((page - 1) * size, (page * size) > dataList.size() ? dataList.size() : (page * size)));
    }

    @Override
    public RelevanceInfo queryUserRelevance(String randomId, Integer imeiCondition, Integer ipCondition, Integer simulatorCondition) {
        RelevanceInfo relevanceInfo = new RelevanceInfo();

        UserDetailsInfo userInfo = this.userService.findUserByUserRamdomId(randomId);
        if (null != userInfo) {
            String userId = String.valueOf(userInfo.getUserId());
            List<Integer> userWhitelist = getUserWhitelist();
            boolean hasWhiteList = userWhitelist != null && !userWhitelist.isEmpty();
            if (hasWhiteList && userWhitelist.contains(userInfo.getUserId())) {//如果是白名单用户，就是只是返回他自己本人的信息就可以了
                List<RelevanceInfo.UserAddInInfo> secondDatas = new ArrayList<>();
                RelevanceInfo.UserAddInInfo userAddInInfo = new RelevanceInfo.UserAddInInfo();
                userAddInInfo.setUserId(userId);
                userAddInInfo.setRandomNum(randomId);
                userAddInInfo.setNickName(userInfo.getNikeName());
                userAddInInfo.setImeiData((String[]) Arrays.asList(RamdomUtil.getRamdImei(String.valueOf(userId))).toArray());
                userAddInInfo.setIpData((String[]) Arrays.asList(RamdomUtil.getRandomIp()).toArray());
                secondDatas.add(userAddInInfo);
                relevanceInfo.setSecondDatas(secondDatas);
                //用完清空，不传给前端
                relevanceInfo.setAllUserId(null);
            } else {//非白名单用户就是要去关联查询，同时要过虑关联到的用户在白名单里面的
                relevanceInfo = getRelevanceInfo(Integer.valueOf(userId), ipCondition == 1, imeiCondition == 1, simulatorCondition);
                if (relevanceInfo.getAllUserId().size() > 0) {
                    List<Integer> userIdList = relevanceInfo.getAllUserId().stream().map(Integer::parseInt).collect(Collectors.toList());
                    List<UserDetailsInfo> userInfoList = this.userService.findUsersByUserIds(new ArrayList<>(userIdList));

                    //直接删除白名单里面的用户
                    relevanceInfo.getSecondDatas().removeIf(info -> hasWhiteList && userWhitelist.contains(Integer.valueOf(info.getUserId())));
                    relevanceInfo.getThirdDatas().removeIf(info -> hasWhiteList && userWhitelist.contains(Integer.valueOf(info.getUserId())));

                    Map<String, RelevanceInfo.UserAddInInfo> secondMap = relevanceInfo.getSecondDatas().stream().collect(Collectors.toMap(RelevanceInfo.UserAddInInfo::getUserId, a -> a, (k1, k2) -> k1));
                    Map<String, RelevanceInfo.UserAddInInfo> thirdMap = relevanceInfo.getThirdDatas().stream().collect(Collectors.toMap(RelevanceInfo.UserAddInInfo::getUserId, a -> a, (k1, k2) -> k1));
                    for (UserDetailsInfo data : userInfoList) {
                        if (data != null) {
                            String tempUserId = String.valueOf(data.getUserId());
                            if (secondMap.containsKey(tempUserId)) {
                                secondMap.get(tempUserId).setRandomNum(data.getRandomNum());
                                secondMap.get(tempUserId).setNickName(data.getNikeName());
                            }
                            if (thirdMap.containsKey(tempUserId)) {
                                thirdMap.get(String.valueOf(data.getUserId())).setRandomNum(data.getRandomNum());
                                thirdMap.get(String.valueOf(data.getUserId())).setNickName(data.getNikeName());
                            }
                        }
                    }

                    relevanceInfo.setSecondDatas(new ArrayList<>(secondMap.values()));
                    relevanceInfo.setThirdDatas(new ArrayList<>(thirdMap.values()));
                }
                //用完清空，不传给前端
                relevanceInfo.setAllUserId(null);
            }
        }
        //获取转豆的关联数据 2级关联
        if(null != userInfo)
        {
            List<RelevanceInfo.UserAddInInfo> flowDatas =getFlowChipDatas(userInfo.getUserId());
            if(flowDatas.size()>0){
                relevanceInfo.setSecondDatas(removeDupliList(relevanceInfo.getSecondDatas(),flowDatas));
            }
            //获取imsi相同的数据 2级关联
            List<RelevanceInfo.UserAddInInfo> imsiDats =getImsiDatas(userInfo.getUserId());
            if(imsiDats.size()>0){
                relevanceInfo.setSecondDatas(removeDupliList(relevanceInfo.getSecondDatas(),imsiDats));
            }
            //获取相同密码的数据 3级关联
            List<RelevanceInfo.UserAddInInfo> passwordDatas =getPasswordDatas(userInfo.getPassword());
            if(passwordDatas.size()>0){
                relevanceInfo.setThirdDatas(removeDupliList(relevanceInfo.getThirdDatas(),passwordDatas));
            }
            //获取支付密码相同密码的数据 3级关联
            List<RelevanceInfo.UserAddInInfo> payPasswordDatas =getPayPasswordDatas(userInfo.getPayPassword());
            if(payPasswordDatas.size()>0){
                relevanceInfo.setThirdDatas(removeDupliList(relevanceInfo.getThirdDatas(),payPasswordDatas));
            }
        }

        return relevanceInfo;
    }


    /**
     * 2个对象合并并去重
     * @param
     * @return
     */
    private static List<RelevanceInfo.UserAddInInfo> removeDupliList(List<RelevanceInfo.UserAddInInfo> personsA,List<RelevanceInfo.UserAddInInfo> personsB) {
        List<RelevanceInfo.UserAddInInfo> collect = new ArrayList<>();
         collect = Stream.of(personsA, personsB)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
         return collect;
    }

    /*查询此账户的转豆记录*/
    private List<RelevanceInfo.UserAddInInfo> getFlowChipDatas(Integer userID)
    {
        List<RelevanceInfo.UserAddInInfo> flowDatas = new ArrayList<>();
        List<FlowChip> userList = this.userService.getFlowChipListByTransferorUid(userID);
        if(userList!=null && userList.size()>0)
        {
            for (FlowChip flow:userList ) {
                RelevanceInfo.UserAddInInfo userAddInInfo = new  RelevanceInfo.UserAddInInfo();
                userAddInInfo.setUserId(String.valueOf(flow.getTransfereeUid()));
                userAddInInfo.setRandomNum(String.valueOf(flow.getTransfereeUid()));
                userAddInInfo.setNickName(flow.getTransfereeName());
                userAddInInfo.setImeiData(new String[]{"flowChip"});
                userAddInInfo.setIpData(new String[]{"amount",String.valueOf(flow.getChip())});
                flowDatas.add(userAddInInfo);
            }
        }
        return flowDatas;
    }
    /*查询此账户的相同密码*/
    private List<RelevanceInfo.UserAddInInfo> getPasswordDatas(String password)
    {
        List<RelevanceInfo.UserAddInInfo> passwordDatas = new ArrayList<>();
        List<UserDetailsInfo> userList = this.userService.findUserByPassword(password);
        if(userList!=null && userList.size()>0)
        {
            for (UserDetailsInfo user:userList ) {
                RelevanceInfo.UserAddInInfo userAddInInfo = new  RelevanceInfo.UserAddInInfo();
                userAddInInfo.setUserId(String.valueOf(user.getUserId()));
                userAddInInfo.setRandomNum(user.getRandomNum());
                userAddInInfo.setNickName(user.getNikeName());
                userAddInInfo.setImeiData(new String[]{"password"});
                userAddInInfo.setIpData(new String[]{user.getPassword().substring(0,6),"*****",user.getPassword().substring(25,30)});
                passwordDatas.add(userAddInInfo);
            }
        }
        return passwordDatas;
    }
    /*查询此账户的相同支付密码*/
    private List<RelevanceInfo.UserAddInInfo> getPayPasswordDatas(String payPassword)
    {
        List<RelevanceInfo.UserAddInInfo> passwordDatas = new ArrayList<>();
        List<UserDetailsInfo> userList = this.userService.findUserByPayPassword(payPassword);
        if(userList!=null && userList.size()>0)
        {
            for (UserDetailsInfo user:userList ) {
                RelevanceInfo.UserAddInInfo userAddInInfo = new  RelevanceInfo.UserAddInInfo();
                userAddInInfo.setUserId(String.valueOf(user.getUserId()));
                userAddInInfo.setRandomNum(user.getRandomNum());
                userAddInInfo.setNickName(user.getNikeName());
                userAddInInfo.setImeiData(new String[]{"paypassword"});
                userAddInInfo.setIpData(new String[]{user.getPassword().substring(0,6),"*****",user.getPassword().substring(25,30)});
                passwordDatas.add(userAddInInfo);
            }
        }
        return passwordDatas;
    }
    /*查询此账户的相同Imsi*/
    private List<RelevanceInfo.UserAddInInfo> getImsiDatas(Integer userid)
    {
        List<RelevanceInfo.UserAddInInfo> imsiDatas = new ArrayList<>();
        //获取本人的信息
        UserBasicInfo  userBasicInfo = this.userService.findUserBasicsByUserid(userid);
        if(null == userBasicInfo)return imsiDatas;
        //获取相同的imsi设备的用户
        List<UserBasicInfo> userList = this.userService.findUserBasicsByImsi(userBasicInfo.getImsi());
        UserDetailsInfo userDetailsInfo = null;
        if(userList!=null && userList.size()>0)
        {
            for (UserBasicInfo user:userList ) {
                RelevanceInfo.UserAddInInfo userAddInInfo = new  RelevanceInfo.UserAddInInfo();
                userDetailsInfo = this.userService.selectByPrimaryKey(user.getUserId());
                userAddInInfo.setUserId(String.valueOf(user.getUserId()));
                userAddInInfo.setRandomNum(userDetailsInfo.getRandomNum());
                userAddInInfo.setNickName(userDetailsInfo.getNikeName());
                userAddInInfo.setImeiData(new String[]{user.getImsi()});
                userAddInInfo.setIpData(new String[]{"imsi"});
                imsiDatas.add(userAddInInfo);
            }
        }
        return imsiDatas;
    }
    private boolean findPassword(String password)
    {
        List<String> list = new ArrayList<>();
        list.add("e10adc3949ba59abbe56e057f20f883e");
        return  list.contains(password);
    }
    private List<Integer> getUserWhitelist() {
        RoomInfoWhitelistConfig whitelistConfig = this.configCache.roomInfoWhitelistConfig();
        return whitelistConfig == null || whitelistConfig.getWhitelist() == null ? new ArrayList<>(0) : whitelistConfig.getWhitelist();
    }

    @Override
    public void exportUserRelevanceInfo(String randomId, Integer imeiCondition, Integer ipCondition, Integer simulatorCondition, Integer level, HttpServletResponse response) throws IOException {
        RelevanceInfo data = queryUserRelevance(randomId, imeiCondition, ipCondition, simulatorCondition);
        ExcelSheet sheet = new ExcelSheet("玩家关联账号信息");
        List<ExcelModel> models = new ArrayList<>();
        sheet.setModels(models);

        List<ExcelRow> rows = new ArrayList<>();

        String[] titles = new String[]{"用户id", "用户昵称","IMEI","IP"};

        List<RelevanceInfo.UserAddInInfo> exportData = level == 1 ? data.getSecondDatas() : level == 2 ? data.getThirdDatas() : null;

        if (exportData != null && exportData.size() != 0) {
            for(RelevanceInfo.UserAddInInfo d : exportData){
                ExcelRow row = new ExcelRow()
                        .add(d.getUserId())
                        .add(d.getNickName())
                        .add(Arrays.toString(d.getImeiData()).replaceAll("\\[","").replaceAll("\\]",""))
                        .add(Arrays.toString(d.getIpData()).replaceAll("\\[","").replaceAll("\\]",""));
                rows.add(row);
            }
        }

        ExcelModel model = ExcelModel.builder()
                .titles(Lists.newArrayList(titles))
                .rows(rows)
                .afterBlankLine(1)
                .build();
        models.add(model);

        export(sheet, response);
    }

    private void export(ExcelSheet sheet, HttpServletResponse response) throws IOException {
        String fileName = URLEncoder.encode(sheet.getName(), "UTF-8");
        response.setHeader("Content-Type", "application/octet-stream");
        response.setHeader("Content-Disposition", String.format("attachment;fileName=\"%s.%s\"", fileName, "xlsx"));
        ExcelUtils.write(response.getOutputStream(), sheet);
    }

    private RelevanceInfo getRelevanceInfo(int userId,boolean needIp,boolean needImei,int dataSource) {
        RelevanceInfo relevanceInfo = new RelevanceInfo(dataSource);

        Set<String> allUserId = new HashSet<>();//所有userid

        try {
            allUserId.add(String.valueOf(userId));

            //查询并加载一级关联数据
            Query firstFilter = new Query().addCriteria(Criteria.where("user_id").in(userId))//时间段
                    .with(new Sort(Sort.Direction.DESC, "time"));//降序
            if(dataSource != 2){
                firstFilter.addCriteria(Criteria.where("virtual").in(dataSource));
            }

            List<String> imeiValues = new ArrayList<>();//关联条件
            List<String> ipValues = new ArrayList();//关联条件

            List<AddRequestInfo> firstMongoDataList = this.mongoTemplate.find(firstFilter, AddRequestInfo.class);
            relevanceInfo.setFirstData(filtrateData(firstMongoDataList,needIp,needImei,ipValues,imeiValues));//装载第一级数据

            log.debug("getRelevanceInfo==>" + Arrays.toString(imeiValues.toArray()) + ",===>" + Arrays.toString(ipValues.toArray()));

            //查询并加载二级关联数据

            Query secondFilter = new Query().with(new Sort(Sort.Direction.DESC, "time"));//降序
            List<String> secondImeiValues = new ArrayList<>();//关联条件
            List<String> secondIpValues = new ArrayList<>();//关联条件
            Map<String,Set<String>> imeiData = new HashMap<>();
            Map<String,Set<String>> ipData = new HashMap<>();
            List<RelevanceInfo.UserAddInInfo> data = new ArrayList<>();
            if(needImei){
                secondFilter.addCriteria(Criteria.where("imei").in(imeiValues));
                if(dataSource != 2){
                    secondFilter.addCriteria(Criteria.where("virtual").in(dataSource));
                }

                List<AddRequestInfo> secondMongoDataList = this.mongoTemplate.find(secondFilter, AddRequestInfo.class);

                for(GpsInfo info : filtrateData(secondMongoDataList,false,needImei,null,secondImeiValues)){
                    if(imeiData.containsKey(info.getUserId())){
                        imeiData.get(info.getUserId()).add(info.getImei());
                    }else{
                        Set<String> d = new HashSet<>();
                        d.add(info.getImei());
                        imeiData.put(info.getUserId(),d);
                    }
                }
            }

            if(needIp){
                secondFilter = new Query().with(new Sort(Sort.Direction.DESC, "time"));//降序
                secondFilter.addCriteria(Criteria.where("ip").in(ipValues));
                if(dataSource != 2){
                    secondFilter.addCriteria(Criteria.where("virtual").in(dataSource));
                }

                List<AddRequestInfo> secondMongoDataList = this.mongoTemplate.find(secondFilter, AddRequestInfo.class);

                for(GpsInfo info : filtrateData(secondMongoDataList,needIp,false,secondIpValues,null)){
                    if(ipData.containsKey(info.getUserId())){
                        ipData.get(info.getUserId()).add(info.getIp());
                    }else{
                        Set<String> d = new HashSet<>();
                        d.add(info.getIp());
                        ipData.put(info.getUserId(),d);
                    }
                }
            }

            if(imeiData.size() > 0 || ipData.size() > 0){//必须要有关联数据才计算
                if(ipData.size() >= imeiData.size()){
                    for(String uid : ipData.keySet()){
                        String[] ipArray = new String[ipData.get(uid).size()];
                        if(imeiData.containsKey(uid)){//imei关联数据也存在，取出并删除
                            String[] imeiArray = new String[imeiData.get(uid).size()];
                            data.add(new RelevanceInfo.UserAddInInfo(uid,imeiData.get(uid).toArray(imeiArray),ipData.get(uid).toArray(ipArray)));
                            imeiData.remove(uid);
                        }else{//否则只有ip关联
                            data.add(new RelevanceInfo.UserAddInInfo(uid,ipData.get(uid).toArray(ipArray),false));
                        }
                    }

                    for(String uid : imeiData.keySet()){//筛选imei数据
                        String[] imeiArray = new String[imeiData.get(uid).size()];
                        data.add(new RelevanceInfo.UserAddInInfo(uid,imeiData.get(uid).toArray(imeiArray),true));
                    }
                }else{
                    for(String uid : imeiData.keySet()){
                        String[] imeiArray = new String[imeiData.get(uid).size()];
                        if(ipData.containsKey(uid)){//ip关联数据也存在，取出并删除
                            String[] ipArray = new String[ipData.get(uid).size()];
                            data.add(new RelevanceInfo.UserAddInInfo(uid,imeiData.get(uid).toArray(imeiArray),ipData.get(uid).toArray(ipArray)));
                            ipData.remove(uid);
                        }else{//否则只有imei关联
                            data.add(new RelevanceInfo.UserAddInInfo(uid,imeiData.get(uid).toArray(imeiArray),true));
                        }
                    }

                    for(String uid : ipData.keySet()){//筛选imei数据
                        String[] ipArray = new String[ipData.get(uid).size()];
                        data.add(new RelevanceInfo.UserAddInInfo(uid,ipData.get(uid).toArray(ipArray),false));
                    }
                }
            }

            relevanceInfo.setSecondDatas(data);//装载二级关联数据

            //查询并加载三级关联数据

            //清空二级关联数据
            imeiData = new HashMap<>();
            ipData = new HashMap<>();
            data = new ArrayList<>();

            Query thirdFilter = new Query().with(new Sort(Sort.Direction.DESC, "time"));//降序
            if(needImei){
                thirdFilter.addCriteria(Criteria.where("imei").in(secondImeiValues));
                if(dataSource != 2){
                    thirdFilter.addCriteria(Criteria.where("virtual").in(dataSource));
                }

                List<AddRequestInfo> thirMongoDataList = this.mongoTemplate.find(thirdFilter, AddRequestInfo.class);

                for(GpsInfo info : filtrateData(thirMongoDataList,false,false,null,null)){
                    if(imeiData.containsKey(info.getUserId())){
                        imeiData.get(info.getUserId()).add(info.getImei());
                    }else{
                        Set<String> d = new HashSet<>();
                        d.add(info.getImei());
                        imeiData.put(info.getUserId(),d);
                    }
                }
            }

            if(needIp){
                thirdFilter = new Query().with(new Sort(Sort.Direction.DESC, "time"));//降序
                thirdFilter.addCriteria(Criteria.where("ip").in(secondIpValues));
                if(dataSource != 2){
                    thirdFilter.addCriteria(Criteria.where("virtual").in(dataSource));
                }

                List<AddRequestInfo> thirMongoDataList = this.mongoTemplate.find(thirdFilter, AddRequestInfo.class);

                for(GpsInfo info : filtrateData(thirMongoDataList,false,false,null,null)){
                    if(ipData.containsKey(info.getUserId())){
                        ipData.get(info.getUserId()).add(info.getIp());
                    }else{
                        Set<String> d = new HashSet<>();
                        d.add(info.getIp());
                        ipData.put(info.getUserId(),d);
                    }
                }
            }

            if(imeiData.size() > 0 || ipData.size() > 0){//必须要有关联数据才计算
                if(ipData.size() >= imeiData.size()){
                    for(String uid : ipData.keySet()){//筛选ip数据
                        String[] ipArray = new String[ipData.get(uid).size()];
                        if(imeiData.containsKey(uid)){//imei关联数据也存在，取出并删除
                            String[] imeiArray = new String[imeiData.get(uid).size()];
                            data.add(new RelevanceInfo.UserAddInInfo(uid,imeiData.get(uid).toArray(imeiArray),ipData.get(uid).toArray(ipArray)));
                            imeiData.remove(uid);
                        }else{//否则只有ip关联
                            data.add(new RelevanceInfo.UserAddInInfo(uid,ipData.get(uid).toArray(ipArray),false));
                        }
                        allUserId.add(uid);
                    }

                    for(String uid : imeiData.keySet()){//筛选imei数据
                        String[] imeiArray = new String[imeiData.get(uid).size()];
                        data.add(new RelevanceInfo.UserAddInInfo(uid,imeiData.get(uid).toArray(imeiArray),true));
                        allUserId.add(uid);
                    }
                }else{
                    for(String uid : imeiData.keySet()){//筛选imei数据
                        String[] imeiArray = new String[imeiData.get(uid).size()];
                        if(ipData.containsKey(uid)){//ip关联数据也存在，取出并删除
                            String[] ipArray = new String[ipData.get(uid).size()];
                            data.add(new RelevanceInfo.UserAddInInfo(uid,imeiData.get(uid).toArray(imeiArray),ipData.get(uid).toArray(ipArray)));
                            ipData.remove(uid);
                        }else{//否则只有imei关联
                            data.add(new RelevanceInfo.UserAddInInfo(uid,imeiData.get(uid).toArray(imeiArray),true));
                        }
                        allUserId.add(uid);
                    }

                    for(String uid : ipData.keySet()){//筛选imei数据
                        String[] ipArray = new String[ipData.get(uid).size()];
                        data.add(new RelevanceInfo.UserAddInInfo(uid,ipData.get(uid).toArray(ipArray),false));
                        allUserId.add(uid);
                    }
                }
            }

            relevanceInfo.setAllUserId(allUserId);

            relevanceInfo.setThirdDatas(data);//装载三级关联账户数据

        } catch (Exception e) {
            log.debug("getRelevanceInfo list error:" + e);
        }

        return relevanceInfo;
    }

    private List<GpsInfo> filtrateData(List<AddRequestInfo> firstMongoDataList, boolean needIp, boolean needImei, List<String> ipValues, List<String> imeiValues){
        List<GpsInfo> gpsInfos = new ArrayList<>();

        for(AddRequestInfo d : firstMongoDataList){
            //TODO 这里需要增加个社区名的数据返回，数据源依赖于该表存入数据时
            if(needIp && null != ipValues){
                ipValues.add(d.getIp());
            }
            if(needImei && null != imeiValues){
                imeiValues.add(d.getImei());
            }
            gpsInfos.add(GpsInfo.builder().userId(String.valueOf(d.getUserId())).ip(d.getIp()).imei(d.getImei()).time(String.valueOf(d.getTime())).build());
        }

        return gpsInfos;
    }

    @Override
    public RelevanceTotal queryUserRelevanceTotal(UserInfo operationUser, Set<Integer> userIds, Integer clubRandomId, LocalDateTime startTime, LocalDateTime endTime) {
        Integer clubId = null;
        if (clubRandomId != null) {
            ClubRecord club = clubRecordService.findByRandomId(clubRandomId);
            clubId = club == null ? null : club.getId();
        }

        UserStatsParams params = UserStatsParams.builder()
                .operationUser(operationUser)
                .userIds(userIds)
                .clubId(clubId)
                .coefficient(0)
                .startTime(startTime)
                .endTime(endTime)
                .page(1)
                .size(1)
                .build();
        UserTotalStat userStats = gameDetailService.findUserStats(params);
        return RelevanceTotal.builder()
                .totalBring(userStats.getTotalBring())
                .totalInsurance(userStats.getTotalInsurance())
                .totalIntegral(userStats.getTotalIntegral())
                .totalHands(userStats.getTotalHands())
                .build();
    }

    @Override
    public Map<Integer, FirstLevelRelevanceCount> findFirstLevelRelevanceCountMap(Set<Integer> userIds) {
        if (userIds.isEmpty()) {
            return new HashMap<>(0);
        }
        //查询并加载一级关联数据
        Query firstFilter = new Query().addCriteria(Criteria.where("user_id").in(userIds))
                .with(new Sort(Sort.Direction.DESC, "time"));

        List<AddRequestInfo> list = this.mongoTemplate.find(firstFilter, AddRequestInfo.class);

        Map<Integer, FirstLevelRelevanceCount> map = new HashMap<>(userIds.size());
        Set<String> imeiSet = new HashSet<>();
        Set<String> ipSet = new HashSet<>();
        list.forEach(info -> {
            FirstLevelRelevanceCount count = map.computeIfAbsent(info.getUserId(), FirstLevelRelevanceCount::new);
            if (!StringUtils.isEmpty(info.getImei())) {
                count.getImeiSet().add(info.getImei());
                imeiSet.add(info.getImei());
            }
            if (!StringUtils.isEmpty(info.getIp())) {
                count.getIpSet().add(info.getIp());
                ipSet.add(info.getIp());
            }
        });
        //查询有哪些用户的 imei 和 ip 和当前用户的相关
        Query imeiFilter = new Query().addCriteria(Criteria.where("imei").in(imeiSet))
                .with(new Sort(Sort.Direction.DESC, "time"));
        List<AddRequestInfo> imeiInfoList = this.mongoTemplate.find(imeiFilter, AddRequestInfo.class);
        //转成map，一个imei对应多个userId
        Map<String, Set<Integer>> imeiUserMap = imeiInfoList.stream().filter(info -> Objects.nonNull(info.getImei()))
                .collect(Collectors.groupingBy(AddRequestInfo::getImei, Collectors.mapping(AddRequestInfo::getUserId, Collectors.toSet())));

        List<String> ipList = new ArrayList<>(ipSet);
        Query ipFilter = new Query().addCriteria(Criteria.where("ip").in(ipList))
                .with(new Sort(Sort.Direction.DESC, "time"));
        List<AddRequestInfo> ipInfoList = this.mongoTemplate.find(ipFilter, AddRequestInfo.class);
        //转成map，一个ip对应多个userId
        Map<String, Set<Integer>> ipUserMap = ipInfoList.stream().filter(info -> Objects.nonNull(info.getIp()))
                .collect(Collectors.groupingBy(AddRequestInfo::getIp, Collectors.mapping(AddRequestInfo::getUserId, Collectors.toSet())));

        Set<Integer> defaultSet = new HashSet<>(0);
        map.forEach((userId, count) -> {
            Set<Integer> imeiUsers = new HashSet<>();
            Set<Integer> ipUsers = new HashSet<>();
            count.getImeiSet().forEach(imei -> imeiUsers.addAll(imeiUserMap.getOrDefault(imei, defaultSet)));
            count.getIpSet().forEach(ip -> ipUsers.addAll(ipUserMap.getOrDefault(ip, defaultSet)));
            //数量不包含自己
            imeiUsers.remove(userId);
            ipUsers.remove(userId);
            count.setEquipmentCount(imeiUsers.size());
            count.setIpCount(ipUsers.size());
        });

        return map;
    }
}
