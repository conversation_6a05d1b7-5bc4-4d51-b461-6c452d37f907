package com.allinpokers.yunying.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Getter;

/**
 * 俱乐部记录表  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClubRecord {
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private Integer id;

    /**
     * 俱乐部随机id，用于展示
     */
    @ApiModelProperty("俱乐部随机id，用于展示")
    private Integer randomId;

    /**
     * 俱乐部名称
     */
    @ApiModelProperty("俱乐部名称")
    private String name;

    /**
     * 俱乐部头像
     */
    @ApiModelProperty("俱乐部头像")
    private String header;

    /**
     * 俱乐部描述
     */
    @ApiModelProperty("俱乐部描述")
    private String description;

    /**
     * 俱乐部部长ID
     */
    @ApiModelProperty("俱乐部部长ID")
    private String creator;

    /**
     * 俱乐部的状态 0正常1是关闭
     */
    @ApiModelProperty("俱乐部的状态 0正常1是关闭")
    private Short clubStatus;

    /**
     * 俱乐部当前成员数
     */
    @ApiModelProperty("俱乐部当前成员数")
    private Integer clubMembers;

    /**
     * 俱乐部人员上限
     */
    @ApiModelProperty("俱乐部人员上限")
    private Integer upperLimit;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 只许部长组局
     */
    @ApiModelProperty("只许部长组局")
    private Integer onlyChief;

    /**
     * 俱乐部所属地区编码
     */
    @ApiModelProperty("俱乐部所属地区编码")
    private String areaId;

    /**
     * 俱乐部所拥有基金总金额
     */
    @ApiModelProperty("俱乐部所拥有基金总金额")
    private Integer fund;

    /**
     * 俱乐部初始信用值
     */
    @ApiModelProperty("俱乐部初始信用值")
    private Integer initialCredit;

    /**
     * 俱乐部信用值状态(1: 开；0: 无限制)
     */
    @ApiModelProperty("俱乐部信用值状态(1: 开；0: 无限制)")
    private Integer creditStatus;

    /**
     * totalInsure
     */
    @ApiModelProperty("totalInsure")
    private Integer totalInsure;

    /**
     * 部落状态（0:默认未创建部落；1：已创建部落，且未解散；2：已创建部落，且已解散）
     */
    @ApiModelProperty("部落状态（0:默认未创建部落；1：已创建部落，且未解散；2：已创建部落，且已解散）")
    private Integer tribeStatus;

    /**
     * 俱乐部加入部落的个数（1个俱乐部最多只能加5个部落）
     */
    @ApiModelProperty("俱乐部加入部落的个数（1个俱乐部最多只能加5个部落）")
    private Integer tribeCount;

    /**
     * 系数
     */
    @ApiModelProperty("系数")
    private Integer ratio;

    /**
     * 系数修改时间
     */
    @ApiModelProperty("系数修改时间")
    private LocalDateTime ratioTime;

    /**
     * 是否官方俱乐部（0：不是，1：是）
     */
    @ApiModelProperty("是否官方俱乐部（0：不是，1：是）")
    private Integer officailClub;

    /**
     * 是否冻结基金 (0 否 1 是)
     */
    @ApiModelProperty("是否冻结基金 (0 否 1 是)")
    private Short frozen;

    /**
     * 百分比，此值10代表百分之10
     */
    @ApiModelProperty("百分比，此值10代表百分之10")
    private Integer profit;

    /**
     * 俱乐部交易模式,0-俱乐部充值,1-平台充值(默认)
     */
    @ApiModelProperty("俱乐部交易模式,0-俱乐部充值,1-平台充值(默认)")
    private Integer transType;

    /**
     * 交易模式修改时间戳
     */
    @ApiModelProperty("交易模式修改时间戳")
    private Long modTransTypeTime;

    /**
     * 代充渠道标识(0-不是代充渠道,1-是代充渠道)
     */
    @ApiModelProperty("代充渠道标识(0-不是代充渠道,1-是代充渠道)")
    private Boolean payChannelFlag;

    /**
     * 开启战绩返佣:0-关闭,1-开启
     */
    @ApiModelProperty("开启战绩返佣:0-关闭,1-开启")
    private Boolean openRebate;

    /**
     * 战绩返佣比例
     */
    @ApiModelProperty("战绩返佣比例")
    private Double rebateRatio;

    /**
     * 战绩返佣总额(金豆)
     */
    @ApiModelProperty("战绩返佣总额(金豆)")
    private Long rebateSum;

    /**
     * 牌局累计分润,单位：记分牌
     */
    @ApiModelProperty("牌局累计分润,单位：记分牌")
    private BigDecimal roomProfit;

    /**
     * 俱乐部积分房的总服务费用
     */
    @ApiModelProperty("俱乐部积分房的总服务费用")
    private Integer clubRoomCharge;

    /**
     * 俱乐部积分房的总保险费用
     */
    @ApiModelProperty("俱乐部积分房的总保险费用")
    private Integer clubRoomInsureTotal;

    /**
     * 百分比，此值10代表百分之10
     */
    @ApiModelProperty("百分比，此值10代表百分之10")
    private Integer tribeProfit;

    /**
     * chip
     */
    @ApiModelProperty("chip")
    private Integer chip;

    /**
     * 充值费用%
     */
    @ApiModelProperty("充值费用%")
    private Integer rechargeFeeRate;

    /**
     * insuranceLossLimit
     */
    @ApiModelProperty("insuranceLossLimit")
    private Integer insuranceLossLimit;

    @ApiModelProperty(value = "是否使用自定义头像 0否 1是")
    private Integer useCustom;
    @ApiModelProperty(value = "自定义头像")
    private String customUrl;

    /**
     * totalAmount
     */
    @ApiModelProperty("totalAmount")
    private Integer totalAmount;

    @Getter
    public enum ClubStatus {
        OPEN(0, "正常"),
        CLOSE(1, "关闭");

        private final int value;
        private final String desc;

        ClubStatus(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    @Getter
    public enum UseCustom {
        NO(0, "否"),
        YES(1, "是");

        private final int value;
        private final String desc;

        UseCustom(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }
    }
}