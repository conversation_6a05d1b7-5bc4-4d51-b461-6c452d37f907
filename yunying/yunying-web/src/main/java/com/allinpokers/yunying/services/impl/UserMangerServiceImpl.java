package com.allinpokers.yunying.services.impl;

import com.alibaba.fastjson.JSONObject;
import com.allinpokers.yunying.appmessage.bean.AppMessageNotice;
import com.allinpokers.yunying.appmessage.notice.NoticeCache;
import com.allinpokers.yunying.appmessage.service.AppMessageNoticeService;
import com.allinpokers.yunying.dao.crazypoker.*;
import com.allinpokers.yunying.entity.crazypoker.*;
import com.allinpokers.yunying.entity.crazypoker.example.*;
import com.allinpokers.yunying.entity.plus.user.SmallUserInfo;
import com.allinpokers.yunying.enu.ResponseCodeEnum;
import com.allinpokers.yunying.model.request.PageReq;
import com.allinpokers.yunying.model.request.user.*;
import com.allinpokers.yunying.model.request.userdataedit.LeaveTableBean;
import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.model.response.PageBean;
import com.allinpokers.yunying.model.response.user.*;
import com.allinpokers.yunying.oss.service.OssService;
import com.allinpokers.yunying.permission.security.UserInfo;
import com.allinpokers.yunying.rabbitmq.client.MessageSender;
import com.allinpokers.yunying.rabbitmq.client.bean.ForbbidenUser;
import com.allinpokers.yunying.rabbitmq.client.bean.SystemMessage;
import com.allinpokers.yunying.rabbitmq.client.bean.user.InfoModMessage;
import com.allinpokers.yunying.rabbitmq.constant.EMessageChannelCode;
import com.allinpokers.yunying.rabbitmq.constant.EMessageCode;
import com.allinpokers.yunying.service.RedisService;
import com.allinpokers.yunying.services.MessageUnreadService;
import com.allinpokers.yunying.services.UserAccountService;
import com.allinpokers.yunying.services.UserDetailsInfoService;
import com.allinpokers.yunying.services.UserMangerService;
import com.allinpokers.yunying.services.model.UserDetails;
import com.allinpokers.yunying.sms.constant.ESmsType;
import com.allinpokers.yunying.sms.service.ISmsService;
import com.allinpokers.yunying.useronline.service.UserLevelService;
import com.allinpokers.yunying.util.JsonUtils;
import com.allinpokers.yunying.util.PhoneNumUtil;
import com.allinpokers.yunying.util.excel.ExcelModel;
import com.allinpokers.yunying.util.excel.ExcelRow;
import com.allinpokers.yunying.util.excel.ExcelSheet;
import com.allinpokers.yunying.util.excel.ExcelUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Log4j2
public class UserMangerServiceImpl implements UserMangerService {

    @Resource
    private UserDetailsInfoService userDetailsInfoService;
    @Resource
    private UserDetailsInfoDao userDetailsInfoDao;
    @Resource
    private UserBasicInfoDao userBasicInfoDao;
    @Resource
    private RoomManagerDao roomManagerDao;
    @Resource
    private MessageSender messageSender;
    @Resource
    private MessageUnreadService messageUnreadService;
    @Resource
    private MessageSystemRecordDao messageSystemRecordDao;
    @Resource
    private ISmsService iSmsService;
    @Resource
    private UserAccountService userAccountService;
    @Resource
    private AutoCreateUserTaskDao autoCreateUserTaskDao;
    @Resource
    private AutoCreateUserLogDao autoCreateUserLogDao;
    @Resource
    private MessageReceiveUserDao messageReceiveUserDao;
    @Resource
    private UserLevelConfigDao userLevelConfigDao;
    @Resource
    private UserLevelService userLevelService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private OssService ossService;
    @Resource
    private AppMessageNoticeService appMessageNoticeService;

    @Resource
    NoticeCache noticeCache;

    @Resource
    UserGoldAccountDao userGoldAccountDao;

    @Override
    public CommonRespon updateClubUserStatus(UpdateUserStatusReq req) {
        log.info("冻结|解冻用户请求 data={}", JsonUtils.write(req));
        //操作类型 0-冻结,1-正常
        Integer op = req.getOp();
        if (op == null || op < 0 || op > 1) {
            return CommonRespon.failure(ResponseCodeEnum.OP_NOT_EXIST);
        }

        if (StringUtils.isEmpty(req.getOperatorUser().getMobileAreaCode()) || StringUtils.isEmpty(req.getOperatorUser().getMobileNo())) {
            return CommonRespon.failure(ResponseCodeEnum.USER_HAS_NO_PHONE);
        }
        boolean checkCode = this.iSmsService.checkSmsCode(ESmsType.FROZEN_USER.getValue(), req.getOperatorUser().getMobileNo(), req.getCode(),
                req.getOperatorUser().getMobileAreaCode());
        if (!checkCode) {
            return CommonRespon.failure(ResponseCodeEnum.SMS_CAPTCHA_CODE_ERROR);
        }

        UserDetailsInfo user = userDetailsInfoService.findUserByUserRamdomId(req.getUserId());
        //检查用户是否存在
        if (user == null) {
            return CommonRespon.failure(ResponseCodeEnum.ACCOUNT_NOT_EXIST);
        }
        if (op.equals(0)) {
            //冻结: 钱包 和 登录限制
            this.updateWalletStatus(user.getUserId(), 2);
            this.updateLoginStatus(user.getUserId(), 0);
            this.sendSystemMsg("YY-" + req.getOperatorUser().getId(), user.getUserId().toString(), req.getOperatorUser().getUsername(),
                    EMessageCode.SYSTEM_USER_FORZEN, EMessageChannelCode.JPUSH);
        }
        if (op.equals(1)) {
            //正常: 钱包 和 登录限制
            this.updateWalletStatus(user.getUserId(), 1);
            this.updateLoginStatus(user.getUserId(), 1);
        }
        return CommonRespon.success();
    }


    /**
     * 玩家列表
     *
     * @param req
     * @return
     */
    @Override
    public CommonRespon<PageBean<ClubUserBean>> userInfoList(UserSearchReq req) {
        List<String> list = null;
        if (!StringUtils.isEmpty(req.getContent())) {
            list = Arrays.asList(req.getContent().trim().split(","));
        }

        PageHelper.startPage(req.getPage(), req.getSize());
        PageBean<ClubUserBean> data = new PageBean<>();
        LocalDateTime start = null;
        LocalDateTime end = null;
        if (req.getRegTimeStart() != null && req.getRegTimeEnd() != null) {
            start = LocalDateTime.of(req.getRegTimeStart(), LocalTime.MIN);
            end = LocalDateTime.of(req.getRegTimeEnd(), LocalTime.MAX);
        }
        List<SmallUserInfo> dbList = new ArrayList<>();
        if (req.getClubId() != null && req.getClubId() <= 0) {
            dbList = userDetailsInfoDao.selectUserNoClubList(list, req.getRegType(), start, end);
        } else {
            dbList = userDetailsInfoDao.selectUserClubList(list, req.getClubId(), req.getRegType(), start, end);
        }
        PageInfo<SmallUserInfo> pageInfo = new PageInfo<>(dbList);

        List<ClubUserBean> userBeanList = new ArrayList<>();
        this.changeDbSmallUserInfoToView(dbList, userBeanList);
        
        if (!CollectionUtils.isEmpty(userBeanList)) {
            // 获取所有userId, userGoldAccountDao.selectByUserIds(userIdList);
            List<Integer> userIdList = userBeanList.stream()
                    .map(BaseUserBean::getUserId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            log.info("userIdList: {}", userIdList);

            List<UserGoldAccount> userGoldAccounts = userGoldAccountDao.selectByUserIds(userIdList);
            Map<Integer, UserGoldAccount> userGoldAccountMap = userGoldAccounts.stream()
                    .collect(Collectors.toMap(UserGoldAccount::getUserId, e -> e));

            log.info("userGoldAccounts size={} userBeanList size={}", userGoldAccounts.size(), userBeanList.size());
            log.info("userGoldAccountMap = {}", JSONObject.toJSONString(userGoldAccountMap));

            // 设置金币数量
            userBeanList.forEach(e -> {
                UserGoldAccount account = userGoldAccountMap.get(e.getUserId());
                if (account != null) {
                    e.setGold(account.getGold());
                }
            });
        }

        data.setBeanList(userBeanList).setPages(pageInfo.getPages()).setLastPage(pageInfo.isIsLastPage()).setTotal(pageInfo.getTotal());
        return CommonRespon.success(data);

    }

    @Override
    public CommonRespon<PageBean<LeaveTableBean>> userInfoLeaveTableList(UserLeaveTableReq req) {
        List<Integer> list=new ArrayList<>();
        if (req.getUserRandomId()==null) {
            Set<String> leaveTableRooms = redisService.getUserLeaveTableRooms();
            for (String s : leaveTableRooms) {
                list.add(Integer.valueOf(s));
            }
        }

        Map<Integer, String> rooms = redisService.getRooms();
        PageHelper.startPage(req.getPage(), req.getSize());
        PageBean<LeaveTableBean> data = new PageBean<>();
        List<SmallUserInfo> dbList = userDetailsInfoDao.selectUserLeaveTableList(list,req.getUserRandomId());
        List<LeaveTableBean> userBeanList = new ArrayList<>();
        PageInfo<SmallUserInfo> pageInfo = new PageInfo<>(dbList);
        if (rooms.size()!=0){
            for (SmallUserInfo userInfo:dbList){
                Set<Integer> leaveTableRooms1 = redisService.getLeaveTableRooms(userInfo.getUserId());
                for (Integer s : leaveTableRooms1) {
                    if (rooms.containsKey(s)) {
                        String s1 = rooms.get(s);
                        JSONObject jsonObject = JSONObject.parseObject(s1);
                        try {
                            userBeanList.add(LeaveTableBean.builder().roomId(jsonObject.getInteger("roomId"))
                                    .roomName(jsonObject.getString("roomName"))
                                    .mangzhu(jsonObject.getInteger("mangzhu"))
                                    .roomType(jsonObject.getInteger("roomPath"))
                                    .userId(userInfo.getUserId()).userNickname(userInfo.getNickName())
                                    .userRandomId(userInfo.getRandomNum()).build());
                        } catch (NullPointerException e) {
                            log.error("json解析错误========>" + e.getMessage());
                        }
                    }

                }
            }
        }
        PageInfo<LeaveTableBean> resultPageInfo = new PageInfo<>(userBeanList);
        data.setBeanList(userBeanList).setPages(resultPageInfo.getPages()).setLastPage(resultPageInfo.isIsLastPage()).setTotal(resultPageInfo.getTotal());
        return CommonRespon.success(data);
    }

    private void changeDbSmallUserInfoToView(List<SmallUserInfo> dbList, List<ClubUserBean> userBeanList) {
        if (dbList != null && !dbList.isEmpty()) {
            //1.获取用户的金币数量
            List<Integer> userIdList = dbList.stream().map(e -> e.getUserId()).collect(Collectors.toList());
            Map<Integer, UserAccount> userAccountMap = this.userAccountService.getUserAccountMapByUserIdList(userIdList);
            UserAccount defaultUserAccount = new UserAccount();
            defaultUserAccount.setChip(0);
            defaultUserAccount.setNotExtractChip(0);
            //2.用户转换视图表示
            dbList.forEach(e -> {
                ClubUserBean item = new ClubUserBean();
                UserAccount account = userAccountMap.getOrDefault(e.getUserId(), defaultUserAccount);
                //计算账户状态: 钱包状态 和 登录限制都禁用才视为账户禁用
                Integer status = e.getForbid().equals(0) && account.getStatus().equals(2) ? 0 : 1;
                item.setChip(account.getChip() + account.getNotExtractChip())
                        .setStatu(status)
                        .setWalletStatus(account.getStatus())
                        .setLoginStatus(e.getForbid())
                        .setNotExtractChip(account.getNotExtractChip())
                        .setCanExtractChip(account.getChip())
                        .setClubName(e.getClubName())
                        .setUserType(e.getPromotionType())
                        .setRandomId(e.getRandomNum())
                        .setNickName(e.getNickName())
                        .setUserLabel(e.getUserLabel());

                item.setPhone(PhoneNumUtil.decoder(e.getPhone()));
                item.setRegTime(e.getCreatedTime());
                //设置钱包状态
                item.setUserId(account.getUserId());
                item.setCreateTime(e.getCreatedTime());
                item.setWalletStatus(account.getStatus());
                item.setOldNickName(e.getOldNickName());
                item.setInnerType(e.getInnerType());
                // 头像
                item.setUserHead(e.getUserHead());
                item.setUseCustom(e.getUseCustom());
                item.setCustomUrl(e.getCustomUrl());
                // 注册渠道
                item.setRegType(e.getRegType());
                userBeanList.add(item);
            });
        }
    }

    @Override
    public CommonRespon<AgentLogigResp> agentLogin(AgentLoginReq req) {
        log.info("代理登录 data{} phone={}", JsonUtils.write(req), PhoneNumUtil.encoder(req.getPhoneNum()));
        UserDetailsInfo userDetailsInfo = this.userDetailsInfoService.findUserByUserPone(PhoneNumUtil.encoder(req.getPhoneNum()));
        if (userDetailsInfo == null) {
            return CommonRespon.failure(ResponseCodeEnum.USER_NOT_EXIST);
        }
        if (StringUtils.isEmpty(req.getPwd()) || !userDetailsInfo.getPassword().toLowerCase().equals(req.getPwd().toLowerCase())) {
            return CommonRespon.failure(ResponseCodeEnum.USER_PWD_ERROR);
        }
        AgentLogigResp data = new AgentLogigResp();
        data.setUser_id(userDetailsInfo.getUserId());
        data.setRandomId(userDetailsInfo.getRandomNum());
        data.setNick_name(userDetailsInfo.getNikeName());

        return CommonRespon.success(data);
    }


    /**
     * 给开放创建牌局权限
     *
     * @param userId 用户id
     * @return 结果
     */
    @Override
    public CommonRespon openUserCreateRoomOp(String userId) {

        if (StringUtils.isEmpty(userId)) {
            return CommonRespon.failure(ResponseCodeEnum.PARAM_ERROR);
        }
        UserDetailsInfo userDetailsInfo = this.userDetailsInfoService.findUserByUserRamdomId(userId);
        if (userDetailsInfo == null) {
            return CommonRespon.failure(ResponseCodeEnum.RECORD_NOT_EXIST);
        }

        RoomManagerExample example = new RoomManagerExample();
        example.createCriteria().andUserIdEqualTo(userDetailsInfo.getUserId());
        List<RoomManager> roomManagerList = this.roomManagerDao.selectByExample(example);
        //已经有创建牌局权限了
        if (roomManagerList != null && roomManagerList.size() > 0) {
            log.info("roomManagerList {}", JsonUtils.write(roomManagerList));
            return CommonRespon.failure(ResponseCodeEnum.USER_HAS_CREATE_ROOM_OP);
        }

        //查询符合条件的记录
        int count = this.userDetailsInfoDao.selectUserCount(userDetailsInfo.getUserId());
        if (count != 1) {
            log.error("给开放创建牌局权限 用户权限检查不通过 记录有={} 条", count);
            return CommonRespon.failure(ResponseCodeEnum.USER_CANNOT_CREATE_ROOM);
        }

        //添加到开局权限表
        RoomManager roomManager = RoomManager.builder().createdTime(LocalDateTime.now())
                .userId(userDetailsInfo.getUserId())
                .build();
        this.roomManagerDao.insertSelective(roomManager);
        return CommonRespon.success();
    }

    /**
     * 开房权限的用户列表
     *
     * @param content 关键字
     * @param page    当前页
     * @param size    页面大小
     * @return 结果
     */
    @Override
    public CommonRespon<PageBean<ClubUserBean>> createRoomUserList(String content, Integer page, Integer size) {
        List<String> list = null;
        if (!StringUtils.isEmpty(content)) {
            list = Arrays.asList(content.trim().split(","));
        }
        PageHelper.startPage(page, size);
        PageBean<ClubUserBean> data = new PageBean<>();
        List<SmallUserInfo> dbList = this.roomManagerDao.selectCreateRoomUserList(list);
        if (dbList == null || dbList.isEmpty()) {
            return CommonRespon.success(data);
        }

        PageInfo<SmallUserInfo> pageInfo = new PageInfo<>(dbList);
        List<ClubUserBean> userBeanList = new ArrayList<>();
        this.changeDbSmallUserInfoToView(dbList, userBeanList);
        data.setBeanList(userBeanList).setPages(pageInfo.getPages()).setLastPage(pageInfo.isIsLastPage()).setTotal(pageInfo.getTotal());
        return CommonRespon.success(data);
    }

    /**
     * 关闭用户创建牌局权限
     *
     * @param ids
     * @return
     */
    @Override
    public CommonRespon closeCreateRoomOp(Set<String> ids) {

        log.info("关闭用户创建牌局权限  ids={}", JsonUtils.write(ids));
        //批量删除记录
        List<UserDetailsInfo> userDetailsInfoList = selectUserInfoListByRamdIds(ids);
        if (!ArrayUtils.isEmpty(userDetailsInfoList.toArray())) {
            List<Integer> idList = new ArrayList<>();
            userDetailsInfoList.stream().forEach(e -> {
                idList.add(e.getUserId());
            });
            RoomManagerExample example = new RoomManagerExample();
            example.createCriteria().andUserIdIn(idList);
            this.roomManagerDao.deleteByExample(example);
        }
        return CommonRespon.success();
    }

    private List<UserDetailsInfo> selectUserInfoListByRamdIds(Set<String> ramdIds) {
        if (ramdIds == null && ramdIds.isEmpty()) {
            return null;
        }
        UserDetailsInfoExample example = new UserDetailsInfoExample();
        example.createCriteria().andRandomNumIn(new ArrayList<>(ramdIds));
        List<UserDetailsInfo> userDetailsInfos = this.userDetailsInfoDao.selectByExample(example);
        return userDetailsInfos;
    }


    /**
     * 推送系统信息到mq
     */
    public void sendSystemMsg(String senderId, String reciver, String content, EMessageCode messageCode, EMessageChannelCode channelCode) {
        MessageSystemRecord record = null;
        switch (messageCode) {
            case SYSTEM_USER_FORZEN:
                record = this.makeRecord(senderId, reciver, content, messageCode);
                break;
            default:
                break;
        }
        if (record != null && channelCode != null) {
            //1. 保存相关的记录表数据
            log.info("推送系统信息到记录到数据库：{}", JsonUtils.write(record));
            messageSystemRecordDao.insertSelective(record);
            messageUnreadService.addUnread(record);
            //2. 推送一条俱乐部信息的消息到mq
            sendRabbitMq(record, channelCode.getCode());
        } else {
            log.warn("推送系统信息到mq EMessageChannelCode={} EMessageCode={} ", channelCode, record);
        }
    }

    /**
     * 根据手机号码查询玩家的基本信息
     *
     * @param phone 手机号码，必填
     * @return 记录不存在，则返回null；否则返回以下字段：
     * userId
     * userIdShow
     * userName
     */
    @Override
    public UserDetails checkAndGetUserBy(String phone) {
        UserDetails user = null;
        if (null == phone || "".equals(phone.trim())) {
            return user;
        }

        phone = phone.trim();
        String phoneEncrypted = PhoneNumUtil.encoder(phone);
        UserDetailsInfo info = this.userDetailsInfoService.findUserByUserPone(phoneEncrypted);
        if (null == info)
            return user;

        user = new UserDetails();
        user.setUserId(info.getUserId());
        user.setUserIdShow(info.getRandomNum());
        user.setUserName(info.getNikeName());

        return user;
    }

    /**
     * 根据玩家的显性ID，批量查询玩家的基本信息
     *
     * @param randomIdLst 待查询的玩家显性ID
     *
     * @return
     *    记录不存在，则返回null；否则返回以下字段：
     *      userId
     *      userIdShow
     *      userName
     */
    public List<UserDetailsInfo> checkAndGetUserBy(List<String> randomIdLst) {
        List<UserDetailsInfo> resultLst = null;
        if (null == randomIdLst || randomIdLst.isEmpty()) {
            return resultLst;
        }
        randomIdLst = randomIdLst.stream()
                .filter(e -> (e != null && !"".equals(e.trim())))
                .map(e -> e.trim()).distinct().collect(Collectors.toList());

        UserDetailsInfoExample example = new UserDetailsInfoExample();
        example.createCriteria()
                .andRandomNumIn(randomIdLst);
        resultLst = this.userDetailsInfoDao.selectByExample(example);
        return resultLst;
    }

    private void sendRabbitMq(MessageSystemRecord record, int pushChannel, List<String> receiverIds) {
        SystemMessage systemMessage = SystemMessage.builder()
                .senderId(record.getSenderId())
                .reciverUserIds(receiverIds)
                .header(record.getHeader())
                .title(record.getTitle())
                .content(record.getContent())
                .remark(record.getRemark())
                .type(record.getType())
                .msgStatus(record.getMsgStatus())
                .pushChannel(pushChannel)
                .build();
        String msgId = this.messageSender.sendSystemMessage(systemMessage);
        log.info("send system message to rabbitmq, msgId={}", msgId);
    }

    private MessageSystemRecord makeRecord(String senderId, String reciver, String content, EMessageCode messageCode) {
        String uuid = UUID.randomUUID().toString();
        return MessageSystemRecord.builder()
                .msgId(uuid)
                .senderId(senderId)
                .reciverId(reciver)
                .header("-1")
                .title(messageCode.getDesc())
                .content(content)
                .remark("")
                .type(messageCode.getCode())
                .msgStatus(0)
                .createTime(LocalDateTime.now())
                .build();
    }

    private void sendForbbidenUserMessage(Integer clubId, String operaterName, String clubName, Integer userId) {
        ForbbidenUser message = ForbbidenUser.builder().clubId(clubId).operateUserName(operaterName).clubName(clubName).userId(userId).build();
        String msgId = this.messageSender.sendForbbidenUserMessage(message);
        log.info("发送冻结信息到牌局 msgId={} data={}", msgId, JsonUtils.write(message));
    }

    //发送单条的处理
    private void sendRabbitMq(MessageSystemRecord record, int pushChannel) {
        sendRabbitMq(record, pushChannel, Collections.singletonList(record.getReciverId()));
    }

    @Override
    public CommonRespon closeWallet(UserInfo operator, Integer userId, String smsCode) {
        log.info("closeWallet: operatorId={}, userId={}, smsCode={}", operator.getId(), userId, smsCode);
        //检查验证码是否正确
        boolean checkSmsCode = iSmsService.checkSmsCode(ESmsType.WALLET_CLOSE.getValue(), operator.getMobileNo(), smsCode, operator.getMobileAreaCode());
        if (!checkSmsCode) {
            return CommonRespon.failure(ResponseCodeEnum.SMS_CAPTCHA_CODE_ERROR);
        }
        updateWalletStatus(userId, 2);
        return CommonRespon.success();
    }

    private void updateWalletStatus(Integer userId, int status) {
        UserAccount userAccount = userAccountService.selectByPrimaryKey(userId);
        if (userAccount == null) {
            return;
        }
        if (userAccount.getStatus().equals(status)) {
            //已关闭/开启，无需重复关闭/开启
            return;
        }
        userAccountService.updateStatus(userAccount.getUserId(), status);
    }

    @Override
    public CommonRespon openWallet(UserInfo operator, Integer userId, String smsCode) {
        log.info("openWallet: operatorId={}, userId={}, smsCode={}", operator.getId(), userId, smsCode);
        //检查验证码是否正确
        boolean checkSmsCode = iSmsService.checkSmsCode(ESmsType.WALLET_OPEN.getValue(), operator.getMobileNo(), smsCode, operator.getMobileAreaCode());
        if (!checkSmsCode) {
            return CommonRespon.failure(ResponseCodeEnum.SMS_CAPTCHA_CODE_ERROR);
        }
        updateWalletStatus(userId, 1);
        return CommonRespon.success();
    }

    @Override
    public CommonRespon autoCreateUser(AutoCreateUserReq req) {
        if (req.getNum() <= 0 || req.getNum() > 1000) {
            return CommonRespon.failure(ResponseCodeEnum.PARAM_VALID_FAILED);
        }
        if (StringUtils.isEmpty(req.getPassword())) {
            req.setPassword("123456");
        }
        AutoCreateUserTask record = AutoCreateUserTask.builder().actualNumber(0).password(req.getPassword()).targetNumber(req.getNum())
                .submitTime(LocalDateTime.now())
                .status(0).build();
        autoCreateUserTaskDao.insertSelective(record);
        return CommonRespon.success();
    }

    @Override
    public CommonRespon<PageBean<AutoCreateUserTask>> autoCreateUserList(PageReq req) {

        PageBean<AutoCreateUserTask> data = new PageBean<>();
        PageHelper.startPage(req.getPage(), req.getSize());

        AutoCreateUserTaskExample example = new AutoCreateUserTaskExample();
        example.or();
        List<AutoCreateUserTask> taskList = autoCreateUserTaskDao.selectByExample(example);
        PageInfo<AutoCreateUserTask> pageInfo = new PageInfo<>(taskList);

        data.setTotal(pageInfo.getTotal()).setPages(pageInfo.getPages()).setLastPage(pageInfo.isIsLastPage())
                .setBeanList(taskList);
        return CommonRespon.success(data);
    }

    @Override
    public CommonRespon msgReceiveUserAdd(MsgReceiveUserAddReq req) {
        UserDetailsInfo userDetailsInfo = userDetailsInfoService.findUserByUserRamdomId(req.getUserId());
        if (userDetailsInfo == null) {
            return CommonRespon.failure(ResponseCodeEnum.USER_ACCOUNT_NOT_EXIST);
        }
        MessageReceiveUserExample example = new MessageReceiveUserExample();
        example.or().andUserIdEqualTo(userDetailsInfo.getUserId());
        if (!messageReceiveUserDao.selectByExample(example).isEmpty()) {
            return CommonRespon.failure(ResponseCodeEnum.USER_ALREADY_EXIST);
        }
        MessageReceiveUser userInfo = MessageReceiveUser.builder().opId(req.getOperatorUser().getId()).createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now()).userId(userDetailsInfo.getUserId()).build();
        messageReceiveUserDao.insertSelective(userInfo);
        return CommonRespon.success();
    }

    @Override
    public CommonRespon<List<UserBaseVo>> checkUserByUserNickNameOrId(CheckUserByKeyWrodReq req) {
        List<UserDetailsInfo> userDetailsInfos = userDetailsInfoService.findUsersByNicknameRandomId(req.getContent(), req.getContent());

        List<UserBaseVo> userBaseVoList = new ArrayList<>();

        userDetailsInfos.forEach(e -> {
            userBaseVoList.add(UserBaseVo.builder().nickName(e.getNikeName()).userIdShow(e.getRandomNum()).build());
        });
        log.info("checkUserByUserNickNameOrId data={}", JsonUtils.write(userBaseVoList));
        return CommonRespon.success(userBaseVoList);
    }

    @Override
    public CommonRespon msgReceiveUserDel(MsgReceiveUserDelReq req) {
        messageReceiveUserDao.deleteByPrimaryKey(req.getId());
        return CommonRespon.success();
    }

    /**
     * 用户层次配置列表
     *
     * @return
     */
    @Override
    public CommonRespon<List<UserLevelConfigResp>> userLevelConfigList() {
        UserLevelConfigExample example = new UserLevelConfigExample();
        List<UserLevelConfig> userLevelConfigList = userLevelConfigDao.selectByExample(example);

        List<UserLevelConfigResp> list = userLevelConfigList.stream()
                .flatMap(e -> Stream.of(e.getScores().split(",")).map(Integer::valueOf).map(score -> {
                    UserLevelConfigResp item = new UserLevelConfigResp();
                    BeanUtils.copyProperties(e, item);
                    item.setLevelScore(score);
                    return item;
                }))
                .sorted(Comparator.comparingInt(UserLevelConfigResp::getLevelScore)
                        .thenComparingInt(UserLevelConfigResp::getLevelCode))
                .collect(Collectors.toList());
        return CommonRespon.success(list);
    }

    /**
     * 用户层次配置编辑
     *
     * @param req
     * @return
     */
    @Override
    public CommonRespon userLevelConfigEdit(UserLevelConfigEditReq req) {

        if (StringUtils.isEmpty(req.getLevelShowName())) {
            return CommonRespon.failure(ResponseCodeEnum.PARAM_VALID_FAILED);
        }
        UserLevelConfig item = UserLevelConfig.builder().id(req.getId()).opId(req.getOperatorUser().getId()).updateTime(LocalDateTime.now())
                .levelShowName(req.getLevelShowName()).build();
        userLevelConfigDao.updateByPrimaryKeySelective(item);
        return CommonRespon.success();
    }

    /**
     * 推送列表
     *
     * @param req
     * @return
     */
    @Override
    public CommonRespon<PageBean<MsgReceiveUserInfo>> msgReceiveUserList(MsgReceiveUserReq req) {
        PageHelper.startPage(req.getPage(), req.getSize());
        PageBean<MsgReceiveUserInfo> data = new PageBean<>();
        MessageReceiveUserExample example = new MessageReceiveUserExample();
        example.or();
        List<MessageReceiveUser> messageReceiveUsers = messageReceiveUserDao.selectByExample(example);
        PageInfo pageInfo = new PageInfo(messageReceiveUsers);

        List<MsgReceiveUserInfo> beanList = new ArrayList<>();
        List<Integer> idList = messageReceiveUsers.stream().map(e -> e.getUserId()).collect(Collectors.toList());
        Map<Integer, UserDetailsInfo> userDetailsInfoMap = userDetailsInfoService.findUsersMapByUserIds(idList);
        log.info("idList size={} userDetailsInfoMap size={} ", idList.size(), userDetailsInfoMap.size());

        messageReceiveUsers.forEach(e -> {
            UserDetailsInfo userDetailsInfo = userDetailsInfoMap.get(e.getUserId());
            if (userDetailsInfo != null) {
                MsgReceiveUserInfo item = new MsgReceiveUserInfo();
                item.setId(e.getId())
                        .setNickName(userDetailsInfo.getNikeName() == null ? "" : userDetailsInfo.getNikeName())
                        .setRandomId(userDetailsInfo.getRandomNum());
                beanList.add(item);
            }
        });
        return CommonRespon.success(data.setBeanList(beanList).setLastPage(pageInfo.isIsLastPage()).setTotal(pageInfo.getTotal()));
    }

    @Override
    public void exportAutoCreateUser(Integer taskId, HttpServletResponse response) throws IOException {

        AutoCreateUserTask record = autoCreateUserTaskDao.selectByPrimaryKey(taskId);
        if (record.getStatus() != 2) {
            CommonRespon.failure(ResponseCodeEnum.DATA_UN_FINISH);
        }
        AutoCreateUserLogExample example = new AutoCreateUserLogExample();
        example.or().andAutoTaskIdEqualTo(taskId);
        List<AutoCreateUserLog> userLogList = autoCreateUserLogDao.selectByExample(example);
        if (userLogList.isEmpty()) {
            CommonRespon.failure(ResponseCodeEnum.DATA_EMPT);
        }

        ExcelSheet sheet = new ExcelSheet("一键创建账户" + taskId);
        List<ExcelModel> models = new ArrayList<>();
        sheet.setModels(models);

        List<ExcelRow> rows = new ArrayList<>();

        String[] titles = new String[]{"序号", "昵称", "显性id", "手机号", "密码"};


        for (int i = 0; i < userLogList.size(); i++) {
            AutoCreateUserLog userLog = userLogList.get(i);
            ExcelRow row = new ExcelRow()
                    .add(i)
                    .add(userLog.getNickName())
                    .add(userLog.getRandomId())
                    .add(userLog.getUserPhone())
                    .add(userLog.getPassword());
            rows.add(row);
        }
        ExcelModel model = ExcelModel.builder()
                .titles(Lists.newArrayList(titles))
                .rows(rows)
                .afterBlankLine(1)
                .build();
        models.add(model);

        export(sheet, response);
    }

    private void export(ExcelSheet sheet, HttpServletResponse response) throws IOException {
        String fileName = URLEncoder.encode(sheet.getName(), "UTF-8");
        response.setHeader("Content-Type", "application/octet-stream");
        response.setHeader("Content-Disposition", String.format("attachment;fileName=\"%s.%s\"", fileName, "xlsx"));
        ExcelUtils.write(response.getOutputStream(), sheet);
    }

    @Override
    public CommonRespon loginForbid(UserInfo operator, Integer userId, String smsCode) {
        log.info("loginForbid: operatorId={}, username={}, userId={}, smsCode={}",
                operator.getId(), operator.getUsername(), userId, smsCode);

        boolean checkCode = this.iSmsService.checkSmsCode(ESmsType.USER_LOGIN_FORBID.getValue(), operator.getMobileNo(), smsCode,
                operator.getMobileAreaCode());
        if (!checkCode) {
            return CommonRespon.failure(ResponseCodeEnum.SMS_CAPTCHA_CODE_ERROR);
        }
        updateLoginStatus(userId, 0);

        MessageSystemRecord record = this.makeRecord("YY-" + operator.getId(), userId.toString(), operator.getUsername(), EMessageCode.SYSTEM_USER_FORZEN);
        sendRabbitMq(record, EMessageChannelCode.JPUSH.getCode());
        return CommonRespon.success();
    }

    @Override
    public CommonRespon loginAllow(UserInfo operator, Integer userId, String smsCode) {
        boolean checkCode = this.iSmsService.checkSmsCode(ESmsType.USER_LOGIN_ALLOW.getValue(), operator.getMobileNo(), smsCode,
                operator.getMobileAreaCode());
        if (!checkCode) {
            return CommonRespon.failure(ResponseCodeEnum.SMS_CAPTCHA_CODE_ERROR);
        }
        updateLoginStatus(userId, 1);
        return CommonRespon.success();
    }

    private void updateLoginStatus(Integer userId, int status) {
        UserDetailsInfo userDetailsInfo = userDetailsInfoService.selectByPrimaryKey(userId);
        //检查用户是否存在
        if (userDetailsInfo == null) {
            return;
        }
        //登录限制的状态改成正常
        UserBasicInfo info = UserBasicInfo.builder().userId(userDetailsInfo.getUserId()).forbid(status).build();
        userBasicInfoDao.updateByPrimaryKeySelective(info);
    }

    @Override
    public CommonRespon<NewUserData> getNewUserData(NewUserDataReq req) {
        log.info("getNewUserData req={}", JsonUtils.write(req));
        Integer newUserNum = userDetailsInfoDao.getNewUserNum(req.getStartTime().atStartOfDay(),
                req.getEndTime().atTime(23, 59, 59));
        Integer finishNum = userDetailsInfoDao.getNewUserFinishOrderNum(req.getStartTime().atStartOfDay(),
                req.getEndTime().atTime(23, 59, 59));
        return CommonRespon.success(NewUserData.builder().newUserNum(newUserNum).finishNum(finishNum).build());
    }

    /**
     * 是否內層设置
     *
     * @param req
     * @return
     */
    @Override
    public CommonRespon setInnerType(UserInnerReq req) {
        log.info("是否內層设置 req={}", JsonUtils.write(req));
        if (req.getOp() == null || req.getUserId() == null) {
            return CommonRespon.failure(ResponseCodeEnum.OP_NOT_EXIST);
        }

        UserDetailsInfo user = userDetailsInfoService.findUserByUserRamdomId(req.getUserId());
        if (user == null) {
            return CommonRespon.failure(ResponseCodeEnum.TRIBE_NOT_EXIST);
        }

        //更新状态
        user.setInnerType(req.getOp());
        this.updatetUserInnerType(user);

        return CommonRespon.success();
    }

    public void updatetUserInnerType(UserDetailsInfo user) {
        UserDetailsInfoExample example = new UserDetailsInfoExample();
        UserDetailsInfoExample.Criteria criteria = example.createCriteria();
        
        criteria.andRandomNumEqualTo(user.getRandomNum());
    
        UserDetailsInfo updatedUser = new UserDetailsInfo();
        updatedUser.setInnerType(user.getInnerType());
    
        userDetailsInfoDao.updateByExampleSelective(updatedUser, example);
    }

    @Override
    public CommonRespon updateUserNickName(UserInfoReq req) {
        if (req == null || req.getUserId() == null || StringUtils.isBlank(req.getNickName())) {
            return CommonRespon.failure(ResponseCodeEnum.PARAM_ERROR);
        }
        UserDetailsInfo userDetailsInfo = userDetailsInfoDao.selectByPrimaryKey(req.getUserId());
        if (userDetailsInfo == null) {
            return CommonRespon.failure(ResponseCodeEnum.USER_NOT_EXIST);
        }
        String newName = req.getNickName();
        String oldName = !StringUtils.isBlank(userDetailsInfo.getOldNikeName()) ? userDetailsInfo.getOldNikeName() + "," + userDetailsInfo.getNikeName() : userDetailsInfo.getNikeName();
        UserDetailsInfo updateUser = UserDetailsInfo.builder().userId(req.getUserId()).nikeName(newName).oldNikeName(oldName).build();
        userDetailsInfoDao.updateByPrimaryKeySelective(updateUser);
        return CommonRespon.success();
    }

    @Override
    public CommonRespon resetUserHead(UserResetHeadReq req) {
        if (req == null || req.getUserId() == null || StringUtils.isBlank(req.getWarningContent())) {
            return CommonRespon.failure(ResponseCodeEnum.PARAM_ERROR);
        }
        UserDetailsInfo userDetailsInfo = userDetailsInfoDao.selectByPrimaryKey(req.getUserId());
        if (userDetailsInfo == null) {
            return CommonRespon.failure(ResponseCodeEnum.USER_NOT_EXIST);
        }
        if (userDetailsInfo.getUseCustom().equals(1)) {
            log.info("重设玩家默认头像-------url: {}", userDetailsInfo.getCustomUrl());
            if (!StringUtils.isBlank(userDetailsInfo.getCustomUrl())) {
                // 删除oss资源
//                ossService.deleteFile(userDetailsInfo.getCustomUrl());
            }
            // 重设默认头像
            userDetailsInfoDao.resetUserHead(req.getUserId());
            // 发送app警告消息
            AppMessageNotice appMessageNotice = new AppMessageNotice();
            appMessageNotice.setCreatedBy(Long.valueOf(req.getOperatorUser().getId()));
            Map<String, Object> content = new HashMap<>();
            Map<String, Object> warningContent = new HashMap<>();
            warningContent.put("title", "头像违规");
            warningContent.put("content", req.getWarningContent());
            content.put("zh_CN", warningContent);
            content.put("en_US", warningContent);
            content.put("zh_HK", warningContent);
            appMessageNotice.setContent(JSONObject.toJSONString(content));
            appMessageNotice.setCategoryCode("system");
            appMessageNotice.setRenderType(1);
            appMessageNotice.setSendType(1);
            appMessageNotice.setTargetUserIds(userDetailsInfo.getRandomNum());

            // 调用 Service 层方法，新增公告
            appMessageNoticeService.addMessageNotice(appMessageNotice);

            // 刷新缓存
            noticeCache.reloadCache();

            //发送用户修改信息
            messageSender.sendUserMessage(InfoModMessage.builder()
                    .userId(userDetailsInfo.getUserId())
                    .nickname(userDetailsInfo.getNikeName())
                    .head(userDetailsInfo.getHead())
                    .build());
            return CommonRespon.success();
        }

        return CommonRespon.success();
    }
}
