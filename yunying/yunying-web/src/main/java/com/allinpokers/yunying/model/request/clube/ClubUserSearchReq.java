package com.allinpokers.yunying.model.request.clube;

import com.allinpokers.yunying.model.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "俱乐成员查询")
@Data
public class ClubUserSearchReq extends PageReq {

    @ApiModelProperty(value = "用户身份类型",notes = "0是玩家 1推广员 -1就是所有类型的用户")
    private Integer userType;
    @ApiModelProperty(value = "用户状态",notes = "0就是所有状态")
    private Integer statu;
    @ApiModelProperty(value = "玩家显性id或者玩家昵称",notes = "没有就是该俱乐部所有玩家查询")
    private String key;


}
