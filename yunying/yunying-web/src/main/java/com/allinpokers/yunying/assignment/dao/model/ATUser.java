package com.allinpokers.yunying.assignment.dao.model;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ATUser  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ATUser {
    /**
     * userId
     */
    @ApiModelProperty("userId")
    private Integer userId;

    /**
     * 牌局类型：61 = 德州 , 63 = 德州-AOF
     */
    @ApiModelProperty("牌局类型：61 = 德州 , 63 = 德州-AOF")
    private Integer roomPath;

    /**
     * userType
     */
    @ApiModelProperty("userType")
    private String userType;

    /**
     * visitLevel
     */
    @ApiModelProperty("visitLevel")
    private String visitLevel;

    /**
     * blind
     */
    @ApiModelProperty("blind")
    private String blind;

    /**
     * status
     */
    @ApiModelProperty("status")
    private String status;

    /**
     *  格式：HH:MM:00
     */
    @ApiModelProperty(" 格式：HH:MM:00")
    private LocalTime startTime;

    /**
     *  格式：HH:MM:00
     */
    @ApiModelProperty(" 格式：HH:MM:00")
    private LocalTime endTime;

    /**
     * createdTime
     */
    @ApiModelProperty("createdTime")
    private LocalDateTime createdTime;

    /**
     * createdBy
     */
    @ApiModelProperty("createdBy")
    private Integer createdBy;

    /**
     * updatedTime
     */
    @ApiModelProperty("updatedTime")
    private LocalDateTime updatedTime;

    /**
     * updatedBy
     */
    @ApiModelProperty("updatedBy")
    private Integer updatedBy;
}