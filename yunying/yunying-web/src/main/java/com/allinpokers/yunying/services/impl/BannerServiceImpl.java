package com.allinpokers.yunying.services.impl;

import com.allinpokers.yunying.dao.crazypoker.BannerDao;
import com.allinpokers.yunying.entity.crazypoker.Banner;
import com.allinpokers.yunying.entity.crazypoker.example.BannerExample;
import com.allinpokers.yunying.enu.ResponseCodeEnum;
import com.allinpokers.yunying.model.request.banner.AddBannerReq;
import com.allinpokers.yunying.model.request.banner.DeleteBannerReq;
import com.allinpokers.yunying.model.request.banner.ListBannerReq;
import com.allinpokers.yunying.model.request.banner.UpdateBannerReq;
import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.model.response.PageBean;
import com.allinpokers.yunying.services.BannerService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service("bannerService")
public class BannerServiceImpl implements BannerService {
    @Resource
    private BannerDao bannerDao;

    @Override
    public PageBean<Banner> listBanners(ListBannerReq req) {
        Page<Banner> page=null;
        PageHelper.startPage(req.getPage(), req.getSize());
        if (req.getType()==5){
            page = (Page<Banner>) bannerDao.getMttBannerList();
            for (int i = 0; i < page.getResult().size(); i++) {
                page.getResult().get(i).setLang("zh_CN");
                page.getResult().get(i).setProduct(0);
                page.getResult().get(i).setType(5);
            }
        }else {
            BannerExample example = new BannerExample();
            example.or().andTypeEqualTo(req.getType());
            example.setOrderByClause("id asc");
            page = (Page<Banner>)bannerDao.selectByExample(example);
        }
        return PageBean.of(page.getTotal(), req.getPage(), req.getSize(), page.getResult());
    }

    @Override
    public CommonRespon addBanner(AddBannerReq req) {
        //新增
        Banner banner = Banner.builder()
                .product(req.getProduct())
                .lang(req.getLang())
                .status(req.getStatus())
                .type(req.getType())
                .imageUrl(req.getImageUrl())
                .redirectUrl(req.getRedirectUrl())
                .description(req.getDescription())
                .createdOn(LocalDateTime.now())
                .updatedOn(LocalDateTime.now())
                .build();
        if (banner.getType()==5){
            List<Banner> mttBannerList = bannerDao.getMttBannerList();
            if (mttBannerList.size()>=1){
                return CommonRespon.failure(ResponseCodeEnum.ERROR,"请先删掉上一记录");
            }
            bannerDao.addMttBanner(banner);
        }else {
            bannerDao.insert(banner);
        }

        return CommonRespon.success();
    }

    @Override
    public CommonRespon updateBanner(UpdateBannerReq req) {
        if (req.getType()==5){
            Banner mttBannerOne = bannerDao.getMttBannerOne(req.getId());
            if (mttBannerOne == null) {
                return CommonRespon.failure(ResponseCodeEnum.DATA_EMPT);
            }
            mttBannerOne.setProduct(req.getProduct());
            mttBannerOne.setStatus(req.getStatus());
            mttBannerOne.setImageUrl(req.getImageUrl());
            mttBannerOne.setDescription(req.getDescription());
            bannerDao.updateMttBanner(mttBannerOne);
        }else {
            //更新
            Banner banner = bannerDao.selectByPrimaryKey(req.getId());
            if (banner == null) {
                return CommonRespon.failure(ResponseCodeEnum.DATA_EMPT);
            }

            banner.setProduct(req.getProduct());
            banner.setLang(req.getLang());
            banner.setStatus(req.getStatus());
            banner.setImageUrl(req.getImageUrl());
            banner.setRedirectUrl(req.getRedirectUrl());
            banner.setDescription(req.getDescription());
            banner.setUpdatedOn(LocalDateTime.now());
            bannerDao.updateByPrimaryKey(banner);
        }

        return CommonRespon.success();
    }

    @Transactional(transactionManager = "crazyPokerTransactionManager", rollbackFor = RuntimeException.class)
    @Override
    public CommonRespon deleteBanner(DeleteBannerReq req) {
        List<Integer> bannerIds = req.getBannerIds();
        if (bannerIds == null || bannerIds.isEmpty()) {
            return CommonRespon.failure(ResponseCodeEnum.PARAM_VALID_FAILED);
        }
        if (req.getType()==5){
            bannerDao.delMttBanner(bannerIds.get(0));
        }else {
            BannerExample example = new BannerExample();
            example.or().andIdIn(bannerIds);
            List<Banner> banners = bannerDao.selectByExample(example);

            if (banners.size() != bannerIds.size()) {
                return CommonRespon.failure(ResponseCodeEnum.DATA_EMPT);
            }
            //批量删除
            bannerDao.deleteByExample(example);
        }
        return CommonRespon.success();
    }
}
