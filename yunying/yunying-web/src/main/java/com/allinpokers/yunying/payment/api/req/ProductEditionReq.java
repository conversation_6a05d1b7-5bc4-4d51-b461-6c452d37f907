package com.allinpokers.yunying.payment.api.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import org.springframework.web.multipart.MultipartFile;

@Setter
@Getter
@ApiModel("商品新增或编辑请求")
public class ProductEditionReq {
    @ApiModelProperty(
            required = false,
            position = 0,
            notes="商品系统标识,如果此参数值不存在，则表示新增此"
    )
    private Integer id;

    @ApiModelProperty(
            required = false,
            position = 1,
            notes="商品编码，具有唯一性。"
    )
    private String code;

    @ApiModelProperty(
            required = false,
            position = 2,
            notes="商品名称"
    )
    private String title;

    @ApiModelProperty(
            required = false,
            position = 3,
            notes="商品图标URL"
    )
    private String icon;

    @ApiModelProperty(
            required = true,
            position = 4,
            notes="商品价格"
    )
    private BigDecimal price;

    @ApiModelProperty(
            required = false,
            position = 5,
            notes="商品折扣",
            example = "1.0"
    )
    private Integer discount;

    @ApiModelProperty(
            required = true,
            position = 6,
            notes="商品价值金豆数量"

    )
    private Integer idouNum;

    @ApiModelProperty(
            required = true,
            position = 7,
            allowableValues="-1,0,1",
            notes="商品状态：-1 =  删除 ，0  = 下架 ，1  = 上架"
    )
    private Byte status;

    @ApiModelProperty(
            required = false,
            position = 8,
            allowableValues="1,2,3",
            notes="使用渠道类型：1 = 扑克王国 , 2 = 茉莉的牌局 , 3 = 疯狂扑克"
    )
    private Integer channelId;

    @ApiModelProperty(
            required = false,
            position = 9,
            allowableValues="1,2,3,4",
            notes="使用客户端类型,多个则逗号(,)分隔：1=android , 2=ios , 3=web , 4=wap"
    )
    private String clientTypes="";

    @ApiModelProperty(
            required = false,
            position = 10,
            allowableValues="true,false",
            notes="是否内购商品：true=内购 ， false=非内购"
    )
    private Boolean isIospay;

    @ApiModelProperty(
            required = false,
            position = 11,
            notes="排序号，数字越小越往前显示"
    )
    private Integer sortNo;

    @ApiModelProperty(
            required = false,
            position = 12,
            notes="备注"
    )
    private String remark="";

    @ApiModelProperty(
        required = true,
        position = 13,
        notes="0-钻石 1-金币"
    )
    private Byte balanceType;

    @ApiModelProperty(
        required = true,
        position = 14,
        notes="0-人民币 1-金币"
    )
    private Byte currencyType;
}
