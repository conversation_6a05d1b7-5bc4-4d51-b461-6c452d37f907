package com.allinpokers.yunying.feign;

import com.allinpokers.yunying.config.YunyingWebConfig;
import feign.Feign;
import feign.httpclient.ApacheHttpClient;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * 在这里配置 feign ，设置第三方接口实际的调用地址
 *
 *
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class FeignClientConfig {
    @Resource(name = "yunyingWebConfig")
    private YunyingWebConfig config;

    @Bean
    public PayChannelClient payChannelClient() {
        return Feign.builder()
                .client(new ApacheHttpClient())
                .encoder(new JacksonEncoder())
                .decoder(new JacksonDecoder())
                .target(PayChannelClient.class, config.getPayChannelBaseUrl());
    }
}
