package com.allinpokers.yunying.message.dao;

import com.allinpokers.yunying.dao.BaseDao;
import com.allinpokers.yunying.message.bean.UserCmsMessageInfo;
import com.allinpokers.yunying.message.entity.CmsMessage;
import com.allinpokers.yunying.message.entity.example.CmsMessageExample;
import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;

/**
 * CMS消息  Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CmsMessageDao extends BaseDao<CmsMessage, CmsMessageExample, String> {

    /**
     * 获取用户cms消息
     *
     * @param userId
     * @param startTime
     * @return
     */
    Page<UserCmsMessageInfo> findByUserId(Integer userId, LocalDateTime startTime);
}