package com.allinpokers.yunying.services;

import com.allinpokers.yunying.model.response.PageBean;
import com.allinpokers.yunying.model.response.jpbaccount.JPBAccountRakebackResp;
import com.allinpokers.yunying.model.response.maintain.MaintainMessageResp;
import com.allinpokers.yunying.services.model.MaintainMessage;

import java.util.Date;
import java.util.List;

/**
 * 消息配置
 *
 * <AUTHOR>
 */
public interface MaintainMessageService {

    /**
     * 查询维护消息列表
     * @param page
     * @param size
     */
    PageBean<MaintainMessageResp> configList(Integer page, Integer size);

    void config(int operator, Integer id, Date startTime, Date endTime, int status, String exampleContent);

    void updateStatus(int operator, Integer id, int status);

    /**
     * 根据id查询维护消息内容
     * @param id
     */
    MaintainMessageResp queryMaintainMessageById(Integer id);

    MaintainMessage queryMaintainMessage();
}
