package com.allinpokers.yunying.controller;

import com.alibaba.excel.EasyExcel;
import com.allinpokers.yunying.enu.ResponseCodeEnum;
import com.allinpokers.yunying.model.request.complaint.CheatComplaintReq;
import com.allinpokers.yunying.model.request.complaint.UpdateComplaintReq;
import com.allinpokers.yunying.model.request.userdataedit.UserDataEditReq;
import com.allinpokers.yunying.model.request.userdataedit.UserDeltaReq;
import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.model.response.PageBean;
import com.allinpokers.yunying.mongodb.listener.BaseGameDataProfitImportListener;
import com.allinpokers.yunying.mongodb.model.BaseGameDataProfit;
import com.allinpokers.yunying.services.ICheatComplaintService;
import com.allinpokers.yunying.services.IUserDataEditService;
import com.allinpokers.yunying.services.model.ComplaintInfo;
import com.allinpokers.yunying.util.ExcelTemplateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
@Api(value = "/user/data", description = "用户数据编辑模块", tags = {"用户数据编辑模块"})
@RestController
@RequestMapping("/user/data/")
@Slf4j
public class UserDataEditController extends BaseController {

    @Autowired
    private IUserDataEditService userDataEditService;

    @ApiOperation(value = "用户数据编辑模块", produces = "application/json", httpMethod = "POST")
    @PostMapping("edit")
    public CommonRespon editUserData(@RequestBody(required = true) UserDataEditReq req) {
        return userDataEditService.editUserData(req);
    }

    @ApiOperation(value = "用户delta值编辑", produces = "application/json", httpMethod = "POST")
    @PostMapping("delta")
    public CommonRespon editUserDelta(@RequestBody(required = true) UserDeltaReq req) {
        return userDataEditService.editUserDelta(req);
    }

    @PostMapping("export")
    public void exportTemplate(HttpServletResponse response) {
        String[] headers = {
                "机器人ID", "用户ID", "用户名", "allin_hand", "allin_win_hand", "bring_in", "call_hand", "cbet_hand",
                "game_cnt", "pfr_hand", "pool_hand", "pool_win_hand", "raise_hand", "showdown_hand", "showdown_win_hand",
                "tbet_hand", "total_earn", "total_hand", "win_hand", "month_allin_hand", "month_allin_win_hand", "month_bring_in",
                "month_call_hand", "month_cbet_hand", "month_game_cnt", "month_pfr_hand", "month_pool_hand", "month_pool_win_hand",
                "month_raise_hand", "month_showdown_hand", "month_showdown_win_hand", "month_tbet_hand", "month_total_earn",
                "month_total_hand", "month_win_hand", "week_allin_hand", "week_allin_win_hand", "week_bring_in", "week_call_hand",
                "week_cbet_hand", "week_game_cnt", "week_pfr_hand", "week_pool_hand", "week_pool_win_hand", "week_raise_hand",
                "week_showdown_hand", "week_showdown_win_hand", "week_tbet_hand", "week_total_earn", "week_total_hand", "week_win_hand"
        };
        try {
            ExcelTemplateUtils.exportTemplate(response, headers, "原始战绩导入模板");
        } catch (IOException e) {
            throw new RuntimeException("导出失败", e);
        }
    }

    @PostMapping("importData")
    public CommonRespon<Object> importData(@RequestParam("file") MultipartFile file) {
        try {
            // 获取文件输入流
            InputStream inputStream = file.getInputStream();

            // 执行Excel解析
            EasyExcel.read(inputStream, BaseGameDataProfit.class, new BaseGameDataProfitImportListener())
                    .sheet()
                    .doRead();

            return CommonRespon.success(ResponseCodeEnum.SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            return CommonRespon.failure(ResponseCodeEnum.ERROR);
        }
    }


}
