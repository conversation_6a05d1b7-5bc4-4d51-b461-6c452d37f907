package com.allinpokers.yunying.dao.crazypoker;

import com.allinpokers.yunying.dao.BaseDao;
import com.allinpokers.yunying.entity.crazypoker.ActivityRpLimitConfig;
import com.allinpokers.yunying.entity.crazypoker.example.ActivityRpLimitConfigExample;
import org.apache.ibatis.annotations.Mapper;

/**
 * ActivityRpLimitConfigDao  Mapper
 * 
 * <AUTHOR>
 */
@Mapper
public interface ActivityRpLimitConfigDao extends BaseDao<ActivityRpLimitConfig, ActivityRpLimitConfigExample, Integer> {
}