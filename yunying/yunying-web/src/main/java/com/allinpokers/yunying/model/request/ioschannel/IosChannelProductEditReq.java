package com.allinpokers.yunying.model.request.ioschannel;

import com.allinpokers.yunying.permission.security.UserInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@ApiModel(value = "内购商品配置-编辑")
@Data
public class IosChannelProductEditReq{

    /**
     * id
     */
    @ApiModelProperty("id")
    @NotNull
    private Integer id;

    /**
     * idouNum
     */
    @ApiModelProperty("idouNum")
    @NotNull
    private Integer idouNum;
    /**
     * 渠道id
     */
    @ApiModelProperty("渠道id")
    @NotNull
    private Integer channelId;

    /**
     * 商品code
     */
    @ApiModelProperty("商品code")
    @NotEmpty
    private String productCode;

    /**
     * 标题
     */
    @ApiModelProperty("标题")
    @NotEmpty
    private String title;

    /**
     * 图片logo
     */
    @ApiModelProperty("图片logo")
    @NotEmpty
    private String icon;


    /**
     * 价格
     */
    @ApiModelProperty("价格")
    private BigDecimal price;

    @ApiModelProperty("折扣")
    private BigDecimal discount;

    /**
     * -1 = 删 0 = 下架  1  = 上架
     */
    @ApiModelProperty("-1 = 删 0 = 下架  1  = 上架")
    @NotNull
    private Integer status;

    /**
     * 权重
     */
    @ApiModelProperty("权重")
    @Max(99)
    @Min(0)
    private Integer sortNo;
    /**
     * remark
     */
    @ApiModelProperty("remark")
    private String remark;

    /**
     * 操作的用户
     */
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private UserInfo operatorUser;
}
