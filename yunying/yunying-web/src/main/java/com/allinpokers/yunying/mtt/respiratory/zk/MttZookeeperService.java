package com.allinpokers.yunying.mtt.respiratory.zk;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.recipes.cache.ChildData;
import org.apache.curator.framework.recipes.cache.TreeCache;
import org.apache.curator.framework.recipes.cache.TreeCacheEvent;
import org.apache.curator.framework.recipes.cache.TreeCacheListener;

import java.nio.charset.Charset;
import java.util.concurrent.Callable;

@Slf4j
public class MttZookeeperService {

    /**
     * 所有Room服务在一个类型标识的节点下，对应的值为空
     * - %s Room服务标识符的占位符
     * 每个Room服务对应一个节点，对应的值为空
     * - %s Room服务ID的占位符
     * 其下的结构是：
     * - /out    : 对应的值是外部访问的ip:port
     * - /ru     : 对应的值是当前Room服务的已分配的牌局数量
     * - /lu     : 对应的值是当前Room服务的已加载的牌局数量
     * - /cu     : 对应的值是当前Room服务的已建立的连接数量
     */
    private static final String SERVER_TYPE_PATH = "/dzpk/roomsvr/%s";
    private static final String SERVER_RU_FLAG = "ru";
    private static final String SERVER_LU_FLAG = "lu";
    private static final String SERVER_CU_FLAG = "cu";
    private static final String SERVER_OUTACS_FLAG = "out";

    /** zookeeper客户端 */
    private CuratorFramework curatorFramework;

    /** 字符集 */
    private Charset defaultCharset;

    /** 对应的Res服务类型 */
    private String serverType;

    /** 本服务的节点路径 */
    private String typePath=null;     // 标识Res服务的节点路径
    private TreeCache typePathCache=null; // 所有Room服务的根节点，监控此节点

    /** 上层业务回调 */
    private IServerNodeUpdated serverNodeUpdated;
    private IServerNodeRemoved serverNodeRemoved;

    public MttZookeeperService(String serverType,
                              Charset defaultCharset,
                              CuratorFramework curatorFramework,
                              IServerNodeUpdated serverNodeUpdated,
                              IServerNodeRemoved serverNodeRemoved){
        this.serverType = serverType;
        this.curatorFramework = curatorFramework;
        this.defaultCharset = null == defaultCharset? Charset.forName("UTF-8"):defaultCharset;
        this.serverNodeUpdated = serverNodeUpdated;
        this.serverNodeRemoved = serverNodeRemoved;

        this.initialize();
    }

    public void initialize() {
        StringBuilder traceLog = new StringBuilder(String.format("初始化%s服务ZK节点：",this.serverType));

        Exception throwEx = null;
        try {
            // 初始化节点路径
            this.typePath = String.format(SERVER_TYPE_PATH, this.serverType);
            if(null != traceLog){
                traceLog.append(String.format("%s typePath=%s",
                        System.lineSeparator(),this.typePath));
            }

            //本服务对应的节点Cache
            this.typePathCache = new TreeCache(this.curatorFramework, this.typePath);
            this.typePathCache.getListenable().addListener(new ChildrenNodeListener(this));
            this.startTypeNodeCache();
            if(null != traceLog){
                traceLog.append(String.format("%s %s服务节点Cache启动成功",
                        System.lineSeparator(),this.typePath));
            }
        }catch (Exception ex){
            throwEx = ex;
            throw ex;
        }finally {
            if(throwEx == null){
                if(null != traceLog){
                    traceLog.append(String.format("%s 初始化成功！",
                            System.lineSeparator()));
                    log.info(traceLog.toString());
                }
            }else{
                if(null != traceLog){
                    traceLog.append(String.format("%s 初始化失败：%s",
                            System.lineSeparator(),throwEx.getMessage()));
                    log.info(traceLog.toString(),throwEx);
                }
            }
        }
    }

    public void destroy(){
        this.stopTypeNodeCache();
    }

    /**
     * 增加已分配牌桌数量，分配牌局时调用
     *
     * @param serverId  当前分配的服务器ID
     * @param deltaNum  减少数量，必须大于等于1
     * @param tryNum    重试次数，默认：3
     *
     * @return
     */
    public IntegerUpdatedResult addRuByDelta(String serverId,int deltaNum,Integer tryNum){
        if(deltaNum<=0){
            return null;
        }
        if(null == serverId || "".equals(serverId.trim()))
            return null;

        if(null == tryNum || tryNum<=0)
            tryNum = 3;

        String ruPath = String.format("%s/%s/%s",this.typePath,serverId.trim(),SERVER_RU_FLAG);
        IntegerIncrUpdator updator = new IntegerIncrUpdator(this,this.curatorFramework,ruPath,
                false,deltaNum,null,null,tryNum);
        IntegerUpdatedResult result = updator.call();

        return result;
    }

    /**
     * 启动Room服务节点监控
     */
    private void startTypeNodeCache(){
        try {
            this.typePathCache.start();
        }catch (Exception ex){
            throw new RuntimeException(String.format("启动%s服务节点Cache[ %s ]失败：%s",
                    this.serverType,this.typePath,ex.getMessage()),ex);
        }
    }
    /**
     * 关闭Room服务节点监控
     */
    private void stopTypeNodeCache(){
        if(null == this.typePathCache)
            return ;

        try{
            this.typePathCache.close();
        }catch (Exception ex){
            log.error(String.format("关闭%s服务节点Cache[ %s ]失败：%s",
                    this.serverType,this.typePath,ex.getMessage()),ex);
        }
    }

    private byte[] serializeInt(int value){
        return String.valueOf(value).getBytes(this.defaultCharset);
    }
    private int deserialize2Int(byte[] data){
        if(null == data || data.length<=0)
            return 0;

        return Integer.parseInt(new String(data,this.defaultCharset));
    }
    private byte[] serializeString(String value){
        return value.getBytes(this.defaultCharset);
    }
    private String deserialize2String(byte[] data){
        if(null == data || data.length<=0)
            return null;

        return new String(data,this.defaultCharset);
    }

    /** 节点变化事件处理 */
    private void nodeUpdated(String chgPath){
        StringBuilder traceLog = new StringBuilder(String.format("%s服务节点变更：%s",this.serverType,chgPath));

        Throwable throwEx = null;
        try {
            // 校验是否ru节点
            // 路径以ru结尾
            if (null == chgPath || "".equals(chgPath.trim())) {
                if (null != traceLog)
                    traceLog.append(String.format("%s   节点路径为空 -> 忽略！", System.lineSeparator()));
                return;
            }
            chgPath = chgPath.trim();
            if (!chgPath.endsWith(SERVER_RU_FLAG)) {
                if(null != traceLog)
                    traceLog.append(String.format("%s   非ru节点 -> 忽略！",System.lineSeparator()));
                return;
            }

            // 移除pu
            // 以/结尾
            String servicePath = chgPath.replace(SERVER_RU_FLAG, "");
            if (null != traceLog)
                traceLog.append(String.format("%s   service-path=%s", System.lineSeparator(),servicePath));

            // 解析出serverId
            // 格式：/dzpk/roomsvr/room/20010001/ru
            // 20010001是serverId
            String[] pathFieldArr = servicePath.split("/");
            String serverId = pathFieldArr[pathFieldArr.length - 1];
            if (null != traceLog)
                traceLog.append(String.format("%s   server-id=%s", System.lineSeparator(),serverId));

            // 获取对外访问IP:PORT
            // 获取当前连接数
            String path = String.format("%s%s", servicePath, SERVER_OUTACS_FLAG);
            String outIpPort = this.deserialize2String(this.typePathCache.getCurrentData(path).getData());
            if (null != traceLog)
                traceLog.append(String.format("%s   outIpPort-path=%s , value=%s", System.lineSeparator(),path,outIpPort));

            path = String.format("%s%s", servicePath, SERVER_RU_FLAG);
            int ru = this.deserialize2Int(this.typePathCache.getCurrentData(path).getData());
            if (null != traceLog)
                traceLog.append(String.format("%s   ru-path=%s , value=%s", System.lineSeparator(),path,ru));

            path = String.format("%s%s", servicePath, SERVER_LU_FLAG);
            int lu = this.deserialize2Int(this.typePathCache.getCurrentData(path).getData());
            if (null != traceLog)
                traceLog.append(String.format("%s   lu-path=%s , value=%s", System.lineSeparator(),path,lu));

            path = String.format("%s%s", servicePath, SERVER_CU_FLAG);
            int cu = this.deserialize2Int(this.typePathCache.getCurrentData(path).getData());
            if (null != traceLog)
                traceLog.append(String.format("%s   cu-path=%s , value=%s", System.lineSeparator(),path,cu));

            String[] ipPortArr = outIpPort.split(":");
            String outIp = ipPortArr[0];
            int outPort = Integer.parseInt(ipPortArr[1]);

            this.serverNodeUpdated.updateNode(serverId,outIp,outPort,ru,lu,cu);
            if (null != traceLog)
                traceLog.append(String.format("%s   处理成功！", System.lineSeparator()));
        }catch (Exception ex){
            throwEx = ex;
            if (null != traceLog)
                traceLog.append(String.format("%s   处理失败：%s", System.lineSeparator(),ex.getMessage()));
        }finally {
            if(null == throwEx){
                if(null != traceLog)
                    log.info(traceLog.toString());
            }else{
                if(null != traceLog)
                    log.warn(traceLog.toString(),throwEx);
                else
                    log.warn(String.format("%s服务节点变更失败：%s",this.serverType,chgPath),throwEx);
            }
        }
    }
    private void nodeRemoved(String chgPath){
        StringBuilder traceLog  = new StringBuilder(String.format("%s服务节点移除：%s",this.serverType,chgPath));

        Throwable throwEx = null;
        try {
            // 校验是否pu节点
            // 路径以pu结尾
            if (null == chgPath || "".equals(chgPath.trim())) {
                if (null != traceLog)
                    traceLog.append(String.format("%s   节点路径为空 -> 忽略！", System.lineSeparator()));
                return;
            }
            chgPath = chgPath.trim();
            if (!chgPath.endsWith(SERVER_RU_FLAG)) {
                if(null != traceLog)
                    traceLog.append(String.format("%s   非ru节点 -> 忽略！",System.lineSeparator()));
                return;
            }

            // 移除pu
            // 以/结尾
            String servicePath = chgPath.replace(SERVER_RU_FLAG, "");
            if (null != traceLog)
                traceLog.append(String.format("%s   service-path=%s", System.lineSeparator(),servicePath));

            // 解析出serverId
            // 格式：/dzpk/roomsvr/res/20010001/pu
            // 20010001是serverId
            String[] pathFieldArr = servicePath.split("/");
            String serverId = pathFieldArr[pathFieldArr.length - 1];
            if (null != traceLog)
                traceLog.append(String.format("%s   server-id=%s", System.lineSeparator(),serverId));

            this.serverNodeRemoved.removeNode(serverId);
            if (null != traceLog)
                traceLog.append(String.format("%s   处理成功！", System.lineSeparator()));
        }catch (Exception ex){
            throwEx = ex;
            if (null != traceLog)
                traceLog.append(String.format("%s   处理失败：%s", System.lineSeparator(),ex.getMessage()));
        }finally {
            if(null == throwEx){
                if(null != traceLog)
                    log.info(traceLog.toString());
            }else{
                if(null != traceLog)
                    log.warn(traceLog.toString(),throwEx);
                else
                    log.warn(String.format("%s服务节点移除：%s",this.serverType,chgPath),throwEx);
            }
        }
    }

    /**
     * Room服务节点的事件监听器
     * Room服务中只需要关注连接重连的时候
     * 重新创建服务节点,其它事件无需考虑。
     */
    @Slf4j
    private static class ChildrenNodeListener implements TreeCacheListener {
        private MttZookeeperService watchService;

        public ChildrenNodeListener(MttZookeeperService watchService){
            this.watchService = watchService;
        }

        /**
         * 监控Room服务节点下的每个pu节点
         * 每个Room服务的pu节点的增加/修改/删除事件
         * 才能准确反映Room服务是否有效
         *
         * 每个Room服务的pu节点的增加/修改事件 -> 新增节点
         * 每个Room服务的pu节点的删除事件     ->  移除节点
         *
         * @param client
         * @param event
         * @throws Exception
         */
        public void childEvent(CuratorFramework client, TreeCacheEvent event) throws Exception{
            if(event.getType() == TreeCacheEvent.Type.NODE_ADDED ||
                    event.getType() == TreeCacheEvent.Type.NODE_UPDATED){
                this.watchService.nodeUpdated(event.getData().getPath());
            }else if(event.getType() == TreeCacheEvent.Type.NODE_REMOVED){
                this.watchService.nodeRemoved(event.getData().getPath());
            }
        }
    }

    @Getter
    @Setter
    public static class IntegerUpdatedResult{
        /** 扣减时的值 */
        private int value;
        private int version;
        private int incrValue;

        /** 扣减后的值 */
        private int newValue;
        private int newVersion;
    }

    /**
     * 整数增量更新器
     */
    @Slf4j
    private static class IntegerIncrUpdator implements Callable<IntegerUpdatedResult> {
        private CuratorFramework curatorFramework;
        private MttZookeeperService watchService;

        private String nodePath;
        private Integer originalValue;
        private Integer originalVersion;
        private Integer incrementalValue;
        private boolean isDeduct = false;

        private int tryNum;

        /**
         * 构造函数
         * @param curatorFramework  zookeeper客户端
         * @param path              节点路径
         * @param originalValue     原始值
         * @param originalVersion   原始值的版本号
         * @param incrementalValue  增量值，可赋值
         * @param isDeduct          是否扣减：true=扣减 ， false=增加
         */
        public IntegerIncrUpdator(MttZookeeperService service, CuratorFramework curatorFramework, String path, boolean isDeduct,
                                  int incrementalValue, Integer originalValue, Integer originalVersion,
                                  int tryNum){
            this.curatorFramework = curatorFramework;
            this.watchService = service;
            this.nodePath = path;
            this.originalValue = originalValue;
            this.originalVersion = originalVersion;
            this.incrementalValue = incrementalValue;
            this.isDeduct = isDeduct;
            this.tryNum = tryNum;
        }

        public IntegerUpdatedResult call() {
            IntegerUpdatedResult result = new IntegerUpdatedResult();
            int tryNum = this.tryNum<=0?3:this.tryNum>10?3:this.tryNum;
            while (tryNum>0) {
                tryNum ++;
                if(null == this.originalValue) {
                    ChildData nodeData = this.watchService.typePathCache.getCurrentData(this.nodePath);
                    if(nodeData == null){
                        log.warn("Room服务节点[{}}不存在，无法完成更新：{}",this.nodePath,this.incrementalValue);
                        return null;
                    }

                    this.originalValue = this.watchService.deserialize2Int(nodeData.getData());
                    this.originalVersion = nodeData.getStat().getVersion();
                }

                int newValue = 0;
                int newVersion = 0;
                int incrValue = 0;
                try {
                    incrValue = this.incrementalValue;
                    if(this.isDeduct){
                        newValue = this.originalValue - incrValue;
                    }else{
                        newValue = this.originalValue + incrValue;
                    }
                    if(newValue<0)
                        newValue = 0;

                    newVersion = this.curatorFramework
                            .setData()
                            .withVersion(this.originalVersion)
                            .forPath(this.nodePath, this.watchService.serializeInt(newValue))
                            .getVersion();

                    if (log.isDebugEnabled()) {
                        log.info(String.format("成功修改Room服务节点：路径=%s , 增加/扣减=%s , 增量值=%s , 原节点值=%s , 原版本号=%s , 新节点值=%s , 新版本号=%s",
                                this.nodePath,
                                this.isDeduct?"减少":"增加",
                                incrValue,
                                this.originalValue,
                                this.originalVersion,
                                newValue,
                                newVersion));
                    }
                    result.setValue(this.originalValue);
                    result.setVersion(this.originalVersion);
                    result.setIncrValue(incrValue);
                    result.setNewValue(newValue);
                    result.setNewVersion(newVersion);
                    break;
                } catch (Exception ex) {
                    log.info(String.format("修改Room服务节点失败：路径=%s , 增加/扣减=%s , 增量值=%s , 原节点值=%s , 原版本号=%s , 新节点值=%s , 新版本号=%s -> %s",
                            this.nodePath,
                            this.isDeduct?"减少":"增加",
                            incrValue,
                            this.originalValue,
                            this.originalVersion,
                            newValue,
                            newVersion,
                            ex.getMessage()),ex);
                    this.originalValue=null;
                    this.originalVersion=null;
                }
            }

            return result;
        }
    }
}
