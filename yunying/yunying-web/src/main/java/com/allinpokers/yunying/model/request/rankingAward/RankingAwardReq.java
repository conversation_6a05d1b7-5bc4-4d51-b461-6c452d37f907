package com.allinpokers.yunying.model.request.rankingAward;

import com.allinpokers.yunying.model.request.PageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class RankingAwardReq extends PageReq {
    private int id;
    @ApiModelProperty("名次")
    private Integer grade;

    @ApiModelProperty("等级奖励")
    private BigDecimal reward;
}
