package com.allinpokers.yunying.model.response.tribe;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ApiModel(description = "联盟")
@Accessors(chain = true)
public class TribeInfoBean {
    @ApiModelProperty(value = "联盟id")
    private int id;
    @ApiModelProperty(value = "联盟名称")
    private String tribeName;
    @ApiModelProperty(value = "联盟头像地址")
    private String tribeHead;
    @ApiModelProperty(value = "是否使用自定义头像 0否 1是")
    private Integer useCustom;
    @ApiModelProperty(value = "联盟自定义头像")
    private String customUrl;
    @ApiModelProperty(value = "联盟创建者id")
    private String creatorId;
    @ApiModelProperty(value = "联盟创建者昵称", name = "creatorName", dataType = "string")
    private String creatorName;
    @ApiModelProperty(value = "联盟显性id", name = "randomId", dataType = "int")
    private Integer randomId;
    @ApiModelProperty(value = "俱乐部数", name = "clubMumbers", dataType = "int")
    private Integer clubMumbers;
    @ApiModelProperty(value = "俱乐部名称")
    private String clubName;
    @ApiModelProperty(value = "俱乐部id")
    private Integer clubeId;
    @ApiModelProperty(value = "联盟金豆数量", name = "todayTribeSelfPay")
    private Integer tribeChip=0;
    @ApiModelProperty(value = "俱乐部数上限", name = "upperLimit", dataType = "int")
    private Integer upperLimit;
    @ApiModelProperty(value = "联系方式", name = "contact", dataType = "string")
    private String contact;
    @ApiModelProperty(value = "创建时间", name = "createTime", dataType = "date")
    private LocalDateTime createTime;
    @ApiModelProperty(value = "比例")
    private Integer profit;
    @ApiModelProperty(value = "今日业绩流水")
    private Integer todayTribeAchievement=0;
    @ApiModelProperty(value = "今日联盟分润（不含直属俱乐部）")
    private Double todayTribeWin = 0d;
    @ApiModelProperty(value = "联盟状态  0正常 1关闭")
    private Short statu;
    @ApiModelProperty(value ="允许亏损额度")
    private Integer insuranceLossLimit;
    @ApiModelProperty(value ="聯盟抽水 %")
    private Double platformFeeRate;
    @ApiModelProperty(value ="保險分成 %")
    private Double platformInsuranceFeeRate;
    /**
     * 是否主联盟，0 否 1是
     */
    @ApiModelProperty("是否主联盟，0 否 1是")
    private Short ismain;
    @ApiModelProperty("联盟类型，-1子联盟   0无状态联盟（非主非子） 1主联盟 ")
    private Integer tribeType = 0;
    @ApiModelProperty("子联盟数量")
    private Integer subTribeMembers = 0;
    @ApiModelProperty(value = "主联盟名称", notes = "只是子联盟才有这个字段")
    private String mainTribeName;

    @ApiModelProperty("今日联盟直属俱乐部分润（记分牌）")
    private BigDecimal todayTribeDirectlyClubProfit;
    @ApiModelProperty("今日联盟总分润（含直属俱乐部）")
    private BigDecimal todayTribeTotalProfit;


    /**
     * 对赌基金配置信息
     * */
    @ApiModelProperty(value = "联盟对赌基金仓", dataType = "long")
    private Long chip = 0L;
    @ApiModelProperty(value = "风险比例")
    private Integer chipRabtio = 0;
    @ApiModelProperty(value = "对赌占成比例")
    private Integer recordRabtio = 0;
    @ApiModelProperty(value = "是否开启 0关闭 1是开启")
    private Boolean open;
    @ApiModelProperty(value = "俱乐部返佣比例")
    private Integer clubShareRabio = 0;
    @ApiModelProperty(value = "联盟返佣比例")
    private Integer recordShareRabio = 0;
    @ApiModelProperty(value = "是否兜底")
    private Boolean contract;
    @ApiModelProperty(value = "是否爆仓")
    private Boolean burstStatus;

    @ApiModelProperty(value ="联盟对赌返佣")
    private Integer rebateChip =0;
    @ApiModelProperty(value ="联盟对赌输赢")
    private Integer winChip =0;

    /**
    * 对赌基金俱乐部配置信息
    * */
    @ApiModelProperty(value = "俱乐部对赌基金仓", dataType = "long")
    private Long clubChip = 0L;
    @ApiModelProperty(value = "对赌占成比例")
    private Integer clubRabtio = 0;
    @ApiModelProperty(value ="俱乐部对赌返佣")
    private Integer clubRebateChip =0;
    @ApiModelProperty(value ="俱乐部对赌输赢")
    private Integer clubWinChip =0;
    /**
     * 是否內層，0 否 1是
     */
    @ApiModelProperty("是否主內層，0 否 1是")
    private Integer innerType;
}
