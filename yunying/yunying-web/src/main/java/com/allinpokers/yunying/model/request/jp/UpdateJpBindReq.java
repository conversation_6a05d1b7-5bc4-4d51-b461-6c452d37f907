package com.allinpokers.yunying.model.request.jp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(description = "修改jp 盲注设置")
@Data
public class UpdateJpBindReq {


    @ApiModelProperty(value = "jp子彩池id")
    private Integer poolId;
    @ApiModelProperty(value = "盲注级别code")
    private Integer blindCode;
    @ApiModelProperty(value = "皇家同花顺系数")
    private BigDecimal royalFlushRatio;
    @ApiModelProperty(value = "同花顺系数")
    private BigDecimal straightFlushRatio;
    @ApiModelProperty(value = "四条系数")
    private BigDecimal fourOfAKindRatio;
    @ApiModelProperty(value = "操作人", hidden = true)
    private Integer userId;
}
