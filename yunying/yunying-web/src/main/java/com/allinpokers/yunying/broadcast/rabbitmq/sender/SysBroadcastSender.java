package com.allinpokers.yunying.broadcast.rabbitmq.sender;


import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * SysBroadcastSender
 *
 * <AUTHOR>
 * @date 13/2/2025
 */
@Slf4j
@Component
public class SysBroadcastSender {

    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 创建广播任务
     */
    public void refreshBroadcastTask() {
        log.info("send refresh broadcast task !");
        rabbitTemplate.convertAndSend(
                SysBroadcastRabbitMqKeys.Exchange.BROADCAST_TASK,
                SysBroadcastRabbitMqKeys.RoutingKey.BROADCAST_TASK,
                JSONObject.toJSONString(new JSONObject().fluentPut("refresh", true))
        );
    }

}
