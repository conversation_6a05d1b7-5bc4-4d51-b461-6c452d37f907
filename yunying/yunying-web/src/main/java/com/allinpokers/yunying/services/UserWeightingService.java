package com.allinpokers.yunying.services;

import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.model.response.PageBean;
import com.allinpokers.yunying.model.response.userweighting.UserWeightingInfo;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
public interface UserWeightingService {

	/**
	 * 列出加权记录
	 *
	 * @param startTime              开始日期
	 * @param endTime                结束日期
	 * @param userRandomIdOrNickname 用户显性ID或昵称
	 * @param page                   页码
	 * @param size                   每页记录数
	 * @return page
	 */
	PageBean<UserWeightingInfo> listWeightingData(LocalDate startTime, LocalDate endTime, String userRandomIdOrNickname, Integer page, Integer size);

	/**
	 * 修改加权状态开关
	 * @param status 0-关闭，1-开启
	 * @return
	 */
	CommonRespon switchWeightStatus(Integer status) throws Exception;

	/**
	 * 查询加权开关状态
	 * @return
	 */
	CommonRespon<Integer> queryWeightStatus() throws Exception;
}
