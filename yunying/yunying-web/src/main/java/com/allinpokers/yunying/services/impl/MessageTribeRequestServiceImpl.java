package com.allinpokers.yunying.services.impl;

import com.allinpokers.yunying.appmessage.send.AppBusinessMessageSender;
import com.allinpokers.yunying.appmessage.send.bean.JoinTribe;
import com.allinpokers.yunying.appmessage.send.bean.TribeCreation;
import com.allinpokers.yunying.dao.crazypoker.MessageClubRecordDao;
import com.allinpokers.yunying.dao.crazypoker.MessageTribeRecordDao;
import com.allinpokers.yunying.dao.crazypoker.MessageTribeRequestDao;
import com.allinpokers.yunying.dao.crazypoker.TribeGroupRecordDao;
import com.allinpokers.yunying.entity.crazypoker.*;
import com.allinpokers.yunying.entity.crazypoker.example.MessageTribeRequestExample;
import com.allinpokers.yunying.enu.ETribeMembersType;
import com.allinpokers.yunying.enu.ETribeRequestMessageCode;
import com.allinpokers.yunying.enu.ResponseCodeEnum;
import com.allinpokers.yunying.exception.UserException;
import com.allinpokers.yunying.model.request.message.ApproveTribeCreateReq;
import com.allinpokers.yunying.model.request.message.BatchOperationMessageReq;
import com.allinpokers.yunying.model.request.message.MessageListReq;
import com.allinpokers.yunying.model.request.message.OperationMessageReq;
import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.model.response.PageBean;
import com.allinpokers.yunying.model.response.message.MessageTribeCreateList;
import com.allinpokers.yunying.model.response.message.MessageTribeCreate;
import com.allinpokers.yunying.model.response.message.MessageTribeJoin;
import com.allinpokers.yunying.model.response.message.TribeJoinOne;
import com.allinpokers.yunying.model.response.message.TribeJoinValidResult;
import com.allinpokers.yunying.model.response.message.param.TribeCreateRequestPo;
import com.allinpokers.yunying.model.response.message.param.TribeJoinRequestPo;
import com.allinpokers.yunying.entity.crazypoker.UserAccount;
import com.allinpokers.yunying.entity.crazypoker.UserAccountLog;
import com.allinpokers.yunying.services.UserAccountService;
import com.allinpokers.yunying.rabbitmq.client.MessageSender;
import com.allinpokers.yunying.rabbitmq.client.bean.ClubMessage;
import com.allinpokers.yunying.rabbitmq.client.bean.TribeMessage;
import com.allinpokers.yunying.rabbitmq.constant.EMessageChannelCode;
import com.allinpokers.yunying.rabbitmq.constant.EMessageCode;
import com.allinpokers.yunying.enu.EUserAccountLogType;
import com.allinpokers.yunying.services.*;
import com.allinpokers.yunying.tier.dao.TierDao;
import com.allinpokers.yunying.util.JsonUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 2019/3/23
 *
 * <AUTHOR>
 */
@Service("messageTribeRequestService")
@Slf4j
public class MessageTribeRequestServiceImpl implements MessageTribeRequestService {
    @Resource
    private MessageTribeRequestDao messageTribeRequestDao;
    @Resource
    private MessageTribeRecordDao messageTribeRecordDao;
    @Resource
    private UserDetailsInfoService userDetailsInfoService;
    @Resource
    private TribeMembersService tribeMembersService;
    @Resource
    private TribeRecordService tribeRecordService;
    @Resource
    private MessageSender messageSender;
    @Resource
    private ClubRecordService clubRecordService;
    @Resource
    private MessageUnreadService messageUnreadService;
    @Resource
    private MessageClubRecordDao messageClubRecordDao;
    @Resource
    private TribeGroupRecordDao tribeGroupRecordDao;
    @Resource
    private UserAccountService userAccountService;

    @Resource(name = "messageTribeRequestService")
    private MessageTribeRequestServiceImpl self;

    @Resource
    AppBusinessMessageSender appBusinessMessageSender;

    @Resource
    MessageService messageService;

    @Resource
    TierDao tierDao;

    @Override
    public PageBean<MessageTribeCreateList> listTribeCreateMessages(MessageListReq req) {
        PageHelper.startPage(req.getPage(), req.getSize());
        Page<MessageTribeRequest> page = messageTribeRequestDao.findByTypeCode(ETribeRequestMessageCode.TRIBE_CREATE);
        if (page.isEmpty()) {
            return PageBean.empty();
        }
        //查询用户昵称
        Set<Integer> userIdSet = page.stream().map(msg -> Integer.valueOf(msg.getUserId())).collect(Collectors.toSet());
        Map<Integer, UserDetailsInfo> userMap = userDetailsInfoService.findUsersMapByUserIds(userIdSet);

        //转换成messageInfo
        List<MessageTribeCreateList> beanList = new ArrayList<>();
        page.forEach(message -> {
            UserDetailsInfo user = userMap.getOrDefault(Integer.valueOf(message.getUserId()), new UserDetailsInfo());
            String nickname = user.getNikeName();
            String randomNum = user.getRandomNum();
            //查询当前用户的clubId
            ClubRecord club = clubRecordService.findByClubId(Integer.valueOf(message.getClubId()));
            Integer clubRandomId = club.getRandomId();
            //联系方式
            TribeCreateRequestPo param = JsonUtils.read(message.getParam(), TribeCreateRequestPo.class);
            String contact = getContact(param);
            MessageTribeCreateList info = MessageTribeCreateList.builder()
                    .msgId(message.getMsgId())
                    .userId(message.getUserId())
                    .randomNum(randomNum)
                    .nickname(nickname)
                    .clubId(message.getClubId())
                    .clubRandomNum(clubRandomId)
                    .tribeName(param.getTribeName())
                    .tribeHead(param.getTribeHead())
                    .useCustom(param.getUseCustom())
                    .customUrl(param.getCustomUrl())
                    .contact(contact)
                    .message(param.getMessage())
                    .createTime(message.getCreateTime())
                    .build();
            beanList.add(info);
        });

        return PageBean.of(page.getTotal(), req.getPage(), req.getSize(), beanList);
    }

    private CommonRespon checkTribeRandomId(ApproveTribeCreateReq req) {
        //判断俱乐部随机号是否存在
        if (req.getRandomId() ==null) {
            return CommonRespon.failure(ResponseCodeEnum.TRIBE_ID_FAIL_1);
        }

        // 检查格式不能以0开头，且要4-7位数字
        String reg = "^[1-9]\\d{3,6}$";
        if (!req.getRandomId().toString().matches(reg)) {
            return CommonRespon.failure(ResponseCodeEnum.TRIBE_ID_FAIL_2);
        }

        // 检查是否已经存在
        if (tribeRecordService.findByRandomId(req.getRandomId()) != null) {
            return CommonRespon.failure(ResponseCodeEnum.TRIBE_ID_FAIL_3);
        }

        return null;
    }


    @Transactional(transactionManager = "crazyPokerTransactionManager", rollbackFor = RuntimeException.class)
    @Override
    public CommonRespon approveTribeCreateMessage(ApproveTribeCreateReq req) {

        // 检查ID
        CommonRespon checkRandomIdResult = checkTribeRandomId(req);
        if (checkRandomIdResult != null) {
            return checkRandomIdResult;
        }

        //参数检查
        if (req.getIsMainTribe() == null) {
            return CommonRespon.failure(ResponseCodeEnum.PARAM_VALID_FAILED);
        }
        if (req.getIsMainTribe().equals((short)1) && req.getMainTribeId() != null) {
            //设置该俱乐部为主联盟，不需要传其他的主联盟id
            return CommonRespon.failure(ResponseCodeEnum.PARAM_VALID_FAILED);
        }
        if (req.getMainTribeId() != null) {
            //检查设置的主联盟是否存在
            TribeRecord mainTribe = tribeRecordService.findByTribeId(req.getMainTribeId());
            if (mainTribe == null) {
                return CommonRespon.failure(ResponseCodeEnum.DATA_EMPT);
            }
        }

        //设置占成率和俱乐部上限的默认值
        int minProfit = 10;
        if (req.getProfit() == null) {
            req.setProfit(minProfit);
        }
        if (req.getClubUpperLimit() == null) {
            req.setClubUpperLimit(20);
        }


        MessageTribeRequest message = messageTribeRequestDao.selectByPrimaryKey(req.getMsgId());
        //校验
        if (message == null) {
            return CommonRespon.failure(ResponseCodeEnum.DATA_EMPT);
        }
        if (!message.getType().equals(ETribeRequestMessageCode.TRIBE_CREATE.getCode())) {
            return CommonRespon.failure(ResponseCodeEnum.MESSAGE_CANT_SUPPORT_MESSAGE_TYPE);
        }

        //这里可以使用参数配置
        int maxProfit = 20;
        if (req.getProfit() < minProfit || req.getProfit() > maxProfit) {
            return CommonRespon.failure(ResponseCodeEnum.MESSAGE_TRIBE_PROFIT_OUT_OF_RANGE);
        }
        if (req.getClubUpperLimit() < 1 || req.getClubUpperLimit() > 20) {
            return CommonRespon.failure(ResponseCodeEnum.MESSAGE_TRIBE_CLUB_UPPER_LIMIT_OUT_OF_RANGE);
        }

        Integer clubId = Integer.valueOf(message.getClubId());
        TribeMembers tribeMember = tribeMembersService.findByClubId(clubId);
        boolean isJoinedOrHasTribe = tribeMember != null;
        if (isJoinedOrHasTribe && tribeMember.getType().equals(ETribeMembersType.OWNER.getCode())) {
            //已经拥有了一个联盟，不能再次创建
            return CommonRespon.failure(ResponseCodeEnum.MESSAGE_IS_A_TRIBE_CANT_CREATE);
        }
        if (isJoinedOrHasTribe && tribeMember.getType().equals(ETribeMembersType.MEMBER.getCode())) {
            //已经是联盟成员，不能再次创建
            return CommonRespon.failure(ResponseCodeEnum.MESSAGE_JOIN_TRIBE_CANT_CREATE);
        }

        //1. 查看该俱乐部是否已经加入到联盟
        //判断联盟的名称是否重复
        TribeCreateRequestPo param = JsonUtils.read(message.getParam(), TribeCreateRequestPo.class);
        // req.setClubUpperLimit(Integer.parseInt(param.getClubCount()));
        TribeRecord tribeRecord = tribeRecordService.findByTribeName(param.getTribeName());
        if (tribeRecord != null) {
            return CommonRespon.failure(ResponseCodeEnum.MESSAGE_EXIST_SAME_TRIBE_NAME_CANT_CREATE);
        }

        //0. 删除消息请求表对应的数据
        int deleteRow = messageTribeRequestDao.deleteByPrimaryKey(message.getMsgId());
        if (deleteRow <= 0) {
            //没有删除成功，记录不存在，不需要往下执行，直接返回
            throw new UserException(ResponseCodeEnum.DATA_EMPT);
        }

        tribeRecord = tribeRecordService.insertByApproveCreateMessage(message, param, req);
        //写联盟成员
        tribeMembersService.insertByDefault(tribeRecord.getId(), clubId, ETribeMembersType.OWNER);
        if (req.getMainTribeId() != null) {
            //写子联盟关系
            TribeGroupRecord tribeGroupRecord = TribeGroupRecord.builder()
                    .tribeId(tribeRecord.getId())
                    .mainTribeId(req.getMainTribeId())
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .opId(req.getOperatorUserId())
                    .build();
            tribeGroupRecordDao.insertSelective(tribeGroupRecord);
        }

        // 先插入默认数据
        tierDao.insertTribeDefaultPaymentActivityTier(tribeRecord.getId());
        tierDao.insertTribeDefaultClubTier(tribeRecord.getId());

        // 如果是指定加入联盟，则插入层级数据
        messageService.insertClubUserTier(clubId);

        /* ******** 注释旧消息推送 <AUTHOR>
        //2. 记录消息记录表
        EMessageCode messageCode = EMessageCode.TRIBE_CREATE_S;
        MessageTribeRecord record = makeRecord(message, messageCode, req.getOperatorId(), param.getTribeName());
        messageTribeRecordDao.insert(record);
        messageUnreadService.addUnread(record);

        //4. 推送一条同意创建的消息到mq
        sendRabbitMq(record, EMessageChannelCode.TIM.getCode());
        */

        appBusinessMessageSender.notifyTribeCreationSuccessful(TribeCreation.builder()
                .tribeName(param.getTribeName())
                .tribeId(Long.valueOf(tribeRecord.getId()))
                .userId(Long.valueOf(message.getUserId()))
                .build());

        return CommonRespon.success();
    }


    @Transactional(transactionManager = "crazyPokerTransactionManager", rollbackFor = RuntimeException.class)
    @Override
    public CommonRespon rejectTribeCreateMessage(OperationMessageReq req, Integer id) {
        MessageTribeRequest message = messageTribeRequestDao.selectByPrimaryKey(req.getMsgId());
        if (message == null) {
            return CommonRespon.failure(ResponseCodeEnum.DATA_EMPT);
        }
        if (!message.getType().equals(ETribeRequestMessageCode.TRIBE_CREATE.getCode())) {
            return CommonRespon.failure(ResponseCodeEnum.MESSAGE_CANT_SUPPORT_MESSAGE_TYPE);
        }
        String userId = message.getUserId();

        //0. 删除消息请求表对应的数据
        int deleteRow = messageTribeRequestDao.deleteByPrimaryKey(message.getMsgId());
        if (deleteRow <= 0) {
            //没有删除成功，记录不存在，不需要往下执行，直接返回
            throw new UserException(ResponseCodeEnum.DATA_EMPT);
        }

        //1. 保存相关的记录表数据
        TribeCreateRequestPo param = JsonUtils.read(message.getParam(), TribeCreateRequestPo.class);
        /* ******** 注释旧消息推送 <AUTHOR>
        EMessageCode messageCode = EMessageCode.TRIBE_CREATE_F;
        MessageTribeRecord record = makeRecord(message, messageCode, req.getOperatorId(), param.getTribeName());
        messageTribeRecordDao.insert(record);
        messageUnreadService.addUnread(record);
        */
        //返還創建時扣除的鑽石
        Integer amount = Integer.parseInt(param.getFee());
        
        // UserAccount
        UserAccount userAccount = userAccountService.selectByPrimaryKey(Integer.parseInt(userId));
        Integer currentExtractChip = userAccount.getChip() ;
        Integer currentNotExtractChip = userAccount.getNotExtractChip();
        
        userAccountService.addExtractChip(Integer.parseInt(userId), amount);
        
        //add UserAccountLog
        UserAccountLog userAccountLog = UserAccountLog.builder()
                .userId(Integer.parseInt(userId))
                .type(UserAccountLog.Type.RETURN_CREATE_TRIBE.getValue())
                .changeSource(1)
                .currentChip(currentExtractChip)
                .changeChip(amount)
                .currNotExtractChip(currentNotExtractChip)
                .changeNotExtractChip(0)
                .currPlCount(userAccount.getPlCount())
                .changePlCount(0)
                .desction("返还创建联盟时扣除的钻石")
                .externalId("")
                .opId(id)
                .createdTime(LocalDateTime.now())
                .build();
        userAccountService.addLog(userAccountLog);

        /*
        ******** 注释旧消息推送 <AUTHOR>
        //3. 推送一条拒绝创建联盟的消息到mq
        sendRabbitMq(record, EMessageChannelCode.TIM.getCode());
        */

        appBusinessMessageSender.notifyTribeCreationFailed(TribeCreation.builder()
                .tribeName(param.getTribeName())
                .tribeId(Long.valueOf(id))
                .userId(Long.valueOf(userId))
                .build());

        return CommonRespon.success();
    }

    @Override
    public CommonRespon<PageBean<MessageTribeJoin>> listTribeJoinMessages(MessageListReq req) {
        if (Strings.isBlank(req.getOperatorUser().getClubRandomId())) {
            return CommonRespon.failure(ResponseCodeEnum.MESSAGE_JOIN_TRIBE_CLUB_OPERATOR_NOT_CLUB);
        }
        //查询当前用户的clubId
        ClubRecord currentClub = clubRecordService.findByRandomId(Integer.valueOf(req.getOperatorUser().getClubRandomId()));
        if (currentClub == null) {
            return CommonRespon.failure(ResponseCodeEnum.MESSAGE_JOIN_TRIBE_CLUB_IS_NOT_OWNER_CLUB);
        }
        //查询当前club创建的联盟
        TribeMembers tribeMember = tribeMembersService.findByClubId(currentClub.getId());
        if (tribeMember == null || !tribeMember.getType().equals(1)) {
            //该俱乐部不是 创建者
            return CommonRespon.failure(ResponseCodeEnum.MESSAGE_JOIN_TRIBE_CLUB_IS_NOT_OWNER_CLUB);
        }

        PageHelper.startPage(req.getPage(), req.getSize());
        Page<MessageTribeRequest> page = messageTribeRequestDao.findByTribeIdTypeCode(tribeMember.getTribeId(), ETribeRequestMessageCode.TRIBE_JOIN_REQUEST);
        if (page.isEmpty()) {
            return CommonRespon.success();
        }

        Set<Integer> clubIds = page.stream().map(v -> Integer.valueOf(v.getClubId())).collect(Collectors.toSet());
        Map<Integer, ClubRecord> clubMap = clubRecordService.findClubMapByClubIds(clubIds);

        //转换成MessageTribeJoin
        List<MessageTribeJoin> beanList = new ArrayList<>();
        page.forEach(message -> {
            TribeJoinRequestPo param = JsonUtils.read(message.getParam(), TribeJoinRequestPo.class);
            ClubRecord club = clubMap.getOrDefault(Integer.valueOf(message.getClubId()), new ClubRecord());
            MessageTribeJoin info = MessageTribeJoin.builder()
                    .msgId(message.getMsgId())
                    .clubId(message.getClubId())
                    .clubRandomId(club.getRandomId() + "")
                    .clubName(param.getClubName())
                    .tribeName(param.getTribeName())
                    .message(param.getMessage())
                    .createTime(message.getCreateTime())
                    .build();
            beanList.add(info);
        });

        PageBean<MessageTribeJoin> pageBean = PageBean.of(page.getTotal(), req.getPage(), req.getSize(), beanList);
        return CommonRespon.success(pageBean);
    }


    @Override
    public CommonRespon approveTribeJoinMessage(BatchOperationMessageReq req) {
        TribeJoinValidResult result = validTribeJoin(req);
        if (!result.isSuccess()) {
            return result.getErrorResponse();
        }

        //检查是否超出范围
        TribeRecord tribeRecord = tribeRecordService.findByTribeId(result.tribeId());
        if (tribeRecord.getClubLimit() < tribeRecord.getClubCount() + result.getMessages().size()) {
            return CommonRespon.failure(ResponseCodeEnum.MESSAGE_CANT_JOIN_TRIBE_CLUB_UPPER_LIMIT);
        }

        List<TribeJoinOne> tribeJoinOnes = new ArrayList<>();
        List<CommonRespon<TribeJoinOne>> failResponses = new ArrayList<>();
        for (MessageTribeRequest message : result.getMessages()) {
            try {
                CommonRespon<TribeJoinOne> response = self.approvalJoinOne(req, message, result.tribeId());
                if (response.isSuccess()) {
                    tribeJoinOnes.add(response.getData());
                } else {
                    failResponses.add(response);
                }
            } catch (Exception e) {
                log.error("approveTribeJoinMessage: msgId={}", message.getMsgId(), e);
            }
        }


        /*
        for (TribeJoinOne tribeJoinOne : tribeJoinOnes) {
            //4. 推送一条同意加入的消息到mq
            sendRabbitMq(tribeJoinOne.getRecord(), EMessageChannelCode.ALL.getCode());
            //5. 推送一条俱乐部变动的消息到mq（给该联盟下所有俱乐部）
            if (tribeJoinOne.getNoticeRecord() != null) {
                sendRabbitMq(tribeJoinOne.getNoticeRecord(), EMessageChannelCode.TIM.getCode(), tribeJoinOne.getNoticeUserIds());
            }
        }
        */
        if (!failResponses.isEmpty()) {
            log.info("failResponses: {}", failResponses);
            CommonRespon<TribeJoinOne> com = failResponses.get(0);
            com.setData(null);
            return com;
        }
        return CommonRespon.success();
    }

    private TribeJoinValidResult validTribeJoin(BatchOperationMessageReq req) {
        TribeJoinValidResult result = new TribeJoinValidResult();

        List<MessageTribeRequest> messages = this.findRequestByMsgIds(req.getMsgIds());
        if (req.getMsgIds() == null || req.getMsgIds().size() != messages.size()) {
            return result.failure(ResponseCodeEnum.DATA_EMPT);
        }
        //查询当前用户的clubId
        ClubRecord ownerClub = clubRecordService.findByRandomId(Integer.valueOf(req.getOperatorUser().getClubRandomId()));
        if (ownerClub == null) {
            return result.failure(ResponseCodeEnum.MESSAGE_JOIN_TRIBE_CLUB_IS_NOT_OWNER_CLUB);
        }
        //查询当前club创建的联盟
        TribeMembers ownerMember = tribeMembersService.findByClubId(ownerClub.getId());
        if (ownerMember == null || !ownerMember.getType().equals(1)) {
            //该俱乐部不是 创建者
            return result.failure(ResponseCodeEnum.MESSAGE_JOIN_TRIBE_CLUB_IS_NOT_OWNER_CLUB);
        }

        //校验messages
        for (MessageTribeRequest message : messages) {
            if (!message.getType().equals(ETribeRequestMessageCode.TRIBE_JOIN_REQUEST.getCode())) {
                return result.failure(ResponseCodeEnum.MESSAGE_CANT_SUPPORT_MESSAGE_TYPE);
            }
            Integer clubId = Integer.valueOf(message.getClubId());
            if (!message.getTribeId().equals(ownerMember.getTribeId() + "")) {
                return result.failure(ResponseCodeEnum.PERMISSION_ACCESS_DENIED);
            }
        }

        result.setOwnerClub(ownerClub);
        result.setOwnerMember(ownerMember);
        result.setMessages(messages);
        return result;
    }

    @Transactional(transactionManager = "crazyPokerTransactionManager", rollbackFor = RuntimeException.class)
    public CommonRespon<TribeJoinOne> approvalJoinOne(BatchOperationMessageReq req,  MessageTribeRequest message, Integer tribeId) {
        Integer clubId = Integer.valueOf(message.getClubId());
        TribeMembers tribeMember = tribeMembersService.findByClubId(clubId);
        boolean isJoined = tribeMember != null;
        if (isJoined) {
            //改俱乐部已加入其他联盟，审批失败
            log.info("approvalJoinOne: msgId={}, but isJoined other tribe. tribeId={}", message.getMsgId(), tribeMember.getTribeId());
            messageTribeRequestDao.deleteByPrimaryKey(message.getMsgId());
            return CommonRespon.failure(ResponseCodeEnum.MESSAGE_CANT_JOIN_OTHER_TRIBE);
        }

        //0. 删除消息请求表对应的数据
        int deleteRow = messageTribeRequestDao.deleteByPrimaryKey(message.getMsgId());
        if (deleteRow <= 0) {
            //没有删除成功，记录不存在，不需要往下执行，直接返回
            return CommonRespon.failure(ResponseCodeEnum.DATA_EMPT);
        }

        tribeMembersService.insertByDefault(tribeId, clubId, ETribeMembersType.MEMBER);
        //修改联盟的人数和俱乐部数量
        ClubRecord clubRecord = clubRecordService.findByClubId(clubId);
        boolean success = tribeRecordService.addClubCountMemberCount(tribeId, 1, clubRecord.getClubMembers());
        if (!success) {
            throw UserException.MESSAGE_CANT_JOIN_TRIBE_CLUB_UPPER_LIMIT;
        }

        //2. 记录消息记录表
        TribeJoinRequestPo param = JsonUtils.read(message.getParam(), TribeJoinRequestPo.class);
        EMessageCode messageCode = EMessageCode.CLUB_JOIN_TRIBE_REQUEST_S;
        MessageClubRecord record = MessageClubRecord.builder()
                .msgId(message.getMsgId())
                .clubId(message.getClubId())
                .senderId("YY-" + req.getOperatorUser().getId())
                .reciverId(message.getUserId())
                .header("-1")
                .title(messageCode.getDesc())
                .content(param.getTribeName())
                .remark("")
                .type(messageCode.getCode())
                .msgStatus(0)
                .createTime(LocalDateTime.now())
                .build();
        record.setRemark(param.getTribeName());
        /* ******** 注释旧消息推送 <AUTHOR>
        messageClubRecordDao.insert(record);
        messageUnreadService.addUnread(record);
        */

        //5. 保存 推送一条俱乐部变动的消息到消息记录表中  只发送给联盟创建者
        TribeRecord tribe = tribeRecordService.findByTribeId(Integer.valueOf(message.getTribeId()));
        List<String> receiverIds = new ArrayList<>();
        MessageTribeRecord noticeRecord = MessageTribeRecord.builder()
                .msgId(message.getMsgId())
                .tribeId(message.getTribeId())
                .senderId("-1")
                .reciverId(tribe.getCreator())
                .header("-1")
                .title(EMessageCode.TRIBE_JOIN_NEW_CLUB.getDesc())
                .content(param.getClubName())
                .remark(param.getTribeName())
                .type(EMessageCode.TRIBE_JOIN_NEW_CLUB.getCode())
                .msgStatus(0)
                .createTime(LocalDateTime.now())
                .build();
        receiverIds.add(noticeRecord.getReciverId());
        /* ******** 注释旧消息推送 <AUTHOR>
        messageTribeRecordDao.insert(noticeRecord);
        messageUnreadService.addUnread(noticeRecord);
        */

        // 推送消息
        appBusinessMessageSender.notifyJoinTribeSuccessful(JoinTribe.builder()
                .clubId(Long.valueOf(clubRecord.getId()))
                .clubName(clubRecord.getName())
                .clubOwnerId(Long.valueOf(clubRecord.getCreator()))
                .tribeId(Long.valueOf(tribe.getId()))
                .tribeName(tribe.getTribeName())
                .tribeOwnerId(Long.valueOf(tribe.getCreator()))
                .build());

        return CommonRespon.success(new TribeJoinOne(record, noticeRecord, receiverIds));
    }


    @Transactional(transactionManager = "crazyPokerTransactionManager", rollbackFor = RuntimeException.class)
    @Override
    public CommonRespon rejectTribeJoinMessage(BatchOperationMessageReq req) {
        TribeJoinValidResult result = validTribeJoin(req);
        if (!result.isSuccess()) {
            return result.getErrorResponse();
        }

        List<MessageClubRecord> records = new ArrayList<>();
        for (MessageTribeRequest message : result.getMessages()) {
            //0. 删除消息请求表对应的数据
            int deleteRow = messageTribeRequestDao.deleteByPrimaryKey(message.getMsgId());
            if (deleteRow <= 0) {
                //没有删除成功，记录不存在，不需要往下执行，直接返回
                throw new UserException(ResponseCodeEnum.DATA_EMPT);
            }

            //1. 保存相关的记录表数据
            TribeJoinRequestPo param = JsonUtils.read(message.getParam(), TribeJoinRequestPo.class);
            EMessageCode messageCode = EMessageCode.CLUB_JOIN_TRIBE_REQUEST_F;
            MessageClubRecord record = MessageClubRecord.builder()
                    .msgId(message.getMsgId())
                    .clubId(message.getClubId())
                    .senderId("YY-" + req.getOperatorUser().getId())
                    .reciverId(message.getUserId())
                    .header("-1")
                    .title(messageCode.getDesc())
                    .content(param.getTribeName())
                    .remark("")
                    .type(messageCode.getCode())
                    .msgStatus(0)
                    .createTime(LocalDateTime.now())
                    .build();
            record.setRemark(param.getTribeName());
            record.setRemark(param.getTribeName());
            /** ******** 注释旧消息推送 <AUTHOR>
            messageClubRecordDao.insert(record);
            messageUnreadService.addUnread(record);
             */
            records.add(record);
        }

        for (MessageClubRecord record : records) {
            //3. 推送一条拒绝加入的消息到mq
            sendRabbitMq(record, EMessageChannelCode.ALL.getCode());
        }
        return CommonRespon.success();
    }

    private List<MessageTribeRequest> findRequestByMsgIds(List<String> msgIds) {
        if (msgIds == null || msgIds.isEmpty()) {
            return new ArrayList<>();
        }
        MessageTribeRequestExample example = new MessageTribeRequestExample();
        example.or().andMsgIdIn(msgIds);
        return messageTribeRequestDao.selectByExample(example);
    }

    private String getContact(TribeCreateRequestPo param) {
        if (!StringUtils.isEmpty(param.getPhone())) {
            return param.getPhone();
        }
        if (!StringUtils.isEmpty(param.getWechat())) {
            return param.getWechat();
        }
        if (!StringUtils.isEmpty(param.getTelegram())) {
            return param.getTelegram();
        }
        if (!StringUtils.isEmpty(param.getEmail())) {
            return param.getEmail();
        }
        return "";
    }

    private MessageTribeRecord makeRecord(MessageTribeRequest message, EMessageCode messageCode, String senderId, String content) {
        return MessageTribeRecord.builder()
                .msgId(message.getMsgId())
                .tribeId(message.getTribeId())
                .senderId(senderId)
                .reciverId(message.getUserId())
                .header("-1")
                .title(messageCode.getDesc())
                .content(content)
                .remark("")
                .type(messageCode.getCode())
                .msgStatus(0)
                .createTime(LocalDateTime.now())
                .build();
    }

    private void sendRabbitMq(MessageClubRecord record, int pushChannel) {
        sendRabbitMq(record, pushChannel, Collections.singletonList(record.getReciverId()));
    }

    private void sendRabbitMq(MessageClubRecord record, int pushChannel, List<String> receiverIds) {
        ClubMessage clubMessage = ClubMessage.builder()
                .clubId(record.getClubId())
                .senderId(record.getSenderId())
                .reciverUserIds(receiverIds)
                .header(record.getHeader())
                .title(record.getTitle())
                .content(record.getContent())
                .remark(record.getRemark())
                .type(record.getType())
                .msgStatus(record.getMsgStatus())
                .pushChannel(pushChannel)
                .build();
        String msgId = messageSender.sendClubMessage(clubMessage);
        log.info("send club message to rabbitmq, msgId={}", msgId);
    }

    private void sendRabbitMq(MessageTribeRecord record, int pushChannel, List<String> receiverIds) {
        TribeMessage tribeMessage = TribeMessage.builder()
                .tribeId(record.getTribeId())
                .senderId(record.getSenderId())
                .reciverUserIds(receiverIds)
                .header(record.getHeader())
                .title(record.getTitle())
                .content(record.getContent())
                .remark(record.getRemark())
                .type(record.getType())
                .msgStatus(record.getMsgStatus())
                .pushChannel(pushChannel)
                .build();
        String msgId = messageSender.sendTribeMessage(tribeMessage);
        log.info("send tribe message to rabbitmq, msgId={}", msgId);
    }

    private void sendRabbitMq(MessageTribeRecord record, int pushChannel) {
        sendRabbitMq(record, pushChannel, Collections.singletonList(record.getReciverId()));
    }
}
