package com.allinpokers.yunying.model.response.manualrecharge;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserChipInfo {

    @ApiModelProperty("用户隐性ID")
    private Integer userId;

    @ApiModelProperty("用户昵称")
    private String nickname;

    @ApiModelProperty("用户可提金豆余额")
    private Integer extractChip;

    @ApiModelProperty("用户不可提金豆余额")
    private Integer notExtractChip;
}
