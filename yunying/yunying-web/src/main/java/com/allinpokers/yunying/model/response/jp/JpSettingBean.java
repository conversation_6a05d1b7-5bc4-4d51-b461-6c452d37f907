package com.allinpokers.yunying.model.response.jp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@ApiModel(description = "彩池盲注设置")
@Data
@Accessors(chain = true)
public class JpSettingBean implements Comparable<JpSettingBean>{

    @ApiModelProperty(value = "子彩池金豆")
    private Double childPoolFund;
    @ApiModelProperty(value = "子彩池id")
    private Integer childPoolId;
    @ApiModelProperty(value = "盲注级别code")
    private Integer blindCode;
    @ApiModelProperty(value = "盲注级别名称")
    private String  blindName;
    @ApiModelProperty(value = "皇家同花顺")
    private Double royalFlushRatio;
    @ApiModelProperty(value = "皇家同花顺比例上限")
    private Double royalFlushRatioLimit;
    @ApiModelProperty(value = "同花顺")
    private Double straightFlushRatio;
    @ApiModelProperty(value = "同花顺上限")
    private Double straightFlushRatioLimit;
    @ApiModelProperty(value = "四条")
    private Double fourOfAKindRatio;
    @ApiModelProperty(value = "四条比例上限")
    private Double fourOfAKindRatioLimit;

    @Override
    public int compareTo(JpSettingBean jpSettingBean) {
        return this.blindCode-jpSettingBean.blindCode;
    }
}

