package com.allinpokers.yunying.entity.vest;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ios 马甲配置  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class IosVestInfo {
    /**
     * id
     */
    @ApiModelProperty("id")
    private Integer id;

    /**
     * 渠道id
     */
    @ApiModelProperty("渠道id")
    private String iosChannelId;

    /**
     * 渠道名称
     */
    @ApiModelProperty("渠道名称")
    private String channelName;

    /**
     * opId
     */
    @ApiModelProperty("opId")
    private Integer opId;

    /**
     * createTime
     */
    @ApiModelProperty("createTime")
    private LocalDateTime createTime;

    /**
     * updateTime
     */
    @ApiModelProperty("updateTime")
    private LocalDateTime updateTime;

    /**
     * 渠道描述
     */
    @ApiModelProperty("渠道描述")
    private String channelDesc;

    /**
     * 0:关闭 1：打开
     */
    @ApiModelProperty("0:关闭 1：打开")
    private Integer adOpen;

    /**
     * targeUrl
     */
    @ApiModelProperty("targeUrl")
    private String targeUrl;
}