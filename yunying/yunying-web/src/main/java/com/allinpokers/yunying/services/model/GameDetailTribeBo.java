package com.allinpokers.yunying.services.model;

import com.allinpokers.yunying.mongodb.model.GameDetail;
import com.allinpokers.yunying.util.ArrayUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GameDetailTribeBo {
    private Integer tribeId;
    private Double commission;

    public static List<GameDetailTribeBo> of(GameDetail detail) {
        Integer[] clubIds = ArrayUtils.splitInt(detail.getTribeId());
        Double[] commissions = ArrayUtils.splitDouble(detail.getCommissionTribe());

        List<GameDetailTribeBo> users = new ArrayList<>();
        for (int i = 0; i < clubIds.length; i++) {
            GameDetailTribeBo user = GameDetailTribeBo.builder()
                    .tribeId(clubIds[i])
                    .commission(ArrayUtils.value(commissions, i, 0D))
                    .build();
            users.add(user);
        }
        return users;
    }
}
