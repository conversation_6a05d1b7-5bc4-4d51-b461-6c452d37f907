package com.allinpokers.yunying.payment.dao.model;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PaymentProduct  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PaymentProduct {
    /**
     * id
     */
    @ApiModelProperty("id")
    private Integer id;

    /**
     * code
     */
    @ApiModelProperty("code")
    private String code;

    /**
     * title
     */
    @ApiModelProperty("title")
    private String title;

    /**
     * icon
     */
    @ApiModelProperty("icon")
    private String icon;

    /**
     * price
     */
    @ApiModelProperty("price")
    private BigDecimal price;

    /**
     * 赠送商品数量
     */
    @ApiModelProperty("赠送商品数量")
    private Integer discount;

    /**
     * idouNum
     */
    @ApiModelProperty("idouNum")
    private Integer idouNum;

    /**
     * -1 =  删除
     *             0  = 下架
     *             1  = 上架
     */
    @ApiModelProperty("-1 =  删除<br/>            0  = 下架<br/>            1  = 上架")
    private Byte status;

    /**
     * 0-钻石 1-金币 2-聯盟幣
     */
    @ApiModelProperty("0-钻石 1-金币 2-聯盟幣")
    private Byte balanceType;

    /**
     * 0-人民币 1-钻石
     */
    @ApiModelProperty("0-人民币 1-钻石")
    private Byte currencyType;

    /**
     * 使用的客户端类型，多个则逗号(,)分隔
     *             有效值：1 android / 2 ios / 3 cms / 4 wap
     */
    @ApiModelProperty("使用的客户端类型，多个则逗号(,)分隔<br/>            有效值：1 android / 2 ios / 3 cms / 4 wap")
    private String clientTypes;

    /**
     * isIospay
     */
    @ApiModelProperty("isIospay")
    private Boolean isIospay;

    /**
     * 排序号，数字越小越往前显示
     */
    @ApiModelProperty("排序号，数字越小越往前显示")
    private Integer sortNo;

    /**
     * remark
     */
    @ApiModelProperty("remark")
    private String remark;

    /**
     * createdTime
     */
    @ApiModelProperty("createdTime")
    private LocalDateTime createdTime;

    /**
     * updatedTime
     */
    @ApiModelProperty("updatedTime")
    private LocalDateTime updatedTime;

    /**
     * 已無用
     * 渠道类型
     *             1 = 扑克王国
     *             2 = 茉莉的牌局
     *             3 = 疯狂扑克
     */
    @ApiModelProperty("已無用<br/>渠道类型<br/>            1 = 扑克王国<br/>            2 = 茉莉的牌局<br/>            3 = 疯狂扑克")
    private Integer channelId;
}