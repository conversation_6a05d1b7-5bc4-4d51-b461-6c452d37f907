package com.allinpokers.yunying.appmessage.dao;


import com.allinpokers.yunying.appmessage.bean.AppMessageNotice;
import com.allinpokers.yunying.appmessage.bean.AppMessageRecord;
import com.allinpokers.yunying.appmessage.bean.AppMessageRecordQuery;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

@Mapper
public interface AppMessageRecordMapper {


    /**
     * 查询消息记录
     * @param queryParams 查询条件
     * @return 列表
     */
    List<AppMessageRecord> findUserMessageRecords(AppMessageRecordQuery queryParams);

    /**
     * 统计消息记录数
     * @param queryParams 查询条件
     * @return 记录数
     */
    long countUserMessageRecords(AppMessageRecordQuery queryParams);

    /**
     * 插入消息记录
     * @param params 参数
     * @return 插入记录数
     */
    Long insertMessageRecord(Map<String, Object> params);

    /**
     * 插入消息接收者
     * @param params 参数
     * @return 插入记录数
     */
    Long insertMessageReceiver(Map<String, Object> params);

    /**
     * 插入消息业务记录
     * @param params 参数
     * @return 插入记录数
     */
    Long insertMessageBusinessRecord(Map<String, Object> params);

    /**
     * 删除消息记录
     * @param messageId 消息ID
     */
    void deleteMessageRecord(Long messageId);

    /**
     * 删除消息接收者
     * @param receiverId 接收者ID
     */
    void deleteMessageReceiver(Long receiverId);

    /**
     * 删除消息业务记录
     * @param recordId 记录ID
     */
    void deleteMessageBusinessRecord(Long recordId);



}
