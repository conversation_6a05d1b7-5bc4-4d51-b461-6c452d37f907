package com.allinpokers.yunying.dao.crazypoker;

import com.allinpokers.yunying.dao.BaseDao;
import com.allinpokers.yunying.entity.crazypoker.ClubPromoteInfo;
import com.allinpokers.yunying.entity.crazypoker.example.ClubPromoteInfoExample;
import com.allinpokers.yunying.entity.plus.clube.ClubPromoteInfoAndClubName;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 俱乐部推广统计表  Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ClubPromoteInfoDao extends BaseDao<ClubPromoteInfo, ClubPromoteInfoExample, Integer> {


    List<ClubPromoteInfoAndClubName> selectByClubPromoteInfoList(LocalDateTime start, LocalDateTime end, String content);
}