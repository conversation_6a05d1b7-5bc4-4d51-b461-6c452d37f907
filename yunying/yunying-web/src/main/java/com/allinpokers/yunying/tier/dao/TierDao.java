package com.allinpokers.yunying.tier.dao;


import com.allinpokers.yunying.tier.bean.*;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * TierDao
 *
 * <AUTHOR>
 * @since 2025/6/4
 */
@Mapper
public interface TierDao {

    List<MemberRoomTier> findMemberRoomTier(MemberRoomTierQuery memberRoomTierQuery);

    Long countMemberRoomTier(MemberRoomTierQuery memberRoomTierQuery);

    List<MemberRoomTier> findUserClubInfo(MemberRoomTierQuery memberRoomTierQuery);

    MemberRoomTier findTierInfo(MemberRoomTierQuery memberRoomTierQuery);

    MemberRoomTier findUserTier(MemberRoomTierQuery memberRoomTierQuery);

    void insertTribeDefaultRoomTier(MemberRoomTierQuery memberRoomTierQuery);

    @Select("SELECT user_id FROM club_members WHERE club_id = #{clubId}")
    List<Long> findClubMemberIds(@Param("clubId") Integer clubId);

    @Select("SELECT tribe_id FROM tribe_members WHERE club_id = #{clubId} LIMIT 1")
    Integer getTribeIdByClubId(@Param("clubId") Integer clubId);

    @Select("SELECT id FROM user_room_tier WHERE user_id = #{userId} AND tribe_id = #{tribeId} LIMIT 1")
    Long findUserTribeRoomTier(@Param("userId") Integer userId, @Param("tribeId") Integer tribeId);

    @Insert("INSERT INTO user_room_tier (user_id, tribe_id, tier_id) VALUES (#{userId}, #{tribeId}, #{tierId})")
    void insertUserTribeRoomTier(@Param("userId") Integer userId, @Param("tribeId") Integer tribeId, @Param("tierId") Integer tierId);

    @Insert("insert into tribe_user_payment_activity_tier (tribe_id,club_id,user_id,tpa_tier_id) values (#{tribeId},#{clubId},#{userId},(SELECT id FROM crazy_poker.tribe_payment_activity_tier WHERE tribe_id = #{tribeId} AND is_use = 1 AND is_lock = 0 AND manageable = 0 LIMIT 1))")
    void insertUserDefaultPaymentActivityTier(
            @Param("tribeId") Integer tribeId,
            @Param("clubId") Integer clubId,
            @Param("userId") Integer userId
    );

    @Update("UPDATE crazy_poker.tribe_members m " +
            "JOIN crazy_poker.tribe_club_tier t ON m.tribe_id = t.tribe_id " +
            "SET m.tribe_club_tier_id = t.id " +
            "WHERE t.tier_name = '未分层' AND t.manageable = 0 AND m.club_id = #{clubId} ")
    void updateClubMembersToDefaultTier(@Param("clubId") Integer clubId);



    @Select("SELECT id FROM room_tier WHERE manageable = 1 ORDER BY value ASC LIMIT 1")
    Integer getTribeMinTier();

    @Select({
            "<script>",
            "select " ,
            "    cr.random_id as randomId, ",
            "    cr.name, ",
            "    tm.club_id as clubId, ",
            "    tm.tribe_club_tier_id as tribeClubTierId, ",
            "    tct.tier_name as tierName",
            "from crazy_poker.tribe_members tm ",
            "join crazy_poker.club_record cr on tm.club_id = cr.id ",
            "join crazy_poker.tribe_club_tier tct on tm.tribe_club_tier_id = tct.id ",
            "where tm.tribe_club_tier_id = #{tierId} and tm.tribe_id = #{tribeId} ",
            "<if test='clubRandomIds != null and clubRandomIds.size>0 '>",
                " and cr.random_id in ",
                "<foreach item='item' collection='clubRandomIds' open='(' separator=',' close=')'>",
                "#{item}",
                "</foreach>",
            " </if>",
            "</script>"
    })
    List<ClubTier> findClubTier(ClubTierQuery clubTierQuery);

    @Select({
            "<script>",
            "select " ,
            "count(tm.club_id) as count ",
            "from crazy_poker.tribe_members tm ",
            "join crazy_poker.club_record cr on tm.club_id = cr.id ",
            "join crazy_poker.tribe_club_tier tct on tm.tribe_club_tier_id = tct.id ",
            "where tm.tribe_club_tier_id = #{tierId} and tm.tribe_id = #{tribeId} ",
            "<if test='clubRandomIds != null and clubRandomIds.size>0 '>",
            "and cr.random_id in ",
            "<foreach item='item' collection='clubRandomIds' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            " </if>",
            "</script>"
    })
    Long countClubTier(ClubTierQuery clubTierQuery);


    @Select({
            "<script>",
            "select",
            "count(tupat.id) as count",
            "from tribe_user_payment_activity_tier tupat",
            "join tribe_payment_activity_tier tpat ON tpat.id = tupat.tpa_tier_id",
            "join club_record cr ON cr.id = tupat.club_id",
            "join user_details_info udi ON udi.USER_ID = tupat.user_id",
            "where tupat.tribe_id = #{tribeId}",
            "<if test='tpaTierId != null'>",
            "and tupat.tpa_tier_id = #{tpaTierId}",
            "</if>",
            "<if test='userRandomNums != null and userRandomNums.size > 0'>",
            "and udi.random_num in",
            "<foreach item='item' collection='userRandomNums' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='clubId != null'>",
            "and tupat.club_id = #{clubId}",
            "</if>",
            "</script>"
    })
    Long countPaymentActivityTierUser(MemberRoomTierQuery memberRoomTierQuery);


    @Select({
            "<script>",
            "select ",
            "tupat.user_id as userId, ",
            "tupat.club_id as clubId, ",
            "tupat.tribe_id as tribeId, ",
            "tupat.tpa_tier_id as tpaTierId, ",
            "cr.random_id as clubRandomId, ",
            "cr.name as clubName, ",
            "tpat.tier_name as tierName, ",
            "udi.random_num as randomNum, ",
            "udi.nike_name as nickname ",
            "from tribe_user_payment_activity_tier tupat",
            "join tribe_payment_activity_tier tpat ON tpat.id = tupat.tpa_tier_id",
            "join club_record cr ON cr.id = tupat.club_id",
            "join user_details_info udi ON udi.USER_ID = tupat.user_id",
            "where tupat.tribe_id = #{tribeId}",
            "<if test='tpaTierId != null'>",
            "and tupat.tpa_tier_id = #{tpaTierId}",
            "</if>",
            "<if test='userRandomNums != null and userRandomNums.size > 0'>",
            "and udi.random_num in",
            "<foreach item='item' collection='userRandomNums' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='clubId != null'>",
            "and tupat.club_id = #{clubId}",
            "</if>",
            "</script>"
    })
    List<MemberPaymentActivityTier> listPaymentActivityTierUser(MemberRoomTierQuery memberRoomTierQuery);

    @Select({
            "SELECT " ,
            "COUNT(CASE WHEN ubal.type IN (19, 11) THEN ubal.id END) AS rechargeQuantity, ",
            "IFNULL(SUM(CASE WHEN ubal.type IN (19, 11, 21) THEN ubal.balance_change END), 0) AS rechargeAmount, ",
            "COUNT(CASE WHEN ubal.type IN (15, 20) THEN ubal.id END) AS withdrawAuantity, ",
            "IFNULL(SUM(CASE WHEN ubal.type IN (15, 20, 22) THEN ubal.balance_change END), 0) AS withdrawAmount ",
            "FROM user_balance_audit_log ubal ",
            "WHERE ubal.user_id = #{userId} ",
            "AND ubal.club_id = #{clubId} ",
            "AND (ubal.tribe_id IS NULL OR ubal.tribe_id = #{tribeId}) "
    })
    MemberPaymentActivityTier countUserCombined(
            @Param("userId") Integer userId,
            @Param("clubId") Integer clubId,
            @Param("tribeId") Integer tribeId
    );


    @Insert("INSERT INTO crazy_poker.tribe_payment_activity_tier  " +
            "(tribe_id, tier_name, level, remark, min_recharge_quantity, min_recharge_amount, min_withdraw_quantity, min_withdraw_amount, unique_id, is_use, is_lock, manageable) " +
            "values ( " +
            "#{tribeId}, " +
            "'未分层', " +
            "0, " +
            "'未分层，默认层级', " +
            "0, " +
            "0, " +
            "0, " +
            "0, " +
            "CONCAT(#{tribeId},':0:0:0:0'), " +
            "1, " +
            "0, " +
            "0); ")
    void insertTribeDefaultPaymentActivityTier(@Param("tribeId") Integer tribeId);

    @Insert("INSERT INTO crazy_poker.tribe_club_tier (tribe_id, tier_name, remark, manageable) " +
            "values (#{tribeId}, '未分层', '未分层，默认层级', 0), (#{tribeId}, '内层', '适用于内层定位的俱乐部', 1);")
    void insertTribeDefaultClubTier(@Param("tribeId") Integer tribeId);
}
