package com.allinpokers.yunying.assignment.api.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ExplosionTableConfigVo {
    @ApiModelProperty("时间范围-开始时间(包含)<br/>" +
            " 格式：HH:mm ,形如：08:00")
    private String startTime;
    @ApiModelProperty("时间范围-开始时间(包含)<br/>" +
            " 格式：HH:mm ,形如：08:00<br/>" +
            " 不包含此时间点<br/>" +
            " 允许小于【startTime】表示第二天的对应时间点")
    private String endTime;
    @ApiModelProperty("爆牌牌桌阈值<br/>" +
            " <0 , 不修改原来值或不开启此时间范围<br/>")
    private Integer tableNum;
}
