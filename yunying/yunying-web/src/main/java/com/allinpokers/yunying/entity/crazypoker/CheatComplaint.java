package com.allinpokers.yunying.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 伙牌投诉记录  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CheatComplaint {
    /**
     * id
     */
    @ApiModelProperty("id")
    private Integer id;

    /**
     * 牌局id
     */
    @ApiModelProperty("牌局id")
    private Integer roomid;

    /**
     * 大盲注
     */
    @ApiModelProperty("大盲注")
    private Integer bbChip;

    /**
     * 盲注级别对应手续费（单位：金豆）
     */
    @ApiModelProperty("盲注级别对应手续费（单位：金豆）")
    private Integer fee;

    /**
     * 投诉类型。1-伙牌，2-使用外挂，3-其他
     */
    @ApiModelProperty("投诉类型。1-伙牌，2-使用外挂，3-其他")
    private Integer type;

    /**
     * 投诉内容
     */
    @ApiModelProperty("投诉内容")
    private String content;

    /**
     * 发起投诉的玩家id
     */
    @ApiModelProperty("发起投诉的玩家id")
    private Integer complainant;

    /**
     * 被投诉玩家id，已英文逗号分隔
     */
    @ApiModelProperty("被投诉玩家id，已英文逗号分隔")
    private String respondent;

    /**
     * 投诉处理状态。0-未处理，1-已处理
     */
    @ApiModelProperty("投诉处理状态。0-未处理，1-已处理")
    private Integer status;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间，即处理投诉时间
     */
    @ApiModelProperty("更新时间，即处理投诉时间")
    private LocalDateTime updateTime;

    /**
     * 更新人，指处理人的id
     */
    @ApiModelProperty("更新人，指处理人的id")
    private Integer updateBy;
}