package com.allinpokers.yunying.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 德州扑克用户详细信息表  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FlowChip {
    /**
     * 外键
     */
    @ApiModelProperty("外键")
    private Integer id;

    /**
     * nikeName
     */
    @ApiModelProperty("转账ID")
    private String flowChipNo;

    /**
     * 头像
     */
    @ApiModelProperty("转出人ID")
    private Integer transferorUid;

    /**
     * 性别（0男1女）
     */
    @ApiModelProperty("转出人显性ID")
    private String transferorRid;

    /**
     * 筹码
     */
    @ApiModelProperty("转出人姓名")
    private String transferorName;

    /**
     * 胜局
     */
    @ApiModelProperty("收款人ID")
    private Integer transfereeUid;

    /**
     * 负局
     */
    @ApiModelProperty("收款人显性ID")
    private String transfereeRid;

    /**
     * 最大手牌
     */
    @ApiModelProperty("收款人姓名")
    private String transfereeName;

    /**
     * 最高拥有筹码
     */
    @ApiModelProperty("转金额")
    private Integer chip;

    /**
     * 最大手牌类型1-10
     */
    @ApiModelProperty("手续费")
    private Integer fee;

    /**
     * 最大赢取
     */
    @ApiModelProperty("战绩抵扣")
    private Integer plDeduction;

}