package com.allinpokers.yunying.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 手动充豆给用户  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ManualRechargeUserChip {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private Integer userId;

    /**
     * 赠送金豆类型，1可提，2不可提
     */
    @ApiModelProperty("赠送金豆类型，1可提，2不可提")
    private Integer chipType;

    /**
     * 充豆类型：
     * 1：手工收费
     * 2：手工扣豆
     * 3：赠送
     * 4：系统红包
     * 5：手工补单
     * 6：营销-牌桌金豆红包
     * 7：BUG赔付
     * 8：手动提现
     */
    @ApiModelProperty("充豆类型：<br/>1：手工收费<br/>2：手工扣豆<br/>3：赠送<br/>4：系统红包<br/>5：手工补单<br/>6：营销-牌桌金豆红包<br/>7：BUG赔付<br/>8：手动提现")
    private Integer rechargeType;

    /**
     * 充豆的金豆数量
     */
    @ApiModelProperty("充豆的金豆数量")
    private Integer rechargeChip;

    /**
     * 当前可提金豆
     */
    @ApiModelProperty("当前可提金豆")
    private Integer currentExtractChip;

    /**
     * 当前不可提金豆
     */
    @ApiModelProperty("当前不可提金豆")
    private Integer currentNotExtractChip;

    /**
     * 充豆后可提金豆
     */
    @ApiModelProperty("充豆后可提金豆")
    private Integer afterRechargeExtractChip;

    /**
     * 充豆后不可提金豆
     */
    @ApiModelProperty("充豆后不可提金豆")
    private Integer afterRechargeNotExtractChip;

    /**
     * 申请人
     */
    @ApiModelProperty("申请人")
    private String applicant;

    /**
     * 审批人
     */
    @ApiModelProperty("审批人")
    private String approver;

    /**
     * 充豆状态（手工收费/手工扣豆/手工提现）：
     * 1：已收
     * 2：未收
     * 3：已付
     * 4：未付
     */
    @ApiModelProperty("充豆状态（手工收费/手工扣豆/手工提现）：<br/>1：已收<br/>2：未收<br/>3：已付<br/>4：未付")
    private Integer rechargeStatus;

    /**
     * 领取状态（系统单号）：1未领取、2已领取
     */
    @ApiModelProperty("领取状态（系统单号）：1未领取、2已领取")
    private Integer receiveStatus;

    /**
     * 补单单号（手工补单）
     */
    @ApiModelProperty("补单单号（手工补单）")
    private String replenishmentNo;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 创建者ID
     */
    @ApiModelProperty("创建者ID")
    private Integer creatorId;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createdTime;

    /**
     * 更新者ID
     */
    @ApiModelProperty("更新者ID")
    private Integer updaterId;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updatedTime;
}