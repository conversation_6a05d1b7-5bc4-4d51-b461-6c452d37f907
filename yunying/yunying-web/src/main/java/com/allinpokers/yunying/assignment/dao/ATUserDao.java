package com.allinpokers.yunying.assignment.dao;

import com.allinpokers.yunying.assignment.dao.model.ATUser;
import com.allinpokers.yunying.assignment.dao.model.ClubOnUserStatistics;
import com.allinpokers.yunying.assignment.dao.model.example.ATUserExample;
import com.allinpokers.yunying.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

/**
 * ATUserDao  Mapper
 * 
 * <AUTHOR>
 */
@Mapper
public interface ATUserDao extends BaseDao<ATUser, ATUserExample, Integer> {
    @Select({"<script>" ,
               "select cm.club_id as clubId," ,
               "       count(if(au.`status`='on'and visit_level='normal',1,null)) as normalNum," ,
               "       count(if(au.`status`='on' and visit_level='operator',1,null)) as opNum" ,
               "  from at_user au left join club_members cm on au.user_id=cm.user_id" ,
               " where cm.club_id in " ,
               "<foreach close=')' collection='clubIdLst' item='clubId' open='(' separator=','>",
               "#{clubId}",
               "</foreach>",
               " group by cm.club_id",
            "</script>"})
    List<ClubOnUserStatistics> onUserOfClub(@Param("clubIdLst") Set<Integer> clubIdLst);

    @Select("select user_id from at_user")
    List<Integer> queryAtUserIds();
}