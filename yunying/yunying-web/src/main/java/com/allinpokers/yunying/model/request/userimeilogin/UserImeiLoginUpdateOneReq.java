package com.allinpokers.yunying.model.request.userimeilogin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel
public class UserImeiLoginUpdateOneReq {

    @ApiModelProperty("用户隐性ID")
    private Integer userId;

    @ApiModelProperty("IMEI集合")
    private Set<String> imeiSet;
}
