package com.allinpokers.yunying.tier.controller;


import com.allinpokers.yunying.tier.bean.ClubTierQuery;
import com.allinpokers.yunying.tier.bean.MemberRoomTierQuery;
import com.allinpokers.yunying.tier.service.TierService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * TierController
 *
 * <AUTHOR>
 * @since 2025/6/4
 */
@Slf4j
@Api(tags = "层级管理")
@RestController
@RequestMapping("/tier")
public class TierController {

    @Resource
    TierService tierService;

    @ApiOperation(value = "导出成员层级记录")
    @PostMapping("/exportMemberRoomTier")
    public void exportMemberRoomTier(@RequestBody MemberRoomTierQuery query, HttpServletResponse response) {
        tierService.exportMemberRoomTier(query, response);
    }

    @ApiOperation(value = "导出俱乐部层级记录")
    @PostMapping("/exportClubTier")
    public void exportClubTier(@RequestBody ClubTierQuery query, HttpServletResponse response) {
        tierService.exportClubTier(query, response);
    }

    @ApiOperation(value = "导出支付及活动层级记录")
    @PostMapping("/exportMemberPaymentActivityTier")
    public void exportMemberPaymentActivityTier(@RequestBody MemberRoomTierQuery query, HttpServletResponse response) {
        tierService.exportMemberPaymentActivityTier(query, response);
    }

}
