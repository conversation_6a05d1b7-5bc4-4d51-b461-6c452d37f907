package com.allinpokers.yunying.horseracelamp.api;

import com.allinpokers.yunying.horseracelamp.api.bean.AccountAddRecord;
import com.allinpokers.yunying.horseracelamp.api.bean.GameInfo;
import com.allinpokers.yunying.horseracelamp.api.bean.GrandRewardsRecordInfo;
import com.allinpokers.yunying.horseracelamp.api.bean.RewardsRecordInfo;
import com.allinpokers.yunying.horseracelamp.api.req.*;
import com.allinpokers.yunying.horseracelamp.service.IHorseRaceLampService;
import com.allinpokers.yunying.model.request.clube.SearchClubReq;
import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.model.response.PageBean;
import com.allinpokers.yunying.model.response.club.ClubInfoBeanDetail;
import com.allinpokers.yunying.model.response.report.RoomHand;
import com.allinpokers.yunying.model.response.report.RoomHandList;
import com.allinpokers.yunying.services.ReportService;
import com.fasterxml.jackson.databind.util.JSONPObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 */
@Api(value = "/horseracelamp/", description = "跑马灯模块", tags = "跑马灯模块")
@RestController
@RequestMapping("/horseracelamp")
public class HorseRaceLampController {

    @Autowired
    private IHorseRaceLampService horseRaceLampService;

    @ApiOperation(value = "开启/关闭跑马灯游戏")
    @PostMapping(value = "/switchStatus")
    @ResponseBody
    public CommonRespon switchStatus(@RequestBody SwitchStatusReq req) {
        horseRaceLampService.switchStatus(req.getStatus());
        return CommonRespon.success();
    }

    @ApiOperation(value = "跑马灯游戏获奖记录统计")
    @PostMapping(value = "/rewards/records/query")
    @ResponseBody
    public CommonRespon<List<RewardsRecordInfo>> rewardsRecordsQuery(@RequestBody RewardsRecordsQueryReq req) {
        return horseRaceLampService.queryRewardsRecordsList(req.getStartDate(), req.getEndDate());
    }

    @ApiOperation(value = "跑马灯游戏大奖记录查询")
    @PostMapping(value = "/grand/rewards/records/query")
    @ResponseBody
    public CommonRespon<List<GrandRewardsRecordInfo>> grandRewardsRecordsQuery() {
        return horseRaceLampService.queryGrandRewardsRecordsList();
    }

    @ApiOperation(value = "跑马灯游戏信息查询")
    @PostMapping(value = "/status/query")
    @ResponseBody
    public CommonRespon<GameInfo> statusQuery() {
        return horseRaceLampService.queryGameInfo();
    }

    @ApiOperation(value = "跑马灯奖池注入",
            notes = "状态码<br/>" +
            "0 = 成功。<br/>" +
            "10001 = 营销账户不可负，金豆不足。<br/>" +
            "999 = 失败，异常情况等<br/>")
    @PostMapping(value = "/account/add")
    @ResponseBody
    public CommonRespon accountAdd(@RequestBody AccountAddReq req) {
        return horseRaceLampService.addAccount(req.getKdou());
    }

    @ApiOperation(value = "跑马灯奖池注入记录查询",
            notes = "状态码<br/>" +
            "0 = 成功。<br/>" +
            "999 = 失败，异常情况等<br/>")
    @PostMapping(value = "/account/add/list")
    @ResponseBody
    public CommonRespon<PageBean<AccountAddRecord>> queryAccountAddRecord(@RequestBody AccountAddRecordReq req) {
        return horseRaceLampService.queryAccountAddRecord(req.getStart(), req.getEnd(), req.getPage(), req.getSize());
    }


    @ApiOperation(value = "跑马灯奖池减少",
            notes = "状态码<br/>" +
                    "0 = 成功。<br/>" +
                    "21001 = 跑马灯奖池余额不足。<br/>" +
                    "999 = 失败，异常情况等<br/>")
    @PostMapping(value = "/account/reduce")
    @ResponseBody
    public CommonRespon accountReduce(@RequestBody AccountReduceReq req) {
        return horseRaceLampService.reduceAccount(req.getKdou());
    }
}
