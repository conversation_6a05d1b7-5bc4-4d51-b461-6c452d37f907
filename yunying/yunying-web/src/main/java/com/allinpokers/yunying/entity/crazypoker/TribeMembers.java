package com.allinpokers.yunying.entity.crazypoker;

import com.allinpokers.yunying.entity.crazypoker.key.TribeMembersKey;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 部落俱乐部列表  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TribeMembers extends TribeMembersKey {
    /**
     * 部落成员类型 (1部长，2部员)
     */
    @ApiModelProperty("部落成员类型 (1部长，2部员)")
    private Integer type;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 俱乐部的信用额
     */
    @ApiModelProperty("俱乐部的信用额")
    private Integer clubCredit;

    /**
     * 初始信用额
     */
    @ApiModelProperty("初始信用额")
    private Integer initCredit;

    /**
     * pl
     */
    @ApiModelProperty("pl")
    private Integer pl;

    /**
     * 是否暂停带入（0 否 1 是）
     */
    @ApiModelProperty("是否暂停带入（0 否 1 是）")
    private Short pause;

    /**
     * 俱乐部在联盟的状态 正常 1 踢出2 转移中3
     */
    @ApiModelProperty("俱乐部在联盟的状态 正常 1 踢出2 转移中3")
    private Short status;
}