package com.allinpokers.yunying.controller;

import com.allinpokers.yunying.model.request.system.CreateBroadCastReq;
import com.allinpokers.yunying.model.request.system.RoomForceCloseReq;
import com.allinpokers.yunying.model.request.system.SendSysMessageReq;
import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.permission.security.SpringSecurityUtils;
import com.allinpokers.yunying.services.SystemService;
import com.allinpokers.yunying.util.JsonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;

@Log4j2
@Api(description = "系统管理", tags = "系统管理")
@RestController
@RequestMapping("/system/")
public class SystemController {


    @Resource
    private SystemService systemService;

    /**
     * 牌局广播
     *
     * @param
     * @return
     */
    @ApiOperation(value = "牌局广播创建")
    @PostMapping(value = "broadcast/broadcastCreate")
    public CommonRespon broadCastCreate(@RequestBody CreateBroadCastReq req) {
        req.setOperatorUser(SpringSecurityUtils.getUserInfo());
        return systemService.broadCastCreate(req);
    }
    /**
     * 产品负责人强制解散房间
     * @return 解散房间
     */
    @ApiOperation(value = "强制解散房间")
    @PostMapping(value = "user/force-close")
    public CommonRespon forceCloseRoom(@RequestBody RoomForceCloseReq req) {
        log.info("forceCloseRoom  data={}",JsonUtils.write(req));
        req.setOperatorUser(SpringSecurityUtils.getUserInfo());
        return systemService.forceCloseRoom(req);
    }

    /**
     * 发送系统消息
     * @param req
     * @return
     */
    @ApiOperation(value = "发送系统消息")
    @PostMapping(value = "message/sendSysMessage")
    public CommonRespon sendSysMessage(@RequestBody SendSysMessageReq req) {
        req.setOperatorUser(SpringSecurityUtils.getUserInfo());
        return systemService.sendSysMessage(req);
    }
}
