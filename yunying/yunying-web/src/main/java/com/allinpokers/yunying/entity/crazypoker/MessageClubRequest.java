package com.allinpokers.yunying.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MessageClubRequest  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MessageClubRequest {
    /**
     * 消息uuid
     */
    @ApiModelProperty("消息uuid")
    private String msgId;

    /**
     * 俱乐部id
     */
    @ApiModelProperty("俱乐部id")
    private String clubId;

    /**
     * 创建者用户id
     */
    @ApiModelProperty("创建者用户id")
    private String userId;

    /**
     * 附加参数  json格式
     */
    @ApiModelProperty("附加参数  json格式")
    private String param;

    /**
     * 请求类型  1、创建俱乐部  2、更改交易模式
     */
    @ApiModelProperty("请求类型  1、创建俱乐部  2、更改交易模式")
    private Integer type;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}