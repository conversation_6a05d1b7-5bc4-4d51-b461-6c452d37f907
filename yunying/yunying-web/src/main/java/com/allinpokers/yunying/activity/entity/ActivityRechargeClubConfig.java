package com.allinpokers.yunying.activity.entity;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ActivityRechargeClubConfig  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityRechargeClubConfig {
    /**
     * 俱乐部id
     */
    @ApiModelProperty("俱乐部id")
    private Integer clubId;

    /**
     * 俱乐部赠送上限
     */
    @ApiModelProperty("俱乐部赠送上限")
    private Integer countLimit;

    /**
     * 1=有效  2=活动已达上限被停止  3=被运营人员停止
     */
    @ApiModelProperty("1=有效  2=活动已达上限被停止  3=被运营人员停止")
    private Integer status;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updatedTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createdTime;
}