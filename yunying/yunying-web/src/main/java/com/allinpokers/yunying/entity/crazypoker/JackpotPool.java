package com.allinpokers.yunying.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * JackpotPool  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class JackpotPool {
    /**
     * jackpotId
     */
    @ApiModelProperty("jackpotId")
    private Integer jackpotId;

    /**
     * 1普通局 2奥马哈 3aof
     */
    @ApiModelProperty("1普通局 2奥马哈 3aof")
    private Integer jackpotType;

    /**
     * fund
     */
    @ApiModelProperty("fund")
    private BigDecimal fund;

    /**
     * createdBy
     */
    @ApiModelProperty("createdBy")
    private Integer createdBy;

    /**
     * createdTime
     */
    @ApiModelProperty("createdTime")
    private LocalDateTime createdTime;

    /**
     * updatedBy
     */
    @ApiModelProperty("updatedBy")
    private Integer updatedBy;

    /**
     * updatedTime
     */
    @ApiModelProperty("updatedTime")
    private LocalDateTime updatedTime;

    /**
     * 彩池更新版本，用于控制相同事件不重复更新，与zk对应
     */
    @ApiModelProperty("彩池更新版本，用于控制相同事件不重复更新，与zk对应")
    private Integer version;

    /**
     * 1 = 有效 0 = 无效
     */
    @ApiModelProperty("1 = 有效 0 = 无效")
    private Integer status;
}