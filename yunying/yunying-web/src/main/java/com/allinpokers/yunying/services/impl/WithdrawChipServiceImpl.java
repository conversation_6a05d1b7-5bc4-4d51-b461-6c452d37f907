package com.allinpokers.yunying.services.impl;

import com.allinpokers.yunying.config.YunyingWebConfig;
import com.allinpokers.yunying.config.cache.IHotConfigCache;
import com.allinpokers.yunying.config.cache.bean.AuditConfig;
import com.allinpokers.yunying.dao.crazypoker.*;
import com.allinpokers.yunying.entity.crazypoker.*;
import com.allinpokers.yunying.entity.crazypoker.example.PayChannelEscrowPaymentExample;
import com.allinpokers.yunying.entity.crazypoker.example.PayChannelExample;
import com.allinpokers.yunying.entity.crazypoker.example.WithdrawChipExample;
import com.allinpokers.yunying.enu.*;
import com.allinpokers.yunying.exception.UserException;
import com.allinpokers.yunying.feign.PayChannelClient;
import com.allinpokers.yunying.feign.dto.PayChannelResponse;
import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.model.response.PageBean;
import com.allinpokers.yunying.model.response.accountpayee.BankInfo;
import com.allinpokers.yunying.model.response.withdrawchip.*;
import com.allinpokers.yunying.permission.constants.AuthSystemCode;
import com.allinpokers.yunying.rabbitmq.client.MessageSender;
import com.allinpokers.yunying.rabbitmq.client.bean.CmsMessage;
import com.allinpokers.yunying.rabbitmq.client.bean.OsmMessage;
import com.allinpokers.yunying.rabbitmq.client.bean.cms.PayChannelParams;
import com.allinpokers.yunying.rabbitmq.client.bean.eventtrack.UserAccountInfo;
import com.allinpokers.yunying.rabbitmq.client.bean.eventtrack.UserDataMessage;
import com.allinpokers.yunying.rabbitmq.client.bean.osm.DefaultOsmParams;
import com.allinpokers.yunying.rabbitmq.constant.*;
import com.allinpokers.yunying.services.*;
import com.allinpokers.yunying.services.model.CountExtractTimesAndTotalChip;
import com.allinpokers.yunying.services.model.ListWithdrawChipParams;
import com.allinpokers.yunying.util.JsonUtils;
import com.allinpokers.yunying.util.PhoneNumUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.allinpokers.yunying.enu.ResponseCodeEnum.WITHDRAW_CHIP_AGREE_PARTIAL_FAIL;

/**
 * 提豆申请
 *
 * <AUTHOR>
 */
@Service("withdrawChipService")
@Slf4j
public class WithdrawChipServiceImpl implements WithdrawChipService {
    @Resource
    private WithdrawChipDao withdrawChipDao;
    @Resource
    private UserDetailsInfoService userDetailsInfoService;
    @Resource
    private UserAccountService userAccountService;
    @Resource
    private MessageMoneyRecordService messageMoneyRecordService;
    @Resource(name = "withdrawChipService")
    private WithdrawChipServiceImpl self;
    @Resource
    private PayChannelDao payChannelDao;
    @Resource
    private PayChannelClient payChannelClient;
    @Resource
    private CmsAccountDao cmsAccountDao;
    @Resource
    private CmsAccountLogDao cmsAccountLogDao;
    @Resource
    private ClubMembersService clubMembersService;
    @Resource
    private PlatformAccountDao platformAccountDao;
    @Resource
    private PlatformAccountLogDao platformAccountLogDao;
    @Resource
    private MessageSender messageSender;
    @Resource
    private PayChannelEscrowPaymentDao payChannelEscrowPaymentDao;
    @Resource
    private EscrowSupportService escrowSupportService;
    @Resource
    private AccountPayeeCheckService accountPayeeCheckService;
    @Resource
    private ClubService clubService;
    @Resource
    private TribeService tribeService;
    @Resource
    private TribeGroupRecordService tribeGroupRecordService;
    @Autowired
    private IHotConfigCache configCache;
    @Resource
    private PayChannelService payChannelService;
    @Resource
    private IAuditService auditService;

    @Autowired
    private CentralAccountService centralAccountService;

    @Resource(name = "yunyingWebConfig")
    private YunyingWebConfig config;

    @Override
    public PageBean<WithdrawChipItem> listWithdrawChips(ListWithdrawChipParams params) {

        WithdrawChipExample example = new WithdrawChipExample();
        WithdrawChipExample.Criteria criteria = example.or();
        if (params.getCmsId() != null) {
            criteria.andPayChannelCmsIdEqualTo(params.getCmsId());
        }
        if (params.getStatus() != null) {
            criteria.andStatusEqualTo(params.getStatus());
        }
        if (params.getStartTime() != null) {
            criteria.andCreatedTimeGreaterThanOrEqualTo(params.getStartTime());
        }
        if (params.getEndTime() != null) {
            criteria.andCreatedTimeLessThanOrEqualTo(params.getEndTime());
        }
        if (params.getApplyStartTime() != null) {
            criteria.andCheckTimeIsNotNull().andCheckTimeGreaterThanOrEqualTo(params.getApplyStartTime());
        }
        if (params.getApplyEndTime() != null) {
            criteria.andCheckTimeIsNotNull().andCheckTimeLessThanOrEqualTo(params.getApplyEndTime());
        }
        if (null != params.getUserIds() &&
                !params.getUserIds().isEmpty()) {
            criteria.andUidIn(params.getUserIds());
        }
        if (params.getPayChannelId() != null) {
            criteria.andPayChannelIdEqualTo(params.getPayChannelId());
        }

        PageHelper.startPage(params.getPage(), params.getSize());
        PageHelper.orderBy("created_time desc");
        Page<WithdrawChip> pageList = (Page<WithdrawChip>) withdrawChipDao.selectByExample(example);

        //查找所有用户信息
        Set<Integer> userIds = pageList.stream().map(WithdrawChip::getUid).collect(Collectors.toSet());
        Map<Integer, UserDetailsInfo> userMap = userDetailsInfoService.findUsersMapByUserIds(userIds);

        //查询用户的俱乐部，子联盟，主联盟等信息
        //对应关系：用户id - 俱乐部
        Map<Integer, ClubRecord> userClubMap = clubService.findUserIdClubMap(userIds);
        //对应关系：俱乐部id - 联盟
        Set<Integer> clubIds = userClubMap.values().stream().map(ClubRecord::getId).collect(Collectors.toSet());
        Map<Integer, TribeRecord> clubTribeMap = tribeService.findClubIdTribeMap(clubIds);
        //对应关系：子联盟id - 主联盟
        Set<Integer> subTribeIds = clubTribeMap.values().stream()
                .filter(tribe -> tribe.getIsmain() != null && tribe.getIsmain().equals((short) 0))
                .map(TribeRecord::getId).collect(Collectors.toSet());
        Map<Integer, TribeRecord> subTribeMainTribeMap = tribeGroupRecordService.findSubTribeIdMainTribeMap(subTribeIds);

        //渠道对应关系
        Set<Integer> payChannelIds = pageList.stream().map(WithdrawChip::getPayChannelId).collect(Collectors.toSet());
        Map<Integer, PayChannel> payChannelMap = payChannelService.findPayChannelMap(payChannelIds);

        List<WithdrawChipItem> list = pageList.stream().map(chip -> {
            UserDetailsInfo user = userMap.getOrDefault(chip.getUid(), new UserDetailsInfo());
            ClubRecord club = userClubMap.getOrDefault(user.getUserId(), new ClubRecord());
            TribeRecord subTribe = clubTribeMap.getOrDefault(club.getId(), new TribeRecord());
            //判断是否为子联盟，如果是子联盟，需要获取主联盟名称
            boolean isMainTribe = subTribe.getIsmain() != null && subTribe.getIsmain().equals((short) 1);
            TribeRecord mainTribe = isMainTribe ? subTribe : subTribeMainTribeMap.getOrDefault(subTribe.getId(), new TribeRecord());
            PayChannel payChannel = payChannelMap.getOrDefault(chip.getPayChannelId(), new PayChannel());

            LocalDateTime finishTime = !chip.getStatus().equals(EWithdrawChipStatus.CHECKING.getCode())
                    && !chip.getStatus().equals(EWithdrawChipStatus.PAYING.getCode()) ? chip.getUpdatedTime() : null;
            return WithdrawChipItem.builder()
                    .id(chip.getId())
                    .userRandomId(user.getRandomNum())
                    .userNickname(user.getNikeName())
                    .withdrawNo(chip.getWithdrawNo())
                    .chip(chip.getChip())
                    .chipFee(chip.getFee())
                    .amount(chip.getAmount())
                    .gas(chip.getGas()*1.00)
                    .createdTime(chip.getCreatedTime())
                    .applyTime(chip.getCheckTime())
                    .status(chip.getStatus())
                    .clubName(club.getName())
                    .clubTransType(club.getTransType())
                    .subTribeName(subTribe.getTribeName())
                    .mainTribeName(mainTribe.getTribeName())
                    .payChannelId(chip.getPayChannelId())
                    .payChannelName(payChannel.getName())
                    .finishTime(finishTime)
                    .remark(chip.getRemark())
                    .payNo(chip.getPayNo())
                    .build();
        }).collect(Collectors.toList());

        return PageBean.of(pageList.getTotal(), params.getPage(), params.getSize(), list);
    }

    @Override
    public CommonRespon<List<WithdrawChipProcessResult>> agree(AuthSystemCode authSystemCode,Integer cmsId, Set<Integer> withdrawChipIds,int opUserId) {
        Map<Integer,String> sdkCodeMap = new HashMap<>();
        if (authSystemCode != AuthSystemCode.OSM) { // OSM来源无需校验
            if(!isUseSdk(cmsId))
                return CommonRespon.failure(ResponseCodeEnum.PLEASE_PROCESS_BY_ARTIFICIAL);

            String sdkCode = recommendSdkCode(cmsId);
            sdkCodeMap.put(cmsId,sdkCode);
        }

        // 常规校验
        CommonRespon<List<WithdrawChip>> response = validateAndFind(authSystemCode,cmsId, withdrawChipIds);
        if (!response.isSuccess()) {
            return CommonRespon.failure(response.getCode(), response.getMsg());
        }
        List<WithdrawChip> withdrawChips = response.getData();

        //收款账户信息：字段和 用户收款账户信息表 一致
        List<WithdrawChipProcessResult> results = new ArrayList<>();
        for (WithdrawChip withdrawChip : withdrawChips) {
            ResponseCodeEnum result;
            try {
                Integer tempCmsId;
                if(authSystemCode == AuthSystemCode.OSM){ // OSM来源需要每条提现订单进行SDK校验
                    if (withdrawChip.getTransType()==1){
                        return CommonRespon.failure(ResponseCodeEnum.JUMP_BANK);
                    }
                    tempCmsId = withdrawChip.getPayChannelCmsId();
                    if(!isUseSdk(tempCmsId)){
                        results.add(newResult(withdrawChip, ResponseCodeEnum.PLEASE_PROCESS_BY_ARTIFICIAL));
                        continue;
                    }

                    if(!sdkCodeMap.containsKey(tempCmsId)){
                        String sdkCode = recommendSdkCode(tempCmsId);
                        sdkCodeMap.put(tempCmsId,sdkCode);
                    }
                }else{
                    tempCmsId = cmsId;
                }

                String sdkCode = sdkCodeMap.get(tempCmsId);
                //目前一个cms账号只会有一个渠道，recommendSdkCode 可以在循环外面使用
                result = this.agreeOne(authSystemCode,withdrawChip, opUserId, sdkCode);
            } catch (Exception e) {
                log.error("withDrawChip agree one exception: id=" + withdrawChip.getId(), e);
                result = e instanceof UserException ? ((UserException) e).getResponseEnum() : ResponseCodeEnum.WITHDRAW_CHIP_OPERATION_FAIL;
            }
            results.add(newResult(withdrawChip, result));
        }
        return fillResultField(results);
    }

    private ResponseCodeEnum agreeOne(AuthSystemCode authSystemCode,WithdrawChip withdrawChip, Integer operatorId, String sdkCode) {
        //调用打款接口失败不能回滚所有，所以这里每个申请都需要一个事务来控制
        log.info("withDrawChip agree one start: systemCode={}, id={}, withdrawNo={}, opUserId={}",
                authSystemCode,withdrawChip.getId(), withdrawChip.getWithdrawNo(),operatorId);

        //判断一下提现银行卡编号是否在代充渠道银行卡下,
        List<Bank> banks = payChannelService.queryBank(withdrawChip.getPayChannelId());
        BankInfo bankInfo = JsonUtils.read(withdrawChip.getPayeeAccount(), BankInfo.class);

        if (banks.stream().noneMatch(e -> e.getBankCode().equals(bankInfo.getBankCode()))) {
            return ResponseCodeEnum.NOT_SUPPORT_BANK;
        }

        //1. 修改状态，记录审核人，审核日期
        int changeRow = self.updateStatusToPaying(authSystemCode,withdrawChip, operatorId, sdkCode);
        if (changeRow < 1) {
            //没有成功更新
            return ResponseCodeEnum.WITHDRAW_CHIP_OPERATION_FAIL;
        }
        //2. 成功更新，调打款接口
        //注意：需要提交更新的事务后再调用打款接口
        try {
            log.info("withDrawChip agree one, start to call withdrawApply, withdrawNo={}, sdkType={}", withdrawChip.getWithdrawNo(), sdkCode);
            ZonedDateTime zonedDateTime = LocalDateTime.now().plusMinutes(config.getPayChannelJwtExpireMinutes()).atZone(ZoneId.systemDefault());
            Date expireTime = Date.from(zonedDateTime.toInstant());
            String param = Jwts.builder()
                    .claim("withdrawNo", withdrawChip.getWithdrawNo())
                    .claim("sdkType", sdkCode)
                    .setIssuedAt(new Date())
                    .setExpiration(expireTime)
                    .signWith(SignatureAlgorithm.HS512, config.getPayChannelJwtSecret())
                    .compact();
            PayChannelResponse response = payChannelClient.withdrawApply(param);
            log.info("withDrawChip agree one, call withdrawApply response. " +
                            "id={}, withdrawNo={}, payChannelCode={}, response={}",
                    withdrawChip.getId(), withdrawChip.getWithdrawNo(), sdkCode, response);
            if (response.getStatus() == 0) {
                return ResponseCodeEnum.WITHDRAW_CHIP_OPERATION_SUCCESS;
            }
            //请求失败，将状态修改为提现失败
            String msg = response.getMsg();
            resetStatusToChecking(withdrawChip, msg);
            return ResponseCodeEnum.WITHDRAW_CHIP_OPERATION_FAIL;
        } catch (Exception e) {
            log.error("", e);
            String msg = e.getMessage() + "";
            int length = msg.length() < 1024 ? msg.length() : 1023;
            msg = msg.substring(0, length);
            resetStatusToChecking(withdrawChip, msg);
            return ResponseCodeEnum.WITHDRAW_CHIP_OPERATION_FAIL;
        }
    }

    private void resetStatusToChecking(WithdrawChip withdrawChip, String msg) {
        WithdrawChip toDbChip = WithdrawChip.builder()
                .id(withdrawChip.getId())
                .status(EWithdrawChipStatus.CHECKING.getCode())
                .remark(msg)
                .build();
        withdrawChipDao.updateByPrimaryKeySelective(toDbChip);
    }

    @Transactional(transactionManager = "crazyPokerTransactionManager", rollbackFor = RuntimeException.class)
    public int updateStatusToPaying(AuthSystemCode authSystemCode,WithdrawChip withdrawChip, Integer operatorId, String sdkCode) {
        WithdrawChip toDbChip = WithdrawChip.builder()
                .id(withdrawChip.getId())
                .status(EWithdrawChipStatus.PAYING.getCode())
                .checkType(authSystemCode.name())
                .checkId(operatorId)
                .checkTime(LocalDateTime.now())
                .paymentCode(sdkCode)
                .updatedTime(LocalDateTime.now())
                .build();
        //更新 未审核的这条记录
        WithdrawChipExample example = new WithdrawChipExample();
        example.or().andIdEqualTo(withdrawChip.getId()).andStatusEqualTo(EWithdrawChipStatus.CHECKING.getCode());
        return withdrawChipDao.updateByExampleSelective(toDbChip, example);
    }

    @Override
    public CommonRespon<List<WithdrawChipProcessResult>> reject(AuthSystemCode authSystemCode,Integer cmsId, Set<Integer> withdrawChipIds, String remark,int opUserId) {
        CommonRespon<List<WithdrawChip>> response = validateAndFind(authSystemCode,cmsId, withdrawChipIds);
        if (!response.isSuccess()) {
            return CommonRespon.failure(response.getCode(), response.getMsg());
        }
        List<WithdrawChip> withdrawChips = response.getData();

        List<WithdrawChipProcessResult> results = new ArrayList<>();
        for (WithdrawChip withdrawChip : withdrawChips) {
            //1. 金豆和手续费返回给 用户账户表 可提取金豆
            //2. 战绩流水抵扣的金豆数  返回到 账户表 战绩流水总和
            ResponseCodeEnum result;
            try {
                if(authSystemCode == AuthSystemCode.OSM) // OSM来源，则使用提现订单中对应的代充渠道ID
                    cmsId = withdrawChip.getPayChannelCmsId();
                boolean success = self.rejectOne(authSystemCode,cmsId, withdrawChip, remark,opUserId);
                result = success ? ResponseCodeEnum.WITHDRAW_CHIP_OPERATION_SUCCESS : ResponseCodeEnum.WITHDRAW_CHIP_OPERATION_FAIL;
            } catch (Exception e) {
                log.error("rejectOne, withdrawChipId={}", withdrawChip.getId(), e);
                result = e instanceof UserException ? ((UserException) e).getResponseEnum() : ResponseCodeEnum.WITHDRAW_CHIP_OPERATION_FAIL;
            }
            results.add(newResult(withdrawChip, result));
        }
        return fillResultField(results);
    }
    @Transactional(transactionManager = "crazyPokerTransactionManager", rollbackFor = RuntimeException.class)
    public boolean rejectOne(AuthSystemCode systemCode,Integer checkId,  WithdrawChip withdrawChip, String remark,int opUserId) {
        int addChip = withdrawChip.getChip();
        int addPlCount = withdrawChip.getPlDeduction();
        //返还给用户
        int changeRow = userAccountService.addChipPlCountSubtractLockChipAndLog(withdrawChip.getUid(), EUserAccountLogType.EXTRACT_BEAN_RETURN, addChip, addPlCount, withdrawChip.getId() + "", opUserId);
        if (changeRow < 1) {
            log.info("reject one, addChipPlCountSubtractLockChipAndLog change row < 1, withdrawChipId={}", withdrawChip.getId());
            return false;
        }
        WithdrawChip toDbChip = WithdrawChip.builder()
                .id(withdrawChip.getId())
                .status(EWithdrawChipStatus.CHECK_FAIL.getCode())
                .checkType(systemCode.name())
                .checkId(checkId)
                .checkTime(LocalDateTime.now())
                .remark(remark)
                .updatedTime(LocalDateTime.now())
                .build();
        //更新 未审核的这条记录
        WithdrawChipExample example = new WithdrawChipExample();
        example.or().andIdEqualTo(withdrawChip.getId()).andStatusEqualTo(EWithdrawChipStatus.CHECKING.getCode());
        changeRow = withdrawChipDao.updateByExampleSelective(toDbChip, example);
        if (changeRow < 1) {
            //修改状态失败：待审核 -> 审核失败
            throw new UserException(ResponseCodeEnum.USER_ACCOUNT_NOT_EXIST);
        }
        //发送消息
        EMessageCode messageCode = EMessageCode.MONEY_EXTRACT_KDOU_REQUEST_FAIL;
        Set<String> receiverIds = Sets.newHashSet(withdrawChip.getUid() + "");
        //TODO 未读提豆消息更新
        messageMoneyRecordService.sendMessage(messageCode, "YY-" + checkId, receiverIds, withdrawChip.getWithdrawNo(), withdrawChip.getChip()/100.00 + "", remark);
        return true;
    }
    @Override
    public CommonRespon<List<WithdrawChipProcessResult>> frozen(AuthSystemCode authSystemCode,Integer cmsId, Set<Integer> withdrawChipIds,int opUserId) {
        //所有提款单都可以使用 已处理 ，不需要判断是否能使用sdk
        if (withdrawChipIds.isEmpty()) {
            return CommonRespon.failure(ResponseCodeEnum.PARAM_VALID_FAILED);
        }
        List<WithdrawChip> withdrawChips = this.findByIds(withdrawChipIds);
        if (withdrawChipIds.size() != withdrawChips.size()) {
            //有的ID查询不到
            return CommonRespon.failure(ResponseCodeEnum.DATA_EMPT);
        }
        //操作人不是支付渠道的俱乐部
        if(authSystemCode != AuthSystemCode.OSM) { // OSM操作无需校验
            boolean notMatchClubId = withdrawChips.stream().anyMatch(chip -> !chip.getPayChannelCmsId().equals(cmsId));
            if (notMatchClubId) {
                return CommonRespon.failure(ResponseCodeEnum.PERMISSION_ACCESS_DENIED);
            }
        }
        //收款账户信息：字段和 用户收款账户信息表 一致
        List<WithdrawChipProcessResult> results = new ArrayList<>();
        for (WithdrawChip withdrawChip : withdrawChips) {
            ResponseCodeEnum result = ResponseCodeEnum.WITHDRAW_CHIP_OPERATION_SUCCESS;
            try {
                log.info("processedAccountPayeeChecks: withdrawChipId={}, cmsId={}", withdrawChip.getId(), cmsId);
                //设置点击已处理的时间，在里面更新
                if (withdrawChip.getCheckTime() == null) {
                    withdrawChip.setCheckTime(LocalDateTime.now());
                }
                self.frozenOne(withdrawChip, false,authSystemCode,opUserId);
            } catch (Exception e) {
                log.error("withDrawChip processed one exception: id=" + withdrawChip.getId(), e);
                result = e instanceof UserException ? ((UserException) e).getResponseEnum() : ResponseCodeEnum.WITHDRAW_CHIP_OPERATION_FAIL;
            }
            results.add(newResult(withdrawChip, result));
        }
        return fillResultField(results);
    }

    @Transactional(transactionManager = "crazyPokerTransactionManager", rollbackFor = RuntimeException.class)
    public void frozenOne(WithdrawChip withdrawChip, boolean isManual,AuthSystemCode authSystemCode,int opUserId){
        if (withdrawChip.getStatus().equals(EWithdrawChipStatus.PAY_SUCCESS.getCode())) {
            log.info("processSuccessCallback, status is pay success, return. id={}, withdrawNo={}", withdrawChip.getId(), withdrawChip.getWithdrawNo());
            return;
        }
        log.info("processSuccessCallback: {}", withdrawChip);

        //0. 修改状态
        AuthSystemCode checkType = AuthSystemCode.ofName(withdrawChip.getCheckType());
        if(checkType == null){
            checkType = authSystemCode;
        }
        if(opUserId == -1)
            opUserId = withdrawChip.getCheckId();
        WithdrawChip toDbChip = WithdrawChip.builder()
                .id(withdrawChip.getId())
                .status(EWithdrawChipStatus.FROZEN_CHECK_FAIL.getCode())
                .checkType(checkType.name())
                .checkId(opUserId)
                .checkTime(withdrawChip.getCheckTime())
                .updatedTime(LocalDateTime.now())
                .build();

        List<Integer> beforeStatusList = Lists.newArrayList(EWithdrawChipStatus.CHECKING.getCode(), EWithdrawChipStatus.PAYING.getCode());
        WithdrawChipExample example = new WithdrawChipExample();
        example.or().andIdEqualTo(withdrawChip.getId()).andStatusIn(beforeStatusList);
        int changeRow = withdrawChipDao.updateByExampleSelective(toDbChip, example);
        if (changeRow < 1) {
            //修改状态失败：打款中 -> 打款成功  或  未审核  -> 打款成功
            log.info("processSuccessCallback, update status fail, return. id={}, withdrawNo={}, beforeStatus={}",
                    withdrawChip.getId(), withdrawChip.getWithdrawNo(), beforeStatusList);
            return;
        }

        //减少用户账户锁定金豆
        changeRow = userAccountService.subtractLockChip(withdrawChip.getUid(), withdrawChip.getChip());
        if (changeRow < 1) {
            //减少用户账户锁定金豆失败
            log.info("processSuccessCallback, subtract lock chip fail, return. id={}, withdrawNo={}, beforeStatus={}, uid={}",
                    withdrawChip.getId(), withdrawChip.getWithdrawNo(), beforeStatusList, withdrawChip.getUid());
            throw new UserException(ResponseCodeEnum.USER_ACCOUNT_NOT_EXIST);
        }

        if(checkType != AuthSystemCode.OSM) { // CMS来源，保持原来逻辑
            ClubMembers clubMember = clubMembersService.findByUserId(withdrawChip.getUid() + "");
            Integer clubId = clubMember == null ? null : clubMember.getClubId();

            Integer cmsId = withdrawChip.getPayChannelCmsId();
            //1. 金豆：给对应的支付渠道的CMS账号增加金豆
            changeRow = cmsAccountDao.addChip(cmsId, withdrawChip.getChip());
            if (changeRow < 1) {
                throw new UserException(ResponseCodeEnum.USER_ACCOUNT_NOT_EXIST);
            }

            //2. 变更记录：新增一条CMS账号的变更记录
            CmsAccount cmsAccount = cmsAccountDao.selectByPrimaryKey(cmsId);
            log.info("processSuccessCallback: cmsAccountId={}, cmsChip={}, withdrawChip={}", cmsId, cmsAccount.getChip(), withdrawChip.getChip());
            Long cmsCurChip = cmsAccount.getChip() - withdrawChip.getChip();
            CmsAccountLog cmsAccountLog = CmsAccountLog.builder()
                    .cmsUid(cmsId)
                    .type(3)
                    .currChip(cmsCurChip)
                    .changeChip(withdrawChip.getChip())
                    .opId(opUserId)
                    .clubId(clubId)
                    .externalId(withdrawChip.getId() + "")
                    .createdTime(LocalDateTime.now())
                    .build();
            cmsAccountLogDao.insert(cmsAccountLog);
        }else{
            // 1.玩家的提现金豆返回中央仓
            LocalDateTime sysTime = LocalDateTime.now();
            int updatedNum = this.centralAccountService.incChip(withdrawChip.getChip(),sysTime);
            if (updatedNum < 1) {
                throw new UserException(ResponseCodeEnum.USER_ACCOUNT_NOT_EXIST);
            }

            // 2.中央仓变动日志
            CentralAccount centralAccount = this.centralAccountService.selectCentralAccount();
            Long curChip = centralAccount.getChip() - withdrawChip.getChip();
            CentralAccountLog centralAccountLog = CentralAccountLog.builder()
                    .type(4)
                    .uid(withdrawChip.getUid())
                    .currChip(curChip)
                    .changeChip(withdrawChip.getChip())
                    .createdTime(sysTime).build();
            this.centralAccountService.addCentralAccountLog(centralAccountLog);
        }

        //3. 手续费：给系统仓增加金豆（platform_account, code=300）
        if (withdrawChip.getFee() != null && withdrawChip.getFee() > 0) {
            int platformCode = EPlatformAccountCode.SYSTEM.getCode();
            platformAccountDao.addChip(platformCode, withdrawChip.getFee());

            //4. 变更记录：新增一条系统仓变更记录
            PlatformAccount platformAccount = platformAccountDao.selectByPrimaryKey(platformCode);
            Long platformCurChip = platformAccount.getChip() - withdrawChip.getFee();
            PlatformAccountLog platformAccountLog = PlatformAccountLog.builder()
                    .platformCode(platformCode)
                    .type(12)
                    .changeSource(7)
                    .currentChip(platformCurChip)
                    .changeChip(withdrawChip.getFee())
                    .externalId(withdrawChip.getId() + "")
                    .opId(opUserId)
                    .createdTime(LocalDateTime.now())
                    .build();
            platformAccountLogDao.insertSelective(platformAccountLog);
        }
        //3. 手续费：给系统仓增加金豆（platform_account, code=300）
        if (withdrawChip.getGas() != null && withdrawChip.getGas() > 0) {
            int platformCode = EPlatformAccountCode.SYSTEM.getCode();
            platformAccountDao.addChip(platformCode, withdrawChip.getGas());
            //4. 变更记录：新增一条系统仓变更记录
            PlatformAccount platformAccount = platformAccountDao.selectByPrimaryKey(platformCode);
            Long platformCurChip = platformAccount.getChip()-withdrawChip.getGas();
            PlatformAccountLog platformAccountLog = PlatformAccountLog.builder()
                    .platformCode(platformCode)
                    .type(37)
                    .changeSource(7)
                    .currentChip(platformCurChip)
                    .changeChip(withdrawChip.getGas())
                    .externalId(withdrawChip.getId() + "")
                    .opId(opUserId)
                    .createdTime(LocalDateTime.now())
                    .build();
            platformAccountLogDao.insertSelective(platformAccountLog);
        }

        try {
            // 审计告警
            auditService.insertAuditWarnTask(withdrawChip.getId() + "", withdrawChip.getUid(),
                    withdrawChip.getChip(), withdrawChip.getChip(), 3, new Date());

            // 数据统计发送消息
            messageSender.sendEventTrackMessage(UserDataMessage.builder()
                    .message(JsonUtils.write(
                            UserAccountInfo.builder()
                                    .userId(withdrawChip.getUid())
                                    .type(isManual ? EUserAccMessageType.MANUAL_WITHDRAW.getCode() : EUserAccMessageType.PAY_CHANNEL_WITHDRAW.getCode())
                                    .amount(withdrawChip.getAmount().intValue())
                                    .orderId(withdrawChip.getId().toString())
                                    .build()
                    ))
                    .msgType(EUserdDataMessageType.USER_ACC_CHANGE.getCode())
                    .build());

        } catch (Exception e) {
            log.error("充值发数据统计消息异常", e);
        }


        //发送消息
        EMessageCode messageCode = EMessageCode.MONEY_EXTRACT_KDOU_FROZEN;
        Set<String> receiverIds = Sets.newHashSet(withdrawChip.getUid() + "");
        messageMoneyRecordService.sendMessage(messageCode, String.valueOf(withdrawChip.getPayChannelCmsId()), receiverIds, withdrawChip.getChip()/100.00 + "");

        //发送cms消息
        ECmsMessageCode cmsMessageCode = ECmsMessageCode.PAY_CHANNEL_EXTRACT_DOU_F;
        sendCmsMessage(withdrawChip, cmsMessageCode);


        //发送osm消息
//        sendOsmMessageAfterSuccess(withdrawChip);
    }
    private CommonRespon<List<WithdrawChip>> validateAndFind(AuthSystemCode systemCode,Integer cmsId, Set<Integer> withdrawChipIds) {
        if (withdrawChipIds.isEmpty()) {
            return CommonRespon.failure(ResponseCodeEnum.PARAM_VALID_FAILED);
        }
        List<WithdrawChip> withdrawChips = this.findByIds(withdrawChipIds);
        if (withdrawChipIds.size() != withdrawChips.size()) {
            //有的ID查询不到
            return CommonRespon.failure(ResponseCodeEnum.DATA_EMPT);
        }
        //操作人不是支付渠道的俱乐部
        if(systemCode != AuthSystemCode.OSM) { // OSM来源，此参数为NULL，无需校验
            boolean notMatchClubId = withdrawChips.stream().anyMatch(chip -> !chip.getPayChannelCmsId().equals(cmsId));
            if (notMatchClubId) {
                return CommonRespon.failure(ResponseCodeEnum.PERMISSION_ACCESS_DENIED);
            }
        }
        boolean hasNoWaitStatus = withdrawChips.stream().anyMatch(chip -> !chip.getStatus().equals(EWithdrawChipStatus.CHECKING.getCode()));
        if (hasNoWaitStatus) {
            //有已处理的提豆申请，提示操作员
            return CommonRespon.failure(ResponseCodeEnum.WITHDRAW_CHIP_IS_PROCESSED);
        }
        return CommonRespon.success(withdrawChips);
    }

    private List<WithdrawChip> findByIds(Collection<Integer> withdrawChipIds) {
        if (withdrawChipIds.isEmpty()) {
            return new ArrayList<>();
        }

        WithdrawChipExample example = new WithdrawChipExample();
        example.or().andIdIn(new ArrayList<>(withdrawChipIds));
        return withdrawChipDao.selectByExample(example);
    }

    @Override
    public void processCallback(String msgId, String withdrawNo, Integer status) {
        log.info("processCallback: msgId={}, withdrawNo={}, status={}", msgId, withdrawNo, status);
        WithdrawChip withdrawChip = this.findByWithdrawNo(withdrawNo);
        if (withdrawChip == null) {
            log.info("withdraw process callback, can't find withdrawChip record. " +
                    "msgId={}, withdrawChipNo={}, status={}", msgId, withdrawNo, status);
            return;
        }
        if (status.equals(1)) {
            //成功
            self.processSuccessCallback(withdrawChip,  true,null,-1);
        } else {
            //失败
            //1. 金豆和手续费返回给 用户账户表 可提取金豆
            //2. 战绩流水抵扣的金豆数  返回到 账户表 战绩流水总和
            self.processFailureCallback(withdrawChip);
        }
    }

    @Transactional(transactionManager = "crazyPokerTransactionManager", rollbackFor = RuntimeException.class)
    public void processFailureCallback(WithdrawChip withdrawChip) {
        int addChip = withdrawChip.getChip();
        int addPlCount = withdrawChip.getPlDeduction();
        int changeRow = userAccountService.addChipPlCountSubtractLockChipAndLog(withdrawChip.getUid(), EUserAccountLogType.EXTRACT_BEAN_RETURN, addChip, addPlCount, withdrawChip.getId() + "", -1);
        if (changeRow < 1) {
            log.info("processFailureCallback, addChipPlCountSubtractLockChipAndLog change row < 1, withdrawChipId={}", withdrawChip.getId());
            return;
        }
        WithdrawChip toDbChip = WithdrawChip.builder()
                .id(withdrawChip.getId())
                .status(EWithdrawChipStatus.PAY_FAIL.getCode())
                .checkType("")
                .checkId(-1)
                .checkTime(withdrawChip.getCheckTime())
                .updatedTime(LocalDateTime.now())
                .build();
        //更新 未审核的这条记录
        WithdrawChipExample example = new WithdrawChipExample();
        example.or().andIdEqualTo(withdrawChip.getId()).andStatusEqualTo(EWithdrawChipStatus.PAYING.getCode());
        changeRow = withdrawChipDao.updateByExampleSelective(toDbChip, example);
        if (changeRow < 1) {
            //修改状态失败：打款中 -> 打款失败
            throw new UserException(ResponseCodeEnum.USER_ACCOUNT_NOT_EXIST);
        }
        //发送消息
        EMessageCode messageCode = EMessageCode.MONEY_EXTRACT_KDOU_FAIL;
        Set<String> receiverIds = Sets.newHashSet(withdrawChip.getUid() + "");
        messageMoneyRecordService.sendMessageNotParams(messageCode, "-1", receiverIds);
    }

    @Transactional(transactionManager = "crazyPokerTransactionManager", rollbackFor = RuntimeException.class)
    public void processSuccessCallback(WithdrawChip withdrawChip, boolean isManual,AuthSystemCode authSystemCode,int opUserId) {
        if (withdrawChip.getStatus().equals(EWithdrawChipStatus.PAY_SUCCESS.getCode())) {
            log.info("processSuccessCallback, status is pay success, return. id={}, withdrawNo={}", withdrawChip.getId(), withdrawChip.getWithdrawNo());
            return;
        }
        log.info("processSuccessCallback: {}", withdrawChip);

        //0. 修改状态
        AuthSystemCode checkType = AuthSystemCode.ofName(withdrawChip.getCheckType());
        if(checkType == null){
            checkType = authSystemCode;
        }
        if(opUserId == -1)
            opUserId = withdrawChip.getCheckId();
        WithdrawChip toDbChip = WithdrawChip.builder()
                .id(withdrawChip.getId())
                .status(EWithdrawChipStatus.PAY_SUCCESS.getCode())
                .checkType(checkType.name())
                .checkId(opUserId)
                .checkTime(withdrawChip.getCheckTime())
                .updatedTime(LocalDateTime.now())
                .build();

        List<Integer> beforeStatusList = Lists.newArrayList(EWithdrawChipStatus.CHECKING.getCode(), EWithdrawChipStatus.PAYING.getCode());
        WithdrawChipExample example = new WithdrawChipExample();
        example.or().andIdEqualTo(withdrawChip.getId()).andStatusIn(beforeStatusList);
        int changeRow = withdrawChipDao.updateByExampleSelective(toDbChip, example);
        if (changeRow < 1) {
            //修改状态失败：打款中 -> 打款成功  或  未审核  -> 打款成功
            log.info("processSuccessCallback, update status fail, return. id={}, withdrawNo={}, beforeStatus={}",
                    withdrawChip.getId(), withdrawChip.getWithdrawNo(), beforeStatusList);
            return;
        }

        //减少用户账户锁定金豆
        changeRow = userAccountService.subtractLockChip(withdrawChip.getUid(), withdrawChip.getChip());
        if (changeRow < 1) {
            //减少用户账户锁定金豆失败
            log.info("processSuccessCallback, subtract lock chip fail, return. id={}, withdrawNo={}, beforeStatus={}, uid={}",
                    withdrawChip.getId(), withdrawChip.getWithdrawNo(), beforeStatusList, withdrawChip.getUid());
            throw new UserException(ResponseCodeEnum.USER_ACCOUNT_NOT_EXIST);
        }

        if(checkType != AuthSystemCode.OSM) { // CMS来源，保持原来逻辑
            ClubMembers clubMember = clubMembersService.findByUserId(withdrawChip.getUid() + "");
            Integer clubId = clubMember == null ? null : clubMember.getClubId();

            Integer cmsId = withdrawChip.getPayChannelCmsId();
            //1. 金豆：给对应的支付渠道的CMS账号增加金豆
            changeRow = cmsAccountDao.addChip(cmsId, withdrawChip.getChip());
            if (changeRow < 1) {
                throw new UserException(ResponseCodeEnum.USER_ACCOUNT_NOT_EXIST);
            }

            //2. 变更记录：新增一条CMS账号的变更记录
            CmsAccount cmsAccount = cmsAccountDao.selectByPrimaryKey(cmsId);
            log.info("processSuccessCallback: cmsAccountId={}, cmsChip={}, withdrawChip={}", cmsId, cmsAccount.getChip(), withdrawChip.getChip());
            Long cmsCurChip = cmsAccount.getChip() - withdrawChip.getChip();
            CmsAccountLog cmsAccountLog = CmsAccountLog.builder()
                    .cmsUid(cmsId)
                    .type(3)
                    .currChip(cmsCurChip)
                    .changeChip(withdrawChip.getChip())
                    .opId(opUserId)
                    .clubId(clubId)
                    .externalId(withdrawChip.getId() + "")
                    .createdTime(LocalDateTime.now())
                    .build();
            cmsAccountLogDao.insert(cmsAccountLog);
        }else{
            // 1.玩家的提现金豆返回中央仓
            LocalDateTime sysTime = LocalDateTime.now();
            int updatedNum = this.centralAccountService.incChip(withdrawChip.getChip(),sysTime);
            if (updatedNum < 1) {
                throw new UserException(ResponseCodeEnum.USER_ACCOUNT_NOT_EXIST);
            }

            // 2.中央仓变动日志
            CentralAccount centralAccount = this.centralAccountService.selectCentralAccount();
            Long curChip = centralAccount.getChip() - withdrawChip.getChip();
            CentralAccountLog centralAccountLog = CentralAccountLog.builder()
                    .type(4)
                    .uid(withdrawChip.getUid())
                    .currChip(curChip)
                    .changeChip(withdrawChip.getChip())
                    .createdTime(sysTime).build();
            this.centralAccountService.addCentralAccountLog(centralAccountLog);
        }

        //3. 手续费：给系统仓增加金豆（platform_account, code=300）
        if (withdrawChip.getFee() != null && withdrawChip.getFee() > 0) {
            int platformCode = EPlatformAccountCode.SYSTEM.getCode();
            platformAccountDao.addChip(platformCode, withdrawChip.getFee());

            //4. 变更记录：新增一条系统仓变更记录
            PlatformAccount platformAccount = platformAccountDao.selectByPrimaryKey(platformCode);
            Long platformCurChip = platformAccount.getChip() - withdrawChip.getFee();
            PlatformAccountLog platformAccountLog = PlatformAccountLog.builder()
                    .platformCode(platformCode)
                    .type(12)
                    .changeSource(7)
                    .currentChip(platformCurChip)
                    .changeChip(withdrawChip.getFee())
                    .externalId(withdrawChip.getId() + "")
                    .opId(opUserId)
                    .createdTime(LocalDateTime.now())
                    .build();
            platformAccountLogDao.insertSelective(platformAccountLog);
        }
        //3. 手续费：给系统仓增加金豆（platform_account, code=300）
        if (withdrawChip.getGas() != null && withdrawChip.getGas() > 0) {
            int platformCode = EPlatformAccountCode.SYSTEM.getCode();
            platformAccountDao.addChip(platformCode, withdrawChip.getGas());
            //4. 变更记录：新增一条系统仓变更记录
            PlatformAccount platformAccount = platformAccountDao.selectByPrimaryKey(platformCode);
            Long platformCurChip = platformAccount.getChip()-withdrawChip.getGas();
            PlatformAccountLog platformAccountLog = PlatformAccountLog.builder()
                    .platformCode(platformCode)
                    .type(37)
                    .changeSource(7)
                    .currentChip(platformCurChip)
                    .changeChip(withdrawChip.getGas())
                    .externalId(withdrawChip.getId() + "")
                    .opId(opUserId)
                    .createdTime(LocalDateTime.now())
                    .build();
            platformAccountLogDao.insertSelective(platformAccountLog);
        }

        try {
            // 审计告警
            auditService.insertAuditWarnTask(withdrawChip.getId() + "", withdrawChip.getUid(),
                    withdrawChip.getChip(), withdrawChip.getChip(), 3, new Date());

            // 数据统计发送消息
            messageSender.sendEventTrackMessage(UserDataMessage.builder()
                    .message(JsonUtils.write(
                            UserAccountInfo.builder()
                                    .userId(withdrawChip.getUid())
                                    .type(isManual ? EUserAccMessageType.MANUAL_WITHDRAW.getCode() : EUserAccMessageType.PAY_CHANNEL_WITHDRAW.getCode())
                                    .amount(withdrawChip.getAmount().intValue())
                                    .orderId(withdrawChip.getId().toString())
                                    .build()
                    ))
                    .msgType(EUserdDataMessageType.USER_ACC_CHANGE.getCode())
                    .build());

        } catch (Exception e) {
            log.error("充值发数据统计消息异常", e);
        }


        //发送消息
        EMessageCode messageCode = EMessageCode.MONEY_EXTRACT_KDOU_SUCCESS;
        Set<String> receiverIds = Sets.newHashSet(withdrawChip.getUid() + "");
        messageMoneyRecordService.sendMessage(messageCode, String.valueOf(withdrawChip.getPayChannelCmsId()), receiverIds, withdrawChip.getChip()/100.00 + "");

        //发送cms消息
        ECmsMessageCode cmsMessageCode = ECmsMessageCode.PAY_CHANNEL_EXTRACT_DOU_S;
        sendCmsMessage(withdrawChip, cmsMessageCode);


        //发送osm消息
//        sendOsmMessageAfterSuccess(withdrawChip);
    }

    private void sendOsmMessageAfterSuccess(WithdrawChip withdrawChip) {
        try {
            AuditConfig auditConfig = configCache.auditConfig();
            if (auditConfig == null) {
                log.warn("sendOsmMessageAfterSuccess, but audit config is null. withdrawNo={}", withdrawChip.getWithdrawNo());
                return;
            }
            AuditConfig.Warning warning = auditConfig.getWarning();
            if (warning == null) {
                log.warn("sendOsmMessageAfterSuccess, but audit config's warning is null. withdrawNo={}", withdrawChip.getWithdrawNo());
                return;
            }
            log.info("sendOsmMessageAfterSuccess, withdrawNo={}, warning is {}", withdrawChip.getWithdrawNo(), warning);
            //统计用户24小时内充豆金额
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.plusDays(-1);
            CountExtractTimesAndTotalChip count = withdrawChipDao.countSuccessExtractByUid(withdrawChip.getUid(), startTime, endTime);

            List<String> messages = new ArrayList<>();
            boolean onceNumMatch = warning.getExtractChipOnceNum() != null && withdrawChip.getChip() > warning.getExtractChipOnceNum();
            if (onceNumMatch) {
                //审计系统报警-用户成功提豆单笔超过300万金豆
                messages.add(String.format("单笔大额提豆（%s）", withdrawChip.getChip()));
            }
            boolean totalMatch = warning.getExtractChipTotalIn24h() != null && count.getTotalChip() > warning.getExtractChipTotalIn24h();
            if (totalMatch) {
                //同一用户成功提豆24小时内累计超500万金豆
                messages.add(String.format("当日累计大额提豆（%s）", count.getTotalChip()));
            }
            boolean timesMatch = warning.getExtractChipTimesIn24h() != null && count.getTimes() > warning.getExtractChipTimesIn24h();
            if (timesMatch) {
                //单用户24小时内提豆成功超出5笔
                messages.add(String.format("当日提豆过于频繁（第%s笔）", count.getTimes()));
            }

            if (messages.isEmpty()) {
                log.info("sendOsmMessageAfterSuccess: withdrawNo={}, messages is empty", withdrawChip.getWithdrawNo());
                return;
            }
            String message = Strings.join(messages, ',');
            DefaultOsmParams params = new DefaultOsmParams(message);
            OsmMessage osmMessage = OsmMessage.builder()
                    .senderId("-1")
                    .type(EOsmMessageCode.AUDIT_SYSTEM_WARNING_EXTRACT_CHIP_USER.getCode())
                    .params(JsonUtils.write(params))
                    .build();
            messageSender.sendOsmMessage(osmMessage);
        } catch (Exception e) {
            log.error("sendOsmMessageAfterSuccess catch en exception. withdrawNo={}", withdrawChip.getWithdrawNo(), e);
        }
    }

    private void sendCmsMessage(WithdrawChip withdrawChip, ECmsMessageCode cmsMessageCode) {
        UserDetailsInfo user = userDetailsInfoService.selectByPrimaryKey(withdrawChip.getUid());
        PayChannelParams params = PayChannelParams.builder()
                .cmsAccountId(withdrawChip.getPayChannelCmsId())
                .userId(withdrawChip.getUid())
                .userNickname(user == null ? "" : user.getNikeName())
                .douNum(withdrawChip.getChip())
                .time(System.currentTimeMillis())
                .build();
        CmsMessage cmsMessage = CmsMessage.builder()
                .senderId("-1")
                .type(cmsMessageCode.getCode())
                .params(JsonUtils.write(params))
                .build();
        messageSender.sendCmsMessage(cmsMessage);
    }

    @Override
    public WithdrawChip findByWithdrawNo(String withdrawNo) {
        WithdrawChipExample example = new WithdrawChipExample();
        example.or().andWithdrawNoEqualTo(withdrawNo);
        List<WithdrawChip> withdrawChips = withdrawChipDao.selectByExample(example);
        return withdrawChips.isEmpty() ? null : withdrawChips.get(0);
    }

    @Override
    public boolean isUseSdk(Integer cmsId) {
        PayChannelExample channelExample = new PayChannelExample();
        channelExample.or().andCmsUidEqualTo(cmsId);
        List<PayChannel> payChannels = payChannelDao.selectByExample(channelExample);
        if (payChannels.isEmpty()) {
            return false;
        }
        String recommendSdkCode = recommendSdkCode(cmsId);

        return recommendSdkCode != null;
    }

    /**
     * 获取cms的可用的sdkCode
     *
     * @param cmsId
     * @return
     */
    private String recommendSdkCode(Integer cmsId) {
        //查询系统支持的sdk
        List<EscrowSupport> supports = escrowSupportService.findByStatus(EscrowSupportStatus.ENABLE);
        if (supports.isEmpty()) {
            return null;
        }

        //查找该cms账号拥有的渠道，目前只会有一个
        PayChannelExample channelExample = new PayChannelExample();
        //1为提现
        channelExample.or().andCmsUidEqualTo(cmsId);
        List<PayChannel> payChannels = payChannelDao.selectByExample(channelExample);
        if (payChannels.isEmpty()) {
            return null;
        }


        List<Integer> payChannelIds = payChannels.stream().map(PayChannel::getId).collect(Collectors.toList());

        //查找这些渠道配置的sdk信息，可用的状态
        PayChannelEscrowPaymentExample paymentExample = new PayChannelEscrowPaymentExample();
        paymentExample.or().andPayChannelIdIn(payChannelIds)
                .andStatusEqualTo(EPayChannelEscrowPaymentStatus.ENABLE.getCode())
                .andTypeEqualTo(1)
        ;
        List<PayChannelEscrowPayment> payChannelEscrowPayments = payChannelEscrowPaymentDao.selectByExample(paymentExample);


        Map<Integer, List<PayChannelEscrowPayment>> paymentMap = payChannelEscrowPayments.stream()
                //特殊操作配置Upay提现让他手动处理
                .filter(e -> !"Upay".equals(e.getPaymentCode()))
                .collect(Collectors.groupingBy(PayChannelEscrowPayment::getPriority));

        if (paymentMap == null || paymentMap.size() == 0) {
            return null;
        }

        List<PayChannelEscrowPayment> targetList = paymentMap.get(paymentMap.keySet().stream().min(Integer::compareTo).get());

        Random random = new Random(System.currentTimeMillis());
        int index = Math.abs(random.nextInt() % targetList.size());
        return targetList.get(index).getPaymentCode();
    }

    @Override
    public CommonRespon<List<WithdrawChipProcessResult>> processedAccountPayeeChecks(AuthSystemCode authSystemCode,Integer cmsId, Set<Integer> withdrawChipIds,int opUserId) {
        //所有提款单都可以使用 已处理 ，不需要判断是否能使用sdk
        if (withdrawChipIds.isEmpty()) {
            return CommonRespon.failure(ResponseCodeEnum.PARAM_VALID_FAILED);
        }
        List<WithdrawChip> withdrawChips = this.findByIds(withdrawChipIds);
        if (withdrawChipIds.size() != withdrawChips.size()) {
            //有的ID查询不到
            return CommonRespon.failure(ResponseCodeEnum.DATA_EMPT);
        }
        //操作人不是支付渠道的俱乐部
        if(authSystemCode != AuthSystemCode.OSM) { // OSM操作无需校验
            boolean notMatchClubId = withdrawChips.stream().anyMatch(chip -> !chip.getPayChannelCmsId().equals(cmsId));
            if (notMatchClubId) {
                return CommonRespon.failure(ResponseCodeEnum.PERMISSION_ACCESS_DENIED);
            }
        }
        //收款账户信息：字段和 用户收款账户信息表 一致
        List<WithdrawChipProcessResult> results = new ArrayList<>();
        for (WithdrawChip withdrawChip : withdrawChips) {
            ResponseCodeEnum result = ResponseCodeEnum.WITHDRAW_CHIP_OPERATION_SUCCESS;
            try {
                log.info("processedAccountPayeeChecks: withdrawChipId={}, cmsId={}", withdrawChip.getId(), cmsId);
                //设置点击已处理的时间，在里面更新
                if (withdrawChip.getCheckTime() == null) {
                    withdrawChip.setCheckTime(LocalDateTime.now());
                }
                self.processSuccessCallback(withdrawChip, false,authSystemCode,opUserId);
            } catch (Exception e) {
                log.error("withDrawChip processed one exception: id=" + withdrawChip.getId(), e);
                result = e instanceof UserException ? ((UserException) e).getResponseEnum() : ResponseCodeEnum.WITHDRAW_CHIP_OPERATION_FAIL;
            }
            results.add(newResult(withdrawChip, result));
        }
        return fillResultField(results);
    }

    private WithdrawChipProcessResult newResult(WithdrawChip withdrawChip, ResponseCodeEnum result) {
        return WithdrawChipProcessResult.builder()
                .withdrawNo(withdrawChip.getWithdrawNo())
                .userId(withdrawChip.getUid())
                .chip(withdrawChip.getChip())
                .amount(withdrawChip.getAmount())
                .result(result)
                .resultCode(result.getCode())
                .resultMsg(result.getMsg())
                .build();
    }

    private CommonRespon<List<WithdrawChipProcessResult>> fillResultField(List<WithdrawChipProcessResult> results){
        Set<Integer> userIds = results.stream().map(WithdrawChipProcessResult::getUserId).collect(Collectors.toSet());
        Map<Integer, UserDetailsInfo> userMap = userDetailsInfoService.findUsersMapByUserIds(userIds);
        Map<Integer, ClubRecord> clubMap = clubService.findUserIdClubMap(userIds);

        results.forEach(result -> {
            UserDetailsInfo user = userMap.getOrDefault(result.getUserId(), new UserDetailsInfo());
            ClubRecord club = clubMap.getOrDefault(user.getUserId(), new ClubRecord());
            result.setUserRandomId(user.getRandomNum());
            result.setUserNickname(user.getNikeName());
            result.setClubName(club.getName());
        });

        //只操作一个, 状态码也需要在最外部返回
        boolean isSuccess = results.get(0).getResult().equals(ResponseCodeEnum.WITHDRAW_CHIP_OPERATION_SUCCESS);
        ResponseCodeEnum code = results.size() == 1 ? (isSuccess ? ResponseCodeEnum.SUCCESS : results.get(0).getResult()) : ResponseCodeEnum.SUCCESS;
        CommonRespon<List<WithdrawChipProcessResult>> resultResponse = CommonRespon.failure(code);
        resultResponse.setData(results);
        return resultResponse;
    }

    @Override
    public CommonRespon<AccountInfo> getAccountInfo(Integer cmsId, AuthSystemCode systemCode, Integer withdrawChipId) {
        WithdrawChip withdrawChip = withdrawChipDao.selectByPrimaryKey(withdrawChipId);
        //osm系统不受影响
        boolean notAllow = !systemCode.equals(AuthSystemCode.OSM) && !cmsId.equals(withdrawChip.getPayChannelCmsId());
        if (notAllow) {
            return CommonRespon.failure(ResponseCodeEnum.PERMISSION_ACCESS_DENIED);
        }

        BankInfo bankInfo = JsonUtils.read(withdrawChip.getPayeeAccount(), BankInfo.class);
        //多语言，重新设置bankName
        accountPayeeCheckService.setBankOfLang(bankInfo);

        UserDetailsInfo user = userDetailsInfoService.selectByPrimaryKey(withdrawChip.getUid());
        String contact = user == null ? "" : PhoneNumUtil.decoder(user.getPhone());
        AccountInfo accountInfo = AccountInfo.builder()
                .bankAccName(bankInfo.getBankAccName())
                .bankAccNo(bankInfo.getBankAccNo())
                .bankName(bankInfo.getBankName())
                .bankCode(bankInfo.getBankCode())
                .bankOfDeposit(bankInfo.getBankOfDeposit())
                .contact(contact)
                .build();
        return CommonRespon.success(accountInfo);
    }

    @Override
    public WithdrawChipTotal calcTotal(Integer operatorId, LocalDateTime startTime, LocalDateTime endTime) {
        return withdrawChipDao.calcTotal(operatorId, startTime, endTime);
    }

    @Override
    public WithdrawChipPlatformTotal countTotal(ListWithdrawChipParams params) {
        log.info("countTotal params={} ",JsonUtils.write(params));
        WithdrawChipPlatformTotal total = withdrawChipDao.calcTotalByParams(params);
        log.info("countTotal total={} ",JsonUtils.write(total));
        total.calcScore();
        return total;
    }
}
