package com.allinpokers.yunying.tier.bean;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * ClubTierQuery
 *
 * <AUTHOR>
 * @since 2025/6/18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClubTierQuery {

    private Integer tierId;
    private Integer tribeId;
    private List<Integer> clubRandomIds;

}
