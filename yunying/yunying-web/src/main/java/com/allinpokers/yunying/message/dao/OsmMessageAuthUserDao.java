package com.allinpokers.yunying.message.dao;

import com.allinpokers.yunying.dao.BaseDao;
import com.allinpokers.yunying.message.entity.OsmMessageAuthUser;
import com.allinpokers.yunying.message.entity.example.OsmMessageAuthUserExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * OSM消息 - 用户  Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface OsmMessageAuthUserDao extends BaseDao<OsmMessageAuthUser, OsmMessageAuthUserExample, Integer> {

    /**
     * 批量插入关联用户
     *
     * @param msgId
     * @param receiverIds
     */
    void batchInsert(@Param("msgId") String msgId, @Param("receiverIds") Collection<Integer> receiverIds);

    /**
     * 批量修改状态为 未读/未处理 的记录为 已读/已处理
     * @param userId
     * @return
     */
    int updateToReadStatus(@Param("userId") Integer userId);
}