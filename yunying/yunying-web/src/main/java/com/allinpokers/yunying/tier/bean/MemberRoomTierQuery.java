package com.allinpokers.yunying.tier.bean;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MemberRoomTierQuery
 *
 * <AUTHOR>
 * @since 2025/6/4
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MemberRoomTierQuery {

    private Integer tierId;

    private Integer userId;

    private Integer tribeId;

    private Integer clubId;

    private String userRandomNums;

    private String userIds;

    private Integer tpaTierId;

}
