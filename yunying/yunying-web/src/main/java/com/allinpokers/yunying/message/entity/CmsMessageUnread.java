package com.allinpokers.yunying.message.entity;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * CMS未读消息  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CmsMessageUnread {
    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private Integer authUserId;

    /**
     * 统计
     */
    @ApiModelProperty("统计")
    private Integer count;

    /**
     * 最后一次消息ID
     */
    @ApiModelProperty("最后一次消息ID")
    private String lastMsgId;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updatedTime;
}