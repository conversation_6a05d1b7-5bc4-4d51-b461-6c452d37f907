package com.allinpokers.yunying.model.request.message;

import com.allinpokers.yunying.model.request.PageReq;
import com.allinpokers.yunying.permission.security.UserInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "消息管理列表请求参数")
@Data
public class MessageListReq extends PageReq {

    /**
     * 操作的用户
     */
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private UserInfo operatorUser;
}
