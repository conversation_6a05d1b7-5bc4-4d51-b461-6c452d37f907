package com.allinpokers.yunying.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 运营web段需要用到的配置
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "yunying.web")
@Data
@Slf4j
public class YunyingWebConfig {

    /**
     * 支付渠道接口的基础url
     */
    private String payChannelBaseUrl;
    /**
     * 支付渠道用来给Jwt签名使用的密钥
     */
    private String payChannelJwtSecret;
    /**
     * 支付渠道的Jwt过期分钟，默认3分钟
     */
    private Integer payChannelJwtExpireMinutes = 3;

    /**
     * 高德地图的key
     */
    private String amapKey;

    /**
     * ip离线数据库的文件地址
     */
    private String ipdbPath;
}
