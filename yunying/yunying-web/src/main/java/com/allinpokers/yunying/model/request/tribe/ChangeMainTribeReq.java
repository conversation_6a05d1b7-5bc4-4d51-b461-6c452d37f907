package com.allinpokers.yunying.model.request.tribe;


import com.allinpokers.yunying.permission.security.UserInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@ApiModel("联盟组转移")
@Accessors(chain = true)
public class ChangeMainTribeReq {
    @ApiModelProperty(value = "等待转移的联盟id ", required = true)
    private List<Integer> subTribeIdList;
    @ApiModelProperty("主联盟id")
    private Integer mainTribeId;
    /**
     * 操作的用户
     */
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private UserInfo operatorUser;
}
