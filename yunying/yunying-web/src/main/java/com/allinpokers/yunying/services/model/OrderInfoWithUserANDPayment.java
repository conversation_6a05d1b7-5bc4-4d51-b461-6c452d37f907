package com.allinpokers.yunying.services.model;

import com.allinpokers.yunying.payment.dao.model.Orders;
import com.allinpokers.yunying.payment.dao.model.OrdersWithBLOBs;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Getter
@Setter
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderInfoWithUserANDPayment {
     /**
     * id
     */
    @ApiModelProperty("id")
    private Integer id;

    /**
     * 0 = pending , 1 = finished , 2=error , 3= cancelled , 4=error
     */
    @ApiModelProperty("0 = pending , 1 = finished , 2=error , 3= cancelled , 4=error")
    private Integer status;

    /**
     * createdAt
     */
    @ApiModelProperty("createdAt")
    private LocalDateTime createdAt;

    /**
     * updatedAt
     */
    @ApiModelProperty("updatedAt")
    private LocalDateTime updatedAt;

    /**
     * clubId
     */
    @ApiModelProperty("clubId")
    private Integer clubId;

    /**
     * tribeId
     */
    @ApiModelProperty("tribeId")
    private Integer tribeId;

    /**
     * paymentId
     */
    @ApiModelProperty("paymentId")
    private String paymentId;

    /**
     * lockedBy
     */
    @ApiModelProperty("lockedBy")
    private Integer lockedBy;

    /**
     * locked
     */
    @ApiModelProperty("locked")
    private Integer locked;

    /**
     * buy / sell
     */
    @ApiModelProperty("buy / sell")
    private String type;

    /**
     * buy / sell
     */
    @ApiModelProperty("buy / sell")
    private String action;

    /**
     * gatewaysId
     */
    @ApiModelProperty("gatewaysId")
    private String gatewaysId;

    /**
     * methodsId
     */
    @ApiModelProperty("methodsId")
    private String methodsId;

    /**
     * amount
     */
    @ApiModelProperty("amount")
    private BigDecimal amount;

    /**
     * discount
     */
    @ApiModelProperty("discount")
    private BigDecimal discount;

    /**
     * source
     */
    @ApiModelProperty("source")
    private String source;

    /**
     * orderId
     */
    @ApiModelProperty("orderId")
    private String orderId;

    /**
     * createJson
     */
    @ApiModelProperty("createJson")
    private String createJson;

    /**
     * callbackJson
     */
    @ApiModelProperty("callbackJson")
    private String callbackJson;

    /**
     * orderJson
     */
    @ApiModelProperty("orderJson")
    private String orderJson;

    /**
     * userId
     */
    @ApiModelProperty("userId")
    private String userId;

    /**
     * userRandomId
     */
    @ApiModelProperty("userRandomId")
    private String userRandomId;

    /**
     * gatewaysOrderId
     */
    @ApiModelProperty("gatewaysOrderId")
    private String gatewaysOrderId;

    @ApiModelProperty("玩家RandomId")
    private String randomId;

    @ApiModelProperty("玩家名稱")
    private String userName;

    @ApiModelProperty("reissue Id")
    private Integer reissueId;

    @ApiModelProperty("reissue名稱")
    private String reissueName;

    @ApiModelProperty("paymentNameJson")
    private String paymentNameJson;
}
