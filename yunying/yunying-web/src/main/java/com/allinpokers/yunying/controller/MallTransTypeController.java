package com.allinpokers.yunying.controller;

import com.allinpokers.yunying.model.request.mallpaytranstype.*;
import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.services.MallTransTypeService;
import com.allinpokers.yunying.services.model.MallTransType;
import com.allinpokers.yunying.services.model.MallTransTypeSDKInfo;
import com.allinpokers.yunying.services.model.PaySdkInfo;
import com.allinpokers.yunying.util.TimeUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
@Api(value = "/mall/transtype", description = "商城支付通道配置", tags = {"商城支付通道模块"})
@RestController
@RequestMapping("/mall/transtype/")
@Slf4j
public class MallTransTypeController extends BaseController {

    @Autowired
    private MallTransTypeService mallTransTypeService;

    @ApiOperation(value = "商城支付通道配置列表", produces = "application/json", httpMethod = "POST")
    @PostMapping("list")
    public CommonRespon<List<MallTransType>> queryPayChannelList() {
        return mallTransTypeService.queryMallTransTypeList();
    }

    @ApiOperation(value = "支付SDK列表", produces = "application/json", httpMethod = "POST")
    @PostMapping("pay/list")
    public CommonRespon<List<PaySdkInfo>> queryPaySdkChannelList() {
        return mallTransTypeService.queryPaySdkChannelList();
    }

    @ApiOperation(value = "根据支付通道id查询内容", produces = "application/json", httpMethod = "POST")
    @PostMapping("query")
    public CommonRespon<MallTransTypeSDKInfo> queryById(@RequestBody(required = false) QueryReq req) {
        return mallTransTypeService.queryById(req.getId());
    }

    @ApiOperation(value = "优先推荐更新", produces = "application/json", httpMethod = "POST")
    @PostMapping("priority/update")
    public CommonRespon priorityUpdate(@RequestBody(required = false) PriorityUpdateReq req) {
        return mallTransTypeService.priorityUpdate(req.getTransType(), req.getIsFirst());
    }

    @ApiOperation(value = "是否使用更新", produces = "application/json", httpMethod = "POST")
    @PostMapping("use/update")
    public CommonRespon useUpdate(@RequestBody(required = false) UseUpdateReq req) {
        return mallTransTypeService.useUpdate(req.getId(), req.getIsUsed());
    }

    @ApiOperation(value = "sdk是否可以用", notes = "针对sdk 的状态控制", produces = "application/json", httpMethod = "POST")
    @PostMapping("sdkUse/update")
    public CommonRespon sdkUseUpdate(@RequestBody(required = false) SdkUseUpdateReq req) {
        return mallTransTypeService.sdkUseUpdate(req);
    }


    @ApiOperation(value = "更新支付通道名称、商品、手续费、时间配置,每日限额，每月限额，弹窗开关", produces = "application/json", httpMethod = "POST")
    @PostMapping("products/update")
    public CommonRespon productsUpdate(@Valid @RequestBody ProductsUpdateReq req) {
        return mallTransTypeService.productsUpdate(req);
    }
}
