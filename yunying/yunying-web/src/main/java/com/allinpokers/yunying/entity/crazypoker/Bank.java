package com.allinpokers.yunying.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Bank  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Bank {
    /**
     * 银行代号
     */
    @ApiModelProperty("银行代号")
    private String bankCode;

    /**
     * 银行名称
     */
    @ApiModelProperty("银行名称")
    private String bankName;
}