package com.allinpokers.yunying.dao.crazypoker;

import com.allinpokers.yunying.dao.BaseDao;
import com.allinpokers.yunying.entity.crazypoker.PromotionUserFeedbackRecord;
import com.allinpokers.yunying.entity.crazypoker.example.PromotionUserFeedbackRecordExample;
import com.allinpokers.yunying.entity.plus.promotion.ClubPromotionBackPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户个人返豆记录表  Mapper
 * 
 * <AUTHOR>
 */
@Mapper
public interface PromotionUserFeedbackRecordDao extends BaseDao<PromotionUserFeedbackRecord, PromotionUserFeedbackRecordExample, Integer> {

    @Select("SELECT IFNULL(SUM(beans),0) from  promotion_user_feedback_record where club_id=#{clubId} and grant_time BETWEEN #{todayStart} and #{todayEnd} and status='1'")
    Integer selectClubTodayChips(@Param("clubId") Integer clubId,@Param("todayStart") LocalDateTime todayStart,@Param("todayEnd") LocalDateTime todayEnd);


    List<ClubPromotionBackPo> selectClubChipsByClubIdListAndTime(@Param("clubIdList") List<Integer> clubIdList, @Param("start") LocalDateTime start,@Param("end") LocalDateTime end);
}