package com.allinpokers.yunying.entity.crazypoker.example;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class PayChannelExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table pay_channel
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table pay_channel
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table pay_channel
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pay_channel
     *
     * @mbg.generated
     */
    public PayChannelExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pay_channel
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pay_channel
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pay_channel
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pay_channel
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pay_channel
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pay_channel
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pay_channel
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pay_channel
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pay_channel
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pay_channel
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table pay_channel
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCmsUidIsNull() {
            addCriterion("cms_uid is null");
            return (Criteria) this;
        }

        public Criteria andCmsUidIsNotNull() {
            addCriterion("cms_uid is not null");
            return (Criteria) this;
        }

        public Criteria andCmsUidEqualTo(Integer value) {
            addCriterion("cms_uid =", value, "cmsUid");
            return (Criteria) this;
        }

        public Criteria andCmsUidNotEqualTo(Integer value) {
            addCriterion("cms_uid <>", value, "cmsUid");
            return (Criteria) this;
        }

        public Criteria andCmsUidGreaterThan(Integer value) {
            addCriterion("cms_uid >", value, "cmsUid");
            return (Criteria) this;
        }

        public Criteria andCmsUidGreaterThanOrEqualTo(Integer value) {
            addCriterion("cms_uid >=", value, "cmsUid");
            return (Criteria) this;
        }

        public Criteria andCmsUidLessThan(Integer value) {
            addCriterion("cms_uid <", value, "cmsUid");
            return (Criteria) this;
        }

        public Criteria andCmsUidLessThanOrEqualTo(Integer value) {
            addCriterion("cms_uid <=", value, "cmsUid");
            return (Criteria) this;
        }

        public Criteria andCmsUidIn(List<Integer> values) {
            addCriterion("cms_uid in", values, "cmsUid");
            return (Criteria) this;
        }

        public Criteria andCmsUidNotIn(List<Integer> values) {
            addCriterion("cms_uid not in", values, "cmsUid");
            return (Criteria) this;
        }

        public Criteria andCmsUidBetween(Integer value1, Integer value2) {
            addCriterion("cms_uid between", value1, value2, "cmsUid");
            return (Criteria) this;
        }

        public Criteria andCmsUidNotBetween(Integer value1, Integer value2) {
            addCriterion("cms_uid not between", value1, value2, "cmsUid");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andOpenRebateIsNull() {
            addCriterion("open_rebate is null");
            return (Criteria) this;
        }

        public Criteria andOpenRebateIsNotNull() {
            addCriterion("open_rebate is not null");
            return (Criteria) this;
        }

        public Criteria andOpenRebateEqualTo(Boolean value) {
            addCriterion("open_rebate =", value, "openRebate");
            return (Criteria) this;
        }

        public Criteria andOpenRebateNotEqualTo(Boolean value) {
            addCriterion("open_rebate <>", value, "openRebate");
            return (Criteria) this;
        }

        public Criteria andOpenRebateGreaterThan(Boolean value) {
            addCriterion("open_rebate >", value, "openRebate");
            return (Criteria) this;
        }

        public Criteria andOpenRebateGreaterThanOrEqualTo(Boolean value) {
            addCriterion("open_rebate >=", value, "openRebate");
            return (Criteria) this;
        }

        public Criteria andOpenRebateLessThan(Boolean value) {
            addCriterion("open_rebate <", value, "openRebate");
            return (Criteria) this;
        }

        public Criteria andOpenRebateLessThanOrEqualTo(Boolean value) {
            addCriterion("open_rebate <=", value, "openRebate");
            return (Criteria) this;
        }

        public Criteria andOpenRebateIn(List<Boolean> values) {
            addCriterion("open_rebate in", values, "openRebate");
            return (Criteria) this;
        }

        public Criteria andOpenRebateNotIn(List<Boolean> values) {
            addCriterion("open_rebate not in", values, "openRebate");
            return (Criteria) this;
        }

        public Criteria andOpenRebateBetween(Boolean value1, Boolean value2) {
            addCriterion("open_rebate between", value1, value2, "openRebate");
            return (Criteria) this;
        }

        public Criteria andOpenRebateNotBetween(Boolean value1, Boolean value2) {
            addCriterion("open_rebate not between", value1, value2, "openRebate");
            return (Criteria) this;
        }

        public Criteria andRebateRatioIsNull() {
            addCriterion("rebate_ratio is null");
            return (Criteria) this;
        }

        public Criteria andRebateRatioIsNotNull() {
            addCriterion("rebate_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andRebateRatioEqualTo(Double value) {
            addCriterion("rebate_ratio =", value, "rebateRatio");
            return (Criteria) this;
        }

        public Criteria andRebateRatioNotEqualTo(Double value) {
            addCriterion("rebate_ratio <>", value, "rebateRatio");
            return (Criteria) this;
        }

        public Criteria andRebateRatioGreaterThan(Double value) {
            addCriterion("rebate_ratio >", value, "rebateRatio");
            return (Criteria) this;
        }

        public Criteria andRebateRatioGreaterThanOrEqualTo(Double value) {
            addCriterion("rebate_ratio >=", value, "rebateRatio");
            return (Criteria) this;
        }

        public Criteria andRebateRatioLessThan(Double value) {
            addCriterion("rebate_ratio <", value, "rebateRatio");
            return (Criteria) this;
        }

        public Criteria andRebateRatioLessThanOrEqualTo(Double value) {
            addCriterion("rebate_ratio <=", value, "rebateRatio");
            return (Criteria) this;
        }

        public Criteria andRebateRatioIn(List<Double> values) {
            addCriterion("rebate_ratio in", values, "rebateRatio");
            return (Criteria) this;
        }

        public Criteria andRebateRatioNotIn(List<Double> values) {
            addCriterion("rebate_ratio not in", values, "rebateRatio");
            return (Criteria) this;
        }

        public Criteria andRebateRatioBetween(Double value1, Double value2) {
            addCriterion("rebate_ratio between", value1, value2, "rebateRatio");
            return (Criteria) this;
        }

        public Criteria andRebateRatioNotBetween(Double value1, Double value2) {
            addCriterion("rebate_ratio not between", value1, value2, "rebateRatio");
            return (Criteria) this;
        }

        public Criteria andRebateSumIsNull() {
            addCriterion("rebate_sum is null");
            return (Criteria) this;
        }

        public Criteria andRebateSumIsNotNull() {
            addCriterion("rebate_sum is not null");
            return (Criteria) this;
        }

        public Criteria andRebateSumEqualTo(Long value) {
            addCriterion("rebate_sum =", value, "rebateSum");
            return (Criteria) this;
        }

        public Criteria andRebateSumNotEqualTo(Long value) {
            addCriterion("rebate_sum <>", value, "rebateSum");
            return (Criteria) this;
        }

        public Criteria andRebateSumGreaterThan(Long value) {
            addCriterion("rebate_sum >", value, "rebateSum");
            return (Criteria) this;
        }

        public Criteria andRebateSumGreaterThanOrEqualTo(Long value) {
            addCriterion("rebate_sum >=", value, "rebateSum");
            return (Criteria) this;
        }

        public Criteria andRebateSumLessThan(Long value) {
            addCriterion("rebate_sum <", value, "rebateSum");
            return (Criteria) this;
        }

        public Criteria andRebateSumLessThanOrEqualTo(Long value) {
            addCriterion("rebate_sum <=", value, "rebateSum");
            return (Criteria) this;
        }

        public Criteria andRebateSumIn(List<Long> values) {
            addCriterion("rebate_sum in", values, "rebateSum");
            return (Criteria) this;
        }

        public Criteria andRebateSumNotIn(List<Long> values) {
            addCriterion("rebate_sum not in", values, "rebateSum");
            return (Criteria) this;
        }

        public Criteria andRebateSumBetween(Long value1, Long value2) {
            addCriterion("rebate_sum between", value1, value2, "rebateSum");
            return (Criteria) this;
        }

        public Criteria andRebateSumNotBetween(Long value1, Long value2) {
            addCriterion("rebate_sum not between", value1, value2, "rebateSum");
            return (Criteria) this;
        }

        public Criteria andClubCountIsNull() {
            addCriterion("club_count is null");
            return (Criteria) this;
        }

        public Criteria andClubCountIsNotNull() {
            addCriterion("club_count is not null");
            return (Criteria) this;
        }

        public Criteria andClubCountEqualTo(Integer value) {
            addCriterion("club_count =", value, "clubCount");
            return (Criteria) this;
        }

        public Criteria andClubCountNotEqualTo(Integer value) {
            addCriterion("club_count <>", value, "clubCount");
            return (Criteria) this;
        }

        public Criteria andClubCountGreaterThan(Integer value) {
            addCriterion("club_count >", value, "clubCount");
            return (Criteria) this;
        }

        public Criteria andClubCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("club_count >=", value, "clubCount");
            return (Criteria) this;
        }

        public Criteria andClubCountLessThan(Integer value) {
            addCriterion("club_count <", value, "clubCount");
            return (Criteria) this;
        }

        public Criteria andClubCountLessThanOrEqualTo(Integer value) {
            addCriterion("club_count <=", value, "clubCount");
            return (Criteria) this;
        }

        public Criteria andClubCountIn(List<Integer> values) {
            addCriterion("club_count in", values, "clubCount");
            return (Criteria) this;
        }

        public Criteria andClubCountNotIn(List<Integer> values) {
            addCriterion("club_count not in", values, "clubCount");
            return (Criteria) this;
        }

        public Criteria andClubCountBetween(Integer value1, Integer value2) {
            addCriterion("club_count between", value1, value2, "clubCount");
            return (Criteria) this;
        }

        public Criteria andClubCountNotBetween(Integer value1, Integer value2) {
            addCriterion("club_count not between", value1, value2, "clubCount");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeIsNull() {
            addCriterion("payment_code is null");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeIsNotNull() {
            addCriterion("payment_code is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeEqualTo(String value) {
            addCriterion("payment_code =", value, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeNotEqualTo(String value) {
            addCriterion("payment_code <>", value, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeGreaterThan(String value) {
            addCriterion("payment_code >", value, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeGreaterThanOrEqualTo(String value) {
            addCriterion("payment_code >=", value, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeLessThan(String value) {
            addCriterion("payment_code <", value, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeLessThanOrEqualTo(String value) {
            addCriterion("payment_code <=", value, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeLike(String value) {
            addCriterion("payment_code like", value, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeNotLike(String value) {
            addCriterion("payment_code not like", value, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeIn(List<String> values) {
            addCriterion("payment_code in", values, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeNotIn(List<String> values) {
            addCriterion("payment_code not in", values, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeBetween(String value1, String value2) {
            addCriterion("payment_code between", value1, value2, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andPaymentCodeNotBetween(String value1, String value2) {
            addCriterion("payment_code not between", value1, value2, "paymentCode");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerIdIsNull() {
            addCriterion("allin_partner_id is null");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerIdIsNotNull() {
            addCriterion("allin_partner_id is not null");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerIdEqualTo(Integer value) {
            addCriterion("allin_partner_id =", value, "allinPartnerId");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerIdNotEqualTo(Integer value) {
            addCriterion("allin_partner_id <>", value, "allinPartnerId");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerIdGreaterThan(Integer value) {
            addCriterion("allin_partner_id >", value, "allinPartnerId");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("allin_partner_id >=", value, "allinPartnerId");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerIdLessThan(Integer value) {
            addCriterion("allin_partner_id <", value, "allinPartnerId");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerIdLessThanOrEqualTo(Integer value) {
            addCriterion("allin_partner_id <=", value, "allinPartnerId");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerIdIn(List<Integer> values) {
            addCriterion("allin_partner_id in", values, "allinPartnerId");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerIdNotIn(List<Integer> values) {
            addCriterion("allin_partner_id not in", values, "allinPartnerId");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerIdBetween(Integer value1, Integer value2) {
            addCriterion("allin_partner_id between", value1, value2, "allinPartnerId");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("allin_partner_id not between", value1, value2, "allinPartnerId");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerKeyIsNull() {
            addCriterion("allin_partner_key is null");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerKeyIsNotNull() {
            addCriterion("allin_partner_key is not null");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerKeyEqualTo(String value) {
            addCriterion("allin_partner_key =", value, "allinPartnerKey");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerKeyNotEqualTo(String value) {
            addCriterion("allin_partner_key <>", value, "allinPartnerKey");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerKeyGreaterThan(String value) {
            addCriterion("allin_partner_key >", value, "allinPartnerKey");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerKeyGreaterThanOrEqualTo(String value) {
            addCriterion("allin_partner_key >=", value, "allinPartnerKey");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerKeyLessThan(String value) {
            addCriterion("allin_partner_key <", value, "allinPartnerKey");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerKeyLessThanOrEqualTo(String value) {
            addCriterion("allin_partner_key <=", value, "allinPartnerKey");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerKeyLike(String value) {
            addCriterion("allin_partner_key like", value, "allinPartnerKey");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerKeyNotLike(String value) {
            addCriterion("allin_partner_key not like", value, "allinPartnerKey");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerKeyIn(List<String> values) {
            addCriterion("allin_partner_key in", values, "allinPartnerKey");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerKeyNotIn(List<String> values) {
            addCriterion("allin_partner_key not in", values, "allinPartnerKey");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerKeyBetween(String value1, String value2) {
            addCriterion("allin_partner_key between", value1, value2, "allinPartnerKey");
            return (Criteria) this;
        }

        public Criteria andAllinPartnerKeyNotBetween(String value1, String value2) {
            addCriterion("allin_partner_key not between", value1, value2, "allinPartnerKey");
            return (Criteria) this;
        }

        public Criteria andContactTypeIsNull() {
            addCriterion("contact_type is null");
            return (Criteria) this;
        }

        public Criteria andContactTypeIsNotNull() {
            addCriterion("contact_type is not null");
            return (Criteria) this;
        }

        public Criteria andContactTypeEqualTo(Integer value) {
            addCriterion("contact_type =", value, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeNotEqualTo(Integer value) {
            addCriterion("contact_type <>", value, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeGreaterThan(Integer value) {
            addCriterion("contact_type >", value, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("contact_type >=", value, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeLessThan(Integer value) {
            addCriterion("contact_type <", value, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeLessThanOrEqualTo(Integer value) {
            addCriterion("contact_type <=", value, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeIn(List<Integer> values) {
            addCriterion("contact_type in", values, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeNotIn(List<Integer> values) {
            addCriterion("contact_type not in", values, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeBetween(Integer value1, Integer value2) {
            addCriterion("contact_type between", value1, value2, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("contact_type not between", value1, value2, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactAccountIsNull() {
            addCriterion("contact_account is null");
            return (Criteria) this;
        }

        public Criteria andContactAccountIsNotNull() {
            addCriterion("contact_account is not null");
            return (Criteria) this;
        }

        public Criteria andContactAccountEqualTo(String value) {
            addCriterion("contact_account =", value, "contactAccount");
            return (Criteria) this;
        }

        public Criteria andContactAccountNotEqualTo(String value) {
            addCriterion("contact_account <>", value, "contactAccount");
            return (Criteria) this;
        }

        public Criteria andContactAccountGreaterThan(String value) {
            addCriterion("contact_account >", value, "contactAccount");
            return (Criteria) this;
        }

        public Criteria andContactAccountGreaterThanOrEqualTo(String value) {
            addCriterion("contact_account >=", value, "contactAccount");
            return (Criteria) this;
        }

        public Criteria andContactAccountLessThan(String value) {
            addCriterion("contact_account <", value, "contactAccount");
            return (Criteria) this;
        }

        public Criteria andContactAccountLessThanOrEqualTo(String value) {
            addCriterion("contact_account <=", value, "contactAccount");
            return (Criteria) this;
        }

        public Criteria andContactAccountLike(String value) {
            addCriterion("contact_account like", value, "contactAccount");
            return (Criteria) this;
        }

        public Criteria andContactAccountNotLike(String value) {
            addCriterion("contact_account not like", value, "contactAccount");
            return (Criteria) this;
        }

        public Criteria andContactAccountIn(List<String> values) {
            addCriterion("contact_account in", values, "contactAccount");
            return (Criteria) this;
        }

        public Criteria andContactAccountNotIn(List<String> values) {
            addCriterion("contact_account not in", values, "contactAccount");
            return (Criteria) this;
        }

        public Criteria andContactAccountBetween(String value1, String value2) {
            addCriterion("contact_account between", value1, value2, "contactAccount");
            return (Criteria) this;
        }

        public Criteria andContactAccountNotBetween(String value1, String value2) {
            addCriterion("contact_account not between", value1, value2, "contactAccount");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreatorIdIsNull() {
            addCriterion("creator_id is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIdIsNotNull() {
            addCriterion("creator_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorIdEqualTo(Integer value) {
            addCriterion("creator_id =", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdNotEqualTo(Integer value) {
            addCriterion("creator_id <>", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdGreaterThan(Integer value) {
            addCriterion("creator_id >", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("creator_id >=", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdLessThan(Integer value) {
            addCriterion("creator_id <", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdLessThanOrEqualTo(Integer value) {
            addCriterion("creator_id <=", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdIn(List<Integer> values) {
            addCriterion("creator_id in", values, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdNotIn(List<Integer> values) {
            addCriterion("creator_id not in", values, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdBetween(Integer value1, Integer value2) {
            addCriterion("creator_id between", value1, value2, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdNotBetween(Integer value1, Integer value2) {
            addCriterion("creator_id not between", value1, value2, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNull() {
            addCriterion("created_time is null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNotNull() {
            addCriterion("created_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeEqualTo(LocalDateTime value) {
            addCriterion("created_time =", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotEqualTo(LocalDateTime value) {
            addCriterion("created_time <>", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThan(LocalDateTime value) {
            addCriterion("created_time >", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("created_time >=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThan(LocalDateTime value) {
            addCriterion("created_time <", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("created_time <=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIn(List<LocalDateTime> values) {
            addCriterion("created_time in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotIn(List<LocalDateTime> values) {
            addCriterion("created_time not in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("created_time between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("created_time not between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdIsNull() {
            addCriterion("updater_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdIsNotNull() {
            addCriterion("updater_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdEqualTo(Integer value) {
            addCriterion("updater_id =", value, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdNotEqualTo(Integer value) {
            addCriterion("updater_id <>", value, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdGreaterThan(Integer value) {
            addCriterion("updater_id >", value, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("updater_id >=", value, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdLessThan(Integer value) {
            addCriterion("updater_id <", value, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdLessThanOrEqualTo(Integer value) {
            addCriterion("updater_id <=", value, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdIn(List<Integer> values) {
            addCriterion("updater_id in", values, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdNotIn(List<Integer> values) {
            addCriterion("updater_id not in", values, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdBetween(Integer value1, Integer value2) {
            addCriterion("updater_id between", value1, value2, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdaterIdNotBetween(Integer value1, Integer value2) {
            addCriterion("updater_id not between", value1, value2, "updaterId");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNull() {
            addCriterion("updated_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNotNull() {
            addCriterion("updated_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeEqualTo(LocalDateTime value) {
            addCriterion("updated_time =", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotEqualTo(LocalDateTime value) {
            addCriterion("updated_time <>", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThan(LocalDateTime value) {
            addCriterion("updated_time >", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("updated_time >=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThan(LocalDateTime value) {
            addCriterion("updated_time <", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("updated_time <=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIn(List<LocalDateTime> values) {
            addCriterion("updated_time in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotIn(List<LocalDateTime> values) {
            addCriterion("updated_time not in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("updated_time between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("updated_time not between", value1, value2, "updatedTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table pay_channel
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table pay_channel
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}