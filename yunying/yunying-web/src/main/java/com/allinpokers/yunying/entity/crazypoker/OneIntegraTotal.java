package com.allinpokers.yunying.entity.crazypoker;

import com.allinpokers.yunying.model.response.PageBean;
import com.allinpokers.yunying.model.response.club.ClubInfoListBean;
import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
@Getter
@Setter
@ApiModel(description = "玩家详情列表带分页信息")
public class OneIntegraTotal extends PageBean<OneIntegra> {
    private int integraTotal;
    private int feeTotal;
    private int insuranceExpenditureTotal;
    private int insuranceProfitOneTotal;
    private int insuranceProfitTowTotal;
}
