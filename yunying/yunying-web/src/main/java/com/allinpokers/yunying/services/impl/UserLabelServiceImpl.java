package com.allinpokers.yunying.services.impl;

import com.allinpokers.yunying.dao.crazypoker.UserLabelConfigDao;
import com.allinpokers.yunying.dao.crazypoker.UserLabelDao;
import com.allinpokers.yunying.entity.crazypoker.UserLabel;
import com.allinpokers.yunying.entity.crazypoker.UserLabelConfig;
import com.allinpokers.yunying.entity.crazypoker.example.UserLabelConfigExample;
import com.allinpokers.yunying.entity.crazypoker.example.UserLabelExample;
import com.allinpokers.yunying.enu.ResponseCodeEnum;
import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.services.UserLabelService;
import com.allinpokers.yunying.services.model.UserLabelInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class UserLabelServiceImpl implements UserLabelService {
	@Resource
	private UserLabelDao userLabelDao;
	@Resource
	private UserLabelConfigDao userLabelConfigDao;
	private String notAllowName = "无标签";

	@Override
	public List<UserLabelConfig> listConfigs() {
		return userLabelConfigDao.selectByExample(new UserLabelConfigExample());
	}

	@Override
	public CommonRespon addConfig(String name) {
		CommonRespon<Object> failure = CommonRespon.failure(ResponseCodeEnum.PARAM_VALID_FAILED, "重复的标签名称，请重新输入");
		if (notAllowName.equals(name)) {
			return failure;
		}
		int changeRow = userLabelConfigDao.insertIgnore(name);
		return changeRow > 0 ? CommonRespon.success() : failure;
	}

	@Override
	public CommonRespon updateConfig(Integer configId, String name) {
		CommonRespon<Object> failure = CommonRespon.failure(ResponseCodeEnum.PARAM_VALID_FAILED, "重复的标签名称，请重新输入");
		if (notAllowName.equals(name)) {
			return failure;
		}
		UserLabelConfigExample example = new UserLabelConfigExample();
		example.or().andNameEqualTo(name);
		List<UserLabelConfig> configs = userLabelConfigDao.selectByExample(example);
		boolean hasOtherLabel = configs.stream().map(UserLabelConfig::getId).anyMatch(id -> !id.equals(configId));
		if (hasOtherLabel) {
			return failure;
		}

		UserLabelConfig config = UserLabelConfig.builder()
				.id(configId)
				.name(name)
				.updatedTime(LocalDateTime.now())
				.build();
		userLabelConfigDao.updateByPrimaryKeySelective(config);
		return CommonRespon.success();
	}

	@Transactional(transactionManager = "crazyPokerTransactionManager", rollbackFor = RuntimeException.class)
	@Override
	public void deleteConfig(List<Integer> idList) {
		if (idList == null || idList.isEmpty()) {
			return;
		}
		UserLabelExample userLabelExample = new UserLabelExample();
		userLabelExample.or().andUserLabelConfigIdIn(idList);
		userLabelDao.deleteByExample(userLabelExample);

		UserLabelConfigExample example = new UserLabelConfigExample();
		example.or().andIdIn(idList);
		userLabelConfigDao.deleteByExample(example);
	}

	@Override
	public Map<Integer, UserLabelConfig> findUserLabelMap(List<Integer> userIds) {
		if (userIds == null || userIds.isEmpty()) {
			return new HashMap<>(0);
		}
		List<UserLabelInfo> userLabelInfos = userLabelDao.findUserLabelInfo(userIds);
		return userLabelInfos.stream().collect(Collectors.toMap(UserLabelInfo::getUserId, UserLabelInfo::getConfig, (s, s2) -> s2));
	}

	@Transactional(transactionManager = "crazyPokerTransactionManager", rollbackFor = RuntimeException.class)
	@Override
	public void addLabel(Integer userId, Integer labelConfigId) {
		deleteLabel(userId, labelConfigId);
		UserLabel userLabel = UserLabel.builder()
				.userId(userId)
				.userLabelConfigId(labelConfigId)
				.build();
		userLabelDao.insert(userLabel);
	}

	@Override
	public void deleteLabel(Integer userId, Integer labelConfigId) {
		//labelConfigId暂时没有使用到, 如果一个用户可以使用多个标签, 则可以使用到
		UserLabelExample userLabelExample = new UserLabelExample();
		userLabelExample.or().andUserIdEqualTo(userId);
		userLabelDao.deleteByExample(userLabelExample);
	}
}
