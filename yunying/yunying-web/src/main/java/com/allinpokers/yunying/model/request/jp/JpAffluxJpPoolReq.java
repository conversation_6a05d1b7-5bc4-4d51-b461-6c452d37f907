package com.allinpokers.yunying.model.request.jp;

import com.allinpokers.yunying.permission.security.UserInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
@ApiModel(description = "注入彩池金豆")
@Data
public class JpAffluxJpPoolReq {


    /**
     * 操作的用户
     */
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private UserInfo operatorUser;
    @ApiModelProperty(value = "注入的金豆")
    private Integer chip;
    @ApiModelProperty(value = "彩池id")
    private Integer poolId;
}
