package com.allinpokers.yunying.services;

import com.allinpokers.yunying.model.request.manualrecharge.ManualRechargeOpReq;
import com.allinpokers.yunying.model.request.manualrecharge.ManualRechargeReq;
import com.allinpokers.yunying.model.request.platformaccount.MarketAccountSearchReq;
import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.model.response.PageBean;
import com.allinpokers.yunying.model.response.manualrecharge.ManualRechargeUpdateLogResp;

/**
 * <AUTHOR>
 */
public interface ManualRechargeService {

    /**
     * 手工充值仓余额
     *
     * @return
     */
    CommonRespon<Long> balance();

    /**
     * 手工充值仓金豆修改
     * @param req
     * @return
     */
    CommonRespon manualRechargeOp(ManualRechargeOpReq req);

    /**
     * 手工充值仓修改记录
     * @param req
     * @return
     */
    PageBean<ManualRechargeUpdateLogResp> listLogs(ManualRechargeReq req);
}
