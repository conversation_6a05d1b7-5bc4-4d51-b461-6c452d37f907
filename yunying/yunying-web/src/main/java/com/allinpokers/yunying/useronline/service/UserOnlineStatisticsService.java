package com.allinpokers.yunying.useronline.service;

import com.allinpokers.yunying.useronline.bean.UserOnlineExpand;
import com.allinpokers.yunying.useronline.bean.UserOnlineParams;
import com.allinpokers.yunying.useronline.bean.UserOnlinePage;
import com.allinpokers.yunying.useronline.entity.UserOnlineStatistics;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface UserOnlineStatisticsService {

    /**
     * 获取在线玩家
     *
     * @param params
     * @return
     */
    UserOnlinePage getOnlineList(UserOnlineParams params);

    UserOnlinePage getOnlineListWithHeartbeat(UserOnlineParams params);

    /**
     * 在线玩家 - 展开
     *
     * @param userId
     * @return
     */
    UserOnlineExpand expandUser(Integer userId);

    /**
     * 查找用户对应的统计
     *
     * @param userIds
     * @return
     */
    Map<Integer, UserOnlineStatistics> findUserStatisticsMap(Collection<Integer> userIds);

}
