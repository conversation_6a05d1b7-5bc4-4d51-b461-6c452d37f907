package com.allinpokers.yunying.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * CentralAccount  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CentralAccount {
    /**
     * id
     */
    @ApiModelProperty("id")
    private Integer id;

    /**
     * 金豆数
     */
    @ApiModelProperty("金豆数")
    private Long chip;

    /**
     * createdTime
     */
    @ApiModelProperty("createdTime")
    private LocalDateTime createdTime;

    /**
     * updatedTime
     */
    @ApiModelProperty("updatedTime")
    private LocalDateTime updatedTime;
}