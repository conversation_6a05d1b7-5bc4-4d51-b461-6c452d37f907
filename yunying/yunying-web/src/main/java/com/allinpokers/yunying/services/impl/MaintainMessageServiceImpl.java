package com.allinpokers.yunying.services.impl;

import com.allinpokers.yunying.dao.crazypoker.MaintainMessageDao;
import com.allinpokers.yunying.dao.crazypoker.RoomCreationPlanConfigDao;
import com.allinpokers.yunying.model.response.PageBean;
import com.allinpokers.yunying.model.response.maintain.MaintainMessageResp;
import com.allinpokers.yunying.services.MaintainMessageService;
import com.allinpokers.yunying.services.model.MaintainMessage;
import com.allinpokers.yunying.util.PageUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 用户收款信息 提豆管理
 *
 * <AUTHOR>
 */
@Service
public class MaintainMessageServiceImpl implements MaintainMessageService {

    @Autowired
    private MaintainMessageDao maintainMessageDao;

    @Autowired
    private RoomCreationPlanConfigDao roomCreationPlanConfigDao;

    @Override
    public PageBean<MaintainMessageResp> configList(Integer page, Integer size) {
        List<MaintainMessage> maintainMessageList = new ArrayList<>();
        size = PageUtil.calculateSize(size);
        page = PageUtil.calculatePage(page);
        int start = page * size;
        maintainMessageList = maintainMessageDao.queryMaintainPage(start, size);
        int total = maintainMessageDao.countMaintainList();
        List<MaintainMessageResp> respList = new ArrayList<>();
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (maintainMessageList != null && maintainMessageList.size() > 0) {
            maintainMessageList.forEach(mes -> {
                MaintainMessageResp resp = new MaintainMessageResp();
                resp.setId(mes.getId());
                resp.setStatus(mes.getStatus());
                resp.setExampleContent(mes.getExampleContent());
                resp.setStartTime(mes.getStartTime());
                resp.setEndTime(mes.getEndTime());
                resp.setStartTimeStr(df.format(mes.getStartTime()));
                resp.setEndTimeStr(df.format(mes.getEndTime()));
                if (mes.getStartTime().before(new Date())) {
                    if (mes.getEndTime().after(new Date())) {
                        resp.setStatusValue(1);
                    } else {
                        return;
                    }
                } else {
                    resp.setStatusValue(0);
                }
                respList.add(resp);
            });
        }
        PageBean<MaintainMessageResp> pageBeans = new PageBean<>();
        pageBeans.set(total, page + 1, size, respList);
        return pageBeans;
    }

    @Override
    public void config(int operator, Integer id , Date startTime, Date endTime, int status, String exampleContent) {
        if(id == null || id.equals("")) {
            maintainMessageDao.insert(startTime, endTime, status, exampleContent, new Date(), operator);
        } else {
            maintainMessageDao.update(id, startTime, endTime, status, exampleContent, new Date(), operator);
        }
//        if(status == 0) {
//            // 关闭维护
//            roomCreationPlanConfigDao.updateStatusWhenMaintainOff();
//        }else {
//            // 开启维护
//            roomCreationPlanConfigDao.updateStatusWhenMaintainOn();
//        }
    }

    @Override
    public void updateStatus(int operator, Integer id, int status) {
        maintainMessageDao.updateStatus(id, status, new Date(), operator);
    }

    @Override
    public MaintainMessageResp queryMaintainMessageById(Integer id) {
        MaintainMessage maintainMessage = maintainMessageDao.queryMaintainMessageById(id);
        MaintainMessageResp resp = new MaintainMessageResp();
        if (maintainMessage != null) {
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            resp.setId(maintainMessage.getId());
            resp.setStatus(maintainMessage.getStatus());
            resp.setStartTime(maintainMessage.getStartTime());
            resp.setEndTime(maintainMessage.getEndTime());
            resp.setStartTimeStr(df.format(maintainMessage.getStartTime()));
            resp.setEndTimeStr(df.format(maintainMessage.getEndTime()));
            resp.setExampleContent(maintainMessage.getExampleContent());

            if (maintainMessage.getStartTime().before(new Date())) {
                if (maintainMessage.getEndTime().after(new Date())) {
                    resp.setStatusValue(1);
                } else {
                    return null;
                }
            } else {
                resp.setStatusValue(0);
            }
        }
        return resp;
    }

    @Override
    public MaintainMessage queryMaintainMessage() {
        MaintainMessage maintainMessage = maintainMessageDao.queryMaintainMessage();
        return maintainMessage;
    }
}
