package com.allinpokers.yunying.model.response.warehouseacquisition;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <AUTHOR>
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SystemChipView {


    @ApiModelProperty("开始金豆")
    private Long starTotalChip;

    @ApiModelProperty("结束金豆")
    private Long endTotalChip;
    @ApiModelProperty("比较值，正数表示多，负数表示少")
    private Long compareChip;

}
