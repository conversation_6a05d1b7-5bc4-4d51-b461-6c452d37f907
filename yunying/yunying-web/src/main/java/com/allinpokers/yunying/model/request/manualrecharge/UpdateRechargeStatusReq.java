package com.allinpokers.yunying.model.request.manualrecharge;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class UpdateRechargeStatusReq {

    @ApiModelProperty("充豆记录ID")
    private Integer rechargeChipId;

    @ApiModelProperty("充豆状态：按充豆类型区分"
            + "<br/>手工收费/手工扣豆：1已收，2未收"
            + "<br/>手工提现：3已付，4未付"
    )
    @Min(1)
    @Max(4)
    private Integer rechargeStatus;
}
