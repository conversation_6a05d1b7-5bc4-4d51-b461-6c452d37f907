package com.allinpokers.yunying.count.bean;


import lombok.*;

import java.sql.Timestamp;

/**
 * DailyPlatformAccountSnapshot
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class DailyPlatformAccountSnapshot {

    /**
     * ID
     */
    private Long id;

    /**
     * 钻石-总数
     */
    private Long diamond;

    /**
     * 联盟币-总数
     */
    private Long tribeChip;

    /**
     * 联盟币-服务费分成总数
     */
    private Long tribeChipServiceShare;

    /**
     * 联盟币-保险费分成总数
     */
    private Long tribeChipInsuranceShare;

    /**
     * 联盟币-总分成总数
     */
    private Long tribeChipShare;

    /**
     * 金币-总数
     */
    private Long gold;

    /**
     * 金币-服务费分成总数
     */
    private Long goldServiceShare;

    /**
     * 金币-保险费分成总数
     */
    private Long goldInsuranceShare;

    /**
     * 金币-总分成总数
     */
    private Long goldShare;

    /**
     * 统计日期
     */
    private Timestamp countedAt;

    /**
     * 创建时间
     */
    private Timestamp createdAt;

}
