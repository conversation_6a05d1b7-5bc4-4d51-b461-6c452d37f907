package com.allinpokers.yunying.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 平台充值渠道  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PayChannel {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * cms账户id
     */
    @ApiModelProperty("cms账户id")
    private Integer cmsUid;

    /**
     * 渠道名称
     */
    @ApiModelProperty("渠道名称")
    private String name;

    /**
     * 渠道类型，0：代充充值渠道
     */
    @ApiModelProperty("渠道类型，0：代充充值渠道")
    private Integer type;

    /**
     * 开启战绩返佣
     */
    @ApiModelProperty("开启战绩返佣")
    private Boolean openRebate;

    /**
     * 战绩返佣比例
     */
    @ApiModelProperty("战绩返佣比例")
    private Double rebateRatio;

    /**
     * rebateSum
     */
    @ApiModelProperty("rebateSum")
    private Long rebateSum;

    /**
     * 俱乐部数量
     */
    @ApiModelProperty("俱乐部数量")
    private Integer clubCount;

    /**
     * 第三方支付商代码
     */
    @ApiModelProperty("第三方支付商代码")
    private String paymentCode;

    /**
     * 内部商户ID
     */
    @ApiModelProperty("内部商户ID")
    private Integer allinPartnerId;

    /**
     * 内部商户key
     */
    @ApiModelProperty("内部商户key")
    private String allinPartnerKey;

    /**
     * 联系方式。0-电话，1-微信，
     */
    @ApiModelProperty("联系方式。0-电话，1-微信，")
    private Integer contactType;

    /**
     * 联系账号
     */
    @ApiModelProperty("联系账号")
    private String contactAccount;

    /**
     * 渠道状态。0-正常可用，1-不可用
     */
    @ApiModelProperty("渠道状态。0-正常可用，1-不可用")
    private Integer status;

    /**
     * 创建者ID
     */
    @ApiModelProperty("创建者ID")
    private Integer creatorId;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createdTime;

    /**
     * 更新者ID
     */
    @ApiModelProperty("更新者ID")
    private Integer updaterId;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updatedTime;
}