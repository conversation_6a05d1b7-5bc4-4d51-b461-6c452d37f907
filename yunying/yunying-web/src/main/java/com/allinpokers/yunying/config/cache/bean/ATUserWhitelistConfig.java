package com.allinpokers.yunying.config.cache.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Getter
@Setter
@ToString
@Slf4j
public class ATUserWhitelistConfig {
    /**
     * 用户ID
     * 0值表示全局配置
     * >0表示用户级别配置
     */
    private Integer userId;
    /**
     * 允许访问的手机号码
     */
    private List<String> phoneLst;
    /**
     * 允许访问的IP
     */
    private List<String> ipLst;

    public boolean checkIfMatch(String phone,String ip){
        log.info("操作人 ip=>{},phone=>{}",ip,phone);
        if (ip == null || "".equals(ip.trim())) {
            return false;
        }
        if (phone == null || "".equals(phone.trim())) {
            return false;
        }

        //匹配ip/手机号码白名单
        log.info("操作人 ip=>{},phone=>{} -> ipWhitelist={} , phoneWhitelist={}",
                ip,phone,this.ipLst.toString(),this.phoneLst.toString());
        if (this.ipLst.contains(ip.trim()) && this.phoneLst.contains(phone.trim())) {
            return true;
        }
        return false;
    }
}
