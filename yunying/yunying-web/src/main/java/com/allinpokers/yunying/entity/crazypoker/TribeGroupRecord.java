package com.allinpokers.yunying.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TribeGroupRecord  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TribeGroupRecord {
    /**
     * id
     */
    @ApiModelProperty("id")
    private Integer id;

    /**
     * 子联盟id
     */
    @ApiModelProperty("子联盟id")
    private Integer tribeId;

    /**
     * 主联盟id
     */
    @ApiModelProperty("主联盟id")
    private Integer mainTribeId;

    /**
     * createTime
     */
    @ApiModelProperty("createTime")
    private LocalDateTime createTime;

    /**
     * updateTime
     */
    @ApiModelProperty("updateTime")
    private LocalDateTime updateTime;

    /**
     * 操作人id
     */
    @ApiModelProperty("操作人id")
    private Integer opId;
}