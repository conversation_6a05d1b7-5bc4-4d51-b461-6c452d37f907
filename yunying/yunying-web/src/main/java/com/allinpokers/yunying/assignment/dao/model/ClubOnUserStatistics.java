package com.allinpokers.yunying.assignment.dao.model;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClubOnUserStatistics {
    /**
     * clubId
     */
    @ApiModelProperty("clubId")
    private Integer clubId;

    /**
     * atNum
     */
    @ApiModelProperty("normalNum")
    private Integer normalNum;

    /**
     * atOpNum
     */
    @ApiModelProperty("opNum")
    private Integer opNum;
}
