package com.allinpokers.yunying.entity.crazypoker;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
@Builder
public class ClubUserFeeTotal {
    //俱乐部的用户数
    private int total;
    //俱乐部的总费用
    private int clubFeeTotal;
    //俱乐部的At总费用
    private int clubATFeeTotal;
    //俱乐部的收入
    private int clubProportion;
    //俱乐部的用户总费用
    private int clubUserFees;
    //俱乐部的成员
    private List<ClubUserFee> clubUserFee;
}
