package com.allinpokers.yunying.services.impl;

import com.allinpokers.yunying.appmessage.send.AppBusinessMessageSender;
import com.allinpokers.yunying.appmessage.send.bean.ManualRechargeDiamonds;
import com.allinpokers.yunying.dao.crazypoker.ManualRechargeUserChipDao;
import com.allinpokers.yunying.dao.crazypoker.PlatformAccountDao;
import com.allinpokers.yunying.entity.crazypoker.*;
import com.allinpokers.yunying.entity.crazypoker.example.ManualRechargeUserChipExample;
import com.allinpokers.yunying.enu.*;
import com.allinpokers.yunying.exception.UserException;
import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.model.response.PageBean;
import com.allinpokers.yunying.model.response.manualrecharge.BatchRechargeUserResult;
import com.allinpokers.yunying.model.response.manualrecharge.ManualRechargeChipSum;
import com.allinpokers.yunying.model.response.manualrecharge.ManualRechargeUserChipInfo;
import com.allinpokers.yunying.model.response.manualrecharge.UserChipInfo;
import com.allinpokers.yunying.permission.constants.EDataPermission;
import com.allinpokers.yunying.permission.entity.AuthUser;
import com.allinpokers.yunying.permission.security.UserInfo;
import com.allinpokers.yunying.permission.service.AuthUserService;
import com.allinpokers.yunying.rabbitmq.client.MessageSender;
import com.allinpokers.yunying.rabbitmq.client.bean.InteriorMessage;
import com.allinpokers.yunying.rabbitmq.constant.EMessageCode;
import com.allinpokers.yunying.services.*;
import com.allinpokers.yunying.services.model.ListManualRechargeUserChipParams;
import com.allinpokers.yunying.services.model.ManualRechargeBatchUserChipParams;
import com.allinpokers.yunying.services.model.ManualRechargeLimit;
import com.allinpokers.yunying.services.model.ManualRechargeOneUserChipParams;
import com.allinpokers.yunying.sms.constant.ESmsType;
import com.allinpokers.yunying.sms.service.ISmsService;
import com.allinpokers.yunying.util.excel.ExcelModel;
import com.allinpokers.yunying.util.excel.ExcelRow;
import com.allinpokers.yunying.util.excel.ExcelSheet;
import com.allinpokers.yunying.util.excel.ExcelUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("manualRechargeUserChipService")
public class ManualRechargeUserChipServiceImpl implements ManualRechargeUserChipService {
    @Resource
    private ISmsService smsService;
    @Resource
    private UserAccountService userAccountService;
    @Resource
    private ManualRechargeUserChipDao manualRechargeUserChipDao;
    @Resource
    private UserDetailsInfoService userDetailsInfoService;
    @Resource
    private AuthUserService authUserService;
    @Resource
    private PlatformAccountDao platformAccountDao;
    @Resource
    private PlatformAccountLogService platformAccountLogService;
    @Resource
    private MessageMoneyRecordService messageMoneyRecordService;
    @Resource
    private ManualRechargeUserChipServiceImpl self;
    @Resource
    private MessageSender messageSender;

    @Resource
    AppBusinessMessageSender appBusinessMessageSender;

    @Override
    public PageBean<ManualRechargeUserChipInfo> listRechargeChipLogs(ListManualRechargeUserChipParams params) {
        ManualRechargeUserChipExample example = new ManualRechargeUserChipExample();
        ManualRechargeUserChipExample.Criteria criteria = example.or();
        if (!Strings.isNullOrEmpty(params.getUserRandomId())) {
            UserDetailsInfo user = userDetailsInfoService.findUserByUserRamdomId(params.getUserRandomId());
            if (user == null) {
                return PageBean.empty();
            }
            criteria.andUserIdEqualTo(user.getUserId());
        }
        if (params.getRechargeType() != null) {
            criteria.andRechargeTypeEqualTo(params.getRechargeType());
        }
        //时间必传
        criteria.andCreatedTimeBetween(params.getStartTime(), params.getEndTime());
        PageHelper.orderBy("created_time DESC");
        PageHelper.startPage(params.getPage() == null ? 0 : params.getPage(), params.getSize() == null ? 0 : params.getSize(), true, null, true);
        Page<ManualRechargeUserChip> page = (Page<ManualRechargeUserChip>) manualRechargeUserChipDao.selectByExample(example);
        if (page.isEmpty()) {
            return PageBean.empty();
        }
        Set<Integer> userIds = page.stream().map(ManualRechargeUserChip::getUserId).collect(Collectors.toSet());
        Map<Integer, UserDetailsInfo> userMap = userDetailsInfoService.findUsersMapByUserIds(userIds);

        Set<Integer> authUserIds = page.stream().map(ManualRechargeUserChip::getCreatorId).collect(Collectors.toSet());
        Map<Integer, AuthUser> operatorMap = authUserService.findUserMapByUserIds(authUserIds);

        List<ManualRechargeUserChipInfo> infoList = page.stream().map(record -> {
            UserDetailsInfo user = userMap.getOrDefault(record.getUserId(), new UserDetailsInfo());
            AuthUser operator = operatorMap.getOrDefault(record.getCreatorId(), new AuthUser());
            return ManualRechargeUserChipInfo.builder()
                    .id(record.getId())
                    .userNickname(user.getNikeName())
                    .userRandomId(user.getRandomNum())
                    .currentExtractChip(record.getCurrentExtractChip())
                    .currentNotExtractChip(record.getCurrentNotExtractChip())
                    .afterRechargeExtractChip(record.getAfterRechargeExtractChip())
                    .afterRechargeNotExtractChip(record.getAfterRechargeNotExtractChip())
                    .chipType(record.getChipType())
                    .rechargeType(record.getRechargeType())
                    .rechargeStatus(record.getRechargeStatus())
                    .rechargeChip(record.getRechargeChip())
                    .operatorId(record.getCreatorId())
                    .operatorUsername(operator.getUsername())
                    .applicant(record.getApplicant())
                    .approver(record.getApprover())
                    .remark(record.getRemark())
                    .replenishmentNo(record.getReplenishmentNo())
                    .createdTime(record.getCreatedTime())
                    .build();
        }).collect(Collectors.toList());

        return PageBean.of(page.getTotal(), params.getPage(), params.getSize(), infoList);
    }

    @Override
    public ManualRechargeChipSum sumRechargeChipLogs(ListManualRechargeUserChipParams params) {
        Integer userId = null;
        if (!Strings.isNullOrEmpty(params.getUserRandomId())) {
            UserDetailsInfo user = userDetailsInfoService.findUserByUserRamdomId(params.getUserRandomId());
            if (user == null) {
                return new ManualRechargeChipSum(0L);
            }
            userId = user.getUserId();
        }

        long totalAbsRechargeChip = manualRechargeUserChipDao.sumUserAbsRechargeChip(userId, params.getRechargeType(), params.getStartTime(), params.getEndTime());
        return new ManualRechargeChipSum(totalAbsRechargeChip);
    }

    @Override
    public void exportRechargeChipLogs(ListManualRechargeUserChipParams params, HttpServletResponse response) throws IOException {
        //导出不需要分页
        params.setPage(null);
        params.setSize(null);

        PageBean<ManualRechargeUserChipInfo> page = this.listRechargeChipLogs(params);
        ManualRechargeChipSum sum = this.sumRechargeChipLogs(params);


        ExcelSheet sheet = new ExcelSheet("手工充豆-用户");
        List<ExcelModel> models = new ArrayList<>();
        sheet.setModels(models);

        List<ExcelRow> rows = Lists.newArrayList(
                new ExcelRow().add("充豆总量：").add(sum.getTotalAbsRechargeChip())
        );
        ExcelModel model = ExcelModel.builder()
                .rows(rows)
                .afterBlankLine(1)
                .build();
        models.add(model);

        String[] titles = new String[]{"序号", "用户昵称", "用户ID", "用户金豆余额（可提）", "用户金豆余额（不可提）", "用户赠送后余额（可提）", "用户赠送后余额（不可提）", "金豆类型", "充豆类型",
                "充豆状态", "充豆数量", "申请人", "审批人", "补单单号", "操作账号", "操作原因", "操作时间"};
        rows = new ArrayList<>();
        for (int i = 0; i < page.getBeanList().size(); i++) {
            ManualRechargeUserChipInfo info = page.getBeanList().get(i);

            ExcelRow row = new ExcelRow()
                    .add(i + 1)
                    .add(info.getUserNickname())
                    .add(info.getUserRandomId())
                    .add(info.getCurrentExtractChip())
                    .add(info.getCurrentNotExtractChip())
                    .add(info.getAfterRechargeExtractChip())
                    .add(info.getAfterRechargeNotExtractChip())
                    .add(EManualRechargeUserChipChipType.getDescByCode(info.getChipType()))
                    .add(EManualRechargeUserChipRechargeType.getDescByCode(info.getRechargeType()))
                    .add(EManualRechargeUserChipRechargeStatus.getDescByCode(info.getRechargeStatus()))
                    .add(info.getRechargeChip())
                    .add(info.getApplicant())
                    .add(info.getApprover())
                    .add(info.getReplenishmentNo())
                    .add(info.getOperatorUsername())
                    .add(info.getRemark())
                    .add(info.getCreatedTime());
            rows.add(row);
        }
        model = ExcelModel.builder()
                .titles(Lists.newArrayList(titles))
                .rows(rows)
                .afterBlankLine(1)
                .build();
        models.add(model);

        String fileName = URLEncoder.encode(sheet.getName(), "UTF-8");
        response.setHeader("Content-Type", "application/octet-stream");
        response.setHeader("Content-Disposition", String.format("attachment;fileName=\"%s.%s\"", fileName, "xlsx"));
        ExcelUtils.write(response.getOutputStream(), sheet);
    }

    @Override
    public CommonRespon doManualRecharge(UserInfo operationUser, ManualRechargeOneUserChipParams params, String smsCode) {
        log.info("doManualRecharge: operatorId={}, params={}, smsCode={}", operationUser.getId(), params, smsCode);
        // boolean checkSmsCode = smsService.checkSmsCode(ESmsType.MANUAL_RECHARGE_CHIP.getValue(), operationUser.getMobileNo(), smsCode, operationUser.getMobileAreaCode());
        // if (!checkSmsCode) {
        //     return CommonRespon.failure(ResponseCodeEnum.SMS_CAPTCHA_CODE_ERROR);
        // }
        self.doRechargeOne(operationUser, params);
        return CommonRespon.success();
    }

    /**
     * 参数校验完成，开始充豆给某个用户
     *
     * @param params
     */
    @Transactional(transactionManager = "crazyPokerTransactionManager", rollbackFor = RuntimeException.class)
    public void doRechargeOne(UserInfo operator, ManualRechargeOneUserChipParams params) {
        EManualRechargeUserChipRechargeType rechargeType = EManualRechargeUserChipRechargeType.valueOf(params.getRechargeType());
        switch (rechargeType) {
            case MANUAL_CHARGE:
                //1, 手工收费
                manualCharge(operator, params);
                break;
            case HAND_PICKED_BEANS:
                //2, 手工扣豆
                handPickedBeans(operator, params);
                break;
            case GIFT:
                //3, 赠送
                gift(operator, params);
                break;
            case SYSTEM_RED_ENVELOPE:
                //4, 系统红包
                systemRedEnvelope(operator, params);
                break;
            case MANUAL_REPLENISHMENT:
                //5, 手工补单
                manualReplenishment(operator, params);
                break;
            case RED_ENVELOPE_IN_GAME:
                //6, 营销-牌桌金豆红包
                redEnvelopeInGame(operator, params);
                break;
            case BUG_PAYOUT:
                //7, BUG赔付
                bugPayout(operator, params);
                break;
            case MANUAL_WITHDRAWAL:
                //8, 手动提现
                manualWithdrawal(operator, params);
                break;
            case T_LIST:
                //9, T名单
                tList(operator, params);
                break;
            default:
                throw new UserException(ResponseCodeEnum.MANUAL_RECHARGE_NOT_ALLOW_RECHARGE_TYPE);
        }
    }


    /**
     * 手工收费
     *
     * @param params
     */
    private void manualCharge(UserInfo operator, ManualRechargeOneUserChipParams params) {
        if (params.getRechargeChip() == null || params.getRechargeChip() <= 0) {
            //参数错误：充豆的金豆数量必须 >0
            throw new UserException(ResponseCodeEnum.PARAM_ERROR);
        }
        if (params.getRechargeStatus() == null || params.getRechargeStatus() < 1 || params.getRechargeStatus() > 2) {
            //参数错误：需要设置充豆状态
            throw new UserException(ResponseCodeEnum.PARAM_ERROR);
        }
        //手工收费 不需要记录单号
        params.setReplenishmentNo(null);

        updateDatabase(operator, params);
    }

    /**
     * 手工扣豆
     *
     * @param params
     */
    private void handPickedBeans(UserInfo operator, ManualRechargeOneUserChipParams params) {
        if (params.getRechargeChip() == null || params.getRechargeChip() >= 0) {
            //参数错误：充豆的金豆数量必须 <0
            throw new UserException(ResponseCodeEnum.PARAM_ERROR);
        }
        if (params.getRechargeStatus() == null || params.getRechargeStatus() < 1 || params.getRechargeStatus() > 2) {
            //参数错误：需要设置充豆状态
            throw new UserException(ResponseCodeEnum.PARAM_ERROR);
        }
        //手工扣豆 不需要记录单号
        params.setReplenishmentNo(null);

        updateDatabase(operator, params);
    }

    /**
     * 3, 赠送
     *
     * @param params
     */
    private void gift(UserInfo operator, ManualRechargeOneUserChipParams params) {
        if (params.getRechargeChip() == null || params.getRechargeChip() <= 0) {
            //参数错误：充豆的金豆数量必须 >0
            throw new UserException(ResponseCodeEnum.PARAM_ERROR);
        }
        //赠送 不需要记录状态和单号
        params.setRechargeStatus(null);
        params.setReplenishmentNo(null);
        updateDatabase(operator, params);
    }


    /**
     * 4, 系统红包
     *
     * @param params
     */
    private void systemRedEnvelope(UserInfo operator, ManualRechargeOneUserChipParams params) {
        if (params.getRechargeChip() == null || params.getRechargeChip() <= 0) {
            //参数错误：充豆的金豆数量必须 >0
            throw new UserException(ResponseCodeEnum.PARAM_ERROR);
        }
        //系统红包 不需要记录状态和单号
        params.setRechargeStatus(null);
        params.setReplenishmentNo(null);

        //这里可能需要做特殊处理：
        //TODO 系统红包：先扣手工充值仓的豆，但是不给用户加金豆，等用户领取了之后再加金豆
        //TODO 2019/7/22 这里修改时, 需要修改web-api, 钱包的其他记录中和 系统红包 相关的sql
        updateDatabase(operator, params);
    }

    /**
     * 5, 手工补单
     *
     * @param params
     */
    private void manualReplenishment(UserInfo operator, ManualRechargeOneUserChipParams params) {
        if (params.getRechargeChip() == null || params.getRechargeChip() <= 0) {
            //参数错误：充豆的金豆数量必须 >0
            throw new UserException(ResponseCodeEnum.PARAM_ERROR);
        }
        //手工补单 不需要记录状态
        params.setRechargeStatus(null);

        updateDatabase(operator, params);
    }

    /**
     * 6, 营销-牌桌金豆红包
     *
     * @param params
     */
    private void redEnvelopeInGame(UserInfo operator, ManualRechargeOneUserChipParams params) {
        if (params.getRechargeChip() == null || params.getRechargeChip() <= 0) {
            //参数错误：充豆的金豆数量必须 >0
            throw new UserException(ResponseCodeEnum.PARAM_ERROR);
        }
        //营销-牌桌金豆红包 不需要记录状态和单号
        params.setRechargeStatus(null);
        params.setReplenishmentNo(null);

        updateDatabase(operator, params);
        //发送牌局红包雨消息
        InteriorMessage message = InteriorMessage.builder()
                .param1(params.getUserId().toString())
                .param2(params.getRechargeChip().toString())
                .type(6010)
                .build();
        messageSender.sendInteriorMessage(message);
    }

    /**
     * 7, BUG赔付
     *
     * @param params
     */
    private void bugPayout(UserInfo operator, ManualRechargeOneUserChipParams params) {
        if (params.getRechargeChip() == null || params.getRechargeChip() <= 0) {
            //参数错误：充豆的金豆数量必须 >0
            throw new UserException(ResponseCodeEnum.PARAM_ERROR);
        }
        //BUG赔付 不需要记录状态和单号
        params.setRechargeStatus(null);
        params.setReplenishmentNo(null);

        updateDatabase(operator, params);
    }

    /**
     * 8, 手动提现
     *
     * @param params
     */
    private void manualWithdrawal(UserInfo operator, ManualRechargeOneUserChipParams params) {
        if (params.getRechargeChip() == null || params.getRechargeChip() >= 0) {
            //参数错误：充豆的金豆数量必须 <0
            throw new UserException(ResponseCodeEnum.PARAM_ERROR);
        }
        if (params.getRechargeStatus() == null || params.getRechargeStatus() < 3 || params.getRechargeStatus() > 4) {
            //参数错误：需要设置充豆状态
            throw new UserException(ResponseCodeEnum.PARAM_ERROR);
        }
        //手动提现 不需要记录单号
        params.setReplenishmentNo(null);

        updateDatabase(operator, params);
    }

    /**
     * 9, t名单
     * @param operator
     * @param params
     */
    private void tList(UserInfo operator, ManualRechargeOneUserChipParams params) {
        if (params.getRechargeChip() == null || params.getRechargeChip() <= 0) {
            //参数错误：充豆的金豆数量必须 >0
            throw new UserException(ResponseCodeEnum.PARAM_ERROR);
        }
        //系统红包 不需要记录状态和单号
        params.setRechargeStatus(null);
        params.setReplenishmentNo(null);
        updateDatabase(operator, params);
    }

    private void updateDatabase(UserInfo operator, ManualRechargeOneUserChipParams params) {
        //检查手工充豆权限
        /* ******** 需求变动，需要解除充值限制
        checkPermission(operator, Math.abs(params.getRechargeChip()));
        */
        //扣减 手工充值仓
        int platformAccountCode = EPlatformAccountCode.MANUAL_RECHARGE.getCode();
        int changeRow = platformAccountDao.subtractChipNotLessThanZero(platformAccountCode, params.getRechargeChip());
        if (changeRow < 1) {
            throw new UserException(ResponseCodeEnum.MANUAL_RECHARGE_MINUS);
        }

        boolean changeExtractable = params.getChipType().equals(EManualRechargeUserChipChipType.EXTRACTABLE.getCode());
        if (changeExtractable) {
            //加/减 可提金豆
            changeRow = userAccountService.addExtractChip(params.getUserId(), params.getRechargeChip());
        } else {
            //加/减 可提金豆
            changeRow = userAccountService.addNotExtractChip(params.getUserId(), params.getRechargeChip());
        }
        if (changeRow < 1) {
            throw new UserException(ResponseCodeEnum.MANUAL_RECHARGE_BALANCE_LACK);
        }

        //保存 手工充值记录
        UserAccount userAccount = userAccountService.selectByPrimaryKey(params.getUserId());
        Integer currentExtractChip = changeExtractable ? userAccount.getChip() - params.getRechargeChip() : userAccount.getChip();
        Integer currentNotExtractChip = changeExtractable ? userAccount.getNotExtractChip() : userAccount.getNotExtractChip() - params.getRechargeChip();
        ManualRechargeUserChip manualRechargeUserChip = ManualRechargeUserChip.builder()
                .userId(params.getUserId())
                .chipType(params.getChipType())
                .rechargeType(params.getRechargeType())
                .rechargeChip(params.getRechargeChip())
                .currentExtractChip(currentExtractChip)
                .currentNotExtractChip(currentNotExtractChip)
                .afterRechargeExtractChip(userAccount.getChip())
                .afterRechargeNotExtractChip(userAccount.getNotExtractChip())
                .applicant(params.getApplicant())
                .approver(params.getApprover())
                .rechargeStatus(params.getRechargeStatus())
                .replenishmentNo(params.getReplenishmentNo())
                .remark(params.getRemark())
                .creatorId(operator.getId())
                .createdTime(LocalDateTime.now())
                .updaterId(operator.getId())
                .updatedTime(LocalDateTime.now())
                .build();
        manualRechargeUserChipDao.insertSelective(manualRechargeUserChip);

        //保存 手工充值仓 变化日志
        PlatformAccount platformAccount = platformAccountDao.selectByPrimaryKey(platformAccountCode);
        long oldPlatformAccountChip = platformAccount.getChip() + params.getRechargeChip();
        platformAccountLogService.insertPlatFormAccountLog(EPlatformAccountChangeSource.OSM_TYPE,
                EPlatformAccount.MANUAL_RECHARGE, EPlatformAccountLogType.MANUAL_RECHARGE_TO_USER,
                (int) oldPlatformAccountChip, -params.getRechargeChip(), operator.getId(), manualRechargeUserChip.getId() + "");

        //用户金豆变化日志
        UserAccountLog userAccountLog = UserAccountLog.builder()
                .userId(params.getUserId())
                .type(EUserAccountLogType.MANUAL_RECHARGE_CHIP.getCode())
                .changeSource(7)
                .currentChip(currentExtractChip)
                .changeChip(changeExtractable ? params.getRechargeChip() : 0)
                .currNotExtractChip(currentNotExtractChip)
                .changeNotExtractChip(changeExtractable ? 0 : params.getRechargeChip())
                .currPlCount(userAccount.getPlCount())
                .changePlCount(0)
                .desction(params.getRemark())
                .externalId(manualRechargeUserChip.getId() + "")
                .opId(operator.getId())
                .createdTime(LocalDateTime.now())
                .build();
        userAccountService.addLog(userAccountLog);

        if (params.getRechargeChip() > 0) {
            /* ******** 注释掉旧的消息发送 <AUTHOR>
            //发送钱包消息
            messageMoneyRecordService.sendMessage(EMessageCode.MONEY_SYSTEM_MANUAL_RECHARGE, "YY-" + operator.getId(),
                    Collections.singletonList(params.getUserId() + ""), params.getRechargeChip()/100.00 + "");
            */

            // 新的消息发送
            appBusinessMessageSender.notifyManualRechargeDiamondsSuccessful(ManualRechargeDiamonds.builder()
                    .userId((long) params.getUserId())
                    .diamondAmount( params.getRechargeChip() / 100.00)
                    .build());

        }
    }

    /**
     * 检查充豆权限
     *
     * @param operator        operator
     * @param absRechargeChip
     */
    @Override
    public void checkPermission(UserInfo operator, int absRechargeChip) {
        absRechargeChip = Math.abs(absRechargeChip);
        if (operator.hasData(EDataPermission.osm_manual_recharge_chip_supper_permission.name())) {
            //手工充豆 - 超级账号：不限制，此级别不需要添加到权限设置中
            return;
        }

        LocalDateTime start = LocalDate.now().atTime(LocalTime.MIN);
        LocalDateTime end = LocalDate.now().atTime(LocalTime.MAX);
        int thousand = 10000;

        List<ManualRechargeLimit> limits = Lists.newArrayList(
                new ManualRechargeLimit(EDataPermission.osm_manual_recharge_chip_platform_permission, 50 * thousand, 1000 * thousand),
                new ManualRechargeLimit(EDataPermission.osm_manual_recharge_chip_operation_permission, 20 * thousand, 200 * thousand),
                new ManualRechargeLimit(EDataPermission.osm_manual_recharge_chip_customer_service_permission, 10 * thousand, 100 * thousand)
        );
        int totalChip = manualRechargeUserChipDao.sumUserAndPayChannelRechargeChip(operator.getId(), start, end);
        for (ManualRechargeLimit limit : limits) {
            if (operator.hasData(limit.getDataPermission().name())) {
                //手工充豆 - 平台权限：单笔数量限制在50万金豆以下，单日总额限制在1000万金豆以下
                if (absRechargeChip > limit.getSingleLimit()) {
                    log.info("manual recharge chip, check permissions: operatorId={}, username={}, absRechargeChip={}, singleLimit={}",
                            operator.getId(), operator.getUsername(), absRechargeChip, limit.getSingleLimit());
                    throw new UserException(ResponseCodeEnum.PERMISSION_ACCESS_DENIED);
                }
                if (totalChip + absRechargeChip > limit.getOneDayLimit()) {
                    log.info("manual recharge chip, check permissions: operatorId={}, username={}, absRechargeChip={}, beforeTotalChip={}, oneDayLimit={}",
                            operator.getId(), operator.getUsername(), absRechargeChip, totalChip, limit.getOneDayLimit());
                    throw new UserException(ResponseCodeEnum.PERMISSION_ACCESS_DENIED);
                }
                //有该权限，并且没有超出单笔限制和单日总额限制
                return;
            }
        }
        //没有权限
        throw new UserException(ResponseCodeEnum.PERMISSION_ACCESS_DENIED);
    }

    @Override
    public CommonRespon<UserChipInfo> getUserInfo(String userRandomId) {
        UserDetailsInfo user = userDetailsInfoService.findUserByUserRamdomId(userRandomId);
        if (user == null) {
            return CommonRespon.failure(ResponseCodeEnum.USER_NOT_EXIST);
        }
        UserAccount userAccount = userAccountService.selectByPrimaryKey(user.getUserId());
        if (userAccount == null) {
            return CommonRespon.failure(ResponseCodeEnum.USER_NOT_EXIST);
        }

        UserChipInfo info = UserChipInfo.builder()
                .userId(user.getUserId())
                .nickname(user.getNikeName())
                .extractChip(userAccount.getChip())
                .notExtractChip(userAccount.getNotExtractChip())
                .build();
        return CommonRespon.success(info);
    }

    @Override
    public CommonRespon<List<BatchRechargeUserResult>> doBatchManualRecharge(UserInfo operationUser, ManualRechargeBatchUserChipParams batchParams) {
        log.info("doBatchManualRecharge: operatorId={}, batchParams={}", operationUser.getId(), batchParams);
        boolean checkSmsCode = smsService.checkSmsCode(ESmsType.MANUAL_RECHARGE_CHIP.getValue(),
                operationUser.getMobileNo(), batchParams.getSmsCode(), operationUser.getMobileAreaCode());
        if (!checkSmsCode) {
            return CommonRespon.failure(ResponseCodeEnum.SMS_CAPTCHA_CODE_ERROR);
        }

        List<UserDetailsInfo> users = userDetailsInfoService.findUserByUserRamdomIds(batchParams.getUserRandomIds());
        Map<String, UserDetailsInfo> map = users.stream().collect(Collectors.toMap(UserDetailsInfo::getRandomNum, v -> v));
        List<String> doneRecharges = new ArrayList<>();

        List<BatchRechargeUserResult> results = new ArrayList<>(batchParams.getUserRandomIds().size());
        for (String userRandomId : batchParams.getUserRandomIds()) {
            if (doneRecharges.contains(userRandomId)) {
                //已执行过，不需要重复执行
                results.add(new BatchRechargeUserResult(userRandomId, "重复输入，不予操作"));
                continue;
            }
            UserDetailsInfo user = map.get(userRandomId);
            if (user == null) {
                results.add(new BatchRechargeUserResult(userRandomId, "用户不存在，不予操作"));
                continue;
            }
            ManualRechargeOneUserChipParams params = ManualRechargeOneUserChipParams.builder()
                    .userId(user.getUserId())
                    .chipType(batchParams.getChipType())
                    .rechargeType(batchParams.getRechargeType())
                    .rechargeStatus(batchParams.getRechargeStatus())
                    .replenishmentNo(batchParams.getReplenishmentNo())
                    .rechargeChip(batchParams.getRechargeChip())
                    .applicant(batchParams.getApplicant())
                    .approver(batchParams.getApprover())
                    .remark(batchParams.getRemark())
                    .build();
            try {
                self.doRechargeOne(operationUser, params);
                results.add(new BatchRechargeUserResult(userRandomId, "操作成功"));
            } catch (Exception e) {
                if (e instanceof UserException) {
                    ResponseCodeEnum responseEnum = ((UserException) e).getResponseEnum();
                    log.info("doBatchManualRecharge failure: userId={}, params={}, exception={}",
                            user.getUserId(), params, responseEnum);
                    String msg;
                    switch (responseEnum) {
                        case MANUAL_RECHARGE_MINUS:
                            msg = "手工充值仓余额不足，请充值后重新操作";
                            break;
                        case MANUAL_RECHARGE_BALANCE_LACK:
                            msg = "用户金豆余额不足以扣除，操作失败";
                            break;
                        default:
                            msg = responseEnum.getMsg();
                    }
                    results.add(new BatchRechargeUserResult(userRandomId, msg));
                } else {
                    log.info("doBatchManualRecharge failure: userId={}, params={}", user.getUserId(), params, e);
                    results.add(new BatchRechargeUserResult(userRandomId, "操作失败，请重新操作"));
                }
            } finally {
                //无论失败和成功，都视为已执行
                doneRecharges.add(userRandomId);
            }
        }

        return CommonRespon.success(results);
    }

    @Override
    public CommonRespon updateRechargeStatus(Integer operatorId, Integer rechargeUserChipId, Integer rechargeStatus) {
        ManualRechargeUserChip recharge = manualRechargeUserChipDao.selectByPrimaryKey(rechargeUserChipId);
        if (recharge == null) {
            return CommonRespon.failure(ResponseCodeEnum.DATA_EMPT);
        }
        log.info("updateRechargeStatus: operatorId={}, rechargeUserChipId={}, oldRechargeStatus={}, newRechargeStatus={}",
                operatorId, rechargeUserChipId, recharge.getRechargeStatus(), rechargeStatus);

        List<EManualRechargeUserChipRechargeType> exceptTypes = Lists.newArrayList(
                EManualRechargeUserChipRechargeType.MANUAL_CHARGE, EManualRechargeUserChipRechargeType.HAND_PICKED_BEANS,
                EManualRechargeUserChipRechargeType.MANUAL_WITHDRAWAL
        );
        boolean isExceptType = exceptTypes.stream().anyMatch(type -> recharge.getRechargeType() != null && recharge.getRechargeType().equals(type.getCode()));
        if (!isExceptType) {
            return CommonRespon.failure(ResponseCodeEnum.MANUAL_RECHARGE_NOT_ALLOW_RECHARGE_TYPE);
        }
        if (rechargeStatus.equals(recharge.getRechargeStatus())) {
            //旧的状态和新的状态一致，无需更新
            return CommonRespon.success();
        }

        ManualRechargeUserChip updateRecharge = ManualRechargeUserChip.builder()
                .id(recharge.getId())
                .rechargeStatus(rechargeStatus)
                .updaterId(operatorId)
                .updatedTime(LocalDateTime.now())
                .build();
        manualRechargeUserChipDao.updateByPrimaryKeySelective(updateRecharge);
        return CommonRespon.success();
    }
}
