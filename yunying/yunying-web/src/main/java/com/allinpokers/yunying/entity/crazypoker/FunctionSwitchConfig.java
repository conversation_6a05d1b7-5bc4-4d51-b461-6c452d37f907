package com.allinpokers.yunying.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 功能模块开关配置  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FunctionSwitchConfig {
    /**
     * 表主键
     */
    @ApiModelProperty("表主键")
    private Integer id;

    /**
     * 功能名称
     */
    @ApiModelProperty("功能名称")
    private String function;

    /**
     * 开关状态。0-关闭，1-开启
     */
    @ApiModelProperty("开关状态。0-关闭，1-开启")
    private Integer status;

    /**
     * 功能配置的值
     */
    @ApiModelProperty("功能配置的值")
    private String value;

    /**
     * 创建者人员
     */
    @ApiModelProperty("创建者人员")
    private Integer createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人员
     */
    @ApiModelProperty("更新人员")
    private Integer updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
}