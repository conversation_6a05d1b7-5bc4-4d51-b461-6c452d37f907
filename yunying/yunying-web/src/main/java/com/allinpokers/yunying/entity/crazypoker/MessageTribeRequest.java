package com.allinpokers.yunying.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MessageTribeRequest  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MessageTribeRequest {
    /**
     * 消息uuid
     */
    @ApiModelProperty("消息uuid")
    private String msgId;

    /**
     * 联盟id
     */
    @ApiModelProperty("联盟id")
    private String tribeId;

    /**
     * 创建者用户id
     */
    @ApiModelProperty("创建者用户id")
    private String userId;

    /**
     * 俱乐部id
     */
    @ApiModelProperty("俱乐部id")
    private String clubId;

    /**
     * 附件参数 json
     */
    @ApiModelProperty("附件参数 json")
    private String param;

    /**
     * 请求类型  1、创建联盟
     */
    @ApiModelProperty("请求类型  1、创建联盟")
    private Integer type;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}