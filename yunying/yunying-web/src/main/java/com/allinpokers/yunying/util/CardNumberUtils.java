package com.allinpokers.yunying.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * CardNumberUtils
 */
@Slf4j
public class CardNumberUtils {

    private static Map<Integer, String> cardMap = new HashMap<>();

    static {
        cardMap.put(0, "2d");
        cardMap.put(1, "3d");
        cardMap.put(2, "4d");
        cardMap.put(3, "5d");
        cardMap.put(4, "6d");
        cardMap.put(5, "7d");
        cardMap.put(6, "8d");
        cardMap.put(7, "9d");
        cardMap.put(8, "Td");
        cardMap.put(9, "Jd");
        cardMap.put(10, "Qd");
        cardMap.put(11, "Kd");
        cardMap.put(12, "Ad");
        cardMap.put(13, "2c");
        cardMap.put(14, "3c");
        cardMap.put(15, "4c");
        cardMap.put(16, "5c");
        cardMap.put(17, "6c");
        cardMap.put(18, "7c");
        cardMap.put(19, "8c");
        cardMap.put(20, "9c");
        cardMap.put(21, "Tc");
        cardMap.put(22, "Jc");
        cardMap.put(23, "Qc");
        cardMap.put(24, "Kc");
        cardMap.put(25, "Ac");
        cardMap.put(26, "2h");
        cardMap.put(27, "3h");
        cardMap.put(28, "4h");
        cardMap.put(29, "5h");
        cardMap.put(30, "6h");
        cardMap.put(31, "7h");
        cardMap.put(32, "8h");
        cardMap.put(33, "9h");
        cardMap.put(34, "Th");
        cardMap.put(35, "Jh");
        cardMap.put(36, "Qh");
        cardMap.put(37, "Kh");
        cardMap.put(38, "Ah");
        cardMap.put(39, "2s");
        cardMap.put(40, "3s");
        cardMap.put(41, "4s");
        cardMap.put(42, "5s");
        cardMap.put(43, "6s");
        cardMap.put(44, "7s");
        cardMap.put(45, "8s");
        cardMap.put(46, "9s");
        cardMap.put(47, "Ts");
        cardMap.put(48, "Js");
        cardMap.put(49, "Qs");
        cardMap.put(50, "Ks");
        cardMap.put(51, "As");
    }

    /**
     * 获取卡名称
     * @param number 排序
     * @return
     */
    public static String getCardName(Integer number) {
        if (number == null) {
            return null;
        }
        String cardName = cardMap.get(number);
        if (StringUtils.isBlank(cardName)) {
            return null;
        }
        return cardName;
    }
}