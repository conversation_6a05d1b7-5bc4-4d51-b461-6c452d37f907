package com.allinpokers.yunying.enu;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum EscrowSupportStatus implements BaseEnum {
    //
    DISABLE(0, "不启用"),
    ENABLE(1, "启用");
    private final int code;
    private final String desc;

    public static EscrowSupportStatus valueOf(Integer code) {
        return BaseEnum.valueOf(EscrowSupportStatus.class, code);
    }
}
