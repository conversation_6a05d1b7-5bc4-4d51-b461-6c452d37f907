package com.allinpokers.yunying.model.response.club;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
@Data
@ApiModel(description = "俱乐部头部信息")
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClubHeadInfoBean {
    @ApiModelProperty(value ="俱乐部id" )
    private Integer id;
    @ApiModelProperty(value = "俱乐部显性id ")
    private String clubRandomId;
    @ApiModelProperty(value ="俱乐部名称" )
    private String clubName;
    @ApiModelProperty(value ="俱乐部头像地址" )
    private String clubHead;
    @ApiModelProperty("俱乐部描述")
    private String description;
    @ApiModelProperty("俱乐部所属地区编码")
    private String areaId;
    @ApiModelProperty(value ="俱乐部创建者id" )
    private String creatorId;
    @ApiModelProperty(value ="俱乐部创建者昵称" )
    private String creatorName;
    @ApiModelProperty(value ="俱乐部显性id",name = "randomId",dataType = "int")
    private Integer randomId;
    @ApiModelProperty(value ="俱乐部推广员数",name = "promoterNumbers",dataType = "long")
    private Long promoterNumbers=0l;
    @ApiModelProperty(value ="俱乐部推广员数==今日",dataType = "int")
    private Integer promoterTodayNum=0;
    @ApiModelProperty(value ="俱乐部推广员业绩")
    private Integer promoterAchievement=0;
    @ApiModelProperty(value ="俱乐部当前成员数",name = "clubMembers",dataType = "int")
    private Integer clubMembers=0;
    @ApiModelProperty(value ="俱乐部今日返豆支出")
    private Integer clubTodayChips=0;
    @ApiModelProperty(value ="俱乐部返豆支出")
    private Integer clubBackChips=0;
    @ApiModelProperty(value ="俱乐部今日业绩")
    private Integer clubTodayAchievement=0;

    @ApiModelProperty(value ="俱乐部今日分成")
    private Double clubTodayCommission=0d;
    /**
     * 俱乐部交易模式,0-俱乐部充值(默认),1-平台充值
     */
    @ApiModelProperty(value = "俱乐部交易模式,0-俱乐部充值(默认),1-平台充值")
    private Integer transType;
    @ApiModelProperty("开启战绩返佣:0-关闭,1-开启")
    private Integer openRebate;
    @ApiModelProperty("是否重置 1-是 0=否")
    private Integer resetTrans;
    @ApiModelProperty(value ="俱乐部人员上限数量",dataType = "int")
    private Integer upperLimit;
    @ApiModelProperty(value ="联盟id",name = "tribeId",dataType = "int")
    private Integer tribeId;
    @ApiModelProperty(value ="联盟名称",name = "tribeName",dataType = "int")
    private String tribeName;
    @ApiModelProperty(value ="俱乐部基金",name = "fund",dataType = "int")
    private Integer fund;
    @ApiModelProperty(value ="是否冻结基金 (0 否 1 是)",name = "fund",dataType = "int")
    private Short frozen;
    @ApiModelProperty(value ="占成比例",name = "percent")
    private Integer profit;
    @ApiModelProperty(value ="创建时间",name = "performance",dataType = "date")
    private LocalDateTime createTime;
    @ApiModelProperty(value ="联系方式",name = "contact",dataType = "string")
    private String contact;
    @ApiModelProperty(value =" 俱乐部的状态 0正常1是关闭",dataType = "string")
    private Short clubStatus;
    @ApiModelProperty("俱乐部今日分润（记分牌）")
    private BigDecimal clubRoomProfit;


    @ApiModelProperty(value ="俱乐部对赌返佣")
    private Integer rebateChip =0;
    /**
     * 对赌基金配置信息
     * */
    @ApiModelProperty(value = "联盟对赌基金仓", dataType = "long")
    private Long clubChip = 0L;
    @ApiModelProperty(value = "俱乐部对赌输赢")
    private Integer winClub = 0;
    @ApiModelProperty(value = "俱乐部对赌占成")
    private Integer clubRatio = 0;

    //俱乐部积分房的总服务费用
    @ApiModelProperty("俱乐部积分房的总服务费用")
    private Integer clubRoomCharge;

    //俱乐部积分房的总保险费用
    @ApiModelProperty("俱乐部积分房的总保险费用")
    private Integer clubRoomInsureTotal;

    @ApiModelProperty(value = "是否使用自定义头像 0否 1是")
    private Integer useCustom;
    @ApiModelProperty(value = "自定义头像")
    private String customUrl;
}
