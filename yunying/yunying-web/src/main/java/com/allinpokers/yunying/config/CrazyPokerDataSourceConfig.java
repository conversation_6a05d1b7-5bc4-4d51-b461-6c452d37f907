package com.allinpokers.yunying.config;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 */
@Configuration
@MapperScan(basePackages = {
        "com.allinpokers.yunying.dao.crazypoker",
        "com.allinpokers.yunying.network.dao",
        "com.allinpokers.yunying.assignment.dao",
        "com.allinpokers.yunying.permission.crazypoker.dao",
        "com.allinpokers.yunying.useronline.dao",
        "com.allinpokers.yunying.activity.dao",
        "com.allinpokers.yunying.appmessage.dao",
        "com.allinpokers.yunying.tier.dao",
        "com.allinpokers.yunying.appversion.dao",
        "com.allinpokers.yunying.broadcast.dao",
        "com.allinpokers.yunying.count.dao",
        "com.allinpokers.yunying.captcha.mapper",
        "com.allinpokers.yunying.dictionary.mapper"
},
        sqlSessionFactoryRef = "crazyPokerSqlSessionFactory")
public class CrazyPokerDataSourceConfig {

    @Bean
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.crazy-poker")
    public DataSource crazyPokerDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "crazyPokerSqlSessionFactory")
    @Primary
    public SqlSessionFactory crazyPokerSqlSessionFactory(@Qualifier("crazyPokerDataSource") DataSource datasource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(datasource);
        bean.setMapperLocations((new PathMatchingResourcePatternResolver()).getResources("classpath*:mapper/crazypoker/**/*.xml"));
        bean.getObject().getConfiguration().setMapUnderscoreToCamelCase(true);
        return bean.getObject();
    }

    @Bean(name = "crazyPokerSqlSessionTemplate")
    @Primary
    public SqlSessionTemplate crazyPokerSqlSessionTemplate(@Qualifier("crazyPokerSqlSessionFactory") SqlSessionFactory sessionFactory) {
        return new SqlSessionTemplate(sessionFactory);
    }

    @Bean(name = "crazyPokerTransactionManager")
    public PlatformTransactionManager crazyPokerTransactionManager(@Qualifier("crazyPokerDataSource") DataSource datasource) {
        return new DataSourceTransactionManager(datasource);
    }
}