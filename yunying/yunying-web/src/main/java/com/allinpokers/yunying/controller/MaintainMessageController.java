package com.allinpokers.yunying.controller;

import com.allinpokers.yunying.enu.ResponseCodeEnum;
import com.allinpokers.yunying.model.request.maintain.MaintainMessageReq;
import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.model.response.PageBean;
import com.allinpokers.yunying.model.response.maintain.MaintainMessageResp;
import com.allinpokers.yunying.permission.security.SpringSecurityUtils;
import com.allinpokers.yunying.services.MaintainMessageService;
import com.allinpokers.yunying.services.model.MaintainMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Api(tags = "维护消息内容")
@RestController
@RequestMapping("/maintain/message")
public class MaintainMessageController {

    @Resource
    private MaintainMessageService maintainMessageService;

    /**
     *
     * 维护消息列表
     *
     * @return resp
     */
    @ApiOperation(value = "维护消息列表")
    @PostMapping("/configList")
    public CommonRespon<PageBean<MaintainMessageResp>> configList(@RequestBody(required = false) MaintainMessageReq req) {
        PageBean<MaintainMessageResp> configResp = maintainMessageService.configList(req.getPage(), req.getSize());
        return CommonRespon.success(configResp);
    }

    /**
     *
     * 配置维护消息内容
     *
     * @return resp
     */
    @ApiOperation(value = "配置维护消息内容")
    @PostMapping("/config")
    public CommonRespon config(@RequestBody(required = false) MaintainMessageReq req) {
        if (req == null || req.getStartTime() == null || req.getEndTime() == null || StringUtils.isBlank(req.getExampleContent())) {
            return CommonRespon.failure(ResponseCodeEnum.PARAM_ERROR);
        }
        if (req.getEndTime().before(new Date())) {
            return CommonRespon.failure(ResponseCodeEnum.MAINTAIN_MESSAGE_CONFIG);
        }
        req.setStatus(1);
        Integer userId = SpringSecurityUtils.getUserId().intValue();
        maintainMessageService.config(userId, req.getId(), req.getStartTime(), req.getEndTime(), req.getStatus(), req.getExampleContent());
        return CommonRespon.success();
    }

    /**
     *
     * 根据id查询维护消息内容
     *
     * @return resp
     */
    @ApiOperation(value = "根据id查询维护消息内容")
    @PostMapping("/queryById")
    public CommonRespon<MaintainMessageResp> maintainMessageQueryById(@RequestBody(required = false) MaintainMessageReq req) {
        if (req == null || req.getId() == null) {
            return CommonRespon.failure(ResponseCodeEnum.PARAM_ERROR);
        }
        MaintainMessageResp maintainMessage = maintainMessageService.queryMaintainMessageById(req.getId());
        return CommonRespon.success(maintainMessage);
    }

    /**
     *
     * 更新维护消息状态
     *
     * @return resp
     */
    @ApiOperation(value = "更新维护消息状态")
    @PostMapping("/updateStatus")
    public CommonRespon maintainMessageUpdateStatus(@RequestBody(required = false) MaintainMessageReq req) {
        if (req == null || req.getId() == null || req.getStatus() == null) {
            return CommonRespon.failure(ResponseCodeEnum.PARAM_ERROR);
        }
        Integer userId = SpringSecurityUtils.getUserId().intValue();
        maintainMessageService.updateStatus(userId, req.getId(), req.getStatus());
        return CommonRespon.success();
    }

    /**
     *
     * 查询维护消息内容
     *
     * @return resp
     */
    @ApiOperation(value = "查询维护消息内容")
    @PostMapping("/query")
    public CommonRespon<MaintainMessage> maintainMessageQuery() {
        MaintainMessage maintainMessage = maintainMessageService.queryMaintainMessage();
        return CommonRespon.success(maintainMessage);
    }

}
