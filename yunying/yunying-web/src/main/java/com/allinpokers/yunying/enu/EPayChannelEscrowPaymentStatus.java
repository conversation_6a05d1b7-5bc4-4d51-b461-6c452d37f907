package com.allinpokers.yunying.enu;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum  EPayChannelEscrowPaymentStatus implements BaseEnum {
    //
    DISABLE(0, "不启用"),
    ENABLE(1, "启用");
    private final int code;
    private final String desc;

    public static EPayChannelEscrowPaymentStatus valueOf(Integer code) {
        return BaseEnum.valueOf(EPayChannelEscrowPaymentStatus.class, code);
    }
}
