package com.allinpokers.yunying.controller;

import com.allinpokers.yunying.enu.ResponseCodeEnum;
import com.allinpokers.yunying.exception.TException;
import com.allinpokers.yunying.model.response.CommonRespon;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 * controller统一异常处理
 *
 * <AUTHOR>
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(value = {RuntimeException.class})
    @ResponseBody
    public Object defaultErrorHandlerJson(HttpServletRequest req, Exception ex) {
        ResponseCodeEnum msg;
        // 自定义异常处理
        if (ex instanceof TException) {
            msg = ((TException) ex).getResponseEnum();
        } else {
            msg = ResponseCodeEnum.ERROR;
            log.error("", ex);
        }
        return CommonRespon.failure(msg);
    }
}
