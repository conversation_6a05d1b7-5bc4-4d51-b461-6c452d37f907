package com.allinpokers.yunying.weighting.api.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class CancelUserManualWeightingReq {
    @ApiModelProperty("用户隐性ID，必填")
    private Integer userId;

    @ApiModelProperty("类型,必填<br/>" +
            "有效范围：<br/>"+
            "   1=永久注销加权<br/>" +
            "   0=取消当前加权"
    )
    private Integer type;
}
