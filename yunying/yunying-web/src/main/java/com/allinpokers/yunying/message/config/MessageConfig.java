package com.allinpokers.yunying.message.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.context.MessageSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.util.StringUtils;

import java.time.Duration;

/**
 * <AUTHOR>
 */
@Configuration
public class MessageConfig {

    @Bean
    @ConfigurationProperties(prefix = "messages.osm")
    public MessageSourceProperties osmMessageSourceProperties() {
        return new MessageSourceProperties();
    }

    @Bean
    public MessageSource osmMessageSource(@Qualifier("osmMessageSourceProperties") MessageSourceProperties properties) {
        return newMessageSource(properties);
    }

    @Bean
    @ConfigurationProperties(prefix = "messages.cms")
    public MessageSourceProperties cmsMessageSourceProperties() {
        return new MessageSourceProperties();
    }

    @Bean
    public MessageSource cmsMessageSource(@Qualifier("cmsMessageSourceProperties") MessageSourceProperties properties) {
        return newMessageSource(properties);
    }

    private MessageSource newMessageSource(MessageSourceProperties properties) {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
        if (StringUtils.hasText(properties.getBasename())) {
            messageSource.setBasenames(StringUtils.commaDelimitedListToStringArray(
                    StringUtils.trimAllWhitespace(properties.getBasename())));
        }
        if (properties.getEncoding() != null) {
            messageSource.setDefaultEncoding(properties.getEncoding().name());
        }
        messageSource.setFallbackToSystemLocale(properties.isFallbackToSystemLocale());
        Duration cacheDuration = properties.getCacheDuration();
        if (cacheDuration != null) {
            messageSource.setCacheMillis(cacheDuration.toMillis());
        }
        messageSource.setAlwaysUseMessageFormat(properties.isAlwaysUseMessageFormat());
        messageSource.setUseCodeAsDefaultMessage(properties.isUseCodeAsDefaultMessage());
        return messageSource;
    }
}
