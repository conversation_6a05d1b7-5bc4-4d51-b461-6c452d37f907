package com.allinpokers.yunying.model.request.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel("同意创建联盟")
@Data
public class ApproveTribeCreateReq {
    @ApiModelProperty(value = "当前操作用户ID，由spring security提供", hidden = true)
    private String operatorId;
    @ApiModelProperty(hidden = true)
    private Integer operatorUserId;
    @ApiModelProperty(value = "消息UUID")
    private String msgId;
    @ApiModelProperty(value = "占成率，不传表示使用默认占成率，10表示10%")
    private Integer profit;
    @ApiModelProperty(value = "俱乐部上限，不传表示使用默认上限")
    private Integer clubUpperLimit;
    @ApiModelProperty("是否为主联盟，0 否 1是")
    private Short isMainTribe;
    @ApiModelProperty("主联盟ID, isMainTribe=1时该字段传空")
    private Integer mainTribeId;
    @ApiModelProperty(value = "联盟ID")
    private Integer randomId;
}
