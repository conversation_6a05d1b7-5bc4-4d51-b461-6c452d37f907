package com.allinpokers.yunying.weighting.api;

import com.allinpokers.yunying.annotation.ApiResponseEnum;
import com.allinpokers.yunying.entity.crazypoker.UserDetailsInfo;
import com.allinpokers.yunying.entity.crazypoker.UserWeighting;
import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.services.UserDetailsInfoService;
import com.allinpokers.yunying.weighting.api.req.CancelUserManualWeightingReq;
import com.allinpokers.yunying.weighting.api.req.SetUserManualWeightingReq;
import com.allinpokers.yunying.weighting.bean.UserWeightingStatusVo;
import com.allinpokers.yunying.weighting.constant.ECancelWeightingType;
import com.allinpokers.yunying.weighting.constant.EUserWeightingStatus;
import com.allinpokers.yunying.weighting.service.IUserWeightingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import java.util.*;

import static com.allinpokers.yunying.enu.ResponseCodeEnum.*;

@Slf4j
@Api(tags = "用户加权管理")
@Controller
@RequestMapping(value = "/user/weighting",
        method = RequestMethod.POST,
        produces = "application/json;charset=utf-8")
public class WeightingController {
    @Autowired
    private UserDetailsInfoService detailsService;

    @Autowired
    private IUserWeightingService weightingService;

    @ApiOperation(value = "查询用户加权状态<br/>")
    @ApiResponseEnum({
            PARAM_ERROR,
            ERROR
    })
    @ResponseBody
    @RequestMapping(value = "/{userId}",consumes = "application/json")
    public CommonRespon<UserWeightingStatusVo> getWeightingOfUser(
            @ApiParam(required=true,name="userId",value = "用户ID") @PathVariable String userId) {
        // 查询用户数据
        UserDetailsInfo userDetailsInfo = this.detailsService.findUserByUserRamdomId(userId);
        if(null == userDetailsInfo){
            return CommonRespon.failure(PARAM_ERROR);
        }

        // 查询用户的加权数据
        List<Integer> userHidLst = new ArrayList<>();
        userHidLst.add(userDetailsInfo.getUserId());
        Map<Integer,UserWeighting> weightingMap = this.weightingService.getWeightBy(userHidLst);

        UserWeighting weighting = null == weightingMap?null:weightingMap.get(userDetailsInfo.getUserId());
        int weightingStatus = EUserWeightingStatus.NotWeighting.value();
        int limitPl = -1;
        if(weighting != null){
            if(weighting.getWeightByHand()!=null && weighting.getWeightByHand())
            {
                weightingStatus = EUserWeightingStatus.ManualWeighting.value();
                limitPl = (weighting.getManualLimitProfit()==null ||
                        weighting.getManualLimitProfit()<0)?-1:
                        weighting.getManualLimitProfit();
            }else{
                weightingStatus = weighting.getWeightCode();
            }
        }

        UserWeightingStatusVo statusVo = new UserWeightingStatusVo();
        statusVo.setNickName(userDetailsInfo.getNikeName());
        statusVo.setUserId(userDetailsInfo.getRandomNum());
        statusVo.setUserHid(userDetailsInfo.getUserId());
        statusVo.setWeightingStatus(weightingStatus);
        statusVo.setLimitPl(limitPl);

        return CommonRespon.success(statusVo);
    }

    @ApiOperation(value = "设置手动加权<br/>")
    @ApiResponseEnum({
            PARAM_ERROR,
            WEIGHTING_USER_MANUAL_NOTSATISFIED,
            ERROR
    })
    @ResponseBody
    @RequestMapping(value = "/manualSet",consumes = "application/json")
    public CommonRespon<UserWeightingStatusVo> setManualWeighting(@RequestBody SetUserManualWeightingReq req) {
        // 提取登陆用户
        // int userId = SpringSecurityUtils.getUserId();

        // 参数校验
        if(null == req)
            return CommonRespon.failure(PARAM_ERROR);
        if(null == req.getUserId() || req.getUserId() <=0)
            return CommonRespon.failure(PARAM_ERROR);
        if(null == req.getDayNum() || req.getDayNum()<3 || req.getDayNum()>7)
            return CommonRespon.failure(PARAM_ERROR);

        int limitPl = -1;
        if(null != req.getLimitPl() && req.getLimitPl()>=0)
            limitPl = req.getLimitPl();
        return this.weightingService.setManualWeighting(req.getUserId(),req.getDayNum(),limitPl);
    }

    @ApiOperation(value = "取消手动加权/自动加权<br/>")
    @ApiResponseEnum({
            PARAM_ERROR,
            WEIGHTING_CANCEL_NOTSATISFIED,
            ERROR
    })
    @ResponseBody
    @RequestMapping(value = "/cancel",consumes = "application/json")
    public CommonRespon<UserWeightingStatusVo> cancelManualWeighting(@RequestBody CancelUserManualWeightingReq req) {
        // 提取登陆用户
        // int userId = SpringSecurityUtils.getUserId();

        // 参数校验
        if(null == req)
            return CommonRespon.failure(PARAM_ERROR);
        if(null == req.getUserId() || req.getUserId() <=0)
            return CommonRespon.failure(PARAM_ERROR);
        ECancelWeightingType type = ECancelWeightingType.ofValue(req.getType());
        if(null == type)
            return CommonRespon.failure(PARAM_ERROR);

        return this.weightingService.cancelManualWeighting(req.getUserId(),type);
    }
}
