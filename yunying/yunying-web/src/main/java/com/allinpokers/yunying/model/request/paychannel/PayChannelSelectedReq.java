package com.allinpokers.yunying.model.request.paychannel;

import com.allinpokers.yunying.model.request.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "已选支付渠道查询")
@Data
public class PayChannelSelectedReq extends PageReq {

    @ApiModelProperty(value = "渠道id")
    private Integer channelId;

    @ApiModelProperty(value = "搜索俱乐部id或昵称")
    private String search;
}
