package com.allinpokers.yunying.model.request.paychannel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel(value = "更新渠道信息")
@Data
public class UpdateChannelReq {

    @ApiModelProperty(value = "商户号")
    String businessAccount;

    @ApiModelProperty(value = "商户key.(如果非对称公私钥加密时，则传私钥)")
    String businessKey;

    @ApiModelProperty(value = "如果非对称公私钥加密时，公钥的值")
    String publicKey;

    @ApiModelProperty(value = "渠道id")
    Integer id;

    @ApiModelProperty(value = "第三方支付商代号。Upay")
    String paymentCode;

    @ApiModelProperty(value = "是否使用该渠道。0-否，1-是")
    Integer isUsed;

/*    @ApiModelProperty(value = "是否默认使用。0-否，1-是")
    Integer isDefault;*/

    @ApiModelProperty(value = "商户使用场景：0-充值，1-提现")
    @NotNull(message = "商户使用场景：0-充值，1-提现")
    Integer type;

    @ApiModelProperty(value = "下发模式：1-普通，2-垫资模式")
    private Integer paymentMode;

    @ApiModelProperty(value = "dkId 对应sdk列表的id,如果没有视为新增，如果有视为更新")
    Integer sdkId;

    @ApiModelProperty(value = "渠道名称")
    String name;
}
