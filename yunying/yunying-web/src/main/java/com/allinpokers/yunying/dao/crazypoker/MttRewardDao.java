package com.allinpokers.yunying.dao.crazypoker;

import com.allinpokers.yunying.dao.BaseDao;
import com.allinpokers.yunying.entity.crazypoker.MttReward;
import com.allinpokers.yunying.entity.crazypoker.example.MttRewardExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * MTT 赛事奖励表  Mapper
 * 
 * <AUTHOR>
 */
@Mapper
public interface MttRewardDao extends BaseDao<MttReward, MttRewardExample,Integer> {

    /**
     * 批量插入mtt奖励表
     * @param records
     */
    void batchInsertMttReward(@Param("records") List<MttReward> records);
}