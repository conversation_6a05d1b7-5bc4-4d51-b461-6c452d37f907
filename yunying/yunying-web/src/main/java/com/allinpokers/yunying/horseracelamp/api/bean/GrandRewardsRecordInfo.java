package com.allinpokers.yunying.horseracelamp.api.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * Description:
 * <p>
 */
@Builder
@Accessors(chain = true)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class GrandRewardsRecordInfo {

    @ApiModelProperty(value = "玩家昵称")
    private String nickName;

    @ApiModelProperty(value = "玩家id")
    private Long userId;

    @ApiModelProperty(value = "奖金级别")
    private Integer rewardsLevel;

    @ApiModelProperty(value = "金豆数量")
    private Integer kdou;

    @ApiModelProperty(value = "获奖时间")
    private String rewardsTime;

}
