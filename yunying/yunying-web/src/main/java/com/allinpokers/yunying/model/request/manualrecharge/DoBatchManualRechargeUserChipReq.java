package com.allinpokers.yunying.model.request.manualrecharge;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class DoBatchManualRechargeUserChipReq {
    @ApiModelProperty("用户显性ID集合")
    private List<String> userRandomIds;

    @ApiModelProperty("金豆类型，1可提，2不可提")
    @Min(1)
    @Max(2)
    private Integer chipType;

    @ApiModelProperty("充豆类型："
            + "<br/>1：手工收费"
            + "<br/>2：手工扣豆"
            + "<br/>3：赠送"
            + "<br/>4：系统红包"
            + "<br/>5：手工补单"
            + "<br/>6：营销-牌桌金豆红包"
            + "<br/>7：BUG赔付"
            + "<br/>8：手动提现"
            + "<br/>9：T名单"
    )
    @Min(1)
    @Max(9)
    private Integer rechargeType;

    @ApiModelProperty("充豆状态：1已收，2未收，3已付，4未付")
    @Min(1)
    @Max(4)
    private Integer rechargeStatus;

    @ApiModelProperty("补单订单号，充豆类型是手工补单")
    private String replenishmentNo;

    @ApiModelProperty("充豆的金豆数量")
    @NotNull
    private Integer rechargeChip;

    @ApiModelProperty("申请人")
    private String applicant;

    @ApiModelProperty("审批人")
    private String approver;

    @ApiModelProperty("短信验证码")
    @NotNull
    private String smsCode;

    @ApiModelProperty("备注")
    private String remark;
}
