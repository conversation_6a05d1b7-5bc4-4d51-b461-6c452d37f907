package com.allinpokers.yunying.network.api.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("网络切换-切换数据")
@Getter
@Setter
public class NetworkSwitchVo {
    /**
     * id
     */
    @ApiModelProperty("系统标识,修改必须回传，必填")
    private Integer id;

    /**
     * Http服务访问所用IP，非空则优先域名使用IP
     */
    @ApiModelProperty("Http服务访问所用IP，非空则优先域名使用IP，可选")
    private String httpIp;

    /**
     * TCP服务访问所用IP，非空则优先域名使用IP
     */
    @ApiModelProperty("TCP服务访问所用IP，非空则优先域名使用IP，可选")
    private String sckIp;

    /**
     * 资源更新或版本服务访问所用IP，非空则优先域名使用IP
     */
    @ApiModelProperty("资源更新或版本服务访问所用IP，非空则优先域名使用IP，可选")
    private String resIp;
}
