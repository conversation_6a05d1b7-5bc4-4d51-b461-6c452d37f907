package com.allinpokers.yunying.model.response.rechargeorder;

import com.allinpokers.yunying.model.response.PageBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 支持以下接口的返回：
 * 1. 俱乐部平台充值订单   user_recharge_order
 * 2. 渠道平台充值订单     user_recharge_order
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class RechargeOrderTotalInfo extends PageBean<RechargeOrderInfo> {
    @ApiModelProperty("总充值金额")
    private BigDecimal totalOrderPrice;
}
