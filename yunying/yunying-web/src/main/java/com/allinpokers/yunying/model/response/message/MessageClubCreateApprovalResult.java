package com.allinpokers.yunying.model.response.message;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 俱乐部创建同意之后的返回
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MessageClubCreateApprovalResult {

    @ApiModelProperty("俱乐部隐性ID")
    private Integer clubId;
}
