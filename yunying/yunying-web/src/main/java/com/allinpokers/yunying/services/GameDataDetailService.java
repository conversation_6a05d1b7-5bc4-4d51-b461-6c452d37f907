package com.allinpokers.yunying.services;


import com.allinpokers.yunying.model.response.PageBean;
import com.allinpokers.yunying.mongodb.model.GameDataDetail;
import com.allinpokers.yunying.services.model.GameDataDetailQuery;

import javax.servlet.http.HttpServletResponse;

/**
 * GameDataDetailService
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
public interface GameDataDetailService {

    interface RoomType {
        int TRIBE = 1;
        int CLUB = 2;
        int PERSON = 3;
    }

    /**
     * 查询
     * @param queryParam
     * @return
     */
    PageBean<GameDataDetail> findAll(GameDataDetailQuery queryParam);

    /**
     * 导出
     * @param queryParam
     * @param response
     */
    void export(GameDataDetailQuery queryParam, HttpServletResponse response);

}
