package com.allinpokers.yunying.model.request.jp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
@Data
@ApiModel(value = "个人jp 查询")
public class UserJpSearchReq {

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime start;
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime end;
    @ApiModelProperty(value = "昵称")
    private List<String> nickNames;



}
