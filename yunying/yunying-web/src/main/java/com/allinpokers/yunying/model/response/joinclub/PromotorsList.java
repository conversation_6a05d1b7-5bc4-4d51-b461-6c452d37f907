package com.allinpokers.yunying.model.response.joinclub;

import com.allinpokers.yunying.model.response.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@ApiModel("根据俱乐部id查询推广员列表")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PromotorsList {

    @ApiModelProperty("俱乐部名称")
    private String clubName;

    @ApiModelProperty("俱乐部ID")
    private Long clubId;

    @ApiModelProperty("推广员信息")
    private PageBean<PromotorInfo> promotorInfos;
}
