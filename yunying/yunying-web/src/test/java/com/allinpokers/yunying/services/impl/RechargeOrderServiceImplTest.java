package com.allinpokers.yunying.services.impl;


import com.allinpokers.yunying.dao.crazypoker.JPContributionDao;
import com.allinpokers.yunying.model.request.rechargeorder.PlayerRechargeOrderReq;
import com.allinpokers.yunying.model.request.rechargeorder.RechargeOrderReq;
import com.allinpokers.yunying.model.response.rechargeorder.RechargeOrderTotalInfo;
import com.allinpokers.yunying.model.response.rechargeorder.UserRechargeOrderTotalInfo;
import com.allinpokers.yunying.mongodb.model.JPContribution;
import com.allinpokers.yunying.services.RechargeOrderService;
import com.allinpokers.yunying.util.JsonUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.util.List;


@RunWith(SpringRunner.class)
@SpringBootTest()
public class RechargeOrderServiceImplTest {

    @Autowired
    RechargeOrderService rechargeOrderService;
    @Autowired
    private JPContributionDao jpContributionDao;

    @Test
    public void listCenterRechargeOrders() {
        RechargeOrderReq req = new RechargeOrderReq();
        req.setExternalOrderNo("xxxxxe22c9b4416a98e816");


        UserRechargeOrderTotalInfo userRechargeOrderTotalInfo = rechargeOrderService.listCenterRechargeOrders(req);
        System.out.println(userRechargeOrderTotalInfo);
    }

    @Test
    public void exportPlayerRechargeOrders() {
        PlayerRechargeOrderReq req = new PlayerRechargeOrderReq();


        RechargeOrderTotalInfo rechargeOrderTotalInfo = rechargeOrderService.listPlayerRechargeOrders(req);
        System.out.println(rechargeOrderTotalInfo);
    }
}
