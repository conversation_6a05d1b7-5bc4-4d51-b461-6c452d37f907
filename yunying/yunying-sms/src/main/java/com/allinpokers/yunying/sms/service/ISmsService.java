package com.allinpokers.yunying.sms.service;

/**
 * 短信接口(支持国内国际手机号)
 */
public interface ISmsService {

    /**
     * 验证短信验证码
     * @param sendCodeType 验证码类型 1注册 2找回密码
     * @param mobileNo 手机号码
     * @param smsCode  验证码
     * @param countryCode 手机号码的国家码
     */
    boolean checkSmsCode(int sendCodeType, String mobileNo, String smsCode, String countryCode);

    /**
     * 发送短信验证码
     * @param mobileNo 手机号码
     * @param countryCode 手机号码的国家码
     * @param sendCodeType 验证码类型 1注册 2找回密码
     * @return 0 成功 -1 失败
     */
    int sendSmsCode(String mobileNo, String countryCode, int sendCodeType);
}
