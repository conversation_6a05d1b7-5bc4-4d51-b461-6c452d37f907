package com.allinpokers.yunying.sms.service.impl;

import com.allinpokers.yunying.sms.config.ValidaateCodeProperties;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.ConnectException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;

@Slf4j
public class SmsCnUtil {


    public static boolean httpRequest(String mobile, ValidaateCodeProperties properties, String content,String template) {
        boolean result = false;
        try {

            log.info("短信发送内容: mobile: " + mobile + " template: " + properties.getTemplate() + " content: " + content);
            String requestUrl = "";
            String requestMethod = "POST";

            // 创建StringBuffer对象用来操作字符串
            StringBuffer sb = new StringBuffer(properties.getSmsUrl());
            sb.append("ac=").append("send");
            sb.append("&format=").append("json");
            // 向StringBuffer追加用户名
            sb.append("&uid=").append(properties.getUid());
            // 向StringBuffer追加密码（密码采用MD5 32位 小写）
            sb.append("&pwd=").append(properties.getPwd());
            // 向StringBuffer追加手机号码
            sb.append("&mobile=").append(mobile);
            // 向StringBuffer追加消息内容转URL标准码
            sb.append("&content=").append(URLEncoder.encode(content));
            sb.append("&encode=utf8");
            sb.append("&template=").append(template);

            requestUrl = sb.toString();

            URL url = new URL(requestUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();

            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            // 设置请求方式（GET/POST）
            conn.setRequestMethod(requestMethod);

            // 从输入流读取返回内容
            InputStream inputStream = conn.getInputStream();
            InputStreamReader inputStreamReader = new InputStreamReader(
                    inputStream, "utf-8");
            BufferedReader bufferedReader = new BufferedReader(
                    inputStreamReader);
            String str = null;
            StringBuffer buffer = new StringBuffer();
            while ((str = bufferedReader.readLine()) != null) {
                buffer.append(str);
            }

            // 释放资源
            bufferedReader.close();
            inputStreamReader.close();
            inputStream.close();
            inputStream = null;
            conn.disconnect();

//			System.out.println("result content: [" + buffer.toString() + "]");
            // {"stat":"100","message":"发送成功"}
            log.info("sms result log is {}", buffer.toString());
            if (buffer.toString().contains("100")) {
                result = true;
            }
        } catch (ConnectException ce) {
            log.error("连接超时：{}" + "短信发送内容: mobile: " + mobile + " template: " + properties.getTemplate() + " content: " + content, ce);
        } catch (Exception e) {
            log.error("https请求异常：{}" + "短信发送内容: mobile: " + mobile + " template: " + properties.getTemplate() + " content: " + content, e);
        }
        return result;
    }

    public static void main(String[] args) {
        String content = "{\"code\":\"123\"}";
        String template = "516958";

//		Map<String,Object> parameterMap = new HashMap<>();
//		parameterMap.put("code",123);
//		String s = JSON.toJSONString(parameterMap);
//		String mobile = "18616945743";
//		boolean json = httpRequest(mobile, template, s);
//		System.out.println("result: " + json);
    }
}
