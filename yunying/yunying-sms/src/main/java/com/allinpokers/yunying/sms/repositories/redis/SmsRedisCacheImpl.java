package com.allinpokers.yunying.sms.repositories.redis;

import com.allinpokers.yunying.sms.cache.ISmsCache;
import com.allinpokers.yunying.sms.constant.SmsTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class SmsRedisCacheImpl implements ISmsCache {

    /** REDIS的相关KEY都在此定义 */
    private static final String KEY_SMS_CODE_PREFIX="smsCode:%s:%s";

    //短信验证码过期时间(单位: 分钟)
    private static final int SMSCODE_EXPIRED_TIME = 5;

    private static String getSmsCodeKey(SmsTemplate smsCodeType, String mobileNo) {
        return String.format(KEY_SMS_CODE_PREFIX, smsCodeType.getType(), mobileNo);
    }


    @Lazy
    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public String getSmsCode( String mobileNo) {
        return stringRedisTemplate.opsForValue().get(mobileNo);
    }

    @Override
    public void setSmsCode( String mobileNo, String smsCode) {
        String key = mobileNo;
        stringRedisTemplate.opsForValue().set(key,smsCode,SMSCODE_EXPIRED_TIME, TimeUnit.MINUTES);
    }
}
