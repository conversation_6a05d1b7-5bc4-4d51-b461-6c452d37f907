package com.allinpokers.yunying.service.impl;

import com.allinpokers.yunying.enu.zookeeper.ZookeeperPathEnum;
import com.allinpokers.yunying.service.ZookeeperService;
import lombok.extern.log4j.Log4j2;
import org.apache.curator.framework.CuratorFramework;
import org.apache.zookeeper.CreateMode;
import org.apache.zookeeper.data.Stat;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.nio.charset.Charset;
import java.util.concurrent.atomic.AtomicInteger;


@Component
@Conditional(ZookeeperServiceCondition.class)
@Log4j2
public class ZookeeperServiceImpl implements ZookeeperService {

    @Resource
    private CuratorFramework client;

    /**
     * 字符集
     */
    private Charset charset;
    @Value("${charsetName:UTF-8}")
    private String charsetString;

    @PostConstruct
    public void initialize() {
        log.info("==>Entering the Initialization......");
        // 字符集
        if (null == this.charsetString || "".equals(this.charsetString.trim())) {
            this.charsetString = "UTF-8";
        }
        this.charset = Charset.forName(this.charsetString);
        log.info("====>Exiting the Initialization......");
    }

    /**
     * 注入jp 彩池
     *
     * @param poolId
     * @param addFund
     * @return
     */
    @Override
    public boolean importJackPool(final Integer poolId, final Long addFund, final Long initFund) {
        boolean sign = false;
        try {
            //判断节点是否存在，
            Stat stat = client.checkExists().forPath(ZookeeperPathEnum.JACK_POOL_FUND_PATH_PRE.getPath() + poolId);
            if (stat == null) {
                client.create()
                        .creatingParentContainersIfNeeded()
                        .withMode(CreateMode.PERSISTENT)
                        .forPath(ZookeeperPathEnum.JACK_POOL_FUND_PATH_PRE.getPath() + poolId, String.valueOf(initFund).getBytes());
                sign = true;
            } else {
                sign = true;
                AtomicInteger atomicInteger = new AtomicInteger(0);
                int nowVersion = 0;
                while (true) {
                    if (atomicInteger.get() > 3) {
                        log.error("注入彩池失败 poolId={} version={} 异常信息:{}", poolId, nowVersion, "尝试次数超过 3 次");
                        sign = false;
                        break;
                    }
                    atomicInteger.getAndAdd(1);
                    try {
                        stat = new Stat();
                        //获取当前节点的数据
                        String value = this.deserialize(client.getData().storingStatIn(stat).forPath(ZookeeperPathEnum.JACK_POOL_FUND_PATH_PRE.getPath() + poolId));
                        nowVersion = stat.getVersion();
                        if (!StringUtils.isEmpty(value)) {
                            long dbValue = Long.parseLong(value);
                            client.setData().withVersion(nowVersion).forPath(ZookeeperPathEnum.JACK_POOL_FUND_PATH_PRE.getPath() + poolId, this.serialize(String.valueOf(dbValue + addFund)));
                            break;
                        } else {
                            sign = false;
                            log.error("注入彩池异常 poolId={} 异常信息:{}", poolId, "节点数据data 是空");
                            break;
                        }
                    } catch (Exception ee) {
                        //更新节点数据失败，记录日志就可以了，再去尝试
                        log.error("注入彩池异常 poolId={} version={} 异常信息:{}", poolId, nowVersion, ee.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            sign = false;
            log.error("注入彩池异常 poolId={} 异常信息:{}", poolId, e);
        } finally {
            log.info("注入彩池 poolId={},addFund={},initFund={} 是否成功={} ", poolId, addFund, initFund, sign);
        }
        return sign;
    }


    private byte[] serialize(String value) {
        return value.getBytes(this.charset);
    }

    private String deserialize(byte[] data) {
        if (null == data || data.length <= 0) {
            return null;
        }

        return new String(data, this.charset);
    }
}
