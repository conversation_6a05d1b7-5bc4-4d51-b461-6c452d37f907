package com.allinpokers.yunying.service.impl;

import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

public class ZookeeperServiceCondition implements Condition {
    @Override
    public boolean matches(ConditionContext conditionContext, AnnotatedTypeMetadata annotatedTypeMetadata) {
        try {
            return "true".equalsIgnoreCase(conditionContext.getEnvironment().getProperty("zk.enable"));
        }catch (Exception e){
            return false;
        }

    }

}
