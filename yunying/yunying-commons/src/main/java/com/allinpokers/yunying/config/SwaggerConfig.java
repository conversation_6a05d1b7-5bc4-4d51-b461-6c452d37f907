package com.allinpokers.yunying.config;

import com.allinpokers.yunying.enu.ResponseCodeEnum;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.RequestMethod;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.ResponseMessage;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.ArrayList;

/**
 * 2019/3/26
 *
 * <AUTHOR>
 */
@Configuration
public class SwaggerConfig {
    /**
     * 全局的response code配置
     */
    private ResponseCodeEnum[] codeEnums = new ResponseCodeEnum[]{
            ResponseCodeEnum.SUCCESS,
            ResponseCodeEnum.ERROR,
            ResponseCodeEnum.PARAM_VALID_FAILED,
            ResponseCodeEnum.PERMISSION_ACCESS_DENIED,
            ResponseCodeEnum.DATA_EMPT,
            ResponseCodeEnum.JSON_FAIL
    };

    @Bean
    public Docket createRestApi(ApiInfo apiInfo) {
        //可以在这里使用docket.globalResponseMessage配置全局错误码，这里不使用，把全局错误码放到文档的描述中
        Docket docket = new Docket(DocumentationType.SWAGGER_2)
                .useDefaultResponseMessages(false)
                .apiInfo(apiInfo).select()
                .apis(RequestHandlerSelectors.any())
                .paths(PathSelectors.regex("(?!/error.*).*"))
                .build();
        //设置空，把默认的200，403等状态覆盖
        ArrayList<ResponseMessage> globalMessages = new ArrayList<>();
        docket.globalResponseMessage(RequestMethod.GET, globalMessages);
        docket.globalResponseMessage(RequestMethod.POST, globalMessages);
        docket.globalResponseMessage(RequestMethod.PUT, globalMessages);
        docket.globalResponseMessage(RequestMethod.DELETE, globalMessages);
        return docket;
    }

    @Bean
    public ApiInfo apiInfo() {
        //配置全局错误码到文档描述中
        StringBuilder sb = new StringBuilder();
        sb.append("<strong>全局错误码：</strong><br/>");
        for (ResponseCodeEnum codeEnum : codeEnums) {
            sb.append(codeEnum.getCode())
                    .append("：")
                    .append(codeEnum.getMsg())
                    .append("&emsp;&emsp;&emsp;&emsp;");
        }
        return new ApiInfoBuilder()
                .title("运营接口文档")
                .description(sb.toString())
                .build();
    }
}
