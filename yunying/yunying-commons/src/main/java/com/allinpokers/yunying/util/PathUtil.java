package com.allinpokers.yunying.util;

import java.nio.file.Path;
import java.nio.file.Paths;

public final class PathUtil {
    /** 应用基本信息 */
    // 工作目录
    private static Path APP_WORKSPACE_DIR= null;
    public static Path workspace(){
        return APP_WORKSPACE_DIR;
    }

    /**
     * 检测应用的工作目录
     * 检测顺序：
     * 1. 系统变量
     * 2. 环境变量
     */
    public static void detectWorkspaceDir(){
        Path result = null;

        // 1. 环境变量: -Dapp.workspace=
        String dir = System.getProperty("app.workspace");
        result = PathUtil.toDirectory(dir,false);

        // 2. 系统变量: APP_WORKSPACE
        if(null == result){
            dir = System.getenv("APP_WORKSPACE");
            result = PathUtil.toDirectory(dir,false);
        }

        // 3. 操作系统的特殊变量:user.dir
        if(null == result){
            dir = System.getenv("user.dir");
            result = PathUtil.toDirectory(dir,false);
        }

        if(null != result)
            APP_WORKSPACE_DIR = result.toAbsolutePath();
    }

    /**
     * 检查指定路径是否绝对路径
     * linux   : /开头
     * windows : /或盘符:(c:)开头
     * @param path
     * @return
     *   false  : 非绝对路径，包括参数不存在
     *   true   : 绝对路径
     */
    public static boolean isAbsolutePath(String path){
        boolean result = false;

        if(null == path || "".equals(path.trim()))
            return result;

        path = path.trim();
        if(path.startsWith("/") ||
                path.matches("^([a-z]?|[A-Z]?){1}(\\:){1}(\\d|\\D)*"))
            result = true;

        return result;
    }

    /**
     * 将指定路径转换为绝对路径
     * 如果已经是绝对路径则直接返回
     * 否则转换为baseDir下的绝对路径
     * @param path      路径字符串
     * @param baseDir   基准路径,path相对路径时有效
     * @return
     */
    public static Path toAbsolutePath(String path,Path baseDir){
        Path result = null;
        if(null == path || "".equals(path.trim()))
            return result;

        path = path.trim();
        result = toPath(path,false);
        if(null == result)
            return result;
        if(isAbsolutePath(path))
            return result;

        if(null != baseDir)
            result = baseDir.resolve(result);
        else if(null != workspace())
            result = workspace().resolve(result);

        return result;
    }

    /**
     * 将路径字符串转为为Path对象
     * @param path        路径字符串
     * @param throwEx     是否抛出异常
     * @return
     */
    public static Path toPath(String path,boolean throwEx){
        Path result = null;
        if(null == path || "".equals(path.trim()))
            return result;

        try {
            path = path.trim();
            result = Paths.get(path);
        }catch (Exception ex){
            if(throwEx)
                throw new RuntimeException("存在非法字符，转换失败："+ex.getMessage(),ex);
            result = null;
        }

        return result;
    }

    /**
     * 将路径字符串转为为Path对象
     * @param path        路径字符串
     * @param throwEx     是否抛出异常
     * @return
     */
    public static Path toDirectory(String path,boolean throwEx){
        Path result = null;
        if(null == path || "".equals(path.trim()))
            return result;

        result = toPath(path,throwEx);
        if(null != result && !result.toFile().isDirectory())
            result = null;

        return result;
    }
}
