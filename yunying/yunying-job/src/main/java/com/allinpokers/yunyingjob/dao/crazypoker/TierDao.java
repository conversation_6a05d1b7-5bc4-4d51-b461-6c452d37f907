package com.allinpokers.yunyingjob.dao.crazypoker;


import com.allinpokers.yunyingjob.entity.crazypoker.MemberPaymentActivityTier;
import com.allinpokers.yunyingjob.export.mq.bean.MemberTierQuery;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * TierDao
 *
 * <AUTHOR>
 * @since 2025/6/23
 */
@Mapper
public interface TierDao {



    @Select({
            "<script>",
            "select",
            "count(tupat.id) as count",
            "from tribe_user_payment_activity_tier tupat",
            "join tribe_payment_activity_tier tpat ON tpat.id = tupat.tpa_tier_id",
            "join club_record cr ON cr.id = tupat.club_id",
            "join user_details_info udi ON udi.USER_ID = tupat.user_id",
            "where tupat.tribe_id = #{tribeId}",
            "<if test='tpaTierId != null'>",
            "and tupat.tpa_tier_id = #{tpaTierId}",
            "</if>",
            "<if test='userRandomNums != null and userRandomNums.size > 0'>",
            "and udi.random_num in",
            "<foreach item='item' collection='userRandomNums' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='clubId != null'>",
            "and tupat.club_id = #{clubId}",
            "</if>",
            "</script>"
    })
    Long countPaymentActivityTierUser(MemberTierQuery memberRoomTierQuery);


    @Select({
            "<script>",
            "select ",
            "tupat.user_id as userId, ",
            "tupat.club_id as clubId, ",
            "tupat.tribe_id as tribeId, ",
            "tupat.tpa_tier_id as tpaTierId, ",
            "cr.random_id as clubRandomId, ",
            "cr.name as clubName, ",
            "tpat.tier_name as tierName, ",
            "udi.random_num as randomNum, ",
            "udi.nike_name as nickname ",
            "from tribe_user_payment_activity_tier tupat",
            "join tribe_payment_activity_tier tpat ON tpat.id = tupat.tpa_tier_id",
            "join club_record cr ON cr.id = tupat.club_id",
            "join user_details_info udi ON udi.USER_ID = tupat.user_id",
            "where tupat.tribe_id = #{tribeId}",
            "<if test='tpaTierId != null'>",
            "and tupat.tpa_tier_id = #{tpaTierId}",
            "</if>",
            "<if test='userRandomNums != null and userRandomNums.size > 0'>",
            "and udi.random_num in",
            "<foreach item='item' collection='userRandomNums' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='clubId != null'>",
            "and tupat.club_id = #{clubId}",
            "</if>",
            "</script>"
    })
    List<MemberPaymentActivityTier> listPaymentActivityTierUser(MemberTierQuery memberRoomTierQuery);

    @Select({
            "SELECT " ,
            "COUNT(CASE WHEN ubal.type IN (19, 11) THEN ubal.id END) AS rechargeQuantity, ",
            "IFNULL(SUM(CASE WHEN ubal.type IN (19, 11, 21) THEN ubal.balance_change END), 0) AS rechargeAmount, ",
            "COUNT(CASE WHEN ubal.type IN (15, 20) THEN ubal.id END) AS withdrawQuantity, ",
            "IFNULL(SUM(CASE WHEN ubal.type IN (15, 20, 22) THEN ubal.balance_change END), 0) AS withdrawAmount ",
            "FROM user_balance_audit_log ubal ",
            "WHERE ubal.user_id = #{userId} ",
            "AND ubal.club_id = #{clubId} ",
            "AND (ubal.tribe_id IS NULL OR ubal.tribe_id = #{tribeId}) "
    })
    MemberPaymentActivityTier countUserCombined(
            @Param("userId") Integer userId,
            @Param("clubId") Integer clubId,
            @Param("tribeId") Integer tribeId
    );


}
