package com.allinpokers.yunyingjob.entity.crazypoker;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MemberPaymentActivityTier
 *
 * <AUTHOR>
 * @since 2025/6/23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MemberPaymentActivityTier {
    private String randomNum;

    private String nickname;

    private Integer clubId;

    private Integer clubRandomId;

    private String clubName;

    private String tierName;

    private Integer tpaTierId;

    private Integer userId;

    private Integer tribeId;

    private Integer rechargeQuantity;

    private Long rechargeAmount;

    private Integer withdrawQuantity;

    private Long withdrawAmount;
}
