package com.allinpokers.yunyingjob.entity.crazypoker.example;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class RoomSearchExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table room_search
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table room_search
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table room_search
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table room_search
     *
     * @mbg.generated
     */
    public RoomSearchExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table room_search
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table room_search
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table room_search
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table room_search
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table room_search
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table room_search
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table room_search
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table room_search
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table room_search
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table room_search
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table room_search
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNull() {
            addCriterion("room_id is null");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNotNull() {
            addCriterion("room_id is not null");
            return (Criteria) this;
        }

        public Criteria andRoomIdEqualTo(Integer value) {
            addCriterion("room_id =", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotEqualTo(Integer value) {
            addCriterion("room_id <>", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThan(Integer value) {
            addCriterion("room_id >", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("room_id >=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThan(Integer value) {
            addCriterion("room_id <", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThanOrEqualTo(Integer value) {
            addCriterion("room_id <=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdIn(List<Integer> values) {
            addCriterion("room_id in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotIn(List<Integer> values) {
            addCriterion("room_id not in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdBetween(Integer value1, Integer value2) {
            addCriterion("room_id between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotBetween(Integer value1, Integer value2) {
            addCriterion("room_id not between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andLogoUrlIsNull() {
            addCriterion("logo_url is null");
            return (Criteria) this;
        }

        public Criteria andLogoUrlIsNotNull() {
            addCriterion("logo_url is not null");
            return (Criteria) this;
        }

        public Criteria andLogoUrlEqualTo(String value) {
            addCriterion("logo_url =", value, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andLogoUrlNotEqualTo(String value) {
            addCriterion("logo_url <>", value, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andLogoUrlGreaterThan(String value) {
            addCriterion("logo_url >", value, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andLogoUrlGreaterThanOrEqualTo(String value) {
            addCriterion("logo_url >=", value, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andLogoUrlLessThan(String value) {
            addCriterion("logo_url <", value, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andLogoUrlLessThanOrEqualTo(String value) {
            addCriterion("logo_url <=", value, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andLogoUrlLike(String value) {
            addCriterion("logo_url like", value, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andLogoUrlNotLike(String value) {
            addCriterion("logo_url not like", value, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andLogoUrlIn(List<String> values) {
            addCriterion("logo_url in", values, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andLogoUrlNotIn(List<String> values) {
            addCriterion("logo_url not in", values, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andLogoUrlBetween(String value1, String value2) {
            addCriterion("logo_url between", value1, value2, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andLogoUrlNotBetween(String value1, String value2) {
            addCriterion("logo_url not between", value1, value2, "logoUrl");
            return (Criteria) this;
        }

        public Criteria andRoomPathIsNull() {
            addCriterion("room_path is null");
            return (Criteria) this;
        }

        public Criteria andRoomPathIsNotNull() {
            addCriterion("room_path is not null");
            return (Criteria) this;
        }

        public Criteria andRoomPathEqualTo(Integer value) {
            addCriterion("room_path =", value, "roomPath");
            return (Criteria) this;
        }

        public Criteria andRoomPathNotEqualTo(Integer value) {
            addCriterion("room_path <>", value, "roomPath");
            return (Criteria) this;
        }

        public Criteria andRoomPathGreaterThan(Integer value) {
            addCriterion("room_path >", value, "roomPath");
            return (Criteria) this;
        }

        public Criteria andRoomPathGreaterThanOrEqualTo(Integer value) {
            addCriterion("room_path >=", value, "roomPath");
            return (Criteria) this;
        }

        public Criteria andRoomPathLessThan(Integer value) {
            addCriterion("room_path <", value, "roomPath");
            return (Criteria) this;
        }

        public Criteria andRoomPathLessThanOrEqualTo(Integer value) {
            addCriterion("room_path <=", value, "roomPath");
            return (Criteria) this;
        }

        public Criteria andRoomPathIn(List<Integer> values) {
            addCriterion("room_path in", values, "roomPath");
            return (Criteria) this;
        }

        public Criteria andRoomPathNotIn(List<Integer> values) {
            addCriterion("room_path not in", values, "roomPath");
            return (Criteria) this;
        }

        public Criteria andRoomPathBetween(Integer value1, Integer value2) {
            addCriterion("room_path between", value1, value2, "roomPath");
            return (Criteria) this;
        }

        public Criteria andRoomPathNotBetween(Integer value1, Integer value2) {
            addCriterion("room_path not between", value1, value2, "roomPath");
            return (Criteria) this;
        }

        public Criteria andSbChipIsNull() {
            addCriterion("sb_chip is null");
            return (Criteria) this;
        }

        public Criteria andSbChipIsNotNull() {
            addCriterion("sb_chip is not null");
            return (Criteria) this;
        }

        public Criteria andSbChipEqualTo(Integer value) {
            addCriterion("sb_chip =", value, "sbChip");
            return (Criteria) this;
        }

        public Criteria andSbChipNotEqualTo(Integer value) {
            addCriterion("sb_chip <>", value, "sbChip");
            return (Criteria) this;
        }

        public Criteria andSbChipGreaterThan(Integer value) {
            addCriterion("sb_chip >", value, "sbChip");
            return (Criteria) this;
        }

        public Criteria andSbChipGreaterThanOrEqualTo(Integer value) {
            addCriterion("sb_chip >=", value, "sbChip");
            return (Criteria) this;
        }

        public Criteria andSbChipLessThan(Integer value) {
            addCriterion("sb_chip <", value, "sbChip");
            return (Criteria) this;
        }

        public Criteria andSbChipLessThanOrEqualTo(Integer value) {
            addCriterion("sb_chip <=", value, "sbChip");
            return (Criteria) this;
        }

        public Criteria andSbChipIn(List<Integer> values) {
            addCriterion("sb_chip in", values, "sbChip");
            return (Criteria) this;
        }

        public Criteria andSbChipNotIn(List<Integer> values) {
            addCriterion("sb_chip not in", values, "sbChip");
            return (Criteria) this;
        }

        public Criteria andSbChipBetween(Integer value1, Integer value2) {
            addCriterion("sb_chip between", value1, value2, "sbChip");
            return (Criteria) this;
        }

        public Criteria andSbChipNotBetween(Integer value1, Integer value2) {
            addCriterion("sb_chip not between", value1, value2, "sbChip");
            return (Criteria) this;
        }

        public Criteria andQianzhuIsNull() {
            addCriterion("qianzhu is null");
            return (Criteria) this;
        }

        public Criteria andQianzhuIsNotNull() {
            addCriterion("qianzhu is not null");
            return (Criteria) this;
        }

        public Criteria andQianzhuEqualTo(Integer value) {
            addCriterion("qianzhu =", value, "qianzhu");
            return (Criteria) this;
        }

        public Criteria andQianzhuNotEqualTo(Integer value) {
            addCriterion("qianzhu <>", value, "qianzhu");
            return (Criteria) this;
        }

        public Criteria andQianzhuGreaterThan(Integer value) {
            addCriterion("qianzhu >", value, "qianzhu");
            return (Criteria) this;
        }

        public Criteria andQianzhuGreaterThanOrEqualTo(Integer value) {
            addCriterion("qianzhu >=", value, "qianzhu");
            return (Criteria) this;
        }

        public Criteria andQianzhuLessThan(Integer value) {
            addCriterion("qianzhu <", value, "qianzhu");
            return (Criteria) this;
        }

        public Criteria andQianzhuLessThanOrEqualTo(Integer value) {
            addCriterion("qianzhu <=", value, "qianzhu");
            return (Criteria) this;
        }

        public Criteria andQianzhuIn(List<Integer> values) {
            addCriterion("qianzhu in", values, "qianzhu");
            return (Criteria) this;
        }

        public Criteria andQianzhuNotIn(List<Integer> values) {
            addCriterion("qianzhu not in", values, "qianzhu");
            return (Criteria) this;
        }

        public Criteria andQianzhuBetween(Integer value1, Integer value2) {
            addCriterion("qianzhu between", value1, value2, "qianzhu");
            return (Criteria) this;
        }

        public Criteria andQianzhuNotBetween(Integer value1, Integer value2) {
            addCriterion("qianzhu not between", value1, value2, "qianzhu");
            return (Criteria) this;
        }

        public Criteria andInsuranceOnIsNull() {
            addCriterion("insurance_on is null");
            return (Criteria) this;
        }

        public Criteria andInsuranceOnIsNotNull() {
            addCriterion("insurance_on is not null");
            return (Criteria) this;
        }

        public Criteria andInsuranceOnEqualTo(Byte value) {
            addCriterion("insurance_on =", value, "insuranceOn");
            return (Criteria) this;
        }

        public Criteria andInsuranceOnNotEqualTo(Byte value) {
            addCriterion("insurance_on <>", value, "insuranceOn");
            return (Criteria) this;
        }

        public Criteria andInsuranceOnGreaterThan(Byte value) {
            addCriterion("insurance_on >", value, "insuranceOn");
            return (Criteria) this;
        }

        public Criteria andInsuranceOnGreaterThanOrEqualTo(Byte value) {
            addCriterion("insurance_on >=", value, "insuranceOn");
            return (Criteria) this;
        }

        public Criteria andInsuranceOnLessThan(Byte value) {
            addCriterion("insurance_on <", value, "insuranceOn");
            return (Criteria) this;
        }

        public Criteria andInsuranceOnLessThanOrEqualTo(Byte value) {
            addCriterion("insurance_on <=", value, "insuranceOn");
            return (Criteria) this;
        }

        public Criteria andInsuranceOnIn(List<Byte> values) {
            addCriterion("insurance_on in", values, "insuranceOn");
            return (Criteria) this;
        }

        public Criteria andInsuranceOnNotIn(List<Byte> values) {
            addCriterion("insurance_on not in", values, "insuranceOn");
            return (Criteria) this;
        }

        public Criteria andInsuranceOnBetween(Byte value1, Byte value2) {
            addCriterion("insurance_on between", value1, value2, "insuranceOn");
            return (Criteria) this;
        }

        public Criteria andInsuranceOnNotBetween(Byte value1, Byte value2) {
            addCriterion("insurance_on not between", value1, value2, "insuranceOn");
            return (Criteria) this;
        }

        public Criteria andJackpotOnIsNull() {
            addCriterion("jackpot_on is null");
            return (Criteria) this;
        }

        public Criteria andJackpotOnIsNotNull() {
            addCriterion("jackpot_on is not null");
            return (Criteria) this;
        }

        public Criteria andJackpotOnEqualTo(Byte value) {
            addCriterion("jackpot_on =", value, "jackpotOn");
            return (Criteria) this;
        }

        public Criteria andJackpotOnNotEqualTo(Byte value) {
            addCriterion("jackpot_on <>", value, "jackpotOn");
            return (Criteria) this;
        }

        public Criteria andJackpotOnGreaterThan(Byte value) {
            addCriterion("jackpot_on >", value, "jackpotOn");
            return (Criteria) this;
        }

        public Criteria andJackpotOnGreaterThanOrEqualTo(Byte value) {
            addCriterion("jackpot_on >=", value, "jackpotOn");
            return (Criteria) this;
        }

        public Criteria andJackpotOnLessThan(Byte value) {
            addCriterion("jackpot_on <", value, "jackpotOn");
            return (Criteria) this;
        }

        public Criteria andJackpotOnLessThanOrEqualTo(Byte value) {
            addCriterion("jackpot_on <=", value, "jackpotOn");
            return (Criteria) this;
        }

        public Criteria andJackpotOnIn(List<Byte> values) {
            addCriterion("jackpot_on in", values, "jackpotOn");
            return (Criteria) this;
        }

        public Criteria andJackpotOnNotIn(List<Byte> values) {
            addCriterion("jackpot_on not in", values, "jackpotOn");
            return (Criteria) this;
        }

        public Criteria andJackpotOnBetween(Byte value1, Byte value2) {
            addCriterion("jackpot_on between", value1, value2, "jackpotOn");
            return (Criteria) this;
        }

        public Criteria andJackpotOnNotBetween(Byte value1, Byte value2) {
            addCriterion("jackpot_on not between", value1, value2, "jackpotOn");
            return (Criteria) this;
        }

        public Criteria andOmahaModeIsNull() {
            addCriterion("omaha_mode is null");
            return (Criteria) this;
        }

        public Criteria andOmahaModeIsNotNull() {
            addCriterion("omaha_mode is not null");
            return (Criteria) this;
        }

        public Criteria andOmahaModeEqualTo(Integer value) {
            addCriterion("omaha_mode =", value, "omahaMode");
            return (Criteria) this;
        }

        public Criteria andOmahaModeNotEqualTo(Integer value) {
            addCriterion("omaha_mode <>", value, "omahaMode");
            return (Criteria) this;
        }

        public Criteria andOmahaModeGreaterThan(Integer value) {
            addCriterion("omaha_mode >", value, "omahaMode");
            return (Criteria) this;
        }

        public Criteria andOmahaModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("omaha_mode >=", value, "omahaMode");
            return (Criteria) this;
        }

        public Criteria andOmahaModeLessThan(Integer value) {
            addCriterion("omaha_mode <", value, "omahaMode");
            return (Criteria) this;
        }

        public Criteria andOmahaModeLessThanOrEqualTo(Integer value) {
            addCriterion("omaha_mode <=", value, "omahaMode");
            return (Criteria) this;
        }

        public Criteria andOmahaModeIn(List<Integer> values) {
            addCriterion("omaha_mode in", values, "omahaMode");
            return (Criteria) this;
        }

        public Criteria andOmahaModeNotIn(List<Integer> values) {
            addCriterion("omaha_mode not in", values, "omahaMode");
            return (Criteria) this;
        }

        public Criteria andOmahaModeBetween(Integer value1, Integer value2) {
            addCriterion("omaha_mode between", value1, value2, "omahaMode");
            return (Criteria) this;
        }

        public Criteria andOmahaModeNotBetween(Integer value1, Integer value2) {
            addCriterion("omaha_mode not between", value1, value2, "omahaMode");
            return (Criteria) this;
        }

        public Criteria andBpModeIsNull() {
            addCriterion("bp_mode is null");
            return (Criteria) this;
        }

        public Criteria andBpModeIsNotNull() {
            addCriterion("bp_mode is not null");
            return (Criteria) this;
        }

        public Criteria andBpModeEqualTo(Integer value) {
            addCriterion("bp_mode =", value, "bpMode");
            return (Criteria) this;
        }

        public Criteria andBpModeNotEqualTo(Integer value) {
            addCriterion("bp_mode <>", value, "bpMode");
            return (Criteria) this;
        }

        public Criteria andBpModeGreaterThan(Integer value) {
            addCriterion("bp_mode >", value, "bpMode");
            return (Criteria) this;
        }

        public Criteria andBpModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("bp_mode >=", value, "bpMode");
            return (Criteria) this;
        }

        public Criteria andBpModeLessThan(Integer value) {
            addCriterion("bp_mode <", value, "bpMode");
            return (Criteria) this;
        }

        public Criteria andBpModeLessThanOrEqualTo(Integer value) {
            addCriterion("bp_mode <=", value, "bpMode");
            return (Criteria) this;
        }

        public Criteria andBpModeIn(List<Integer> values) {
            addCriterion("bp_mode in", values, "bpMode");
            return (Criteria) this;
        }

        public Criteria andBpModeNotIn(List<Integer> values) {
            addCriterion("bp_mode not in", values, "bpMode");
            return (Criteria) this;
        }

        public Criteria andBpModeBetween(Integer value1, Integer value2) {
            addCriterion("bp_mode between", value1, value2, "bpMode");
            return (Criteria) this;
        }

        public Criteria andBpModeNotBetween(Integer value1, Integer value2) {
            addCriterion("bp_mode not between", value1, value2, "bpMode");
            return (Criteria) this;
        }

        public Criteria andBpJokerOnIsNull() {
            addCriterion("bp_joker_on is null");
            return (Criteria) this;
        }

        public Criteria andBpJokerOnIsNotNull() {
            addCriterion("bp_joker_on is not null");
            return (Criteria) this;
        }

        public Criteria andBpJokerOnEqualTo(Integer value) {
            addCriterion("bp_joker_on =", value, "bpJokerOn");
            return (Criteria) this;
        }

        public Criteria andBpJokerOnNotEqualTo(Integer value) {
            addCriterion("bp_joker_on <>", value, "bpJokerOn");
            return (Criteria) this;
        }

        public Criteria andBpJokerOnGreaterThan(Integer value) {
            addCriterion("bp_joker_on >", value, "bpJokerOn");
            return (Criteria) this;
        }

        public Criteria andBpJokerOnGreaterThanOrEqualTo(Integer value) {
            addCriterion("bp_joker_on >=", value, "bpJokerOn");
            return (Criteria) this;
        }

        public Criteria andBpJokerOnLessThan(Integer value) {
            addCriterion("bp_joker_on <", value, "bpJokerOn");
            return (Criteria) this;
        }

        public Criteria andBpJokerOnLessThanOrEqualTo(Integer value) {
            addCriterion("bp_joker_on <=", value, "bpJokerOn");
            return (Criteria) this;
        }

        public Criteria andBpJokerOnIn(List<Integer> values) {
            addCriterion("bp_joker_on in", values, "bpJokerOn");
            return (Criteria) this;
        }

        public Criteria andBpJokerOnNotIn(List<Integer> values) {
            addCriterion("bp_joker_on not in", values, "bpJokerOn");
            return (Criteria) this;
        }

        public Criteria andBpJokerOnBetween(Integer value1, Integer value2) {
            addCriterion("bp_joker_on between", value1, value2, "bpJokerOn");
            return (Criteria) this;
        }

        public Criteria andBpJokerOnNotBetween(Integer value1, Integer value2) {
            addCriterion("bp_joker_on not between", value1, value2, "bpJokerOn");
            return (Criteria) this;
        }

        public Criteria andBpDealModeIsNull() {
            addCriterion("bp_deal_mode is null");
            return (Criteria) this;
        }

        public Criteria andBpDealModeIsNotNull() {
            addCriterion("bp_deal_mode is not null");
            return (Criteria) this;
        }

        public Criteria andBpDealModeEqualTo(Integer value) {
            addCriterion("bp_deal_mode =", value, "bpDealMode");
            return (Criteria) this;
        }

        public Criteria andBpDealModeNotEqualTo(Integer value) {
            addCriterion("bp_deal_mode <>", value, "bpDealMode");
            return (Criteria) this;
        }

        public Criteria andBpDealModeGreaterThan(Integer value) {
            addCriterion("bp_deal_mode >", value, "bpDealMode");
            return (Criteria) this;
        }

        public Criteria andBpDealModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("bp_deal_mode >=", value, "bpDealMode");
            return (Criteria) this;
        }

        public Criteria andBpDealModeLessThan(Integer value) {
            addCriterion("bp_deal_mode <", value, "bpDealMode");
            return (Criteria) this;
        }

        public Criteria andBpDealModeLessThanOrEqualTo(Integer value) {
            addCriterion("bp_deal_mode <=", value, "bpDealMode");
            return (Criteria) this;
        }

        public Criteria andBpDealModeIn(List<Integer> values) {
            addCriterion("bp_deal_mode in", values, "bpDealMode");
            return (Criteria) this;
        }

        public Criteria andBpDealModeNotIn(List<Integer> values) {
            addCriterion("bp_deal_mode not in", values, "bpDealMode");
            return (Criteria) this;
        }

        public Criteria andBpDealModeBetween(Integer value1, Integer value2) {
            addCriterion("bp_deal_mode between", value1, value2, "bpDealMode");
            return (Criteria) this;
        }

        public Criteria andBpDealModeNotBetween(Integer value1, Integer value2) {
            addCriterion("bp_deal_mode not between", value1, value2, "bpDealMode");
            return (Criteria) this;
        }

        public Criteria andMttHunterOnIsNull() {
            addCriterion("mtt_hunter_on is null");
            return (Criteria) this;
        }

        public Criteria andMttHunterOnIsNotNull() {
            addCriterion("mtt_hunter_on is not null");
            return (Criteria) this;
        }

        public Criteria andMttHunterOnEqualTo(Integer value) {
            addCriterion("mtt_hunter_on =", value, "mttHunterOn");
            return (Criteria) this;
        }

        public Criteria andMttHunterOnNotEqualTo(Integer value) {
            addCriterion("mtt_hunter_on <>", value, "mttHunterOn");
            return (Criteria) this;
        }

        public Criteria andMttHunterOnGreaterThan(Integer value) {
            addCriterion("mtt_hunter_on >", value, "mttHunterOn");
            return (Criteria) this;
        }

        public Criteria andMttHunterOnGreaterThanOrEqualTo(Integer value) {
            addCriterion("mtt_hunter_on >=", value, "mttHunterOn");
            return (Criteria) this;
        }

        public Criteria andMttHunterOnLessThan(Integer value) {
            addCriterion("mtt_hunter_on <", value, "mttHunterOn");
            return (Criteria) this;
        }

        public Criteria andMttHunterOnLessThanOrEqualTo(Integer value) {
            addCriterion("mtt_hunter_on <=", value, "mttHunterOn");
            return (Criteria) this;
        }

        public Criteria andMttHunterOnIn(List<Integer> values) {
            addCriterion("mtt_hunter_on in", values, "mttHunterOn");
            return (Criteria) this;
        }

        public Criteria andMttHunterOnNotIn(List<Integer> values) {
            addCriterion("mtt_hunter_on not in", values, "mttHunterOn");
            return (Criteria) this;
        }

        public Criteria andMttHunterOnBetween(Integer value1, Integer value2) {
            addCriterion("mtt_hunter_on between", value1, value2, "mttHunterOn");
            return (Criteria) this;
        }

        public Criteria andMttHunterOnNotBetween(Integer value1, Integer value2) {
            addCriterion("mtt_hunter_on not between", value1, value2, "mttHunterOn");
            return (Criteria) this;
        }

        public Criteria andPlayerCountIsNull() {
            addCriterion("player_count is null");
            return (Criteria) this;
        }

        public Criteria andPlayerCountIsNotNull() {
            addCriterion("player_count is not null");
            return (Criteria) this;
        }

        public Criteria andPlayerCountEqualTo(Integer value) {
            addCriterion("player_count =", value, "playerCount");
            return (Criteria) this;
        }

        public Criteria andPlayerCountNotEqualTo(Integer value) {
            addCriterion("player_count <>", value, "playerCount");
            return (Criteria) this;
        }

        public Criteria andPlayerCountGreaterThan(Integer value) {
            addCriterion("player_count >", value, "playerCount");
            return (Criteria) this;
        }

        public Criteria andPlayerCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("player_count >=", value, "playerCount");
            return (Criteria) this;
        }

        public Criteria andPlayerCountLessThan(Integer value) {
            addCriterion("player_count <", value, "playerCount");
            return (Criteria) this;
        }

        public Criteria andPlayerCountLessThanOrEqualTo(Integer value) {
            addCriterion("player_count <=", value, "playerCount");
            return (Criteria) this;
        }

        public Criteria andPlayerCountIn(List<Integer> values) {
            addCriterion("player_count in", values, "playerCount");
            return (Criteria) this;
        }

        public Criteria andPlayerCountNotIn(List<Integer> values) {
            addCriterion("player_count not in", values, "playerCount");
            return (Criteria) this;
        }

        public Criteria andPlayerCountBetween(Integer value1, Integer value2) {
            addCriterion("player_count between", value1, value2, "playerCount");
            return (Criteria) this;
        }

        public Criteria andPlayerCountNotBetween(Integer value1, Integer value2) {
            addCriterion("player_count not between", value1, value2, "playerCount");
            return (Criteria) this;
        }

        public Criteria andOpenTimeIsNull() {
            addCriterion("open_time is null");
            return (Criteria) this;
        }

        public Criteria andOpenTimeIsNotNull() {
            addCriterion("open_time is not null");
            return (Criteria) this;
        }

        public Criteria andOpenTimeEqualTo(Integer value) {
            addCriterion("open_time =", value, "openTime");
            return (Criteria) this;
        }

        public Criteria andOpenTimeNotEqualTo(Integer value) {
            addCriterion("open_time <>", value, "openTime");
            return (Criteria) this;
        }

        public Criteria andOpenTimeGreaterThan(Integer value) {
            addCriterion("open_time >", value, "openTime");
            return (Criteria) this;
        }

        public Criteria andOpenTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("open_time >=", value, "openTime");
            return (Criteria) this;
        }

        public Criteria andOpenTimeLessThan(Integer value) {
            addCriterion("open_time <", value, "openTime");
            return (Criteria) this;
        }

        public Criteria andOpenTimeLessThanOrEqualTo(Integer value) {
            addCriterion("open_time <=", value, "openTime");
            return (Criteria) this;
        }

        public Criteria andOpenTimeIn(List<Integer> values) {
            addCriterion("open_time in", values, "openTime");
            return (Criteria) this;
        }

        public Criteria andOpenTimeNotIn(List<Integer> values) {
            addCriterion("open_time not in", values, "openTime");
            return (Criteria) this;
        }

        public Criteria andOpenTimeBetween(Integer value1, Integer value2) {
            addCriterion("open_time between", value1, value2, "openTime");
            return (Criteria) this;
        }

        public Criteria andOpenTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("open_time not between", value1, value2, "openTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(LocalDateTime value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(LocalDateTime value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(LocalDateTime value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(LocalDateTime value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<LocalDateTime> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<LocalDateTime> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andEmptySeatIsNull() {
            addCriterion("empty_seat is null");
            return (Criteria) this;
        }

        public Criteria andEmptySeatIsNotNull() {
            addCriterion("empty_seat is not null");
            return (Criteria) this;
        }

        public Criteria andEmptySeatEqualTo(Integer value) {
            addCriterion("empty_seat =", value, "emptySeat");
            return (Criteria) this;
        }

        public Criteria andEmptySeatNotEqualTo(Integer value) {
            addCriterion("empty_seat <>", value, "emptySeat");
            return (Criteria) this;
        }

        public Criteria andEmptySeatGreaterThan(Integer value) {
            addCriterion("empty_seat >", value, "emptySeat");
            return (Criteria) this;
        }

        public Criteria andEmptySeatGreaterThanOrEqualTo(Integer value) {
            addCriterion("empty_seat >=", value, "emptySeat");
            return (Criteria) this;
        }

        public Criteria andEmptySeatLessThan(Integer value) {
            addCriterion("empty_seat <", value, "emptySeat");
            return (Criteria) this;
        }

        public Criteria andEmptySeatLessThanOrEqualTo(Integer value) {
            addCriterion("empty_seat <=", value, "emptySeat");
            return (Criteria) this;
        }

        public Criteria andEmptySeatIn(List<Integer> values) {
            addCriterion("empty_seat in", values, "emptySeat");
            return (Criteria) this;
        }

        public Criteria andEmptySeatNotIn(List<Integer> values) {
            addCriterion("empty_seat not in", values, "emptySeat");
            return (Criteria) this;
        }

        public Criteria andEmptySeatBetween(Integer value1, Integer value2) {
            addCriterion("empty_seat between", value1, value2, "emptySeat");
            return (Criteria) this;
        }

        public Criteria andEmptySeatNotBetween(Integer value1, Integer value2) {
            addCriterion("empty_seat not between", value1, value2, "emptySeat");
            return (Criteria) this;
        }

        public Criteria andGameMaxTimeIsNull() {
            addCriterion("game_max_time is null");
            return (Criteria) this;
        }

        public Criteria andGameMaxTimeIsNotNull() {
            addCriterion("game_max_time is not null");
            return (Criteria) this;
        }

        public Criteria andGameMaxTimeEqualTo(Integer value) {
            addCriterion("game_max_time =", value, "gameMaxTime");
            return (Criteria) this;
        }

        public Criteria andGameMaxTimeNotEqualTo(Integer value) {
            addCriterion("game_max_time <>", value, "gameMaxTime");
            return (Criteria) this;
        }

        public Criteria andGameMaxTimeGreaterThan(Integer value) {
            addCriterion("game_max_time >", value, "gameMaxTime");
            return (Criteria) this;
        }

        public Criteria andGameMaxTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("game_max_time >=", value, "gameMaxTime");
            return (Criteria) this;
        }

        public Criteria andGameMaxTimeLessThan(Integer value) {
            addCriterion("game_max_time <", value, "gameMaxTime");
            return (Criteria) this;
        }

        public Criteria andGameMaxTimeLessThanOrEqualTo(Integer value) {
            addCriterion("game_max_time <=", value, "gameMaxTime");
            return (Criteria) this;
        }

        public Criteria andGameMaxTimeIn(List<Integer> values) {
            addCriterion("game_max_time in", values, "gameMaxTime");
            return (Criteria) this;
        }

        public Criteria andGameMaxTimeNotIn(List<Integer> values) {
            addCriterion("game_max_time not in", values, "gameMaxTime");
            return (Criteria) this;
        }

        public Criteria andGameMaxTimeBetween(Integer value1, Integer value2) {
            addCriterion("game_max_time between", value1, value2, "gameMaxTime");
            return (Criteria) this;
        }

        public Criteria andGameMaxTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("game_max_time not between", value1, value2, "gameMaxTime");
            return (Criteria) this;
        }

        public Criteria andDelaySecIsNull() {
            addCriterion("delay_sec is null");
            return (Criteria) this;
        }

        public Criteria andDelaySecIsNotNull() {
            addCriterion("delay_sec is not null");
            return (Criteria) this;
        }

        public Criteria andDelaySecEqualTo(Integer value) {
            addCriterion("delay_sec =", value, "delaySec");
            return (Criteria) this;
        }

        public Criteria andDelaySecNotEqualTo(Integer value) {
            addCriterion("delay_sec <>", value, "delaySec");
            return (Criteria) this;
        }

        public Criteria andDelaySecGreaterThan(Integer value) {
            addCriterion("delay_sec >", value, "delaySec");
            return (Criteria) this;
        }

        public Criteria andDelaySecGreaterThanOrEqualTo(Integer value) {
            addCriterion("delay_sec >=", value, "delaySec");
            return (Criteria) this;
        }

        public Criteria andDelaySecLessThan(Integer value) {
            addCriterion("delay_sec <", value, "delaySec");
            return (Criteria) this;
        }

        public Criteria andDelaySecLessThanOrEqualTo(Integer value) {
            addCriterion("delay_sec <=", value, "delaySec");
            return (Criteria) this;
        }

        public Criteria andDelaySecIn(List<Integer> values) {
            addCriterion("delay_sec in", values, "delaySec");
            return (Criteria) this;
        }

        public Criteria andDelaySecNotIn(List<Integer> values) {
            addCriterion("delay_sec not in", values, "delaySec");
            return (Criteria) this;
        }

        public Criteria andDelaySecBetween(Integer value1, Integer value2) {
            addCriterion("delay_sec between", value1, value2, "delaySec");
            return (Criteria) this;
        }

        public Criteria andDelaySecNotBetween(Integer value1, Integer value2) {
            addCriterion("delay_sec not between", value1, value2, "delaySec");
            return (Criteria) this;
        }

        public Criteria andServerIdIsNull() {
            addCriterion("server_id is null");
            return (Criteria) this;
        }

        public Criteria andServerIdIsNotNull() {
            addCriterion("server_id is not null");
            return (Criteria) this;
        }

        public Criteria andServerIdEqualTo(String value) {
            addCriterion("server_id =", value, "serverId");
            return (Criteria) this;
        }

        public Criteria andServerIdNotEqualTo(String value) {
            addCriterion("server_id <>", value, "serverId");
            return (Criteria) this;
        }

        public Criteria andServerIdGreaterThan(String value) {
            addCriterion("server_id >", value, "serverId");
            return (Criteria) this;
        }

        public Criteria andServerIdGreaterThanOrEqualTo(String value) {
            addCriterion("server_id >=", value, "serverId");
            return (Criteria) this;
        }

        public Criteria andServerIdLessThan(String value) {
            addCriterion("server_id <", value, "serverId");
            return (Criteria) this;
        }

        public Criteria andServerIdLessThanOrEqualTo(String value) {
            addCriterion("server_id <=", value, "serverId");
            return (Criteria) this;
        }

        public Criteria andServerIdLike(String value) {
            addCriterion("server_id like", value, "serverId");
            return (Criteria) this;
        }

        public Criteria andServerIdNotLike(String value) {
            addCriterion("server_id not like", value, "serverId");
            return (Criteria) this;
        }

        public Criteria andServerIdIn(List<String> values) {
            addCriterion("server_id in", values, "serverId");
            return (Criteria) this;
        }

        public Criteria andServerIdNotIn(List<String> values) {
            addCriterion("server_id not in", values, "serverId");
            return (Criteria) this;
        }

        public Criteria andServerIdBetween(String value1, String value2) {
            addCriterion("server_id between", value1, value2, "serverId");
            return (Criteria) this;
        }

        public Criteria andServerIdNotBetween(String value1, String value2) {
            addCriterion("server_id not between", value1, value2, "serverId");
            return (Criteria) this;
        }

        public Criteria andAccessIpIsNull() {
            addCriterion("access_ip is null");
            return (Criteria) this;
        }

        public Criteria andAccessIpIsNotNull() {
            addCriterion("access_ip is not null");
            return (Criteria) this;
        }

        public Criteria andAccessIpEqualTo(String value) {
            addCriterion("access_ip =", value, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessIpNotEqualTo(String value) {
            addCriterion("access_ip <>", value, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessIpGreaterThan(String value) {
            addCriterion("access_ip >", value, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessIpGreaterThanOrEqualTo(String value) {
            addCriterion("access_ip >=", value, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessIpLessThan(String value) {
            addCriterion("access_ip <", value, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessIpLessThanOrEqualTo(String value) {
            addCriterion("access_ip <=", value, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessIpLike(String value) {
            addCriterion("access_ip like", value, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessIpNotLike(String value) {
            addCriterion("access_ip not like", value, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessIpIn(List<String> values) {
            addCriterion("access_ip in", values, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessIpNotIn(List<String> values) {
            addCriterion("access_ip not in", values, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessIpBetween(String value1, String value2) {
            addCriterion("access_ip between", value1, value2, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessIpNotBetween(String value1, String value2) {
            addCriterion("access_ip not between", value1, value2, "accessIp");
            return (Criteria) this;
        }

        public Criteria andAccessPortIsNull() {
            addCriterion("access_port is null");
            return (Criteria) this;
        }

        public Criteria andAccessPortIsNotNull() {
            addCriterion("access_port is not null");
            return (Criteria) this;
        }

        public Criteria andAccessPortEqualTo(Integer value) {
            addCriterion("access_port =", value, "accessPort");
            return (Criteria) this;
        }

        public Criteria andAccessPortNotEqualTo(Integer value) {
            addCriterion("access_port <>", value, "accessPort");
            return (Criteria) this;
        }

        public Criteria andAccessPortGreaterThan(Integer value) {
            addCriterion("access_port >", value, "accessPort");
            return (Criteria) this;
        }

        public Criteria andAccessPortGreaterThanOrEqualTo(Integer value) {
            addCriterion("access_port >=", value, "accessPort");
            return (Criteria) this;
        }

        public Criteria andAccessPortLessThan(Integer value) {
            addCriterion("access_port <", value, "accessPort");
            return (Criteria) this;
        }

        public Criteria andAccessPortLessThanOrEqualTo(Integer value) {
            addCriterion("access_port <=", value, "accessPort");
            return (Criteria) this;
        }

        public Criteria andAccessPortIn(List<Integer> values) {
            addCriterion("access_port in", values, "accessPort");
            return (Criteria) this;
        }

        public Criteria andAccessPortNotIn(List<Integer> values) {
            addCriterion("access_port not in", values, "accessPort");
            return (Criteria) this;
        }

        public Criteria andAccessPortBetween(Integer value1, Integer value2) {
            addCriterion("access_port between", value1, value2, "accessPort");
            return (Criteria) this;
        }

        public Criteria andAccessPortNotBetween(Integer value1, Integer value2) {
            addCriterion("access_port not between", value1, value2, "accessPort");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNull() {
            addCriterion("updated_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNotNull() {
            addCriterion("updated_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeEqualTo(LocalDateTime value) {
            addCriterion("updated_time =", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotEqualTo(LocalDateTime value) {
            addCriterion("updated_time <>", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThan(LocalDateTime value) {
            addCriterion("updated_time >", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("updated_time >=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThan(LocalDateTime value) {
            addCriterion("updated_time <", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("updated_time <=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIn(List<LocalDateTime> values) {
            addCriterion("updated_time in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotIn(List<LocalDateTime> values) {
            addCriterion("updated_time not in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("updated_time between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("updated_time not between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedItemIsNull() {
            addCriterion("updated_item is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedItemIsNotNull() {
            addCriterion("updated_item is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedItemEqualTo(String value) {
            addCriterion("updated_item =", value, "updatedItem");
            return (Criteria) this;
        }

        public Criteria andUpdatedItemNotEqualTo(String value) {
            addCriterion("updated_item <>", value, "updatedItem");
            return (Criteria) this;
        }

        public Criteria andUpdatedItemGreaterThan(String value) {
            addCriterion("updated_item >", value, "updatedItem");
            return (Criteria) this;
        }

        public Criteria andUpdatedItemGreaterThanOrEqualTo(String value) {
            addCriterion("updated_item >=", value, "updatedItem");
            return (Criteria) this;
        }

        public Criteria andUpdatedItemLessThan(String value) {
            addCriterion("updated_item <", value, "updatedItem");
            return (Criteria) this;
        }

        public Criteria andUpdatedItemLessThanOrEqualTo(String value) {
            addCriterion("updated_item <=", value, "updatedItem");
            return (Criteria) this;
        }

        public Criteria andUpdatedItemLike(String value) {
            addCriterion("updated_item like", value, "updatedItem");
            return (Criteria) this;
        }

        public Criteria andUpdatedItemNotLike(String value) {
            addCriterion("updated_item not like", value, "updatedItem");
            return (Criteria) this;
        }

        public Criteria andUpdatedItemIn(List<String> values) {
            addCriterion("updated_item in", values, "updatedItem");
            return (Criteria) this;
        }

        public Criteria andUpdatedItemNotIn(List<String> values) {
            addCriterion("updated_item not in", values, "updatedItem");
            return (Criteria) this;
        }

        public Criteria andUpdatedItemBetween(String value1, String value2) {
            addCriterion("updated_item between", value1, value2, "updatedItem");
            return (Criteria) this;
        }

        public Criteria andUpdatedItemNotBetween(String value1, String value2) {
            addCriterion("updated_item not between", value1, value2, "updatedItem");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table room_search
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table room_search
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}