package com.allinpokers.yunyingjob.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 俱乐部玩家列表  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClubMembers {
    /**
     * 俱乐部主键ID
     */
    @ApiModelProperty("俱乐部主键ID")
    private Integer clubId;

    /**
     * 玩家ID
     */
    @ApiModelProperty("玩家ID")
    private String userId;

    /**
     * 邀请人ID
     */
    @ApiModelProperty("邀请人ID")
    private String inviteId;

    /**
     * 玩家类型 (1部长，2部员，4管理员)
     */
    @ApiModelProperty("玩家类型 (1部长，2部员，4管理员)")
    private Integer type;

    /**
     * 玩家当前信用值
     */
    @ApiModelProperty("玩家当前信用值")
    private Integer currentCredit;

    /**
     * 玩家初始信用值
     */
    @ApiModelProperty("玩家初始信用值")
    private Integer initialCredit;

    /**
     * 俱乐部信用值状态(1: 开；0: 无限制)
     */
    @ApiModelProperty("俱乐部信用值状态(1: 开；0: 无限制)")
    private Integer creditStatus;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 结算状态 (1:待结算; 0:无结算)
     */
    @ApiModelProperty("结算状态 (1:待结算; 0:无结算)")
    private Integer clearRequestStatus;

    /**
     * 用户的社区系数
     */
    @ApiModelProperty("用户的社区系数")
    private Integer ratio;

    /**
     * 用户社区开关状态 默认是0就是没有开启1就是开启
     */
    @ApiModelProperty("用户社区开关状态 默认是0就是没有开启1就是开启")
    private Integer ratiostatus;

    /**
     * push状态 (1:开; 0:关)
     */
    @ApiModelProperty("push状态 (1:开; 0:关)")
    private Integer pushStatus;

    /**
     * 推广身份，默认为0。0-玩家，1-推广员
     */
    @ApiModelProperty("推广身份，默认为0。0-玩家，1-推广员")
    private Integer promotionType;
}