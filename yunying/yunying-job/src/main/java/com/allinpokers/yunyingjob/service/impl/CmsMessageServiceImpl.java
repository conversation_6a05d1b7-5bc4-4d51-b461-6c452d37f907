package com.allinpokers.yunyingjob.service.impl;

import com.allinpokers.yunyingjob.dao.yunying.CmsMessageAuthUserDao;
import com.allinpokers.yunyingjob.dao.yunying.CmsMessageDao;
import com.allinpokers.yunyingjob.dao.yunying.CmsMessageUnreadDao;
import com.allinpokers.yunyingjob.service.CmsMessageService;
import com.allinpokers.yunyingjob.service.model.Unread;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("cmsMessageService")
public class CmsMessageServiceImpl implements CmsMessageService {
    @Resource
    private CmsMessageDao cmsMessageDao;
    @Resource
    private CmsMessageAuthUserDao cmsMessageAuthUserDao;
    @Resource
    private CmsMessageUnreadDao cmsMessageUnreadDao;
    private DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Transactional(transactionManager = "yunyingTransactionManager", rollbackFor = RuntimeException.class)
    @Override
    public void removeExpireMessages(int days) {
        LocalDateTime time = LocalDateTime.now().plusDays(-days);
        //查询该日期之前的未读消息有多少条
        List<Unread> unreads = cmsMessageAuthUserDao.findUnreadBeforeTime(time);
        log.info("CmsMessage, removeExpireMessages: days={}, time={}, {}", days, time.format(formatter), unreads);

        cmsMessageDao.deleteBeforeTime(time);

        //重新统计有未读消息的
        Set<Integer> userIds = unreads.stream().map(Unread::getUserId).collect(Collectors.toSet());
        if (!userIds.isEmpty()) {
            cmsMessageUnreadDao.reCalcCount(userIds);
        }
    }
}
