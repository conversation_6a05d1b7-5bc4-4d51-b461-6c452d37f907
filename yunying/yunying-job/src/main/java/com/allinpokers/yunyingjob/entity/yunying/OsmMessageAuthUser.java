package com.allinpokers.yunyingjob.entity.yunying;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * OSM消息 - 用户  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OsmMessageAuthUser {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 消息ID
     */
    @ApiModelProperty("消息ID")
    private String msgId;

    /**
     * 接收者ID
     */
    @ApiModelProperty("接收者ID")
    private Integer receiverId;

    /**
     * 状态 0未读/未处理 1已读/已处理
     */
    @ApiModelProperty("状态 0未读/未处理 1已读/已处理")
    private Integer status;

    /**
     * 阅读时间
     */
    @ApiModelProperty("阅读时间")
    private LocalDateTime readTime;

    /**
     * 处理时间
     */
    @ApiModelProperty("处理时间")
    private LocalDateTime processingTime;
}