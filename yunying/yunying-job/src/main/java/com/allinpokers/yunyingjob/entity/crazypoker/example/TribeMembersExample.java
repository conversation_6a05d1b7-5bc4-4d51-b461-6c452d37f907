package com.allinpokers.yunyingjob.entity.crazypoker.example;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class TribeMembersExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table tribe_members
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table tribe_members
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table tribe_members
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tribe_members
     *
     * @mbg.generated
     */
    public TribeMembersExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tribe_members
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tribe_members
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tribe_members
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tribe_members
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tribe_members
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tribe_members
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tribe_members
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tribe_members
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tribe_members
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tribe_members
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table tribe_members
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andClubIdIsNull() {
            addCriterion("club_id is null");
            return (Criteria) this;
        }

        public Criteria andClubIdIsNotNull() {
            addCriterion("club_id is not null");
            return (Criteria) this;
        }

        public Criteria andClubIdEqualTo(Integer value) {
            addCriterion("club_id =", value, "clubId");
            return (Criteria) this;
        }

        public Criteria andClubIdNotEqualTo(Integer value) {
            addCriterion("club_id <>", value, "clubId");
            return (Criteria) this;
        }

        public Criteria andClubIdGreaterThan(Integer value) {
            addCriterion("club_id >", value, "clubId");
            return (Criteria) this;
        }

        public Criteria andClubIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("club_id >=", value, "clubId");
            return (Criteria) this;
        }

        public Criteria andClubIdLessThan(Integer value) {
            addCriterion("club_id <", value, "clubId");
            return (Criteria) this;
        }

        public Criteria andClubIdLessThanOrEqualTo(Integer value) {
            addCriterion("club_id <=", value, "clubId");
            return (Criteria) this;
        }

        public Criteria andClubIdIn(List<Integer> values) {
            addCriterion("club_id in", values, "clubId");
            return (Criteria) this;
        }

        public Criteria andClubIdNotIn(List<Integer> values) {
            addCriterion("club_id not in", values, "clubId");
            return (Criteria) this;
        }

        public Criteria andClubIdBetween(Integer value1, Integer value2) {
            addCriterion("club_id between", value1, value2, "clubId");
            return (Criteria) this;
        }

        public Criteria andClubIdNotBetween(Integer value1, Integer value2) {
            addCriterion("club_id not between", value1, value2, "clubId");
            return (Criteria) this;
        }

        public Criteria andTribeIdIsNull() {
            addCriterion("tribe_id is null");
            return (Criteria) this;
        }

        public Criteria andTribeIdIsNotNull() {
            addCriterion("tribe_id is not null");
            return (Criteria) this;
        }

        public Criteria andTribeIdEqualTo(Integer value) {
            addCriterion("tribe_id =", value, "tribeId");
            return (Criteria) this;
        }

        public Criteria andTribeIdNotEqualTo(Integer value) {
            addCriterion("tribe_id <>", value, "tribeId");
            return (Criteria) this;
        }

        public Criteria andTribeIdGreaterThan(Integer value) {
            addCriterion("tribe_id >", value, "tribeId");
            return (Criteria) this;
        }

        public Criteria andTribeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("tribe_id >=", value, "tribeId");
            return (Criteria) this;
        }

        public Criteria andTribeIdLessThan(Integer value) {
            addCriterion("tribe_id <", value, "tribeId");
            return (Criteria) this;
        }

        public Criteria andTribeIdLessThanOrEqualTo(Integer value) {
            addCriterion("tribe_id <=", value, "tribeId");
            return (Criteria) this;
        }

        public Criteria andTribeIdIn(List<Integer> values) {
            addCriterion("tribe_id in", values, "tribeId");
            return (Criteria) this;
        }

        public Criteria andTribeIdNotIn(List<Integer> values) {
            addCriterion("tribe_id not in", values, "tribeId");
            return (Criteria) this;
        }

        public Criteria andTribeIdBetween(Integer value1, Integer value2) {
            addCriterion("tribe_id between", value1, value2, "tribeId");
            return (Criteria) this;
        }

        public Criteria andTribeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("tribe_id not between", value1, value2, "tribeId");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(LocalDateTime value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(LocalDateTime value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(LocalDateTime value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<LocalDateTime> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andClubCreditIsNull() {
            addCriterion("club_credit is null");
            return (Criteria) this;
        }

        public Criteria andClubCreditIsNotNull() {
            addCriterion("club_credit is not null");
            return (Criteria) this;
        }

        public Criteria andClubCreditEqualTo(Integer value) {
            addCriterion("club_credit =", value, "clubCredit");
            return (Criteria) this;
        }

        public Criteria andClubCreditNotEqualTo(Integer value) {
            addCriterion("club_credit <>", value, "clubCredit");
            return (Criteria) this;
        }

        public Criteria andClubCreditGreaterThan(Integer value) {
            addCriterion("club_credit >", value, "clubCredit");
            return (Criteria) this;
        }

        public Criteria andClubCreditGreaterThanOrEqualTo(Integer value) {
            addCriterion("club_credit >=", value, "clubCredit");
            return (Criteria) this;
        }

        public Criteria andClubCreditLessThan(Integer value) {
            addCriterion("club_credit <", value, "clubCredit");
            return (Criteria) this;
        }

        public Criteria andClubCreditLessThanOrEqualTo(Integer value) {
            addCriterion("club_credit <=", value, "clubCredit");
            return (Criteria) this;
        }

        public Criteria andClubCreditIn(List<Integer> values) {
            addCriterion("club_credit in", values, "clubCredit");
            return (Criteria) this;
        }

        public Criteria andClubCreditNotIn(List<Integer> values) {
            addCriterion("club_credit not in", values, "clubCredit");
            return (Criteria) this;
        }

        public Criteria andClubCreditBetween(Integer value1, Integer value2) {
            addCriterion("club_credit between", value1, value2, "clubCredit");
            return (Criteria) this;
        }

        public Criteria andClubCreditNotBetween(Integer value1, Integer value2) {
            addCriterion("club_credit not between", value1, value2, "clubCredit");
            return (Criteria) this;
        }

        public Criteria andInitCreditIsNull() {
            addCriterion("init_credit is null");
            return (Criteria) this;
        }

        public Criteria andInitCreditIsNotNull() {
            addCriterion("init_credit is not null");
            return (Criteria) this;
        }

        public Criteria andInitCreditEqualTo(Integer value) {
            addCriterion("init_credit =", value, "initCredit");
            return (Criteria) this;
        }

        public Criteria andInitCreditNotEqualTo(Integer value) {
            addCriterion("init_credit <>", value, "initCredit");
            return (Criteria) this;
        }

        public Criteria andInitCreditGreaterThan(Integer value) {
            addCriterion("init_credit >", value, "initCredit");
            return (Criteria) this;
        }

        public Criteria andInitCreditGreaterThanOrEqualTo(Integer value) {
            addCriterion("init_credit >=", value, "initCredit");
            return (Criteria) this;
        }

        public Criteria andInitCreditLessThan(Integer value) {
            addCriterion("init_credit <", value, "initCredit");
            return (Criteria) this;
        }

        public Criteria andInitCreditLessThanOrEqualTo(Integer value) {
            addCriterion("init_credit <=", value, "initCredit");
            return (Criteria) this;
        }

        public Criteria andInitCreditIn(List<Integer> values) {
            addCriterion("init_credit in", values, "initCredit");
            return (Criteria) this;
        }

        public Criteria andInitCreditNotIn(List<Integer> values) {
            addCriterion("init_credit not in", values, "initCredit");
            return (Criteria) this;
        }

        public Criteria andInitCreditBetween(Integer value1, Integer value2) {
            addCriterion("init_credit between", value1, value2, "initCredit");
            return (Criteria) this;
        }

        public Criteria andInitCreditNotBetween(Integer value1, Integer value2) {
            addCriterion("init_credit not between", value1, value2, "initCredit");
            return (Criteria) this;
        }

        public Criteria andPlIsNull() {
            addCriterion("pl is null");
            return (Criteria) this;
        }

        public Criteria andPlIsNotNull() {
            addCriterion("pl is not null");
            return (Criteria) this;
        }

        public Criteria andPlEqualTo(Integer value) {
            addCriterion("pl =", value, "pl");
            return (Criteria) this;
        }

        public Criteria andPlNotEqualTo(Integer value) {
            addCriterion("pl <>", value, "pl");
            return (Criteria) this;
        }

        public Criteria andPlGreaterThan(Integer value) {
            addCriterion("pl >", value, "pl");
            return (Criteria) this;
        }

        public Criteria andPlGreaterThanOrEqualTo(Integer value) {
            addCriterion("pl >=", value, "pl");
            return (Criteria) this;
        }

        public Criteria andPlLessThan(Integer value) {
            addCriterion("pl <", value, "pl");
            return (Criteria) this;
        }

        public Criteria andPlLessThanOrEqualTo(Integer value) {
            addCriterion("pl <=", value, "pl");
            return (Criteria) this;
        }

        public Criteria andPlIn(List<Integer> values) {
            addCriterion("pl in", values, "pl");
            return (Criteria) this;
        }

        public Criteria andPlNotIn(List<Integer> values) {
            addCriterion("pl not in", values, "pl");
            return (Criteria) this;
        }

        public Criteria andPlBetween(Integer value1, Integer value2) {
            addCriterion("pl between", value1, value2, "pl");
            return (Criteria) this;
        }

        public Criteria andPlNotBetween(Integer value1, Integer value2) {
            addCriterion("pl not between", value1, value2, "pl");
            return (Criteria) this;
        }

        public Criteria andPauseIsNull() {
            addCriterion("pause is null");
            return (Criteria) this;
        }

        public Criteria andPauseIsNotNull() {
            addCriterion("pause is not null");
            return (Criteria) this;
        }

        public Criteria andPauseEqualTo(Short value) {
            addCriterion("pause =", value, "pause");
            return (Criteria) this;
        }

        public Criteria andPauseNotEqualTo(Short value) {
            addCriterion("pause <>", value, "pause");
            return (Criteria) this;
        }

        public Criteria andPauseGreaterThan(Short value) {
            addCriterion("pause >", value, "pause");
            return (Criteria) this;
        }

        public Criteria andPauseGreaterThanOrEqualTo(Short value) {
            addCriterion("pause >=", value, "pause");
            return (Criteria) this;
        }

        public Criteria andPauseLessThan(Short value) {
            addCriterion("pause <", value, "pause");
            return (Criteria) this;
        }

        public Criteria andPauseLessThanOrEqualTo(Short value) {
            addCriterion("pause <=", value, "pause");
            return (Criteria) this;
        }

        public Criteria andPauseIn(List<Short> values) {
            addCriterion("pause in", values, "pause");
            return (Criteria) this;
        }

        public Criteria andPauseNotIn(List<Short> values) {
            addCriterion("pause not in", values, "pause");
            return (Criteria) this;
        }

        public Criteria andPauseBetween(Short value1, Short value2) {
            addCriterion("pause between", value1, value2, "pause");
            return (Criteria) this;
        }

        public Criteria andPauseNotBetween(Short value1, Short value2) {
            addCriterion("pause not between", value1, value2, "pause");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Short value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Short value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Short value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Short value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Short value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Short value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Short> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Short> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Short value1, Short value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Short value1, Short value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table tribe_members
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table tribe_members
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}