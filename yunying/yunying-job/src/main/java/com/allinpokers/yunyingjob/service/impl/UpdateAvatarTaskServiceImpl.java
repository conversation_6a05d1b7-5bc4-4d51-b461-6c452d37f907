package com.allinpokers.yunyingjob.service.impl;

import com.allinpokers.yunyingjob.dao.crazypoker.AvatarLibraryDao;
import com.allinpokers.yunyingjob.dao.crazypoker.UpdateAvatarTaskDao;
import com.allinpokers.yunyingjob.dao.crazypoker.UserAvatarRecordDao;
import com.allinpokers.yunyingjob.dao.crazypoker.UserDetailsInfoDao;
import com.allinpokers.yunyingjob.entity.crazypoker.AvatarLibrary;
import com.allinpokers.yunyingjob.entity.crazypoker.UpdateAvatarTask;
import com.allinpokers.yunyingjob.entity.crazypoker.UserDetailsInfo;
import com.allinpokers.yunyingjob.rabbitmq.client.MessageSender;
import com.allinpokers.yunyingjob.rabbitmq.client.bean.user.InfoModMessage;
import com.allinpokers.yunyingjob.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 更新用户头像
 */
@Service
@Slf4j
public class UpdateAvatarTaskServiceImpl implements UpdateAvatarTaskService {

    @Resource
    private UpdateAvatarTaskDao updateAvatarTaskDao;

    @Resource
    private AvatarLibraryDao avatarLibraryDao;

    @Resource
    private UserDetailsInfoDao userDetailsInfoDao;

    @Resource
    private UserAvatarRecordDao userAvatarRecordDao;

    @Resource
    private MessageSender messageSender;

    @Override
    public void avatarUpdateTasksJob() {
        List<UpdateAvatarTask> taskList = updateAvatarTaskDao.getWaitingTask();
        if (taskList != null && taskList.size() > 0) {
            log.info("更新用户头像--有待执行任务，count: {}", taskList.size());
            taskList.forEach(task -> {
                log.info("---更换头像--===> taskId： {}", task.getId());
                int updateCount = updateAvatarTaskDao.updateTaskRunning(task.getId());
                if (updateCount == 1) {
                    // 锁定任务
                    AvatarLibrary avatarLibrary = avatarLibraryDao.getLibraryById(task.getAvatarLibId());
                    if (avatarLibrary == null) {
                        // 头像库信息错误
                        updateAvatarTaskDao.updateTaskFail(task.getId(), "头像库不存在");
                    } else {
                        if (avatarLibrary.getUseStatus() == 1) {
                            // 头像已被使用
                            updateAvatarTaskDao.updateTaskFail(task.getId(), "头像已被使用");
                        } else {
                            UserDetailsInfo userDetailsInfo = userDetailsInfoDao.selectByPrimaryKey(task.getUserId());
                            if (userDetailsInfo == null) {
                                // 用户信息错误
                                updateAvatarTaskDao.updateTaskFail(task.getId(), "用户信息错误");
                                return;
                            }
                            if (task.getTargetUser() == 1) {
                                int isAt = updateAvatarTaskDao.checkAtUser(task.getUserId());
                                if (isAt < 1) {
                                    // 用户账号类型错误
                                    updateAvatarTaskDao.updateTaskFail(task.getId(), "用户账号类型错误，非at账号");
                                    return;
                                }
                                // ai, 判断ai账号是否在牌局中
                                int countRoom = updateAvatarTaskDao.countUserInRoom(task.getUserId());
                                if (countRoom > 0) {
                                    // 在牌局中
                                    updateAvatarTaskDao.updateTaskFail(task.getId(), "用户在牌局中");
                                    return;
                                }
                            }
                            int updateHead = 0;
                            if (avatarLibrary.getHeadType() == 1) {
                                // 自定义头像
                                updateHead = userDetailsInfoDao.updateUserCustomHead(userDetailsInfo.getUserId(), avatarLibrary.getHeadUrl());
                            } else {
                                // 默认头像
                                updateHead = userDetailsInfoDao.updateUserHead(userDetailsInfo.getUserId(), avatarLibrary.getHeadUrl());
                            }
                            if (updateHead > 0) {
                                log.info("更换头像成功 ===> {}", task.getId());
                                userAvatarRecordDao.insertRecord(task.getTargetUser(), task.getUserId(), task.getAvatarLibId(), null);
                                //发送用户修改信息
                                messageSender.sendUserMessage(InfoModMessage.builder()
                                        .userId(userDetailsInfo.getUserId())
                                        .nickname(userDetailsInfo.getNikeName())
                                        .head(avatarLibrary.getHeadUrl())
                                        .build());
                                // 更新用户头像成功
                                updateAvatarTaskDao.updateTaskSuccess(task.getId(), "执行成功");
                                avatarLibraryDao.updateStatus(avatarLibrary.getId());
                            } else {
                                // 更新用户头像失败
                                updateAvatarTaskDao.updateTaskFail(task.getId(), "更新用户头像失败");
                            }
                        }
                    }
                }
            });
        }
    }
}
