package com.allinpokers.yunyingjob.export.mq.bean;


import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * RoomRecordQuery
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class RoomRecordQuery {

    @ApiModelProperty("联盟ID")
    private Integer tribeId;

    @ApiModelProperty("俱乐部ID")
    private Integer clubId;

    @ApiModelProperty("房间名称或ID")
    private String search;

    @ApiModelProperty("文件名称")
    private String fileName;
}
