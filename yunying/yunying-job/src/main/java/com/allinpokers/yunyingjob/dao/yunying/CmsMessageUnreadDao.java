package com.allinpokers.yunyingjob.dao.yunying;

import com.allinpokers.yunying.dao.BaseDao;
import com.allinpokers.yunyingjob.entity.yunying.CmsMessageUnread;
import com.allinpokers.yunyingjob.entity.yunying.example.CmsMessageUnreadExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Set;

/**
 * CMS未读消息  Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CmsMessageUnreadDao extends BaseDao<CmsMessageUnread, CmsMessageUnreadExample, Integer> {

    /**
     * 重新计算未读
     *
     * @param userIds
     */
    void reCalcCount(@Param("userIds") Set<Integer> userIds);
}