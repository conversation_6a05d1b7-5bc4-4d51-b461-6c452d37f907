package com.allinpokers.yunyingjob.dao.yunying;

import com.allinpokers.yunying.dao.BaseDao;
import com.allinpokers.yunyingjob.entity.yunying.OsmMessageAuthUser;
import com.allinpokers.yunyingjob.entity.yunying.example.OsmMessageAuthUserExample;
import com.allinpokers.yunyingjob.service.model.Unread;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * OSM消息 - 用户  Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface OsmMessageAuthUserDao extends BaseDao<OsmMessageAuthUser, OsmMessageAuthUserExample, Integer> {

    /**
     * 查询未读，且创建时间在time之前的
     *
     * @param time
     * @return
     */
    List<Unread> findUnreadBeforeTime(@Param("time") LocalDateTime time);
}