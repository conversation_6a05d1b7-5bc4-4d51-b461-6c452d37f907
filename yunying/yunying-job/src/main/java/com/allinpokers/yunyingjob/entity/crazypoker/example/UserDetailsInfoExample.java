package com.allinpokers.yunyingjob.entity.crazypoker.example;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class UserDetailsInfoExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table user_details_info
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table user_details_info
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table user_details_info
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_details_info
     *
     * @mbg.generated
     */
    public UserDetailsInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_details_info
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_details_info
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_details_info
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_details_info
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_details_info
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_details_info
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_details_info
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_details_info
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_details_info
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_details_info
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table user_details_info
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andUserIdIsNull() {
            addCriterion("USER_ID is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("USER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Integer value) {
            addCriterion("USER_ID =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Integer value) {
            addCriterion("USER_ID <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Integer value) {
            addCriterion("USER_ID >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("USER_ID >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Integer value) {
            addCriterion("USER_ID <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("USER_ID <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Integer> values) {
            addCriterion("USER_ID in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Integer> values) {
            addCriterion("USER_ID not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Integer value1, Integer value2) {
            addCriterion("USER_ID between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("USER_ID not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andNikeNameIsNull() {
            addCriterion("nike_name is null");
            return (Criteria) this;
        }

        public Criteria andNikeNameIsNotNull() {
            addCriterion("nike_name is not null");
            return (Criteria) this;
        }

        public Criteria andNikeNameEqualTo(String value) {
            addCriterion("nike_name =", value, "nikeName");
            return (Criteria) this;
        }

        public Criteria andNikeNameNotEqualTo(String value) {
            addCriterion("nike_name <>", value, "nikeName");
            return (Criteria) this;
        }

        public Criteria andNikeNameGreaterThan(String value) {
            addCriterion("nike_name >", value, "nikeName");
            return (Criteria) this;
        }

        public Criteria andNikeNameGreaterThanOrEqualTo(String value) {
            addCriterion("nike_name >=", value, "nikeName");
            return (Criteria) this;
        }

        public Criteria andNikeNameLessThan(String value) {
            addCriterion("nike_name <", value, "nikeName");
            return (Criteria) this;
        }

        public Criteria andNikeNameLessThanOrEqualTo(String value) {
            addCriterion("nike_name <=", value, "nikeName");
            return (Criteria) this;
        }

        public Criteria andNikeNameLike(String value) {
            addCriterion("nike_name like", value, "nikeName");
            return (Criteria) this;
        }

        public Criteria andNikeNameNotLike(String value) {
            addCriterion("nike_name not like", value, "nikeName");
            return (Criteria) this;
        }

        public Criteria andNikeNameIn(List<String> values) {
            addCriterion("nike_name in", values, "nikeName");
            return (Criteria) this;
        }

        public Criteria andNikeNameNotIn(List<String> values) {
            addCriterion("nike_name not in", values, "nikeName");
            return (Criteria) this;
        }

        public Criteria andNikeNameBetween(String value1, String value2) {
            addCriterion("nike_name between", value1, value2, "nikeName");
            return (Criteria) this;
        }

        public Criteria andNikeNameNotBetween(String value1, String value2) {
            addCriterion("nike_name not between", value1, value2, "nikeName");
            return (Criteria) this;
        }

        public Criteria andHeadIsNull() {
            addCriterion("HEAD is null");
            return (Criteria) this;
        }

        public Criteria andHeadIsNotNull() {
            addCriterion("HEAD is not null");
            return (Criteria) this;
        }

        public Criteria andHeadEqualTo(String value) {
            addCriterion("HEAD =", value, "head");
            return (Criteria) this;
        }

        public Criteria andHeadNotEqualTo(String value) {
            addCriterion("HEAD <>", value, "head");
            return (Criteria) this;
        }

        public Criteria andHeadGreaterThan(String value) {
            addCriterion("HEAD >", value, "head");
            return (Criteria) this;
        }

        public Criteria andHeadGreaterThanOrEqualTo(String value) {
            addCriterion("HEAD >=", value, "head");
            return (Criteria) this;
        }

        public Criteria andHeadLessThan(String value) {
            addCriterion("HEAD <", value, "head");
            return (Criteria) this;
        }

        public Criteria andHeadLessThanOrEqualTo(String value) {
            addCriterion("HEAD <=", value, "head");
            return (Criteria) this;
        }

        public Criteria andHeadLike(String value) {
            addCriterion("HEAD like", value, "head");
            return (Criteria) this;
        }

        public Criteria andHeadNotLike(String value) {
            addCriterion("HEAD not like", value, "head");
            return (Criteria) this;
        }

        public Criteria andHeadIn(List<String> values) {
            addCriterion("HEAD in", values, "head");
            return (Criteria) this;
        }

        public Criteria andHeadNotIn(List<String> values) {
            addCriterion("HEAD not in", values, "head");
            return (Criteria) this;
        }

        public Criteria andHeadBetween(String value1, String value2) {
            addCriterion("HEAD between", value1, value2, "head");
            return (Criteria) this;
        }

        public Criteria andHeadNotBetween(String value1, String value2) {
            addCriterion("HEAD not between", value1, value2, "head");
            return (Criteria) this;
        }

        public Criteria andSexIsNull() {
            addCriterion("SEX is null");
            return (Criteria) this;
        }

        public Criteria andSexIsNotNull() {
            addCriterion("SEX is not null");
            return (Criteria) this;
        }

        public Criteria andSexEqualTo(Integer value) {
            addCriterion("SEX =", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotEqualTo(Integer value) {
            addCriterion("SEX <>", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexGreaterThan(Integer value) {
            addCriterion("SEX >", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexGreaterThanOrEqualTo(Integer value) {
            addCriterion("SEX >=", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexLessThan(Integer value) {
            addCriterion("SEX <", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexLessThanOrEqualTo(Integer value) {
            addCriterion("SEX <=", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexIn(List<Integer> values) {
            addCriterion("SEX in", values, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotIn(List<Integer> values) {
            addCriterion("SEX not in", values, "sex");
            return (Criteria) this;
        }

        public Criteria andSexBetween(Integer value1, Integer value2) {
            addCriterion("SEX between", value1, value2, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotBetween(Integer value1, Integer value2) {
            addCriterion("SEX not between", value1, value2, "sex");
            return (Criteria) this;
        }

        public Criteria andChipIsNull() {
            addCriterion("CHIP is null");
            return (Criteria) this;
        }

        public Criteria andChipIsNotNull() {
            addCriterion("CHIP is not null");
            return (Criteria) this;
        }

        public Criteria andChipEqualTo(Integer value) {
            addCriterion("CHIP =", value, "chip");
            return (Criteria) this;
        }

        public Criteria andChipNotEqualTo(Integer value) {
            addCriterion("CHIP <>", value, "chip");
            return (Criteria) this;
        }

        public Criteria andChipGreaterThan(Integer value) {
            addCriterion("CHIP >", value, "chip");
            return (Criteria) this;
        }

        public Criteria andChipGreaterThanOrEqualTo(Integer value) {
            addCriterion("CHIP >=", value, "chip");
            return (Criteria) this;
        }

        public Criteria andChipLessThan(Integer value) {
            addCriterion("CHIP <", value, "chip");
            return (Criteria) this;
        }

        public Criteria andChipLessThanOrEqualTo(Integer value) {
            addCriterion("CHIP <=", value, "chip");
            return (Criteria) this;
        }

        public Criteria andChipIn(List<Integer> values) {
            addCriterion("CHIP in", values, "chip");
            return (Criteria) this;
        }

        public Criteria andChipNotIn(List<Integer> values) {
            addCriterion("CHIP not in", values, "chip");
            return (Criteria) this;
        }

        public Criteria andChipBetween(Integer value1, Integer value2) {
            addCriterion("CHIP between", value1, value2, "chip");
            return (Criteria) this;
        }

        public Criteria andChipNotBetween(Integer value1, Integer value2) {
            addCriterion("CHIP not between", value1, value2, "chip");
            return (Criteria) this;
        }

        public Criteria andWinIsNull() {
            addCriterion("WIN is null");
            return (Criteria) this;
        }

        public Criteria andWinIsNotNull() {
            addCriterion("WIN is not null");
            return (Criteria) this;
        }

        public Criteria andWinEqualTo(Integer value) {
            addCriterion("WIN =", value, "win");
            return (Criteria) this;
        }

        public Criteria andWinNotEqualTo(Integer value) {
            addCriterion("WIN <>", value, "win");
            return (Criteria) this;
        }

        public Criteria andWinGreaterThan(Integer value) {
            addCriterion("WIN >", value, "win");
            return (Criteria) this;
        }

        public Criteria andWinGreaterThanOrEqualTo(Integer value) {
            addCriterion("WIN >=", value, "win");
            return (Criteria) this;
        }

        public Criteria andWinLessThan(Integer value) {
            addCriterion("WIN <", value, "win");
            return (Criteria) this;
        }

        public Criteria andWinLessThanOrEqualTo(Integer value) {
            addCriterion("WIN <=", value, "win");
            return (Criteria) this;
        }

        public Criteria andWinIn(List<Integer> values) {
            addCriterion("WIN in", values, "win");
            return (Criteria) this;
        }

        public Criteria andWinNotIn(List<Integer> values) {
            addCriterion("WIN not in", values, "win");
            return (Criteria) this;
        }

        public Criteria andWinBetween(Integer value1, Integer value2) {
            addCriterion("WIN between", value1, value2, "win");
            return (Criteria) this;
        }

        public Criteria andWinNotBetween(Integer value1, Integer value2) {
            addCriterion("WIN not between", value1, value2, "win");
            return (Criteria) this;
        }

        public Criteria andLoseIsNull() {
            addCriterion("LOSE is null");
            return (Criteria) this;
        }

        public Criteria andLoseIsNotNull() {
            addCriterion("LOSE is not null");
            return (Criteria) this;
        }

        public Criteria andLoseEqualTo(Integer value) {
            addCriterion("LOSE =", value, "lose");
            return (Criteria) this;
        }

        public Criteria andLoseNotEqualTo(Integer value) {
            addCriterion("LOSE <>", value, "lose");
            return (Criteria) this;
        }

        public Criteria andLoseGreaterThan(Integer value) {
            addCriterion("LOSE >", value, "lose");
            return (Criteria) this;
        }

        public Criteria andLoseGreaterThanOrEqualTo(Integer value) {
            addCriterion("LOSE >=", value, "lose");
            return (Criteria) this;
        }

        public Criteria andLoseLessThan(Integer value) {
            addCriterion("LOSE <", value, "lose");
            return (Criteria) this;
        }

        public Criteria andLoseLessThanOrEqualTo(Integer value) {
            addCriterion("LOSE <=", value, "lose");
            return (Criteria) this;
        }

        public Criteria andLoseIn(List<Integer> values) {
            addCriterion("LOSE in", values, "lose");
            return (Criteria) this;
        }

        public Criteria andLoseNotIn(List<Integer> values) {
            addCriterion("LOSE not in", values, "lose");
            return (Criteria) this;
        }

        public Criteria andLoseBetween(Integer value1, Integer value2) {
            addCriterion("LOSE between", value1, value2, "lose");
            return (Criteria) this;
        }

        public Criteria andLoseNotBetween(Integer value1, Integer value2) {
            addCriterion("LOSE not between", value1, value2, "lose");
            return (Criteria) this;
        }

        public Criteria andNutHandIsNull() {
            addCriterion("NUT_HAND is null");
            return (Criteria) this;
        }

        public Criteria andNutHandIsNotNull() {
            addCriterion("NUT_HAND is not null");
            return (Criteria) this;
        }

        public Criteria andNutHandEqualTo(String value) {
            addCriterion("NUT_HAND =", value, "nutHand");
            return (Criteria) this;
        }

        public Criteria andNutHandNotEqualTo(String value) {
            addCriterion("NUT_HAND <>", value, "nutHand");
            return (Criteria) this;
        }

        public Criteria andNutHandGreaterThan(String value) {
            addCriterion("NUT_HAND >", value, "nutHand");
            return (Criteria) this;
        }

        public Criteria andNutHandGreaterThanOrEqualTo(String value) {
            addCriterion("NUT_HAND >=", value, "nutHand");
            return (Criteria) this;
        }

        public Criteria andNutHandLessThan(String value) {
            addCriterion("NUT_HAND <", value, "nutHand");
            return (Criteria) this;
        }

        public Criteria andNutHandLessThanOrEqualTo(String value) {
            addCriterion("NUT_HAND <=", value, "nutHand");
            return (Criteria) this;
        }

        public Criteria andNutHandLike(String value) {
            addCriterion("NUT_HAND like", value, "nutHand");
            return (Criteria) this;
        }

        public Criteria andNutHandNotLike(String value) {
            addCriterion("NUT_HAND not like", value, "nutHand");
            return (Criteria) this;
        }

        public Criteria andNutHandIn(List<String> values) {
            addCriterion("NUT_HAND in", values, "nutHand");
            return (Criteria) this;
        }

        public Criteria andNutHandNotIn(List<String> values) {
            addCriterion("NUT_HAND not in", values, "nutHand");
            return (Criteria) this;
        }

        public Criteria andNutHandBetween(String value1, String value2) {
            addCriterion("NUT_HAND between", value1, value2, "nutHand");
            return (Criteria) this;
        }

        public Criteria andNutHandNotBetween(String value1, String value2) {
            addCriterion("NUT_HAND not between", value1, value2, "nutHand");
            return (Criteria) this;
        }

        public Criteria andHighestHaveIsNull() {
            addCriterion("HIGHEST_HAVE is null");
            return (Criteria) this;
        }

        public Criteria andHighestHaveIsNotNull() {
            addCriterion("HIGHEST_HAVE is not null");
            return (Criteria) this;
        }

        public Criteria andHighestHaveEqualTo(Integer value) {
            addCriterion("HIGHEST_HAVE =", value, "highestHave");
            return (Criteria) this;
        }

        public Criteria andHighestHaveNotEqualTo(Integer value) {
            addCriterion("HIGHEST_HAVE <>", value, "highestHave");
            return (Criteria) this;
        }

        public Criteria andHighestHaveGreaterThan(Integer value) {
            addCriterion("HIGHEST_HAVE >", value, "highestHave");
            return (Criteria) this;
        }

        public Criteria andHighestHaveGreaterThanOrEqualTo(Integer value) {
            addCriterion("HIGHEST_HAVE >=", value, "highestHave");
            return (Criteria) this;
        }

        public Criteria andHighestHaveLessThan(Integer value) {
            addCriterion("HIGHEST_HAVE <", value, "highestHave");
            return (Criteria) this;
        }

        public Criteria andHighestHaveLessThanOrEqualTo(Integer value) {
            addCriterion("HIGHEST_HAVE <=", value, "highestHave");
            return (Criteria) this;
        }

        public Criteria andHighestHaveIn(List<Integer> values) {
            addCriterion("HIGHEST_HAVE in", values, "highestHave");
            return (Criteria) this;
        }

        public Criteria andHighestHaveNotIn(List<Integer> values) {
            addCriterion("HIGHEST_HAVE not in", values, "highestHave");
            return (Criteria) this;
        }

        public Criteria andHighestHaveBetween(Integer value1, Integer value2) {
            addCriterion("HIGHEST_HAVE between", value1, value2, "highestHave");
            return (Criteria) this;
        }

        public Criteria andHighestHaveNotBetween(Integer value1, Integer value2) {
            addCriterion("HIGHEST_HAVE not between", value1, value2, "highestHave");
            return (Criteria) this;
        }

        public Criteria andNutHandTypeIsNull() {
            addCriterion("NUT_HAND_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andNutHandTypeIsNotNull() {
            addCriterion("NUT_HAND_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andNutHandTypeEqualTo(Integer value) {
            addCriterion("NUT_HAND_TYPE =", value, "nutHandType");
            return (Criteria) this;
        }

        public Criteria andNutHandTypeNotEqualTo(Integer value) {
            addCriterion("NUT_HAND_TYPE <>", value, "nutHandType");
            return (Criteria) this;
        }

        public Criteria andNutHandTypeGreaterThan(Integer value) {
            addCriterion("NUT_HAND_TYPE >", value, "nutHandType");
            return (Criteria) this;
        }

        public Criteria andNutHandTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("NUT_HAND_TYPE >=", value, "nutHandType");
            return (Criteria) this;
        }

        public Criteria andNutHandTypeLessThan(Integer value) {
            addCriterion("NUT_HAND_TYPE <", value, "nutHandType");
            return (Criteria) this;
        }

        public Criteria andNutHandTypeLessThanOrEqualTo(Integer value) {
            addCriterion("NUT_HAND_TYPE <=", value, "nutHandType");
            return (Criteria) this;
        }

        public Criteria andNutHandTypeIn(List<Integer> values) {
            addCriterion("NUT_HAND_TYPE in", values, "nutHandType");
            return (Criteria) this;
        }

        public Criteria andNutHandTypeNotIn(List<Integer> values) {
            addCriterion("NUT_HAND_TYPE not in", values, "nutHandType");
            return (Criteria) this;
        }

        public Criteria andNutHandTypeBetween(Integer value1, Integer value2) {
            addCriterion("NUT_HAND_TYPE between", value1, value2, "nutHandType");
            return (Criteria) this;
        }

        public Criteria andNutHandTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("NUT_HAND_TYPE not between", value1, value2, "nutHandType");
            return (Criteria) this;
        }

        public Criteria andMaxWinIsNull() {
            addCriterion("MAX_WIN is null");
            return (Criteria) this;
        }

        public Criteria andMaxWinIsNotNull() {
            addCriterion("MAX_WIN is not null");
            return (Criteria) this;
        }

        public Criteria andMaxWinEqualTo(Integer value) {
            addCriterion("MAX_WIN =", value, "maxWin");
            return (Criteria) this;
        }

        public Criteria andMaxWinNotEqualTo(Integer value) {
            addCriterion("MAX_WIN <>", value, "maxWin");
            return (Criteria) this;
        }

        public Criteria andMaxWinGreaterThan(Integer value) {
            addCriterion("MAX_WIN >", value, "maxWin");
            return (Criteria) this;
        }

        public Criteria andMaxWinGreaterThanOrEqualTo(Integer value) {
            addCriterion("MAX_WIN >=", value, "maxWin");
            return (Criteria) this;
        }

        public Criteria andMaxWinLessThan(Integer value) {
            addCriterion("MAX_WIN <", value, "maxWin");
            return (Criteria) this;
        }

        public Criteria andMaxWinLessThanOrEqualTo(Integer value) {
            addCriterion("MAX_WIN <=", value, "maxWin");
            return (Criteria) this;
        }

        public Criteria andMaxWinIn(List<Integer> values) {
            addCriterion("MAX_WIN in", values, "maxWin");
            return (Criteria) this;
        }

        public Criteria andMaxWinNotIn(List<Integer> values) {
            addCriterion("MAX_WIN not in", values, "maxWin");
            return (Criteria) this;
        }

        public Criteria andMaxWinBetween(Integer value1, Integer value2) {
            addCriterion("MAX_WIN between", value1, value2, "maxWin");
            return (Criteria) this;
        }

        public Criteria andMaxWinNotBetween(Integer value1, Integer value2) {
            addCriterion("MAX_WIN not between", value1, value2, "maxWin");
            return (Criteria) this;
        }

        public Criteria andFreeGetIsNull() {
            addCriterion("FREE_GET is null");
            return (Criteria) this;
        }

        public Criteria andFreeGetIsNotNull() {
            addCriterion("FREE_GET is not null");
            return (Criteria) this;
        }

        public Criteria andFreeGetEqualTo(Integer value) {
            addCriterion("FREE_GET =", value, "freeGet");
            return (Criteria) this;
        }

        public Criteria andFreeGetNotEqualTo(Integer value) {
            addCriterion("FREE_GET <>", value, "freeGet");
            return (Criteria) this;
        }

        public Criteria andFreeGetGreaterThan(Integer value) {
            addCriterion("FREE_GET >", value, "freeGet");
            return (Criteria) this;
        }

        public Criteria andFreeGetGreaterThanOrEqualTo(Integer value) {
            addCriterion("FREE_GET >=", value, "freeGet");
            return (Criteria) this;
        }

        public Criteria andFreeGetLessThan(Integer value) {
            addCriterion("FREE_GET <", value, "freeGet");
            return (Criteria) this;
        }

        public Criteria andFreeGetLessThanOrEqualTo(Integer value) {
            addCriterion("FREE_GET <=", value, "freeGet");
            return (Criteria) this;
        }

        public Criteria andFreeGetIn(List<Integer> values) {
            addCriterion("FREE_GET in", values, "freeGet");
            return (Criteria) this;
        }

        public Criteria andFreeGetNotIn(List<Integer> values) {
            addCriterion("FREE_GET not in", values, "freeGet");
            return (Criteria) this;
        }

        public Criteria andFreeGetBetween(Integer value1, Integer value2) {
            addCriterion("FREE_GET between", value1, value2, "freeGet");
            return (Criteria) this;
        }

        public Criteria andFreeGetNotBetween(Integer value1, Integer value2) {
            addCriterion("FREE_GET not between", value1, value2, "freeGet");
            return (Criteria) this;
        }

        public Criteria andGameIntegralIsNull() {
            addCriterion("GAME_INTEGRAL is null");
            return (Criteria) this;
        }

        public Criteria andGameIntegralIsNotNull() {
            addCriterion("GAME_INTEGRAL is not null");
            return (Criteria) this;
        }

        public Criteria andGameIntegralEqualTo(Integer value) {
            addCriterion("GAME_INTEGRAL =", value, "gameIntegral");
            return (Criteria) this;
        }

        public Criteria andGameIntegralNotEqualTo(Integer value) {
            addCriterion("GAME_INTEGRAL <>", value, "gameIntegral");
            return (Criteria) this;
        }

        public Criteria andGameIntegralGreaterThan(Integer value) {
            addCriterion("GAME_INTEGRAL >", value, "gameIntegral");
            return (Criteria) this;
        }

        public Criteria andGameIntegralGreaterThanOrEqualTo(Integer value) {
            addCriterion("GAME_INTEGRAL >=", value, "gameIntegral");
            return (Criteria) this;
        }

        public Criteria andGameIntegralLessThan(Integer value) {
            addCriterion("GAME_INTEGRAL <", value, "gameIntegral");
            return (Criteria) this;
        }

        public Criteria andGameIntegralLessThanOrEqualTo(Integer value) {
            addCriterion("GAME_INTEGRAL <=", value, "gameIntegral");
            return (Criteria) this;
        }

        public Criteria andGameIntegralIn(List<Integer> values) {
            addCriterion("GAME_INTEGRAL in", values, "gameIntegral");
            return (Criteria) this;
        }

        public Criteria andGameIntegralNotIn(List<Integer> values) {
            addCriterion("GAME_INTEGRAL not in", values, "gameIntegral");
            return (Criteria) this;
        }

        public Criteria andGameIntegralBetween(Integer value1, Integer value2) {
            addCriterion("GAME_INTEGRAL between", value1, value2, "gameIntegral");
            return (Criteria) this;
        }

        public Criteria andGameIntegralNotBetween(Integer value1, Integer value2) {
            addCriterion("GAME_INTEGRAL not between", value1, value2, "gameIntegral");
            return (Criteria) this;
        }

        public Criteria andMatchIntegralIsNull() {
            addCriterion("MATCH_INTEGRAL is null");
            return (Criteria) this;
        }

        public Criteria andMatchIntegralIsNotNull() {
            addCriterion("MATCH_INTEGRAL is not null");
            return (Criteria) this;
        }

        public Criteria andMatchIntegralEqualTo(Integer value) {
            addCriterion("MATCH_INTEGRAL =", value, "matchIntegral");
            return (Criteria) this;
        }

        public Criteria andMatchIntegralNotEqualTo(Integer value) {
            addCriterion("MATCH_INTEGRAL <>", value, "matchIntegral");
            return (Criteria) this;
        }

        public Criteria andMatchIntegralGreaterThan(Integer value) {
            addCriterion("MATCH_INTEGRAL >", value, "matchIntegral");
            return (Criteria) this;
        }

        public Criteria andMatchIntegralGreaterThanOrEqualTo(Integer value) {
            addCriterion("MATCH_INTEGRAL >=", value, "matchIntegral");
            return (Criteria) this;
        }

        public Criteria andMatchIntegralLessThan(Integer value) {
            addCriterion("MATCH_INTEGRAL <", value, "matchIntegral");
            return (Criteria) this;
        }

        public Criteria andMatchIntegralLessThanOrEqualTo(Integer value) {
            addCriterion("MATCH_INTEGRAL <=", value, "matchIntegral");
            return (Criteria) this;
        }

        public Criteria andMatchIntegralIn(List<Integer> values) {
            addCriterion("MATCH_INTEGRAL in", values, "matchIntegral");
            return (Criteria) this;
        }

        public Criteria andMatchIntegralNotIn(List<Integer> values) {
            addCriterion("MATCH_INTEGRAL not in", values, "matchIntegral");
            return (Criteria) this;
        }

        public Criteria andMatchIntegralBetween(Integer value1, Integer value2) {
            addCriterion("MATCH_INTEGRAL between", value1, value2, "matchIntegral");
            return (Criteria) this;
        }

        public Criteria andMatchIntegralNotBetween(Integer value1, Integer value2) {
            addCriterion("MATCH_INTEGRAL not between", value1, value2, "matchIntegral");
            return (Criteria) this;
        }

        public Criteria andWeeksIntegralIsNull() {
            addCriterion("WEEKS_INTEGRAL is null");
            return (Criteria) this;
        }

        public Criteria andWeeksIntegralIsNotNull() {
            addCriterion("WEEKS_INTEGRAL is not null");
            return (Criteria) this;
        }

        public Criteria andWeeksIntegralEqualTo(Integer value) {
            addCriterion("WEEKS_INTEGRAL =", value, "weeksIntegral");
            return (Criteria) this;
        }

        public Criteria andWeeksIntegralNotEqualTo(Integer value) {
            addCriterion("WEEKS_INTEGRAL <>", value, "weeksIntegral");
            return (Criteria) this;
        }

        public Criteria andWeeksIntegralGreaterThan(Integer value) {
            addCriterion("WEEKS_INTEGRAL >", value, "weeksIntegral");
            return (Criteria) this;
        }

        public Criteria andWeeksIntegralGreaterThanOrEqualTo(Integer value) {
            addCriterion("WEEKS_INTEGRAL >=", value, "weeksIntegral");
            return (Criteria) this;
        }

        public Criteria andWeeksIntegralLessThan(Integer value) {
            addCriterion("WEEKS_INTEGRAL <", value, "weeksIntegral");
            return (Criteria) this;
        }

        public Criteria andWeeksIntegralLessThanOrEqualTo(Integer value) {
            addCriterion("WEEKS_INTEGRAL <=", value, "weeksIntegral");
            return (Criteria) this;
        }

        public Criteria andWeeksIntegralIn(List<Integer> values) {
            addCriterion("WEEKS_INTEGRAL in", values, "weeksIntegral");
            return (Criteria) this;
        }

        public Criteria andWeeksIntegralNotIn(List<Integer> values) {
            addCriterion("WEEKS_INTEGRAL not in", values, "weeksIntegral");
            return (Criteria) this;
        }

        public Criteria andWeeksIntegralBetween(Integer value1, Integer value2) {
            addCriterion("WEEKS_INTEGRAL between", value1, value2, "weeksIntegral");
            return (Criteria) this;
        }

        public Criteria andWeeksIntegralNotBetween(Integer value1, Integer value2) {
            addCriterion("WEEKS_INTEGRAL not between", value1, value2, "weeksIntegral");
            return (Criteria) this;
        }

        public Criteria andMonthIntegralIsNull() {
            addCriterion("MONTH_INTEGRAL is null");
            return (Criteria) this;
        }

        public Criteria andMonthIntegralIsNotNull() {
            addCriterion("MONTH_INTEGRAL is not null");
            return (Criteria) this;
        }

        public Criteria andMonthIntegralEqualTo(Integer value) {
            addCriterion("MONTH_INTEGRAL =", value, "monthIntegral");
            return (Criteria) this;
        }

        public Criteria andMonthIntegralNotEqualTo(Integer value) {
            addCriterion("MONTH_INTEGRAL <>", value, "monthIntegral");
            return (Criteria) this;
        }

        public Criteria andMonthIntegralGreaterThan(Integer value) {
            addCriterion("MONTH_INTEGRAL >", value, "monthIntegral");
            return (Criteria) this;
        }

        public Criteria andMonthIntegralGreaterThanOrEqualTo(Integer value) {
            addCriterion("MONTH_INTEGRAL >=", value, "monthIntegral");
            return (Criteria) this;
        }

        public Criteria andMonthIntegralLessThan(Integer value) {
            addCriterion("MONTH_INTEGRAL <", value, "monthIntegral");
            return (Criteria) this;
        }

        public Criteria andMonthIntegralLessThanOrEqualTo(Integer value) {
            addCriterion("MONTH_INTEGRAL <=", value, "monthIntegral");
            return (Criteria) this;
        }

        public Criteria andMonthIntegralIn(List<Integer> values) {
            addCriterion("MONTH_INTEGRAL in", values, "monthIntegral");
            return (Criteria) this;
        }

        public Criteria andMonthIntegralNotIn(List<Integer> values) {
            addCriterion("MONTH_INTEGRAL not in", values, "monthIntegral");
            return (Criteria) this;
        }

        public Criteria andMonthIntegralBetween(Integer value1, Integer value2) {
            addCriterion("MONTH_INTEGRAL between", value1, value2, "monthIntegral");
            return (Criteria) this;
        }

        public Criteria andMonthIntegralNotBetween(Integer value1, Integer value2) {
            addCriterion("MONTH_INTEGRAL not between", value1, value2, "monthIntegral");
            return (Criteria) this;
        }

        public Criteria andRoomPersionTypeIsNull() {
            addCriterion("ROOM_PERSION_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andRoomPersionTypeIsNotNull() {
            addCriterion("ROOM_PERSION_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andRoomPersionTypeEqualTo(Integer value) {
            addCriterion("ROOM_PERSION_TYPE =", value, "roomPersionType");
            return (Criteria) this;
        }

        public Criteria andRoomPersionTypeNotEqualTo(Integer value) {
            addCriterion("ROOM_PERSION_TYPE <>", value, "roomPersionType");
            return (Criteria) this;
        }

        public Criteria andRoomPersionTypeGreaterThan(Integer value) {
            addCriterion("ROOM_PERSION_TYPE >", value, "roomPersionType");
            return (Criteria) this;
        }

        public Criteria andRoomPersionTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("ROOM_PERSION_TYPE >=", value, "roomPersionType");
            return (Criteria) this;
        }

        public Criteria andRoomPersionTypeLessThan(Integer value) {
            addCriterion("ROOM_PERSION_TYPE <", value, "roomPersionType");
            return (Criteria) this;
        }

        public Criteria andRoomPersionTypeLessThanOrEqualTo(Integer value) {
            addCriterion("ROOM_PERSION_TYPE <=", value, "roomPersionType");
            return (Criteria) this;
        }

        public Criteria andRoomPersionTypeIn(List<Integer> values) {
            addCriterion("ROOM_PERSION_TYPE in", values, "roomPersionType");
            return (Criteria) this;
        }

        public Criteria andRoomPersionTypeNotIn(List<Integer> values) {
            addCriterion("ROOM_PERSION_TYPE not in", values, "roomPersionType");
            return (Criteria) this;
        }

        public Criteria andRoomPersionTypeBetween(Integer value1, Integer value2) {
            addCriterion("ROOM_PERSION_TYPE between", value1, value2, "roomPersionType");
            return (Criteria) this;
        }

        public Criteria andRoomPersionTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("ROOM_PERSION_TYPE not between", value1, value2, "roomPersionType");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNull() {
            addCriterion("ROOM_ID is null");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNotNull() {
            addCriterion("ROOM_ID is not null");
            return (Criteria) this;
        }

        public Criteria andRoomIdEqualTo(Integer value) {
            addCriterion("ROOM_ID =", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotEqualTo(Integer value) {
            addCriterion("ROOM_ID <>", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThan(Integer value) {
            addCriterion("ROOM_ID >", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("ROOM_ID >=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThan(Integer value) {
            addCriterion("ROOM_ID <", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThanOrEqualTo(Integer value) {
            addCriterion("ROOM_ID <=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdIn(List<Integer> values) {
            addCriterion("ROOM_ID in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotIn(List<Integer> values) {
            addCriterion("ROOM_ID not in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdBetween(Integer value1, Integer value2) {
            addCriterion("ROOM_ID between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotBetween(Integer value1, Integer value2) {
            addCriterion("ROOM_ID not between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andIdouIsNull() {
            addCriterion("IDOU is null");
            return (Criteria) this;
        }

        public Criteria andIdouIsNotNull() {
            addCriterion("IDOU is not null");
            return (Criteria) this;
        }

        public Criteria andIdouEqualTo(Integer value) {
            addCriterion("IDOU =", value, "idou");
            return (Criteria) this;
        }

        public Criteria andIdouNotEqualTo(Integer value) {
            addCriterion("IDOU <>", value, "idou");
            return (Criteria) this;
        }

        public Criteria andIdouGreaterThan(Integer value) {
            addCriterion("IDOU >", value, "idou");
            return (Criteria) this;
        }

        public Criteria andIdouGreaterThanOrEqualTo(Integer value) {
            addCriterion("IDOU >=", value, "idou");
            return (Criteria) this;
        }

        public Criteria andIdouLessThan(Integer value) {
            addCriterion("IDOU <", value, "idou");
            return (Criteria) this;
        }

        public Criteria andIdouLessThanOrEqualTo(Integer value) {
            addCriterion("IDOU <=", value, "idou");
            return (Criteria) this;
        }

        public Criteria andIdouIn(List<Integer> values) {
            addCriterion("IDOU in", values, "idou");
            return (Criteria) this;
        }

        public Criteria andIdouNotIn(List<Integer> values) {
            addCriterion("IDOU not in", values, "idou");
            return (Criteria) this;
        }

        public Criteria andIdouBetween(Integer value1, Integer value2) {
            addCriterion("IDOU between", value1, value2, "idou");
            return (Criteria) this;
        }

        public Criteria andIdouNotBetween(Integer value1, Integer value2) {
            addCriterion("IDOU not between", value1, value2, "idou");
            return (Criteria) this;
        }

        public Criteria andAddUpCountIsNull() {
            addCriterion("ADD_UP_COUNT is null");
            return (Criteria) this;
        }

        public Criteria andAddUpCountIsNotNull() {
            addCriterion("ADD_UP_COUNT is not null");
            return (Criteria) this;
        }

        public Criteria andAddUpCountEqualTo(Integer value) {
            addCriterion("ADD_UP_COUNT =", value, "addUpCount");
            return (Criteria) this;
        }

        public Criteria andAddUpCountNotEqualTo(Integer value) {
            addCriterion("ADD_UP_COUNT <>", value, "addUpCount");
            return (Criteria) this;
        }

        public Criteria andAddUpCountGreaterThan(Integer value) {
            addCriterion("ADD_UP_COUNT >", value, "addUpCount");
            return (Criteria) this;
        }

        public Criteria andAddUpCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("ADD_UP_COUNT >=", value, "addUpCount");
            return (Criteria) this;
        }

        public Criteria andAddUpCountLessThan(Integer value) {
            addCriterion("ADD_UP_COUNT <", value, "addUpCount");
            return (Criteria) this;
        }

        public Criteria andAddUpCountLessThanOrEqualTo(Integer value) {
            addCriterion("ADD_UP_COUNT <=", value, "addUpCount");
            return (Criteria) this;
        }

        public Criteria andAddUpCountIn(List<Integer> values) {
            addCriterion("ADD_UP_COUNT in", values, "addUpCount");
            return (Criteria) this;
        }

        public Criteria andAddUpCountNotIn(List<Integer> values) {
            addCriterion("ADD_UP_COUNT not in", values, "addUpCount");
            return (Criteria) this;
        }

        public Criteria andAddUpCountBetween(Integer value1, Integer value2) {
            addCriterion("ADD_UP_COUNT between", value1, value2, "addUpCount");
            return (Criteria) this;
        }

        public Criteria andAddUpCountNotBetween(Integer value1, Integer value2) {
            addCriterion("ADD_UP_COUNT not between", value1, value2, "addUpCount");
            return (Criteria) this;
        }

        public Criteria andAddUpTimeIsNull() {
            addCriterion("ADD_UP_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddUpTimeIsNotNull() {
            addCriterion("ADD_UP_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddUpTimeEqualTo(Integer value) {
            addCriterion("ADD_UP_TIME =", value, "addUpTime");
            return (Criteria) this;
        }

        public Criteria andAddUpTimeNotEqualTo(Integer value) {
            addCriterion("ADD_UP_TIME <>", value, "addUpTime");
            return (Criteria) this;
        }

        public Criteria andAddUpTimeGreaterThan(Integer value) {
            addCriterion("ADD_UP_TIME >", value, "addUpTime");
            return (Criteria) this;
        }

        public Criteria andAddUpTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("ADD_UP_TIME >=", value, "addUpTime");
            return (Criteria) this;
        }

        public Criteria andAddUpTimeLessThan(Integer value) {
            addCriterion("ADD_UP_TIME <", value, "addUpTime");
            return (Criteria) this;
        }

        public Criteria andAddUpTimeLessThanOrEqualTo(Integer value) {
            addCriterion("ADD_UP_TIME <=", value, "addUpTime");
            return (Criteria) this;
        }

        public Criteria andAddUpTimeIn(List<Integer> values) {
            addCriterion("ADD_UP_TIME in", values, "addUpTime");
            return (Criteria) this;
        }

        public Criteria andAddUpTimeNotIn(List<Integer> values) {
            addCriterion("ADD_UP_TIME not in", values, "addUpTime");
            return (Criteria) this;
        }

        public Criteria andAddUpTimeBetween(Integer value1, Integer value2) {
            addCriterion("ADD_UP_TIME between", value1, value2, "addUpTime");
            return (Criteria) this;
        }

        public Criteria andAddUpTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("ADD_UP_TIME not between", value1, value2, "addUpTime");
            return (Criteria) this;
        }

        public Criteria andWinningCountIsNull() {
            addCriterion("WINNING_COUNT is null");
            return (Criteria) this;
        }

        public Criteria andWinningCountIsNotNull() {
            addCriterion("WINNING_COUNT is not null");
            return (Criteria) this;
        }

        public Criteria andWinningCountEqualTo(Integer value) {
            addCriterion("WINNING_COUNT =", value, "winningCount");
            return (Criteria) this;
        }

        public Criteria andWinningCountNotEqualTo(Integer value) {
            addCriterion("WINNING_COUNT <>", value, "winningCount");
            return (Criteria) this;
        }

        public Criteria andWinningCountGreaterThan(Integer value) {
            addCriterion("WINNING_COUNT >", value, "winningCount");
            return (Criteria) this;
        }

        public Criteria andWinningCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("WINNING_COUNT >=", value, "winningCount");
            return (Criteria) this;
        }

        public Criteria andWinningCountLessThan(Integer value) {
            addCriterion("WINNING_COUNT <", value, "winningCount");
            return (Criteria) this;
        }

        public Criteria andWinningCountLessThanOrEqualTo(Integer value) {
            addCriterion("WINNING_COUNT <=", value, "winningCount");
            return (Criteria) this;
        }

        public Criteria andWinningCountIn(List<Integer> values) {
            addCriterion("WINNING_COUNT in", values, "winningCount");
            return (Criteria) this;
        }

        public Criteria andWinningCountNotIn(List<Integer> values) {
            addCriterion("WINNING_COUNT not in", values, "winningCount");
            return (Criteria) this;
        }

        public Criteria andWinningCountBetween(Integer value1, Integer value2) {
            addCriterion("WINNING_COUNT between", value1, value2, "winningCount");
            return (Criteria) this;
        }

        public Criteria andWinningCountNotBetween(Integer value1, Integer value2) {
            addCriterion("WINNING_COUNT not between", value1, value2, "winningCount");
            return (Criteria) this;
        }

        public Criteria andPoolCntIsNull() {
            addCriterion("POOL_CNT is null");
            return (Criteria) this;
        }

        public Criteria andPoolCntIsNotNull() {
            addCriterion("POOL_CNT is not null");
            return (Criteria) this;
        }

        public Criteria andPoolCntEqualTo(Integer value) {
            addCriterion("POOL_CNT =", value, "poolCnt");
            return (Criteria) this;
        }

        public Criteria andPoolCntNotEqualTo(Integer value) {
            addCriterion("POOL_CNT <>", value, "poolCnt");
            return (Criteria) this;
        }

        public Criteria andPoolCntGreaterThan(Integer value) {
            addCriterion("POOL_CNT >", value, "poolCnt");
            return (Criteria) this;
        }

        public Criteria andPoolCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("POOL_CNT >=", value, "poolCnt");
            return (Criteria) this;
        }

        public Criteria andPoolCntLessThan(Integer value) {
            addCriterion("POOL_CNT <", value, "poolCnt");
            return (Criteria) this;
        }

        public Criteria andPoolCntLessThanOrEqualTo(Integer value) {
            addCriterion("POOL_CNT <=", value, "poolCnt");
            return (Criteria) this;
        }

        public Criteria andPoolCntIn(List<Integer> values) {
            addCriterion("POOL_CNT in", values, "poolCnt");
            return (Criteria) this;
        }

        public Criteria andPoolCntNotIn(List<Integer> values) {
            addCriterion("POOL_CNT not in", values, "poolCnt");
            return (Criteria) this;
        }

        public Criteria andPoolCntBetween(Integer value1, Integer value2) {
            addCriterion("POOL_CNT between", value1, value2, "poolCnt");
            return (Criteria) this;
        }

        public Criteria andPoolCntNotBetween(Integer value1, Integer value2) {
            addCriterion("POOL_CNT not between", value1, value2, "poolCnt");
            return (Criteria) this;
        }

        public Criteria andPoolWinCntIsNull() {
            addCriterion("POOL_WIN_CNT is null");
            return (Criteria) this;
        }

        public Criteria andPoolWinCntIsNotNull() {
            addCriterion("POOL_WIN_CNT is not null");
            return (Criteria) this;
        }

        public Criteria andPoolWinCntEqualTo(Integer value) {
            addCriterion("POOL_WIN_CNT =", value, "poolWinCnt");
            return (Criteria) this;
        }

        public Criteria andPoolWinCntNotEqualTo(Integer value) {
            addCriterion("POOL_WIN_CNT <>", value, "poolWinCnt");
            return (Criteria) this;
        }

        public Criteria andPoolWinCntGreaterThan(Integer value) {
            addCriterion("POOL_WIN_CNT >", value, "poolWinCnt");
            return (Criteria) this;
        }

        public Criteria andPoolWinCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("POOL_WIN_CNT >=", value, "poolWinCnt");
            return (Criteria) this;
        }

        public Criteria andPoolWinCntLessThan(Integer value) {
            addCriterion("POOL_WIN_CNT <", value, "poolWinCnt");
            return (Criteria) this;
        }

        public Criteria andPoolWinCntLessThanOrEqualTo(Integer value) {
            addCriterion("POOL_WIN_CNT <=", value, "poolWinCnt");
            return (Criteria) this;
        }

        public Criteria andPoolWinCntIn(List<Integer> values) {
            addCriterion("POOL_WIN_CNT in", values, "poolWinCnt");
            return (Criteria) this;
        }

        public Criteria andPoolWinCntNotIn(List<Integer> values) {
            addCriterion("POOL_WIN_CNT not in", values, "poolWinCnt");
            return (Criteria) this;
        }

        public Criteria andPoolWinCntBetween(Integer value1, Integer value2) {
            addCriterion("POOL_WIN_CNT between", value1, value2, "poolWinCnt");
            return (Criteria) this;
        }

        public Criteria andPoolWinCntNotBetween(Integer value1, Integer value2) {
            addCriterion("POOL_WIN_CNT not between", value1, value2, "poolWinCnt");
            return (Criteria) this;
        }

        public Criteria andHandCntIsNull() {
            addCriterion("HAND_CNT is null");
            return (Criteria) this;
        }

        public Criteria andHandCntIsNotNull() {
            addCriterion("HAND_CNT is not null");
            return (Criteria) this;
        }

        public Criteria andHandCntEqualTo(Integer value) {
            addCriterion("HAND_CNT =", value, "handCnt");
            return (Criteria) this;
        }

        public Criteria andHandCntNotEqualTo(Integer value) {
            addCriterion("HAND_CNT <>", value, "handCnt");
            return (Criteria) this;
        }

        public Criteria andHandCntGreaterThan(Integer value) {
            addCriterion("HAND_CNT >", value, "handCnt");
            return (Criteria) this;
        }

        public Criteria andHandCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("HAND_CNT >=", value, "handCnt");
            return (Criteria) this;
        }

        public Criteria andHandCntLessThan(Integer value) {
            addCriterion("HAND_CNT <", value, "handCnt");
            return (Criteria) this;
        }

        public Criteria andHandCntLessThanOrEqualTo(Integer value) {
            addCriterion("HAND_CNT <=", value, "handCnt");
            return (Criteria) this;
        }

        public Criteria andHandCntIn(List<Integer> values) {
            addCriterion("HAND_CNT in", values, "handCnt");
            return (Criteria) this;
        }

        public Criteria andHandCntNotIn(List<Integer> values) {
            addCriterion("HAND_CNT not in", values, "handCnt");
            return (Criteria) this;
        }

        public Criteria andHandCntBetween(Integer value1, Integer value2) {
            addCriterion("HAND_CNT between", value1, value2, "handCnt");
            return (Criteria) this;
        }

        public Criteria andHandCntNotBetween(Integer value1, Integer value2) {
            addCriterion("HAND_CNT not between", value1, value2, "handCnt");
            return (Criteria) this;
        }

        public Criteria andGameCntIsNull() {
            addCriterion("GAME_CNT is null");
            return (Criteria) this;
        }

        public Criteria andGameCntIsNotNull() {
            addCriterion("GAME_CNT is not null");
            return (Criteria) this;
        }

        public Criteria andGameCntEqualTo(Integer value) {
            addCriterion("GAME_CNT =", value, "gameCnt");
            return (Criteria) this;
        }

        public Criteria andGameCntNotEqualTo(Integer value) {
            addCriterion("GAME_CNT <>", value, "gameCnt");
            return (Criteria) this;
        }

        public Criteria andGameCntGreaterThan(Integer value) {
            addCriterion("GAME_CNT >", value, "gameCnt");
            return (Criteria) this;
        }

        public Criteria andGameCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("GAME_CNT >=", value, "gameCnt");
            return (Criteria) this;
        }

        public Criteria andGameCntLessThan(Integer value) {
            addCriterion("GAME_CNT <", value, "gameCnt");
            return (Criteria) this;
        }

        public Criteria andGameCntLessThanOrEqualTo(Integer value) {
            addCriterion("GAME_CNT <=", value, "gameCnt");
            return (Criteria) this;
        }

        public Criteria andGameCntIn(List<Integer> values) {
            addCriterion("GAME_CNT in", values, "gameCnt");
            return (Criteria) this;
        }

        public Criteria andGameCntNotIn(List<Integer> values) {
            addCriterion("GAME_CNT not in", values, "gameCnt");
            return (Criteria) this;
        }

        public Criteria andGameCntBetween(Integer value1, Integer value2) {
            addCriterion("GAME_CNT between", value1, value2, "gameCnt");
            return (Criteria) this;
        }

        public Criteria andGameCntNotBetween(Integer value1, Integer value2) {
            addCriterion("GAME_CNT not between", value1, value2, "gameCnt");
            return (Criteria) this;
        }

        public Criteria andUserTypeIsNull() {
            addCriterion("USER_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andUserTypeIsNotNull() {
            addCriterion("USER_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andUserTypeEqualTo(Integer value) {
            addCriterion("USER_TYPE =", value, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeNotEqualTo(Integer value) {
            addCriterion("USER_TYPE <>", value, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeGreaterThan(Integer value) {
            addCriterion("USER_TYPE >", value, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("USER_TYPE >=", value, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeLessThan(Integer value) {
            addCriterion("USER_TYPE <", value, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeLessThanOrEqualTo(Integer value) {
            addCriterion("USER_TYPE <=", value, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeIn(List<Integer> values) {
            addCriterion("USER_TYPE in", values, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeNotIn(List<Integer> values) {
            addCriterion("USER_TYPE not in", values, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeBetween(Integer value1, Integer value2) {
            addCriterion("USER_TYPE between", value1, value2, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("USER_TYPE not between", value1, value2, "userType");
            return (Criteria) this;
        }

        public Criteria andUpMsgIdIsNull() {
            addCriterion("UP_MSG_ID is null");
            return (Criteria) this;
        }

        public Criteria andUpMsgIdIsNotNull() {
            addCriterion("UP_MSG_ID is not null");
            return (Criteria) this;
        }

        public Criteria andUpMsgIdEqualTo(Integer value) {
            addCriterion("UP_MSG_ID =", value, "upMsgId");
            return (Criteria) this;
        }

        public Criteria andUpMsgIdNotEqualTo(Integer value) {
            addCriterion("UP_MSG_ID <>", value, "upMsgId");
            return (Criteria) this;
        }

        public Criteria andUpMsgIdGreaterThan(Integer value) {
            addCriterion("UP_MSG_ID >", value, "upMsgId");
            return (Criteria) this;
        }

        public Criteria andUpMsgIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("UP_MSG_ID >=", value, "upMsgId");
            return (Criteria) this;
        }

        public Criteria andUpMsgIdLessThan(Integer value) {
            addCriterion("UP_MSG_ID <", value, "upMsgId");
            return (Criteria) this;
        }

        public Criteria andUpMsgIdLessThanOrEqualTo(Integer value) {
            addCriterion("UP_MSG_ID <=", value, "upMsgId");
            return (Criteria) this;
        }

        public Criteria andUpMsgIdIn(List<Integer> values) {
            addCriterion("UP_MSG_ID in", values, "upMsgId");
            return (Criteria) this;
        }

        public Criteria andUpMsgIdNotIn(List<Integer> values) {
            addCriterion("UP_MSG_ID not in", values, "upMsgId");
            return (Criteria) this;
        }

        public Criteria andUpMsgIdBetween(Integer value1, Integer value2) {
            addCriterion("UP_MSG_ID between", value1, value2, "upMsgId");
            return (Criteria) this;
        }

        public Criteria andUpMsgIdNotBetween(Integer value1, Integer value2) {
            addCriterion("UP_MSG_ID not between", value1, value2, "upMsgId");
            return (Criteria) this;
        }

        public Criteria andDayIndexIsNull() {
            addCriterion("DAY_INDEX is null");
            return (Criteria) this;
        }

        public Criteria andDayIndexIsNotNull() {
            addCriterion("DAY_INDEX is not null");
            return (Criteria) this;
        }

        public Criteria andDayIndexEqualTo(Integer value) {
            addCriterion("DAY_INDEX =", value, "dayIndex");
            return (Criteria) this;
        }

        public Criteria andDayIndexNotEqualTo(Integer value) {
            addCriterion("DAY_INDEX <>", value, "dayIndex");
            return (Criteria) this;
        }

        public Criteria andDayIndexGreaterThan(Integer value) {
            addCriterion("DAY_INDEX >", value, "dayIndex");
            return (Criteria) this;
        }

        public Criteria andDayIndexGreaterThanOrEqualTo(Integer value) {
            addCriterion("DAY_INDEX >=", value, "dayIndex");
            return (Criteria) this;
        }

        public Criteria andDayIndexLessThan(Integer value) {
            addCriterion("DAY_INDEX <", value, "dayIndex");
            return (Criteria) this;
        }

        public Criteria andDayIndexLessThanOrEqualTo(Integer value) {
            addCriterion("DAY_INDEX <=", value, "dayIndex");
            return (Criteria) this;
        }

        public Criteria andDayIndexIn(List<Integer> values) {
            addCriterion("DAY_INDEX in", values, "dayIndex");
            return (Criteria) this;
        }

        public Criteria andDayIndexNotIn(List<Integer> values) {
            addCriterion("DAY_INDEX not in", values, "dayIndex");
            return (Criteria) this;
        }

        public Criteria andDayIndexBetween(Integer value1, Integer value2) {
            addCriterion("DAY_INDEX between", value1, value2, "dayIndex");
            return (Criteria) this;
        }

        public Criteria andDayIndexNotBetween(Integer value1, Integer value2) {
            addCriterion("DAY_INDEX not between", value1, value2, "dayIndex");
            return (Criteria) this;
        }

        public Criteria andDayLoginDateIsNull() {
            addCriterion("DAY_LOGIN_DATE is null");
            return (Criteria) this;
        }

        public Criteria andDayLoginDateIsNotNull() {
            addCriterion("DAY_LOGIN_DATE is not null");
            return (Criteria) this;
        }

        public Criteria andDayLoginDateEqualTo(LocalDateTime value) {
            addCriterion("DAY_LOGIN_DATE =", value, "dayLoginDate");
            return (Criteria) this;
        }

        public Criteria andDayLoginDateNotEqualTo(LocalDateTime value) {
            addCriterion("DAY_LOGIN_DATE <>", value, "dayLoginDate");
            return (Criteria) this;
        }

        public Criteria andDayLoginDateGreaterThan(LocalDateTime value) {
            addCriterion("DAY_LOGIN_DATE >", value, "dayLoginDate");
            return (Criteria) this;
        }

        public Criteria andDayLoginDateGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("DAY_LOGIN_DATE >=", value, "dayLoginDate");
            return (Criteria) this;
        }

        public Criteria andDayLoginDateLessThan(LocalDateTime value) {
            addCriterion("DAY_LOGIN_DATE <", value, "dayLoginDate");
            return (Criteria) this;
        }

        public Criteria andDayLoginDateLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("DAY_LOGIN_DATE <=", value, "dayLoginDate");
            return (Criteria) this;
        }

        public Criteria andDayLoginDateIn(List<LocalDateTime> values) {
            addCriterion("DAY_LOGIN_DATE in", values, "dayLoginDate");
            return (Criteria) this;
        }

        public Criteria andDayLoginDateNotIn(List<LocalDateTime> values) {
            addCriterion("DAY_LOGIN_DATE not in", values, "dayLoginDate");
            return (Criteria) this;
        }

        public Criteria andDayLoginDateBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("DAY_LOGIN_DATE between", value1, value2, "dayLoginDate");
            return (Criteria) this;
        }

        public Criteria andDayLoginDateNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("DAY_LOGIN_DATE not between", value1, value2, "dayLoginDate");
            return (Criteria) this;
        }

        public Criteria andBirthdayIsNull() {
            addCriterion("birthday is null");
            return (Criteria) this;
        }

        public Criteria andBirthdayIsNotNull() {
            addCriterion("birthday is not null");
            return (Criteria) this;
        }

        public Criteria andBirthdayEqualTo(LocalDate value) {
            addCriterion("birthday =", value, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayNotEqualTo(LocalDate value) {
            addCriterion("birthday <>", value, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayGreaterThan(LocalDate value) {
            addCriterion("birthday >", value, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayGreaterThanOrEqualTo(LocalDate value) {
            addCriterion("birthday >=", value, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayLessThan(LocalDate value) {
            addCriterion("birthday <", value, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayLessThanOrEqualTo(LocalDate value) {
            addCriterion("birthday <=", value, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayIn(List<LocalDate> values) {
            addCriterion("birthday in", values, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayNotIn(List<LocalDate> values) {
            addCriterion("birthday not in", values, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayBetween(LocalDate value1, LocalDate value2) {
            addCriterion("birthday between", value1, value2, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayNotBetween(LocalDate value1, LocalDate value2) {
            addCriterion("birthday not between", value1, value2, "birthday");
            return (Criteria) this;
        }

        public Criteria andPersonSignIsNull() {
            addCriterion("PERSON_SIGN is null");
            return (Criteria) this;
        }

        public Criteria andPersonSignIsNotNull() {
            addCriterion("PERSON_SIGN is not null");
            return (Criteria) this;
        }

        public Criteria andPersonSignEqualTo(String value) {
            addCriterion("PERSON_SIGN =", value, "personSign");
            return (Criteria) this;
        }

        public Criteria andPersonSignNotEqualTo(String value) {
            addCriterion("PERSON_SIGN <>", value, "personSign");
            return (Criteria) this;
        }

        public Criteria andPersonSignGreaterThan(String value) {
            addCriterion("PERSON_SIGN >", value, "personSign");
            return (Criteria) this;
        }

        public Criteria andPersonSignGreaterThanOrEqualTo(String value) {
            addCriterion("PERSON_SIGN >=", value, "personSign");
            return (Criteria) this;
        }

        public Criteria andPersonSignLessThan(String value) {
            addCriterion("PERSON_SIGN <", value, "personSign");
            return (Criteria) this;
        }

        public Criteria andPersonSignLessThanOrEqualTo(String value) {
            addCriterion("PERSON_SIGN <=", value, "personSign");
            return (Criteria) this;
        }

        public Criteria andPersonSignLike(String value) {
            addCriterion("PERSON_SIGN like", value, "personSign");
            return (Criteria) this;
        }

        public Criteria andPersonSignNotLike(String value) {
            addCriterion("PERSON_SIGN not like", value, "personSign");
            return (Criteria) this;
        }

        public Criteria andPersonSignIn(List<String> values) {
            addCriterion("PERSON_SIGN in", values, "personSign");
            return (Criteria) this;
        }

        public Criteria andPersonSignNotIn(List<String> values) {
            addCriterion("PERSON_SIGN not in", values, "personSign");
            return (Criteria) this;
        }

        public Criteria andPersonSignBetween(String value1, String value2) {
            addCriterion("PERSON_SIGN between", value1, value2, "personSign");
            return (Criteria) this;
        }

        public Criteria andPersonSignNotBetween(String value1, String value2) {
            addCriterion("PERSON_SIGN not between", value1, value2, "personSign");
            return (Criteria) this;
        }

        public Criteria andModifyNameTimesIsNull() {
            addCriterion("MODIFY_NAME_TIMES is null");
            return (Criteria) this;
        }

        public Criteria andModifyNameTimesIsNotNull() {
            addCriterion("MODIFY_NAME_TIMES is not null");
            return (Criteria) this;
        }

        public Criteria andModifyNameTimesEqualTo(Integer value) {
            addCriterion("MODIFY_NAME_TIMES =", value, "modifyNameTimes");
            return (Criteria) this;
        }

        public Criteria andModifyNameTimesNotEqualTo(Integer value) {
            addCriterion("MODIFY_NAME_TIMES <>", value, "modifyNameTimes");
            return (Criteria) this;
        }

        public Criteria andModifyNameTimesGreaterThan(Integer value) {
            addCriterion("MODIFY_NAME_TIMES >", value, "modifyNameTimes");
            return (Criteria) this;
        }

        public Criteria andModifyNameTimesGreaterThanOrEqualTo(Integer value) {
            addCriterion("MODIFY_NAME_TIMES >=", value, "modifyNameTimes");
            return (Criteria) this;
        }

        public Criteria andModifyNameTimesLessThan(Integer value) {
            addCriterion("MODIFY_NAME_TIMES <", value, "modifyNameTimes");
            return (Criteria) this;
        }

        public Criteria andModifyNameTimesLessThanOrEqualTo(Integer value) {
            addCriterion("MODIFY_NAME_TIMES <=", value, "modifyNameTimes");
            return (Criteria) this;
        }

        public Criteria andModifyNameTimesIn(List<Integer> values) {
            addCriterion("MODIFY_NAME_TIMES in", values, "modifyNameTimes");
            return (Criteria) this;
        }

        public Criteria andModifyNameTimesNotIn(List<Integer> values) {
            addCriterion("MODIFY_NAME_TIMES not in", values, "modifyNameTimes");
            return (Criteria) this;
        }

        public Criteria andModifyNameTimesBetween(Integer value1, Integer value2) {
            addCriterion("MODIFY_NAME_TIMES between", value1, value2, "modifyNameTimes");
            return (Criteria) this;
        }

        public Criteria andModifyNameTimesNotBetween(Integer value1, Integer value2) {
            addCriterion("MODIFY_NAME_TIMES not between", value1, value2, "modifyNameTimes");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNull() {
            addCriterion("PHONE is null");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNotNull() {
            addCriterion("PHONE is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneEqualTo(String value) {
            addCriterion("PHONE =", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotEqualTo(String value) {
            addCriterion("PHONE <>", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThan(String value) {
            addCriterion("PHONE >", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("PHONE >=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThan(String value) {
            addCriterion("PHONE <", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThanOrEqualTo(String value) {
            addCriterion("PHONE <=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLike(String value) {
            addCriterion("PHONE like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotLike(String value) {
            addCriterion("PHONE not like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneIn(List<String> values) {
            addCriterion("PHONE in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotIn(List<String> values) {
            addCriterion("PHONE not in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneBetween(String value1, String value2) {
            addCriterion("PHONE between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotBetween(String value1, String value2) {
            addCriterion("PHONE not between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andCountryIsNull() {
            addCriterion("country is null");
            return (Criteria) this;
        }

        public Criteria andCountryIsNotNull() {
            addCriterion("country is not null");
            return (Criteria) this;
        }

        public Criteria andCountryEqualTo(String value) {
            addCriterion("country =", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotEqualTo(String value) {
            addCriterion("country <>", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryGreaterThan(String value) {
            addCriterion("country >", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryGreaterThanOrEqualTo(String value) {
            addCriterion("country >=", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLessThan(String value) {
            addCriterion("country <", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLessThanOrEqualTo(String value) {
            addCriterion("country <=", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLike(String value) {
            addCriterion("country like", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotLike(String value) {
            addCriterion("country not like", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryIn(List<String> values) {
            addCriterion("country in", values, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotIn(List<String> values) {
            addCriterion("country not in", values, "country");
            return (Criteria) this;
        }

        public Criteria andCountryBetween(String value1, String value2) {
            addCriterion("country between", value1, value2, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotBetween(String value1, String value2) {
            addCriterion("country not between", value1, value2, "country");
            return (Criteria) this;
        }

        public Criteria andCityIsNull() {
            addCriterion("city is null");
            return (Criteria) this;
        }

        public Criteria andCityIsNotNull() {
            addCriterion("city is not null");
            return (Criteria) this;
        }

        public Criteria andCityEqualTo(String value) {
            addCriterion("city =", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotEqualTo(String value) {
            addCriterion("city <>", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThan(String value) {
            addCriterion("city >", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThanOrEqualTo(String value) {
            addCriterion("city >=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThan(String value) {
            addCriterion("city <", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThanOrEqualTo(String value) {
            addCriterion("city <=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLike(String value) {
            addCriterion("city like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotLike(String value) {
            addCriterion("city not like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityIn(List<String> values) {
            addCriterion("city in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotIn(List<String> values) {
            addCriterion("city not in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityBetween(String value1, String value2) {
            addCriterion("city between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotBetween(String value1, String value2) {
            addCriterion("city not between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNull() {
            addCriterion("province is null");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNotNull() {
            addCriterion("province is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceEqualTo(String value) {
            addCriterion("province =", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotEqualTo(String value) {
            addCriterion("province <>", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThan(String value) {
            addCriterion("province >", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThanOrEqualTo(String value) {
            addCriterion("province >=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThan(String value) {
            addCriterion("province <", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThanOrEqualTo(String value) {
            addCriterion("province <=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLike(String value) {
            addCriterion("province like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotLike(String value) {
            addCriterion("province not like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceIn(List<String> values) {
            addCriterion("province in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotIn(List<String> values) {
            addCriterion("province not in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceBetween(String value1, String value2) {
            addCriterion("province between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotBetween(String value1, String value2) {
            addCriterion("province not between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andSpectrumCntIsNull() {
            addCriterion("spectrum_cnt is null");
            return (Criteria) this;
        }

        public Criteria andSpectrumCntIsNotNull() {
            addCriterion("spectrum_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andSpectrumCntEqualTo(Integer value) {
            addCriterion("spectrum_cnt =", value, "spectrumCnt");
            return (Criteria) this;
        }

        public Criteria andSpectrumCntNotEqualTo(Integer value) {
            addCriterion("spectrum_cnt <>", value, "spectrumCnt");
            return (Criteria) this;
        }

        public Criteria andSpectrumCntGreaterThan(Integer value) {
            addCriterion("spectrum_cnt >", value, "spectrumCnt");
            return (Criteria) this;
        }

        public Criteria andSpectrumCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("spectrum_cnt >=", value, "spectrumCnt");
            return (Criteria) this;
        }

        public Criteria andSpectrumCntLessThan(Integer value) {
            addCriterion("spectrum_cnt <", value, "spectrumCnt");
            return (Criteria) this;
        }

        public Criteria andSpectrumCntLessThanOrEqualTo(Integer value) {
            addCriterion("spectrum_cnt <=", value, "spectrumCnt");
            return (Criteria) this;
        }

        public Criteria andSpectrumCntIn(List<Integer> values) {
            addCriterion("spectrum_cnt in", values, "spectrumCnt");
            return (Criteria) this;
        }

        public Criteria andSpectrumCntNotIn(List<Integer> values) {
            addCriterion("spectrum_cnt not in", values, "spectrumCnt");
            return (Criteria) this;
        }

        public Criteria andSpectrumCntBetween(Integer value1, Integer value2) {
            addCriterion("spectrum_cnt between", value1, value2, "spectrumCnt");
            return (Criteria) this;
        }

        public Criteria andSpectrumCntNotBetween(Integer value1, Integer value2) {
            addCriterion("spectrum_cnt not between", value1, value2, "spectrumCnt");
            return (Criteria) this;
        }

        public Criteria andCoinIsNull() {
            addCriterion("coin is null");
            return (Criteria) this;
        }

        public Criteria andCoinIsNotNull() {
            addCriterion("coin is not null");
            return (Criteria) this;
        }

        public Criteria andCoinEqualTo(Integer value) {
            addCriterion("coin =", value, "coin");
            return (Criteria) this;
        }

        public Criteria andCoinNotEqualTo(Integer value) {
            addCriterion("coin <>", value, "coin");
            return (Criteria) this;
        }

        public Criteria andCoinGreaterThan(Integer value) {
            addCriterion("coin >", value, "coin");
            return (Criteria) this;
        }

        public Criteria andCoinGreaterThanOrEqualTo(Integer value) {
            addCriterion("coin >=", value, "coin");
            return (Criteria) this;
        }

        public Criteria andCoinLessThan(Integer value) {
            addCriterion("coin <", value, "coin");
            return (Criteria) this;
        }

        public Criteria andCoinLessThanOrEqualTo(Integer value) {
            addCriterion("coin <=", value, "coin");
            return (Criteria) this;
        }

        public Criteria andCoinIn(List<Integer> values) {
            addCriterion("coin in", values, "coin");
            return (Criteria) this;
        }

        public Criteria andCoinNotIn(List<Integer> values) {
            addCriterion("coin not in", values, "coin");
            return (Criteria) this;
        }

        public Criteria andCoinBetween(Integer value1, Integer value2) {
            addCriterion("coin between", value1, value2, "coin");
            return (Criteria) this;
        }

        public Criteria andCoinNotBetween(Integer value1, Integer value2) {
            addCriterion("coin not between", value1, value2, "coin");
            return (Criteria) this;
        }

        public Criteria andPasswordIsNull() {
            addCriterion("password is null");
            return (Criteria) this;
        }

        public Criteria andPasswordIsNotNull() {
            addCriterion("password is not null");
            return (Criteria) this;
        }

        public Criteria andPasswordEqualTo(String value) {
            addCriterion("password =", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordNotEqualTo(String value) {
            addCriterion("password <>", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordGreaterThan(String value) {
            addCriterion("password >", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordGreaterThanOrEqualTo(String value) {
            addCriterion("password >=", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordLessThan(String value) {
            addCriterion("password <", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordLessThanOrEqualTo(String value) {
            addCriterion("password <=", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordLike(String value) {
            addCriterion("password like", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordNotLike(String value) {
            addCriterion("password not like", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordIn(List<String> values) {
            addCriterion("password in", values, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordNotIn(List<String> values) {
            addCriterion("password not in", values, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordBetween(String value1, String value2) {
            addCriterion("password between", value1, value2, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordNotBetween(String value1, String value2) {
            addCriterion("password not between", value1, value2, "password");
            return (Criteria) this;
        }

        public Criteria andSendCodeTimeIsNull() {
            addCriterion("send_code_time is null");
            return (Criteria) this;
        }

        public Criteria andSendCodeTimeIsNotNull() {
            addCriterion("send_code_time is not null");
            return (Criteria) this;
        }

        public Criteria andSendCodeTimeEqualTo(String value) {
            addCriterion("send_code_time =", value, "sendCodeTime");
            return (Criteria) this;
        }

        public Criteria andSendCodeTimeNotEqualTo(String value) {
            addCriterion("send_code_time <>", value, "sendCodeTime");
            return (Criteria) this;
        }

        public Criteria andSendCodeTimeGreaterThan(String value) {
            addCriterion("send_code_time >", value, "sendCodeTime");
            return (Criteria) this;
        }

        public Criteria andSendCodeTimeGreaterThanOrEqualTo(String value) {
            addCriterion("send_code_time >=", value, "sendCodeTime");
            return (Criteria) this;
        }

        public Criteria andSendCodeTimeLessThan(String value) {
            addCriterion("send_code_time <", value, "sendCodeTime");
            return (Criteria) this;
        }

        public Criteria andSendCodeTimeLessThanOrEqualTo(String value) {
            addCriterion("send_code_time <=", value, "sendCodeTime");
            return (Criteria) this;
        }

        public Criteria andSendCodeTimeLike(String value) {
            addCriterion("send_code_time like", value, "sendCodeTime");
            return (Criteria) this;
        }

        public Criteria andSendCodeTimeNotLike(String value) {
            addCriterion("send_code_time not like", value, "sendCodeTime");
            return (Criteria) this;
        }

        public Criteria andSendCodeTimeIn(List<String> values) {
            addCriterion("send_code_time in", values, "sendCodeTime");
            return (Criteria) this;
        }

        public Criteria andSendCodeTimeNotIn(List<String> values) {
            addCriterion("send_code_time not in", values, "sendCodeTime");
            return (Criteria) this;
        }

        public Criteria andSendCodeTimeBetween(String value1, String value2) {
            addCriterion("send_code_time between", value1, value2, "sendCodeTime");
            return (Criteria) this;
        }

        public Criteria andSendCodeTimeNotBetween(String value1, String value2) {
            addCriterion("send_code_time not between", value1, value2, "sendCodeTime");
            return (Criteria) this;
        }

        public Criteria andRandomNumIsNull() {
            addCriterion("random_num is null");
            return (Criteria) this;
        }

        public Criteria andRandomNumIsNotNull() {
            addCriterion("random_num is not null");
            return (Criteria) this;
        }

        public Criteria andRandomNumEqualTo(String value) {
            addCriterion("random_num =", value, "randomNum");
            return (Criteria) this;
        }

        public Criteria andRandomNumNotEqualTo(String value) {
            addCriterion("random_num <>", value, "randomNum");
            return (Criteria) this;
        }

        public Criteria andRandomNumGreaterThan(String value) {
            addCriterion("random_num >", value, "randomNum");
            return (Criteria) this;
        }

        public Criteria andRandomNumGreaterThanOrEqualTo(String value) {
            addCriterion("random_num >=", value, "randomNum");
            return (Criteria) this;
        }

        public Criteria andRandomNumLessThan(String value) {
            addCriterion("random_num <", value, "randomNum");
            return (Criteria) this;
        }

        public Criteria andRandomNumLessThanOrEqualTo(String value) {
            addCriterion("random_num <=", value, "randomNum");
            return (Criteria) this;
        }

        public Criteria andRandomNumLike(String value) {
            addCriterion("random_num like", value, "randomNum");
            return (Criteria) this;
        }

        public Criteria andRandomNumNotLike(String value) {
            addCriterion("random_num not like", value, "randomNum");
            return (Criteria) this;
        }

        public Criteria andRandomNumIn(List<String> values) {
            addCriterion("random_num in", values, "randomNum");
            return (Criteria) this;
        }

        public Criteria andRandomNumNotIn(List<String> values) {
            addCriterion("random_num not in", values, "randomNum");
            return (Criteria) this;
        }

        public Criteria andRandomNumBetween(String value1, String value2) {
            addCriterion("random_num between", value1, value2, "randomNum");
            return (Criteria) this;
        }

        public Criteria andRandomNumNotBetween(String value1, String value2) {
            addCriterion("random_num not between", value1, value2, "randomNum");
            return (Criteria) this;
        }

        public Criteria andPayPasswordIsNull() {
            addCriterion("pay_password is null");
            return (Criteria) this;
        }

        public Criteria andPayPasswordIsNotNull() {
            addCriterion("pay_password is not null");
            return (Criteria) this;
        }

        public Criteria andPayPasswordEqualTo(String value) {
            addCriterion("pay_password =", value, "payPassword");
            return (Criteria) this;
        }

        public Criteria andPayPasswordNotEqualTo(String value) {
            addCriterion("pay_password <>", value, "payPassword");
            return (Criteria) this;
        }

        public Criteria andPayPasswordGreaterThan(String value) {
            addCriterion("pay_password >", value, "payPassword");
            return (Criteria) this;
        }

        public Criteria andPayPasswordGreaterThanOrEqualTo(String value) {
            addCriterion("pay_password >=", value, "payPassword");
            return (Criteria) this;
        }

        public Criteria andPayPasswordLessThan(String value) {
            addCriterion("pay_password <", value, "payPassword");
            return (Criteria) this;
        }

        public Criteria andPayPasswordLessThanOrEqualTo(String value) {
            addCriterion("pay_password <=", value, "payPassword");
            return (Criteria) this;
        }

        public Criteria andPayPasswordLike(String value) {
            addCriterion("pay_password like", value, "payPassword");
            return (Criteria) this;
        }

        public Criteria andPayPasswordNotLike(String value) {
            addCriterion("pay_password not like", value, "payPassword");
            return (Criteria) this;
        }

        public Criteria andPayPasswordIn(List<String> values) {
            addCriterion("pay_password in", values, "payPassword");
            return (Criteria) this;
        }

        public Criteria andPayPasswordNotIn(List<String> values) {
            addCriterion("pay_password not in", values, "payPassword");
            return (Criteria) this;
        }

        public Criteria andPayPasswordBetween(String value1, String value2) {
            addCriterion("pay_password between", value1, value2, "payPassword");
            return (Criteria) this;
        }

        public Criteria andPayPasswordNotBetween(String value1, String value2) {
            addCriterion("pay_password not between", value1, value2, "payPassword");
            return (Criteria) this;
        }

        public Criteria andPayPwdErrorTimeIsNull() {
            addCriterion("pay_pwd_error_time is null");
            return (Criteria) this;
        }

        public Criteria andPayPwdErrorTimeIsNotNull() {
            addCriterion("pay_pwd_error_time is not null");
            return (Criteria) this;
        }

        public Criteria andPayPwdErrorTimeEqualTo(Integer value) {
            addCriterion("pay_pwd_error_time =", value, "payPwdErrorTime");
            return (Criteria) this;
        }

        public Criteria andPayPwdErrorTimeNotEqualTo(Integer value) {
            addCriterion("pay_pwd_error_time <>", value, "payPwdErrorTime");
            return (Criteria) this;
        }

        public Criteria andPayPwdErrorTimeGreaterThan(Integer value) {
            addCriterion("pay_pwd_error_time >", value, "payPwdErrorTime");
            return (Criteria) this;
        }

        public Criteria andPayPwdErrorTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("pay_pwd_error_time >=", value, "payPwdErrorTime");
            return (Criteria) this;
        }

        public Criteria andPayPwdErrorTimeLessThan(Integer value) {
            addCriterion("pay_pwd_error_time <", value, "payPwdErrorTime");
            return (Criteria) this;
        }

        public Criteria andPayPwdErrorTimeLessThanOrEqualTo(Integer value) {
            addCriterion("pay_pwd_error_time <=", value, "payPwdErrorTime");
            return (Criteria) this;
        }

        public Criteria andPayPwdErrorTimeIn(List<Integer> values) {
            addCriterion("pay_pwd_error_time in", values, "payPwdErrorTime");
            return (Criteria) this;
        }

        public Criteria andPayPwdErrorTimeNotIn(List<Integer> values) {
            addCriterion("pay_pwd_error_time not in", values, "payPwdErrorTime");
            return (Criteria) this;
        }

        public Criteria andPayPwdErrorTimeBetween(Integer value1, Integer value2) {
            addCriterion("pay_pwd_error_time between", value1, value2, "payPwdErrorTime");
            return (Criteria) this;
        }

        public Criteria andPayPwdErrorTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("pay_pwd_error_time not between", value1, value2, "payPwdErrorTime");
            return (Criteria) this;
        }

        public Criteria andPayPwdStatusIsNull() {
            addCriterion("pay_pwd_status is null");
            return (Criteria) this;
        }

        public Criteria andPayPwdStatusIsNotNull() {
            addCriterion("pay_pwd_status is not null");
            return (Criteria) this;
        }

        public Criteria andPayPwdStatusEqualTo(Integer value) {
            addCriterion("pay_pwd_status =", value, "payPwdStatus");
            return (Criteria) this;
        }

        public Criteria andPayPwdStatusNotEqualTo(Integer value) {
            addCriterion("pay_pwd_status <>", value, "payPwdStatus");
            return (Criteria) this;
        }

        public Criteria andPayPwdStatusGreaterThan(Integer value) {
            addCriterion("pay_pwd_status >", value, "payPwdStatus");
            return (Criteria) this;
        }

        public Criteria andPayPwdStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("pay_pwd_status >=", value, "payPwdStatus");
            return (Criteria) this;
        }

        public Criteria andPayPwdStatusLessThan(Integer value) {
            addCriterion("pay_pwd_status <", value, "payPwdStatus");
            return (Criteria) this;
        }

        public Criteria andPayPwdStatusLessThanOrEqualTo(Integer value) {
            addCriterion("pay_pwd_status <=", value, "payPwdStatus");
            return (Criteria) this;
        }

        public Criteria andPayPwdStatusIn(List<Integer> values) {
            addCriterion("pay_pwd_status in", values, "payPwdStatus");
            return (Criteria) this;
        }

        public Criteria andPayPwdStatusNotIn(List<Integer> values) {
            addCriterion("pay_pwd_status not in", values, "payPwdStatus");
            return (Criteria) this;
        }

        public Criteria andPayPwdStatusBetween(Integer value1, Integer value2) {
            addCriterion("pay_pwd_status between", value1, value2, "payPwdStatus");
            return (Criteria) this;
        }

        public Criteria andPayPwdStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("pay_pwd_status not between", value1, value2, "payPwdStatus");
            return (Criteria) this;
        }

        public Criteria andPayPwdLockTimeIsNull() {
            addCriterion("pay_pwd_lock_time is null");
            return (Criteria) this;
        }

        public Criteria andPayPwdLockTimeIsNotNull() {
            addCriterion("pay_pwd_lock_time is not null");
            return (Criteria) this;
        }

        public Criteria andPayPwdLockTimeEqualTo(Long value) {
            addCriterion("pay_pwd_lock_time =", value, "payPwdLockTime");
            return (Criteria) this;
        }

        public Criteria andPayPwdLockTimeNotEqualTo(Long value) {
            addCriterion("pay_pwd_lock_time <>", value, "payPwdLockTime");
            return (Criteria) this;
        }

        public Criteria andPayPwdLockTimeGreaterThan(Long value) {
            addCriterion("pay_pwd_lock_time >", value, "payPwdLockTime");
            return (Criteria) this;
        }

        public Criteria andPayPwdLockTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("pay_pwd_lock_time >=", value, "payPwdLockTime");
            return (Criteria) this;
        }

        public Criteria andPayPwdLockTimeLessThan(Long value) {
            addCriterion("pay_pwd_lock_time <", value, "payPwdLockTime");
            return (Criteria) this;
        }

        public Criteria andPayPwdLockTimeLessThanOrEqualTo(Long value) {
            addCriterion("pay_pwd_lock_time <=", value, "payPwdLockTime");
            return (Criteria) this;
        }

        public Criteria andPayPwdLockTimeIn(List<Long> values) {
            addCriterion("pay_pwd_lock_time in", values, "payPwdLockTime");
            return (Criteria) this;
        }

        public Criteria andPayPwdLockTimeNotIn(List<Long> values) {
            addCriterion("pay_pwd_lock_time not in", values, "payPwdLockTime");
            return (Criteria) this;
        }

        public Criteria andPayPwdLockTimeBetween(Long value1, Long value2) {
            addCriterion("pay_pwd_lock_time between", value1, value2, "payPwdLockTime");
            return (Criteria) this;
        }

        public Criteria andPayPwdLockTimeNotBetween(Long value1, Long value2) {
            addCriterion("pay_pwd_lock_time not between", value1, value2, "payPwdLockTime");
            return (Criteria) this;
        }

        public Criteria andPicFrameIsNull() {
            addCriterion("pic_frame is null");
            return (Criteria) this;
        }

        public Criteria andPicFrameIsNotNull() {
            addCriterion("pic_frame is not null");
            return (Criteria) this;
        }

        public Criteria andPicFrameEqualTo(String value) {
            addCriterion("pic_frame =", value, "picFrame");
            return (Criteria) this;
        }

        public Criteria andPicFrameNotEqualTo(String value) {
            addCriterion("pic_frame <>", value, "picFrame");
            return (Criteria) this;
        }

        public Criteria andPicFrameGreaterThan(String value) {
            addCriterion("pic_frame >", value, "picFrame");
            return (Criteria) this;
        }

        public Criteria andPicFrameGreaterThanOrEqualTo(String value) {
            addCriterion("pic_frame >=", value, "picFrame");
            return (Criteria) this;
        }

        public Criteria andPicFrameLessThan(String value) {
            addCriterion("pic_frame <", value, "picFrame");
            return (Criteria) this;
        }

        public Criteria andPicFrameLessThanOrEqualTo(String value) {
            addCriterion("pic_frame <=", value, "picFrame");
            return (Criteria) this;
        }

        public Criteria andPicFrameLike(String value) {
            addCriterion("pic_frame like", value, "picFrame");
            return (Criteria) this;
        }

        public Criteria andPicFrameNotLike(String value) {
            addCriterion("pic_frame not like", value, "picFrame");
            return (Criteria) this;
        }

        public Criteria andPicFrameIn(List<String> values) {
            addCriterion("pic_frame in", values, "picFrame");
            return (Criteria) this;
        }

        public Criteria andPicFrameNotIn(List<String> values) {
            addCriterion("pic_frame not in", values, "picFrame");
            return (Criteria) this;
        }

        public Criteria andPicFrameBetween(String value1, String value2) {
            addCriterion("pic_frame between", value1, value2, "picFrame");
            return (Criteria) this;
        }

        public Criteria andPicFrameNotBetween(String value1, String value2) {
            addCriterion("pic_frame not between", value1, value2, "picFrame");
            return (Criteria) this;
        }

        public Criteria andPicEffectiveTimeIsNull() {
            addCriterion("pic_effective_time is null");
            return (Criteria) this;
        }

        public Criteria andPicEffectiveTimeIsNotNull() {
            addCriterion("pic_effective_time is not null");
            return (Criteria) this;
        }

        public Criteria andPicEffectiveTimeEqualTo(Long value) {
            addCriterion("pic_effective_time =", value, "picEffectiveTime");
            return (Criteria) this;
        }

        public Criteria andPicEffectiveTimeNotEqualTo(Long value) {
            addCriterion("pic_effective_time <>", value, "picEffectiveTime");
            return (Criteria) this;
        }

        public Criteria andPicEffectiveTimeGreaterThan(Long value) {
            addCriterion("pic_effective_time >", value, "picEffectiveTime");
            return (Criteria) this;
        }

        public Criteria andPicEffectiveTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("pic_effective_time >=", value, "picEffectiveTime");
            return (Criteria) this;
        }

        public Criteria andPicEffectiveTimeLessThan(Long value) {
            addCriterion("pic_effective_time <", value, "picEffectiveTime");
            return (Criteria) this;
        }

        public Criteria andPicEffectiveTimeLessThanOrEqualTo(Long value) {
            addCriterion("pic_effective_time <=", value, "picEffectiveTime");
            return (Criteria) this;
        }

        public Criteria andPicEffectiveTimeIn(List<Long> values) {
            addCriterion("pic_effective_time in", values, "picEffectiveTime");
            return (Criteria) this;
        }

        public Criteria andPicEffectiveTimeNotIn(List<Long> values) {
            addCriterion("pic_effective_time not in", values, "picEffectiveTime");
            return (Criteria) this;
        }

        public Criteria andPicEffectiveTimeBetween(Long value1, Long value2) {
            addCriterion("pic_effective_time between", value1, value2, "picEffectiveTime");
            return (Criteria) this;
        }

        public Criteria andPicEffectiveTimeNotBetween(Long value1, Long value2) {
            addCriterion("pic_effective_time not between", value1, value2, "picEffectiveTime");
            return (Criteria) this;
        }

        public Criteria andHonorNumIsNull() {
            addCriterion("honor_num is null");
            return (Criteria) this;
        }

        public Criteria andHonorNumIsNotNull() {
            addCriterion("honor_num is not null");
            return (Criteria) this;
        }

        public Criteria andHonorNumEqualTo(Long value) {
            addCriterion("honor_num =", value, "honorNum");
            return (Criteria) this;
        }

        public Criteria andHonorNumNotEqualTo(Long value) {
            addCriterion("honor_num <>", value, "honorNum");
            return (Criteria) this;
        }

        public Criteria andHonorNumGreaterThan(Long value) {
            addCriterion("honor_num >", value, "honorNum");
            return (Criteria) this;
        }

        public Criteria andHonorNumGreaterThanOrEqualTo(Long value) {
            addCriterion("honor_num >=", value, "honorNum");
            return (Criteria) this;
        }

        public Criteria andHonorNumLessThan(Long value) {
            addCriterion("honor_num <", value, "honorNum");
            return (Criteria) this;
        }

        public Criteria andHonorNumLessThanOrEqualTo(Long value) {
            addCriterion("honor_num <=", value, "honorNum");
            return (Criteria) this;
        }

        public Criteria andHonorNumIn(List<Long> values) {
            addCriterion("honor_num in", values, "honorNum");
            return (Criteria) this;
        }

        public Criteria andHonorNumNotIn(List<Long> values) {
            addCriterion("honor_num not in", values, "honorNum");
            return (Criteria) this;
        }

        public Criteria andHonorNumBetween(Long value1, Long value2) {
            addCriterion("honor_num between", value1, value2, "honorNum");
            return (Criteria) this;
        }

        public Criteria andHonorNumNotBetween(Long value1, Long value2) {
            addCriterion("honor_num not between", value1, value2, "honorNum");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(LocalDateTime value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(LocalDateTime value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(LocalDateTime value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(LocalDateTime value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<LocalDateTime> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<LocalDateTime> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table user_details_info
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table user_details_info
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}