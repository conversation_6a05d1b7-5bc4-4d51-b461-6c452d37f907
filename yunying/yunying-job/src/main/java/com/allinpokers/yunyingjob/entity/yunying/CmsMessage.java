package com.allinpokers.yunyingjob.entity.yunying;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * CMS消息  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CmsMessage {
    /**
     * 消息ID
     */
    @ApiModelProperty("消息ID")
    private String msgId;

    /**
     * 发送者ID
     */
    @ApiModelProperty("发送者ID")
    private String senderId;

    /**
     * 消息种类
     */
    @ApiModelProperty("消息种类")
    private Integer type;

    /**
     * 操作类型 0阅读 1处理
     */
    @ApiModelProperty("操作类型 0阅读 1处理")
    private Integer operationType;

    /**
     * json形式参数
     */
    @ApiModelProperty("json形式参数")
    private String params;

    /**
     * 状态 0未读/未处理 1已读/已处理
     */
    @ApiModelProperty("状态 0未读/未处理 1已读/已处理")
    private Integer status;

    /**
     * 跳转地址
     */
    @ApiModelProperty("跳转地址")
    private String redirectUrl;

    /**
     * 发送时间
     */
    @ApiModelProperty("发送时间")
    private LocalDateTime sendingTime;

    /**
     * 处理时间
     */
    @ApiModelProperty("处理时间")
    private LocalDateTime processingTime;
}