package com.allinpokers.yunyingjob.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * WarehouseAcquisition  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WarehouseAcquisition {
    /**
     * 采集时间
     */
    @ApiModelProperty("采集时间")
    private LocalDateTime acquisitionTime;

    /**
     * 用户仓_总金豆
     */
    @ApiModelProperty("用户仓_总金豆")
    private Long userChip;

    /**
     * 用户仓_锁定金豆
     */
    @ApiModelProperty("用户仓_锁定金豆")
    private Long userLockChip;

    /**
     * 用户仓_可提金豆数量
     */
    @ApiModelProperty("用户仓_可提金豆数量")
    private Long userExtractChip;

    /**
     * 用户仓_不可提金豆数量
     */
    @ApiModelProperty("用户仓_不可提金豆数量")
    private Long userNotExtractChip;

    /**
     * 基金仓
     */
    @ApiModelProperty("基金仓")
    private Long fundChip;

    /**
     * 营销账户仓
     */
    @ApiModelProperty("营销账户仓")
    private Long marketChip;

    /**
     * JPB仓
     */
    @ApiModelProperty("JPB仓")
    private Long jpbChip;

    /**
     * 彩池仓
     */
    @ApiModelProperty("彩池仓")
    private Long jackpotChip;

    /**
     * 系统仓
     */
    @ApiModelProperty("系统仓")
    private Long systemChip;

    /**
     * 代充渠道仓_总金豆
     */
    @ApiModelProperty("代充渠道仓_总金豆")
    private Long payChannelChip;

    /**
     * 代充渠道仓_未锁定金豆
     */
    @ApiModelProperty("代充渠道仓_未锁定金豆")
    private Long payChannelUnlockChip;

    /**
     * 代充渠道仓_锁定金豆
     */
    @ApiModelProperty("代充渠道仓_锁定金豆")
    private Long payChannelLockChip;

    /**
     * 手工充值仓
     */
    @ApiModelProperty("手工充值仓")
    private Long manualRechargeChip;

    /**
     * 中央仓（其他八个总和）
     */
    @ApiModelProperty("中央仓（其他八个总和）")
    private Long centerChip;

    /**
     * 房间金豆数
     */
    @ApiModelProperty("房间金豆数")
    private Long roomChip;

    /**
     * 奖池仓
     */
    @ApiModelProperty("奖池仓")
    private Long prizePoolChip;

    /**
     * 联盟币
     */
    @ApiModelProperty("联盟币")
    private Long tribeChip;

    /**
     * 金币
     */
    @ApiModelProperty("金币")
    private Long gold;

    /**
     * 联盟币分成
     */
    @ApiModelProperty("联盟币分成")
    private Long tribeChipInsurance;

    /**
     * 金币分成
     */
    @ApiModelProperty("金币分成")
    private Long goldInsurance;
}