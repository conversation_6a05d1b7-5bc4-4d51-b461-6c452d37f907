package com.allinpokers.yunyingjob.job;

import com.allinpokers.yunying.util.JsonUtils;
import com.allinpokers.yunyingjob.service.ClubService;
import com.allinpokers.yunyingjob.service.impl.AutoUserServiceImpl;
import com.allinpokers.yunyingjob.service.impl.UserServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class UserTasks {

    @Resource
    private UserServiceImpl userService;

    private int ii=0;
    @Resource
    private AutoUserServiceImpl autoUserService;
    /**
     * 定时处理创建用户的作业任务
     */
    @Scheduled(cron = "0 0/2 * * * ?  ")
    public void autoCreateUser() {
        userService.autoCreateUserJob();
    }

//    @Scheduled(cron = "0 0/1 * * * ?  ")
//    public void autoUserServiceTest() {
//
//        if(ii==1){
//            return;
//        }
//        ii=1;
//        List<String> tempList = new ArrayList<>();
//        Long start = System.currentTimeMillis();
//        for (int i = 0; i < 100; i++) {
//            tempList.add(autoUserService.getUserHeader(i));
//        }
//        Long end = System.currentTimeMillis();
//       log.info("end ={}   tempList.size()={}  tempList={}" ,(end - start),tempList.size(), JsonUtils.write(tempList));
//        System.out.println(tempList.size());
//
//    }

}
