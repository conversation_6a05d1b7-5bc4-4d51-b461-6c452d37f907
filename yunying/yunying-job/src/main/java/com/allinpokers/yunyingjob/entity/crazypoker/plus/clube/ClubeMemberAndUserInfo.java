package com.allinpokers.yunyingjob.entity.crazypoker.plus.clube;

import com.allinpokers.yunyingjob.entity.crazypoker.ClubMembers;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ClubeMemberAndUserInfo extends ClubMembers {

    @ApiModelProperty(value = "用户联系电话")
    private String phone;
    @ApiModelProperty(value = "用户昵称")
    private String nickName;
    @ApiModelProperty(value = "1正常登录 0禁止登陆 2封号后解禁")
    private Integer forbid;
    @ApiModelProperty(value = "用户显性id")
    private String randomNum;
    @ApiModelProperty(value = "用户金豆数量")
    private Integer chip;
}
