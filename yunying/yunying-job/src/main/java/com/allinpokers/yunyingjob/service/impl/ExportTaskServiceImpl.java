package com.allinpokers.yunyingjob.service.impl;

import com.allinpokers.yunyingjob.dao.crazypoker.ExportTaskLogDao;
import com.allinpokers.yunyingjob.export.mq.bean.ExportTaskMessage;
import com.allinpokers.yunyingjob.dao.crazypoker.ExportTaskDao;
import com.allinpokers.yunyingjob.export.task.domain.ExportTask;
import com.allinpokers.yunyingjob.export.task.domain.ExportTaskLog;
import com.allinpokers.yunyingjob.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 导出任务
 */
@Service
@Slf4j
public class ExportTaskServiceImpl implements ExportTaskService {

    @Resource
    private ExportTaskDao exportTaskDao;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private GameRecordService gameRecordService;

    @Resource
    private ChipLogService chipLogService;

    @Resource
    private OrdersService ordersService;

    @Resource
    private UserLoginLogService userLoginLogService;

    @Resource
    private ExportTaskLogDao exportTaskLogDao;

    @Resource
    TierService tierService;

    @Override
    public void insertExportTask(ExportTaskMessage exportTaskMessage) {
        ExportTask exportTask = ExportTask.builder()
                .clubId(exportTaskMessage.getClubId())
                .name(exportTaskMessage.getName())
                .tribeId(exportTaskMessage.getTribeId())
                .createdBy(exportTaskMessage.getCreatedBy())
                .sys(exportTaskMessage.getSys())
                .code(exportTaskMessage.getCode())
                .type(exportTaskMessage.getType())
                .params(exportTaskMessage.getParamsJson())
                .status(0)
                .build();
        exportTaskDao.insert(exportTask);
    }

    @Override
    public void exportTasksJob() {
        // 查询是否有正在导出的任务
        ExportTask exportTask = exportTaskDao.findOneRunningTask();
        if (exportTask != null) {
            // 判断导出中时间是否过长
            log.info("---有导出中任务---");
        } else {
            log.info("---获取待导出任务---");
            // 根据时间获取一条待处理任务
            exportTask = exportTaskDao.findOneWaitingTask();
            if (exportTask != null) {
                // 处理中
                int count = exportTaskDao.updateRunning(exportTask.getId());
                log.info("---请求锁定导出任务---count: {}", count);
                if (count == 1) {
                    log.info("成功锁定待导出任务");
                    exportTask.setStatus(1);
                    exportTaskLogDao.insert(ExportTaskLog.builder()
                            .taskId(exportTask.getId())
                            .status(1)
                            .message("开始执行任务")
                            .build());
                    doExportTask(exportTask);
                }
            }
        }
    }

    @Override
    public void exportTasksRetryJob() {
        // 查询是否有正在重试导出的任务
        ExportTask exportTask = exportTaskDao.findOneRetryingTask();
        if (exportTask != null) {
            // 判断导出中时间是否过长
            log.info("---有重试导出中任务---");
        } else {
            log.info("---获取待重试导出任务---");
            // 根据时间获取一条待处理任务
            exportTask = exportTaskDao.findOneRetryTask();
            if (exportTask != null) {
                // 处理中
                int count = exportTaskDao.updateRetrying(exportTask.getId());
                log.info("---请求锁定重试导出任务---count: {}", count);
                if (count == 1) {
                    log.info("成功锁定待重试导出任务");
                    exportTask.setStatus(1);
                    exportTaskLogDao.insert(ExportTaskLog.builder()
                            .taskId(exportTask.getId())
                            .status(1)
                            .message("开始执行重试任务")
                            .build());
                    doExportTask(exportTask);
                }
            }
        }
    }

    void doExportTask(ExportTask exportTask) {
        switch (exportTask.getCode()) {
            case 10001:
                // cms-俱乐部管理-战绩查询
                gameRecordService.cmsGameRecord(exportTask);
                break;
            case 10002:
                // cms-联盟管理-战绩查询
                gameRecordService.cmsTribeGameRecord(exportTask);
                break;
            case 10003:
                // CMS-俱乐部管理-房间记录
                gameRecordService.cmsRoomRecord(exportTask);
                break;
            case 10004:
                // CMS-联盟管理-房间记录
                gameRecordService.cmsTribeRoomRecord(exportTask);
                break;
            case 10005:
                // CMS-俱乐部联盟币明细
                chipLogService.cmsClubChipLog(exportTask);
                break;
            case 10006:
                // CMS-俱乐部管理-用户联盟币明细
                chipLogService.cmsUserChipLog(exportTask);
                break;
            case 10007:
                // CMS-联盟管理-俱乐部联盟币明细
                chipLogService.cmsClubChipLogForTribe(exportTask);
                break;
            case 10008:
                // CMS-俱乐部管理-联盟币订单记录
                ordersService.cmsClubOrders(exportTask);
                break;
            case 10009:
                // CMS-联盟管理-联盟币订单记录
                ordersService.cmsTribeOrders(exportTask);
                break;
            case 10010:
                // CMS-联盟管理-成员查询
                userLoginLogService.cmsUserLoginLog(exportTask);
                break;
            case 10011:
                // CMS-系统首页-钻石明细
                chipLogService.cmsUserDiamondLog(exportTask);
                break;
            case 10012:
                // CMS-系统首页-金币明细
                chipLogService.cmsUserGoldLog(exportTask);
                break;
            case 10013:
                // CMS-联盟管理-联盟币明细
                chipLogService.cmsTribeChipLog(exportTask);
                break;
            case 20001:
                log.info("test");
                break;
            case 11001:
                // CMS-联盟管理-成员分层-支付及活动成员列表
                tierService.exportPaymentActivityMembers(exportTask);
                break;
            default:
                log.error("exportTask => 业务编码不匹配, taskId: {}", exportTask.getId());
                exportTaskDao.updateFail(exportTask.getId(), "业务编码不匹配");
                break;
        }
    }

}
