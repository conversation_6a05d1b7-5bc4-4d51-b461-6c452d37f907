package com.allinpokers.yunyingjob.export.mq.bean;


import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

/**
 * DiamondLogResult
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class DiamondLogResult {

    @ApiModelProperty("时间")
    private Date createTime;

    @ApiModelProperty("变动类型")
    private Integer type;

    @ApiModelProperty("变动前")
    private Integer currentChip;

    @ApiModelProperty("变动数值")
    private Integer changeChip;

    @ApiModelProperty("房间名称")
    private String roomName;

    @ApiModelProperty("房间ID")
    private Integer roomId;

    @ApiModelProperty("用户名称")
    private String externalUserName;

    @ApiModelProperty("用户ID")
    private String externalUserRandomId;

    @ApiModelProperty("操作人")
    private String operatorName;

    @ApiModelProperty("备注")
    private String remark;
}
