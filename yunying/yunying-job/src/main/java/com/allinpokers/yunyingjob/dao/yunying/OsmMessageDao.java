package com.allinpokers.yunyingjob.dao.yunying;

import com.allinpokers.yunying.dao.BaseDao;
import com.allinpokers.yunyingjob.entity.yunying.OsmMessage;
import com.allinpokers.yunyingjob.entity.yunying.example.OsmMessageExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

/**
 * OSM消息  Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface OsmMessageDao extends BaseDao<OsmMessage, OsmMessageExample, String> {

    /**
     * 删除在这之前的数据和关联的
     *
     * @param time
     * @return
     */
    int deleteBeforeTime(@Param("time") LocalDateTime time);
}