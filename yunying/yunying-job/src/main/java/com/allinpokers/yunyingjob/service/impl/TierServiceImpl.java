package com.allinpokers.yunyingjob.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.allinpokers.yunyingjob.dao.crazypoker.ExportTaskDao;
import com.allinpokers.yunyingjob.dao.crazypoker.ExportTaskLogDao;
import com.allinpokers.yunyingjob.dao.crazypoker.TierDao;
import com.allinpokers.yunyingjob.entity.crazypoker.MemberPaymentActivityTier;
import com.allinpokers.yunyingjob.export.mq.bean.MemberTierQuery;
import com.allinpokers.yunyingjob.export.mq.bean.OrdersQuery;
import com.allinpokers.yunyingjob.export.task.domain.ExportTask;
import com.allinpokers.yunyingjob.export.task.domain.ExportTaskLog;
import com.allinpokers.yunyingjob.oss.service.OssService;
import com.allinpokers.yunyingjob.service.TierService;
import com.allinpokers.yunyingjob.util.excel.ExcelModel;
import com.allinpokers.yunyingjob.util.excel.ExcelRow;
import com.allinpokers.yunyingjob.util.excel.ExcelSheet;
import com.allinpokers.yunyingjob.util.excel.ExcelUtils;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * TierServiceImpl
 *
 * <AUTHOR>
 * @since 2025/6/23
 */
@Slf4j
@Service
public class TierServiceImpl implements TierService {


    @Resource
    private ExportTaskDao exportTaskDao;

    @Resource
    private ExportTaskLogDao exportTaskLogDao;

    @Resource
    private OssService ossService;

    @Value("${export.expiredConfig}")
    private String expiredConfig;


    @Resource
    TierDao tierDao;

    @Override
    public void exportPaymentActivityMembers(ExportTask exportTask) {
        try {
            log.info("exportTask==> {}", JSONObject.toJSONString(exportTask));
            MemberTierQuery queryParam = JSONObject.parseObject(exportTask.getParams(), MemberTierQuery.class);
            log.info("queryParam==> {}", JSONObject.toJSONString(queryParam));
            if (StringUtils.isEmpty(queryParam.getFileName())) {
                queryParam.setFileName("支付及活动层级-成员列表");
            }

            long total = tierDao.countPaymentActivityTierUser(queryParam);

            List<MemberPaymentActivityTier> list = new ArrayList<>();

            if (total > 0) {
                int pageNum = 1;
                int pageSize = 2000;
                int totalPage = (int) Math.ceil((double) total / pageSize);
                for (int i = 0; i < totalPage; i++) {
                    PageHelper.startPage(pageNum, pageSize);
                    List<MemberPaymentActivityTier> pageList = tierDao.listPaymentActivityTierUser(queryParam);
                    if (pageList != null && !pageList.isEmpty()) {
                        for (MemberPaymentActivityTier memberPaymentActivityTier : pageList) {
                            MemberPaymentActivityTier userCombined = tierDao.countUserCombined(
                                    memberPaymentActivityTier.getUserId(),
                                    memberPaymentActivityTier.getClubId(),
                                    memberPaymentActivityTier.getTribeId()
                            );
                            if (userCombined != null) {
                                memberPaymentActivityTier.setRechargeQuantity(userCombined.getRechargeQuantity());
                                memberPaymentActivityTier.setRechargeAmount(Math.abs(userCombined.getRechargeAmount() / 100L));
                                memberPaymentActivityTier.setWithdrawQuantity(userCombined.getWithdrawQuantity());
                                memberPaymentActivityTier.setWithdrawAmount(Math.abs(userCombined.getWithdrawAmount() / 100L));
                            } else {
                                memberPaymentActivityTier.setRechargeQuantity(0);
                                memberPaymentActivityTier.setRechargeAmount(0L);
                                memberPaymentActivityTier.setWithdrawQuantity(0);
                                memberPaymentActivityTier.setWithdrawAmount(0L);
                            }
                        }

                        // 将查询结果添加到总列表中
                        list.addAll(pageList);
                    }
                    pageNum++;
                }
            }

            ExcelSheet sheet = new ExcelSheet("层级成员列表");
            List<ExcelModel> models = new ArrayList<>();

            models.add(ExcelModel.builder()
                    .rows(Lists.newArrayList(new ExcelRow().add("记录总数：").add(total)))
                    .afterBlankLine(1)
                    .build());

            String[] title = new String[]{"序号", "玩家ID", "玩家名称", "俱乐部ID", "俱乐部名称", "充值笔数", "充值金额", "提款笔数", "提款金额", "当前分层"};

            List<ExcelRow> rows = new ArrayList<>();

            for (int i = 0; i < list.size(); i++) {
                MemberPaymentActivityTier info = list.get(i);
                ExcelRow row = new ExcelRow()
                        .add(i + 1)
                        // 玩家ID
                        .add(info.getRandomNum())
                        // 玩家名称
                        .add(info.getNickname())
                        // 俱乐部ID
                        .add(info.getClubRandomId())
                        // 俱乐部名称
                        .add(info.getClubName())
                        // 当前分层
                        .add(info.getRechargeQuantity())
                        .add(info.getRechargeAmount())
                        .add(info.getWithdrawQuantity())
                        .add(info.getWithdrawAmount())
                        .add(info.getTierName());
                rows.add(row);
            }

            models.add(ExcelModel.builder()
                    .titles(Lists.newArrayList(title))
                    .rows(rows)
                    .afterBlankLine(1)
                    .build());

            sheet.setModels(models);

            log.info("生成文件---");

            String dateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String time = String.valueOf(new Date().getTime());
            String random = time.substring(time.length() - 6);
            String filePath = "/" + queryParam.getFileName() + "_" + dateTime + random + ".xlsx";
            log.info("生成文件---filePath：{}", filePath);

            //创建一个Excel文件
            MultipartFile multipartFile;
            try (SXSSFWorkbook workbook = new SXSSFWorkbook(100)) {
                // 填充数据
                try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                    ExcelUtils.write(workbook, sheet);
                    workbook.write(outputStream);
                    workbook.dispose(); // 清理临时文件
                    byte[] excelBytes = outputStream.toByteArray();
                    // 构造 MultipartFile
                    // 转换逻辑
                    ByteArrayInputStream inputStream = new ByteArrayInputStream(excelBytes);
                    multipartFile = new MockMultipartFile(
                            "excelFile",          // 表单字段名（可自定义）
                            queryParam.getFileName() + "_" + dateTime + random + ".xlsx",        // 文件名（需包含扩展名）
                            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                            inputStream
                    );
                    log.info("转换文件类型---成功");
                }
            }

            log.info("转换文件类型---");
            if (multipartFile.getSize() <= 0) {
                log.error("导出任务失败-文件转换失败");
                exportTaskDao.updateFail(exportTask.getId(), "文件转换失败");
                return;
            }
            String path = ossService.uploadFile(multipartFile, true, exportTask.getCreatedBy(), 6);
            log.info("上传文件至oss---path：{}", path);

            int fileSize = Integer.parseInt(Long.toString(multipartFile.getSize()));
            LocalDateTime expiredAt = LocalDateTime.now().plusDays(expiredConfig != null ? Long.parseLong(expiredConfig) : 30L);
            log.info("导出任务==> {}-{}, 总条数：{}, 大小：{}, 过期时间：{}, path: {}", exportTask.getId(), filePath, total, fileSize, expiredAt, path);
            exportTaskDao.updateSuccess(exportTask.getId(), Integer.parseInt(Long.toString(total)), fileSize, filePath, path, expiredAt);
            exportTaskLogDao.insert(ExportTaskLog.builder()
                    .taskId(exportTask.getId())
                    .status(2)
                    .message("成功执行任务")
                    .build());
        } catch (Exception e) {
            log.error("导出任务失败-{}", e.getMessage());
            exportTaskDao.updateFail(exportTask.getId(), e.getMessage());
            exportTaskLogDao.insert(ExportTaskLog.builder()
                    .taskId(exportTask.getId())
                    .status(3)
                    .message("执行任务失败：" + e.getMessage())
                    .build());
            e.printStackTrace();
        }
    }

}
