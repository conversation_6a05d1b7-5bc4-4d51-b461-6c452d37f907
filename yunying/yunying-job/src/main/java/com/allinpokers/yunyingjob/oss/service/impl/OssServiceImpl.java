package com.allinpokers.yunyingjob.oss.service.impl;

import com.allinpokers.yunyingjob.oss.OssClientUtils;
import com.allinpokers.yunyingjob.oss.config.OssConfig;
import com.allinpokers.yunyingjob.oss.repositories.model.UploadFileRecord;
import com.allinpokers.yunyingjob.dao.crazypoker.IUploadFileRecordDao;
import com.allinpokers.yunyingjob.oss.service.OssService;
import com.allinpokers.yunyingjob.oss.uploader.FileUploader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j
public class OssServiceImpl implements OssService {

    @Autowired
    private OssConfig ossConfig;

    @Autowired
    private IUploadFileRecordDao uploadFileRecordDao;

    @Override
    public String uploadFile(MultipartFile file, Bo<PERSON>an fullPath, Integer operatorId, Integer fromType) {
        String filePath = "";
        log.info("--------上传文件--------- end: {}, buck: {}", ossConfig.getEndpoint(), ossConfig.getBucketName());
        FileUploader fileUploader = new FileUploader(ossConfig.getAccessKeyId(), ossConfig.getAccessKeySecret(), ossConfig.getEndpoint(), ossConfig.getBucketName());
        if (!file.isEmpty()) {
            log.info("------开始上传-------");
            filePath = fileUploader.multipartFileUpload(file);
            log.info("------上传完成-------filePath:{}", filePath);
            UploadFileRecord uploadFileRecord = new UploadFileRecord();
            uploadFileRecord.setFileName(filePath);
            uploadFileRecord.setFilePath(filePath);
            if (fullPath != null && fullPath) {
                log.info("------完整地址-------");
                filePath = OssClientUtils.getServerUrl(filePath, ossConfig.getEndpoint(), ossConfig.getBucketName());
            }
            if (!StringUtils.isEmpty(filePath)) {
                uploadFileRecord.setFileUrl(filePath);
                uploadFileRecord.setType(fromType);
                uploadFileRecord.setOperatorId(operatorId);
                uploadFileRecordDao.insertRecord(uploadFileRecord);
                log.info("-------插入上传记录--------");
            }
        }
        fileUploader.close();
        return filePath;
    }

    @Override
    public void deleteFile(String path) {
        if (StringUtils.isEmpty(path)) {
            log.info("资源地址为空");
            return;
        }
        FileUploader fileUploader = new FileUploader(ossConfig.getAccessKeyId(), ossConfig.getAccessKeySecret(), ossConfig.getEndpoint(), ossConfig.getBucketName());
        try {
            fileUploader.delete(path);
            log.info("删除资源成功： {}", path);
            // 删除上传记录
            uploadFileRecordDao.deleteRecord(path);
        } catch (Exception e) {
            log.error("删除资源错误： {}， error：{}", path, e.getMessage());
            e.printStackTrace();
        }
    }
}
