package com.allinpokers.yunyingjob.dao.crazypoker;

import com.allinpokers.yunying.dao.BaseDao;
import com.allinpokers.yunyingjob.entity.crazypoker.CmsAccount;
import com.allinpokers.yunyingjob.entity.crazypoker.example.CmsAccountExample;
import com.allinpokers.yunyingjob.service.model.CmsAccountSum;
import org.apache.ibatis.annotations.Mapper;

/**
 * CmsAccountDao  Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CmsAccountDao extends BaseDao<CmsAccount, CmsAccountExample, Integer> {

    /**
     * 代付渠道仓
     *
     * @return
     */
    CmsAccountSum sumChipAndFreezeChip();

}