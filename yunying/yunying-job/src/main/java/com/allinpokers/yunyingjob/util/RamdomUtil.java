package com.allinpokers.yunyingjob.util;

import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import java.util.Random;

public class RamdomUtil {

    /**
     * 随机字符串
     * @param length
     * @return
     */
    public static String getRanStr(int length) {
        String base = "abcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(base.length());
            sb.append(base.charAt(number));
        }
        return sb.toString();

    }


    /**
     * 随便机ip
     *
     * @return
     */
    public static String getRandomIp() {

        // ip范围
        int[][] range = {{607649792, 608174079}, // *********-*************
                {1038614528, 1039007743}, // **********-**************
                {1783627776, 1784676351}, // **********-**************
                {2035023872, 2035154943}, // **********-**************
                {2078801920, 2079064063}, // ***********-***************
                {-1950089216, -1948778497}, // ***********-***************
                {-1425539072, -1425014785}, // *********-**************
                {-1236271104, -1235419137}, // **********-**************
                {-770113536, -768606209}, // **********-**************
                {-569376768, -564133889}, // **********-**************
        };

        Random rdint = new Random();
        int index = rdint.nextInt(10);
        String ip = num2ip(range[index][0] + new Random().nextInt(range[index][1] - range[index][0]));
        return ip;
    }

    /*
     * 将十进制转换成IP地址
     */
    public static String num2ip(int ip) {
        int[] b = new int[4];
        String x = "";
        b[0] = (int) ((ip >> 24) & 0xff);
        b[1] = (int) ((ip >> 16) & 0xff);
        b[2] = (int) ((ip >> 8) & 0xff);
        b[3] = (int) (ip & 0xff);
        x = Integer.toString(b[0]) + "." + Integer.toString(b[1]) + "." + Integer.toString(b[2]) + "." + Integer.toString(b[3]);

        return x;
    }

    /**
     *  原有的gps简单做一下随机计算，返回时同一个新的gps
     * @param randomSeed 随机种子 目前传入的是gps 格式是 xxx,xxx 经纬度用逗号,隔开
     * @return
     */
    public static String getRandomGps(String randomSeed){
        if(randomSeed == null){
            return ",";
        }
        //用户随机gps
        //目前先返回空gps
        String  gps = ",";
        String [] location = randomSeed.split(",");
        if(location == null || location.length == 2){
            double temp1 = Double.parseDouble(location[0]) + Math.random();
            double temp2 = Double.parseDouble(location[1]) + Math.random();
            if (temp1 > 1 && temp2 > 1) {
                gps = String.format("%.6f,%.6f", temp1, temp2);
            }
        }
        return gps;
    }

}
