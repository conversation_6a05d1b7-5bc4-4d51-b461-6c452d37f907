package com.allinpokers.yunyingjob.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.allinpokers.yunyingjob.dao.crazypoker.ExportTaskDao;
import com.allinpokers.yunyingjob.dao.crazypoker.ExportTaskLogDao;
import com.allinpokers.yunyingjob.dao.yunying.OrdersDao;
import com.allinpokers.yunyingjob.export.mq.bean.ChipLogQuery;
import com.allinpokers.yunyingjob.export.mq.bean.ChipLogResult;
import com.allinpokers.yunyingjob.export.mq.bean.OrdersQuery;
import com.allinpokers.yunyingjob.export.mq.bean.OrdersResult;
import com.allinpokers.yunyingjob.export.task.domain.ExportTask;
import com.allinpokers.yunyingjob.export.task.domain.ExportTaskLog;
import com.allinpokers.yunyingjob.oss.service.OssService;
import com.allinpokers.yunyingjob.service.OrdersService;
import com.allinpokers.yunyingjob.util.excel.ExcelModel;
import com.allinpokers.yunyingjob.util.excel.ExcelRow;
import com.allinpokers.yunyingjob.util.excel.ExcelSheet;
import com.allinpokers.yunyingjob.util.excel.ExcelUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 订单记录
 */
@Service
@Slf4j
public class OrdersServiceImpl implements OrdersService {

    @Resource
    private ExportTaskDao exportTaskDao;

    @Resource
    private ExportTaskLogDao exportTaskLogDao;

    @Autowired
    private OrdersDao ordersDao;

    @Resource
    private OssService ossService;

    @Value("${export.expiredConfig}")
    private String expiredConfig;

    @Override
    public void cmsClubOrders(ExportTask exportTask) {
        try {
            log.info("exportTask==> {}", JSONObject.toJSONString(exportTask));
            OrdersQuery queryParam = JSONObject.parseObject(exportTask.getParams(), OrdersQuery.class);
            log.info("queryParam==> {}", JSONObject.toJSONString(queryParam));
            if (!StringUtils.isEmpty(queryParam.getStartDate())) {
                queryParam.setStartDate(queryParam.getStartDate() + " 00:00:00");
            }
            if (!StringUtils.isEmpty(queryParam.getEndDate())) {
                queryParam.setEndDate(queryParam.getEndDate() + " 23:59:59");
            }

            List<OrdersResult> list = ordersDao.getClubOrders(queryParam.getClubId(), queryParam.getSearch(), queryParam.getAction(), queryParam.getStatus(), queryParam.getStartDate(), queryParam.getEndDate());
            queryParam.setFileName(queryParam.getFileName() != null ? queryParam.getFileName() : "俱乐部订单记录");
            long total = list.size();
            log.info("统计条数：{}", list.size());

            ExcelSheet sheet = new ExcelSheet(queryParam.getFileName() != null ? queryParam.getFileName() : "俱乐部订单记录");
            List<ExcelModel> models = new ArrayList<>();


            models.add(ExcelModel.builder()
                    .rows(Lists.newArrayList(new ExcelRow().add("记录总数：").add(total)))
                    .afterBlankLine(1)
                    .build());


            String[] title = new String[]{"编号", "订单ID", "订单类型", "订单所属俱乐部", "俱乐部ID", "用户名称", "用户ID", "购买数量", "赠送数量", "渠道", "子通道", "金额", "日期", "回调日期", "汇款通过日期", "订单状态", "操作人"};
            List<ExcelRow> rows = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                double amount = 0;
                double price = 0;
                double discountP = 0;
                double discount = 0;
                OrdersResult info = list.get(i);
                DecimalFormat df = new DecimalFormat("0.##");
                JSONObject orderInfo = JSONObject.parseObject(info.getOrderJson());
                if (orderInfo != null) {
                    amount = orderInfo.getDouble("amount");
                    price = orderInfo.getDouble("price");
                }
                JSONObject createJson = JSONObject.parseObject(info.getCreateJson());
                if (createJson != null) {
                    JSONObject extra = JSONObject.parseObject(createJson.getString("extraOptions"));
                    if (extra != null) {
                        JSONObject productionInfo = JSONObject.parseObject(extra.getString("payment_setting_json"));
                        if (productionInfo.getDouble("discount") != null) {
                            discountP = productionInfo.getDouble("discount");
                            discount = amount * (discountP / 100);
                        }
                    }
                }
                if (orderInfo != null) {
                    if (info.getAction().equals("sell")) {
                        if (orderInfo.getDouble("discount") != null) {
                            double sellDiscount = orderInfo.getDouble("discount");
                            discount = amount * (sellDiscount / 100);
                        }
                    }
                }
                String paymentName = "-";
                if (!StringUtils.isEmpty(info.getNameJson())) {
                    JSONObject nameJson = JSONObject.parseObject(info.getNameJson());
                    if (nameJson != null) {
                        paymentName = nameJson.getString("sc");
                    }
                }
                String paymentSubchannelName = "-";
                if (!StringUtils.isEmpty(info.getOriginNameJson())) {
                    JSONObject originNameJson = JSONObject.parseObject(info.getOriginNameJson());
                    if (originNameJson != null) {
                        paymentSubchannelName = originNameJson.getString("sc");
                    }
                    if (!StringUtils.isEmpty(info.getCustomNameJson())) {
                        JSONObject customNameJson = JSONObject.parseObject(info.getCustomNameJson());
                        if (customNameJson != null) {
                            if (!StringUtils.isEmpty(customNameJson.getString("sc"))) {
                                paymentSubchannelName = customNameJson.getString("sc");
                            }
                        }
                    }
                }
                ExcelRow row = new ExcelRow()
                        .add(i + 1)
                        // 订单ID
                        .add(getStringValue(info.getOrderId()))
                        // 订单类型
                        .add(getOrderType(info.getAction()))
                        // 订单所属俱乐部
                        .add(getStringValue(info.getClubName()))
                        // 俱乐部ID
                        .add(getStringValue(info.getClubRandomId() + ""))
                        // 用户名称
                        .add(getStringValue(info.getUserName()))
                        // 用户ID
                        .add(getStringValue(info.getUserRandomId()))
                        // 购买数量
                        .add(df.format(amount))
                        // 赠送数量
                        .add(df.format(discount))
                        // 渠道
                        .add(getStringValue(paymentName))
                        // 子通道
                        .add(getStringValue(paymentSubchannelName))
                        // 金额
                        .add(df.format(price))
                        // 日期
                        .add(getFmtTime2(info.getCreatedAt()))
                        // 回调日期
                        .add(getFmtTime2(info.getCallbackAt()))
                        // 汇款通过日期
                        .add(getFmtTime2(info.getApproveAt()))
                        // 订单状态
                        .add(getOrderStatus(info.getAction(), info.getStatus()))
                        // 操作人
                        .add(getStringValue(info.getLockedByName()));
                rows.add(row);
            }

            models.add(ExcelModel.builder()
                    .titles(Lists.newArrayList(title))
                    .rows(rows)
                    .afterBlankLine(1)
                    .build());

            sheet.setModels(models);
            log.info("生成文件---");

            String dateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String time = String.valueOf(new Date().getTime());
            String random = time.substring(time.length()-6);
            String filePath = "/" + queryParam.getFileName() + "_" + dateTime + random + ".xlsx";
            log.info("生成文件---filePath：{}", filePath);

            //创建一个Excel文件
            MultipartFile multipartFile;
            try (SXSSFWorkbook workbook = new SXSSFWorkbook(100)) {
                // 填充数据
                try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                    ExcelUtils.write(workbook, sheet);
                    workbook.write(outputStream);
                    workbook.dispose(); // 清理临时文件
                    byte[] excelBytes = outputStream.toByteArray();
                    // 构造 MultipartFile
                    // 转换逻辑
                    ByteArrayInputStream inputStream = new ByteArrayInputStream(excelBytes);
                    multipartFile = new MockMultipartFile(
                            "excelFile",          // 表单字段名（可自定义）
                            queryParam.getFileName() + "_" + dateTime + random + ".xlsx",        // 文件名（需包含扩展名）
                            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                            inputStream
                    );
                    log.info("转换文件类型---成功");
                }
            }

            log.info("转换文件类型---");
            if (multipartFile.getSize() <= 0) {
                log.error("导出任务失败-文件转换失败");
                exportTaskDao.updateFail(exportTask.getId(), "文件转换失败");
                return;
            }
            String path = ossService.uploadFile(multipartFile, true, exportTask.getCreatedBy(), 6);
            log.info("上传文件至oss---path：{}", path);

            int fileSize = Integer.parseInt(Long.toString(multipartFile.getSize()));
            LocalDateTime expiredAt = LocalDateTime.now().plusDays(expiredConfig != null ? Long.parseLong(expiredConfig) : 30L);
            log.info("导出任务==> {}-{}, 总条数：{}, 大小：{}, 过期时间：{}, path: {}", exportTask.getId(), filePath, total, fileSize, expiredAt, path);
            exportTaskDao.updateSuccess(exportTask.getId(), Integer.parseInt(Long.toString(total)), fileSize, filePath, path, expiredAt);
            exportTaskLogDao.insert(ExportTaskLog.builder()
                    .taskId(exportTask.getId())
                    .status(2)
                    .message("成功执行任务")
                    .build());
        } catch (Exception e) {
            log.error("导出任务失败-{}", e.getMessage());
            exportTaskDao.updateFail(exportTask.getId(), e.getMessage());
            exportTaskLogDao.insert(ExportTaskLog.builder()
                    .taskId(exportTask.getId())
                    .status(3)
                    .message("执行任务失败：" + e.getMessage())
                    .build());
            e.printStackTrace();
        }
    }

    @Override
    public void cmsTribeOrders(ExportTask exportTask) {
        try {
            log.info("exportTask==> {}", JSONObject.toJSONString(exportTask));
            OrdersQuery queryParam = JSONObject.parseObject(exportTask.getParams(), OrdersQuery.class);
            log.info("queryParam==> {}", JSONObject.toJSONString(queryParam));
            if (!StringUtils.isEmpty(queryParam.getStartDate())) {
                queryParam.setStartDate(queryParam.getStartDate() + " 00:00:00");
            }
            if (!StringUtils.isEmpty(queryParam.getEndDate())) {
                queryParam.setEndDate(queryParam.getEndDate() + " 23:59:59");
            }

            List<OrdersResult> list = ordersDao.getTribeOrders(exportTask.getTribeId(), queryParam.getSearch(), queryParam.getAction(), queryParam.getStatus(), queryParam.getStartDate(), queryParam.getEndDate());
            queryParam.setFileName(queryParam.getFileName() != null ? queryParam.getFileName() : "联盟订单记录");
            long total = list.size();
            log.info("统计条数：{}", list.size());

            ExcelSheet sheet = new ExcelSheet(queryParam.getFileName() != null ? queryParam.getFileName() : "联盟订单记录");
            List<ExcelModel> models = new ArrayList<>();


            models.add(ExcelModel.builder()
                    .rows(Lists.newArrayList(new ExcelRow().add("记录总数：").add(total)))
                    .afterBlankLine(1)
                    .build());


            String[] title = new String[]{"编号", "订单ID", "订单类型", "订单所属俱乐部", "俱乐部ID", "用户名称", "用户ID", "购买数量", "赠送数量", "渠道", "子通道", "金额", "日期", "回调日期", "汇款通过日期", "订单状态", "操作人"};
            List<ExcelRow> rows = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                double amount = 0;
                double price = 0;
                double discountP = 0;
                double discount = 0;
                OrdersResult info = list.get(i);
                DecimalFormat df = new DecimalFormat("0.##");
                JSONObject orderInfo = JSONObject.parseObject(info.getOrderJson());
                if (orderInfo != null) {
                    amount = orderInfo.getDouble("amount");
                    price = orderInfo.getDouble("price");
                }
                JSONObject createJson = JSONObject.parseObject(info.getCreateJson());
                if (createJson != null) {
                    JSONObject extra = createJson.getJSONObject("extraOptions");
                    if (extra != null) {
                        JSONObject productionInfo = JSONObject.parseObject(extra.getString("payment_setting_json"));
                        if (productionInfo != null && productionInfo.getDouble("discount") != null) {
                            discountP = productionInfo.getDouble("discount");
                            discount = amount * (discountP / 100);
                        }
                    }
                }
                if (orderInfo != null) {
                    if (info.getAction().equals("sell")) {
                        if (orderInfo.getDouble("discount") != null) {
                            double sellDiscount = orderInfo.getDouble("discount");
                            discount = amount * (sellDiscount / 100);
                        }
                    }
                }
                String paymentName = "-";
                if (!StringUtils.isEmpty(info.getNameJson())) {
                    JSONObject nameJson = JSONObject.parseObject(info.getNameJson());
                    if (nameJson != null) {
                        paymentName = nameJson.getString("sc");
                    }
                }
                String paymentSubchannelName = "-";
                if (!StringUtils.isEmpty(info.getOriginNameJson())) {
                    JSONObject originNameJson = JSONObject.parseObject(info.getOriginNameJson());
                    if (originNameJson != null) {
                        paymentSubchannelName = originNameJson.getString("sc");
                    }
                    if (!StringUtils.isEmpty(info.getCustomNameJson())) {
                        JSONObject customNameJson = JSONObject.parseObject(info.getCustomNameJson());
                        if (customNameJson != null) {
                            if (!StringUtils.isEmpty(customNameJson.getString("sc"))) {
                                paymentSubchannelName = customNameJson.getString("sc");
                            }
                        }
                    }
                }
                ExcelRow row = new ExcelRow()
                        .add(i + 1)
                        // 订单ID
                        .add(getStringValue(info.getOrderId()))
                        // 订单类型
                        .add(getOrderType(info.getAction()))
                        // 订单所属俱乐部
                        .add(getStringValue(info.getClubName()))
                        // 俱乐部ID
                        .add(getStringValue(info.getClubRandomId() + ""))
                        // 用户名称
                        .add(getStringValue(info.getUserName()))
                        // 用户ID
                        .add(getStringValue(info.getUserRandomId()))
                        // 购买数量
                        .add(df.format(amount))
                        // 赠送数量
                        .add(df.format(discount))
                        // 渠道
                        .add(getStringValue(paymentName))
                        // 子通道
                        .add(getStringValue(paymentSubchannelName))
                        // 金额
                        .add(df.format(price))
                        // 日期
                        .add(getFmtTime2(info.getCreatedAt()))
                        // 回调日期
                        .add(getFmtTime2(info.getCallbackAt()))
                        // 汇款通过日期
                        .add(getFmtTime2(info.getApproveAt()))
                        // 订单状态
                        .add(getOrderStatus(info.getAction(), info.getStatus()))
                        // 操作人
                        .add(getStringValue(info.getLockedByName()));
                rows.add(row);
            }

            models.add(ExcelModel.builder()
                    .titles(Lists.newArrayList(title))
                    .rows(rows)
                    .afterBlankLine(1)
                    .build());

            sheet.setModels(models);
            log.info("生成文件---");

            String dateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String time = String.valueOf(new Date().getTime());
            String random = time.substring(time.length()-6);
            String filePath = "/" + queryParam.getFileName() + "_" + dateTime + random + ".xlsx";
            log.info("生成文件---filePath：{}", filePath);

            //创建一个Excel文件
            MultipartFile multipartFile;
            try (SXSSFWorkbook workbook = new SXSSFWorkbook(100)) {
                // 填充数据
                try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                    ExcelUtils.write(workbook, sheet);
                    workbook.write(outputStream);
                    workbook.dispose(); // 清理临时文件
                    byte[] excelBytes = outputStream.toByteArray();
                    // 构造 MultipartFile
                    // 转换逻辑
                    ByteArrayInputStream inputStream = new ByteArrayInputStream(excelBytes);
                    multipartFile = new MockMultipartFile(
                            "excelFile",          // 表单字段名（可自定义）
                            queryParam.getFileName() + "_" + dateTime + random + ".xlsx",        // 文件名（需包含扩展名）
                            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                            inputStream
                    );
                    log.info("转换文件类型---成功");
                }
            }

            log.info("转换文件类型---");
            if (multipartFile.getSize() <= 0) {
                log.error("导出任务失败-文件转换失败");
                exportTaskDao.updateFail(exportTask.getId(), "文件转换失败");
                return;
            }
            String path = ossService.uploadFile(multipartFile, true, exportTask.getCreatedBy(), 6);
            log.info("上传文件至oss---path：{}", path);

            int fileSize = Integer.parseInt(Long.toString(multipartFile.getSize()));
            LocalDateTime expiredAt = LocalDateTime.now().plusDays(expiredConfig != null ? Long.parseLong(expiredConfig) : 30L);
            log.info("导出任务==> {}-{}, 总条数：{}, 大小：{}, 过期时间：{}, path: {}", exportTask.getId(), filePath, total, fileSize, expiredAt, path);
            exportTaskDao.updateSuccess(exportTask.getId(), Integer.parseInt(Long.toString(total)), fileSize, filePath, path, expiredAt);
            exportTaskLogDao.insert(ExportTaskLog.builder()
                    .taskId(exportTask.getId())
                    .status(2)
                    .message("成功执行任务")
                    .build());
        } catch (Exception e) {
            log.error("导出任务失败-{}", e.getMessage());
            exportTaskDao.updateFail(exportTask.getId(), e.getMessage());
            exportTaskLogDao.insert(ExportTaskLog.builder()
                    .taskId(exportTask.getId())
                    .status(3)
                    .message("执行任务失败：" + e.getMessage())
                    .build());
            e.printStackTrace();
        }
    }

    /**
     * 订单状态
     * @param status
     * @return
     */
    private String getOrderStatus(String action, Integer status) {
        switch (action) {
            case "buy":
                return getBuyOrderStatus(status);
            case "sell":
                return getSellOrderStatus(status);
            default:
                return "-";
        }
    }
    private String getBuyOrderStatus(Integer status) {
        switch (status) {
            case 0:
                return "待到帐";
            case 1:
                return "充值成功";
            case 2:
                return "错误";
            case 3:
                return "已取消";
            case 4:
                return "扣除失败";
            case 5:
                return "充值失败";
            case 99:
                return "订单超时";
            default:
                return "-";
        }
    }
    private String getSellOrderStatus(Integer status) {
        switch (status) {
            case 0:
                return "待到帐";
            case 1:
                return "出售成功";
            case 2:
                return "错误";
            case 3:
                return "已取消";
            case 4:
                return "扣除失败";
            default:
                return "-";
        }
    }
    /**
     * 订单类型
     * @param type
     * @return
     */
    private String getOrderType(String type) {
        switch (type) {
            case "buy":
                return "充值";
            case "sell":
                return "出售";
            default:
                return "-";
        }
    }
    private String getStringValue(String value) {
        if (StringUtils.isEmpty(value)) {
            return "-";
        }
        return value;
    }

    /**
     * 时间戳，单位为秒，需要转换成时间格式:yyyy-MM-dd HH:mm:ss
     * @param time
     * @return
     */
    private String getFmtTime(Long time) {
        if (time == null) {
            return "";
        }
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(time * 1000));
    }
    private String getFmtTime2(Date time) {
        if (time == null) {
            return "";
        }
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(time);
    }
}
