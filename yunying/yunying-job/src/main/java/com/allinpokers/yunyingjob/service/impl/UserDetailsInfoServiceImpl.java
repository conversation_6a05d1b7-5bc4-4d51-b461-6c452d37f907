package com.allinpokers.yunyingjob.service.impl;


import com.allinpokers.yunyingjob.dao.crazypoker.UserDetailsInfoDao;
import com.allinpokers.yunyingjob.entity.crazypoker.UserDetailsInfo;
import com.allinpokers.yunyingjob.entity.crazypoker.example.UserDetailsInfoExample;
import com.allinpokers.yunyingjob.service.UserDetailsInfoService;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 2019/3/22
 *
 * <AUTHOR>
 */
@Service
public class UserDetailsInfoServiceImpl implements UserDetailsInfoService {
    @Resource
    private UserDetailsInfoDao userDetailsInfoDao;

    @Override
    public List<UserDetailsInfo> findUsersByUserIds(Collection<Integer> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return new ArrayList<>();
        }
        UserDetailsInfoExample example = new UserDetailsInfoExample();
        example.or().andUserIdIn(new ArrayList<>(userIds));
        return userDetailsInfoDao.selectByExample(example);
    }

    @Override
    public UserDetailsInfo findUserByUserPone(String phone) {
        UserDetailsInfoExample example = new UserDetailsInfoExample();
        example.or().andPhoneEqualTo(phone);
        List<UserDetailsInfo> list = userDetailsInfoDao.selectByExample(example);
        return list.isEmpty() ? null : list.get(0);
    }

    @Override
    public UserDetailsInfo selectByPrimaryKey(Integer userId) {
        return userDetailsInfoDao.selectByPrimaryKey(userId);
    }

    @Override
    public Map<Integer, UserDetailsInfo> findUsersMapByUserIds(Collection<Integer> userIds) {
        List<UserDetailsInfo> list = this.findUsersByUserIds(userIds);
        return list.stream().collect(Collectors.toMap(UserDetailsInfo::getUserId, v -> v));
    }

    @Override
    public UserDetailsInfo findUserByUserRamdomId(String userId) {
        UserDetailsInfoExample example = new UserDetailsInfoExample();
        example.createCriteria().andRandomNumEqualTo(userId);
        List<UserDetailsInfo> list = userDetailsInfoDao.selectByExample(example);
        return ArrayUtils.isEmpty(list.toArray()) ? null : list.get(0);
    }

    @Override
    public List<UserDetailsInfo> findUserByUserRamdomIds(Collection<String> randomIds) {
        if (randomIds == null || randomIds.isEmpty()) {
            return new ArrayList<>();
        }
        UserDetailsInfoExample example = new UserDetailsInfoExample();
        example.or().andRandomNumIn(new ArrayList<>(randomIds));
        return userDetailsInfoDao.selectByExample(example);
    }

    @Override
    public List<UserDetailsInfo> findAllUser() {
        UserDetailsInfoExample example = new UserDetailsInfoExample();
        example.or();
        return userDetailsInfoDao.selectByExample(example);
    }

    @Override
    public Integer addUserDetailInfo(UserDetailsInfo userDetailsInfo) {
        return userDetailsInfoDao.insertSelective(userDetailsInfo);
    }

    @Override
    public Integer updateUserDetailInfo(UserDetailsInfo userDetailsInfo) {
        return userDetailsInfoDao.updateByPrimaryKeySelective(userDetailsInfo);
    }
}
