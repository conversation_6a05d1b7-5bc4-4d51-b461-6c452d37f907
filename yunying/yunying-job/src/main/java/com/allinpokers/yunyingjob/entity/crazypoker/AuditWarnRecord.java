package com.allinpokers.yunyingjob.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AuditWarnRecord  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuditWarnRecord {
    /**
     * 自增id
     */
    @ApiModelProperty("自增id")
    private Long id;

    /**
     * 用户昵称
     */
    @ApiModelProperty("用户昵称")
    private String nickName;

    /**
     * 用户随机id
     */
    @ApiModelProperty("用户随机id")
    private String randomId;

    /**
     * 原因备注
     */
    @ApiModelProperty("原因备注")
    private String remark;

    /**
     * 异常类型 1、充值异常 2、提豆异常 3、平台每日自检异常
     */
    @ApiModelProperty("异常类型 1、充值异常 2、提豆异常 3、平台每日自检异常")
    private Integer type;

    /**
     * 处理状态 0、未处理  1、已处理
     */
    @ApiModelProperty("处理状态 0、未处理  1、已处理")
    private Integer status;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    /**
     * 记录时间
     */
    @ApiModelProperty("记录时间")
    private LocalDateTime createdTime;
}