<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunyingjob.dao.crazypoker.UserAccountLogDao">
  <resultMap id="BaseResultMap" type="com.allinpokers.yunyingjob.entity.crazypoker.UserAccountLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="change_source" jdbcType="INTEGER" property="changeSource" />
    <result column="current_chip" jdbcType="INTEGER" property="currentChip" />
    <result column="change_chip" jdbcType="INTEGER" property="changeChip" />
    <result column="curr_not_extract_chip" jdbcType="INTEGER" property="currNotExtractChip" />
    <result column="change_not_extract_chip" jdbcType="INTEGER" property="changeNotExtractChip" />
    <result column="curr_pl_count" jdbcType="BIGINT" property="currPlCount" />
    <result column="change_pl_count" jdbcType="INTEGER" property="changePlCount" />
    <result column="desction" jdbcType="VARCHAR" property="desction" />
    <result column="external_id" jdbcType="VARCHAR" property="externalId" />
    <result column="op_id" jdbcType="INTEGER" property="opId" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, user_id, type, change_source, current_chip, change_chip, curr_not_extract_chip, 
    change_not_extract_chip, curr_pl_count, change_pl_count, desction, external_id, op_id, 
    created_time
  </sql>
  <select id="selectByExample" parameterType="com.allinpokers.yunyingjob.entity.crazypoker.example.UserAccountLogExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user_account_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from user_account_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from user_account_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.allinpokers.yunyingjob.entity.crazypoker.example.UserAccountLogExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from user_account_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.allinpokers.yunyingjob.entity.crazypoker.UserAccountLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into user_account_log (id, user_id, type, 
      change_source, current_chip, change_chip, 
      curr_not_extract_chip, change_not_extract_chip, 
      curr_pl_count, change_pl_count, desction, 
      external_id, op_id, created_time
      )
    values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, 
      #{changeSource,jdbcType=INTEGER}, #{currentChip,jdbcType=INTEGER}, #{changeChip,jdbcType=INTEGER}, 
      #{currNotExtractChip,jdbcType=INTEGER}, #{changeNotExtractChip,jdbcType=INTEGER}, 
      #{currPlCount,jdbcType=BIGINT}, #{changePlCount,jdbcType=INTEGER}, #{desction,jdbcType=VARCHAR}, 
      #{externalId,jdbcType=VARCHAR}, #{opId,jdbcType=INTEGER}, #{createdTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.allinpokers.yunyingjob.entity.crazypoker.UserAccountLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into user_account_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="changeSource != null">
        change_source,
      </if>
      <if test="currentChip != null">
        current_chip,
      </if>
      <if test="changeChip != null">
        change_chip,
      </if>
      <if test="currNotExtractChip != null">
        curr_not_extract_chip,
      </if>
      <if test="changeNotExtractChip != null">
        change_not_extract_chip,
      </if>
      <if test="currPlCount != null">
        curr_pl_count,
      </if>
      <if test="changePlCount != null">
        change_pl_count,
      </if>
      <if test="desction != null">
        desction,
      </if>
      <if test="externalId != null">
        external_id,
      </if>
      <if test="opId != null">
        op_id,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="changeSource != null">
        #{changeSource,jdbcType=INTEGER},
      </if>
      <if test="currentChip != null">
        #{currentChip,jdbcType=INTEGER},
      </if>
      <if test="changeChip != null">
        #{changeChip,jdbcType=INTEGER},
      </if>
      <if test="currNotExtractChip != null">
        #{currNotExtractChip,jdbcType=INTEGER},
      </if>
      <if test="changeNotExtractChip != null">
        #{changeNotExtractChip,jdbcType=INTEGER},
      </if>
      <if test="currPlCount != null">
        #{currPlCount,jdbcType=BIGINT},
      </if>
      <if test="changePlCount != null">
        #{changePlCount,jdbcType=INTEGER},
      </if>
      <if test="desction != null">
        #{desction,jdbcType=VARCHAR},
      </if>
      <if test="externalId != null">
        #{externalId,jdbcType=VARCHAR},
      </if>
      <if test="opId != null">
        #{opId,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.allinpokers.yunyingjob.entity.crazypoker.example.UserAccountLogExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from user_account_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_account_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.changeSource != null">
        change_source = #{record.changeSource,jdbcType=INTEGER},
      </if>
      <if test="record.currentChip != null">
        current_chip = #{record.currentChip,jdbcType=INTEGER},
      </if>
      <if test="record.changeChip != null">
        change_chip = #{record.changeChip,jdbcType=INTEGER},
      </if>
      <if test="record.currNotExtractChip != null">
        curr_not_extract_chip = #{record.currNotExtractChip,jdbcType=INTEGER},
      </if>
      <if test="record.changeNotExtractChip != null">
        change_not_extract_chip = #{record.changeNotExtractChip,jdbcType=INTEGER},
      </if>
      <if test="record.currPlCount != null">
        curr_pl_count = #{record.currPlCount,jdbcType=BIGINT},
      </if>
      <if test="record.changePlCount != null">
        change_pl_count = #{record.changePlCount,jdbcType=INTEGER},
      </if>
      <if test="record.desction != null">
        desction = #{record.desction,jdbcType=VARCHAR},
      </if>
      <if test="record.externalId != null">
        external_id = #{record.externalId,jdbcType=VARCHAR},
      </if>
      <if test="record.opId != null">
        op_id = #{record.opId,jdbcType=INTEGER},
      </if>
      <if test="record.createdTime != null">
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_account_log
    set id = #{record.id,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=INTEGER},
      type = #{record.type,jdbcType=INTEGER},
      change_source = #{record.changeSource,jdbcType=INTEGER},
      current_chip = #{record.currentChip,jdbcType=INTEGER},
      change_chip = #{record.changeChip,jdbcType=INTEGER},
      curr_not_extract_chip = #{record.currNotExtractChip,jdbcType=INTEGER},
      change_not_extract_chip = #{record.changeNotExtractChip,jdbcType=INTEGER},
      curr_pl_count = #{record.currPlCount,jdbcType=BIGINT},
      change_pl_count = #{record.changePlCount,jdbcType=INTEGER},
      desction = #{record.desction,jdbcType=VARCHAR},
      external_id = #{record.externalId,jdbcType=VARCHAR},
      op_id = #{record.opId,jdbcType=INTEGER},
      created_time = #{record.createdTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.allinpokers.yunyingjob.entity.crazypoker.UserAccountLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_account_log
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="changeSource != null">
        change_source = #{changeSource,jdbcType=INTEGER},
      </if>
      <if test="currentChip != null">
        current_chip = #{currentChip,jdbcType=INTEGER},
      </if>
      <if test="changeChip != null">
        change_chip = #{changeChip,jdbcType=INTEGER},
      </if>
      <if test="currNotExtractChip != null">
        curr_not_extract_chip = #{currNotExtractChip,jdbcType=INTEGER},
      </if>
      <if test="changeNotExtractChip != null">
        change_not_extract_chip = #{changeNotExtractChip,jdbcType=INTEGER},
      </if>
      <if test="currPlCount != null">
        curr_pl_count = #{currPlCount,jdbcType=BIGINT},
      </if>
      <if test="changePlCount != null">
        change_pl_count = #{changePlCount,jdbcType=INTEGER},
      </if>
      <if test="desction != null">
        desction = #{desction,jdbcType=VARCHAR},
      </if>
      <if test="externalId != null">
        external_id = #{externalId,jdbcType=VARCHAR},
      </if>
      <if test="opId != null">
        op_id = #{opId,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.allinpokers.yunyingjob.entity.crazypoker.UserAccountLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update user_account_log
    set user_id = #{userId,jdbcType=INTEGER},
      type = #{type,jdbcType=INTEGER},
      change_source = #{changeSource,jdbcType=INTEGER},
      current_chip = #{currentChip,jdbcType=INTEGER},
      change_chip = #{changeChip,jdbcType=INTEGER},
      curr_not_extract_chip = #{currNotExtractChip,jdbcType=INTEGER},
      change_not_extract_chip = #{changeNotExtractChip,jdbcType=INTEGER},
      curr_pl_count = #{currPlCount,jdbcType=BIGINT},
      change_pl_count = #{changePlCount,jdbcType=INTEGER},
      desction = #{desction,jdbcType=VARCHAR},
      external_id = #{externalId,jdbcType=VARCHAR},
      op_id = #{opId,jdbcType=INTEGER},
      created_time = #{createdTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>