<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunyingjob.dao.crazypoker.WarehouseAcquisitionDao">
  <resultMap id="BaseResultMap" type="com.allinpokers.yunyingjob.entity.crazypoker.WarehouseAcquisition">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="acquisition_time" jdbcType="TIMESTAMP" property="acquisitionTime" />
    <result column="user_chip" jdbcType="BIGINT" property="userChip" />
    <result column="user_lock_chip" jdbcType="BIGINT" property="userLockChip" />
    <result column="user_extract_chip" jdbcType="BIGINT" property="userExtractChip" />
    <result column="user_not_extract_chip" jdbcType="BIGINT" property="userNotExtractChip" />
    <result column="fund_chip" jdbcType="BIGINT" property="fundChip" />
    <result column="market_chip" jdbcType="BIGINT" property="marketChip" />
    <result column="jpb_chip" jdbcType="BIGINT" property="jpbChip" />
    <result column="jackpot_chip" jdbcType="BIGINT" property="jackpotChip" />
    <result column="system_chip" jdbcType="BIGINT" property="systemChip" />
    <result column="pay_channel_chip" jdbcType="BIGINT" property="payChannelChip" />
    <result column="pay_channel_unlock_chip" jdbcType="BIGINT" property="payChannelUnlockChip" />
    <result column="pay_channel_lock_chip" jdbcType="BIGINT" property="payChannelLockChip" />
    <result column="manual_recharge_chip" jdbcType="BIGINT" property="manualRechargeChip" />
    <result column="center_chip" jdbcType="BIGINT" property="centerChip" />
    <result column="room_chip" jdbcType="BIGINT" property="roomChip" />
    <result column="prize_pool_chip" jdbcType="BIGINT" property="prizePoolChip" />
    <result column="tribe_chip" jdbcType="BIGINT" property="tribeChip" />
    <result column="gold" jdbcType="BIGINT" property="gold" />
    <result column="tribe_chip_insurance" jdbcType="BIGINT" property="tribeChipInsurance" />
    <result column="gold_insurance" jdbcType="BIGINT" property="goldInsurance" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    acquisition_time, user_chip, user_lock_chip, user_extract_chip, user_not_extract_chip, 
    fund_chip, market_chip, jpb_chip, jackpot_chip, system_chip, pay_channel_chip, pay_channel_unlock_chip, 
    pay_channel_lock_chip, manual_recharge_chip, center_chip, room_chip, prize_pool_chip, 
    tribe_chip, gold, tribe_chip_insurance, gold_insurance
  </sql>
  <select id="selectByExample" parameterType="com.allinpokers.yunyingjob.entity.crazypoker.example.WarehouseAcquisitionExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from warehouse_acquisition
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.time.LocalDateTime" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from warehouse_acquisition
    where acquisition_time = #{acquisitionTime,jdbcType=TIMESTAMP}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.time.LocalDateTime">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from warehouse_acquisition
    where acquisition_time = #{acquisitionTime,jdbcType=TIMESTAMP}
  </delete>
  <delete id="deleteByExample" parameterType="com.allinpokers.yunyingjob.entity.crazypoker.example.WarehouseAcquisitionExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from warehouse_acquisition
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.allinpokers.yunyingjob.entity.crazypoker.WarehouseAcquisition">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into warehouse_acquisition (acquisition_time, user_chip, user_lock_chip, 
      user_extract_chip, user_not_extract_chip, fund_chip, 
      market_chip, jpb_chip, jackpot_chip, 
      system_chip, pay_channel_chip, pay_channel_unlock_chip, 
      pay_channel_lock_chip, manual_recharge_chip, center_chip, 
      room_chip, prize_pool_chip, tribe_chip, 
      gold, tribe_chip_insurance, gold_insurance
      )
    values (#{acquisitionTime,jdbcType=TIMESTAMP}, #{userChip,jdbcType=BIGINT}, #{userLockChip,jdbcType=BIGINT}, 
      #{userExtractChip,jdbcType=BIGINT}, #{userNotExtractChip,jdbcType=BIGINT}, #{fundChip,jdbcType=BIGINT}, 
      #{marketChip,jdbcType=BIGINT}, #{jpbChip,jdbcType=BIGINT}, #{jackpotChip,jdbcType=BIGINT}, 
      #{systemChip,jdbcType=BIGINT}, #{payChannelChip,jdbcType=BIGINT}, #{payChannelUnlockChip,jdbcType=BIGINT}, 
      #{payChannelLockChip,jdbcType=BIGINT}, #{manualRechargeChip,jdbcType=BIGINT}, #{centerChip,jdbcType=BIGINT}, 
      #{roomChip,jdbcType=BIGINT}, #{prizePoolChip,jdbcType=BIGINT}, #{tribeChip,jdbcType=BIGINT}, 
      #{gold,jdbcType=BIGINT}, #{tribeChipInsurance,jdbcType=BIGINT}, #{goldInsurance,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.allinpokers.yunyingjob.entity.crazypoker.WarehouseAcquisition">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into warehouse_acquisition
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="acquisitionTime != null">
        acquisition_time,
      </if>
      <if test="userChip != null">
        user_chip,
      </if>
      <if test="userLockChip != null">
        user_lock_chip,
      </if>
      <if test="userExtractChip != null">
        user_extract_chip,
      </if>
      <if test="userNotExtractChip != null">
        user_not_extract_chip,
      </if>
      <if test="fundChip != null">
        fund_chip,
      </if>
      <if test="marketChip != null">
        market_chip,
      </if>
      <if test="jpbChip != null">
        jpb_chip,
      </if>
      <if test="jackpotChip != null">
        jackpot_chip,
      </if>
      <if test="systemChip != null">
        system_chip,
      </if>
      <if test="payChannelChip != null">
        pay_channel_chip,
      </if>
      <if test="payChannelUnlockChip != null">
        pay_channel_unlock_chip,
      </if>
      <if test="payChannelLockChip != null">
        pay_channel_lock_chip,
      </if>
      <if test="manualRechargeChip != null">
        manual_recharge_chip,
      </if>
      <if test="centerChip != null">
        center_chip,
      </if>
      <if test="roomChip != null">
        room_chip,
      </if>
      <if test="prizePoolChip != null">
        prize_pool_chip,
      </if>
      <if test="tribeChip != null">
        tribe_chip,
      </if>
      <if test="gold != null">
        gold,
      </if>
      <if test="tribeChipInsurance != null">
        tribe_chip_insurance,
      </if>
      <if test="goldInsurance != null">
        gold_insurance,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="acquisitionTime != null">
        #{acquisitionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userChip != null">
        #{userChip,jdbcType=BIGINT},
      </if>
      <if test="userLockChip != null">
        #{userLockChip,jdbcType=BIGINT},
      </if>
      <if test="userExtractChip != null">
        #{userExtractChip,jdbcType=BIGINT},
      </if>
      <if test="userNotExtractChip != null">
        #{userNotExtractChip,jdbcType=BIGINT},
      </if>
      <if test="fundChip != null">
        #{fundChip,jdbcType=BIGINT},
      </if>
      <if test="marketChip != null">
        #{marketChip,jdbcType=BIGINT},
      </if>
      <if test="jpbChip != null">
        #{jpbChip,jdbcType=BIGINT},
      </if>
      <if test="jackpotChip != null">
        #{jackpotChip,jdbcType=BIGINT},
      </if>
      <if test="systemChip != null">
        #{systemChip,jdbcType=BIGINT},
      </if>
      <if test="payChannelChip != null">
        #{payChannelChip,jdbcType=BIGINT},
      </if>
      <if test="payChannelUnlockChip != null">
        #{payChannelUnlockChip,jdbcType=BIGINT},
      </if>
      <if test="payChannelLockChip != null">
        #{payChannelLockChip,jdbcType=BIGINT},
      </if>
      <if test="manualRechargeChip != null">
        #{manualRechargeChip,jdbcType=BIGINT},
      </if>
      <if test="centerChip != null">
        #{centerChip,jdbcType=BIGINT},
      </if>
      <if test="roomChip != null">
        #{roomChip,jdbcType=BIGINT},
      </if>
      <if test="prizePoolChip != null">
        #{prizePoolChip,jdbcType=BIGINT},
      </if>
      <if test="tribeChip != null">
        #{tribeChip,jdbcType=BIGINT},
      </if>
      <if test="gold != null">
        #{gold,jdbcType=BIGINT},
      </if>
      <if test="tribeChipInsurance != null">
        #{tribeChipInsurance,jdbcType=BIGINT},
      </if>
      <if test="goldInsurance != null">
        #{goldInsurance,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.allinpokers.yunyingjob.entity.crazypoker.example.WarehouseAcquisitionExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from warehouse_acquisition
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update warehouse_acquisition
    <set>
      <if test="record.acquisitionTime != null">
        acquisition_time = #{record.acquisitionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.userChip != null">
        user_chip = #{record.userChip,jdbcType=BIGINT},
      </if>
      <if test="record.userLockChip != null">
        user_lock_chip = #{record.userLockChip,jdbcType=BIGINT},
      </if>
      <if test="record.userExtractChip != null">
        user_extract_chip = #{record.userExtractChip,jdbcType=BIGINT},
      </if>
      <if test="record.userNotExtractChip != null">
        user_not_extract_chip = #{record.userNotExtractChip,jdbcType=BIGINT},
      </if>
      <if test="record.fundChip != null">
        fund_chip = #{record.fundChip,jdbcType=BIGINT},
      </if>
      <if test="record.marketChip != null">
        market_chip = #{record.marketChip,jdbcType=BIGINT},
      </if>
      <if test="record.jpbChip != null">
        jpb_chip = #{record.jpbChip,jdbcType=BIGINT},
      </if>
      <if test="record.jackpotChip != null">
        jackpot_chip = #{record.jackpotChip,jdbcType=BIGINT},
      </if>
      <if test="record.systemChip != null">
        system_chip = #{record.systemChip,jdbcType=BIGINT},
      </if>
      <if test="record.payChannelChip != null">
        pay_channel_chip = #{record.payChannelChip,jdbcType=BIGINT},
      </if>
      <if test="record.payChannelUnlockChip != null">
        pay_channel_unlock_chip = #{record.payChannelUnlockChip,jdbcType=BIGINT},
      </if>
      <if test="record.payChannelLockChip != null">
        pay_channel_lock_chip = #{record.payChannelLockChip,jdbcType=BIGINT},
      </if>
      <if test="record.manualRechargeChip != null">
        manual_recharge_chip = #{record.manualRechargeChip,jdbcType=BIGINT},
      </if>
      <if test="record.centerChip != null">
        center_chip = #{record.centerChip,jdbcType=BIGINT},
      </if>
      <if test="record.roomChip != null">
        room_chip = #{record.roomChip,jdbcType=BIGINT},
      </if>
      <if test="record.prizePoolChip != null">
        prize_pool_chip = #{record.prizePoolChip,jdbcType=BIGINT},
      </if>
      <if test="record.tribeChip != null">
        tribe_chip = #{record.tribeChip,jdbcType=BIGINT},
      </if>
      <if test="record.gold != null">
        gold = #{record.gold,jdbcType=BIGINT},
      </if>
      <if test="record.tribeChipInsurance != null">
        tribe_chip_insurance = #{record.tribeChipInsurance,jdbcType=BIGINT},
      </if>
      <if test="record.goldInsurance != null">
        gold_insurance = #{record.goldInsurance,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update warehouse_acquisition
    set acquisition_time = #{record.acquisitionTime,jdbcType=TIMESTAMP},
      user_chip = #{record.userChip,jdbcType=BIGINT},
      user_lock_chip = #{record.userLockChip,jdbcType=BIGINT},
      user_extract_chip = #{record.userExtractChip,jdbcType=BIGINT},
      user_not_extract_chip = #{record.userNotExtractChip,jdbcType=BIGINT},
      fund_chip = #{record.fundChip,jdbcType=BIGINT},
      market_chip = #{record.marketChip,jdbcType=BIGINT},
      jpb_chip = #{record.jpbChip,jdbcType=BIGINT},
      jackpot_chip = #{record.jackpotChip,jdbcType=BIGINT},
      system_chip = #{record.systemChip,jdbcType=BIGINT},
      pay_channel_chip = #{record.payChannelChip,jdbcType=BIGINT},
      pay_channel_unlock_chip = #{record.payChannelUnlockChip,jdbcType=BIGINT},
      pay_channel_lock_chip = #{record.payChannelLockChip,jdbcType=BIGINT},
      manual_recharge_chip = #{record.manualRechargeChip,jdbcType=BIGINT},
      center_chip = #{record.centerChip,jdbcType=BIGINT},
      room_chip = #{record.roomChip,jdbcType=BIGINT},
      prize_pool_chip = #{record.prizePoolChip,jdbcType=BIGINT},
      tribe_chip = #{record.tribeChip,jdbcType=BIGINT},
      gold = #{record.gold,jdbcType=BIGINT},
      tribe_chip_insurance = #{record.tribeChipInsurance,jdbcType=BIGINT},
      gold_insurance = #{record.goldInsurance,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.allinpokers.yunyingjob.entity.crazypoker.WarehouseAcquisition">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update warehouse_acquisition
    <set>
      <if test="userChip != null">
        user_chip = #{userChip,jdbcType=BIGINT},
      </if>
      <if test="userLockChip != null">
        user_lock_chip = #{userLockChip,jdbcType=BIGINT},
      </if>
      <if test="userExtractChip != null">
        user_extract_chip = #{userExtractChip,jdbcType=BIGINT},
      </if>
      <if test="userNotExtractChip != null">
        user_not_extract_chip = #{userNotExtractChip,jdbcType=BIGINT},
      </if>
      <if test="fundChip != null">
        fund_chip = #{fundChip,jdbcType=BIGINT},
      </if>
      <if test="marketChip != null">
        market_chip = #{marketChip,jdbcType=BIGINT},
      </if>
      <if test="jpbChip != null">
        jpb_chip = #{jpbChip,jdbcType=BIGINT},
      </if>
      <if test="jackpotChip != null">
        jackpot_chip = #{jackpotChip,jdbcType=BIGINT},
      </if>
      <if test="systemChip != null">
        system_chip = #{systemChip,jdbcType=BIGINT},
      </if>
      <if test="payChannelChip != null">
        pay_channel_chip = #{payChannelChip,jdbcType=BIGINT},
      </if>
      <if test="payChannelUnlockChip != null">
        pay_channel_unlock_chip = #{payChannelUnlockChip,jdbcType=BIGINT},
      </if>
      <if test="payChannelLockChip != null">
        pay_channel_lock_chip = #{payChannelLockChip,jdbcType=BIGINT},
      </if>
      <if test="manualRechargeChip != null">
        manual_recharge_chip = #{manualRechargeChip,jdbcType=BIGINT},
      </if>
      <if test="centerChip != null">
        center_chip = #{centerChip,jdbcType=BIGINT},
      </if>
      <if test="roomChip != null">
        room_chip = #{roomChip,jdbcType=BIGINT},
      </if>
      <if test="prizePoolChip != null">
        prize_pool_chip = #{prizePoolChip,jdbcType=BIGINT},
      </if>
      <if test="tribeChip != null">
        tribe_chip = #{tribeChip,jdbcType=BIGINT},
      </if>
      <if test="gold != null">
        gold = #{gold,jdbcType=BIGINT},
      </if>
      <if test="tribeChipInsurance != null">
        tribe_chip_insurance = #{tribeChipInsurance,jdbcType=BIGINT},
      </if>
      <if test="goldInsurance != null">
        gold_insurance = #{goldInsurance,jdbcType=BIGINT},
      </if>
    </set>
    where acquisition_time = #{acquisitionTime,jdbcType=TIMESTAMP}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.allinpokers.yunyingjob.entity.crazypoker.WarehouseAcquisition">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update warehouse_acquisition
    set user_chip = #{userChip,jdbcType=BIGINT},
      user_lock_chip = #{userLockChip,jdbcType=BIGINT},
      user_extract_chip = #{userExtractChip,jdbcType=BIGINT},
      user_not_extract_chip = #{userNotExtractChip,jdbcType=BIGINT},
      fund_chip = #{fundChip,jdbcType=BIGINT},
      market_chip = #{marketChip,jdbcType=BIGINT},
      jpb_chip = #{jpbChip,jdbcType=BIGINT},
      jackpot_chip = #{jackpotChip,jdbcType=BIGINT},
      system_chip = #{systemChip,jdbcType=BIGINT},
      pay_channel_chip = #{payChannelChip,jdbcType=BIGINT},
      pay_channel_unlock_chip = #{payChannelUnlockChip,jdbcType=BIGINT},
      pay_channel_lock_chip = #{payChannelLockChip,jdbcType=BIGINT},
      manual_recharge_chip = #{manualRechargeChip,jdbcType=BIGINT},
      center_chip = #{centerChip,jdbcType=BIGINT},
      room_chip = #{roomChip,jdbcType=BIGINT},
      prize_pool_chip = #{prizePoolChip,jdbcType=BIGINT},
      tribe_chip = #{tribeChip,jdbcType=BIGINT},
      gold = #{gold,jdbcType=BIGINT},
      tribe_chip_insurance = #{tribeChipInsurance,jdbcType=BIGINT},
      gold_insurance = #{goldInsurance,jdbcType=BIGINT}
    where acquisition_time = #{acquisitionTime,jdbcType=TIMESTAMP}
  </update>
</mapper>