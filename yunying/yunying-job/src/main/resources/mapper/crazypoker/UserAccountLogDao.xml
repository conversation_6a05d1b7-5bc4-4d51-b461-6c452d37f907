<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunyingjob.dao.crazypoker.UserAccountLogDao">

    <select id="sumStoreAndClubRecharge" resultType="long">
        select ifnull(sum(change_not_extract_chip + change_chip),0) as total
        from user_account_log
        where type in(31,32) and created_time &gt; #{lastDate} and created_time &lt;= #{currentDate};
    </select>

    <select id="findTakeinAndEsRecord" resultType="com.allinpokers.yunyingjob.entity.crazypoker.UserAccountLog">
        SELECT *
        from user_account_log
        WHERE type in(5,7) and external_id
        in(SELECT room_id from room_search WHERE status = 4) and created_time &gt; #{lastDate} and created_time &lt;= #{currentDate};
    </select>

    <select id="sumRoomChip" resultType="long">
      SELECT 	ifnull( SUM(IFNULL(change_chip, 0)) + SUM(
						IFNULL(change_not_extract_chip, 0)
					), 0) AS chip
        from user_account_log
        WHERE type in(5,7)
        <if test='roomIds != null and roomIds.size>0 '> and external_id in
            <foreach collection='roomIds' item='item' open='(' close=')' separator=','>#{item}</foreach>
        </if>
    </select>

</mapper>