<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.permission.dao.AuthUserDao">
  <resultMap id="BaseResultMap" type="com.allinpokers.yunying.permission.entity.AuthUser">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="auth_type_code" jdbcType="VARCHAR" property="authTypeCode" />
    <result column="nickname" jdbcType="VARCHAR" property="nickname" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="mobile_area_code" jdbcType="VARCHAR" property="mobileAreaCode" />
    <result column="mobile_no" jdbcType="VARCHAR" property="mobileNo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="club_random_id" jdbcType="VARCHAR" property="clubRandomId" />
    <result column="is_tribe_owner" jdbcType="BIT" property="tribeOwner" />
    <result column="created_by" jdbcType="INTEGER" property="createdBy" />
    <result column="updated_by" jdbcType="INTEGER" property="updatedBy" />
    <result column="deleted_by" jdbcType="INTEGER" property="deletedBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="deleted_time" jdbcType="TIMESTAMP" property="deletedTime" />
    <result column="totp_secret" jdbcType="VARCHAR" property="totpSecret" />
    <result column="totp_enable" jdbcType="TINYINT" property="totpEnable" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, parent_id, auth_type_code, nickname, username, password, status, mobile_area_code, 
    mobile_no, remark, club_random_id, is_tribe_owner, created_by, updated_by, deleted_by, 
    created_time, updated_time, deleted, deleted_time, totp_secret, totp_enable
  </sql>
  <select id="selectByExample" parameterType="com.allinpokers.yunying.permission.entity.example.AuthUserExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from auth_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from auth_user
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from auth_user
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.allinpokers.yunying.permission.entity.example.AuthUserExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from auth_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.allinpokers.yunying.permission.entity.AuthUser">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into auth_user (parent_id, auth_type_code, nickname, 
      username, password, status, 
      mobile_area_code, mobile_no, remark, 
      club_random_id, is_tribe_owner, created_by, 
      updated_by, deleted_by, created_time, 
      updated_time, deleted, deleted_time,
      totp_secret, totp_enable
      )
    values (#{parentId,jdbcType=INTEGER}, #{authTypeCode,jdbcType=VARCHAR}, #{nickname,jdbcType=VARCHAR}, 
      #{username,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{mobileAreaCode,jdbcType=VARCHAR}, #{mobileNo,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{clubRandomId,jdbcType=VARCHAR}, #{tribeOwner,jdbcType=BIT}, #{createdBy,jdbcType=INTEGER}, 
      #{updatedBy,jdbcType=INTEGER}, #{deletedBy,jdbcType=INTEGER}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{updatedTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT}, #{deletedTime,jdbcType=TIMESTAMP},
      #{totpSecret,jdbcType=VARCHAR}, 
      <if test="totpEnable != null">
        #{totpEnable,jdbcType=TINYINT}
      </if>
      <if test="totpEnable == null">
        0
      </if>
      )
  </insert>
  <insert id="insertSelective" parameterType="com.allinpokers.yunying.permission.entity.AuthUser">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into auth_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="authTypeCode != null">
        auth_type_code,
      </if>
      <if test="nickname != null">
        nickname,
      </if>
      <if test="username != null">
        username,
      </if>
      <if test="password != null">
        password,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="mobileAreaCode != null">
        mobile_area_code,
      </if>
      <if test="mobileNo != null">
        mobile_no,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="clubRandomId != null">
        club_random_id,
      </if>
      <if test="tribeOwner != null">
        is_tribe_owner,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="deletedBy != null">
        deleted_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="deletedTime != null">
        deleted_time,
      </if>
      <if test="totpSecret != null">
        totpSecret,
      </if>
      totp_enable,
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="parentId != null">
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="authTypeCode != null">
        #{authTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="mobileAreaCode != null">
        #{mobileAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="mobileNo != null">
        #{mobileNo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="clubRandomId != null">
        #{clubRandomId,jdbcType=VARCHAR},
      </if>
      <if test="tribeOwner != null">
        #{tribeOwner,jdbcType=BIT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=INTEGER},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=INTEGER},
      </if>
      <if test="deletedBy != null">
        #{deletedBy,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="deletedTime != null">
        #{deletedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totpSecret != null">
        #{totpSecret,jdbcType=VARCHAR},
      </if>
      <choose>
        <when test="totpEnable != null">
          #{totpEnable,jdbcType=TINYINT},
        </when>
        <otherwise>
          0,
        </otherwise>
      </choose>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.allinpokers.yunying.permission.entity.example.AuthUserExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from auth_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update auth_user
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.parentId != null">
        parent_id = #{record.parentId,jdbcType=INTEGER},
      </if>
      <if test="record.authTypeCode != null">
        auth_type_code = #{record.authTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.nickname != null">
        nickname = #{record.nickname,jdbcType=VARCHAR},
      </if>
      <if test="record.username != null">
        username = #{record.username,jdbcType=VARCHAR},
      </if>
      <if test="record.password != null">
        password = #{record.password,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.mobileAreaCode != null">
        mobile_area_code = #{record.mobileAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.mobileNo != null">
        mobile_no = #{record.mobileNo,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.clubRandomId != null">
        club_random_id = #{record.clubRandomId,jdbcType=VARCHAR},
      </if>
      <if test="record.tribeOwner != null">
        is_tribe_owner = #{record.tribeOwner,jdbcType=BIT},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=INTEGER},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=INTEGER},
      </if>
      <if test="record.deletedBy != null">
        deleted_by = #{record.deletedBy,jdbcType=INTEGER},
      </if>
      <if test="record.createdTime != null">
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedTime != null">
        updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.deletedTime != null">
        deleted_time = #{record.deletedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.totpSecret != null">
        totp_secret = #{record.totpSecret,jdbcType=VARCHAR},
      </if>
      <if test="record.totpEnable != null">
        totp_enable = #{record.totpEnable,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update auth_user
    set id = #{record.id,jdbcType=INTEGER},
      parent_id = #{record.parentId,jdbcType=INTEGER},
      auth_type_code = #{record.authTypeCode,jdbcType=VARCHAR},
      nickname = #{record.nickname,jdbcType=VARCHAR},
      username = #{record.username,jdbcType=VARCHAR},
      password = #{record.password,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      mobile_area_code = #{record.mobileAreaCode,jdbcType=VARCHAR},
      mobile_no = #{record.mobileNo,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      club_random_id = #{record.clubRandomId,jdbcType=VARCHAR},
      is_tribe_owner = #{record.tribeOwner,jdbcType=BIT},
      created_by = #{record.createdBy,jdbcType=INTEGER},
      updated_by = #{record.updatedBy,jdbcType=INTEGER},
      deleted_by = #{record.deletedBy,jdbcType=INTEGER},
      created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT},
      deleted_time = #{record.deletedTime,jdbcType=TIMESTAMP},
      totp_secret = #{record.totpSecret,jdbcType=VARCHAR},
      totp_enable = #{record.totpEnable,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.allinpokers.yunying.permission.entity.AuthUser">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update auth_user
    <set>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="authTypeCode != null">
        auth_type_code = #{authTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        nickname = #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        password = #{password,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="mobileAreaCode != null">
        mobile_area_code = #{mobileAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="mobileNo != null">
        mobile_no = #{mobileNo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="clubRandomId != null">
        club_random_id = #{clubRandomId,jdbcType=VARCHAR},
      </if>
      <if test="tribeOwner != null">
        is_tribe_owner = #{tribeOwner,jdbcType=BIT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=INTEGER},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=INTEGER},
      </if>
      <if test="deletedBy != null">
        deleted_by = #{deletedBy,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="deletedTime != null">
        deleted_time = #{deletedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totpSecret != null">
        totp_secret = #{totpSecret,jdbcType=VARCHAR},
      </if>
      <if test="totpEnable != null">
        totp_enable = #{totpEnable,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.allinpokers.yunying.permission.entity.AuthUser">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update auth_user
    set parent_id = #{parentId,jdbcType=INTEGER},
      auth_type_code = #{authTypeCode,jdbcType=VARCHAR},
      nickname = #{nickname,jdbcType=VARCHAR},
      username = #{username,jdbcType=VARCHAR},
      password = #{password,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      mobile_area_code = #{mobileAreaCode,jdbcType=VARCHAR},
      mobile_no = #{mobileNo,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      club_random_id = #{clubRandomId,jdbcType=VARCHAR},
      is_tribe_owner = #{tribeOwner,jdbcType=BIT},
      created_by = #{createdBy,jdbcType=INTEGER},
      updated_by = #{updatedBy,jdbcType=INTEGER},
      deleted_by = #{deletedBy,jdbcType=INTEGER},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT},
      deleted_time = #{deletedTime,jdbcType=TIMESTAMP},
      totp_secret = #{totpSecret,jdbcType=VARCHAR},
      totp_enable = #{totpEnable,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>