package com.allinpokers.yunying.permission.security;

import com.allinpokers.yunying.permission.model.response.ApiResourcePermission;
import com.allinpokers.yunying.permission.service.AuthResourceService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.access.SecurityConfig;
import org.springframework.security.web.FilterInvocation;
import org.springframework.security.web.access.intercept.FilterInvocationSecurityMetadataSource;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * 在这里做权限的动态刷新
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SecurityMetadataSource implements FilterInvocationSecurityMetadataSource {
    private FilterInvocationSecurityMetadataSource superMetadataSource = null;
    private Map<Matcher, List<ConfigAttribute>> matcherMap = new LinkedHashMap<>();
    @Resource
    private AuthResourceService authResourceService;

    public void setSuperMetadataSource(FilterInvocationSecurityMetadataSource superMetadataSource) {
        this.superMetadataSource = superMetadataSource;
    }

    /**
     * 刷新权限
     * 修改角色/菜单/权限时需要调用该方法刷新
     */
    public synchronized void reload() {
        //查找接口以及允许的权限（目前没有角色概念）
        List<ApiResourcePermission> resources = authResourceService.findApiResourcesAndAllowPermissions();

        Map<Matcher, List<ConfigAttribute>> tempMap = new LinkedHashMap<>(resources.size());
        resources.forEach(resource -> {
            if (resource.getPermissionIds() == null || resource.getPermissionIds().isEmpty()) {
                return;
            }
            Matcher matcher = new Matcher(resource.getMethod(), resource.getValue());
            List<ConfigAttribute> permissions = tempMap.getOrDefault(matcher, new ArrayList<>());
            resource.getPermissionIds().forEach(permissionId -> {
                permissions.add(new SecurityConfig(getRoleName(permissionId)));
            });
            tempMap.put(matcher, permissions);
            log.info("刷新权限：method={}, url={}, permissions={}", matcher.getMethod(), matcher.getUrl(), permissions);
        });
        matcherMap.clear();
        matcherMap.putAll(tempMap);
        log.info("刷新权限成功：一共刷新了" + tempMap.size() + "个URL");
    }

    public static String getRoleName(Integer permissionId) {
        //前缀必须是ROLE_
        return "ROLE_" + permissionId;
    }

    @PostConstruct
    public void init() {
        reload();
    }

    @Override
    public Collection<ConfigAttribute> getAttributes(Object object) throws IllegalArgumentException {
        FilterInvocation fi = (FilterInvocation) object;
        for (Map.Entry<Matcher, List<ConfigAttribute>> entry : matcherMap.entrySet()) {
            Matcher matcher = entry.getKey();
            if (matcher.getMatcher().matches(fi.getRequest())) {
                return entry.getValue();
            }
        }
        if (superMetadataSource == null) {
            return new ArrayList<>();
        }
        //  返回代码定义的默认配置
        return superMetadataSource.getAttributes(object);
    }

    @Override
    public Collection<ConfigAttribute> getAllConfigAttributes() {
        return null;
    }

    @Override
    public boolean supports(Class<?> clazz) {
        return FilterInvocation.class.isAssignableFrom(clazz);
    }

    /**
     * 重写equal和hashCode
     */
    @Data
    private class Matcher {
        private String method;
        private String url;
        private AntPathRequestMatcher matcher;

        Matcher(String method, String url) {
            this.method = method;
            this.url = url;
            matcher = new AntPathRequestMatcher(url, method);
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            Matcher that = (Matcher) o;
            return Objects.equals(method, that.method) &&
                    Objects.equals(url, that.url);
        }

        @Override
        public int hashCode() {
            return Objects.hash(method, url);
        }
    }
}
