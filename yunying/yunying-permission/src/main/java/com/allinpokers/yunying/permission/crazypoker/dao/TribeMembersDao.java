package com.allinpokers.yunying.permission.crazypoker.dao;

import com.allinpokers.yunying.dao.BaseDao;
import com.allinpokers.yunying.permission.crazypoker.entity.TribeMembers;
import com.allinpokers.yunying.permission.crazypoker.entity.example.TribeMembersExample;
import com.allinpokers.yunying.permission.crazypoker.entity.key.TribeMembersKey;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * 部落俱乐部列表  Mapper
 * 
 * <AUTHOR>
 */
@Mapper
@Repository("permissionTribeMembersDao")
public interface TribeMembersDao extends BaseDao<TribeMembers, TribeMembersExample, TribeMembersKey> {
}