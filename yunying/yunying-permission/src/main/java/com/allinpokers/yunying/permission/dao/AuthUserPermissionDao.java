package com.allinpokers.yunying.permission.dao;

import com.allinpokers.yunying.dao.BaseDao;
import com.allinpokers.yunying.permission.constants.EPermissionCode;
import com.allinpokers.yunying.permission.entity.AuthUserPermission;
import com.allinpokers.yunying.permission.entity.example.AuthUserPermissionExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 用户权限  Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AuthUserPermissionDao extends BaseDao<AuthUserPermission, AuthUserPermissionExample, Integer> {

    /**
     * 查询允许的资源ID
     *
     * @param authUserId
     * @param systemCode 系统代码
     * @return 资源ID
     */
    Set<Integer> findAllowResourceIds(@Param("authUserId") Integer authUserId, @Param("systemCode") String systemCode);

    /**
     * 批量删除
     *
     * @param authUserIds
     * @param permissionIds
     * @param operatorId
     */
    void deleteIn(@Param("authUserIds") Set<Integer> authUserIds,
                  @Param("permissionIds") Set<Integer> permissionIds, @Param("operatorId") Integer operatorId);

    /**
     * 获取用户在某个系统的数据权限
     *
     * @param authUserId
     * @param systemCode
     * @return
     */
    Set<String> findAuthUserDataPermission(@Param("authUserId") Integer authUserId, @Param("systemCode") String systemCode);

    void batchInsert(@Param("userPermissions") List<AuthUserPermission> userPermissions);

    List<AuthUserPermission> findDescendantUserPermissions(@Param("authUserIds") Set<Integer> authUserIds, @Param("permissionCodes") List<String> permissionCodes);
}