package com.allinpokers.yunying.permission.model.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(value = {"password"}, allowSetters = true)
public class LoginReq {
    @ApiModelProperty(value = "用户名", example = "root")
    private String username;

    @ApiModelProperty(value = "密码，需要进行Base64编码", example = "YWxsaW4xNjg=")
    private String password;

    @ApiModelProperty(value = "登录的系统, OSM/CMS", example = "OSM")
    private String systemCode;

    @ApiModelProperty("验证码，超级账号需要传，如果是超级账号，并且没有传验证码过来，返回错误码 10016")
    private String smsCode;

    @ApiModelProperty(value = "TOTP验证码", example = "123456")
    private String totpCode;

    @ApiModelProperty(hidden = true)
    private HttpServletRequest request;
}
