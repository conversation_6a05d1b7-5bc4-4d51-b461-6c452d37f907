package com.allinpokers.yunying.permission.entity;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 权限支持的授权类型  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuthPermissionStandBy {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 授权类型代码
     */
    @ApiModelProperty("授权类型代码")
    private String authTypeCode;

    /**
     * 权限ID
     */
    @ApiModelProperty("权限ID")
    private Integer authPermissionId;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updatedTime;
}