<?xml version="1.0" encoding="UTF-8"?>
<rooms>
	<!-- 房间数据配置 本次服务器编号为800000 -->
	<!--		
		1、房间一级分为6大类配置文件中按照typy_1 至type_6分类（新手 初级 中级 高级 比赛场 自建场 1 2 3 4 5 6）
		2、房间二级分为fast、slow两大类
		3、房间三级分为若干类根据盲注不同进行分类
		4、三级分类item可以有多个不能重复，最多只能有999个
		5、房间长度共7位以上，服务器编号+房间类型编号+房间编号组成完整房间编号
		   1-3位为房间编号(每个分类最多只能初始化999个房间) 4-6位为房间类型编号(最多只能有999个二级分类) 6-后面的为服务器编号
		   eg:1001001 其中服务器编号为1000000 房间类型编号为1001000
	-->
	<!-- 新手房间信息配置 -->
	<type_1>
	    <!-- 快速场  wait 等待时间-->
		<fast wait="15">
			<!--  id:房间类型编号 manzhu:小盲注 gold:筹码(普通场中一局需要携带的筹码或者比赛场中一局的参赛费) count:初始化房间个数 maxgold 携带区间-->
			<item id="100100" manzhu="1" damanzhu="2" gold="100" count="0" fuwufei="6" maxgold="500" min="150" max="6000" sl="0.1"/>
		</fast>
		<!-- 慢速场  wait 等待时间-->
		<solw wait="20">
			<item id="100200" manzhu="1" damanzhu="2" gold="100" count="0"  fuwufei="6" maxgold="500" min="150" max="6000" sl="0.1"/>
		</solw>
	</type_1>
	<!-- 初级房间信息配置 -->
	<type_2>
		<fast wait="15">
			<item id="100300" manzhu="5" damanzhu="10" gold="300"   count="0"  fuwufei="30" maxgold="6000" min="2000" max="50000" sl="0.1" />
			<item id="100400" manzhu="20" damanzhu="40" gold="1000"   count="0"  fuwufei="120" maxgold="18000" min="6000" max="70000"  sl="0.1"/>	
		</fast>
		<solw wait="20">
			<item id="100900" manzhu="5" damanzhu="10" gold="300"   count="0"  fuwufei="30" maxgold="6000" min="2000" max="50000" sl="0.1" />
			<item id="101000" manzhu="20" damanzhu="40" gold="1000"   count="0"  fuwufei="120" maxgold="18000" min="6000" max="70000"  sl="0.1"/>	
		</solw>
	</type_2>
	<!-- 中级房间信息配置 -->
	<type_3>
		<fast wait="15">
			<item id="110100" manzhu="50" damanzhu="100" gold="4000"   count="0" fuwufei="300" maxgold="60000" min="20000" max="500000" sl="0.1" />
			<item id="110200" manzhu="200" damanzhu="400" gold="16000"   count="0" fuwufei="1200" maxgold="180000" min="60000" max="700000" sl="0.1" />	
		</fast>
		<solw wait="20">
			<item id="110900" manzhu="50" damanzhu="100" gold="4000"   count="0" fuwufei="300" maxgold="60000" min="20000" max="300000"  sl="0.1"/>
			<item id="111000" manzhu="200" damanzhu="400" gold="16000"   count="0" fuwufei="1200" maxgold="180000" min="60000" max="700000" sl="0.1" />	
		</solw>
	</type_3>
	<!-- 高级房间信息配置 -->
	<type_4>
		<fast wait="15">
			<item id="120100" manzhu="500" damanzhu="1000" gold="40000"   count="0" fuwufei="3000" maxgold="500000" min="200000" max="2147483647" sl="0.1" />
		</fast>
		<solw wait="20">
			<item id="120900" manzhu="500" damanzhu="1000" gold="40000"   count="0" fuwufei="3000" maxgold="500000" min="200000" max="2147483647" sl="0.1" />
		</solw>
	</type_4>
	<!-- 比赛房间信息配置 -->
	<!-- 
	比赛gold 为参赛费用  最后决出1 2 3名，服务器 奖金按照总参赛费用的 50% 25% 15% 10% 默认分配2000
	gold 参赛费用
	 -->
	<type_5>
		<fast wait="15">
			<item id="130100" manzhu="10" damanzhu="20" gold="400"  count="0" fuwufei="20"  maxgold="400" min="400" max="10000" d1="2000" d2="1200"  sl="0.1"/>
			<item id="130200" manzhu="20" damanzhu="30" gold="2000"  count="0" fuwufei="20"  maxgold="2000" min="2000" max="20000" d1="10000" d2="6000"   sl="0.1"/>
			<item id="130300" manzhu="30" damanzhu="40" gold="20000"  count="0" fuwufei="20"  maxgold="20000" min="20000" max="2147483647" d1="100000" d2="60000"  sl="0.1"/>
		</fast>
		<solw wait="20">
			<item id="131100" manzhu="5" damanzhu="10" gold="440"  count="1" fuwufei="20"  maxgold="440" min="440" max="10000" d1="2000" d2="1200" sl="0.1"/>
		</solw>
	</type_5>
	<type_6>
		<fast wait="15">
			<item id="100000" manzhu="1" damanzhu="2" gold="400"  count="1" fuwufei="30" maxgold="200" min="200" max="2147483647" sl="0.1" />
		</fast>
		<solw wait="20">
			<item id="100000" manzhu="1" damanzhu="2" gold="400"  count="0" fuwufei="30" maxgold="200" min="200" max="2147483647" sl="0.1" />
		</solw>
        <solw wait="20">
            <item id="100000" manzhu="1" damanzhu="2" gold="400"  count="0" fuwufei="30" maxgold="200" min="200" max="2147483647" sl="0.1" />
        </solw>
	</type_6>
</rooms>