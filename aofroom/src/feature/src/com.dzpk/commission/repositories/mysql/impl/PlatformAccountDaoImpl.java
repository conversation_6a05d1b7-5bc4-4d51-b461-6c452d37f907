package com.dzpk.commission.repositories.mysql.impl;

import com.dzpk.commission.constant.ESystemAccount;
import com.dzpk.commission.repositories.mysql.IPlatformAccountDao;
import com.dzpk.commission.repositories.mysql.model.PlatformAccountLog;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.repositories.mysql.dbpool.DBUtil;
import org.apache.logging.log4j.Logger;

import java.math.BigDecimal;
import java.sql.*;

public class PlatformAccountDaoImpl implements IPlatformAccountDao {

    /** 日志服务 */
    private static Logger logger = LogUtil.getLogger(PlatformAccountDaoImpl.class);


    @Override
    public boolean updateJPBAccount(Integer amount) throws SQLException {
        return updatePlatformAccount(amount,ESystemAccount.JPB_ACCOUNT.getCode());
    }

    @Override
    public BigDecimal queryNewJPBAccount(Integer xiaoMang, Integer roomPath) throws SQLException {
        return getAccountChip(xiaoMang,roomPath);
    }

    @Override
    public boolean updateSystemAccount(Integer amount) throws SQLException {
        return updatePlatformAccount(amount,ESystemAccount.PLATFORM_ACCOUNT.getCode());
    }

    @Override
    public Long queryJPBAccount() throws SQLException {
        return getPlatformAccountChip(ESystemAccount.JPB_ACCOUNT.getCode());
    }

    @Override
    public Long querySystemAccount() throws SQLException {
        return getPlatformAccountChip(ESystemAccount.PLATFORM_ACCOUNT.getCode());
    }

    @Override
    public boolean insertJPBAccountRecordForIntoByDz(PlatformAccountLog platformAccountLog) throws SQLException {
        platformAccountLog.setChangeSource(8);//aof
        addJPBParam(platformAccountLog);
        return insertPlatformAccountLog(platformAccountLog);
    }

    @Override
    public boolean insertSystemAccountRecordForGameProfitByDz(PlatformAccountLog platformAccountLog) throws SQLException {
        platformAccountLog.setChangeSource(8);//aof
        addGameProfitParam(platformAccountLog);
        return insertPlatformAccountLog(platformAccountLog);
    }

    @Override
    public boolean insertSystemAccountRecordForGameConsume(PlatformAccountLog platformAccountLog) throws SQLException {
        platformAccountLog.setChangeSource(8);//aof
        addGameConsumeParam(platformAccountLog);
        return insertPlatformAccountLog(platformAccountLog);
    }

    @Override
    public boolean insertJPBetRecordByDz(PlatformAccountLog platformAccountLog) throws SQLException {
        platformAccountLog.setChangeSource(8);//aof
        addJPBetParam(platformAccountLog);
        return insertPlatformAccountLog(platformAccountLog);
    }
    @Override
    public int queryAndUpdateJPAccount(Integer amout,Integer mangzhu, Integer roomPath) {
        return updateJPAccount(amout,mangzhu,roomPath);
    }

    @Override
    public int queryJPTotalAccount(Integer roomPath) throws SQLException {
        Connection conn = null;
        PreparedStatement stam = null;
        ResultSet rs = null;
        conn = DBUtil.getConnection();
        int jackpotType = getjackType(roomPath);
        int totalFund=0;
        String  sql = "select sum(fund) as fund from jackpot_pool where jackpot_type="+jackpotType;
        try {
            stam = conn.prepareStatement(sql);
            rs = stam.executeQuery();
            if (rs.next()){
                totalFund=rs.getBigDecimal("fund").intValue();
            }
            return totalFund;
        }catch  (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(),e);
        }finally {
            DBUtil.closeStatement(stam);
            DBUtil.closeConnection(conn);
            if (rs != null) {
                DBUtil.closeResultSet(rs);
            }
        }
        return totalFund;
    }
    /**
     * 查询跑马灯奖池仓余额
     *
     * @return
     * @throws SQLException
     */
    @Override
    public Long queryHorseRaceLampAccount() throws SQLException {
        return getPlatformAccountChip(ESystemAccount.HORSE_RACE_LAMP_ACCOUNT.getCode());
    }

    /**
     * 更新跑马灯奖池仓余额
     *
     * @param amount
     * @return
     * @throws SQLException
     */
    @Override
    public boolean updateHorseRaceLampAccount(Integer amount) throws SQLException {
        return updatePlatformAccount(amount,ESystemAccount.HORSE_RACE_LAMP_ACCOUNT.getCode());
    }

    /**
     * 插入跑马灯奖池仓更新记录 来源德州项目
     *
     * @param platformAccountLog
     * @return
     * @throws SQLException
     */
    @Override
    public boolean insertHorseRaceLampAccountRecordForIntoByDz(PlatformAccountLog platformAccountLog) throws SQLException {
        platformAccountLog.setChangeSource(8);//aof
        addHorseRaceLampParam(platformAccountLog);
        return insertPlatformAccountLog(platformAccountLog);
    }

    /**
     * 添加跑马灯仓操作的初始设置
     * @param platformAccountLog
     */
    private void addHorseRaceLampParam(PlatformAccountLog platformAccountLog) {
        platformAccountLog.setPlatformCode(ESystemAccount.HORSE_RACE_LAMP_ACCOUNT.getCode());
        platformAccountLog.setType(33);//注入跑马灯奖池
        platformAccountLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
    }

    /**
     * 添加JP操作的初始设置
     * @param platformAccountLog
     */
    private void addJPBetParam(PlatformAccountLog platformAccountLog){
        platformAccountLog.setPlatformCode(ESystemAccount.JPBET_ACCOUNT.getCode());
        platformAccountLog.setType(28);//JP投彩
        platformAccountLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
    }

    /**
     * 添加玩家消费行为的初始设置
     * @param platformAccountLog
     */
    private void addGameConsumeParam(PlatformAccountLog platformAccountLog){
        platformAccountLog.setPlatformCode(ESystemAccount.PLATFORM_ACCOUNT.getCode());
        platformAccountLog.setType(10);//牌局内消耗行为
        platformAccountLog.setOpId(-1);//系统
        platformAccountLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
    }
    private BigDecimal getAccountChip(Integer xiaoMang, Integer roomPath) {
        Connection dbConnection = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        ResultSet selectJack=null;
        int jackpotType = getjackType(roomPath);
        String selectJackPoolSetting="select jackpot_id from jackpot_pool_setting where blind_code="+xiaoMang+" and jackpot_type="+jackpotType;

        try {
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(selectJackPoolSetting);
            selectJack=ps.executeQuery();
            Integer jackpotId=null;
            if (selectJack.next()) {
                jackpotId = selectJack.getInt("jackpot_id");
            }
            String getPlatformAccountChipSql = "select fund from jackpot_pool where jackpot_id = " +jackpotId;
            ps = dbConnection.prepareStatement(getPlatformAccountChipSql);
            rs = ps.executeQuery();
            if (rs.next()) {
                BigDecimal platformAccountChip = rs.getBigDecimal("fund");
                return platformAccountChip;
            }
        } catch (Exception e) {
            logger.debug(e.getMessage());
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }

        return BigDecimal.ZERO;
    }

    /**
     * 添加JPB操作的初始设置
     * @param platformAccountLog
     */
    private void addJPBParam(PlatformAccountLog platformAccountLog){
        platformAccountLog.setPlatformCode(ESystemAccount.JPB_ACCOUNT.getCode());
        platformAccountLog.setType(1);//JPB注入
        platformAccountLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
    }

    /**
     * 添加战绩分润操作的初始设置
     * @param platformAccountLog
     */
    private void addGameProfitParam(PlatformAccountLog platformAccountLog){
        platformAccountLog.setPlatformCode(ESystemAccount.PLATFORM_ACCOUNT.getCode());
        platformAccountLog.setType(3);//战绩分润
        platformAccountLog.setOpId(-1);//系统
        platformAccountLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
    }

    private long getPlatformAccountChip(int code) {
        Connection dbConnection = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        String getPlatformAccountChipSql =
                "select chip from platform_account where code = " +code;
        try {
            logger.debug("getPlatformAccountChipSql===>" + getPlatformAccountChipSql);
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(getPlatformAccountChipSql);
            rs = ps.executeQuery();
            if (rs.next()) {
                long platformAccountChip = rs.getLong("chip");
                return platformAccountChip;
            }
        } catch (Exception e) {
            logger.debug(e.getMessage());
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }

        return 0;
    }
    private int updateJPAccount(Integer amout,int mangzhu,int roomPath){
        Connection dbConnection = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        int jackpotType = getjackType(roomPath);
        String getPlatformAccountChipSql = "select * from jackpot_pool_setting setting inner join " +
                "jackpot_pool pool on setting.jackpot_id=pool.jackpot_id where blind_code=" +mangzhu+
                " and pool.jackpot_type="+jackpotType;

        int jpChip=0;
        try {
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(getPlatformAccountChipSql);
            rs = ps.executeQuery();
            if (rs.next()) {
                jpChip = rs.getInt("fund");
            }
            String updatePlatformAccountChipSql = "update jackpot_pool pool join jackpot_pool_setting setting " +
                    "on setting.jackpot_id=pool.jackpot_id set fund = fund + " + amout + " ,pool.updated_time=now(), " +
                    "version=version+1 where setting.blind_code=" +mangzhu+ " and pool.jackpot_type="+jackpotType;
            ps = dbConnection.prepareStatement(updatePlatformAccountChipSql);
            int effectedRowNum = ps.executeUpdate();
            return effectedRowNum > 0?jpChip+amout:jpChip;
        } catch (Exception e) {
            logger.debug(e.getMessage());
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }
        return 0;
    }
    private int getjackType(Integer roomPath){
        int jackType=0;
        if (roomPath==61){
            jackType=1;
        }else if (roomPath==91){
            jackType=2;
        }else if (roomPath==31){
            jackType=3;
        }else jackType=4;
        return jackType;
    }

    /**
     * 更新对应code仓 金豆
     * @param updateChip
     * @param code
     * @return
     */
    private boolean updatePlatformAccount(int updateChip,int code) {

        Connection dbConnection = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        String updatePlatformAccountChipSql =
                "update platform_account set chip = chip + " + updateChip + " ,updated_time=now() where code = " + code;
        try {
            logger.debug("updatePlatformAccountChipSql===>" + updatePlatformAccountChipSql);
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(updatePlatformAccountChipSql);
            int effectedRowNum = ps.executeUpdate();

            return effectedRowNum > 0;

        } catch (Exception e) {
            logger.debug(e.getMessage());
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }

        return false;
    }

    /**
     * 插入仓操作记录
     * @param platformAccountLog
     * @return
     */
    private boolean insertPlatformAccountLog(PlatformAccountLog platformAccountLog) {
        Connection dbConnection = null;
        PreparedStatement stam = null;
        ResultSet rs = null;

        String insertPlatformAccountLogSql =
                "insert into platform_account_log (platform_code,type,change_source,current_chip,change_chip,desction," +
                        "external_id,op_id,created_time) values (?,?,?,?,?,?,?,?,?)";
        try {
            dbConnection = DBUtil.getConnection();
            stam = dbConnection.prepareStatement(insertPlatformAccountLogSql, Statement.RETURN_GENERATED_KEYS);
            int i = 1;
            stam.setInt(i++, platformAccountLog.getPlatformCode());
            stam.setInt(i++, platformAccountLog.getType());
            stam.setInt(i++,platformAccountLog.getChangeSource());
            stam.setLong(i++, platformAccountLog.getCurrentChip());
            stam.setInt(i++, platformAccountLog.getChangeChip());
            stam.setString(i++, platformAccountLog.getDescription());
            stam.setString(i++, platformAccountLog.getExternalId());
            stam.setInt(i++, platformAccountLog.getOpId());
            stam.setTimestamp(i++,platformAccountLog.getCreateTime());
            stam.executeUpdate();
            rs = stam.getGeneratedKeys();
            logger.debug("insertPlatformAccountLogSql===>" + stam.toString());

        } catch (Exception e) {
            logger.debug(e.getMessage());
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(stam);
            DBUtil.closeConnection(dbConnection);
        }

        return false;
    }


}
