package com.dzpk.commission;

import com.dzpk.commission.constant.EWho;
import org.apache.commons.lang.NullArgumentException;

public class RoomFeeConfig {

    /** 应用目标 */
    private EWho target;

    /**
     * 战绩盈亏比例
     */
    private Double earningRatio;
    /**
     * 保险盈亏比例
     */
    private Double insuranceRatio;

    /**
     * JP击中奖励比例
     */
    private Double jackpotRatio;

    /**
     * 综合比例
     */
    private Double overallRatio;

    /**
     * 构建服务费配置
     * @param dst             使用目标
     * @param earningRatio    战绩盈亏比例, 百分数
     * @param insuranceRatio  保险盈亏比例, 百分数
     * @param jackpotRatio    JP击中奖励比例, 百分数
     * @param overallRatio    综合比例, 百分数
     */
    public RoomFeeConfig(EWho dst,
                         double earningRatio, double insuranceRatio, double jackpotRatio, double overallRatio){
        if(null == dst)
            throw new NullArgumentException("dst is null");

        this.target = dst;
        this.earningRatio = earningRatio;
        this.insuranceRatio = insuranceRatio;
        this.jackpotRatio = jackpotRatio;
        this.overallRatio = overallRatio;
    }

    public EWho getTarget() {
        return target;
    }

    public Double getEarningRatio() {
        return earningRatio;
    }

    public Double getInsuranceRatio() {
        return insuranceRatio;
    }

    public Double getJackpotRatio() {
        return jackpotRatio;
    }

    public Double getOverallRatio() {
        return overallRatio;
    }
}
