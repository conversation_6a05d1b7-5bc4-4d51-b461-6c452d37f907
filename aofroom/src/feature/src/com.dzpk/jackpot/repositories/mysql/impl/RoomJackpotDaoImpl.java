package com.dzpk.jackpot.repositories.mysql.impl;

import com.dzpk.jackpot.RoomJackpotConfig;
import com.dzpk.jackpot.repositories.mysql.IRoomJackpotDao;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.repositories.mysql.dbpool.DBUtil;
import org.apache.logging.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class RoomJackpotDaoImpl implements IRoomJackpotDao {
    /** 日志服务 */
    private static Logger logger = LogUtil.getLogger(RoomJackpotDaoImpl.class);

    /**
     * 通过房间id获取对应房间的jackpot玩法配置
     *
     * @param roomPath  房间类型
     * @param roomId    房间id
     */
    private static  final String SQL_GET_ROOM_JACKPOTCONFIG = "select jackpot_id,blind_code,blind_name,royalFlush_ratio," +
            "straightFlush_ratio,fourOfAKind_ratio " +
            "from room_jackpot_setting where room_id = ? and room_path = ?";
    public RoomJackpotConfig getRoomJackpotConfig(int roomId, int roomPath){
        RoomJackpotConfig config = null;

        Connection conn = null;
        PreparedStatement statement = null;
        ResultSet rs = null;
        try {
            conn = DBUtil.getConnection();
            statement = this.createStatement(conn,SQL_GET_ROOM_JACKPOTCONFIG,
                    roomId,roomPath);
            rs = statement.executeQuery();
            if (rs.next()) {
                config = new RoomJackpotConfig();
                config.setJackpotId(rs.getInt("jackpot_id"));
                config.setBlindCode(rs.getInt("blind_code"));
                config.setBlindName(rs.getString("blind_name"));
                config.setRoyalFlushRatio(rs.getDouble("royalFlush_ratio"));
                config.setStraightFlushRatio(rs.getDouble("straightFlush_ratio"));
                config.setFourOfAKindRatio(rs.getDouble("fourOfAKind_ratio"));
            }

            return config;
        } catch (Exception ex) {
            logger.error(String.format("加载牌局的JP配置失败：roomId=%s , roomPath=%s -> %s",
                    roomId,roomPath,ex.getMessage()), ex);
            return config;
        } finally {
            this.close(rs,statement,conn);
        }
    }

    private PreparedStatement createStatement(Connection conn,String sql, Object... params) throws SQLException {
        PreparedStatement stam = conn.prepareStatement(sql);
        int index = 1;
        if (params != null && params.length > 0) {
            for (Object param : params) {
                stam.setObject(index, param);
                index++;
            }
        }
        return stam;
    }
    private void close(ResultSet result, PreparedStatement stam, Connection conn) {
        closeResultSet(result);
        closePreparedStatement(stam);
        closeConnection(conn);
    }
    private void closeResultSet(ResultSet result) {
        try {
            if (result != null) {
                result.close();
            }
        } catch (Exception se) {
            logger.warn("SQL Exception while closing ResultSet : "+se.getMessage(),se);
        }
    }
    private void closePreparedStatement(PreparedStatement stam) {
        try {
            if (stam != null) {
                stam.close();
            }
        } catch (Exception se) {

        }
    }
    private void closeConnection(Connection conn) {
        try {
            if (conn != null && !conn.isClosed()) {
                conn.close();
            }
        } catch (Exception se) {
            logger.warn("SQL Exception while closing DB Connection : "+se.getMessage(),se);
        }
    }
}
