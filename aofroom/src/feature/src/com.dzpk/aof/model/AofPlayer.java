package com.dzpk.aof.model;

import com.dzpk.common.utils.LogUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.logging.log4j.Logger;

/**
 * 支持aof模式结算时所需要的数据
 */
@Getter
@Setter
public class AofPlayer {

    private Logger logger = LogUtil.getLogger(AofPlayer.class);

    private int userId;  //玩家id
    private int bringOutPl;   //带出的盈利


    public AofPlayer(int userId, int bringOutPl) {
        this.userId = userId;
        this.bringOutPl = bringOutPl;
    }

    public void addBringOutPl(int pl){
        this.bringOutPl = this.bringOutPl + pl;
        logger.debug("玩家累出带出积分,pl={},bringOutPl={}",pl,bringOutPl);
    }

}
