package com.dzpk.component.repositories.mongo;

import com.mongodb.*;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoCollection;
import com.i366.cache.Cache;
import org.apache.logging.log4j.Logger;
import org.bson.Document;
import org.bson.conversions.Bson;

/**
 * Mongodb服务类
 *
 * <AUTHOR>
 */
public class MongodbService {
    /** 日志 */
    private static final Logger logger = org.apache.logging.log4j.LogManager.getLogger(MongodbService.class);

    /** Mongo客户端实例 */
    private static Object mongoClientLock = new Object();
    private static MongoClient mongoClient;

    static {
    /*    LogManager.getLogger("org.mongodb.driver.connection").setLevel(Level.ERROR);
        LogManager.getLogger("org.mongodb.driver.management").setLevel(Level.ERROR);
        LogManager.getLogger("org.mongodb.driver.cluster").setLevel(Level.ERROR);
        LogManager.getLogger("org.mongodb.driver.protocol.insert").setLevel(Level.ERROR);
        LogManager.getLogger("org.mongodb.driver.protocol.query").setLevel(Level.ERROR);
        LogManager.getLogger("org.mongodb.driver.protocol.update").setLevel(Level.ERROR);*/
    }

    /**
     * 获取mongo连接实例
     *
     * @return
     */
    public static MongoClient getMongoInstance() {
        if(null == mongoClient) {
            synchronized (mongoClientLock) {
                if(null == mongoClient) {
                    String url = Cache.p.getProperty("mongodb.url");
                    String databaseName = Cache.p.getProperty("mongodb.database");
                    if(logger.isDebugEnabled()){
                        logger.debug("Initializing mongo's client:" + url);
                    }
                    mongoClient = new CustMongoClient(
                            new MongoClientURI(
                                    url,
                                    MongoClientOptions.builder().cursorFinalizerEnabled(false)
                                            .readPreference(ReadPreference.secondaryPreferred())
                            ),databaseName
                    );
                    if(logger.isDebugEnabled()){
                        logger.debug("Initialized mongo's client successfully:" + url);
                    }
                }
            }
        }

        return mongoClient;
    }

    /**
     * 查找
     *
     * @param coll
     * @param filter
     * @return
     */
    public static MongoCursor<Document> find(MongoCollection<Document> coll, Bson filter) {
        return coll.find(filter).iterator();
    }

    /**
     * 查找多条记录
     *
     * @param coll
     * @param filter
     * @param limit
     * @return
     */
    public static MongoCursor<Document> find(MongoCollection<Document> coll, Bson filter,
                                             int limit, String order, int asc) {
        Bson orderBy = new BasicDBObject(order, asc);
        return coll.find(filter).sort(orderBy).limit(limit).iterator();
    }

    /**
     * 查找单条记录
     *
     * @param coll
     * @param filter
     * @return
     */
    public static MongoCursor<Document> findOne(MongoCollection<Document> coll, Bson filter) {
        return coll.find(filter).limit(1).iterator();
    }

    /**
     * 删掉多条记录
     *
     * @param col
     * @param filter
     */
    public static long removeMany(MongoCollection<Document> col, Bson filter) {
        return col.deleteMany(filter).getDeletedCount();
    }

    /**
     * 分页查找
     */
    public static MongoCursor<Document> findByPage(MongoCollection<Document> coll,
                                                   Bson filter, int pageNo, int pageSize) {
        Bson orderBy = new BasicDBObject("_id", 1);
        return coll.find(filter).sort(orderBy).skip((pageNo - 1) * pageSize).limit(pageSize).iterator();
    }

    /**
     * 批量update
     *
     * @param col
     * @param updateFilter
     * @param updateSet
     */
    public static void updateMany(MongoCollection<Document> col, Bson updateFilter, Bson updateSet) {
        col.updateMany(updateFilter, updateSet);
    }

    /**
     * 关闭指定MongoClient实例
     * 提供给业务方法中调用，由于单例模式，不能关闭。
     */
    public static void close(MongoClient mongoClient) {
        /*if (mongoClient != null) {
            try {
                mongoClient.close();
                if(logger.isDebugEnabled()){
                    logger.debug("Closed mongo's client successfully!");
                }
            }catch (Exception ex){
                logger.warn("Closed mongo's client failed:"+ex.getMessage(),ex);
            }
        }*/
    }

    /**
     * 关闭指定Cursor实例
     */
    public static void closeCursor(MongoCursor<?> cursor) {
        if (cursor != null) {
            try {
                cursor.close();
                if(logger.isDebugEnabled()){
                    logger.debug("Closed mongo's cursor successfully!!");
                }
            }catch (Exception ex){
                logger.warn("Closed mongo's cursor failed:"+ex.getMessage(),ex);
            }finally {
                cursor = null;
            }
        }
    }
}
