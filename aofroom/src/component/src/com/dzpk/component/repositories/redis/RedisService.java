package com.dzpk.component.repositories.redis;

import java.util.*;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.db.model.RoomSearchInfo;
import com.i366.constant.Constant;
import org.apache.logging.log4j.Logger;
import com.i366.model.room.Room;
import com.alibaba.fastjson.JSON;

import redis.clients.jedis.Jedis;

/**
 * redis 缓存服务类
 * <AUTHOR>
 *
 */
public class RedisService {

	private final static Logger logger = LogUtil.getLogger(RedisService.class);

	private static volatile RedisService redisService = null;

	/** 玩家维度 **/
	private static String userPre = "urs:";
    private static String userAutoOpPre = userPre + "autoOpRooms:";            // 玩家留盲代打房间
    private static String userRoomApplyPre = userPre + "bringInRooms:";        // 玩家带入过的房间

    /** 房间维度 **/
    private static String roomPre = "room:";
    private static String roomKickOutUserPre = roomPre + "kickOutUser:";       // 房间里的踢出玩家集合
    public  static String roomSkyActivityPre = roomPre + "skyActivity:";       // 房间里符合红包资格的玩家集合
    public  static String roomAutoCreatePre = roomPre + "autoCreate:";         // 自动创建房间的集合
    public static String roomPlayerList = roomPre + "roomPlayerList:"; // 房间玩家列表
    public static String roomForceKick = roomPre + "forcedKick:"; // 強制踢人
    public static String roomForceStandUp = roomPre + "forcedStandUp:"; // 強製站起
    /** 运营端维度 **/
    private static String osmPre = "osm:";
    private static String cancelledAOFRoomsKeyPre = osmPre + "cancelAofRooms:";      // 要解散的aof房间集合
    private static String clubBringInRoomPre = osmPre + "clubBringInRooms:";      // 俱乐部中玩家带入过的房间集合
    private static String cardControlRoomPre = osmPre + "ct:";                    // 指定发牌
    private static String userLastRoomIdPre = osmPre + "lastRoomId:";         // 玩家最后带入的房间(德州、必下场、aof公用同一个key)
    private static String mtDispatchPre = osmPre + "mt:";         // mt玩家派遣
    private static String mtDispatchSetPre = osmPre + "dispatch:mt";         // 已经派遣的mt玩家集合

	/**
	 * 初始化redis服务
	 */
	public static RedisService getRedisService() {
		if (redisService == null) {
		    synchronized (RedisService.class) {
                if (redisService == null) {
                    redisService = new RedisService();
                }
		    }
		}
		return redisService;
	}

	public Jedis getJedis() {
		return RedisPoolService.getJedis();
	}

	public void close(Jedis jedis) {
		if (jedis != null) {
			jedis.close();
		}
	}

    /**
     * 增加俱乐部中带入的房间集合
     * @param clubId
     * @param roomId
     */
	public void addClubBringRoomSet(int clubId,int roomId){
        String urs = clubBringInRoomPre + String.valueOf(clubId);
        Jedis jedis = getJedis();
        try {
            jedis.sadd(urs, String.valueOf(roomId));
        } finally {
            close(jedis);
        }
    }

    /**
     * 删除俱乐部中带入的房间
     * @param clubId
     * @param roomId
     */
    public void removeClubBringRoomSet(int clubId,int roomId){
        String urs = clubBringInRoomPre + String.valueOf(clubId);
        Jedis jedis = getJedis();
        try {
            jedis.srem(urs, String.valueOf(roomId));
            if (jedis.scard(urs) == 0) {
                jedis.del(urs);
            }
        } finally {
            close(jedis);
        }
    }

    /**
     * 增加房间到用户已经带入过的集合
     * @param userId
     * @param roomPath
     * @param roomId
     */
    public void addUserRoomApplySet(int userId, int roomPath, int roomId) {
        String urs = userRoomApplyPre  + String.valueOf(userId);
        String member = String.valueOf(roomPath) + ":" + String.valueOf(roomId);
        Jedis jedis = getJedis();
        try {
            jedis.sadd(urs, member);
        } finally {
            close(jedis);
        }
    }

    /**
     * 删除房间到用户已经带入过得集合
     * @param userId
     * @param roomPath
     * @param roomId
     */
    public void removeUserRoomApplySet(int userId, int roomPath, int roomId) {
        String urs = userRoomApplyPre + String.valueOf(userId);
        String member = String.valueOf(roomPath) + ":" + String.valueOf(roomId);
        Jedis jedis = getJedis();
        try {
            jedis.srem(urs, member);
            if (jedis.scard(urs) == 0) {
                jedis.del(urs);
            }
        } finally {
            close(jedis);
        }
    }

    /**
     * 设置玩家留盲代打房间信息
     * @param userId
     * @param map
     */
    public void setAutoOp(int userId, int roomId, Map<String, String> map) {
        String userAutoOpKey = userAutoOpPre + String.format("%d@%d", userId, roomId);
        Jedis jedis = getJedis();
        try {
            jedis.hmset(userAutoOpKey, map);
        } finally {
            close(jedis);
        }
    }

    /**
     * 获取玩家留盲代打房间信息
     * @param userId
     * @param roomId
     * @return
     */
    public Map<String, String> getAutoOp(int userId, int roomId) {
        String userAutoOpKey = userAutoOpPre + String.format("%d@%d", userId, roomId);
        Jedis jedis = getJedis();
        try {
            return jedis.hgetAll(userAutoOpKey);
        } finally {
            close(jedis);
        }
    }

    public boolean hasAutoOp(int userId) {
        String userAutoOpKey = userAutoOpPre + String.format("%d@*", userId);
        Jedis jedis = getJedis();
        try {
            return !jedis.keys(userAutoOpKey).isEmpty();
        } finally {
            close(jedis);
        }
    }

    /**
     * 运营系统，记录用户最后一次带入成功的房间
     * @param roomId
     * @param userId
     */
    public void addLastRoomIdToUser(int roomId, int userId) {
        String roomKey = userLastRoomIdPre + userId;
        Jedis jedis = getJedis();
        try {
            jedis.set(roomKey, String.valueOf(roomId));
        } finally {
            close(jedis);
        }
    }

    /**
     * 运营系统，房间关闭时，用户还在当前房间则清理记录
     * @param roomId
     * @param userIdList
     * @param throwEx true : 抛出内部异常，false ： 不抛出异常
     */
    public void delLastRoomIdByUser(int roomId, Set<Integer> userIdList,boolean throwEx) {
        if(null == userIdList || userIdList.isEmpty())
            return;

        Jedis jedis = getJedis();
        String[] realDelKeys = null;
        try {
            int idx = 0;
            String[] delKeys = new String[userIdList.size()];
            String roomIdStr = String.valueOf(roomId);
            for(Integer userId : userIdList) {
                String roomKey = userLastRoomIdPre + userId;
                String lastRoomId = jedis.get(roomKey);
                logger.debug("delLastRoomIdByUser roomId:" + roomId + " userId=" + userId + " lastRoomId=" + lastRoomId);
                if (roomIdStr.equals(lastRoomId)) {
                    delKeys[idx++] = roomKey;
                }
            }

            if(idx>0){
                realDelKeys = new String[idx];
                System.arraycopy(delKeys,0,realDelKeys,0,idx);
                jedis.del(realDelKeys);
            }
        }catch (Exception ex){
            if(throwEx)
                throw ex;
            else{
                logger.warn(String.format("Failed to delete redis for room %s with key : %s ",
                        roomId, (null != realDelKeys? Arrays.toString(realDelKeys):"")),ex);
            }
        }finally{
            close(jedis);
        }
    }

    /**
     * 删除玩家留盲代打信息
     * @param userId
     * @param roomId
     */
    public void delAutoOp(int userId, int roomId) {
        String userAutoOpKey = userAutoOpPre + String.format("%d@%d", userId, roomId);
        Jedis jedis = getJedis();
        try {
            jedis.del(userAutoOpKey);
        } finally {
            close(jedis);
        }
    }

    /**
     * 重启服务器 清空对应服务器上 玩家带入信息
     * @param roomInfoSet
     */
	private void clearUserRoomApplyKey(Set<String> roomInfoSet) {
        Jedis jedis = getJedis();
        try {

            Set<String> keys = jedis.keys(userRoomApplyPre + "*");
            String[] members = new String[roomInfoSet.size()];
            for (String k : keys) {
                jedis.srem(k, roomInfoSet.toArray(members));
            }

        } finally {
            close(jedis);
        }
	}

    /**
     * 重启服务器 清空对应服务器上 玩家最后带入的房间
     * @param roomIds
     */
    private void clearLastRoomId(Set<Integer> roomIds) {
        Set<String> userIdKeys = new HashSet<>();

        Jedis jedis = getJedis();
        try {

            Set<String> keys = jedis.keys(userLastRoomIdPre + "*");
            for (String k : keys) {
                String roomId = jedis.get(k);
                if(null != roomId && roomIds.contains(Integer.parseInt(roomId))){
                    userIdKeys.add(k);
                }

            }

            if(userIdKeys.size() > 0){
                jedis.del(userIdKeys.toArray(new String[userIdKeys.size()]));
            }
        } finally {
            close(jedis);
        }
    }

    /**
     * 重启服务器 清空对应服务器上 玩家留盲代打信息
     * @param roomIds
     */
    private void clearUserAutoOpKey(Set<Integer> roomIds) {
        Set<String> userAutoOpKeys = new HashSet<>();

        Jedis jedis = getJedis();
        try {

            Set<String> keys = jedis.keys(userAutoOpPre + "*");
            for (String k : keys) {
                Map<String, String> userAutoOp = jedis.hgetAll(k);
                if (userAutoOp != null ) {
                    String roomId = userAutoOp.get("roomId");
                    if(roomIds.contains(Integer.parseInt(roomId))){
                        userAutoOpKeys.add(k);
                    }
                }
            }

            if(!userAutoOpKeys.isEmpty()){
                jedis.del(userAutoOpKeys.toArray(new String[0]));
            }
        } finally {
            close(jedis);
        }

    }

    /**
     * 重启服务器 清空对应服务器上 被踢出牌局的玩家信息
     * @param roomIds
     */
    private void clearKickOutUserKey(Set<Integer> roomIds){
        List<String> roomIdKeys = new ArrayList<>();
        for(int roomId:roomIds){
            String roomIdKey = roomKickOutUserPre + roomId;
            roomIdKeys.add(roomIdKey);
        }
        Jedis jedis = getJedis();
        try {
            if(roomIdKeys.size() > 0){
                jedis.del(roomIdKeys.toArray(new String[roomIdKeys.size()]));
            }
        } finally {
            close(jedis);
        }

    }

    /**
     * 重启服务器 清空对应服务器上 俱乐部带入的牌局信息
     * @param roomInfoSet
     */
    private void clearClubBringInRoomKey(Set<String> roomInfoSet){
        Jedis jedis = getJedis();
        try {

            Set<String> keys = jedis.keys(clubBringInRoomPre + "*");
            String[] members = new String[roomInfoSet.size()];

            for (String k : keys) {
                jedis.srem(k,roomInfoSet.toArray(members));
            }

        } finally {
            close(jedis);
        }

    }

    /**
     * 玩家被强制踢出房间后,增加到房间被踢出的玩家集合中
     * @param userId
     * @param roomId
     */
    public void addKickOutUser(int userId, int roomId) {
        String member = roomKickOutUserPre  + String.valueOf(roomId);
        Jedis jedis = getJedis();
        try {
            jedis.sadd(member, userId + "");
        }finally {
            close(jedis);
        }
    }

    private void clearSkyActivity(Set<Integer> roomIds){
        List<String> roomIdKeys = new ArrayList<>();
        for(int roomId:roomIds){
            String roomIdKey = roomSkyActivityPre + roomId;
            roomIdKeys.add(roomIdKey);
        }
        Jedis jedis = getJedis();
        try {
            if(roomIdKeys.size() > 0){
                jedis.del(roomIdKeys.toArray(new String[roomIdKeys.size()]));
            }
        } finally {
            close(jedis);
        }
    }

    private void clearAutoCreateRoom(){
        Jedis jedis = getJedis();
        try {
            jedis.del(roomAutoCreatePre + Constant.ROOM_PATH_AOF);

        } finally {
            close(jedis);
        }
    }

    /**
     * 重启服务时,清空对应服务器上的key
     * @param roomList 房间ids
     */
	public void clear(List<RoomSearchInfo> roomList) throws Exception{
        try {
            logger.debug("clear redis keys");
            if(roomList.size() > 0){
                Set<Integer> roomIdSet = new HashSet<>();
                Set<String> roomIdStringSet = new HashSet<>();
                Set<String> roomInfoSet = new HashSet<>();
                for(RoomSearchInfo roomSearchInfo:roomList){
                    roomIdSet.add(roomSearchInfo.getRoomId());
                    roomInfoSet.add(roomSearchInfo.getRoomPath() + ":" + roomSearchInfo.getRoomId());
                    roomIdStringSet.add(String.valueOf(roomSearchInfo.getRoomId()));
                }
                logger.debug("clear redis keys,roomIdSet={}",roomIdSet.toString());

                clearUserAutoOpKey(roomIdSet);
                clearKickOutUserKey(roomIdSet);
                clearUserRoomApplyKey(roomInfoSet);
                clearLastRoomId(roomIdSet);
                clearClubBringInRoomKey(roomIdStringSet);
                clearSkyActivity(roomIdSet);
                clearAutoCreateRoom();
            }

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("clear redis keys, error",e);
            throw new Exception(e);
        }
    }

    public Set<String> getCancelledGame(int roomPath) {
        String cancelledSetKey = "";
        switch (roomPath){
            case 63:
                cancelledSetKey = cancelledAOFRoomsKeyPre;
                break;
        }
        Jedis jedis = getJedis();
        try {
            Set<String> roomIds = jedis.smembers(cancelledSetKey);
            return roomIds;
        } finally {
            close(jedis);
        }
    }

    public void removeCancelledGame(int roomPath,String key){
        String cancelledSetKey = "";
        switch (roomPath){
            case 63:
                cancelledSetKey = cancelledAOFRoomsKeyPre;
                break;
        }
        Jedis jedis = getJedis();
        try {
            logger.debug("removeCancelledGame key =>" + cancelledSetKey + "," + key);
            jedis.srem(cancelledSetKey,key);
        } finally {
            close(jedis);
        }
    }

    /**
     * 获取该房间被踢出的玩家的集合
     * @param roomId
     * @return
     */
    public Set<String> getKickOutUser(int roomId) {
        Jedis jedis = getJedis();
        try {
            Set<String> kickOutUserSet = jedis.smembers(roomKickOutUserPre + String.valueOf(roomId));
            return kickOutUserSet;
        } finally {
            close(jedis);
        }
    }

    /**
     * 获取指定房间发的牌的信息
     * @param roomId
     * @param roomPath
     * @return
     */
    public Map<String, String> getCardControlInfo(int roomId,int roomPath) {
        String roomKey = cardControlRoomPre + roomPath  + "_" + roomId;
        Jedis jedis = getJedis();
        try {
            Map<String, String> roomInfo = jedis.hgetAll(roomKey);
            return roomInfo;
        } finally {
            close(jedis);
        }
    }

    /**
     * 删除指定房间发的牌的信息
     * @param roomId
     * @param roomPath
     * @return
     */
    public void delCardControlInfo(int roomId,int roomPath) {
        String roomKey = cardControlRoomPre + roomPath  + "_" + roomId;
        Jedis jedis = getJedis();
        try {
            jedis.del(roomKey);
        } finally {
            close(jedis);
        }
    }

    /**
     * 获取mt玩家id
     * @param roomId
     * @param roomPath
     * @return
     */
    public int getMtDispatchUserId(int roomId,int roomPath) {
        String roomKey = mtDispatchPre + roomPath  + "_" + roomId;
        Jedis jedis = getJedis();
        try {
            int mtUserId = jedis.get(roomKey) == null ? 0 :Integer.parseInt(jedis.get(roomKey));
            return mtUserId;
        } finally {
            close(jedis);
        }
    }

    /**
     * 删除mt玩家id
     * @param roomId
     * @param roomPath
     * @return
     */
    public void delMtDispatchUserId(int roomId,int roomPath) {
        String roomKey = mtDispatchPre + roomPath  + "_" + roomId;
        Jedis jedis = getJedis();
        try {
            jedis.del(roomKey);
        } finally {
            close(jedis);
        }
    }

    /**
     * 在已经派遣过的mt玩家集合删除离开的mt
     * @return
     */
    public void delDispatchMtSet(String userId) {
        Jedis jedis = getJedis();
        try {
            Set<String> dispatchMtSet = jedis.smembers(mtDispatchSetPre);
            if(dispatchMtSet.contains(userId)){
                jedis.srem(mtDispatchSetPre,userId);
            }
        } finally {
            close(jedis);
        }
    }

    /**
     * 在已经派遣过的mt玩家集合删除离开的mt
     * @return
     */
    public void delAllDispatchMtSet(Set<String> userIds) {
        Jedis jedis = getJedis();
        try {
            jedis.srem(mtDispatchSetPre,userIds.toArray(new String[userIds.size()]));
        } finally {
            close(jedis);
        }
    }

    /**
     * 获取自动创桌的房间集合
     * @param roomPath
     * @return
     */
    public Set<String> getAutoCreateRooms(int roomPath) {
        String roomAutoCreateSetKey = roomAutoCreatePre + roomPath;
        Jedis jedis = getJedis();
        try {
            logger.debug("roomAutoCreateSetKey =>" + roomAutoCreateSetKey);
            Set<String> roomIds = jedis.smembers(roomAutoCreateSetKey);
            return roomIds;
        } finally {
            close(jedis);
        }
    }

    public void removeAutoCreateRooms(int roomPath,String key){
        String roomAutoCreateSetKey = roomAutoCreatePre + roomPath;

        Jedis jedis = getJedis();
        try {
            logger.debug("removeAutoCreateRooms key =>" + roomAutoCreateSetKey + "," + key);
            jedis.srem(roomAutoCreateSetKey,key);
        } finally {
            close(jedis);
        }
    }

     /*
     * 房間玩家列表
     * 
     */
    public void addRoomPlayerList(Integer roomId, String roomplayers) {
        String urs = roomPlayerList + roomId;

        Jedis jedis = getJedis();
        try {
            logger.debug("chungTest: add");
            jedis.set(urs, roomplayers);
            logger.debug("chungTest Success: add");
        }catch (Exception e) {
            logger.error("chungTest Error: " + e.getMessage(), e);
        } finally {
            close(jedis);
        }
    }

    /**
     * 删除房間玩家列表
     * 
     * @param roomId
     * @return
     */
    public void delRoomPlayerList(Integer roomId, Integer roomPath) {
        String urs = roomPlayerList + roomId + ":" + roomPath;
        Jedis jedis = getJedis();
        try {
            logger.debug("chungTest: delete");
            jedis.del(urs);
        }catch (Exception e) {
            logger.error("chungTest Error: " + e.getMessage(), e);
        }finally {
            close(jedis);
        }
    }

    /**
     * 更改房間玩家列表
     * 
     * @param roomId
     * @return
     */
    public void modifyRoomPlayerList(Integer roomId, Integer roomPath, String roomplayers) {
        String urs = roomPlayerList + roomId + ":" + roomPath;
        
        Jedis jedis = getJedis();
        try {
            if(jedis.exists(urs)){
                jedis.del(urs);
            }
            jedis.set(urs, roomplayers);
        }catch (Exception e) {
            logger.error("Error: " + e.getMessage(), e);
        }finally {
            close(jedis);
        }
    }

    public String getRoomPlayerList(Integer roomId, Integer roomPath) {
        String urs = roomPlayerList + roomId + ":" + roomPath;
        Jedis jedis = getJedis();
        String list = null;
        try {
            list = jedis.get(urs);
        } finally {
            close(jedis);
        }
        return list;
    }

    public Map<Integer, Set<String>> getForceKickList(int roomPath) {
        String usr = roomForceKick + "*:" + roomPath;
        Jedis jedis = getJedis();
        try {
            Set<String> keys = jedis.keys(usr);
            if (keys.isEmpty()) {
                return null;
            }

            Map<Integer, Set<String>> result = new HashMap<>();
            for (String key : keys) {
                String[] split = key.split(":");
                Integer roomId = Integer.parseInt(split[2]);
                Set<String> userIds = jedis.smembers(key);
                result.put(roomId, userIds);
            }

            return result;
        } finally {
            close(jedis);
        }
    }

    public void delKickList(Integer roomId, Integer roomPath) {
        String urs = roomForceKick + roomId + ":" + roomPath;
        Jedis jedis = getJedis();
        try {
            jedis.del(urs);
        }catch (Exception e) {
            logger.error("Error: " + e.getMessage(), e);
        }finally {
            close(jedis);
        }
    }

    public Map<Integer, Set<String>> getForceStandUpList(int roomPath) {
        String usr = roomForceStandUp + "*:" + roomPath;
        Jedis jedis = getJedis();
        try {
            Set<String> keys = jedis.keys(usr);
            if (keys.isEmpty()) {
                return null;
            }

            Map<Integer, Set<String>> result = new HashMap<>();
            for (String key : keys) {
                String[] split = key.split(":");
                Integer roomId = Integer.parseInt(split[2]);
                Set<String> userIds = jedis.smembers(key);
                result.put(roomId, userIds);
            }

            return result;
        } finally {
            close(jedis);
        }
    }

    public void delStandUpList(Integer roomId, Integer roomPath) {
        String urs = roomForceStandUp + roomId + ":" + roomPath;
        Jedis jedis = getJedis();
        try {
            logger.debug("chungTest: delete");
            jedis.del(urs);
        }catch (Exception e) {
            logger.error("chungTest Error: " + e.getMessage(), e);
        }finally {
            close(jedis);
        }
    }

}