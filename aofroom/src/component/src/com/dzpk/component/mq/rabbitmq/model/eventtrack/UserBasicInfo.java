package com.dzpk.component.mq.rabbitmq.model.eventtrack;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 玩家基本信息
 */
@Data
@ToString
public class UserBasicInfo extends EventTrackBasicInfo implements Serializable {

    private String ip;         // ip
    private double longitude;  // GPS经度信息
    private double latitude;   // GPS纬度信息
    private int seatSize;      // 座位号
    private String imei;       // 机器码
    private int isVirtual;  // 是否为模拟器

}

