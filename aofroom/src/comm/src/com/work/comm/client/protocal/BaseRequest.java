package com.work.comm.client.protocal;

import io.netty.channel.Channel;

public class BaseRequest {
    protected Channel channel;
    protected byte[] bt;
    protected int requestCode;
    
    public Channel getChannel() {
        return channel;
    }
    public void setChannel(Channel channel) {
        this.channel = channel;
    }
    public byte[] getBt() {
        return bt;
    }
    public void setBt(byte[] bt) {
        this.bt = bt;
    }
    public int getRequestCode() {
        return requestCode;
    }
    public void setRequestCode(int requestCode) {
        this.requestCode = requestCode;
    }
    
}
