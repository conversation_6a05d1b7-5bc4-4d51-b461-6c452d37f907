package com.work.comm.endpoints.core;

import com.work.comm.s2s.callback.IChannelAuthCallback;
import com.work.comm.s2s.client.AppConfig;
import com.work.comm.s2s.client.ServerManager;
import com.work.comm.s2s.common.DateUtil;
import com.work.comm.s2s.common.ES2ServerType;
import com.work.comm.s2s.protocal.Protocal;
import com.work.comm.s2s.protocal.S2PacckageUtil;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.util.concurrent.GenericFutureListener;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class CoreServerManager implements IChannelAuthCallback {
    /** 单例 */
    private static CoreServerManager INSTANCE = new CoreServerManager();
    public static CoreServerManager getInstance(){
        return INSTANCE;
    }
    public static void initialize() {
        AppConfig appConfig = ServerManager.getInstance().getAppConfig();
        INSTANCE.setAppType(appConfig.getAppType());
        INSTANCE.setAppId(appConfig.getAppId());
        ServerManager.getInstance().subscribe(INSTANCE.authCallback());
    }
    private CoreServerManager(){
        this.seqNoStartTime = DateUtil.getDateTillHour(null);
        this.seqNo = 0;
    }
    public IChannelAuthCallback authCallback(){
        return this;
    }
    /**
     * 发送数据包
     * 由上层业务调用
     *
     * @param serverId  对端App的标识符，类型内唯一，可选
     *                  当不指定时，则是向此类型的消息发送
     * @param sendAll   是否向所有服务器发送
     *                  true  : 是
     *                  false : 否
     * @param data       待发送的消息,必填
     *
     * @throws Exception
     *    IllegalArgumentException 参数无效
     */
    public void sendData(String serverId,boolean sendAll, byte[] data) throws Exception{
        if(null == serverId)
            serverId="";
        else
            serverId = serverId.trim();

        ServerManager.getInstance().sendDataBy(ES2ServerType.core.name(),serverId,sendAll,data);
    }

    /** 所在应用标识 */
    private String appType;
    private String appId;

    /**
     * 节点数据变化回调
     * 每个类型只能添加一个
     */
    private Map<Class<?>,ResNodeChgCallback> nodeChgCallbackMap = new ConcurrentHashMap<>();
    public void subscribe(ResNodeChgCallback callback){
        if(null == callback)
            return;

        Class<?> implClazz = callback.getClass();
        nodeChgCallbackMap.put(implClazz,callback);
    }

    /**
     * res服务节点集合
     * appId -> ServerNode
     */
    private Map<String,ResServerNode> serverNodeMap = new ConcurrentHashMap<>();
    public boolean updateServerNode(int seqNoTime,int seqNo,List<ResServerNode> serverNodeLst){
        boolean result = false;
        if(null == serverNodeLst || serverNodeLst.isEmpty())
            return result;

        List<ResServerNode> nodeLst = null;
        synchronized (this.serverNodeMap) {
            for (ResServerNode node : serverNodeLst) {
                if (null == node.getServerId() || "".equals(node.getServerId().trim()))
                    continue;

                if(!node.checkAndAdjust())
                    continue;

                String serverId = node.getServerId().trim().toLowerCase();
                ResServerNode existing = this.serverNodeMap.get(serverId);
                if (null == existing) {
                    result = true;
                    existing = node;
                    existing.setServerId(serverId);
                    this.serverNodeMap.put(serverId, existing);
                } else {
                    boolean temp = existing.update(seqNoTime, seqNo, node);
                    if(temp){
                        result = temp;
                    }
                }
            }

            // 组装数据
            if(result){
                nodeLst = new ArrayList<>();
                Iterator<ResServerNode> it = this.serverNodeMap.values().iterator();
                while(it.hasNext()){
                    ResServerNode nd  = it.next();
                    if(!nd.checkAndAdjust())
                        continue;

                    nodeLst.add(nd);
                }
            }
        }

        // 更新res服务节点
        if(result){
            Iterator<ResNodeChgCallback> it = this.nodeChgCallbackMap.values().iterator();
            while(it.hasNext()){
                ResNodeChgCallback callback = it.next();
                callback.updateResNode(nodeLst);
            }
        }

        return result;
    }

    // 数据包的流水号
    // 开始计时的时间，每小时重置
    private Date seqNoStartTime;
    private int seqNo;
    private int[] getSeqNo(){
        int[] result = new int[2];
        synchronized (this.seqNoStartTime){
            Date sysTime = DateUtil.getDateTillHour(null);
            if(sysTime.after(this.seqNoStartTime)){
                this.seqNoStartTime = sysTime;
                this.seqNo = 1;
            }else{
                this.seqNo += 1;
            }
            result[0] = (int)(this.seqNoStartTime.getTime()/1000);
            result[1] = this.seqNo;
        }

        return result;
    }

    /** 当前牌局数 */
    private Object roomNumLock = new Object();
    private int roomNum = 0;
    private void addRoomNum(boolean isAdd){
        synchronized (this.roomNumLock){
            int currVal = this.roomNum<0?0:this.roomNum;
           if(isAdd)
               currVal +=1;
           else if(currVal>0)
               currVal -=1;

           this.roomNum = currVal;
        }
    }

    /**
     * core服务的连接数
     * 大于0，认为不再需要发送初始化数据
     *
     * appId -> 计数器
      */
    private Map<String,Integer> serverCounterMap = new ConcurrentHashMap<>();
    private boolean addCounter(String appId,boolean isAdd){
        appId = appId.trim();
        boolean needReport;
        synchronized (this.serverCounterMap) {
            Integer counter = this.serverCounterMap.get(appId);
            counter = (null == counter || counter < 0 ? 0 : counter);
            needReport = counter == 0;
            boolean needDel = false;
            if (isAdd)
                counter += 1;
            else if (counter > 0) {
                counter -= 1;
                needDel = counter <= 0;
            }
            if (needDel) {
                this.serverCounterMap.remove(appId);
                seqNoStartTime = DateUtil.getDateTillHour(null);
                seqNo = 0;
            }
            else
                this.serverCounterMap.put(appId, counter);
        }

        return needReport;
    }
    /**
     * 确认是core服务后，
     * 上报本服务的牌局数量
     * @param appId
     */
    private void report2Core(String appId){
        int[] seqNoGroup = this.getSeqNo();
        int reqCode = 1001;

        Object[][] dataObjs = {
                {130, this.appType, Protocal.TYPE_STRING_UTF16},
                {131, this.appId, Protocal.TYPE_STRING_UTF16},
                {132, this.roomNum, Protocal.TYPE_INT_4},
                {133, seqNoGroup[0], Protocal.TYPE_INT_4},
                {134, seqNoGroup[1], Protocal.TYPE_INT_4}
        };
        if(log.isDebugEnabled())
            log.debug("上报本服务的加载牌局数-{}: appType={},appId={},roomNum={},seqNoTime={},seqNo={}",
                    reqCode,this.appType,this.appId,
                    this.roomNum,seqNoGroup[0],seqNoGroup[1]);
        try {
            byte[] bytes = S2PacckageUtil.packAll(dataObjs, reqCode);
            if(log.isDebugEnabled())
                log.debug("上报本服务的加载牌局数-{}：{}",reqCode, Arrays.toString(bytes));
            sendData(appId,false, bytes);
        }catch (Exception ex){
            log.error(String.format("上报本服务的加载牌局数-%s失败：%s",reqCode,ex.getMessage()),ex);
        }
    }

    /**
     * 连接通道身份成功识别
     *
     * @param channel    被识别的连接通道，必填
     * @param appType    对端应用类型，必填
     * @param appId      对端应用ID，必填
     */
    @Override
    public void auth(Channel channel,String appType,String appId){
        log.debug("监听到连接已经完成身份标识：ch={},appType={},appId={}",channel,appType,appId);
        if(null == channel)
            return;
        if(null == appId || "".equals(appId.trim()))
            return;
        if(null == appType || !ES2ServerType.core.name().equalsIgnoreCase(appType.trim()))
            return;

        appId = appId.trim();
        boolean needReport = this.addCounter(appId,true);
        channel.closeFuture().addListener(new CoreChannelCloseMonitor(appId));

        if(needReport){
            this.report2Core(appId);
        }
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }
    public void setAppId(String appId) {
        this.appId = appId;
    }

    private class CoreChannelCloseMonitor implements GenericFutureListener<ChannelFuture>{
        private String appId;

        public CoreChannelCloseMonitor(String appId){
            this.appId = appId;
        }
        public void operationComplete(ChannelFuture f) throws Exception{
            if(f.isDone() && f.isSuccess()){
                // 减少服务器的计数器
                CoreServerManager.this.addCounter(this.appId,false);
            }
            f.removeListener(this);
        }
    }
}
