package com.dzpk.common.config;

import com.dzpk.common.utils.LogUtil;
import org.apache.logging.log4j.Logger;

import java.util.Properties;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;

/**
 * 加载对应的配置文件路径信息
 */
public class PropertiesPathUtil {

    private static Logger logger = LogUtil.getLogger(PropertiesPathUtil.class);

    private static Properties prop = new Properties();

    public static String getProperty(String key){
        if (prop.isEmpty())
            loadWGProperties();
        return prop.getProperty(key);
    }


    public static void loadWGProperties(){
        FileInputStream wgStream=null;
        try{
            wgStream = new FileInputStream("wg.properties");
            prop.load(wgStream);
        }catch(FileNotFoundException ex){
            logger.error("file not found", ex);
        }catch(Exception ex){
            logger.debug("error", ex);
        }finally{
            try{
                if(wgStream!=null){
                    wgStream.close();
                }
            } catch(IOException ex){
                logger.error(" Fail to close wgStream ");
            }
        }
    }

}
