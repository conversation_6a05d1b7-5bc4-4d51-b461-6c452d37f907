
package com.i366.processor.client;

import java.util.Map;

import com.i366.constant.Constant;
import com.dzpk.common.utils.LogUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import org.apache.logging.log4j.Logger;

import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;
import com.i366.room.RoomService;
import com.work.comm.io.Handler;
import com.work.comm.client.protocal.BaseRequest;
import com.work.comm.client.protocal.Request;
import com.i366.util.IPUtil;
import com.dzpk.db.model.UserInfo;

/**
 * 玩家牌局内行为包括坐下,站起,离开,提前离桌,强制站起,强制踢出
 */
public class Processor_45_Seta extends Handler {

    private Logger logger = LogUtil.getLogger(Processor_45_Seta.class);

    @Override
    public byte[] handleRequest(BaseRequest req) {
        Request request = (Request) req;
        int[][] int2 = {
                {60, I366ClientPickUtil.TYPE_INT_1},
                {61, I366ClientPickUtil.TYPE_INT_1},
                {62, I366ClientPickUtil.TYPE_STRING_UTF16},     // 经度
                {63, I366ClientPickUtil.TYPE_STRING_UTF16},    // 纬度
                {64, I366ClientPickUtil.TYPE_STRING_UTF16},     //客户端ip
                {131, I366ClientPickUtil.TYPE_INT_4},
                {130, I366ClientPickUtil.TYPE_INT_4}};
        Map<Integer, Object> map = I366ClientPickUtil.pickAll(request.getBt(), int2);
        int action = (Integer) map.get(60);
        int seatId = (Integer) map.get(61);
        int roomId = (Integer) map.get(131);
        int roomPath = (Integer) map.get(130);
        String clientIp = (String)map.get(64);//客户端上传上来的ip地址
        if (seatId == 255) {
            seatId = -1;
        }
        String ip = IPUtil.getIPByChannel(request.getChannel());
        logger.debug(" Processor_45_Seta action:" + action + " seatId:" + seatId + " roomId:" + roomId + " roomPath:"
                + roomPath + " ip:" + ip + "===>" + clientIp + " userId: " + request.getUserId());

        // 提交任务
        Task task = new Task(Constant.REQ_GAME_SEND_SEAT_ACTION, map, request, roomId, roomPath);
        WorkThreadService.submit(roomId, task);
        return null;
    }

}

