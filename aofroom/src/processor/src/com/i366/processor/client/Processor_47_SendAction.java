
package com.i366.processor.client;

import java.util.Map;

import com.i366.constant.Constant;
import com.dzpk.common.utils.LogUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import org.apache.logging.log4j.Logger;

import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;
import com.i366.room.RoomService;
import com.work.comm.io.Handler;
import com.work.comm.client.protocal.BaseRequest;
import com.work.comm.client.protocal.Request;

/**
 * 玩家下注操作，包括加注、跟注、让牌、弃牌、全下、超时弃牌
 */
public class Processor_47_SendAction extends Handler {

    private final Logger logger = LogUtil.getLogger(Processor_47_SendAction.class);

    @Override
    public byte[] handleRequest(BaseRequest req) {
        Request request = (Request) req;
        //  =======================相关验证=================
        int[][] int2 = {
                {60, I366ClientPickUtil.TYPE_INT_1},        // 动作
                {132, I366ClientPickUtil.TYPE_INT_4},       // 房间id
                {131, I366ClientPickUtil.TYPE_INT_4},       // 房间路径
                {133, I366ClientPickUtil.TYPE_INT_4},        // 下注筹码
                {134, I366ClientPickUtil.TYPE_INT_4},        // 房间累计的第几次操作
        };
        Map<Integer, Object> map = I366ClientPickUtil.pickAll(request.getBt(), int2);
        int action = (Integer) map.get(60);
        int roomId = (Integer) map.get(132);
        int roomPath = (Integer) map.get(131);
        int anteNumber = (Integer) map.get(133);
        logger.debug("Processor_47_SendAction userId is : " + request.getUserId() + "action: " + action + " ,roomId: " + roomId + " ,roomPath: " + roomPath
                + " ,anteNumber: " + anteNumber);

        RoomService.setUserChannel(request, roomId);

        Task task = new Task(Constant.REQ_GAME_SEND_ACTION, map, request, roomId, roomPath);
        WorkThreadService.submit(roomId, task);
        return null;
    }

}

