package com.i366.processor.client;

import java.util.Map;

import com.dzpk.common.utils.LogUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import org.apache.logging.log4j.Logger;

import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;
import com.i366.constant.Constant;
import com.work.comm.io.Handler;
import com.work.comm.client.protocal.BaseRequest;
import com.work.comm.client.protocal.Request;

/**
 * 花钻石看底牌
 */
public class Processor_410_ShowCardsByUser extends Handler {

    private Logger logger = LogUtil.getLogger(Processor_410_ShowCardsByUser.class);

    @Override
    public byte[] handleRequest(BaseRequest req) {
        Request request = (Request) req;
        logger.debug("ShowCardsByUserProcessor userId is : " + request.getUserId());
        
        int[][] int2 = {
                {131, I366ClientPickUtil.TYPE_INT_4},   // roompath
                {132, I366ClientPickUtil.TYPE_INT_4},   // roomid
                {60, I366ClientPickUtil.TYPE_INT_1},    // 已发的底牌个数 
        };
        Map<Integer, Object> map = I366ClientPickUtil.pickAll(request.getBt(), int2);
        int roomPath = (Integer) map.get(131);
        int roomId = (Integer) map.get(132);
        int cardCnt = map.get(60) == null ? 5 : (Integer) map.get(60);

        logger.info("received roompath: " + roomPath + ", roomId:" + roomId + ", cardCount:" + cardCnt);

        // 提交任务
        Task task = new Task(Constant.REQ_SHOW_CARDS_BY_USER, map, request, roomId, roomPath);
        WorkThreadService.submit(roomId, task);
        return null;
    }
}
