
package com.i366.processor.client;

import java.util.Map;

import com.i366.constant.Constant;
import com.dzpk.common.utils.LogUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import org.apache.logging.log4j.Logger;

import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;
import com.i366.room.RoomService;
import com.work.comm.io.Handler;
import com.work.comm.client.protocal.BaseRequest;
import com.work.comm.client.protocal.Request;
import com.dzpk.db.model.UserInfo;

/**
 * @deprecated
 *
 * 茉莉牌局中开启控制带入开关
 * 疯狂扑克中废弃,暂时保留
 */
public class Processor_69_CarryControlSwitch extends Handler {

    private final Logger logger = LogUtil.getLogger(Processor_69_CarryControlSwitch.class);

    @Override
    public byte[] handleRequest(BaseRequest req) {
        Request request = (Request) req;
        logger.debug("Processor_69_CarryControlSwitch id " + request.getUserId());
        int[][] int2 = {
                {60, I366ClientPickUtil.TYPE_INT_1},            // 0:关闭 1:开启
                {130, I366ClientPickUtil.TYPE_INT_4},           // roomPath
                {131, I366ClientPickUtil.TYPE_INT_4}            // roomId
        };
        Map<Integer, Object> map = I366ClientPickUtil.pickAll(request.getBt(), int2);
        boolean control = (Integer) map.get(60) == 1;
        int roomPath = (Integer) map.get(130);
        int roomId = (Integer) map.get(131);

        RoomService.setUserChannel(request, roomId);
        logger.debug("roomId:" + roomId + " roomPath :" + roomPath + " control: " + control);
        
        Task task = new Task(Constant.REQ_GAME_CARRY_CONTROL_SWITCH, map, request, roomId, roomPath);
        WorkThreadService.submit(roomId, task);
        return null;
    }

}

