package com.i366.processor.client;

import java.util.Map;

import com.i366.constant.Constant;
import com.dzpk.common.utils.LogUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import org.apache.logging.log4j.Logger;

import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;
import com.i366.room.RoomService;
import com.work.comm.io.Handler;
import com.work.comm.client.protocal.BaseRequest;
import com.work.comm.client.protocal.Request;
import com.dzpk.db.model.UserInfo;

/**
 * 玩家收藏牌谱
 */
public class Processor_217_ReplayCollect extends Handler {
    private final Logger logger = LogUtil.getLogger(Processor_217_ReplayCollect.class);

    @Override
    public byte[] handleRequest(BaseRequest req) {
        Request request = (Request) req;
        logger.debug("Processor_217_ReplayCollect id " + request.getUserId());
        int[][] int2 = {
                {61, I366ClientPickUtil.TYPE_INT_1},        // 0 收藏 1 取消收藏
                {130, I366ClientPickUtil.TYPE_INT_4},       // roomId
                {131, I366ClientPickUtil.TYPE_INT_4},       // roomPath
                {132, I366ClientPickUtil.TYPE_INT_4},       // page
        };
        Map<Integer, Object> map = I366ClientPickUtil.pickAll(request.getBt(), int2);
        int roomId = (Integer) map.get(130);
        int roomPath = (Integer) map.get(131);

        RoomService.setUserChannel(request, roomId);

        Task task = new Task(Constant.REQ_GAME_PREV_GAME_COLLECT, map, request, roomId, roomPath);
        WorkThreadService.submit(roomId, task);
        return null;
    }

}
