
package com.i366.processor.client;

import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;
import com.i366.constant.Constant;
import com.i366.room.RoomService;
import com.dzpk.common.utils.LogUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.io.Handler;
import com.work.comm.client.protocal.BaseRequest;
import com.work.comm.client.protocal.Request;
import com.dzpk.db.model.UserInfo;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * 玩家操作延时
 */
public class Processor_186_UserDelay extends Handler {
    private final Logger logger = LogUtil.getLogger(Processor_186_UserDelay.class);

    @Override
    public byte[] handleRequest(BaseRequest req) {
        Request request = (Request) req;
        int userId = request.getUserId();
        logger.debug("Processor_186_UserDelay id " + userId);
        int[][] int2 = {
                {130, I366ClientPickUtil.TYPE_INT_4},        // roomId
                {131, I366ClientPickUtil.TYPE_INT_4},        // roomPath
                {132, I366ClientPickUtil.TYPE_INT_4},        // 延时时间(S)
                {133, I366ClientPickUtil.TYPE_INT_4},        // 消耗砖石,免费传0
                {134, I366ClientPickUtil.TYPE_INT_4},        // 玩家座位号
                {135, I366ClientPickUtil.TYPE_INT_4},        // 消耗筹码,免费传0
        };
        Map<Integer, Object> map = I366ClientPickUtil.pickAll(request.getBt(), int2);
        int roomId = (Integer) map.get(130);
        int roomPath = (Integer) map.get(131);
        RoomService.setUserChannel(request, roomId);
        Task task = new Task(Constant.REQ_GAME_USER_DELAY, map, request, roomId, roomPath);
        WorkThreadService.submit(roomId, task);
        return null;
    }

}
