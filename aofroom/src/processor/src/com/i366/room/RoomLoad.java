/*
 * $RCSfile: RoomLoad.java,v $
 * $Revision: 1.1  $
 * $Date: 2012-8-10  $
 *
 * Copyright (C) 2005 Bettem, Inc. All rights reserved.
 *
 * This software is the proprietary information of Bettem, Inc.
 * Use is subject to license terms.
 */
package com.i366.room;

import com.dzpk.common.config.PropertiesPathUtil;
import com.dzpk.common.utils.LogUtil;
import com.i366.cache.Cache;
import com.i366.model.room.LeveRoom;
import com.i366.model.room.Room;
import com.i366.model.room.SecondRoom;
import com.i366.model.room.StairRoom;
import org.apache.logging.log4j.Logger;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.JDOMException;
import org.jdom.input.SAXBuilder;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 定时读取房间配置文件
 * <p>Title: RoomLoad</p> 
 * <p>Description: </p> 
 * <p>Copyright: Copyright (c) 2006</p> 
 * <AUTHOR>
 * @version 1.0
 */
public class RoomLoad implements Runnable{
	private int r_ = 0;
	public static Logger  logger = LogUtil.getLogger(RoomLoad.class);
	private final static long SPACE_LOAD_TIME = Long.valueOf(Cache.p.getProperty("load.room.space.time"));
	private final static String PATH =  "room.xml";
	private static int isLoad = -999;
	public void run() {
		while (r_ == 0) {
			try {
				loadRoomXML();
				//暂停
				Thread.sleep(SPACE_LOAD_TIME);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		}
	}
	
	public void loadRoomXML() {
		logger.debug("begin load room xml........");
        try{
            SAXBuilder saxBuilder = new SAXBuilder(PropertiesPathUtil.getProperty("wg.SAXParser"));
            Document document = saxBuilder.build(new File(PATH));
            Element root = document.getRootElement();
            //对应item重复的配置不给于加载
            Map<Integer, Integer> roomItme = new HashMap<Integer, Integer>();
            int i = 1; //房间一级类型 1~5 6
            int j = 1; //1~2 快慢场
            int k = 1; //
            Map<Integer, StairRoom> srMap = Cache.getStairRoomMap();
            logger.info("init stair room map size: " + srMap.size());
            logger.info("isLoad: " + String.valueOf(isLoad));
        	// type1 ~ type6
        	if (isLoad < 0) {
        		isLoad = 999;
        		String ip = Cache.p.getProperty("socket.ip");
        		int port = Integer.valueOf(Cache.p.getProperty("socket.port"));
        		logger.info("room ip: " + ip);
        		logger.info("room port: " + String.valueOf(port));
                for (Element element : (List<Element>)root.getChildren()) {
                	StairRoom sr = new StairRoom();
                	sr.setIp(ip);
                	sr.setPort(port);
                	srMap.put(i, sr);
                	j = 1;
                	Map<Integer, SecondRoom> secondRoomMap = sr.getSecondRoom();
                	for (Element element2 : (List<Element>)element.getChildren()) {
                		SecondRoom secondRoom = new SecondRoom();
                		int wait = Integer.valueOf(element2.getAttributeValue("wait"));
                		secondRoom.setWint(wait);
                		secondRoomMap.put(j, secondRoom);
                		k = 1;
                		Map<Integer, LeveRoom> leveMap = secondRoom.getLeveRoomMap();
                		for (Element element3 : (List<Element>)element2.getChildren()) {
                			//id="1001000" manzhu="1" damanzhu="2" gold="20"   count="30"
                			int id = Integer.valueOf(element3.getAttributeValue("id"));
                			int manzhu = Integer.valueOf(element3.getAttributeValue("manzhu"));
                			int damanzhu = Integer.valueOf(element3.getAttributeValue("damanzhu"));
                			int gold = Integer.valueOf(element3.getAttributeValue("gold"));
                			int count = Integer.valueOf(element3.getAttributeValue("count"));
                			
                			if (count < 1) {
                				continue;
                			}
                			for (int m = 0; m < count; m++) {
                				LeveRoom leveRoom = new LeveRoom();
                    			leveRoom.setId(id + m);
                    			leveRoom.setManzhu(manzhu);
                    			leveRoom.setDamanzhu(damanzhu);
                    			leveRoom.setGold(gold);
                    			leveRoom.setCount(count);
                    			leveRoom.setRoomPath(i * 10 + j);
                    			leveRoom.setWait(wait);

                    			leveMap.put(leveRoom.getId(), leveRoom);
                    			Map<Integer, Room> RoomMap = leveRoom.getRoomMap();
                    			//放空对象房间
                				RoomMap.put(leveRoom.getId(), null);
                			}              			
                			k++;
                		}
                		j++;
                	}
                	i++;
                }
        	} 

        	logger.info("room numbers: " + String.valueOf(srMap.size()));
        	logger.debug("load room.xml ok........");
        	
//        	RoomDateLoad roomDateLoad = new RoomDateLoad();
//       	 	roomDateLoad.action();
        } catch(JDOMException ex){
            logger.error("parse class mapping error", ex);
        } catch(IOException ex){
            logger.error("xml config read error", ex);
        }
	}
	
	
	public void close() {
		r_ = -999;
	}
	
	public static void main(String[] args) {
		(new RoomLoad()).loadRoomXML();
	}
}

