
package com.i366.model.pocer;


import com.i366.model.card.CardNumber;
import com.i366.model.card.CardSuit;
import com.dzpk.common.utils.LogUtil;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;

/**
 * 一张扑克牌
 * <p>Title: Pocer</p> 
 * <p>Description: </p> 
 * <p>Copyright: Copyright (c) 2006</p> 
 * <AUTHOR>
 * @version 1.0
 */
public class Pocer {
	public static Logger logger = LogUtil.getLogger(Pocer.class);
	private int type; //方块1 草花2 红桃3 黑桃4
	private int size1; //对应数字 0~12 方块        13~25草花      26~38红桃  39~51黑桃
	private int size2; // 2~14 表示 2-A~ 14也可以表示1
	private int resType;//0 系统牌 1个人牌 
	private static String[] names = {"方块","草花","红桃","黑桃"};
	private CardSuit suit;
	private CardNumber number;
	
    private static Map<Integer, CardNumber> cardNumbers = new HashMap<Integer, CardNumber>(14);
    
    static {
    	cardNumbers.put(2, CardNumber.TWO);
    	cardNumbers.put(3, CardNumber.THREE);
    	cardNumbers.put(4, CardNumber.FOUR);
    	cardNumbers.put(5, CardNumber.FIVE);
    	cardNumbers.put(6, CardNumber.SIX);
    	cardNumbers.put(7, CardNumber.SEVEN);
    	cardNumbers.put(8, CardNumber.EIGHT);
    	cardNumbers.put(9, CardNumber.NINE);
    	cardNumbers.put(10, CardNumber.TEN);
    	cardNumbers.put(11, CardNumber.JACK);
    	cardNumbers.put(12, CardNumber.QUEEN);
    	cardNumbers.put(13, CardNumber.KING);
    	cardNumbers.put(14, CardNumber.ACE);
    }	
//  SPADE("\u2660"),
//  HEART("\u2665"),
//  CLUB("\u2663"),
//  DIAMOND("\u2666");
	////方块1 草花2 红桃3 黑桃4
	public String getName() {
		String str = null;
		switch (getType()) {
		case 1:
			str = names[0];
			break;
		case 2:
			str = names[1];
			break;
		case 3:
			str = names[2];
			break;
		case 4:
			str = names[3];
			break;
		}
		return str;
	}
	public Pocer(int s) {
		if (s < 0 || s > 51) {
			logger.error("new Pocer err......",new Exception());
		}
		size1 = s;
		if (s < 13) {
			suit = CardSuit.DIAMOND;
			type = 1;
			size2 = s + 2;
		}else if (12 < s && s < 26) {
			suit = CardSuit.CLUB;
			type = 2;
			size2 = s - 13 + 2;
		}else if (25 < s && s < 39) {
			suit = CardSuit.HEART;
			type = 3;
			size2 = s - 26 + 2;
		}else if (38 < s && s < 52) {
			suit = CardSuit.SPADE;
			type = 4;
			size2 = s - 39 + 2;
		}
		number = cardNumbers.get(size2);
	}
	
	public CardNumber getNumber() {
		return number;
	}
	public CardSuit getSuit() {
		return suit;
	}
	public int getType() {
		return type;
	}
	/**
	 * 0~51 数字大小
	 * @return
	 */
	public int getSize1() {
		return size1;
	}
	/**
	 * 2~14 表示 2-A~ 14也可以表示1
	 * @return
	 */
	public int getSize2() {
		return size2;
	}
	public int getResType() {
		return resType;
	}
	public void setResType(int resType) {
		this.resType = resType;
	}
	public static void main(String[] args) {
		System.out.println(new Pocer(3).getName());
	}
}

