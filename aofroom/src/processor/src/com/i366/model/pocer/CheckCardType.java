package com.i366.model.pocer;

import java.util.HashSet;
import java.util.Set;

public class CheckCardType {

    private static final int T_1 = 1;
    private static final int T_2 = 2;
    private static final int T_3 = 3;
    private static final int T_4 = 4;
    private static final int T_5 = 5;
    private static final int T_6 = 6;
    private static final int T_7 = 7;
    private static final int T_8 = 8;
    private static final int T_9 = 9;
    private static final int T_10 = 10;
    // 1:皇家同花顺 2:同花顺 3:4条 4:葫芦 5:同花 6:顺子 7:三张 8:两对 9:一对 10:高牌

    
    /**
     * 最大牌 牌类型
     * @param pokers 需要比的5-7张牌
     * @return
     */
    public static int checkCardsType(int[] pokers) {
        // 扑克牌个数
        int size = pokers.length;
        // 扑克牌二维数组（按4种花色来分类）
        int[][] cards2 = new int[][]{
                new int[]{-1,-1,-1,-1,-1,-1,-1},
                new int[]{-1,-1,-1,-1,-1,-1,-1}, 
                new int[]{-1,-1,-1,-1,-1,-1,-1}, 
                new int[]{-1,-1,-1,-1,-1,-1,-1}
                };      
        int[] cards = new int[size]; // 扑克牌(5-7张)
        int i = 0;                   // 用于循环
        boolean bool;                // 用于验证是否存在
        Set<Integer> set = new HashSet<Integer>(); // 用于存放不同的牌
        /*******************************************转换扑克牌为（1-13）**************************************/
        int a1 = 0, a2 = 0, a3 = 0, a4 = 0;
        for (i = 0; i < size; i++) {
            int crad = convertCrads( pokers[i]);
            if (pokers[i]<13) {         //方块
                cards2[0][a1] = crad;
                a1 ++;
            }else if (pokers[i]<26) {   //梅花
                cards2[1][a2] = crad;
                a2 ++;
            }else if (pokers[i]<39) {   //红桃
                cards2[2][a3] = crad;
                a3 ++;
            }else if (pokers[i]<52) {   //黑桃
                cards2[3][a4] = crad;
                a4 ++;
            }
            cards[i] = crad;
        }
        System.out.println(a1);
        InsertSortArray(cards);
        for (i = 0; i < cards.length; i++) {
            System.out.print(cards[i] + " ");
        }
        /*******************************************同花/同花顺*****************************************/
        i = 0;
        for (int[] is : cards2) {
            for (int j = 0; j < is.length; j++) {
                System.out.print(is[j] + " ");
            }
            System.out.println();
            //同花
            if(is[4] > -1) {
                int s = 0;
                for (int j = 0; j < is.length; j++) {
                    if (is[j] != -1) {
                        s++;
                    }
                }
                int[] iss = new int[s];
                s = 0;
                for (int j = 0; j < is.length; j++) {
                    if (is[j] != -1) {
                        iss[s++] = is[j];
                    }
                }
                is = iss;
                InsertSortArray(is);
                for (int j = 0; j < is.length; j++) {
                    System.out.print(is[j] + " ");
                }
                System.out.println();
                // A、2、3、4、5类型
                if(is[is.length - 1] == 13 && is[0] == 1 && is[3] - is[0] == 3) { 
                    return T_2;
                }
                while(i < size - 4) {                   
                    bool = is[i + 4] - is[i] == 4;
                    //同花顺
                    if (bool) {
                        if(is[i + 4]==13){
                            //皇家同花顺
                            return T_1;
                        }else{
                            return T_2;
                        }
                    }
                    i ++;
                }
                return T_5;
            }
        }
        /***************************************四条/三条/多对/一对***************************************/
        int sitiao = 0;  // 4张相同大小牌数量
        int santiao = 0; // 3张相同大小牌数量
        int jidui = 0;   // 2张相同大小牌数量
        int[] duizi = new int[6];   // 对子(最多3对)
        
        //**************四张*************//
        i = 0;
        while(i < size - 3) {
            sitiao += cards[i] == cards[i + 3] ? 1 : 0;
            i ++;
        }
        //**************三张*************//
        int sindex = -1;  // 记录三张相同大小牌第1张牌索引
        if(sitiao == 0) {
            i = 0;
            while(i < size - 2) {
                if(cards[i] == cards[i + 2]) {
                    santiao ++;
                    sindex = i;
                }
                i ++;
            }
        }
        //**************二张*************//
        if(sitiao == 0 && santiao < 2) {
            i = 0;
            while(i < size - 1) {
                if(i == sindex) { // 如果有三张相同牌，就直接跳过这三张牌
                    i = sindex + 3;
                    continue;
                }
                if( cards[i] == cards[i + 1]) {
                    duizi[jidui] = cards[i];
                    duizi[jidui + 1] = cards[i + 1];
                    jidui ++;
                    i ++;
                }
                i ++;
            }
        }
        /********************************************四条******************************************/
        if(sitiao > 0) {
            return T_3;
        }
        /********************************************葫芦******************************************/
        if( (santiao == 1 && jidui > 0) || santiao > 1 ) {
            return T_4;
        }
        /********************************************顺子******************************************/
        //顺子(至少五张不同牌)
        int length = size - (santiao*2 + jidui);
        if(length >= 5) {
            int[] shunzi = new int[length];
            i = 0;
            for (Integer c : cards) {
                if(set.add(c)) {
                    shunzi[i] = c;
                    i ++;
                }
            }
            set.clear();
            // A、2、3、4、5类型
            if(shunzi[length - 1] == 13 && shunzi[0] == 1 && shunzi[3] - shunzi[0] == 3) { 
                return T_6;
            }
            // 其他顺子类型
            i = 0;
            while(i < length - 4) {
                if(shunzi[i + 4] - shunzi[i] == 4) { 
                    return T_6;
                }
                i ++;
            }
        }
        /********************************************三条********************************************/
        if(santiao == 1 && duizi[0] == 0) {
            return T_7;
        }
        /********************************************两对以上*****************************************/
        if(jidui >= 2) {
            return T_8;
        }
        /**********************************可能同花/可能顺子（5-6张牌）********************************//*
        if(size < 7) {
            *//**********可能同花(有四张同颜色牌)**********//*
            i = 0;
            for (int[] is : cards2) {
                //同花
                if(is[3] > 0) {
                    return N_33;
                }
            }
            *//**********可能顺子(四张牌相连)**********//*
            //可能顺子(至少四张不同牌)
            if(length >= 4) {
                int[] shunzi = new int[length];
                i = 0;
                for (Integer c : cards) {
                    if(set.add(c)) {
                        shunzi[i] = c;
                        i ++;
                    }
                }
                // A、2、3、4类型
                if(shunzi[length - 1] == 13 && shunzi[0] == 1 && shunzi[2] - shunzi[0] == 2) { 
                    return N_33;
                }
                // 其他顺子类型
                i = 0;
                while(i < length - 3) {
                    if(shunzi[i + 3] - shunzi[i] == 3) { 
                        return N_33;
                    }
                    i ++;
                }
            }
        }*/
        /********************************************对子******************************************/
        if(jidui == 1) {
            /***********（QKA）***********/
            if(duizi[0] > 10) {
                return T_9;
            }
            /**********（非QKA）**********/
            if(duizi[0] < 11) {
                return T_9;
            }
        }
        /************************************************高牌*********************************************/
        //最大牌等于K/A
        if(cards[size -1] > 11) {
            return T_10;
        }
        //最大牌非K/A
        return T_10;
    }
    /**
     * 转换牌为（1-13）
     * @param cards
     * @return
     */
    private static int convertCrads(int cards) {

        if( cards > 38 ) {
            cards -= 38;
        }
        else if( cards > 25 ) {
            cards -= 25;    
        }
        else if( cards > 12 ) {
            cards -= 12;
        }else {
            cards ++;
        }
        System.out.println(cards);
        return cards;
    }
    /**
     * 插入法排序
     * @param attr
     */
    private static void InsertSortArray(int[] attr) {
        for(int i = 1; i < attr.length; i ++) {//循环从第二个数组元素开始，因为arr[0]作为最初已排序部分 
            int temp = attr[i];//temp标记为未排序第一个元素 
            int j = i - 1; 
            while (j >= 0 && attr[j] > temp) {/*将hemp与已排序元素从小到大比较，寻找temp应插入的位置*/  
                attr[j + 1] = attr[j]; 
                j --; 
            } 
            attr[j + 1] = temp; 
        }
    }
    
    public static void main(String[] args) {
        int[] pokers = {3, 4, 5, 6, 7};
        System.out.println(checkCardsType(pokers));
    }
}
