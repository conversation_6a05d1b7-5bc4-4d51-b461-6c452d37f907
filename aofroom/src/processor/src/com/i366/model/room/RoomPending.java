/*
 * $RCSfile: Room.java,v $
 * $Revision: 1.1  $
 * $Date: 2012-8-10  $
 *
 * Copyright (C) 2005 Bettem, Inc. All rights reserved.
 *
 * This software is the proprietary information of Bettem, Inc.
 * Use is subject to license terms.
 */
package com.i366.model.room;

import java.util.HashMap;
import java.util.Map;

import com.dzpk.common.utils.LogUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import org.apache.logging.log4j.Logger;

import com.i366.util.PublisherUtil;
import com.dzpk.db.model.UserInfo;

/**
 * 房间pending接口
 * <p>Title: Room</p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2006</p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public class RoomPending {
    public static Logger logger = LogUtil.getLogger(RoomPending.class);
    
    public static final String sp = "@%";
    
    private Map<Integer, UserInfo> audMap;	// 当前进入房间pending的人
    
    private int roomPath;
    private int roomId;
    
    public int getRoomPath() {
        return roomPath;
    }

    public void setRoomPath(int roomPath) {
        this.roomPath = roomPath;
    }

    public int getRoomId() {
        return roomId;
    }

    public void setRoomId(int roomId) {
        this.roomId = roomId;
    }

    public RoomPending () {
    	audMap = new HashMap<Integer, UserInfo>();
    }

    /**
     * pending页加入一个用户
     * @param userInfo
     */
    public void putUserInfo(UserInfo userInfo) {
    	audMap.put(userInfo.getUserId(), userInfo);
    }
    
    /**
     * pending页退出一个用户
     * @param userId
     */
    public void removeUserInfo(int userId) {
    	audMap.remove(userId);
    }
    
    public void clear() {
    	audMap.clear();
    }
    /**
     * 获取进入房间人员列表
     *
     * @return
     */
    public Map<Integer, UserInfo> getAudMap() {
        return audMap;
    }
    
    public static String trimString(String str) {
        return str.replace("@%", "");
    }
    
    /**
     * 组装房间用户基本信息
     *
     * @param status
     * @return
     */
    public synchronized byte[] sendAudBasicInfo(Integer requestCode, int userId, int status) {
    	// 先拷贝一份副本，防止组装数据时audMap在其他地方修改
    	Map<Integer, UserInfo> auds = new HashMap<Integer, UserInfo>(audMap);
        Integer[] playerIdArray = new Integer[auds.size()];
        StringBuilder nickString = new StringBuilder();
        StringBuilder headPicString = new StringBuilder();
        Integer[] playerSexArray = new Integer[auds.size()];

        int i = 0;
        for (Integer key : auds.keySet()) {
        	UserInfo userInfo = auds.get(key);
        	if (userInfo == null) continue;
        	if (i > 0) {
                nickString.append(sp);
                headPicString.append(sp);
            }
            playerIdArray[i] = userInfo.getUserId();
            nickString.append(trimString(userInfo.getNikeName()));
            headPicString.append(trimString(userInfo.getHead()));
            playerSexArray[i] = userInfo.getSex();
            i++;
        }

        logger.debug("nickname: " + nickString.toString());
        Object[][] objs = {
                {60, status, I366ClientPickUtil.TYPE_INT_1},				// 0成功 1失败 2解散
                {132, playerIdArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},
                {135, nickString.toString(), I366ClientPickUtil.TYPE_STRING_UTF16},
                {137, headPicString.toString(), I366ClientPickUtil.TYPE_STRING_UTF16},
                {138, playerSexArray, I366ClientPickUtil.TYPE_INT_1_ARRAY}
        };
        byte[] bytes = I366ClientPickUtil.packAll(objs, requestCode);
        send(bytes, userId, auds);
        auds.clear();
        return bytes;
    }
    
    /**
     * 通知所有用户
     * @param bytes
     */
    public void send(byte[] bytes) {
        send(bytes, audMap);
    }
    
    /**
     * 通知所有用户
     * @param bytes
     * @param auds 指定观众列表
     */
    public void send(byte[] bytes, Map<Integer, UserInfo> auds) {
    	for (Integer key : auds.keySet()) {
    		if (auds.get(key) != null) {
    			PublisherUtil.publisher(auds.get(key), bytes);
    		}
    	}
    }
    
    /**
     * 通知其它玩家
     * @param bytes
     * @param usId
     */
    public void send(byte[] bytes, int usId) {
        send(bytes, usId, audMap);
    }
    
    /**
     * 通知其他玩家
     * @param bytes
     * @param usId 排除玩家id
     * @param auds 通知观众列表
     */
    public void send(byte[] bytes, int usId, Map<Integer, UserInfo> auds) {
        for (Integer key : auds.keySet()) {
    		if (auds.get(key) != null) {
    			if (auds.get(key).getUserId() != usId) {
    				PublisherUtil.publisher(auds.get(key), bytes);
    			}
    		}
    	}
    }

    /**
     * 通知某个玩家
     * @param bytes
     * @param userId
     */
    public void sendByUserId(byte[] bytes, int userId) {
        UserInfo userInfo = audMap.get(userId);
        if (userInfo == null) {
            logger.debug("err-----send err userId is : " + userId);
            return;
        }
        PublisherUtil.publisher(userInfo, bytes);
    }
}

