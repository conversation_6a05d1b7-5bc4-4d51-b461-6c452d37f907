
package com.dzpk.processor.impl;

import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.constant.Constant;
import com.i366.cache.Cache;
import com.i366.model.room.Room;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.server.pack.I366ServerPickUtil;
import com.work.comm.client.protocal.Request;
import com.dzpk.common.utils.LogUtil;
import com.i366.util.PublisherUtil;
import org.apache.logging.log4j.Logger;

/**
 * 房主延时操作，不收取任何费用
 */
public class Request_413_AddTime implements IProcessor {

    private Logger logger = LogUtil.getLogger(Request_413_AddTime.class);

    @Override
    public void handle(Task task) {

        int roomId = task.getRoomId();
        int roomPath = task.getRoomPath();
        Request request = (Request) task.getRequest();
        int userId = request.getUserId();
        int time = (Integer) task.getMap().get(133);

        logger.debug("roomId:" + roomId + " roomPath :" + roomPath + " addTime: " + time + " userId: " + userId);

        try {
            Room room = Cache.getRoom(roomId, roomPath);
            if(null == room){
                logger.error("add room time error,room is null");
                PublisherUtil.publisher(request, pusUser(1));
            }

            if (room.getOwner() != userId) { //校验是否为房主
                logger.error("add room time error, user is not room owner,roomid={},userid={},owner={}",roomId,userId,room.getOwner());
                PublisherUtil.publisher(request, pusUser(1));
            }

            if (room.getRoomService().addTime(time)){
                logger.debug("add room time successfully,roomid={},now room playtime={}",roomId,room.getMaxPlayTime());
                //通知其他玩家房间延时成功
                Object[][] objs = {
                        { 60, 0, I366ClientPickUtil.TYPE_INT_1 }, // 0 成功 1 失败
                        { 130, time, I366ClientPickUtil.TYPE_INT_4 }, // 增加的时间30，60，90
                };
                byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_ROOM_ADD_TIME);
                PublisherUtil.send(room,bytes, userId);

                //通知本人房间延时成功
                Object[][] neObjs = {
                        { 60, 0, I366ClientPickUtil.TYPE_INT_1 }, // 0 成功 1 失败
                        { 130, time, I366ClientPickUtil.TYPE_INT_4 }, // 增加的时间30，60，90
                        { 131, 0, I366ClientPickUtil.TYPE_INT_4 } // 房主剩余钻石
                };
                bytes = I366ClientPickUtil.packAll(neObjs, Constant.REQ_ROOM_ADD_TIME);
                PublisherUtil.sendByUserId(room,bytes, userId);
                return;

            }else{
                PublisherUtil.publisher(request, pusUser(1));
                return;
            }

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("add room time error,roomid={},userid={},addtime={}", roomId,userId,time);
        }

    }

    private byte[] pusUser(int status) {
        Object[][] objs = {
                { 60, status, I366ClientPickUtil.TYPE_INT_1 },
        };
        byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_ROOM_ADD_TIME);
        return bytes;
    }

}
