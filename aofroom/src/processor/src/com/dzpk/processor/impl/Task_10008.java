package com.dzpk.processor.impl;

import com.dzpk.common.utils.LogUtil;
import org.apache.logging.log4j.Logger;

import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.cache.Cache;
import com.i366.model.room.Room;

/**
 * 保险模式
 * <AUTHOR>
 *
 */
public class Task_10008 implements IProcessor {

    private Logger logger = LogUtil.getLogger(Task_10008.class);
    
    @Override
    public void handle(Task task) {
        if (!task.isValid()) {
            logger.debug("invalid message");
            return;
        }
        
        Room room = Cache.getRoom(task.getRoomId(), task.getRoomPath());
        logger.debug("roomId: " + task.getRoomId() + ", roomPath: " + task.getRoomPath());
        // 进入保险状态
        if (room != null) {
            int bipaiIndex = (int) task.getMap().get(1);
            room.roomProcedure.checkInsure(bipaiIndex);
            
            room.roomProcedure.delayTaskMap.remove(task.getId());
        }
    }

}
