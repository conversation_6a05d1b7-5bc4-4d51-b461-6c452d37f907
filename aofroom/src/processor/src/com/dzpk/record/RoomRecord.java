package com.dzpk.record;

import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;

import com.dzpk.common.utils.LogUtil;
import org.apache.logging.log4j.Logger;

import com.i366.room.BiPai;
import com.i366.model.player.RoomPersion;
import com.i366.model.room.Room;
import com.i366.model.pocer.Pocer;

public class RoomRecord {
    
    private Logger logger = LogUtil.getLogger(RoomRecord.class);
    
    // 上局游戏数据
    private PrevGame prevGame;
    // 当前局游戏数据(边打边更新)
    private PrevGame currentGame;
    
    public RoomRecord() {
        currentGame = new PrevGame();
    }
     
    public void setPrevGame(PrevGame prevGame) {
        this.prevGame = prevGame;
    }
    
    public PrevGame getPrevGame() {
        return prevGame;
    }
    
    public void setCurrentGame(PrevGame currentGame) {
        this.currentGame = currentGame;
    }
    
    public PrevGame getCurrentGame() {
        return currentGame;
    }
    
    public void initHandPoker(Room room) {
        for (RoomPersion p : room.getRoomPersions()) {
            if (p != null && p.getNowcounma() >= 0) {
                PrevPlayer prevPlayer = new PrevPlayer();
                prevPlayer.setUserId(p.getUserId());
                prevPlayer.getPocers()[0] = p.getPocers()[0].getSize1();
                prevPlayer.getPocers()[1] = p.getPocers()[1].getSize1();
                prevPlayer.setNikeName(p.getUserInfo().getNikeName());
                prevPlayer.setUserHead(p.getUserInfo().getHead());
                prevPlayer.setSex(p.getUserInfo().getSex());
//                if (p.getSize() == room.getDamanzhuNumber()) {
//                    prevPlayer.setChips(p.getNowcounma() + room.getDamanzhu());
//                } else if (p.getSize() == room.getManzhuNumber()) {
//                    prevPlayer.setChips(p.getNowcounma() + room.getManzhu());
//                } else {
//                    prevPlayer.setChips(p.getNowcounma());
//                }
                prevPlayer.setChips(p.getNowcounma() + p.getBetChouma());
                prevPlayer.setSeatId(p.getSize());
                if (p.getStraddleChip() > 0) {
                    prevPlayer.setStraddleChip(p.getStraddleChip());
                }
                currentGame.getPrevPlayers().put(p.getUserId(), prevPlayer);
                //logger.debug("inithand roomid:" + room.getRoomId() + " userid: " + p.getUserId());
            }
        }
        currentGame.setBigBlind(room.getDamanzhuNumber());
        currentGame.setSmallBlind(room.getManzhuNumber());
        currentGame.setBigBlindChip(room.getDamanzhu());
        currentGame.setSmallBlindChip(room.getManzhu());
        currentGame.setQianzhu(room.getQianzhu());
        currentGame.setDealer(room.getZhuangjiaNumber());
        currentGame.setSeats(room.getPlayerCount());
    }
    
    public void setDeskPoker(Room room) {
        for (int i = 0; i < room.getPocer().length; i++) {
            if (room.getPocer()[i] != null) {
                currentGame.getDeskPocers()[i] = room.getPocer()[i].getSize1();
            } else {
                currentGame.getDeskPocers()[i] = -1;
            }
        }
    }
    
    /**
     * 查找7张牌中最大的5张牌下标
     * @param userId
     * @param maxCards
     */
    public void getMaxCardIndexs(int userId, Pocer[] maxCards) {
        Set<Integer> cards = new HashSet<Integer>();
        for (Pocer p : maxCards) {
            cards.add(p.getSize1());
        }
        int k = 0;
        PrevPlayer player = currentGame.getPrevPlayers().get(userId);
        for (int i = 0; i < 2; i++) {
            if (cards.contains(player.getPocers()[i])) {
                player.setMaxCardIndex(k++, i);
                cards.remove(player.getPocers()[i]);
            }
        }
        for (int i = 0; i < 5; i++) {
            if (cards.isEmpty() || k == 5) {
                break;
            }
            if (cards.contains(currentGame.getDeskPocers()[i])) {
                player.setMaxCardIndex(k++, i + 2);
                cards.remove(currentGame.getDeskPocers()[i]);
            }
        }
        cards = null;
    }
    
    /**
     * 计算牌型 2张牌、5-7张牌
     * @param player
     * @return
     */
    public int getPocerType(PrevPlayer player) {
        int pocerType = 10;     // 高牌
        try {
            if (player.getPocers()[0] % 13 == player.getPocers()[1] % 13) {
                pocerType = 9;      // 一对
            }
            int deskPocerNum = 5;
            for (int i = 4; i >= 0; i--) {
                if (currentGame.getDeskPocers()[i] != -1) {
                    break; 
                }
                deskPocerNum--;
            }
            if (deskPocerNum >= 3) {
                Pocer[] pocers = new Pocer[deskPocerNum + 2];
                pocers[0] = new Pocer(player.getPocers()[0]);
                pocers[1] = new Pocer(player.getPocers()[1]);
                for (int i = 0; i < deskPocerNum; i++) {
                    pocers[i + 2] = new Pocer(currentGame.getDeskPocers()[i]);
                }
                Object[] objs = BiPai.zuidapai1(pocers);
                pocerType = (Integer) objs[1];
                player.setPocerType(pocerType);
                getMaxCardIndexs(player.getUserId(), (Pocer[]) objs[0]);
            }
        } catch (Exception e) {
            logger.error("error: ", e);
        }
        player.setPocerType(pocerType);
        return pocerType;
    }
    
    /**
     * 一局游戏结束，记录牌型和输赢情况
     * @param room
     * @param compare 是否是通过比牌产生最后结果
     */
    public void setGameResult(Room room, boolean compare, Integer[] winners) {
        currentGame.setCompare(compare);
        setDeskPoker(room);
        // 桌上玩家
        for (RoomPersion rp : room.getRoomPersions()) {
            if (rp != null) {
                PrevPlayer prevPlayer = currentGame.getPrevPlayers().get(rp.getUserId());
                if (prevPlayer == null) {
                    logger.error("setGameResult prevPlayer is null userid: " + rp.getUserId());
                    // 通知正在打牌的玩家保险出了异常
                    /*Object[][] objs = {
                            {60, 0, I366ServerPickUtil.TYPE_INT_1}
                    };
                    byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_INSURANCE_EXCEE);
                    room.getRoomService().sendInsuranceError(bytes);*/
                    
                    // 通知牌局创建者和产品负责人保险出现了异常
                    /*String owerMsg = room.getName() + "\"牌桌异常，请提前解散房间。";
                    Date now = new Date(); 
                    SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm:ss");
                    String managerMsg = dateFormat.format(now) + ",\"" + room.getName() + "\"发生了异常，请联系相关人员尽快处理！";
                    room.getRoomService().addErrorInfo(owerMsg, managerMsg);*/
                    continue;
                }
                
//                prevPlayer.setWinChip(rp.getYq() + rp.getWinInsuranceLast());
                prevPlayer.setWinChip(rp.getYq());
                prevPlayer.setWinInsurance(rp.getInsuranceLast());
                // 战绩页保险明细经常出错，加上打印方便查看
                logger.debug("fengbingwen setGameResult roomId=" + room.getRoomId() + " userId:" + rp.getUserId() + " getInsuranceLast=" + rp.getInsuranceLast());
                prevPlayer.setShowCardId(rp.getShowCardsId());
                room.getDealer().addWinCnt(rp.getUserId(), rp.getYq() + rp.getWinInsuranceLast());
                //logger.debug("rp muck:" + rp.getMuck());
                if (rp.getMuck() == true) {
                    prevPlayer.setPocerType(-2);
                } else if (rp.getClientStatus() == 6) {
                    prevPlayer.setPocerType(0);
                } else {
                    if (compare) {
                        prevPlayer.setPocerType(rp.getPocerType());
                        getMaxCardIndexs(rp.getUserId(), rp.getZuidaPocers());
                    } else {
                        getPocerType(prevPlayer);
                    }
                }
            }
        }
        // 站起玩家
        Iterator<Integer> it = room.getStandUpUserInfo().keySet().iterator();
        while (it.hasNext()) {
            int userId = (Integer) it.next();
            Object[] standUpUserInfo = room.getStandUpUserInfo().get(userId);
            int winChip = (Integer) standUpUserInfo[1];
            PrevPlayer prevPlayer = currentGame.getPrevPlayers().get(userId);
            prevPlayer.setPocerType(0);    // 弃牌
            prevPlayer.setWinChip(winChip);
            prevPlayer.setWinInsurance(0);
            RoomPersion rp = room.getAudMap().get(userId);
            if (rp != null) {
                prevPlayer.setShowCardId(rp.getShowCardsId());
                room.getDealer().addWinCnt(rp.getUserId(), winChip);
            }
        }

        // 设置保险收支
        currentGame.setInsurance(room.getInsuranceLast());

        // 设置基金
        currentGame.setFund(0);

        // 设置赢家
        currentGame.setWinners(winners);
        
        // 设置muck开关状态
        currentGame.setMuckSwitchStatus(room.getMuckSwitch());

        //设置当前手的jackpot投入量
        currentGame.setJackpot(null == room.getJackpotService()?0:room.getJackpotService().getCurrentTotalJpBet());

        room.getRoomReplay().setWinners(winners);
        
        nextGame();
    }
    
    // 开始下局游戏
    public void nextGame() {
        prevGame = currentGame;
        currentGame = new PrevGame();
    }
}
