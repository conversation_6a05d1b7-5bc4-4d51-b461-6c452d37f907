package com.ai.dz.config.cache;

import com.ai.dz.config.constant.EAction;
import com.ai.dz.config.constant.EFlowCase;
import com.dzpk.common.utils.Helper;
import com.dzpk.common.utils.LogHelper;
import lombok.Getter;

@Getter
public class CallActionConfigBo {
    /** 对应本来类的动作标识 */
    private EAction action = EAction.call;

    /**
     * 满足条件的比例
     * null或小于0 表示无效，不进行此条件判断
     */
    private Integer gtXBB=null;

    /**
     * 满足条件的比例: 剩余筹码比例
     * null或小于0 表示无效，不进行此条件判断
     * 80% -> 80
     */
    private Integer gteRemainRatio=null;

    private CallActionConfigBo(Integer gtXBB, Integer gteRemainRatio){
        this.gtXBB = gtXBB;
        this.gteRemainRatio = gteRemainRatio;
    }

    public static CallActionConfigBo initialize(Integer gtXBB , int gteRemainRatio){
        return new CallActionConfigBo(gtXBB,gteRemainRatio);
    }

    /**
     * win-preflop : if(cp> xx BB){fold} else { if (CP/当前筹码)>=YY%  {allin} else {call} }
     * win-broad   : if(cp> xx BB){fold} else { if (CP/当前筹码)>=YY%  {allin} else {call} }
     * lose-preflop: if(cp> xx BB){fold} else { if (CP/当前筹码)>=YY%  {allin} else {call} }
     * lose-board  : if(cp> xx BB){fold} else { if (CP/当前筹码)>=YY%  {allin} else {call} }
     *
     * @param flowCase     流程
     * @param remainChip   剩余筹码
     * @param checkChip    cp筹码
     * @param bb           盲注
     *
     * @return  返回Action
     *    null  则表示不满足条件或条件缺失
     */
    public EAction selectAction(EFlowCase flowCase,int remainChip, int checkChip, int bb){
       /* if(flowCase == EFlowCase.perflopWin ||
                flowCase == EFlowCase.flopWin)
            return this.winFlow(flowCase,checkChip,bb);
        else
            return this.loseFlow(flowCase,remainChip,checkChip,bb);*/

        EAction resultAction = this.action;
        String bbMatchReason = "配置无效";
        String ratioMatchReason = "配置无效";

        try {
            // BB
            if (null != this.gtXBB && this.gtXBB >= 0){
                // BB
                int computeBB = Helper.fixDecimalPlace(Helper.multiply(this.gtXBB,bb),0);
                boolean match = checkChip > computeBB;
                bbMatchReason = String.format("checkChip(%s) 大于 bb(%s) * gtXBB(%s) = %s 【%s】 => ",
                        checkChip,bb,this.gtXBB,computeBB,match);
                if(match) {
                    resultAction = EAction.fold;
                    bbMatchReason = bbMatchReason + resultAction.name();
                    return resultAction;
                }
                // 记录这一步的结果Action
                bbMatchReason = bbMatchReason + resultAction.name();

                // 剩余筹码
                if (null != this.gteRemainRatio && this.gteRemainRatio >= 0){
                    int computeRatio = Helper.fixDecimalPlace(Helper.divide(checkChip, remainChip),100);
                    match = computeRatio >= this.gteRemainRatio;
                    ratioMatchReason = String.format("(checkChip(%s) / remainChip(%s) * 100)= %s 大于或等于 gteRemainRatio(%s) 【%s】 => ",
                            checkChip,remainChip,computeRatio,this.gteRemainRatio,match);
                    if(match) {
                        resultAction = EAction.allin;
                    }
                    ratioMatchReason = bbMatchReason + resultAction.name();
                }
            }

            return resultAction;
        } finally {
            LogHelper.log("%s   %s-%s转换Action:remainChip=%s,checkChip=%s,bb=%s" +
                            "-> %s , %s -> %s",
                    System.lineSeparator(), flowCase, this.action, remainChip, checkChip, bb,
                    bbMatchReason,ratioMatchReason,resultAction);
        }
    }

    /**
     * win-preflop : if(cp>3BB) {fold} else {call}
     * win-broad   : if(cp>100000BB){fold} else {call}
     *
     * @param checkChip    cp筹码
     * @param bb           盲注
     *
     * @return  返回Action
     *    null  则表示不满足条件或条件缺失
     */
    private EAction winFlow(EFlowCase flowCase,int checkChip, int bb) {
        EAction resultAction = this.action;
        String bbStr = "配置无效";
        boolean bbMatch = false;

        try {
            // 配置必须存在
            if (null == this.gtXBB || this.gtXBB < 0)
                return resultAction;

            // BB
            int computeBB = Helper.fixDecimalPlace(Helper.multiply(this.gtXBB,bb),0);
            bbStr = String.valueOf(computeBB);
            if(checkChip > computeBB){
                resultAction = EAction.fold;
                bbMatch = true;
            }

            return resultAction;
        } finally {
            LogHelper.log("%s   %s-%s转换的Action:checkChip=%s,bb=%s" +
                            "-> BBMatched[ checkChip(%s) 大于  bb(%s) * gtXBB(%s) = %s ]=%s => %s !",
                    System.lineSeparator(), flowCase, this.action, checkChip, bb,
                    checkChip,bb,this.gtXBB,  bbStr,bbMatch, resultAction);
        }
    }

    /**
     * lose-preflop: if(cp>200BB || cp/当前筹码 >=45%){fold} else {call}
     * lose-board  : if(cp>200BB || cp/当前筹码 >=45%){fold} else {call}
     *
     * @param remainChip   剩余筹码
     * @param checkChip    cp筹码
     * @param bb           盲注
     *
     * @return  返回Action
     *    null  则表示不满足条件或条件缺失
     */
    private EAction loseFlow(EFlowCase flowCase,int remainChip, int checkChip, int bb){
        EAction resultAction = this.action;
        String ratioStr = "配置无效";
        boolean ratioMatch = false;
        String bbStr = "配置无效";
        boolean bbMatch = false;

        try {
            // 剩余筹码
            if (null != this.gteRemainRatio && this.gteRemainRatio >= 0){
                int computeRatio = Helper.fixDecimalPlace(Helper.divide(checkChip, remainChip),100);
                ratioStr = String.valueOf(computeRatio);
                if (computeRatio >= this.gteRemainRatio) {
                    ratioMatch = true;
                }
            }

            // BB
            if (null != this.gtXBB && this.gtXBB >= 0){
                int computeBB = Helper.fixDecimalPlace(Helper.multiply(this.gtXBB,bb),0);
                bbStr = String.valueOf(computeBB);
                if(checkChip > computeBB) {
                    bbMatch = true;
                }
            }

            if(bbMatch || ratioMatch){
                resultAction = EAction.fold;
            }

            return resultAction;
        } finally {
            LogHelper.log("%s   %s-%s转换的Action:remainChip=%s,checkChip=%s,bb=%s" +
                            "-> remainRatioMatched[ checkChip(%s) / remainChip(%s) * 100 = %s 大于或等于 gteRemainRatio(%s) ]=%s," +
                            "BBMatched[ checkChip(%s) 小于 bb(%s) * gtXBB(%s) = %s ]=%s => %s !",
                    System.lineSeparator(), flowCase, this.action, remainChip, checkChip, bb,
                    checkChip,remainChip,ratioStr,this.gteRemainRatio,ratioMatch,
                    checkChip,bb,this.gtXBB,  bbStr,bbMatch, resultAction);
        }
    }
}
