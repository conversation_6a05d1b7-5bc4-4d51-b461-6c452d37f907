package com.ai.dz.config.monitor;

import com.ai.dz.config.cache.ActionOptimeConfigBaseBo;
import com.ai.dz.config.cache.ActionOptimeRatioConfigBo;
import com.ai.dz.config.cache.impl.AiRuleConfigJsonCacheImpl;
import com.dzpk.common.utils.Helper;
import com.dzpk.common.utils.LogHelper;
import com.dzpk.component.file.IFileChangedHandler;
import com.google.common.reflect.TypeToken;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Type;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class ActionOpTimeConfigHandler extends AbstractFileChangedHandler
        implements IFileChangedHandler {
    private String fileName;

    public String fileName(){
        return this.fileName;
    }

    public ActionOpTimeConfigHandler(AiRuleConfigJsonCacheImpl cache){
        super(cache);
        this.fileName = "action_optime_config.json";
    }

    public void handle(Path filePath){
        if(null == filePath)
            return;

        StringBuilder traceMsg = null;
        Throwable exception = null;
        if(log.isDebugEnabled()){
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("【Action思考时间配置】: %s",filePath));
        }

        try {
            String json = this.readJson(filePath);
            if(null == json || "".equals(json.trim())) {
                LogHelper.log("%s   file content is empty -> skipped!!",System.lineSeparator());
                return;
            }

            json = json.trim();
            Type type = new TypeToken<OptimeConfigVo>(){}.getType();
            OptimeConfigVo configVo = this.parseJsonAsSingle(json,type);
            if(null == configVo) {
                LogHelper.log("%s   json is empty -> skipped!!",System.lineSeparator());
                return;
            }

            ActionOptimeConfigBaseBo other = this.parseOtherConfig(configVo.getOther());
            if(null == other){
                LogHelper.log("%s   config invalid: all empty -> skipped!!",System.lineSeparator());
                return;
            }

            List<ActionOptimeConfigBaseBo> configLst = new ArrayList<>();
            if(null != other)
                configLst.add(other);
            this.cache.reloadActionOptimeConfig(configLst);
        }catch (Exception ex){
            exception = ex;
            LogHelper.log("%s   系统异常: %s",System.lineSeparator(),ex.getMessage());
        }finally {
            if(null != traceMsg){
                LogHelper.removeLog();
                log.debug(traceMsg.toString());
            }
            if(null != exception)
                log.error("",exception);
        }
    }

    private ActionOptimeConfigBaseBo parseOtherConfig(OptimeRatioConfigVo[] ratioArr){
        ActionOptimeConfigBaseBo result = null;

        if(null == ratioArr || ratioArr.length==0){
            LogHelper.log("%s   config【other】invalid: null -> skipped!!",System.lineSeparator());
            return result;
        }
        List<ActionOptimeRatioConfigBo> ratioLst = this.parseRatioConfig(ratioArr,"other");
        if(null == ratioLst || ratioLst.isEmpty()){
            LogHelper.log("%s   config【other】invalid: ratio empty -> skipped!!",System.lineSeparator());
            return result;
        }

        result = new ActionOptimeConfigBaseBo(ratioLst);
        return result;
    }

    private List<ActionOptimeRatioConfigBo> parseRatioConfig(OptimeRatioConfigVo[] configArr,String desc){
        List<ActionOptimeRatioConfigBo> resultLst = null;
        if(null == configArr || configArr.length==0)
            return resultLst;

        int pos = -1;
        int posRatio = 1;
        int startRatio=0;
        int endRatio = 0;
        for(OptimeRatioConfigVo config : configArr){
            pos ++;
            if(null == config)
            {
                LogHelper.log("%s   %s.Pos[%s]invalid: null -> skipped",System.lineSeparator(),desc,pos);
                continue;
            }

            int ratio = Helper.parseIntFromPercentFormat(config.getRatio(),-1);
            if(ratio<0){
                LogHelper.log("%s   %s.Pos[%s] invalid: ratio less than zero -> skipped",System.lineSeparator(),desc,pos);
                continue;
            }

            if(null == config.getMinSec() || config.getMinSec()<0){
                LogHelper.log("%s   %s.Pos[%s] invalid: minSec less than zero -> skipped",System.lineSeparator(),desc,pos);
                continue;
            }

            if(null == config.getMaxSec() || config.getMaxSec()<0){
                LogHelper.log("%s   %s.Pos[%s] invalid: maxSec less than zero -> skipped",System.lineSeparator(),desc,pos);
                continue;
            }

            if(config.getMinSec()>config.getMaxSec()){
                LogHelper.log("%s   %s.Pos[%s] invalid: maxSec less than minSec -> skipped",System.lineSeparator(),desc,pos);
                continue;
            }

            ratio = (ratio<=0?0:ratio);
            if(ratio>0){
                startRatio = posRatio;
                posRatio = startRatio+ratio;
                endRatio = posRatio-1;
            }
            ActionOptimeRatioConfigBo bo = new ActionOptimeRatioConfigBo(ratio,
                    config.getMinSec(),config.getMaxSec(),
                    startRatio,endRatio);

            if(null == resultLst)
                resultLst = new ArrayList<>();

            resultLst.add(bo);
        }

        return resultLst;
    }

    @Getter
    @Setter
    private static class OptimeConfigVo{

        /**
         * 操作时间配置
         */
        private OptimeRatioConfigVo[] other;
    }

    @Getter
    @Setter
    private static class OptimeRatioConfigVo{
        /**
         * 随机比例，百分数
         * 格式：50 或 50%
         * null或小于0无效
         */
        private String ratio;

        /**
         * 随机最小秒数
         *
         * 必须>=0
         */
        private Integer minSec;
        /**
         * 随机最最大秒数
         *
         * 必须>=0
         */
        private Integer maxSec;
    }
}
