package com.ai.dz.config.cache;

import com.ai.dz.config.constant.EAction;
import com.dzpk.common.utils.Helper;
import com.dzpk.common.utils.LogHelper;
import lombok.Getter;

@Getter
public class FoldActionConfigBo {
    /** 对应本来类的动作标识 */
    private EAction action = EAction.fold;

    /**
     * 满足条件的比例
     * null或小于0 表示无效，不进行此条件判断
     */
    private Integer lteXBB=null;

    private FoldActionConfigBo(Integer lteXBB){
        this.lteXBB = lteXBB;
    }

    public static FoldActionConfigBo initialize(Integer lteXBB){
        return new FoldActionConfigBo(lteXBB);
    }

    /**
     * win-preflop : if(cp <= 0 BB){check} else{fold};
     * win-broad   : if(cp <= 0 BB){check} else{fold};
     * lose-preflop: if(cp <= 0 BB){check} else{fold};
     * lose-board  : if(cp <= 0 BB){check} else{fold};
     *
     * @param checkChip   下注筹码
     * @param bb          盲注
     *
     * @return  返回Action
     *    null  则表示不满足条件或条件缺失
     */
    public EAction selectAction(int checkChip, int bb){
        EAction resultAction = this.action;
        String ratioStr = "配置无效";

        try {
            // BB
            if (null == this.lteXBB || this.lteXBB<0)
                return resultAction;

            int computeRatio = Helper.fixDecimalPlace(Helper.multiply(this.lteXBB,bb),0);
            boolean matched = checkChip <= computeRatio;
            if(matched) {
                resultAction = EAction.check;
            }else{
                resultAction = EAction.fold;
            }

            ratioStr = String.format("checkChip ( %s ) 小于或等于 [ bb ( %s ) * lteXBB( %s ) = %s ]【%s】",
                    checkChip,bb,this.lteXBB,computeRatio,matched);
            return resultAction;
        }finally {
            LogHelper.log("%s   %s转换的Action:checkChip=%s,bb=%s -> %s -> %s !",
                    System.lineSeparator(),this.action,checkChip,bb,ratioStr,resultAction);
        }
    }
}
