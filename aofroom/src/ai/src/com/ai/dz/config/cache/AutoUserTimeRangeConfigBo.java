package com.ai.dz.config.cache;

import com.dzpk.common.utils.Helper;
import lombok.Getter;

import java.util.List;
import java.util.Map;

@Getter
public class AutoUserTimeRangeConfigBo implements Comparable<AutoUserTimeRangeConfigBo> {
    /**
     * 时间范围
     * 格式：HH:mm
     */
    private Long startTime;
    private Long endTime;

    /** 盲注级别 */
    private Map<String, List<UserConfigBo>> userMap;

    public boolean checkIfTimeMatch(long sysTime){
        boolean match = false;

        match = ((null == startTime || startTime <= sysTime) &&
                (null == endTime || sysTime < endTime));

        return match;
    }

    public List<UserConfigBo> getUserConfig(String blindCode){
        if(null == blindCode || "".equals(blindCode.trim()))
            return null;

        if(null == this.userMap || this.userMap.isEmpty())
            return null;

        return this.userMap.get(blindCode.trim());
    }

    public AutoUserTimeRangeConfigBo(Long startTime,Long endTime,Map<String, List<UserConfigBo>> userMap){
        this.startTime = startTime;
        this.endTime = endTime;
        this.userMap = userMap;
    }

    public int compareTo(AutoUserTimeRangeConfigBo other){
        if(null == other)
            return 1;

        int minResult = Helper.compareTo(this.startTime,other.startTime,false);
        if(minResult != 0)
            return minResult;

        int maxResult = Helper.compareTo(this.endTime,other.endTime,true);
        return maxResult;
    }
}
