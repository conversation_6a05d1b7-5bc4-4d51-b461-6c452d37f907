package com.ai.dz.config.cache;

import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class ActionOptimeRatioConfigBo {
    /**
     * 随机比例
     * 小于0无效
     */
    private int ratio;

    /**
     * 随机最小秒数
     *
     * 必须>=0
     */
    private int minSec;
    /**
     * 随机最最大秒数
     *
     * 必须>=0
     */
    private int maxSec;

    private int minRatio;
    private int maxRatio;

    public ActionOptimeRatioConfigBo(int ratio,int minSec,int maxSec,int minRatio,int maxRatio){
        this.ratio = ratio;
        this.minSec = minSec;
        this.maxSec = maxSec;
        this.minRatio = minRatio;
        this.maxRatio = maxRatio;
    }
}
