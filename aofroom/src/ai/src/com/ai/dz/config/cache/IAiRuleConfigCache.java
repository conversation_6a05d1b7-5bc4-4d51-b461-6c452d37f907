package com.ai.dz.config.cache;

import com.ai.dz.config.cache.impl.AiRuleConfigJsonCacheImpl;
import com.ai.dz.config.constant.*;

import java.util.List;

public interface IAiRuleConfigCache {
    static IAiRuleConfigCache getInstance(){
        return AiRuleConfigJsonCacheImpl.getInstance();
    }

    PocerWeightConfigBo getPocerWeightConfig(String key);
    FlowCaseActionConfigBo getFlowActionConfig(EPerHandPhase phase, boolean useSlave);
    BetConfigBo getBetConfig(EFlowCase flowCase, int weight);
    Integer getOpTime(EOpTime opTime);

    UserOptypeConfigBo getUserTypeConfig(int userId);
    UserConfigBo getAutoUserConfig(int userId);

    ManualStandupConfigBo getManualStandupConfig();
    AutoOverallConfigBo getAutoOverallConfig();

    List<UserConfigBo> getConfigUserIfMatch(String blindCode, long sysTime);
    List<Integer> getAllUserOfAt();

    FunctionSwitchConfigBo getSwitchConfig();
    List<InsuanceBuyRuleItemBo> getInsuanceBuyRuleConfig(EInsuranceConfig config);

    CommonDispatchConfigBo getCommonDispatchConfig();
    CommonStandupConfigBo getCommonStandupConfig();
    CommonBringinConfigBo getCommonBringinConfig();
    BringinPerUserTypeConfigBo getBringinPerUserTypeConfig(EAiType userType);
    CommLeftInAdvanceConfigBo getLeftInAdvanceConfig();

    Boolean checkIfRoomViewerWhitelist(int userId);

    /**
     * 行动思考时间,单位：秒
     *
     * @param isBBOrStradle  是否【BB或stradle】,必填
     * @param checkChip      待跟注筹码，必填
     * @param bbChip         牌局的大盲注,必填
     * @param potChip        牌局的底池，必填
     * @param action         当前确定行动，必填
     * @param betChip        当前行动对应的下注筹码，必填
     *
     * @return   行动思考时间
     *    <0     表示配置缺失或配置错误
     *    >=0    表示确定的思考时间
     */
    int getOptimeOfAction(boolean isBBOrStradle, int checkChip, int bbChip,
                          int potChip, EAction action, int betChip);

    /**
     * 获取时间区间的爆桌配置
     *
     * @return
     */
    ExplosionTableConfigBo getExplosionTableConfig(long sysTimes);
}
