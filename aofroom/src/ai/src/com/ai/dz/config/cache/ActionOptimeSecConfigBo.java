package com.ai.dz.config.cache;

import com.ai.dz.config.constant.EAction;
import com.dzpk.common.utils.LogHelper;

import java.util.List;

public class ActionOptimeSecConfigBo extends ActionOptimeConfigBaseBo {
    /**
     * 匹配条件之一
     * null 或小于0 :  不考虑此条件
     */
    private int cpGteXBB;

    /**
     * 匹配条件之二
     * null 或小于0 :  不考虑此条件
     */
    private int betChipGteXBB;

    /**
     * 匹配条件之三
     * null 或小于0 :  不考虑此条件
     */
    private int potGteXBB;

    public ActionOptimeSecConfigBo(Integer cpGteXBB,Integer betChipGteXBB,Integer potGteXBB,
                                   List<ActionOptimeRatioConfigBo> ranges){
        super(ranges);

        this.cpGteXBB = cpGteXBB==null?-1:cpGteXBB;
        this.betChipGteXBB = betChipGteXBB==null?-1:betChipGteXBB;
        this.potGteXBB = potGteXBB==null?-1:potGteXBB;
    }

    /**
     * 检查是否匹配
     *
     * @param isBBOrStradle  是否【BB或stradle】,必填
     * @param checkChip      待跟注筹码，必填
     * @param bbChip         牌局的大盲注,必填
     * @param potChip        牌局的底池，必填
     * @param action         当前确定行动，必填
     * @param betChip        当前行动对应的下注筹码，必填
     *
     * @return Boolean 是否匹配
     *    null : 表示配置缺失或配置错误
     *    true : 匹配
     *   false : 不匹配
     */
    @Override
    public Boolean checkIfMatch(boolean isBBOrStradle, int checkChip, int bbChip,
                                int potChip, EAction action, int betChip){
        Boolean result = null;

        if(this.cpGteXBB<0 && this.betChipGteXBB<0 && this.potGteXBB<0){
            LogHelper.log("%s   配置无效：cpGteXBB & betChipGteXBB & potGteXBB都小于0",System.lineSeparator());
            return result;
        }

        result = false;
        // CP>=40BB 或 行动值>=40BB 或 pot>=50BB
        int temp = this.cpGteXBB * bbChip;
        if(this.cpGteXBB>0 && checkChip >= temp){
            result = true;
            LogHelper.log("%s   【CP>=40BB】匹配:checkChip(%s) > this.cpGteXBB(%s) * bbChip(%s)=%s",
                    System.lineSeparator(),checkChip,this.cpGteXBB,bbChip,temp);
            return result;
        }

        temp = this.betChipGteXBB * bbChip;
        if(this.betChipGteXBB>0 && betChip >= temp){
            result = true;
            LogHelper.log("%s   【行动值>=40BB】匹配:betChip(%s) >= this.betChipGteXBB(%s) * bbChip(%s)=%s",
                    System.lineSeparator(),betChip,this.betChipGteXBB,bbChip,temp);
            return result;
        }

        temp = this.potGteXBB * bbChip;
        if(this.potGteXBB>0 && potChip >= temp){
            result = true;
            LogHelper.log("%s   【pot>=50BB】匹配:potChip(%s) >= this.potGteXBB(%s) * bbChip(%s)=%s",
                    System.lineSeparator(),potChip,this.potGteXBB,bbChip,temp);
            return result;
        }

        return result;
    }
}
