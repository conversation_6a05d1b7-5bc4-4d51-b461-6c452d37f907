package com.ai.dz.room.util;

import com.dzpk.common.utils.LogUtil;
import com.i366.model.player.RoomPersion;
import com.i366.model.player.RoomPlayer;
import com.i366.model.room.Room;
import org.apache.logging.log4j.Logger;

import java.util.Set;

/**
 * Ai座位相关
 * Created by peter<PERSON><PERSON> on 2018/10/16.
 */
public class AiSeat {

    private static Logger logger = LogUtil.getLogger(AiSeat.class);

    /**
     * 获取剩余的空位数(占座不属于空位)
     * @param room
     * @param seatSet 空位集合
     * @return
     */
    public static int getLeftSeatNum(Room room, Set<Integer> seatSet){
        int leftSeatNum; //剩余的空位数

        int onSeatNum = getOnseatNum(room,seatSet);
        logger.debug("座位上玩家数量: " + onSeatNum);

        int occupySeatNum = getOccupySeatNum(room,seatSet);
        logger.debug("留座离桌玩家数量: " + occupySeatNum);

        int waitingForSeatNum = getWaitingForSeatNum(room,seatSet);
        logger.debug("占座玩家数量: " + waitingForSeatNum);

        leftSeatNum = room.getPlayerCount() - (onSeatNum + occupySeatNum + waitingForSeatNum);
        logger.debug("房间空位数: " + leftSeatNum);

        return leftSeatNum;
    }

    /**
     * 获取座位上的玩家包括处于过庄补盲状态的玩家
     * @param room
     * @return
     */
    private static int getOnseatNum(Room room,Set<Integer> seatSet){
        int onSeatNum = 0;
        for (int i = 0; i < room.getRoomPersions().length; i++) {
            RoomPersion roomPersion = room.getRoomPersions()[i];
            if (roomPersion != null) {
                onSeatNum ++;
                seatSet.remove(i);
            }else{
                RoomPersion ddRoomPersion = room.getDdRoomPersions()[i];
                if(ddRoomPersion != null){
                    onSeatNum ++;
                    seatSet.remove(i);
                }
            }
        }

        return onSeatNum;
    }

    /**
     * 获取留座离桌的玩家数
     * @param room
     * @return
     */
    private static int getOccupySeatNum(Room room,Set<Integer> seatSet){
        int occupySeatNum = 0;
        for(Integer id: room.getOccupySeatPlayers().keySet()) {
            // 当前房间离座留座玩家
            RoomPlayer roomPlayer = room.getOccupySeatPlayers().get(id);
            if (null != roomPlayer) {
                int seat = roomPlayer.getSeatSize();
                if (3 == roomPlayer.getSeatStatus()  && seat >= 0 && seat < room.getPlayerCount()) {
                    occupySeatNum ++;
                    seatSet.remove(seat);
                }
            }
        }

        return occupySeatNum;
    }

    /**
     * 获取占座状态的玩家数
     * @param room
     * @return
     */
    private static int getWaitingForSeatNum(Room room,Set<Integer> seatSet){
        int waitingForSeatNum = 0;
        for(int userid: room.getRoomPlayers().keySet()){
            RoomPlayer roomPlayer = room.getRoomPlayers().get(userid);
            if (null != roomPlayer) {
                int seat = roomPlayer.getSeatSize();
                if (4 == roomPlayer.getSeatStatus()  && seat >= 0 && seat < room.getPlayerCount()) {
                    waitingForSeatNum ++;
                    seatSet.remove(seat);
                }
            }
        }

        return waitingForSeatNum;
    }
}
