jdbc-0.proxool.alias=popo
jdbc-0.proxool.driver-url=${jdbc.url}
jdbc-0.proxool.driver-class=com.mysql.jdbc.Driver
jdbc-0.user=${jdbc.username}
jdbc-0.password=${jdbc.password}
jdbc-0.proxool.house-keeping-sleep-time=40000
jdbc-0.proxool.house-keeping-test-sql=select 1 from dual
jdbc-0.proxool.maximum-connection-count=150
jdbc-0.proxool.minimum-connection-count=3
jdbc-0.proxool.maximum-connection-lifetime=18000000
jdbc-0.proxool.simultaneous-build-throttle=20
jdbc-0.proxool.recently-started-threshold=40000
jdbc-0.proxool.overload-without-refusal-lifetime=50000
jdbc-0.proxool.maximum-active-time=60000
jdbc-0.proxool.verbose=false
jdbc-0.proxool.trace=false
jdbc-0.proxool.fatal-sql-exception=Fatal error
jdbc-0.proxool.prototype-count=2
