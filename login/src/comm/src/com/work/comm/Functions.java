package com.work.comm;

import org.apache.logging.log4j.Logger;


import java.awt.*;

/**
 * Created by IntelliJ IDEA.
 * User: Administrator
 * To change this template use File | Settings | File Templates.
 */
public class Functions {

    public static Logger logger = com.work.comm.util.LogUtil.getLogger(Functions.class);

    public Functions() {
    }

    public static int byteArrayToInt(byte[] byteArrayData, int offset){
        return  (byteArrayData[offset]     & 0xff) << 24 |
                (byteArrayData[offset + 1] & 0xff) << 16 |
                (byteArrayData[offset + 2] & 0xff) << 8  |
                (byteArrayData[offset + 3] & 0xff);
    }

    public static int byteArrayToShortInt(byte[] byteArrayData, int offset){
        return (0     & 0xff) << 24 |
               (0     & 0xff) << 16 |
               (byteArrayData[offset] & 0xff) << 8  |
               (byteArrayData[offset + 1] & 0xff);
    }

    public static byte[] shortToByteArray(int intData){
        byte[] result = new byte[2];
        result[0] = (byte)((intData & 0xFF00) >> 8 );
        result[1] = (byte)(intData & 0xFF);
        return result;
    }


    public static byte[] intToByteArray(int intData){
        byte[] result = new byte[4];
        result[0] = (byte)((intData & 0xFF000000) >> 24 );
        result[1] = (byte)((intData & 0xFF0000) >> 16 );
        result[2] = (byte)((intData & 0xFF00) >> 8 );
        result[3] = (byte)(intData & 0xFF);
        return result;
    }

    public static void setIntToByteArray(byte[] bt, int offset, int intData){
        byte[] result = new byte[4];
        result[0] = (byte)((intData & 0xFF000000) >> 24 );
        result[1] = (byte)((intData & 0xFF0000) >> 16 );
        result[2] = (byte)((intData & 0xFF00) >> 8 );
        result[3] = (byte)(intData & 0xFF);

        for(int i=0; i<result.length; i++) {
            bt[offset + i] = result[i];
        }
    }

    public static void setShortToByteArray(byte[] bt, int offset, int intData){
        byte[] result = new byte[2];
        result[0] = (byte)((intData & 0xFF00) >> 8 );
        result[1] = (byte)(intData & 0xFF);

        for(int i=0; i<result.length; i++) {
            bt[offset + i] = result[i];
        }
    }

    public static boolean isValidNumber(String str) {
        if (str == null) return false;
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            int ascii = (int) c;
            if (ascii < 255) {
                if (ascii >= 48 && ascii <= 57) {
                    //is a number
                } else {
                    logger.info("" + ascii);
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * ascii字节流
     * @param ss
     * @return
     */
    public static byte[] StringToBytesUNICODE(String ss){
//        logger.debug("-->> Enter StringToBytesUNICODE()");
//        logger.info("StringToBytesUNICODE = "+ss);
        if(ss==null)
            return null;

        byte[] ssBuffer = new byte[2];
        try{
            ssBuffer = ss.getBytes("UNICODE");
        }catch(Exception e){
            logger.error("getBytes(\"UNICODE\") Exception: " + e) ;
            //汉字的---错
            ssBuffer[0] = -107;
            ssBuffer[1] = 25;
        }
        return ssBuffer;
    }  
    
    public static byte[] StringToBytes(String ss){
        //logger.debug("-->> Enter StringToBytes()");
        //logger.info("StringToBytes = "+ss);
        if(ss==null)
            return null;

        byte[] ssBuffer = new byte[2];
        try{
            ssBuffer = ss.getBytes("UTF-16");
        }catch(Exception e){
            logger.error("getBytes(\"UTF-16\") Exception: " + e) ;
            //汉字的---错
            ssBuffer[0] = -107;
            ssBuffer[1] = 25;
        }
        return ssBuffer;
    }

    public static byte[] StringToBytesUtf8(String ss){
        //logger.debug("-->> Enter StringToBytes()");
        //logger.info("StringToBytes = "+ss);
        if(ss==null)
            return null;

        byte[] ssBuffer = new byte[2];
        try{
//            ssBuffer = ss.getBytes("ISO-10646-UCS-2");
            ssBuffer = ss.getBytes("UTF-8");
        }catch(Exception e){
            logger.error("getBytes(\"UTF-16\") Exception: " + e) ;
            //汉字的---错
            ssBuffer[0] = -107;
            ssBuffer[1] = 25;
        }
        return ssBuffer;
    }

    public static String BytesToString(byte[] ssBuffer){
        if(ssBuffer==null)
            return null;
        int ssLen = ssBuffer.length/2;
        char[] cc = new char[ssLen];

        for(int i = 0;i<ssLen;i++) {
            cc[i] = (char)(((ssBuffer[i*2] & 0xff) << 8) | (ssBuffer[i*2+1] & 0xff));
        }
        return new String(cc);
    }

    public static String BytesToStringUtf8(byte[] ssBuffer){

        if(ssBuffer==null)
            return null;

        byte[] bbt = new byte[ssBuffer.length*2];

        for(int i = 0; i<ssBuffer.length;i++){
            bbt[i*2] = ssBuffer[i];
            bbt[i*2 + 1] = (byte)0;
        }


        int ssLen = bbt.length/2;
        char[] cc = new char[ssLen];

        for(int i = 0;i<ssLen;i++) {
            cc[i] = (char)(((bbt[i*2] & 0xff) << 8) | (bbt[i*2+1] & 0xff));
        }
        return new String(cc);
    }

        public static Point fourPlayerCoorFromAbsolute(
        boolean clockwise, Point point, int player,
        int boardWidth, int boardHeight){

        int convertedX = 0;
        int convertedY = 0;
        switch(player){
            case 0:
                //the absolute board is based on the view of player0
                convertedX = point.x;
                convertedY = point.y;
                break;
            case 1:
                //Clockwise or not, player1 and player3 switch their seats.
                convertedX = point.y;
                convertedY = -point.x;
                break;
            case 2:
                convertedX = - point.x;
                convertedY = - point.y;
                break;
            case 3:
                //Clockwise or not, player1 and player3 switch their seats.
                convertedX = -point.y;
                convertedY = point.x;
                break;
        }
        convertedX+=(boardWidth-1)/2;
        convertedY+=(boardHeight-1)/2;
        return new Point(convertedX, convertedY);
    }//end fourPlayerCoorFromAbsolute()

    public static Point twoPlayerCoorFromAbsolute(
        boolean clockwise, Point point, int player,
        int boardWidth, int boardHeight){

        int convertedX = 0;
        int convertedY = 0;
        switch(player){
            case 0:
                //the absolute board is based on the view of player0
                convertedX = point.x;
                convertedY = point.y;
                break;
            case 1:
                //Clockwise or not, player1 and player3 switch their seats.
                convertedX = Math.abs(point.x - (boardWidth - 1));
                convertedY = point.y - ((point.y - (boardHeight-6)) * 2 + 1);
                break;

        }
        return new Point(convertedX, convertedY);
    }//end fourPlayerCoorFromAbsolute()

    public static Point twoPlayerCoortoAbsolute(
        boolean clockwise, Point point, int player,
        int boardWidth, int boardHeight){

        int convertedX = 0;
        int convertedY = 0;
        switch(player){
            case 0:
                //the absolute board is based on the view of player0
                convertedX = point.x;
                convertedY = point.y;
                break;
            case 1:
                //Clockwise or not, player1 and player3 switch their seats.
                convertedX = Math.abs(point.x - (boardWidth - 1));
                convertedY = point.y - ((point.y - (boardHeight-6)) * 2 + 1);
                break;

        }
        return new Point(convertedX, convertedY);
    }//end fourPlayerCoorFromAbsolute()



}
