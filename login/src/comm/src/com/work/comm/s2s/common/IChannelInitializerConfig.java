package com.work.comm.s2s.common;

public interface IChannelInitializerConfig {
    // NETTY日志级别
    // 0 = TRACE
    // 1 = DEBUG
    // 2 = INFO
    // 3 = WARN
    // 4 = ERROR
    // 默认是：3
    int getLogLevel();

    // 闲读间隔时间，单位：秒
    // 达到时间间隔则发送心跳数据包
    int getReadIdleTimeSec();

    /** 拆包配置 */
    int getMaxFrameLength();
    int getLengthFieldLength();
    int getLengthFieldOffset();
    int getLengthAdjustment();
    int getInitialBytesToStrip();
}
