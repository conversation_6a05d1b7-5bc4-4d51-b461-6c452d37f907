package com.work.comm.io;

import com.work.comm.MaxConnectionPolicy;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.LengthFieldBasedFrameDecoder;
import io.netty.handler.codec.MessageToMessageEncoder;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpServerCodec;
import io.netty.handler.codec.http.websocketx.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class WSServerInitializer extends ChannelInitializer<SocketChannel> {

    private static final int MAX_FRAME_LENGTH = 1024 * 1024;
    private static final int LENGTH_FIELD_LENGTH = 2;
    private static final int LENGTH_FIELD_OFFSET = 18;
    private static final int LENGTH_ADJUSTMENT = -20;
    private static final int INITIAL_BYTES_TO_STRIP = 0;

    private MaxConnectionPolicy maxConnectionPolicy = null;

    public void setMaxConnectionPolicy(MaxConnectionPolicy maxConnectionPolicy) {
        this.maxConnectionPolicy = maxConnectionPolicy;
    }

    @Override
    public void initChannel(SocketChannel channel) throws Exception {
        if(null != this.maxConnectionPolicy && !maxConnectionPolicy.doPrivileged(channel))
            throw new RuntimeException("System reach the max connection!");

        ChannelPipeline pipeline = channel.pipeline();
        // Add the HTTP server codec to handle the HTTP handshake
        pipeline.addLast(new HttpServerCodec());
        pipeline.addLast(new HttpObjectAggregator(65536));
        // Add the WebSocket server and frame handler
        pipeline.addLast(new WebSocketServerProtocolHandler("/"));
        pipeline.addLast(new WebSocketResponseEncoder());
        pipeline.addLast(new WebSocketFrameDecoder());
        pipeline.addLast("decoder", new LengthFieldBasedFrameDecoder(MAX_FRAME_LENGTH, LENGTH_FIELD_OFFSET,
                LENGTH_FIELD_LENGTH, LENGTH_ADJUSTMENT, INITIAL_BYTES_TO_STRIP, true));
        pipeline.addLast("handler", new ServerHandler());
    }

    static class WebSocketFrameDecoder extends SimpleChannelInboundHandler<WebSocketFrame> {
        @Override
        protected void channelRead0(ChannelHandlerContext ctx, WebSocketFrame frame) {
            if (frame instanceof BinaryWebSocketFrame) {
                ByteBuf content = frame.content();
                ctx.fireChannelRead(content.retain());
            }
        }
    }

    static class WebSocketResponseEncoder extends MessageToMessageEncoder<ByteBuf> {
        @Override
        protected void encode(ChannelHandlerContext channelHandlerContext, ByteBuf byteBuf, List<Object> list) throws Exception {
            if (log.isDebugEnabled()) {
                if (byteBuf.capacity() > 0) {
                    log.debug("encoding response {}", bytesToHex(byteBuf));
                } else {
                    log.debug("response is empty");
                }
            }
            BinaryWebSocketFrame frame = new BinaryWebSocketFrame(Unpooled.copiedBuffer(byteBuf));
            list.add(frame);
        }

        private String bytesToHex(ByteBuf byteBuf) {
            byte[] buf = new byte[Math.min(byteBuf.capacity(), 256)];
            byteBuf.getBytes(0, buf, 0, buf.length);
            StringBuilder sb = new StringBuilder();
            sb.append(String.format("<ByteBuf[%d]", byteBuf.capacity()));
            for (byte b : buf) {
                sb.append(String.format(" %02x", b));
            }
            if (byteBuf.capacity() > 256) {
                sb.append(" ...");
            }
            sb.append(">");
            return sb.toString();
        }
    }
}