package com.work.comm.endpoints.core;

import com.work.comm.s2s.processor.impl.AbstractProcessorImpl;
import com.work.comm.s2s.protocal.Protocal;
import com.work.comm.s2s.protocal.S2PacckageUtil;
import com.work.comm.s2s.protocal.ServiceRequest;
import com.work.data.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>Title: UidAction</p> 
 * <p>Description: </p> 
 * <p>Copyright: Copyright (c) 2006</p> 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class UidAction extends AbstractProcessorImpl {
	/**
	 * 接收的数据包的处理方法
	 *
	 * @param req  请求对象，必填
	 *
	 * @return  返回的业务数据
	 *   null或byte[0] 表示不需要返回客户端
	 */
	@Override
	public byte[] handle(ServiceRequest req) {
		int[][] int2 = {
			       {128, Protocal.TYPE_INT_4_WAPPER_ARRAY},  // 在线用户ＩＤ数组
		}; 
		try {
			Map<Integer, Object> map = S2PacckageUtil.pickAll(req.getDataBuffer(), int2);
			Integer[] userIds = (Integer[]) map.get(128);
				
			Map<Integer, Object[]> uMap = new HashMap<Integer, Object[]>();
			for (int key : userIds) {
				if(Data.LOGIN_MAP.containsKey(key)) {
					uMap.put( key, Data.LOGIN_MAP.get(key) );
				}
			}
			if(uMap.size() > 0) {
				Data.LOGIN_MAP.clear();
				// 更新用户登录记录MAP
				Data.LOGIN_MAP.putAll(uMap);
				log.info("更新用户登录记录MAP成功・・・");
			}
		}catch (Exception e) {
			log.error("程序出现异常》", e);
		}
		return null;
	}
}

