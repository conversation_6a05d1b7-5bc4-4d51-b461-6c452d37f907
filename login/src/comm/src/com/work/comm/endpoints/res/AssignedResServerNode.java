package com.work.comm.endpoints.res;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class AssignedResServerNode {
    private String serverId;
    /** 客户端的访问地址 */
    // IP
    private String outAccessIp;
    // host
    private String outAccessHost;
    // port
    private int outAccessPort=0;

    /** 服务之间的访问地址 */
    // IP
    private String inAccessIp;
    // port
    private int inAccessPort=0;

    // APP到此Res的连接数
    private int clientConnNum;

    // 已分配玩家数量
    private int assignedNum;
}
