package com.work.comm;

import org.apache.logging.log4j.Logger;
import org.apache.jcs.JCS;
import org.apache.jcs.engine.control.CompositeCacheManager;
import org.apache.jcs.access.exception.CacheException;
import java.io.File;
import java.io.FileInputStream;
import java.util.Properties;

/**
 * Created by IntelliJ IDEA.
 * User: Administrator
 * To change this template use File | Settings | File Templates.
 */
public class CacheManager {

    private static Logger logger = com.work.comm.util.LogUtil.getLogger(CacheManager.class);

    private static JCS accountCache = null;
    private static JCS lastRequestCodeCache = null;
    public static JCS scoreCache = null;
    public static JCS moneyCache = null;
    public static JCS exchangeRateCache = null;


    static {
        try {
            Properties props = new Properties();
            props.load(new FileInputStream(new File("cache.ccf")));
            CompositeCacheManager ccm = CompositeCacheManager.getUnconfiguredInstance();
            ccm.configure(props);
            accountCache = JCS.getInstance("AccoutCache");


            lastRequestCodeCache = JCS.getInstance("LastRequestCodeCache");
            scoreCache = JCS.getInstance("scoreCache");
            moneyCache = JCS.getInstance("moneyCache");
            exchangeRateCache = JCS.getInstance("exchangeRateCache");

        }catch(Exception e) {
            logger.error("Init Java Cache Error:" , e);
        }
    }

    public static Object getCacheAccount(String playerID) {
        return accountCache.get(playerID);
    }

    public static Object putCacheAccount(String playerID,Object obj) {
        try {
            accountCache.put(playerID, obj);
        }catch(CacheException e) {
            return null;
        }
        return obj;
    }

    public static Object getCacheLastRequestCode(String playerID) {
        return lastRequestCodeCache.get(playerID);
    }

    public static Object putCacheLastRequestCode(String playerID,Object obj) {
        try {
            lastRequestCodeCache.put(playerID, obj);
        }catch(CacheException e) {
            return null;
        }
        return obj;
    }



    public CacheManager() {
    }
}

