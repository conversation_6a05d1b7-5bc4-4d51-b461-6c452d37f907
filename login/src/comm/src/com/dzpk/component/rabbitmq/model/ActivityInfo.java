package com.dzpk.component.rabbitmq.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by jayce on 2019/5/31
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityInfo {

    private Integer userId;

    private String ip;

    private String imei = "";

    private String phoneModel = "";

    private Integer simulator;

    private Integer gps;

    private Integer type;//1、计算资格 2、需要设定为无资格 3、已有资格需要通知弹窗

}
