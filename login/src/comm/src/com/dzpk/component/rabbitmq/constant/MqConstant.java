package com.dzpk.component.rabbitmq.constant;

public class MqConstant {

    public static String EXCHANGE_TYPE_DIRECT = "direct";  //直连方式
    public static String EXCHANGE_TYPE_TOPIC = "topic";  //主题方式

    //裂变赠送
    public static final String REBATE_KDOU_ACTIVITY_QUENE_NAME = "crazypoker.queue.kdou.activity";
    public static final String REBATE_KDOU_ACTIVITY_EXCHANGE = "crazypoker.direct.exchange.kdou.activity";
    public static final String REBATE_KDOU_ACTIVITY_ROUTEKEY = "crazypoker.route.kdou.activity";

    //在线玩家用户动作行为统计
    public static final String MQ_EVENTTRACK_QUENE_NAME = "crazypoker.queue.eventtrack";
    public static final String MQ_EVENTTRACK_EXCHANGE = "crazypoker.direct.exchange.eventtrack";
    public static final String MQ_EVENTTRACK_ROUTEKEY = "crazypoker.route.eventtrack";
}
