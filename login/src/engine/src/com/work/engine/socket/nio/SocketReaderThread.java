package com.work.engine.socket.nio;

import org.apache.logging.log4j.Logger;

import java.util.*;
import java.nio.channels.Selector;
import java.nio.channels.SelectionKey;
import java.nio.channels.SocketChannel;
import java.nio.ByteBuffer;
import java.io.IOException;

import com.work.engine.socket.Client;
import com.work.engine.socket.Server;
import com.work.engine.socket.SocketObserver;
import com.work.engine.server.ObjectFactory;
import com.work.engine.server.handler.RequestHandler;
import com.work.engine.server.client.ClientPool;
import com.work.engine.protocal.Protocal;
import com.work.engine.protocal.request.Request;
import EDU.oswego.cs.dl.util.concurrent.PooledExecutor;
import EDU.oswego.cs.dl.util.concurrent.BoundedBuffer;

/**
 * Created by IntelliJ IDEA.
 * User: Administrator
 * To change this template use File | Settings | File Templates.
 */
public class SocketReaderThread extends Observable implements Runnable {

    private final static Logger logger = com.work.comm.util.LogUtil.getLogger(SocketReaderThread.class);

    // application layer fields
    private ClientPool clientPool;
    private ObjectFactory factory;
    //最大包长度不能超过8KB
    public final static int MAX_CP_SIZE = 1024 * 8;
    // thread pool use the package from Oswego Univ
    private static PooledExecutor pool;

    /**
     * 每一次收包时间不能超过这个
     */
    public final static int BUY_MAX_PACK_TIEM = 4 * 1000;
    
    // selector for SocketChannel that will be passed in
    public static Selector readerSelector;

    private static int InitThreads;

    private Hashtable packageTable = new Hashtable();

    /**
     * 初始化线程的参数
     *
     * @param readSelector
     */
    public SocketReaderThread(Server server, Selector readSelector, int initThreads) {

        this.clientPool = server.getClientPool();
        this.factory = server.getFactory();
        this.readerSelector = readSelector;
        InitThreads = initThreads;

        pool = new PooledExecutor(new BoundedBuffer(100), 5000);
        pool.setMinimumPoolSize(300);
        pool.setKeepAliveTime(-1);
        pool.discardOldestWhenBlocked();
        if (initThreads <= 0) initThreads = 100;
        pool.createThreads(10);
        pool.setKeepAliveTime(20 * 1000); // 每个现程只允许活20秒

        // 将SocketListener设定为观察者
        addObserver(new SocketObserver(server.getClientPool(), server.getFactory()));
    }

    public void run() {
        String osName = System.getProperty("os.name");
        boolean ifWindows = false;
        if (osName.indexOf("Windows") != -1) {
            ifWindows = true;
        }

        int times = 0;

        while (true) { //循环监听
            try {

                if (ifWindows) {
                    if ((readerSelector.select(1)) > 0) {
                        acceptPendingRequests();
                    }
                    Thread.sleep(10);
                } else {
                    if ((readerSelector.selectNow()) > 0) {
                        acceptPendingRequests();
                    }
                    Thread.sleep(1);
                }
//                Thread.sleep(10);
                times++;

                if (times > 100 * 1000) {
                    times = 0;
                    // 删除 packageTable中的超时的包
                    Enumeration enumTables = packageTable.keys();
                    long thisTime = new Date().getTime();
                    while (enumTables.hasMoreElements()) {
                        Object key = enumTables.nextElement();
                        PackageModel packageModel = (PackageModel) packageTable.get(key);
                        if (packageModel != null) {
                            if (thisTime - packageModel.getReadTime() > 30 * 1000) {
                                packageTable.remove(key);
                            }
                        }
                    }

                }

            } catch (Exception e) {
                logger.error("readerSelector.select error:", e);
                e.printStackTrace();  //To change body of catch statement use File | Settings | File Templates.

            }
        }

    }

    /**
     * 将selector传来的key遍历处理一次
     */
    protected void acceptPendingRequests() {
        Set readyKeys = readerSelector.selectedKeys();

        for (Iterator i = readyKeys.iterator(); i.hasNext();) {
            SelectionKey key = (SelectionKey) i.next();
            i.remove();
            if (key.isValid()) {
                readData(key);
            }
        }
        readerSelector.selectedKeys().remove(readyKeys);
    }

    protected void readData(SelectionKey key) {
        SocketChannel incomingChannel = (SocketChannel) key.channel();

        ByteBuffer readBuffer = ByteBuffer.allocate(MAX_CP_SIZE);
        try {
            int bytesRead = -2;

            if (!key.isReadable()) {
                return;
            }

            while ((bytesRead = incomingChannel.read(readBuffer)) >= 0) {
            	
            	//收包处理 对应长包这里做
            	byte[] vBt = new byte[bytesRead];
            	System.arraycopy(readBuffer.array(), 0, vBt, 0, bytesRead);
            	//包长度
            	int packLong = Request.getPackageSize(vBt);
            	logger.debug("================pack long is: " + packLong);
            	//超过长度直接丢包
            	if (packLong > MAX_CP_SIZE) {
                    //  清除掉此Key在packageTable中 的数据
                    packageTable.remove(key);
                    break;
            	}
            	if (packLong > 1459 ) {
                	long timeold = System.currentTimeMillis();
                	int i = 0;
                	while (i == 0) {
                		incomingChannel.read(readBuffer);
                		bytesRead = readBuffer.position();
                		if (bytesRead >= packLong) {
                			break;
                		}
                		//每次收包时间不能超过 4秒时间
                		if (System.currentTimeMillis() - timeold > BUY_MAX_PACK_TIEM) {
                			packageTable.remove(key);
                			break;
                		}
    				}
            	}
            	bytesRead = readBuffer.position();
                logger.debug("bytesRead=" + bytesRead);
                if (bytesRead == 0) {
                    break;
                }
                byte[] readBt;
                PackageModel packageModel = (PackageModel) packageTable.get(key);
                if (packageModel != null) {
                    long lastPackageReadTime = packageModel.getReadTime();
                    long thisTime = new Date().getTime();
                    byte[] lastPackage = packageModel.getBt();
                    //  合包,先做包头判断和时间判断，如果不是带包头的包则直接扔掉
                    if (thisTime - lastPackageReadTime > 5 * 1000 || lastPackage[0] != Protocal.HEADER_INDICATER_0 || lastPackage[1] != Protocal.HEADER_INDICATER_1
                            || lastPackage[2] != Protocal.HEADER_INDICATER_2 || lastPackage[3] != Protocal.HEADER_INDICATER_3)
                    {
                        packageTable.remove(key);
                        readBt = new byte[bytesRead];
                        System.arraycopy(readBuffer.array(), 0, readBt, 0, bytesRead);
                    } else {
                        readBt = new byte[lastPackage.length + bytesRead];
                        System.arraycopy(lastPackage, 0, readBt, 0, lastPackage.length);
                        System.arraycopy(readBuffer.array(), 0, readBt, lastPackage.length, bytesRead);
                        logger.debug("合包，合包之后的包长 = " + readBt.length);
                    }

                } else {
                    readBt = new byte[bytesRead];
                    System.arraycopy(readBuffer.array(), 0, readBt, 0, bytesRead);
                }

                int packSize = Request.getPackageSize(readBt);
                
                if (packSize > MAX_CP_SIZE * 2) {
                	break;
                }
                
                if (packSize == -1) {
                    //  清除掉此Key在packageTable中 的数据
                    packageTable.remove(key);
                    break;
                }

                Client client = clientPool.getClientBySocket(incomingChannel.socket());
                client.setUpdateTime(System.currentTimeMillis());

                int readySize = readBt.length;
                if (readySize < packSize) {
                    // todo 放到packageTable中
                    long thisTime = new Date().getTime();
                    packageModel = new PackageModel();
                    packageModel.setBt(readBt);
                    packageModel.setReadTime(thisTime);
                    packageTable.remove(key);
                    packageTable.put(key, packageModel);
                    break;
                } else if (readySize > packSize) {

                    byte[] packageBt = new byte[packSize];
                    System.arraycopy(readBt, 0, packageBt, 0, packSize);
                    byte[] packageBbt = new byte[readySize - packSize];
                    System.arraycopy(readBt, packSize, packageBbt, 0, packageBbt.length);
                    if (packageBbt[0] != Protocal.HEADER_INDICATER_0 && packageBbt[1] != Protocal.HEADER_INDICATER_1
                            && packageBbt[2] != Protocal.HEADER_INDICATER_2 && packageBbt[3] != Protocal.HEADER_INDICATER_3)
                    {
                        long thisTime = new Date().getTime();
                        packageModel = new PackageModel();
                        packageModel.setBt(packageBbt);
                        packageModel.setReadTime(thisTime);
                        packageTable.remove(key);
                        packageTable.put(key, packageModel);

                    }
                    ByteBuffer resultBuffer = ByteBuffer.wrap(packageBt);
                    handler(client, incomingChannel, resultBuffer);
                    break;

                } else {
                    // todo 清空 此Key在packageTable中 的数据

                    packageTable.remove(key);

                    readBuffer.flip();
                    byte[] bt = new byte[packSize];
                    System.arraycopy(readBuffer.array(), 0, bt, 0, packSize);
                    ByteBuffer resultBuffer = ByteBuffer.wrap(bt);

                    handler(client, incomingChannel, resultBuffer);
                    break;
                }

            }

            if (bytesRead == -1) {
                logger.debug("-1 readed, closing appserversocket cause SocketListener to throw a SocketClosedEvent to mecoengine");
                packageTable.remove(key);
                setChanged();
                notifyObservers(incomingChannel.socket());
                incomingChannel.keyFor(readerSelector).cancel();
                incomingChannel.close();
            }

        } catch (IOException ex) {
            logger.debug("remote host has closed the connection. " + ex);
            packageTable.remove(key);
            setChanged();
            notifyObservers(incomingChannel.socket());
            try {
                incomingChannel.keyFor(readerSelector).cancel();
                incomingChannel.close();
            } catch (IOException e) {
                e.printStackTrace();  //To change body of catch statement use File | Settings | File Templates.
            }

        }
    }

    /**
     * the resultBuffer.position should be zero
     *
     * @param client
     * @param incomingChannel
     * @param resultBuffer
     */
    private boolean handler(Client client, SocketChannel incomingChannel, ByteBuffer resultBuffer) {
        logger.debug("resultBuffer=" + resultBuffer);

        try {
            Request request = factory.getRequest();

            if (!request.init(client, resultBuffer)) {
                logger.debug("get an invalid data, size=" + resultBuffer.limit());
                setChanged();
                notifyObservers(incomingChannel.socket());
                incomingChannel.keyFor(readerSelector).cancel();
                incomingChannel.close();
                return false;
            }
            logger.debug("RequestHandler, start handler"); 
            RequestHandler handler = factory.getRequestHandler();
            handler.init(request);
            logger.debug("RequestHandler, in handler" + handler);
            pool.execute(handler);
            logger.debug("pool size: " + pool.getPoolSize());
        } catch (Exception ex) {
            logger.debug("error occupy, close client", ex);
            setChanged();
            notifyObservers(incomingChannel.socket());
            incomingChannel.keyFor(readerSelector).cancel();
            try {
                incomingChannel.close();
            } catch (IOException e) {
                e.printStackTrace();  //To change body of catch statement use File | Settings | File Templates.
            }
            return false;
        }

        return true;
    }

    class PackageModel {

        private byte[] bt;
        private long readTime;

        public byte[] getBt() {
            return bt;
        }

        public void setBt(byte[] bt) {
            this.bt = bt;
        }

        public long getReadTime() {
            return readTime;
        }

        public void setReadTime(long readTime) {
            this.readTime = readTime;
        }

    }
}
