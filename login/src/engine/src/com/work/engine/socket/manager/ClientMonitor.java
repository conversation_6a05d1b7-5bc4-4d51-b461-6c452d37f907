package com.work.engine.socket.manager;

import org.apache.logging.log4j.Logger;

import java.util.concurrent.ConcurrentMap;
import java.util.Iterator;
import com.work.engine.server.ObjectFactory;
import com.work.engine.server.handler.EventHandler;
import com.work.engine.server.client.ClientPool;
import com.work.engine.socket.Client;

/**
 * Created by IntelliJ IDEA.
 * User: Administrator
 * To change this template use File | Settings | File Templates.
 */
public class ClientMonitor extends Thread {

    public static Logger logger = com.work.comm.util.LogUtil.getLogger(ClientMonitor.class);

    private ClientPool clientPool;
    private ObjectFactory factory;

    private int runInterval = 1000*60;
    private int socketTimeOut = 1000*60*5;

    public ClientMonitor(ClientPool clientPool, ObjectFactory handlerFactory){
        this.factory = handlerFactory;
        this.clientPool = clientPool;
    }

    public void run() {
        while (true) {
            try {
                Thread.sleep(runInterval);
                checkClientState();
            } catch (Exception ex) {
                logger.error("ClientMonitor error:" + ex);
            }
        }
    }

    /**
     * 逐个检查用户的更新时间。
     *
     */
    private void checkClientState() {
        if( socketTimeOut < 0){
            return;
        }

        long currentTime = System.currentTimeMillis();

        ConcurrentMap clients = clientPool.getClients();
        Iterator iter = clients.values().iterator();
        while(iter.hasNext()) {
            Client client = (Client)iter.next();

            long lasttime = client.getUpdateTime();
            if (currentTime - lasttime >= socketTimeOut) {
                clientTimeout(client);
            }
        }
    }
    private void clientTimeout(Client client){
        //清除应用系统：游戏的状态。
        ClientTimeoutEvent event = new ClientTimeoutEvent();
        event.init(client);
        logger.debug("event=" + event);

        EventHandler handler = (EventHandler)factory.getEventHandler();
        handler.init();
        handler.handle(event);

        logger.debug("remove client");
        clientPool.removeClient(client);
    }



}
