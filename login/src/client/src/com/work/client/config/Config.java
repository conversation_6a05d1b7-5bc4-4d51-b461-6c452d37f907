/*
 * $RCSfile: Config.java,v $
 * $Revision: 1.1  $
 * $Date: 2012-8-8  $
 *
 * Copyright (C) 2005 Bettem, Inc. All rights reserved.
 *
 * This software is the proprietary information of Bettem, Inc.
 * Use is subject to license terms.
 */
package com.work.client.config;

import java.util.HashMap;
import java.util.Map;

import com.work.client.action.CoreAction;
import com.work.client.action.UidAction;
import com.work.data.Data;

/**
 * <p>Title: Config</p> 
 * <p>Description: </p> 
 * <p>Copyright: Copyright (c) 2006</p> 
 * <AUTHOR>
 * @version 1.0
 */
public class Config {
	/**
	 * 核心服务器IP Port
	 */
	public static Object[] CONNECT_CORE_SERVER = { 
		Data.PRO.getProperty("coreServer.ip"), 
		Integer.parseInt( Data.PRO.getProperty("coreServer.port") )
	};
	
	//客户端协议头相关信息
	public final static byte PROTOCOL_VERSION = 0;
	public final static byte LANGUAGE_ID = 1;
	public final static byte MAIN_VERSION = 1;
	public final static byte MINOR_VERSION = 1;
	public final static byte CLIENT_BUILD_LANGUAG_ID = 1;
	public final static byte CLIENT_BUILD_NUMBER = 1;
	public final static int SERVER_NUMBER = Integer.valueOf(Data.PRO.getProperty("service.name"));
	public final static int PRODUCT_ID = 1; //1 loginService 2 roomService 3 resServer 4coreServer

	
	/**
	 * 控制调整配置
	 * KEY :requestCode
	 * VALUE:实现类的class对象
	 */
	public static Map<Integer, Class<?>> eventMapping = new HashMap<Integer, Class<?>>();
	
	static {
		eventMapping.put(86, CoreAction.class);
		eventMapping.put(90, UidAction.class);
	}
}

