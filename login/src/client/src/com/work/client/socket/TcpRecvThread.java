/*
 * $RCSfile: TcpRecvThread.java,v $
 * $Revision: 1.1  $
 * $Date: 2012-8-8  $
 *
 * Copyright (C) 2005 Bettem, Inc. All rights reserved.
 *
 * This software is the proprietary information of Bettem, Inc.
 * Use is subject to license terms.
 */
package com.work.client.socket;

import java.io.DataInputStream;
import java.io.IOException;
import java.nio.ByteBuffer;

import org.apache.logging.log4j.Logger;

import com.work.client.ClientAction;
import com.work.client.config.Config;
import com.work.client.config.Constant;
import com.work.client.protocal.Protocal;

/**
 * 建立SOCKET连接后启动该线程处理从服务器端发回的byte流
 * <p>Title: TcpRecvThread</p> 
 * <p>Description: </p> 
 * <p>Copyright: Copyright (c) 2006</p> 
 * <AUTHOR>
 * @version 1.0
 */
public class TcpRecvThread extends Thread{
	private static final Logger logger = com.work.comm.util.LogUtil.getLogger(TcpRecvThread.class);
	final byte PackageFirstHeadTag[] = {Constant.HEADER_INDICATER_0,Constant.HEADER_INDICATER_1,Constant.HEADER_INDICATER_2,Constant.HEADER_INDICATER_3};
	private int sleepTime = 10;
	//return value
	private DataInputStream dataInput;
	private Registration registration;
	private int ret = 0;
	private int ret2 = 0;
	private static int MAX_PACK_SIZE = 8 * 1024 * 2; //包最大容量
	protected TcpRecvThread(DataInputStream dataInput ,Registration registration) {
		this.dataInput = dataInput;
		this.registration = registration;
	}
	@SuppressWarnings("unused")
	private TcpRecvThread() {}
	
	protected void stop_() {
		ret  =  -999;
		ret2 = -999;
		try {
			//线程生命周期已经结束
			this.interrupt();
			if (dataInput != null) {
				dataInput.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("中断线程出错！", e);
		}
	}
	
	public void run() {
		
		while(ret == 0){
			try {
				if (ret2 != 0) {
					break;
				}
				RecvByteArray();
			} catch (Exception e) {
				registration.close();
			}
			
			try {
				sleep(sleepTime);
			} catch (InterruptedException e) {
			    logger.debug("ret: " + ret + ", ret2: " + ret2);
				logger.error("", e);
				e.printStackTrace();
			}
		}
		logger.debug("close run thread!");
		try {
			if (dataInput!=null) {
				dataInput.close();
			}
		} catch (IOException e) {
			logger.error("", e);
			e.printStackTrace();
		}
		dataInput = null;
	}
	private boolean checkFirstPackageHeadTag(byte []temp){
		if(temp[0] != PackageFirstHeadTag[0])return false;
		if(temp[1] != PackageFirstHeadTag[1])return false;
		if(temp[2] != PackageFirstHeadTag[2])return false;
		if(temp[3] != PackageFirstHeadTag[3])return false;
		return true;
	}
	private int getPackageSize(byte []temp){
		int size = ((int)(temp[Protocal.SRP_SIZE_HIGH]&0xff) <<8) | (int)(temp[Protocal.SRP_SIZE_LOW]&0xff);
		return size;
	}
	private void RecvByteArray(){
		while (ret2 == 0) {
			// 读取包
			try {
				byte btt[] = new byte[MAX_PACK_SIZE];

				int bytesRead = dataInput.read(btt);
				if (bytesRead == -1) {
					logger.debug("check the server has closed");
					ret2 = -999;
					ret = -999;
					continue;
				}
				int packSize = getPackageSize(btt);
				//说明包没读完
				long b = 4000;//每次最多读4S
				int i = 0;
				long t = System.currentTimeMillis();
				if (bytesRead < packSize) {
					while (i == 0) {
						bytesRead = dataInput.read(btt) + bytesRead;
						if (bytesRead >= packSize) {
							break;
						}
						if (System.currentTimeMillis() - t > b) {
							break;
						}
					}
				}
				logger.debug("read data.size=" + bytesRead);

				byte[] bt = new byte[getPackageSize(btt)];
				System.arraycopy(btt, 0, bt, 0, bt.length);
				ByteBuffer resultBuffer = ByteBuffer.wrap(bt);
				if (!checkFirstPackageHeadTag(bt)) {
					ret = -999;
					ret2 = -999;
					break;
				}
				action(resultBuffer);
			} catch (Exception d) {
				 logger.error(d);
				 ret2 = -999;
				 ret = -999;
				 break;
			}
		}
	}
	
	private void action(ByteBuffer resultBuffer) {
		ClientRequest request = new ClientRequest();
		request.init(resultBuffer.array());
		request.setRegistration(registration);
		ClientResponse response_ = new ClientResponse();
		//默认返回为一样的协议
		response_.setRequestCode(request.getRequestCode());
		try {
			//根据request code 获取对应处理逻辑
			ClientAction clientAction = (ClientAction)Config.eventMapping.get(request.getRequestCode()).newInstance();
			ClientResponse response = clientAction.action(request , response_);
			//是否需要返回信息
			if (response != null && response.getBytes() != null && response.getRequestCode() != 0) {
				registration.publishByte(response.getBytes());
			}
			if (registration.getCONNECT_STATUS()  != 0 || (response != null && response.isClose())) {
				registration.close();
			}
		} catch (InstantiationException e) {
			e.printStackTrace();
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		}
		
	}
	
}

