/*
 * $RCSfile: DZPKSource.java,v $
 * $Revision: 1.1  $
 * $Date: 2012-8-10  $
 *
 * Copyright (C) 2005 Bettem, Inc. All rights reserved.
 *
 * This software is the proprietary information of Bettem, Inc.
 * Use is subject to license terms.
 */

package com.work.model;
/**
 * <p>Title: DZPKSource</p> 
 * <p>Description: </p> 
 * <p>Copyright: Copyright (c) 2006</p> 
 * <AUTHOR>
 * @version 1.0
 */
public class DZPKSource {
	
	private int serverID; //服务器编号
	private int prot; // 端口号
	private int serProt; // 内部通讯端口
	private String address; // IP地址
	private String cdnAddress;// IP地址（cdn）
	private int connect; // 连接数
	private String serAddress; // 内部通信IP
	
	
	public int getServerID() {
		return serverID;
	}
	public void setServerID(int serverID) {
		this.serverID = serverID;
	}
	public int getProt() {
		return prot;
	}
	public void setProt(int prot) {
		this.prot = prot;
	}
	public int getSerProt() {
		return serProt;
	}
	public void setSerProt(int serProt) {
		this.serProt = serProt;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public int getConnect() {
		return connect;
	}
	public void setConnect(int connect) {
		this.connect = connect;
	}
	public String getSerAddress() {
		return serAddress;
	}
	public void setSerAddress(String serAddress) {
		this.serAddress = serAddress;
	}

	public String getCdnAddress() {
		return cdnAddress;
	}

	public void setCdnAddress(String cdnAddress) {
		this.cdnAddress = cdnAddress;
	}
}
