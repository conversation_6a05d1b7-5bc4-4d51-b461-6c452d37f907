package com.work.timer;

import java.util.Timer;
import java.util.TimerTask;

import com.work.comm.endpoints.core.CoreServerManager;
import com.work.comm.s2s.protocal.S2PacckageUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 定时向核心服务器请求资源服务器数据任务
 * <AUTHOR>
 *
 */
@Slf4j
public class InstanceData{
	private final static int HOUR = 1000 * 60;
	
	/**
	 * 开启任务
	 */
	public static void start() {
		/*Timer tim = new Timer();
		TimerTask tt = new TimerTask() {
			public void run() {
				try {
					//向核心服务器请求资源服务器数据
					// Registration reg = Registration.getShortReg(Config.CONNECT_CORE_SERVER);
					byte[] bytes = S2PacckageUtil.packAll(new Object[][]{}, 86);
					CoreServerManager.getInstance().sendData(null,bytes);
					log.info("向Core服务请求Res服务数据......");
					// reg.publishByte(bytes);
				}catch (Exception ex) {
					log.error("向Core服务请求Res服务数据失败: "+ex.getMessage(), ex);
				}
			}
		};
		tim.schedule(tt,1000 * 1,HOUR);*/
	}
}
