package com.work.timer;

import java.util.Timer;
import java.util.TimerTask;

import org.apache.logging.log4j.Logger;

import com.work.util.Tools;
/**
 * 定时加载服务器配置文件任务
 * <AUTHOR>
 *
 */
public class InstanceConfig {
	private static Logger logger = com.work.comm.util.LogUtil.getLogger(InstanceConfig.class);
	private static int HOUR = 1000*60*15;
	
	/**
	 * 开启任务
	 */
	public static void start() {
		Timer tim = new Timer();
		TimerTask tt = new TimerTask() {
			public void run() {
				try {
					Tools.setConfig();
					Tools.setSqlMap();
					Tools.setVersionConfig();
				}catch (Exception ex) {
					logger.error("加载服务器配置文件出错!", ex);
				}
			}
		};
		tim.schedule(tt,1000,HOUR);
	}
}
