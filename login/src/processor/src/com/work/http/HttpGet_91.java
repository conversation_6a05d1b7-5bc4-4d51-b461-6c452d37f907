package com.work.http;

import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

import org.apache.logging.log4j.Logger;

import com.work.data.Data;
import com.work.util.Tools;

import net.sf.json.JSONObject;
/**
 * 91登录sessionId有效性验证
 * <AUTHOR>
 *
 */
public class HttpGet_91 {
	private static Logger logger = com.work.comm.util.LogUtil.getLogger(HttpGet_91.class);
	
	 public static int doGet(String uin, String sid) {
    	int result = 0;
    	HttpURLConnection httpURLConn = null;
        try {
        	String str = Data.CONFIG.get("jy_http").toString();
        	URL url = new  URL( str + "&Uin=" + uin + "&SessionId=" + sid + "&Sign=" + 
        			Tools.md5( "105378" + 4 + uin + sid + "e7cbac24d2fcfde82a4ebaa645eb0d7cd77cdb88bd5a02b4" ) 
        	); 
        	httpURLConn = (HttpURLConnection)url.openConnection();
            httpURLConn.setDoOutput(true);
            httpURLConn.setRequestMethod("GET");
            httpURLConn.setIfModifiedSince(999999999);
            httpURLConn.setConnectTimeout(10 * 1000);
            httpURLConn.connect();
            InputStream in = httpURLConn.getInputStream();
            StringBuffer sb = new StringBuffer();
            int n = 0;
            // 读取数据
            int length = in.available();
            while(n < length) {
            	byte[] bytes = new byte[Tools.SIZE];
            	int count = in.read(bytes, n, Tools.SIZE);
            	n += Tools.SIZE;
            	sb.append( new String(bytes, 0, count, "utf-8") );
            } 
            in.close();
            // ErrorCode = 1 成功验证
            // 根据得到的序列化对象 构建JSON对象
            JSONObject injson = JSONObject.fromObject(sb.toString());
            result = Integer.valueOf( injson.get("ErrorCode").toString() );   
        } catch (Exception e) {
        	logger.error("91登录sessionId有效性验证出错", e);
        } finally {
            if(httpURLConn != null) {
                httpURLConn.disconnect();
            }
        }
        return result;
    }
}
