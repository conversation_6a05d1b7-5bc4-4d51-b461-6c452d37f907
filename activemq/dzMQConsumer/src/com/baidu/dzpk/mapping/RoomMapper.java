package com.baidu.dzpk.mapping;

import org.apache.ibatis.annotations.Select;
import com.baidu.dzpk.model.Room;


public interface RoomMapper {

    // 根据房间号查询自建房间数据
    @Select("select room_id as roomId, control, name, qianzhu, insurance, pause, sb_chip as smallBlind, "
            + "in_chip as minChip, charge, max_play_time as maxPlayTime, player_count as playerCount, "
            + "status as progress, creator, create_time as createTime, start_time as startTime, "
            + "min_rate as minRate, max_rate as maxRate, idou as advanceCharge, source as sourceType, "
            + "source_id as sourceId, min_play_time as minPlayTime, tribe_id as tribeId "
            + "from group_room where status!=0 and room_id=#{roomId}")
    public Room findRoom(int roomId);
    
    
    // 根据房间号查询sng房间数据
    @Select("select type as sourceType, player_count as playerCount, creator, source_id as sourceId "
            + "from sng_room where status!=0 and room_id=#{roomId}")
    public Room findSngRoom(int roomId);
}
