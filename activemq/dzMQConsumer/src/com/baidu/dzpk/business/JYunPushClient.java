package com.baidu.dzpk.business;

import org.apache.logging.log4j.Logger;

import cn.jiguang.common.resp.APIConnectionException;
import cn.jiguang.common.resp.APIRequestException;
import cn.jpush.api.JPushClient;
import cn.jpush.api.push.PushResult;
import cn.jpush.api.push.model.Options;
import cn.jpush.api.push.model.Platform;
import cn.jpush.api.push.model.PushPayload;
import cn.jpush.api.push.model.audience.Audience;
import cn.jpush.api.push.model.notification.IosNotification;
import cn.jpush.api.push.model.notification.Notification;

/**
 * 极光推送客户端
 * 
 * <AUTHOR>
 * @date 2017年7月11日
 *
 */
public class JYunPushClient {

    private static String APP_KEY = "c771e942f134ac73a769b4b8";// 应用的key，两端通用
    private static String MASTER_SECRET = "8070db23a32580bb422018ce";// 应用的SECRET

    private static final String APPTITLE = "王者德扑"; // app名

    private static final Logger logger = com.work.comm.util.LogUtil.getLogger(JYunPushClient.class);

    private static JPushClient jpushClient = new JPushClient(MASTER_SECRET, APP_KEY);// 极光推送客户端实例

    /**
     * 推送单体设备(registrationID的方式)
     * 
     * @param deviceType 设备类型 0是android设备 1是ios设备
     * @param contentMsg 通知消息体
     * @param registrationID 唯一设备标识符
     */
    public static void useRegistrationIDPushSingleDevice(int deviceType, String contentMsg, String...registrationID) {
        if (registrationID == null || registrationID.equals("")) {
            logger.debug("registrationID is null or no char");
            return;
        }
        if (deviceType == 1) {
            useRegistrationIDPushToSingleIosDevice(contentMsg, registrationID);
        } else {
            useRegistrationIDPushToSingleAndroidDevice(contentMsg, registrationID);
        }
    }

    /**
     * 推送单体设备(registrationID的方式)
     * 
     * @param deviceType 设备类型 0是android设备 1是ios设备
     * @param contentMsg 通知消息体
     * @param registrationID 唯一设备标识符
     */
    public static void useRegistrationIDPushSingleDevice(String soundFileName, int deviceType, String contentMsg,
            String...registrationID) {
        if (registrationID == null || registrationID.equals("")) {
            logger.debug("registrationID is null or no char");
            return;
        }
        if (deviceType == 1) {
            useRegistrationIDPushToSingleIosDevice(contentMsg, soundFileName, registrationID);
        } else {
            useRegistrationIDPushToSingleAndroidDevice(contentMsg, registrationID);
        }
    }

    /**
     * 推送单体设备(alias的方式)
     * 
     * @param deviceType 设备类型 0是android设备 1是ios设备
     * @param contentMsg 通知消息体
     * @param alias 设备别名(可多个绑定同个)
     */
    public static void useAliasPushSingleDevice(int deviceType, String contentMsg, String...alias) {
        if (alias == null || alias.equals("")) {
            logger.debug("alias is null or no char");
            return;
        }
        if (deviceType == 1) {
            useAliasPushToSingleIosDevice(contentMsg, alias);
        } else {
            useAliasPushToSingleAndroidDevice(contentMsg, alias);
        }
    }

    /**
     * android单推带通知栏消息(registrationID)
     * 
     * @param contentMsg 推送内容
     * @param registrationID 唯一标识符
     */
    public static void useRegistrationIDPushToSingleAndroidDevice(String contentMsg, String...registrationID) {
        if (jpushClient == null) {
            jpushClient = new JPushClient(MASTER_SECRET, APP_KEY);
        }
        if (registrationID == null || registrationID.equals("")) {
            logger.debug("registrationID is null or no char");
            return;
        }
        try {
            PushResult result =
                    jpushClient.sendAndroidNotificationWithRegistrationID(APPTITLE, contentMsg, null, registrationID);
            logger.debug("Got result - " + result);
        } catch (APIConnectionException e) {
            e.printStackTrace();
            logger.debug("jpush APIConnectionException");
        } catch (APIRequestException e) {
            e.printStackTrace();
            logger.debug("jpush APIRequestException");
        } finally {
            logger.debug("jpush close");
            jpushClient.close();
        }
    }

    /**
     * Android单推带通知栏消息(alias)
     * 
     * @param contentMsg 推送内容
     * @param alias 设备别名(可多个绑定同个)
     */
    public static void useAliasPushToSingleAndroidDevice(String contentMsg, String...alias) {
        if (jpushClient == null) {
            jpushClient = new JPushClient(MASTER_SECRET, APP_KEY);
        }
        if (alias == null || alias.equals("")) {
            logger.debug("alias is null or no char");
            return;
        }
        try {
            PushResult result = jpushClient.sendAndroidNotificationWithAlias(APPTITLE, contentMsg, null, alias);
            logger.debug("Got result - " + result);
        } catch (APIConnectionException e) {
            e.printStackTrace();
        } catch (APIRequestException e) {
            e.printStackTrace();
        } finally {
            jpushClient.close();
        }
    }

    /**
     * Ios单推带通知栏消息(registrationID)带默认声音
     * 
     * @param contentMsg 推送内容
     * @param registrationID 唯一标识符
     */
    public static void useRegistrationIDPushToSingleIosDevice(String contentMsg, String...registrationID) {
        if (jpushClient == null) {
            jpushClient = new JPushClient(MASTER_SECRET, APP_KEY);
        }
        if (registrationID == null || registrationID.equals("")) {
            logger.debug("registrationID is null or no char");
            return;
        }
        try {
            Notification notification = Notification.newBuilder()
                    .addPlatformNotification(IosNotification.newBuilder().setSound("sound.caf").setBadge(1)
                            .setAlert(contentMsg)
                            .build())
                    .build();
                    // PushPayload payloadToTest = PushPayload.newBuilder()
                    // .setPlatform(Platform.ios())
                    // .setAudience(Audience.registrationId(registrationID))
                    // .setNotification(notification)
                    // .build();

            // PushResult testResult = jpushClient.sendPush(payloadToTest);
            // logger.debug("Got Testresult - " + testResult);
            Options options = Options.newBuilder().build();
            options.setApnsProduction(true);
            PushPayload payloadToOnLine = PushPayload.newBuilder()
                    .setPlatform(Platform.ios())
                    .setAudience(Audience.registrationId(registrationID))
                    .setNotification(notification)
                    .setOptions(options)
                    .build();
            PushResult onlineResult = jpushClient.sendPush(payloadToOnLine);
            logger.debug("Got onlineResult - " + onlineResult);
        } catch (APIConnectionException e) {
            e.printStackTrace();
            logger.debug("jpush APIConnectionException");
        } catch (APIRequestException e) {
            e.printStackTrace();
            logger.debug("jpush APIRequestException");
        } finally {
            jpushClient.close();
        }
    }

    /**
     * Ios单推带通知栏消息(registrationID)带特定声音
     * 
     * @param contentMsg 推送内容
     * @param extras 声音文件名
     * @param registrationID 唯一标识符
     */
    public static void useRegistrationIDPushToSingleIosDevice(String contentMsg, String extras,
            String...registrationID) {
        if (jpushClient == null) {
            jpushClient = new JPushClient(MASTER_SECRET, APP_KEY);
        }
        if (registrationID == null || registrationID.equals("")) {
            logger.debug("registrationID is null or no char");
            return;
        }
        try {
            Notification notification = Notification.newBuilder()
                    .addPlatformNotification(IosNotification.newBuilder().setSound(extras).setBadge(1)
                            .setAlert(contentMsg)
                            .build())
                    .build();
                    // PushPayload payloadToTest = PushPayload.newBuilder()
                    // .setPlatform(Platform.ios())
                    // .setAudience(Audience.registrationId(registrationID))
                    // .setNotification(notification)
                    // .build();

            // PushResult testResult = jpushClient.sendPush(payloadToTest);
            // logger.debug("Got Testresult - " + testResult);
            Options options = Options.newBuilder().build();
            options.setApnsProduction(true);
            PushPayload payloadToOnLine = PushPayload.newBuilder()
                    .setPlatform(Platform.ios())
                    .setAudience(Audience.registrationId(registrationID))
                    .setNotification(notification)
                    .setOptions(options)
                    .build();
            PushResult onlineResult = jpushClient.sendPush(payloadToOnLine);
            logger.debug("Got onlineResult - " + onlineResult);
        } catch (APIConnectionException e) {
            e.printStackTrace();
            logger.debug("jpush APIConnectionException");
        } catch (APIRequestException e) {
            e.printStackTrace();
            logger.debug("jpush APIRequestException");
        } finally {
            jpushClient.close();
        }
    }

    /**
     * Ios单推带通知栏消息(alias)
     * 
     * @param contentMsg 推送内容
     * @param alias 设备别名(可多个绑定同个)
     */
    public static void useAliasPushToSingleIosDevice(String contentMsg, String...alias) {
        if (jpushClient == null) {
            jpushClient = new JPushClient(MASTER_SECRET, APP_KEY);
        }
        if (alias == null || alias.equals("")) {
            logger.debug("alias is null or no char");
            return;
        }
        try {
            PushResult result = jpushClient.sendIosNotificationWithAlias(contentMsg, null, alias);
            logger.debug("Got result - " + result);
        } catch (APIConnectionException e) {
            e.printStackTrace();
        } catch (APIRequestException e) {
            e.printStackTrace();
        } finally {
            jpushClient.close();
        }
    }

    /**
     * 推送所有设备
     * 
     * @param contentMsg 推送内容
     */
    public static void pushToAllDevice(String contentMsg) {
        if (jpushClient == null) {
            jpushClient = new JPushClient(MASTER_SECRET, APP_KEY);
        }
        try {
            PushResult result = jpushClient.sendNotificationAll(contentMsg);
            logger.debug("Got result - " + result);
        } catch (APIConnectionException e) {
            e.printStackTrace();
        } catch (APIRequestException e) {
            e.printStackTrace();
        } finally {
            jpushClient.close();
        }
    }
}
