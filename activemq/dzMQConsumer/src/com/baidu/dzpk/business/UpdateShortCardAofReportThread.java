/**
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.dzpk.business;

/**
 * <AUTHOR>
 *
 */
public class UpdateShortCardAofReportThread implements Runnable {

    private String userIds;
    private String roomId;
    public UpdateShortCardAofReportThread(String roomId, String userIds) {
        this.userIds = userIds;
        this.roomId = roomId;
    }

    public void run() {
        GameRecord.updateShortCardProfitDataNew(roomId, userIds,2);
    }

}
