CREATE TABLE crazy_poker.sys_broadcast (
    `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '分类ID',
    `broadcast_mode` int DEFAULT 0 COMMENT '广播方式:0-按次数广播,1-按时间段广播',
    `broadcast_content` varchar(255) NOT NULL COMMENT '广播内容',
    `broadcast_count` int DEFAULT 0 COMMENT '广播次数',
    `broadcast_interval` int DEFAULT 0 COMMENT '广播间隔',
    `broadcast_time` timestamp COMMENT '广播时间',
    `broadcast_start_time` timestamp COMMENT '广播开始时间',
    `broadcast_end_time` timestamp COMMENT '广播结束时间',
    `broadcast_range` int DEFAULT 0 COMMENT '广播范围:0-全服广播,1-牌局广播',
    `broadcast_game_type` varchar(100) COMMENT '广播牌局类型:61-德州扑克',
    `broadcast_status` int DEFAULT 0 COMMENT '广播状态：0-未开始,1-进行中,2-已结束',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `created_by` bigint DEFAULT NULL COMMENT '创建人',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='系统广播表';

