package com.ai.dz.room.util;

import com.ai.dz.config.AiRoomManager;
import com.ai.dz.config.AiRuleTemplate;
import com.ai.dz.config.EnterRoomAiPlayer;
import com.ai.dz.config.cache.CommonIdleModeConfigBo;
import com.ai.dz.config.cache.IAiRuleConfigCache;
import com.ai.dz.config.constant.EAiMode;
import com.ai.dz.config.constant.EAiType;
import com.ai.dz.config.constant.EBringinType;
import com.ai.dz.config.constant.EUserStatus;
import com.ai.dz.room.constant.AiConstant;
import com.ai.dz.room.model.player.AiPlayer;
import com.dzpk.common.utils.Helper;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.repositories.redis.RedisService;
import com.dzpk.dealer.Player;
import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;
import com.google.gson.Gson;
import com.i366.constant.LeaveRoomCode;
import com.i366.model.player.RoomPersion;
import com.i366.model.player.RoomPlayer;
import com.i366.model.room.Room;
import com.i366.room.RoomAutoOp;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * Created by peterguo on 2018/9/13.
 */
public class AiHelper {

    private static Logger logger = LogUtil.getLogger(AiHelper.class);

    /**
     * 初始化at的属性值
     * @param roomPlayer
     * @param userId
     * @param minRate
     * @param maxRate
     * @param roomId
     */
    public static void initData(RoomPlayer roomPlayer,int userId,int minRate,int maxRate,int roomId){
        logger.debug("[R-{}][U-{}]初始化at玩家的属性",roomId,userId);
        AiPlayer aiPlayer = roomPlayer.getAiPlayer();
        if(!aiPlayer.isInitData()){  //如果设置过属性则无需再设置

            Integer opType = AiRuleTemplate.getUserType(userId); //获取at类型
            if(opType == null){
                opType = 1;
            }
            aiPlayer.setAiOpreateType(opType);

            EAiMode eAiMode = AiRoomManager.getModeOfPlayer(roomId,userId); //at模式
            int needHand = AiRoomManager.initStandUpMaxHandCnt(userId,roomId,eAiMode); //获取站起指定手数配置
            int randomHand = AiRoomManager.getStandUpConfigEnableLeavingRandomHand(eAiMode,userId,roomId)?Helper.randomIntBy(3, 5):0;//配置随机手数
            aiPlayer.setAiPlayMaxHand(needHand + randomHand);
            int bringRate = AiRoomManager.randomBringinRatio(EAiType.fromValue(opType), EBringinType.total,userId,roomId,eAiMode); //获取随机整体带入倍数
            int firstBringRate = AiRoomManager.randomBringinRatio(EAiType.fromValue(opType), EBringinType.bringin,userId,roomId,eAiMode); //获取首次带入倍数

            bringRate = bringRate * minRate > maxRate ? maxRate : bringRate * minRate;  //补充整体带入不能大于房间的最大带入倍数 如果大于则设置为房间最大带入倍数
            aiPlayer.setBringRate(bringRate);
            aiPlayer.setInitData(true);
            aiPlayer.setFirstBringRate(firstBringRate);
            int addBringInRatio = AiRoomManager.randomPercentOfMinBringin(userId,roomId,eAiMode); //设置补充带入比例
            aiPlayer.setAddBringInRatio(addBringInRatio);
            roomPlayer.setAiPlayer(aiPlayer);
            logger.debug("[R-{}][U-{}]初始化at玩家的属性完成,opType={},needHand={},bringRate={},firstBringRate={},addBringInRatio={}",
                    roomId,userId,opType,needHand,bringRate,firstBringRate,addBringInRatio);
        }
    }


    /**
     * 检查at的状态
     * 是否需要补充带入
     * 是否打完指定手数站起
     * 是否停止派遣
     * @param room
     */
    public static void checkStatus(Room room,int userId){

        RoomPersion aiRoomPersion = room.getAudMap().get(userId);
        if(aiRoomPersion == null){//已经离开房间，直接返回(主动离开房间或者被管理员踢出房间)
            return;
        }

        RoomPlayer aiRoomPlayer = room.getRoomPlayers().get(userId);
        if(aiRoomPlayer.getSeatSize() == -1){ //站起状态，直接返回(主动站起或者被管理员强制站起)
            logger.debug("[R-{}][U-{}] 检查at的状态，站起状态，直接返回",room.getRoomId(),userId);
            return;
        }
        logger.debug("[R-{}][U-{}] 是否需要站起",room.getRoomId(),userId);
        checkNeedStand(room,userId,aiRoomPlayer,aiRoomPersion); //是否需要站起
        if(!aiRoomPersion.isKickOut()) {
            logger.debug("[R-{}][U-{}] 是否需要补充带入",room.getRoomId(),userId);
            AiAddChips.addChipIfNeed(room, aiRoomPlayer, aiRoomPersion); //是否需要补充带入
        }
        if(EAiMode.auto == AiRoomManager.getModeOfPlayer(room.getRoomId(),userId)){
            checkNeedToStopDispach(room,userId);  //是否需要停止派遣
        }
    }


    /**
     * 检查at玩家打的手数是否满足离开条件
     * (按手数维度判断的，包括自动at3人、2人，关闭自动at开关，手动at 3类情况)
     * @param room
     * @param userId
     * @param roomPlayer
     * @param roomPersion
     */
    private static void checkNeedStand(Room room,int userId,RoomPlayer roomPlayer,RoomPersion roomPersion){
        Map<Integer, Player> players = room.getDealer().getPlayers();
        Player player = players.get(userId);
        if(null != player){
            EAiMode eAiMode = AiRoomManager.getModeOfPlayer(room.getRoomId(),userId); //at模式
            AiPlayer aiPlayer = roomPlayer.getAiPlayer();
            int needBringRate = aiPlayer.getNeedBringRate();

            int totalHand = player.getHandCnt();  //已经打的手数
            int needHand = roomPlayer.getAiPlayer().getAiPlayMaxHand();       //指定的手数
            //是否需要再次带入
            boolean needToAddChips=AiAddChips.checkIfNeed(room,roomPlayer,roomPersion);
            logger.debug("[R-{}][U-{}] 校验满足离开的条件,是否需要再次带入：{}",room.getRoomId(),roomPlayer.getUserId(),needToAddChips);
            int needBringIn = needToAddChips? room.getChouma() * needBringRate * room.getMinRate() : 0;//即將買入量(如果需要带入才需要计算即将买入)
            int supplyBringInCnt = (player.getBringInTimes()-1);//需求是补充带入次数，不将首次带入计算在内
            int totalBringIn = player.getBringIn() + needBringIn;// 总带入（首次帶入量 + 已補充買入量)＋即將買入量 >= 總帶入量上限

            int configMaxBringInCnt = AiRoomManager.getStandUpConfigMaxBringInCnt(eAiMode,userId,room.getRoomId());
            int configMaxBringIn = AiRoomManager.getStandUpConfigMaxBringIn(eAiMode,userId,room.getRoomId());

            logger.debug("[R-{}][U-{}] 校验满足离开的条件,总手数={},手数上限={},总带入={},带入上限={},补充带入次数={},带入次数上限={},earn={}"
                    ,room.getRoomId()
                    ,userId
                    ,totalHand
                    ,needHand
                    ,totalBringIn
                    ,configMaxBringIn
                    ,supplyBringInCnt
                    ,configMaxBringInCnt
                    ,player.getEarn() );
            //当需要再次带入时，总带入次数与总带入理超出限制时需要离桌
            //总手数超出限制需要离桌
            if(totalHand >= needHand || (needToAddChips && (totalBringIn >= configMaxBringIn || supplyBringInCnt >= configMaxBringInCnt)) ){
                if(room.isAutoAireadyToStandUp() && EAiMode.auto == eAiMode){ //站起的玩家是自动ai时才将标志位设置成false
//                    room.setAutoAireadyToStandUp(false);
                    room.setReadyToStandUpAi(null);
                }

                logger.info("[R-{}][U-{}][玩家离开] 总手数={},手数上限={},总带入={},带入上限={},补充带入次数={},带入次数上限={},earn={}, enable earning never leaving = {}"
                        ,room.getRoomId()
                        ,userId
                        ,totalHand
                        ,needHand
                        ,totalBringIn
                        ,configMaxBringIn
                        ,supplyBringInCnt
                        ,configMaxBringInCnt
                        ,player.getEarn());

                //如果触发闲置模式（同时满足闲置模式开关开启、在闲置模式生效期内、台面没有真实玩家），则忽略"盈利不离桌"设定，正常离桌
                //如果触发普通模式，满员（满员包含有真实玩家）情况，则忽略"盈利时不离桌"设定，正常离桌
                if (!isIdleModeActivated(room) && !isRoomFullStrength(room)) {
                    //PP36-FE1 若盈利达到设定手数则离桌（若开启盈利配置且玩家此刻盈利则增设玩家手数）
                    if (!playerEarningLeaving(room,userId,roomPlayer)) {
                        return;
                    }
                }

                logger.debug("[R-{}][U-{}]【玩家离开】at玩家满足设定条件需要站起,需要打的手数={},目前打的手数={},指定带入次数={},目前带入次数={},目前带入总量={},即时带入量(如果不需要带入则=0)={},指定带入总量={}",
                        room.getRoomId(),
                        userId,
                        needHand,totalHand,
                        configMaxBringInCnt,
                        supplyBringInCnt,
                        totalBringIn,
                        needBringIn,
                        configMaxBringIn);
                long userLeftTime = RoomAutoOp.needAutoOp(room,userId); //需要判断最短上桌时间，如果未达到最短上桌时间，启动延迟任务（剩余时间+60-180s随机）

                if (roomPersion.isKickOut()) {
                    return;
                }

                roomPersion.setStandupType(1);
                roomPersion.setKickOut(true);
                RedisService.getRedisService().addKickOutUser(roomPlayer.getUserId(),room.getRoomId());   //将用户加入到该房间被踢的集合中，在进入房间时判断

                if(userLeftTime > 0){
                    Map<Integer, Object> map = new HashMap<Integer, Object>();
                    int likaiType = 3; //离开类型 1自动ai离开 2手动ai离开 3未达到最短上桌时间
                    map.put(1,userId);
                    map.put(2,likaiType);

                    Random r = new Random();
                    userLeftTime = userLeftTime + r.nextInt(121) + 60;
                    logger.debug("[R-{}][U-{}]该房间开启了最短上桌时间,玩家需要继续游戏的时间={},",room.getRoomId(),userId,userLeftTime);
                    Task task = new Task(AiConstant.TASK_AI_AUTO_STAND, map, room.getRoomId(), room.getRoomPath(), userId); // 20241118 add userId
                    WorkThreadService.submitDelayTask(room.getRoomId(), task, userLeftTime * 1000 );
                    room.roomProcedure.delayTaskMap.put(task.getId(),task);
                }else{

                    LeaveRoomCode leaveRoomCode = LeaveRoomCode.AI_PLAY_ENOUGH_HANDS_LEAVE;
                    if (supplyBringInCnt > configMaxBringInCnt) {
                        leaveRoomCode = LeaveRoomCode.AI_PLAY_ENOUGH_BRING_IN_TIMES_LEAVE;
                    }else if (totalBringIn >= configMaxBringIn) {
                        leaveRoomCode = LeaveRoomCode.AI_PLAY_ENOUGH_BRING_IN_LEAVE;
                    }
                    AiHelper.leaveRoom(room,roomPersion,leaveRoomCode);
                }
            }
        }

    }

    /**
     * 检查玩家是否被停止派遣,是否被停止派遣，如果停止了需要执行单独终止任务（终止任务：自己随机打n（1-5之间，不用可配置）手后，离开房间）
     * @param room
     * @param standUpAiUserId
     */
    private static void checkNeedToStopDispach(Room room,int standUpAiUserId){

        EnterRoomAiPlayer enterRoomAiPlayer = AiRoomManager.getPlayerOfAuto(room.getRoomId(),standUpAiUserId);
        if(EUserStatus.off == enterRoomAiPlayer.getStatus()){ //检查ai的状态 是否被停止派遣
            RoomPlayer roomPlayer = room.getRoomPlayers().get(standUpAiUserId);
            if(!roomPlayer.getAiPlayer().isStopDispatch()){  //已经启动终止派遣,无需再次启动
                int aiRandomHand = AiRoomManager.getRandomStandingHandNum(EAiMode.auto,standUpAiUserId,0,0,room.getPlayerCount(),room.getRoomId());
                logger.debug("房间id={},玩家id={},检测到被停止派遣,需要再打的手数={}",room.getRoomId(),standUpAiUserId,aiRandomHand);

                Map<Integer, Player> players = room.getDealer().getPlayers();
                Player player = players.get(standUpAiUserId);
                if(player != null){
                    int totalhand = player.getHandCnt();  //已经打的手数
                    roomPlayer.getAiPlayer().setAiPlayMaxHand(aiRandomHand + totalhand);
                    logger.debug("当前已经打的手数={},需要站起的手数={}",totalhand,roomPlayer.getAiPlayer().getAiPlayMaxHand());
                }
            }
        }
    }

    /**
     * 适用于mt玩家
     * 检查座位上的玩家是否有且只有一个人是ai
     * 牌桌内仅有1个ai（没有其他玩家），n（5-8分钟随机，可配置）分钟后自动站起。（站起前又有玩家坐下，取消站起任务）
     * @param room
     */
    public static void checkOnlyMtPlaying(Room room){

        int aiPlayerCount = 0;  //ai玩家数量
        int playerCount = 0;    //所有玩家数量
        int aiUserId = 0;
        for (int i = 0; i < room.getRoomPersions().length; i++) {
            RoomPersion roomPersion = room.getRoomPersions()[i];
            if (roomPersion != null && roomPersion.getNowcounma() >= room.getMinChip()){
                if(EAiMode.manual == AiRoomManager.getModeOfPlayer(room.getRoomId(),roomPersion.getUserId())){
                    aiPlayerCount++;
                    aiUserId = roomPersion.getUserId();
                }
                playerCount++;
            }
        }

        logger.debug("playerCount={},aiPlayerCount={}",playerCount,aiPlayerCount);
        if(aiPlayerCount == 1 && playerCount == 1){  //有且只有一个人是ai 3-5分钟后自动站离开
            Map<Integer, Object> map = new HashMap<Integer, Object>();
            int likaiType = 2; //离开类型 1自动ai离开 2手动ai离开 3未达到最短上桌时间
            map.put(1,aiUserId);
            map.put(2,likaiType);
            int opTime = AiRoomManager.getStandingTimeInSec(EAiMode.manual,playerCount - aiPlayerCount,aiPlayerCount,room.getRoomId());//获取随机站起时间
            logger.debug("检测到at玩家为唯一房间内玩家,房间id={},玩家id={},需要站起的时间={}",room.getRoomId(),aiUserId,opTime);
            Task task = new Task(AiConstant.TASK_AI_AUTO_STAND, map, room.getRoomId(), room.getRoomPath(), aiUserId); // 20241118 add userId
            WorkThreadService.submitDelayTask(room.getRoomId(), task, opTime * 1000);
            room.roomProcedure.delayTaskMap.put(task.getId(),task);
        }
    }

    /**
     * at离开房间策略
     * @param room
     */
    public static void standStrategy(Room room){

        List<RoomPersion> aiRoomPersionList = new ArrayList<>();
        int aiPlayerCount = 0;  //at玩家数量
        int playerCount = 0;    //所有玩家数量
        for (int i = 0; i < room.getRoomPersions().length; i++) {
            RoomPersion roomPersion = room.getRoomPersions()[i];
            /* 旧逻辑不知为何要排除剩余筹码 小于 房间最小带入筹码的在座玩家
            if (roomPersion != null && roomPersion.getNowcounma() >= room.getMinChip()){
                if(EAiMode.auto == AiRoomManager.getModeOfPlayer(room.getRoomId(),roomPersion.getUserId())){
                    aiPlayerCount++;
                    aiRoomPersionList.add(roomPersion);
                }
                playerCount++;
            }
            */
            if (roomPersion != null){
                if(EAiMode.auto == AiRoomManager.getModeOfPlayer(room.getRoomId(),roomPersion.getUserId())){
                    aiPlayerCount++;
                    aiRoomPersionList.add(roomPersion);
                }
                playerCount++;
            }
        }

        /* 添加闲置模式之前的旧逻辑，包含有爆桌模式
        if(room.getAtRoomConfig().isBoomDeckMode()){
            doBoomDeckModeLeave(room,playerCount,aiPlayerCount,aiRoomPersionList);
        }else{
            doNormalModeLeave(room,playerCount,aiPlayerCount,aiRoomPersionList);
        }
         */
        //PP50-FE1 无论是否触发闲置模式，先取消上一次触发了闲置模式时可能已经开始的等待任务
        cancelAiIdleModeWaitingTask(room,aiRoomPersionList);
        //PP50-FE1 修改成闲置模式开关开启、在闲置模式生效期内、台面没有真实玩家才调用闲置模式逻辑，否则调用普通模式
        if(isIdleModeActivated(room)){
            doIdleModeLeave(room,playerCount,aiPlayerCount,aiRoomPersionList);
        }else{
            doNormalModeLeave(room,playerCount,aiPlayerCount,aiRoomPersionList);
        }
    }

    /**
     * 爆桌模式at离开房间策略 todo 调整5分钟内
     *     游戏结束30分钟内
     *     1）牌桌内仅有2个at（没有其他玩家），当打满n（8-15手随机，可配置）手后随机站起一个。（站起前又有玩家坐下，也离开）
     *     2）牌桌内仅有1个at（没有其他玩家），n（3-5分钟随机，可配置）分钟后自动站起。（站起前又有玩家坐下，取消站起任务）
     * @param room
     * @param playerCount
     * @param aiPlayerCount
     * @param aiRoomPersionList
     */
    @Deprecated
    private static void doBoomDeckModeLeave(Room room,int playerCount,int aiPlayerCount,List<RoomPersion> aiRoomPersionList){
        logger.debug("爆桌模式,检测是否触发at玩家站起策略,当前时间={},房间开始时间={},房间游戏时间={}",System.currentTimeMillis(),room.getGameBeginTime(),room.getMaxPlayTime());
        if(((room.getGameBeginTime() + room.getMaxPlayTime() * 60000) - System.currentTimeMillis()) <= 5 * 60000
                && !room.isAutoAireadyToStandUp()){

            logger.debug("执行at站起策略,目前房间内玩家数量={},at玩家数量={}",playerCount,aiPlayerCount);
            if(aiPlayerCount == 2 && playerCount == 2){
                stand2ai(room,aiPlayerCount,aiRoomPersionList);
            }else if(aiPlayerCount == 1 && playerCount == 1){
                stand1ai(room,playerCount,aiPlayerCount);
            }
        }
    }

    /**
     * 普通模式at离开房间策略
     *      游戏开始20分钟后（点了牌局内开始按键）时间可配置
     *      当空位=0时，打n（1-5手随机，可配置）手后站起1个盈利最少的ai。（站起前又有玩家离开，也离开）
     *
     * @param room
     * @param playerCount
     * @param aiPlayerCount
     * @param aiRoomPersonList
     */
    private static void doNormalModeLeave(Room room,int playerCount,int aiPlayerCount,List<RoomPersion> aiRoomPersonList){
        logger.debug("【站起策略】【JYM】普通模式,检测是否触发at玩家站起策略,房间ID={},当前时间={},房间开始时间={},配置的自动站起时间={}",room.getRoomId(),System.currentTimeMillis(),room.getGameBeginTime(),room.getAtRoomConfig().getAutoAiStandUpTime());
        if(System.currentTimeMillis() - room.getGameBeginTime() > room.getAtRoomConfig().getAutoAiStandUpTime() * 1000
                && !room.isAutoAireadyToStandUp()){
            logger.debug("【站起策略】【JYM】执行at站起策略,房间允许进入的最大玩家数量={},目前房间内玩家数量={},at玩家数量={}",room.getPlayerCount(),playerCount,aiPlayerCount);
            if(isRoomFullStrength(room)){
                //如果房间已满，则找出盈利最少AI并站起离桌
                leastEarnAIStandUp(room,aiPlayerCount,aiRoomPersonList,true);
            }
        }
    }

    /**
     * 当前牌局，闲置模式下at离开房间策略
     * 游戏开始N分钟后（点了牌局内开始按键）时间可配置
     *
     * @param room
     * @param playerCount
     * @param aiPlayerCount
     * @param aiRoomPersonList
     */
    private static void doIdleModeLeave(Room room,int playerCount,int aiPlayerCount,List<RoomPersion> aiRoomPersonList){
        logger.debug("[R-{}]【JYM】 闲置模式 执行at站起策略,房间允许进入的最大玩家数量={},目前房间内玩家数量={},at玩家数量={}",room.getRoomId(),room.getPlayerCount(),playerCount,aiPlayerCount);
        if(aiPlayerCount > 1 ) {
            if (room.isAutoAireadyToStandUp()) {
                logger.debug("[R-{}]【JYM】 闲置模式 有一个AI已经被选出正在玩剩余手数，不再走最少盈利离桌逻辑",room.getRoomId());
                return;
            }
            logger.debug("[R-{}]【JYM】 闲置模式 房间有2个以上AI，找出最少盈利AI并设置随机玩多3-5手离桌",room.getRoomId());
            //找出最少盈利AI并设置随机玩多3-5手离桌
            leastEarnAIStandUp(room,aiPlayerCount,aiRoomPersonList,false);
        }else {
            idleModeWaitToLeave(room,aiRoomPersonList);
        }
    }
    private static void idleModeLeave(Room room,int standUpAiUserId,List<RoomPersion> aiRoomPersonList,Player standUpPlayer,RoomPlayer standUpRoomPlayer) {
        int totalHand = standUpPlayer.getHandCnt();  //已经打的手数
        int aiRandomHand = AiRoomManager.getIdleModeRandomHand(room.getRoomId());
        standUpRoomPlayer.getAiPlayer().setAiPlayMaxHand(aiRandomHand + totalHand);
//            room.setAutoAireadyToStandUp(true);
        room.setReadyToStandUpAi(standUpRoomPlayer.getUserId());
        logger.debug("[R-{}][U-{}]【闲置模式离桌】【最少盈利AI离桌 配置站起手数】【JYM】 牌桌内有{}个ai,当前已经打的手数={},需要站起时的手数={}",
                room.getRoomId(),
                standUpAiUserId,
                aiRoomPersonList.size(),
                totalHand,
                standUpRoomPlayer.getAiPlayer().getAiPlayMaxHand());
    }
    private static void idleModeWaitToLeave (Room room,List<RoomPersion> aiRoomPersonList) {
        if (aiRoomPersonList==null || aiRoomPersonList.isEmpty()) {
            return;
        }
        RoomPersion roomPersion = aiRoomPersonList.get(0);
        //理论上这里不可能有空值,防御性代码
        if (roomPersion == null) {
            logger.warn("[R-{}]AI玩家列表中有空对象：{}", room.getRoomId(), aiRoomPersonList);
            return;
        }
        //获取等待执行离桌任务的时间
        int waitingTimeSec = AiRoomManager.getIdleModeWaitingTimeSec(room.getRoomId());
        //PP50 获取房间指定配置的闲置模式参数
        if (room.getAtGamePlan() != null && room.getAtGamePlan().getAdvancedIdleEnable() && StringUtils.isNotBlank(room.getAtGamePlan().getAdvancedIdleConfig())) {
            String idleConfigStr = room.getAtGamePlan().getAdvancedIdleConfig();
            Gson jsonMapper = new Gson();
            CommonIdleModeConfigBo idleModeConfigBo = jsonMapper.fromJson(idleConfigStr, CommonIdleModeConfigBo.class);
            waitingTimeSec = idleModeConfigBo.getWaitingTimeSec();
            logger.debug("[R-{}][房间指定派遣配置] 通过房间指定派遣配置设置 闲置模式的等待时间：{}",room.getRoomId(),waitingTimeSec);
        }

        roomPersion.startWaitingTask(waitingTimeSec,()->{
            int userId = roomPersion.getUserId();
            RoomPlayer roomPlayer = room.getRoomPlayers().get(userId);
            logger.debug("[R-{}][U-{}]【JYM】 闲置模式 房间只有1个AI，AI已经等待超时",room.getRoomId(),userId);
            //离开房间
            roomPersion.setStandupType(1);
            roomPersion.setKickOut(true);
            RedisService.getRedisService().addKickOutUser(roomPlayer.getUserId(),room.getRoomId());   //将用户加入到该房间被踢的集合中，在进入房间时判断

            leaveRoom(room,roomPersion,LeaveRoomCode.AI_ONLY_ONE_PERSION_LEAVE);
        });
    }

    /**
     * at离开房间
     * @param room
     * @param roomPersion
     * @param leaveRoomCode
     */
    public static void leaveRoom(Room room,RoomPersion roomPersion,LeaveRoomCode leaveRoomCode){

        int atUserId = roomPersion.getUserId();
        RoomPlayer roomPlayer = room.getRoomPlayers().get(atUserId);
        //boolean needToAheadLeave = needToAheadLeave(room,roomPlayer,atUserId);
        if(room.getAheadLeaveMode() == 1){
            logger.debug("[R-{}][U-{}]at离开房间需要提前离桌",room.getRoomId(),atUserId);
            roomPlayer.setAheadLeave(1);
        }
        logger.info("[R-{}][U-{}]at离开房间,准备下一次派遣任务...",room.getRoomId(),atUserId);
        room.getRoomService().likai(roomPersion, leaveRoomCode);
        AiDispatch.disPatchAi(room); // at离开后再派遣
    }

    /**
     * at离开时是否需要提前离桌
     * @param room
     * @param roomPlayer
     * @param userId
     * @return
     * @deprecated
     */
    @Deprecated
    private static boolean needToAheadLeave(Room room,RoomPlayer roomPlayer,int userId){

        boolean needToAheadLeave = false;
        if(room.getAheadLeaveMode() == 0){//非提前离桌模式不走代码
            logger.info("ahead leave error,room is not aheadLeaveMode");
            return needToAheadLeave;
        }

        if(1 == roomPlayer.getAheadLeave()){  //如果该玩家已经点击了提前离桌，则直接返回
            logger.info("ahead leave error,user have click aheadLeave button");
            return needToAheadLeave;
        }

        if(room.getMinPlayTime() > 0){ //游戏中未到最短打牌时间不让提前离桌
            long userLeftPlayTime = RoomAutoOp.needAutoOp(room,userId);
            if (userLeftPlayTime > 0) {
                logger.debug("ahead leave error,player not play min playtime");
                return needToAheadLeave;
            }
        }

        needToAheadLeave = AiRoomManager.randomInAdvanceMode(room.getRoomId(),userId);

        return needToAheadLeave;
    }


    /**
     * 牌桌内当空位=0时，打n（1-5手随机，暂不可配置）手后站起1个盈利最少的ai。（站起前又有玩家离开，也离开）
     * @param room
     * @param aiPlayerCount
     * @param aiRoomPersonList
     */
    private static void leastEarnAIStandUp(Room room,int aiPlayerCount,List<RoomPersion> aiRoomPersonList,boolean isNormalModeLeave){

        Player standUpPlayer = findSecondLeastEarnAI(room,aiPlayerCount,aiRoomPersonList);
        if (standUpPlayer == null) {
            logger.warn("[R-{}]  找不到最少盈利AI玩家",room.getRoomId());
            return;
        }

        int standUpAiUserId = standUpPlayer.getUserId();
        RoomPlayer standUpRoomPlayer = room.getRoomPlayers().get(standUpAiUserId);

        if (isNormalModeLeave) {
            //PP73 是否开启有真人才触发满员离桌
            boolean enableHasPlayerOverLeaving = AiRoomManager.getStandUpConfigEnableHasPlayerOverLeaving(standUpAiUserId,room.getRoomId());
            logger.info("[R-{}][U-{}]【满员离桌策略】是否开启有真人才触发满员离桌：{}",room.getRoomId(),standUpAiUserId,enableHasPlayerOverLeaving);
            if (enableHasPlayerOverLeaving) {
                if (hasRealPlayer(room)) {
                    normalLeaving(room,standUpAiUserId,aiRoomPersonList,standUpPlayer,standUpRoomPlayer);
                }
            }else {
                normalLeaving(room,standUpAiUserId,aiRoomPersonList,standUpPlayer,standUpRoomPlayer);
            }
        }else {
            idleModeLeave(room,standUpAiUserId,aiRoomPersonList,standUpPlayer,standUpRoomPlayer);
        }
    }

    /**
     * 牌桌内有3个ai，当空位=0时，打n（3-5手随机，可配置）手后站起1个盈利最少的ai。（站起前又有玩家离开，也离开）
     * @param room
     * @param aiPlayerCount
     * @param aiRoomPersionList
     */
    @Deprecated
    private static void stand3ai(Room room,int aiPlayerCount,List<RoomPersion> aiRoomPersionList){
        int standUpAiUserId = 0; //站起玩家id
        int leaastEarn = 0;  //最少的盈利
        for(RoomPersion aiRoomPersion:aiRoomPersionList){
            Player aiPlayer = room.getDealer().getPlayers().get(aiRoomPersion.getUserId());
            int earn = aiPlayer.getEarn();
            if(earn < leaastEarn){  //找出盈利最少的玩家
                leaastEarn = earn;
                standUpAiUserId = aiRoomPersion.getUserId();
            }
        }

        int aiRandomHand = AiRoomManager.getRandomStandingHandNum(EAiMode.auto,standUpAiUserId,room.getPlayerCount() - aiPlayerCount,aiPlayerCount,room.getPlayerCount(),room.getRoomId());
        logger.debug("房间id={},牌桌内有3个ai,被站起玩家id={},,需要再打的手数={}",room.getRoomId(),standUpAiUserId,aiRandomHand);

        RoomPlayer roomPlayer = room.getRoomPlayers().get(standUpAiUserId);
        Map<Integer, Player> players = room.getDealer().getPlayers();
        Player player = players.get(standUpAiUserId);
        if(player != null){
            int totalhand = players.get(standUpAiUserId).getHandCnt();  //已经打的手数
            roomPlayer.getAiPlayer().setAiPlayMaxHand(aiRandomHand + totalhand);
//            room.setAutoAireadyToStandUp(true);
            room.setReadyToStandUpAi(roomPlayer.getUserId());
            logger.debug("[R-{}][U-{}]当前已经打的手数={},需要站起时的手数={}",room.getRoomId(),roomPlayer.getUserId(),totalhand,roomPlayer.getAiPlayer().getAiPlayMaxHand());
        }
    }

    /**
     * 牌桌内仅有2个ai（没有其他玩家），当打满n（8-15手随机，可配置）手后随机离开一个。（站起前又有玩家坐下，也离开）
     * @param room
     * @param aiPlayerCount
     * @param aiRoomPersionList
     */
    private static void stand2ai(Room room,int aiPlayerCount,List<RoomPersion> aiRoomPersionList){
        int standUpAiUserId = 0; //站起玩家id
        int randomIndex = System.currentTimeMillis() % 2 == 0 ? 0 : 1; //随机一个离开
        standUpAiUserId = aiRoomPersionList.get(randomIndex).getUserId();
        int aiRandomHand = AiRoomManager.getRandomStandingHandNum(EAiMode.auto,standUpAiUserId,0,aiPlayerCount,room.getPlayerCount(),room.getRoomId());
        logger.debug("房间id={},牌桌内仅有2个ai,被站起玩家id={},,需要再打的手数={}",room.getRoomId(),standUpAiUserId,aiRandomHand);
        RoomPlayer roomPlayer = room.getRoomPlayers().get(standUpAiUserId);

        Map<Integer, Player> players = room.getDealer().getPlayers();
        Player player = players.get(standUpAiUserId);
        if(player != null){
            int totalhand = player.getHandCnt();  //已经打的手数
            roomPlayer.getAiPlayer().setAiPlayMaxHand(aiRandomHand + totalhand);
//            room.setAutoAireadyToStandUp(true);
            room.setReadyToStandUpAi(roomPlayer.getUserId());
            logger.debug("[R-{}][U-{}]当前已经打的手数={},需要站起时的手数={}",room.getRoomId(),roomPlayer.getUserId(),totalhand,roomPlayer.getAiPlayer().getAiPlayMaxHand());
        }

    }

    /**
     * 适用于at玩家
     * 牌桌内仅有1个ai（没有其他玩家），n（3-5分钟随机，可配置）分钟后自动站起。（站起前又有玩家坐下，取消站起任务）
     * @param room
     * @param playerCount
     * @param aiPlayerCount
     */
    private static void stand1ai(Room room,int playerCount,int aiPlayerCount){
        int aiUserId = 0;
        for (int i = 0; i < room.getRoomPersions().length; i++) {
            RoomPersion roomPersion = room.getRoomPersions()[i];
            if (roomPersion != null && roomPersion.getNowcounma() >= room.getMinChip()){
                if(EAiMode.auto == AiRoomManager.getModeOfPlayer(room.getRoomId(),roomPersion.getUserId())){
                    aiUserId = roomPersion.getUserId();
                }
            }
        }
        logger.debug("房间id={},牌桌内有1个ai,被站起的玩家id={}",room.getRoomId(),aiUserId);

        Map<Integer, Object> map = new HashMap<Integer, Object>();
        int likaiType = 1;  //离开类型 1自动ai离开 2手动ai离开 3未达到最短上桌时间
        map.put(1,aiUserId);
        map.put(2,likaiType);
        int opTime = AiRoomManager.getStandingTimeInSec(EAiMode.auto,playerCount - aiPlayerCount,aiPlayerCount,room.getRoomId());//获取随机站起时间
        logger.debug("房间id={},牌桌内仅有1个at,被站起玩家id={},需要站起的时间={}秒",room.getRoomId(),aiUserId,opTime);
        Task task = new Task(AiConstant.TASK_AI_AUTO_STAND, map, room.getRoomId(), room.getRoomPath(), aiUserId); // 20241118 add userId
        WorkThreadService.submitDelayTask(room.getRoomId(), task, opTime * 1000);
        room.roomProcedure.delayTaskMap.put(task.getId(),task);

    }

    /**
     * 是否触发了闲置模式
     * 触发闲置模式必须满足3个条件：闲置模式开关开启、在闲置模式生效期内、台面没有真实玩家
     * */
    public static boolean isIdleModeActivated(Room room) {

        //获取限制模式配置
        CommonIdleModeConfigBo idleModeConfigBo = null;
        //PP50 从《获取房间指定配置的闲置模式》获取闲置模式生效时间
        if (room.getAtGamePlan() != null ) {
            if (!StringUtils.isNotBlank(room.getAtGamePlan().getAdvancedIdleConfig())) {
                logger.debug("[R-{}][房间指定闲置模式配置] 闲置配置为空，此时默认关闭闲置模式 ",room.getRoomId());
                //PP50-FE5 如果发现没有数据，则闲置模式默认为关闭
                idleModeConfigBo = new CommonIdleModeConfigBo(false,0,0,null);
            }else {
                String idleConfigStr = room.getAtGamePlan().getAdvancedIdleConfig();
                Gson jsonMapper = new Gson();
                idleModeConfigBo = jsonMapper.fromJson(idleConfigStr, CommonIdleModeConfigBo.class);
                //PP50如果存在房间指定闲置配置，但冇开启进阶闲置模式就当闲置模式是关闭的
                idleModeConfigBo.setSwitchOn(room.getAtGamePlan().getAdvancedIdleEnable());
                logger.debug("[R-{}][房间指定闲置模式配置] 闲置配置:{} ",room.getRoomId(),idleConfigStr);
            }
        }else {
            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
            idleModeConfigBo = cache.getCommonIdleModeConfigBo();
            logger.debug("[R-{}][全局闲置模式配置] 闲置配置:{} ",room.getRoomId(),idleModeConfigBo==null?"读取全局闲置配置出错":idleModeConfigBo.isSwitchOn());
        }

        if (idleModeConfigBo == null) {
            logger.debug("[R-{}][闲置模式配置] not found config ! ",room.getRoomId());
            return false;
        }
        boolean idleModeOn = idleModeConfigBo.isSwitchOn();
        //未开启闲置模式，则直接返回false
        if (!idleModeOn) {
            logger.debug("[R-{}][闲置模式配置] 关",room.getRoomId());
            return false;
        }
        //获取闲置模式生效时间
        long leftTimeStamp = idleModeConfigBo.getGameLeftTimeSec() * 1000L;
        //获取游戏结束时间的时间戳
        long gameEndTimeStamp = room.getGameBeginTime() + room.getMaxPlayTime() * 60 * 1000L;
        //牌局前段不生效
        //假如 牌局为30分钟，配置了牌局剩余时间25分，那么是在牌局开局后剩余<=25分会做闲置模式
        //即 在前5分钟都不会触发
        //牌局中有真实玩家坐下不触发
        boolean activated = false;
        boolean hasRealPlayer = hasRealPlayer(room);
        if(gameEndTimeStamp - System.currentTimeMillis() <= leftTimeStamp && !hasRealPlayer) {
            activated = true;
        }
        logger.debug("[R-{}]【JYM】闲置模式是否触发,当前时间={},房间结束时间={},配置的生效时间(牌局剩下时间)={},是否有真实玩家={},room.isAutoAlreadyToStandUp()={},idle mode activated ={}",
                room.getRoomId(),
                System.currentTimeMillis(),
                gameEndTimeStamp,
                leftTimeStamp,
                hasRealPlayer,
                room.isAutoAireadyToStandUp(),
                activated);

        return activated;
    }

    /**
     * 判断当前台面是否有真实玩家坐下
     * */
    private static boolean hasRealPlayer(Room room) {
        boolean isHasRealPlayer = false;
        for (int i = 0; i < room.getRoomPersions().length; i++) {
            RoomPersion roomPersion = room.getRoomPersions()[i];
            if (roomPersion != null){
                //有正常玩家
                if(!AiRuleTemplate.isAiUser(roomPersion.getUserId())){
                    isHasRealPlayer = true;
                    logger.info("[R-{}][U-{}]【JYM】 房间有真实玩家，玩家昵称={}",room.getRoomId(),roomPersion.getUserId(),roomPersion.getUserInfo().getNikeName());
                    break;
                }
            }
        }
        return isHasRealPlayer;
    }
    /**
     * 取消已经开始的闲置模式离桌等待时间
     * 条件：当前台面有两个玩家以上（包含有真实玩家情况）
     * */
    private static void cancelAiIdleModeWaitingTask(Room room,List<RoomPersion> aiRoomPersonList) {
        //当有两个玩家（包含有真实玩家情况）的时候，取消AI离开计时器
        if (room.getRoomPersions().length > 1) {
            aiRoomPersonList.forEach(person -> {
                person.cancelWaitingTask(()->{
                    logger.debug("[R-{}][U-{}]【JYM】 闲置模式 重置等待的开始时间",room.getRoomId(),person.getUserId());
                });
            });
        }
    }

    /**
     * 判断当前房间是否满员
     * */
    private static boolean isRoomFullStrength(Room room) {
        int roomMaxPlayerCount = room.getPlayerCount();
        int aiPlayerCount = 0;  //at玩家数量
        int playerCount = 0;    //所有玩家数量
        for (int i = 0; i < room.getRoomPersions().length; i++) {
            RoomPersion roomPersion = room.getRoomPersions()[i];
            if (roomPersion != null){
                if(EAiMode.auto == AiRoomManager.getModeOfPlayer(room.getRoomId(),roomPersion.getUserId())){
                    aiPlayerCount++;
                }
                playerCount++;
            }
        }
        if(playerCount == roomMaxPlayerCount){
            logger.debug("[R-{}] [判断房间是否满员] 房间已满员：房间有{}个座位，现有{}个玩家,其中有{}个AI",room.getRoomId(),roomMaxPlayerCount,playerCount,aiPlayerCount);
            return true;
        }
        logger.debug("[R-{}]  [判断房间是否满员] 房间还有空位房间有{}个座位，现有{}个玩家,其中有{}个AI",room.getRoomId(),roomMaxPlayerCount,playerCount,aiPlayerCount);
        return false;
    }
    private static void normalLeaving(Room room,int standUpAiUserId,List<RoomPersion> aiRoomPersonList,Player player,RoomPlayer roomPlayer) {
        //[PP47-FE2] 找出当前房间次少手数的AI是不是最少盈利的AI，如果是则选第三少盈利的AI（目的是不想AI刚派遣就被赶出房间）
        Player leastHandCntPlayer = reFindAILeave(room,standUpAiUserId,aiRoomPersonList);
        if (leastHandCntPlayer != null) {
            logger.info("[R-{}]【满员离桌策略】【次少盈利AI离桌】次少盈利的AI({})，最终需要离桌的AI({})",room.getRoomId(),standUpAiUserId,leastHandCntPlayer.getUserId());
            player = leastHandCntPlayer;
            standUpAiUserId = player.getUserId();
            roomPlayer = room.getRoomPlayers().get(standUpAiUserId);
        }
        //满员不离桌设定
        boolean enableOverNeverLeaving = AiRoomManager.getStandUpConfigEnableOverNeverLeaving(standUpAiUserId,room.getRoomId());
        logger.debug("[R-{}][U-{}]【满员离桌策略】【次少盈利AI离桌】【JYM】是否开启满员不离桌={}",room.getRoomId(),standUpAiUserId,enableOverNeverLeaving);
        if(!enableOverNeverLeaving){
            int totalHand = player.getHandCnt();  //已经打的手数
            int earn = player.getEarn();
            //[PP47-FE2] 滿員離桌 - 選擇離桌機器人邏輯需考慮盈利機器人是否打滿30手
            //假如选中AI盈利值是正数，则检查总手数是否>=30，足够30手则选择离开，如果未够则放弃这次选中AI，进入下一轮筛选
            if (earn > 0 && totalHand < 30) {
                logger.info("[R-{}][U-{}]【满员离桌策略】【次少盈利AI离桌】【放弃此次离开】盈利={},当前已经打的手数={},需要站起时的手数={}",
                        room.getRoomId(),
                        player.getUserId(),
                        earn,
                        totalHand,
                        roomPlayer.getAiPlayer().getAiPlayMaxHand());
                return;
            }

            int aiRandomHand = Helper.randomIntBy(3, 5);
            roomPlayer.getAiPlayer().setAiPlayMaxHand(aiRandomHand + totalHand);
//            room.setAutoAireadyToStandUp(true);
            room.setReadyToStandUpAi(roomPlayer.getUserId());
            logger.info("[R-{}][U-{}]【满员离桌策略】【次少盈利AI离桌】【JYM】盈利={},当前已经打的手数={},需要站起时的手数={}",
                    room.getRoomId(),
                    player.getUserId(),
                    earn,
                    totalHand,
                    roomPlayer.getAiPlayer().getAiPlayMaxHand());
        }

    }

    /**
     * [PP47-FE2] 滿員離桌 - 選擇離桌機器人邏輯需考慮盈利機器人是否打滿30手
     * 找出次少盈利AI
     * */
    private static Player findSecondLeastEarnAI(Room room, int aiPlayerCount, List<RoomPersion> aiRoomPersonList) {
        Player player = null;
        //满员后，发现只有一个AI，AI根据随机最后手数离桌
        if (aiPlayerCount == 1) {
            player = room.getDealer().getPlayers().get(aiRoomPersonList.get(0).getUserId());
        }else {
            Player leastEarnPlayer = null;
            Integer leastEarn = null;
            Integer secondLeastEarn = null;
            // 有多个 AI 玩家，遍历找出最少盈利和次少盈利的玩家
            for (RoomPersion aiRoomPerson : aiRoomPersonList) {
                Player aiPlayer = room.getDealer().getPlayers().get(aiRoomPerson.getUserId());
                int earn = aiPlayer.getEarn();
                if (leastEarn == null || earn < leastEarn) {
                    // 更新最少盈利玩家和次少盈利玩家
                    secondLeastEarn = leastEarn;
                    player = leastEarnPlayer;
                    leastEarn = earn;
                    leastEarnPlayer = aiPlayer;
                } else if (secondLeastEarn == null || earn < secondLeastEarn) {
                    // 更新次少盈利玩家
                    secondLeastEarn = earn;
                    player = aiPlayer;
                }
            }
        }
        logger.debug("[R-{}][查找最少盈利AI] ai player user id：{}",room.getRoomId(),player==null?-1:player.getUserId());
        return player;
    }

    /**
     * 找出重新计算适合离开游戏的AI
     * */
    private static Player reFindAILeave(Room room,int secondLeastEarnUserId,List<RoomPersion> aiRoomPersonList) {
        if (logger.isDebugEnabled()) {
            StringBuilder earnLogStr = new StringBuilder("[R-" + room.getRoomId() + "] 台面所有玩家的盈利情况: ");
            for (int i = 0; i < aiRoomPersonList.size(); i++) {
                RoomPersion aiRoomPerson = aiRoomPersonList.get(i);
                Player aiPlayer = room.getDealer().getPlayers().get(aiRoomPerson.getUserId());
                int earn = aiPlayer.getEarn();
                earnLogStr.append("[U-").append(aiPlayer.getUserId()).append(":").append(aiRoomPerson.getUserInfo().getNikeName()).append("] 盈利: ").append(earn).append(" 手数：").append(aiPlayer.getHandCnt());
                if (i < aiRoomPersonList.size() - 1) {
                    earnLogStr.append(", ");
                }
            }
            logger.debug(earnLogStr.toString());
        }

        // 找出最少手数的玩家
        Player minHandCntPlayer = null;
        for (RoomPersion aiRoomPerson : aiRoomPersonList) {
            Player aiPlayer = room.getDealer().getPlayers().get(aiRoomPerson.getUserId());
            if (minHandCntPlayer == null || aiPlayer.getHandCnt() < minHandCntPlayer.getHandCnt()) {
                minHandCntPlayer = aiPlayer;
            }
        }

        // 判断最少手数的玩家是否是次少盈利的玩家
        if (minHandCntPlayer !=null && minHandCntPlayer.getUserId() == secondLeastEarnUserId) {
            // 定义最少、次少、第三少盈利玩家及其盈利值
            Player leastEarn = null, secondLeastEarn = null, thirdLeastEarn = null;
            int leastEarnValue = Integer.MAX_VALUE, secondEarnValue = Integer.MAX_VALUE, thirdEarnValue = Integer.MAX_VALUE;

            // 找出最少、次少、第三少盈利的玩家
            for (RoomPersion aiRoomPerson : aiRoomPersonList) {
                Player aiPlayer = room.getDealer().getPlayers().get(aiRoomPerson.getUserId());
                int earn = aiPlayer.getEarn();
                if (earn < leastEarnValue) {
                    thirdEarnValue = secondEarnValue;
                    thirdLeastEarn = secondLeastEarn;
                    secondEarnValue = leastEarnValue;
                    secondLeastEarn = leastEarn;
                    leastEarnValue = earn;
                    leastEarn = aiPlayer;
                } else if (earn < secondEarnValue) {
                    thirdEarnValue = secondEarnValue;
                    thirdLeastEarn = secondLeastEarn;
                    secondEarnValue = earn;
                    secondLeastEarn = aiPlayer;
                } else if (earn < thirdEarnValue) {
                    thirdEarnValue = earn;
                    thirdLeastEarn = aiPlayer;
                }
            }
            logger.debug("[R-{}][根据游戏手数重新决定离开AI] 最少手数AI与次少盈利AI是同一个，则需要找第三少盈利的AI离桌：第三少盈利AI:U-{}",
                    room.getRoomId(), thirdLeastEarn == null ? -1 : thirdLeastEarn.getUserId());
            return thirdLeastEarn;
        }

        logger.debug("[R-{}][根据游戏手数重新决定离开AI] 最少手数AI与次少盈利AI并不是同一个，可以选择次少盈利的AI离桌：U-{}",
                room.getRoomId(), secondLeastEarnUserId);
        return null;
    }

    /**
     * PP36-FE1 若盈利达到设定手数则离桌
     * 玩家盈利时，根据配置与盈利状态计算是否需要离开
     * */
    private static boolean playerEarningLeaving(Room room,int userId,RoomPlayer roomPlayer) {
        Map<Integer, Player> players = room.getDealer().getPlayers();
        Player player = players.get(userId);
        boolean playerLeaving = false;
        if (player != null) {
            EAiMode eAiMode = AiRoomManager.getModeOfPlayer(room.getRoomId(),userId); //at模式
            boolean enableEarningConfig = AiRoomManager.getStandUpConfigEnableEarningConfig(eAiMode,userId,room.getRoomId());
            //PP36-FE1 盈利时玩多几手离桌
            if (player.getEarn() >0 && enableEarningConfig) {
                //如果玩家已经增设过手数则意味着准备离开（roomPlayer.getIsEarningLeaving()=true），此时不需要就算是盈利也无需增设手数
                if (!roomPlayer.getIsEarningLeaving()) {
                    //玩家不需离开，玩多配置的手数
                    int totalHand = player.getHandCnt();  //已经打的手数
                    int addHandCnt = AiRoomManager.getStandUpConfigHandCntOfEarning(eAiMode,userId,room.getRoomId());
                    roomPlayer.getAiPlayer().setAiPlayMaxHand(addHandCnt + totalHand);

                    //标记下一次满足手数条件离开时，直接离开
                    roomPlayer.setIsEarningLeaving(true);
                    //玩家此刻不能离开
                    playerLeaving = false;
                    logger.info("[R-{}][U-{}][离桌配置] 玩家盈利，需要多玩几手后离开：盈亏={}, 盈利离桌配置开关=开，玩家目前总手数={},增设手数={},增设后玩家游戏手数上限={}"
                            ,room.getRoomId()
                            ,userId
                            ,player.getEarn()
                            ,totalHand
                            ,addHandCnt
                            ,roomPlayer.getAiPlayer().getAiPlayMaxHand());
                }else {
                    //玩家需要离开，重置玩家盈利离开的初始状态
                    roomPlayer.setIsEarningLeaving(false);
                    //玩家此刻离开
                    playerLeaving = true;
                    logger.info("[R-{}][U-{}][离桌配置] 玩家已经增设过手数，玩家离开（roomPlayer.getIsEarningLeaving()=true）：盈亏={}, 盈利离桌配置开关=开"
                            ,room.getRoomId()
                            ,userId
                            ,player.getEarn());
                }
            }else {
                //没有开启盈利配置，玩家此刻离开
                playerLeaving = true;
                logger.info("[R-{}][U-{}][离桌配置] 玩家没有盈利或没有开启盈利配置,玩家离开: 盈亏={}, 盈利离桌配置开关={}"
                        ,room.getRoomId()
                        ,userId
                        ,player.getEarn()
                        ,enableEarningConfig);
            }
        }

        return playerLeaving;
    }

}
