package com.ai.dz.room.util;

import com.ai.dz.config.AiRuleTemplate;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;
import com.i366.cache.Cache;
import com.i366.constant.Constant;
import com.i366.model.player.RoomPersion;
import com.i366.model.pocer.Pocer;
import com.i366.model.room.Room;
import com.i366.room.BiPai;
import com.work.comm.client.protocal.Request;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;

public class AiJackPot {

    private static Logger logger = LogUtil.getLogger(AiJackPot.class);

    /**
     * 判断at玩家牌型是否满足击中jp 如果满足则亮牌(点亮小眼睛)
     * @param room
     */
    public static void needToShowCards(Room room){
        int currentPlayCount = room.getCurrentPlayerCount();
        for(RoomPersion roomPersion:room.getRoomPersions()){
            if(roomPersion != null ){
                if(AiRuleTemplate.isAiUser(roomPersion.getUserId()) && (roomPersion.getType() == 3) && currentPlayCount >= 4){//判断ai玩家手牌与已发的公共牌是否满足击中jp，满足则点亮小眼睛
                    boolean checkAiBetJpResult = checkBetJackPot(room,roomPersion);
                    if(checkAiBetJpResult){
                        mockShowCards(room,roomPersion);
                    }
                }
            }
        }
    }

    /**
     * 判断手牌与已发的公共牌是否满足击中jp
     * @param room
     * @param aiRoomPersion
     * @return
     */
    public static boolean checkBetJackPot(Room room,RoomPersion aiRoomPersion){
        //logger.debug("checkBetJackPot, roomid: " + room.getRoomId() + " aiUserId: " + aiRoomPersion.getUserId());
        RoomPersion tempRoomPersion = new RoomPersion();

        int publicCardNum = 0;
        for (int m = 0; m < room.getPocer().length; m++) {
            if (room.getPocer()[m] != null) {
                publicCardNum ++;
            }
        }

        Pocer[] pocer = new Pocer[publicCardNum + 2];
        pocer[0] = aiRoomPersion.getPocers()[0];
        pocer[1] = aiRoomPersion.getPocers()[1];
        for (int m = 0; m < publicCardNum ; m++) {
            if(room.isCardControl()){ //如果开启了控制发牌则直接从设置好的公共牌里取
                pocer[m + 2] = room.getPocerLink().getPublicPoker(m);
            }else{
                if (Cache.p.getProperty("cards.control", "0").equals("1")) { // 指定发牌
                    pocer[m + 2] = room.getPocerLink().getPublicPoker(m);
                } else {
                    if (room.getPocer()[m] != null) {
                        pocer[m + 2] = room.getPocer()[m];
                    }
                }
            }
        }

        Object[] obj = BiPai.zuidapai1(pocer);
        pocer = (Pocer[]) obj[0];
        tempRoomPersion.setZuidaPocers(pocer);
        tempRoomPersion.setPocerType((Integer) obj[1]);
        tempRoomPersion.setStatus(aiRoomPersion.getStatus());
        tempRoomPersion.setOnlinerType(aiRoomPersion.getOnlinerType());
        tempRoomPersion.setPocers(aiRoomPersion.getPocers());
        //logger.debug("pocerType: " + tempRoomPersion.getPocerType());

        if(tempRoomPersion.getPocerType() == 1 || tempRoomPersion.getPocerType() == 2 || tempRoomPersion.getPocerType() == 3){//只有皇家同花顺、同花顺、四条牌型才算击中
            int maxPocerCount = 0;//公共牌在最大组合的数量
            logger.debug("person hand card [0]:" + tempRoomPersion.getPocers()[0].getSize1());
            logger.debug("person hand card [1]:" + tempRoomPersion.getPocers()[1].getSize1());
            for(Pocer maxPocer : pocer){
                //logger.debug("maxPocer "  + maxPocer.getSize1());
                if((tempRoomPersion.getPocers()[0].getSize1() == maxPocer.getSize1()) ||
                        (tempRoomPersion.getPocers()[1].getSize1() == maxPocer.getSize1())){
                    maxPocerCount++;
                }
            }
            //logger.debug("maxPocerCount: "  + maxPocerCount + " pocerType: " + tempRoomPersion.getPocerType());
            if((tempRoomPersion.getPocerType() == 1 || tempRoomPersion.getPocerType() == 2 ) && maxPocerCount == 2){
                return true;
            }

            /**
             * 四条需要特殊处理,当2张手牌的数字和四条中的数字都相同时才算击中
             */
            if(tempRoomPersion.getPocerType() == 3){
                int quadsPocerNumber = pocer[0].getSize2();  //四条牌型的数字
                logger.debug("quadsPocerNumber: " + quadsPocerNumber);
                if(quadsPocerNumber == tempRoomPersion.getPocers()[0].getSize2() && quadsPocerNumber == tempRoomPersion.getPocers()[1].getSize2()){
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 满足击中jp条件则需要点击亮牌
     * @param room
     * @param roomPersion
     */
    private static void mockShowCards(Room room, RoomPersion roomPersion){
        logger.debug("检测到玩家符合击中jp规则,需要亮手牌,房间id={},玩家id={}",room.getRoomId(),roomPersion.getUserId());

        Map<Integer, Object> map = new HashMap<>();
        map.put(131,room.getRoomId());
        map.put(132,room.getRoomPath());
        map.put(133,3);  // 亮牌id 0不亮 1第一张 2第二张 3两张
        Request request = new Request();
        request.setChannel(roomPersion.getUserInfo().getChannel());
        request.setUserId(roomPersion.getUserId());
        Task task = new Task(Constant.REQ_GAME_SHOW_CARDS, map, request, room.getRoomId(), room.getRoomPath());
        WorkThreadService.submit(room.getRoomId(), task);
    }
}
