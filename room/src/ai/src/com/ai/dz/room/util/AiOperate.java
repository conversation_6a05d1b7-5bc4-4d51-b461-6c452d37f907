package com.ai.dz.room.util;

import com.ai.dz.config.AiRuleTemplate;
import com.ai.dz.config.FlopAction;
import com.ai.dz.config.cache.IAiRuleConfigCache;
import com.ai.dz.config.constant.*;
import com.ai.dz.config.optype.*;
import com.ai.dz.room.constant.AiConstant;
import com.dzpk.common.utils.LogUtil;
import com.ai.dz.vigilance.AiVigilante;
import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;
import com.i366.cache.Cache;
import com.i366.model.player.RoomPersion;
import com.i366.model.player.RoomPlayer;
import com.i366.model.pocer.Pocer;
import com.i366.model.room.Room;
import com.i366.util.RoomUtil;
import org.apache.logging.log4j.Logger;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * at操作策略 区分翻牌前翻牌后
 */
public class AiOperate {

    private static final Logger logger = LogUtil.getLogger(AiOperate.class);

    /**
     * at策略 区分翻牌前 翻牌后
     * @param room
     * @param opRoomPersion at玩家
     * @param genzhu 需要跟注的值
     * @param isFisrtOperate 是否是本手第一操作人
     */
    public static void operate(Room room, RoomPersion opRoomPersion, int genzhu,boolean isFisrtOperate){
        logger.debug("执行at操作策略,房间={},玩家id={},需要的跟注值={},是否是每手第一行动人={}",room.getRoomId(),opRoomPersion.getUserId(),genzhu,isFisrtOperate);

        int totalPoolChip = 0;
        Integer[] poolChips = room.getRoomService().getPoolInfo(true);//计算总底池积分
        for(int chip : poolChips){
            totalPoolChip += chip;
        }

        int actionType = 0;  // 玩家最终的操作操作类型
        int betChouma = 0;   // 下注筹码
        int opTime = 2; //默认操作时间为2秒
        FlopAction action;  //玩家行动
        OperationType operationType = null;  //操作类型
        boolean normalAFAndFirstAt = false;
        int firstAFBetchip = 0; //第一个有激进行为的玩家下注筹码
        boolean isLastAfAt = false; //上轮最后一个激进的是否为at
        try {
            evaluateHandStrength(room);

            RoomPlayer roomPlayer = room.getRoomPlayers().get(opRoomPersion.getUserId());
            EAiType eAiType = roomPlayer == null ? EAiType.fromValue(1) : EAiType.fromValue(roomPlayer.getAiPlayer().getAiOpreateType());//默认为紧聪类型
            //暫不考慮玩家名单
            //eAiType = transferAiType(room,eAiType); //是否需要转换性格

            /**
             * 1、每一轮第一个激进的玩家必须是普通玩家
             * 2、第一个激进的普通玩家，激进时候下注筹码为betChip，必须满足下面条件
             * 3、以第一个激进的普通玩家为基础，顺序号位的第一个at才会执行该策略
             */

            if(room.getAttackId() > -1){

                logger.debug("第一个激进玩家是否为普通玩家: " + room.getIsfirstAfNormal());
                logger.debug("激进玩家后是否有at操作过: " + room.isAtOperated());
                if(room.getIsfirstAfNormal() == 1 && !room.isAtOperated()){
                    normalAFAndFirstAt = true;
                    room.setAtOperated(true);
                }

                firstAFBetchip = normalAFAndFirstAt ? room.getFirstAfBetChip() : 0;

            }

            // 2025/01/24
            // 目前 ai 策略不用做這個判斷
            // 並且如果玩家剛好離桌會導致 NPE
            //isLastAfAt = checkLastAfIsAt(room,opRoomPersion.getUserId());

            if(room.getRoomStatus() == 3){
                action = getPreFlopAction(room,opRoomPersion,genzhu,totalPoolChip,eAiType,normalAFAndFirstAt,firstAFBetchip,isLastAfAt);
            } else {
                action = flopAction(room,opRoomPersion,genzhu,totalPoolChip,eAiType,normalAFAndFirstAt,firstAFBetchip,isLastAfAt);
            }

            if (null != action) {
                actionType =action.getAction()!=null? action.getAction().value():EAction.fold.value();
                betChouma = action.getBetChip();
            }

            if(-1 == betChouma){//随机到raise操作 但是实际是要执行allin
                actionType = 5;
                betChouma = opRoomPersion.getNowcounma();
            }

            opTime = getAtOpTime(room,operationType,opRoomPersion,genzhu,totalPoolChip,betChouma,actionType,isFisrtOperate);

            Map<Integer, Object> map = new HashMap<Integer, Object>();
            map.put(1, opRoomPersion.getUserId());
            map.put(2, actionType);     // 操作类型
            map.put(3, betChouma);    // 下注筹码
            Task task = new Task(AiConstant.TASK_AI_AUTO_OPERATE, map, room.getRoomId(), room.getRoomPath(), opRoomPersion.getUserId()); // 20241118 add userId
            WorkThreadService.submitDelayTask(room.getRoomId(), task, opTime);
            room.roomProcedure.delayTaskMap.put(task.getId(),task);

        } catch (Exception e) {
            logger.error("执行at操作策略异常", e);
        }

    }


    /**
     * 获取翻牌前操作的分支类型
     * @param room
     * @param betChouma ai已经下注的筹码
     * @return
     */
    private static OperationType getPreflopOperationType(Room room,int betChouma){
        OperationType operationType = null;
        if(room.getAllinCount() >= 1){
            AllinType allinType = OperationFactory.createAllinType();
            allinType.setPlayerNumOfOp(room.getAllinCount());
            allinType.setPlayerNumOfHand(room.getBeginHandPlayerCount());
            allinType.setTotalChipOfHand(room.getTotalBeginHandChouma());
            allinType.setMaxChipOfAllin(room.getMaxAllinChouma());
            allinType.setRaisedChipOfHand(betChouma);
            operationType = allinType;
        }else if(room.getCallOrRaiseCount() > 0){
            BetRaiseCallType betRaiseCallType = OperationFactory.createBetRaiseCallType();
            betRaiseCallType.setPlayerNumOfOp(room.getCallOrRaiseCount());
            operationType = betRaiseCallType;
        }else {
            BBStradleType bbStradleType = OperationFactory.createBBStradleType();
            bbStradleType.setPlayerNumOfOp(room.getCallToBBorStraddleCount());
            operationType = bbStradleType;
        }

        return operationType;
    }

    /**
     * 获取翻牌后操作的分支类型
     * @param room
     * @param betChouma ai已经下注的筹码     * @return
     */
    private static OperationType getFlopOperationType(Room room,int betChouma){
        OperationType operationType = null;
        if(room.getAllinCount() >= 1){
            AllinType allinType = OperationFactory.createAllinType();
            allinType.setPlayerNumOfOp(room.getAllinCount());
            allinType.setPlayerNumOfHand(room.getBeginHandPlayerCount());
            allinType.setTotalChipOfHand(room.getTotalBeginHandChouma());
            allinType.setMaxChipOfAllin(room.getMaxAllinChouma());
            allinType.setRaisedChipOfHand(betChouma);
            operationType = allinType;
        }else {
            BetRaiseCallType betRaiseCallType = OperationFactory.createBetRaiseCallType();
            betRaiseCallType.setPlayerNumOfOp(room.getCallOrRaiseCount());
            operationType = betRaiseCallType;
        }

        return operationType;
    }

    /**
     * 翻牌前获取at行动
     * @param room
     * @param opRoomPersion
     * @param genzhu
     * @param totalPoolChip
     * @param eAiType
     * @param normalAFAndFirstAt
     * @param firstAFBetchip
     * @param isLastAfAt
     * @return
     */
    private static FlopAction getPreFlopAction(Room room,RoomPersion opRoomPersion,int genzhu,int totalPoolChip,EAiType eAiType,
                                                boolean normalAFAndFirstAt,int firstAFBetchip,boolean isLastAfAt){
        FlopAction action;

        EAiPosition eAiPosition = EAiPosition.EP; //暫不分辨 //AiPositon.getAbsolutePositon(room,opRoomPersion.getSize());//计算绝对位置
        OperationType operationType = getPreflopOperationType(room,opRoomPersion.getBetChouma());//获取操作的条件分支
        EPocerType pokerType = null;
        int preFlopActionWeightMin = 0;

        if (getVigilante(room).isThreatNeutralized() && getVigilante(room).isParticipating(opRoomPersion.getUserId())) {
            Pocer[] publicCard = getVigilante(room).peekNextDeal(5);
            EPocerType newPokerType = AiBipai.calPokerType(opRoomPersion,publicCard,publicCard.length);
            logger.debug("風險警報, 權重修正 {} -> {} - publicCard={}", pokerType, newPokerType, Arrays.toString(publicCard));
            pokerType = newPokerType;
            preFlopActionWeightMin = getVigilante(room).getAiPreFlopActionWeightMin();
        }

        logger.debug("翻牌前操作,比牌结果: isAiWin=" + opRoomPersion.isAiWin());
        if(opRoomPersion.isAiWin()){
            action = AiRuleTemplate.preflopAndWin(opRoomPersion.getUserId(),genzhu,totalPoolChip,
                    opRoomPersion.getNowcounma() /* ai剩余筹码 */,eAiType,
                    eAiPosition,opRoomPersion.getBetChouma(),
                    opRoomPersion.getHandBeginChip(),
                    operationType,normalAFAndFirstAt,firstAFBetchip, opRoomPersion.getNowcounma(),
                    isLastAfAt,opRoomPersion.getPocers(),pokerType,preFlopActionWeightMin,room);
        }else{
            action = AiRuleTemplate.preflopAndLoss(opRoomPersion.getUserId(),genzhu,totalPoolChip,
                    opRoomPersion.getNowcounma() /* ai剩余筹码 */,eAiType,
                    eAiPosition,opRoomPersion.getBetChouma() /* 已下注筹码 */ ,
                    opRoomPersion.getHandBeginChip() /* 本手初始筹码 */,
                    operationType,normalAFAndFirstAt,firstAFBetchip, opRoomPersion.getNowcounma(),
                    isLastAfAt,opRoomPersion.getPocers(),pokerType,room);
        }

        return action;
    }

    /**
     * 翻牌后获取at行动
     * @param room
     * @param opRoomPersion
     * @param genzhu
     * @param totalPoolChip
     * @param eAiType
     * @param normalAFAndFirstAt
     * @param firstAFBetchip
     * @param isLastAfAt
     * @return
     */
    private static FlopAction flopAction(Room room,RoomPersion opRoomPersion,int genzhu,int totalPoolChip,EAiType eAiType,
                                               boolean normalAFAndFirstAt,int firstAFBetchip,boolean isLastAfAt){
        FlopAction action;

        logger.debug("翻牌后操作,比牌结果: isAiWin=" + opRoomPersion.isAiWin());
        EAiPosition eAiPosition = EAiPosition.EP; //暫不分辨 //AiPositon.getRelativePositon(room,opRoomPersion.getSize()); //绝对位置
        int publicCardNum = RoomUtil.getPublicCardNum(room); //获取公共牌数量
        EFlopPhase eFlopPhase = EFlopPhase.of(publicCardNum); //根据公共牌数量返回对应的牌局处于哪个阶段
        EPocerType pokerType = AiBipai.calPokerType(opRoomPersion,room.getPocer(),publicCardNum);//重新计算牌型;

        if (getVigilante(room).isThreatNeutralized() && getVigilante(room).isParticipating(opRoomPersion.getUserId())) {
            Pocer[] publicCard = Arrays.copyOf(room.getPocer(), 5);
            Pocer[] peekNext = getVigilante(room).peekNextDeal(publicCard.length - publicCardNum);
            System.arraycopy(peekNext, 0, publicCard, publicCardNum, peekNext.length);
            EPocerType newPokerType = AiBipai.calPokerType(opRoomPersion,publicCard,publicCard.length);
            logger.debug("風險警報, 權重修正 {} -> {} - publicCard={}", pokerType, newPokerType, Arrays.toString(publicCard));
            pokerType = newPokerType;
        }

        OperationType operationType = getFlopOperationType(room,opRoomPersion.getBetChouma());

        if(opRoomPersion.isAiWin()){//调用AI工具类，翻牌后赢逻辑
            action = AiRuleTemplate.flopAndWin(opRoomPersion.getUserId(),genzhu,totalPoolChip,
                    opRoomPersion.getNowcounma() /* ai剩余筹码 */,eAiType,eAiPosition,
                    opRoomPersion.getBetChouma(),opRoomPersion.getHandBeginChip(),operationType,
                    opRoomPersion.getPocers(), pokerType,normalAFAndFirstAt,firstAFBetchip,opRoomPersion.getNowcounma(),
                    isLastAfAt,eFlopPhase,room);
        } else {//调用AI工具类，翻牌后输逻辑
            action = AiRuleTemplate.flopAndLoss(opRoomPersion.getUserId(),genzhu,totalPoolChip,
                    opRoomPersion.getNowcounma(), eAiType,eAiPosition,
                    opRoomPersion.getBetChouma() /* 已下注筹码 */ , opRoomPersion.getHandBeginChip() /* 本手初始筹码 */, operationType,
                    opRoomPersion.getPocers(), pokerType,normalAFAndFirstAt,firstAFBetchip,opRoomPersion.getNowcounma(),
                    isLastAfAt,eFlopPhase,room);
        }

        return action;
    }

    /**
     * 获取at随机操作时间
     * @param room
     * @param operationType
     * @param opRoomPersion
     * @param genzhu
     * @param totalPoolChip
     * @param betChouma
     * @param actionType
     * @param isFisrtOperate
     * @return
     */
    private static int getAtOpTime(Room room,OperationType operationType,RoomPersion opRoomPersion,
                                   int genzhu,int totalPoolChip,int betChouma,int actionType,boolean isFisrtOperate){
        int opTime = 2;
        try {

            boolean isBBOrStradle = false;
            if(operationType instanceof BBStradleType){ //判断是否是bb/straddle
                isBBOrStradle = true;
            }

            opTime =  AiRuleTemplate.randomOpTime(room.getRoomId(),opRoomPersion.getUserId(),isBBOrStradle,genzhu,room.getDamanzhu(),totalPoolChip, actionType,betChouma);//调用生成随机操作时间的方法
            if(isFisrtOperate){
                logger.debug("当前操作为每手第一操作人,需要延时操作,随机到的操作时间={}",opTime);
                opTime = opTime + getOptimePreflopDelay();
                logger.debug("延时后的操作时间={}",opTime);

            }

            if(room.getRoomStatus() > 3 && opRoomPersion.getSize() == room.getFirstOpFlopSize()){
                logger.debug("当前操作为翻牌后每轮第一操作人,需要延时操作,随机到的操作时间={}",opTime);
                opTime = opTime + getOptimeFlopDelay();
                logger.debug("延时后的操作时间={}",opTime);
            }

        } catch (Exception e) {
            logger.error("执行at操作时间异常", e);
        }

        return opTime;
    }
    private static int getOptimePreflopDelay() {
        return Integer.parseInt(Cache.p.getProperty("ai.optime.preflopDelay", "4"));
    }
    private static int getOptimeFlopDelay() {
        return Integer.parseInt(Cache.p.getProperty("ai.optime.flopDelay", "2"));
    }
    /*
    / **
     * 判断at每次操作前的最后一个激进玩家（bet或者raise或者allin）。
     *  如果是黑名单，则任意性格转换为highbalance 性格；
     *  如果是绿/蓝/白名单，则任意性格转换为normalfool性格；
     *  如果不是灰/黑/绿/蓝/白名单，则使用默认性格不用转化
     *
     * @param room
     * @param eAiType 默认性格
     * @return
     * /
    private static EAiType transferAiType(Room room,EAiType eAiType){
        int lastAfUserId = 0;

        if(room.getAttackId() > -1){
            RoomPersion attacker = room.getRoomPersions()[room.getAttackId()]; //激进玩家
            if(null != attacker){
                lastAfUserId = attacker.getUserId();
            }
        }

        if(lastAfUserId > 0){
            RoomPlayer afRoomPlayer = room.getRoomPlayers().get(lastAfUserId);
            if(UserLevelCode.BLACK == afRoomPlayer.getUserLevelCode()){
                eAiType = EAiType.highBalance;
                logger.debug("最后一个激进玩家为黑名单,转换性格为highbalance");
            }else if(UserLevelCode.GREEN == afRoomPlayer.getUserLevelCode() ||
                    UserLevelCode.BLUE == afRoomPlayer.getUserLevelCode() || UserLevelCode.WHITE == afRoomPlayer.getUserLevelCode()){
                eAiType = EAiType.normalFool;
                logger.debug("最后一个激进玩家为绿/蓝/白名单,转换性格为normalfool");
            }else{
                logger.debug("使用默认性格不用转化");
            }
        }

        return eAiType;
    }*/

    /**
     * 如果本轮最后激进的玩家是at，在下一轮行动的时候，该激进的at应用副表的配置表文件，其他at还是用主表配置文件。
     * 如果本轮最后激进玩家不是at，则下轮行动所有at都是使用主表。
     *
     * 每一轮最后激进的玩家都有可能不同，下一轮应用副表的配置表文件行动的at也不一样）
     * 每一手开始和每一轮结束都需要重置
     *
     * @param room 房间
     * @param opUserId at玩家id
     * @return
     */
    private static boolean checkLastAfIsAt(Room room,int opUserId){
        if(room.getPreAttackId() > -1){
            RoomPersion lastAttackPerson = room.getRoomPersions()[room.getPreAttackId()];
            logger.debug("检查上一轮操作的玩家是否为at,rid={},opUserId={},afUserId={}",room.getRoomId(),opUserId,lastAttackPerson.getUserId());
            return opUserId == lastAttackPerson.getUserId();
        }
        return false;
    }

    /**
     * 判斷 ai 手牌強弱
     *
     * @param room
     */
    private static void evaluateHandStrength(Room room) {
        IAiRuleConfigCache config = IAiRuleConfigCache.getInstance();
        for (RoomPersion roomPersion : room.getRoomPersions()) {
            if (null != roomPersion && AiRuleTemplate.isAiUser(roomPersion.getUserId())) {
                if (config.isEnableBipai()) {
                    AiBipai.biPai(roomPersion,room);
                } else {
                    roomPersion.setAiWin(true);
                }
            }
        }
    }

    private static AiVigilante getVigilante(Room room) {
        return (AiVigilante) room.getVigilante();
    }
}
