package com.ai.dz.room.model.card;


import com.ai.dz.config.constant.EPocerType;
import com.dzpk.common.utils.LogUtil;
import com.i366.model.pocer.Pocer;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class PokerUtils {

    private static Logger logger = LogUtil.getLogger(PokerUtils.class);

    /**
     * 获取牌型, 可能返回空
     *
     * @param publicPocers 公共牌
     * @param handPocers   手牌
     * @return
     */
    public static EPocerType getCardType(List<Pocer> publicPocers, List<Pocer> handPocers) {

        if (isFlushStraight(publicPocers, handPocers)) {
            return EPocerType.chouHuaChou2TouShun;
        }
        if (isPairFlush(publicPocers, handPocers)) {
            return EPocerType.zhongDuiChouHua;
        }
        if (isPumpFlush(publicPocers, handPocers)) {
            return EPocerType.chouHua;
        }
        if (isPairStraight(publicPocers, handPocers)) {
            return EPocerType.zhongDuiChou2TouShun;
        }
        if (isBothSidesStraight(publicPocers, handPocers)) {
            return EPocerType.chou2TouShun;
        }
        return EPocerType.gaopai;
    }

    /**
     * 是否为双头顺
     * 描述:
     *
     * @param publicPocers 公共牌
     * @param handPocers   手牌
     * @return 是否为双头顺
     */
    public static boolean isBothSidesStraight(List<Pocer> publicPocers, List<Pocer> handPocers) {
        return isBothSidesStraight(publicPocers, handPocers, 2);
    }

    /**
     * 是否为双头顺
     *
     * @param publicPocers     公共牌
     * @param handPocers       手牌
     * @param useHandPocerSize 至少使用几张手牌, 1 or 2
     * @return
     */
    private static boolean isBothSidesStraight(List<Pocer> publicPocers, List<Pocer> handPocers, int useHandPocerSize) {
        if (useHandPocerSize < 1 || useHandPocerSize > 2) {
            throw new IllegalArgumentException("user HandPocerSize必须是1或2");
        }
        for (; useHandPocerSize <= 2; useHandPocerSize++) {
            Combination<Pocer> handCombination = new Combination<>(handPocers, useHandPocerSize);
            while (handCombination.hasNext()) {
                List<Pocer> pocers = handCombination.next();
                //组双头顺需要4张牌, 手牌如果只使用1张, 公共牌需要使用3张
                Combination<Pocer> publicCombination = new Combination<>(publicPocers, 4 - useHandPocerSize);
                while (publicCombination.hasNext()) {
                    List<Pocer> next = publicCombination.next();
                    next.addAll(pocers);
                    boolean bothSidesStraight = isBothSidesStraight(next);
                    if (bothSidesStraight) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private static boolean isBothSidesStraight(List<Pocer> Pocers) {
        if (Pocers.size() != 4) {
            return false;
        }
        //从小到大排序  2, 3, 4 ...K, A
        Pocers.sort(Comparator.comparing(Pocer::getSize2));
        Pocer lastPocer = Pocers.get(Pocers.size() - 1);
        if (lastPocer.getSize2() == 14) {
            //最后一张是A, 不是双头顺
            return false;
        }
        //判断是否按顺序递增
        List<Integer> nums = Pocers.stream().map(Pocer::getSize2).collect(Collectors.toList());
        return isIncrement(nums, 1);
    }

    /**
     * 是否为中对抽双头顺
     *
     * @param publicPocers 公共牌
     * @param handPocers   手牌
     * @return 是否为中对抽双头顺
     */
    public static boolean isPairStraight(List<Pocer> publicPocers, List<Pocer> handPocers) {
        if (hasOnePair(publicPocers)) {
            //公共牌有一对, 不算
            return false;
        }
        if (publicPocers.size() == 4 && isBothSidesStraight(publicPocers)) {
            //公共牌已成双头顺, 不算
            return false;
        }
        boolean bothSidesStraight = isBothSidesStraight(publicPocers, handPocers, 1);
        if (!bothSidesStraight) {
            //不是双头顺
            return false;
        }
        //这里已经是双头顺
        //上面已经判断公共牌没有一对, 这里如果能组成一对, 可以确定一定使用了至少一张手牌
        List<Pocer> allPocers = new ArrayList<>(publicPocers);
        allPocers.addAll(handPocers);
        return hasOnePair(allPocers);
    }

    /**
     * 是否为抽花
     *
     * @param publicPocers 公共牌
     * @param handPocers   手牌
     * @return 是否为抽花
     */
    public static boolean isPumpFlush(List<Pocer> publicPocers, List<Pocer> handPocers) {
        Map<Integer, Set<Pocer>> groupMap = handPocers.stream().collect(Collectors.groupingBy(Pocer::getType, Collectors.toSet()));
        if (groupMap.size() != 1) {
            //有两种或以上的花色
            return false;
        }
        //公共牌只需要有两张同种花色的牌
        return calcColorNum(publicPocers, handPocers.get(0).getType()) == 2;
    }

    /**
     * 是否为中对抽花
     *
     * @param publicPocers 公共牌
     * @param handPocers   手牌
     * @return 是否为中对抽花
     */
    public static boolean isPairFlush(List<Pocer> publicPocers, List<Pocer> handPocers) {
        if (!isPumpFlush(publicPocers, handPocers)) {
            //不是抽花
            return false;
        }
        //判断是否至少一张手牌和公共牌组成一对
        Set<Integer> publicPocerNums = publicPocers.stream().map(Pocer::getSize2).collect(Collectors.toSet());
        return handPocers.stream().anyMatch(handPocer -> publicPocerNums.contains(handPocer.getSize2()));
    }

    /**
     * 是否为抽花双头顺
     *
     * @param publicPocers 公共牌
     * @param handPocers   手牌
     * @return 是否为抽花双头顺
     */
    private static boolean isFlushStraight(List<Pocer> publicPocers, List<Pocer> handPocers) {
        //两张手牌花色一直与公共牌组成抽花 (这里已经判断公共牌不为同花了)
        if (!isPumpFlush(publicPocers, handPocers)) {
            return false;
        }
        if (publicPocers.size() == 4 && isBothSidesStraight(publicPocers)) {
            //公共牌已成双头顺, 不算
            return false;
        }
        //至少一张手牌与公共牌组成双头顺
        return isBothSidesStraight(publicPocers, handPocers, 1);
    }

    /**
     * 是否递增
     *
     * @param nums 数字集合
     * @param step 递增步数
     * @return
     */
    private static boolean isIncrement(List<Integer> nums, int step) {
        for (int i = 0; i < nums.size(); i++) {
            if (i == nums.size() - 1) {
                continue;
            }
            int current = nums.get(i);
            int next = nums.get(i + 1);
            if (next - current != step) {
                return false;
            }
        }
        return true;
    }

    /**
     * 是否含有一对
     *
     * @param Pocers
     * @return
     */
    private static boolean hasOnePair(List<Pocer> Pocers) {
        return Pocers.stream().map(Pocer::getSize2)
                .collect(Collectors.groupingBy(k -> k, Collectors.counting()))
                .values().stream().anyMatch(num -> num > 1);
    }

    /**
     * 计算同种花色的牌有多少张
     *
     * @param Pocers
     * @param type   花色
     * @return
     */
    private static int calcColorNum(List<Pocer> Pocers, int type) {
        return (int) Pocers.stream().filter(Pocer -> type == Pocer.getType()).count();
    }
}
