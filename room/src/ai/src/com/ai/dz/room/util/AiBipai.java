package com.ai.dz.room.util;

import com.ai.dz.config.constant.EPocerType;
import com.ai.dz.room.model.card.PokerUtils;
import com.i366.cache.Cache;
import com.i366.model.player.RoomPersion;
import com.i366.model.pocer.PocerLink;
import com.i366.model.room.Room;
import com.i366.model.pocer.Pocer;
import com.dzpk.common.utils.LogUtil;
import com.i366.room.RoomService;
import com.i366.util.RoomUtil;
import org.apache.logging.log4j.Logger;

import java.util.Arrays;
import java.util.List;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * Created by peterguo on 2018/9/17.
 */
public class AiBipai {

    private static final Logger logger = LogUtil.getLogger(AiBipai.class);

    /**
     * 翻牌后当ai玩家需要操作的时候需要与其他玩家进行比牌 (平局也算赢)
     * 牌力大或者相同都算赢
     * AI玩家比所有玩家都大才算赢
     * 只需要跟不弃牌的玩家比
     * @return
     */
    public static void biPai(RoomPersion aiRoomPersion,Room room){
        biPai(aiRoomPersion, room, 7, () -> getPublicPocer(room));
    }

    public static void biPai(RoomPersion aiRoomPersion, Room room, int numPocers, Supplier<Pocer[]> publicCardSupplier){
        if (logger.isTraceEnabled()) {
            logger.trace("aiBipai: rid={} stage={} numPocers={} publicCard={}", room.getRoomId(), room.getStage(), numPocers, Arrays.toString(publicCardSupplier.get()));
        }
        RoomPersion aiTempRoomPersion = getTempPersion(aiRoomPersion,numPocers,publicCardSupplier);
        aiRoomPersion.setAiWin(true);
        for (RoomPersion roomPersion : room.getRoomPersions()) {
            if (roomPersion != null && roomPersion.getBetChouma() >= room.getMinChip() && roomPersion.getUserId() != aiRoomPersion.getUserId()
                    && roomPersion.getStatus() != -3) {

                RoomPersion bipaiPersion = getTempPersion(roomPersion,numPocers,publicCardSupplier);

                if(RoomService.bipai2(aiTempRoomPersion,bipaiPersion) > 0) {
                    aiRoomPersion.setAiWin(false);
                    if (logger.isTraceEnabled()) {
                        logger.trace("my uid={} type={} hand={}", aiRoomPersion.getUserId(), aiTempRoomPersion.getPocerType(), Arrays.toString(aiTempRoomPersion.getZuidaPocers()));
                        logger.trace("her uid={} type={} hand={}", roomPersion.getUserId(), bipaiPersion.getPocerType(), Arrays.toString(bipaiPersion.getZuidaPocers()));
                    }
                    break;
                }
            }
        }

        logger.trace("aiBiPai: rid={} stage={} status={} uid={} win={} ============================= ", room.getRoomId(), room.getStage(), room.getRoomStatus(), aiRoomPersion.getUserId(), aiRoomPersion.isAiWin());
    }

    /**
     * 获取临时RoomPersion，用于ai比牌
     * @param roomPersion
     * @param numPocers 用多少張牌去比 (5-7)
     * @param publicCardSupplier
     * @return
     */
    private static RoomPersion getTempPersion(RoomPersion roomPersion,int numPocers,Supplier<Pocer[]> publicCardSupplier) {
        RoomPersion tempRoomPersion = new RoomPersion();
        Pocer[] pocer = new Pocer[numPocers];
        pocer[0] = roomPersion.getPocers()[0];
        pocer[1] = roomPersion.getPocers()[1];

        Pocer[] publicPocer = publicCardSupplier.get();//获取公共牌
        for (int i = 0; i < numPocers - 2 ; i++) {
            pocer[i + 2] = publicPocer[i];
        }
        Object[] obj = RoomService.zuidapai1(pocer);
        pocer = (Pocer[]) obj[0];
        tempRoomPersion.setZuidaPocers(pocer);
        tempRoomPersion.setPocerType((Integer) obj[1]);
        tempRoomPersion.setStatus(roomPersion.getStatus());
        tempRoomPersion.setOnlinerType(roomPersion.getOnlinerType());
        return tempRoomPersion;
    }


    /**
     * 获取牌局的公共牌
     * @param room
     * @return
     */
    private static Pocer[] getPublicPocer(Room room){
        if(room.getAiPocer() != null){
            if (room.getVigilante().isThreatNeutralized()) {
                // 追擊發動時需要特別處理
                int publicCardNum = RoomUtil.getPublicCardNum(room); //获取公共牌数量
                Pocer[] publicCard = Arrays.copyOf(room.getPocer(), 5);
                Pocer[] peekNext = room.getVigilante().peekNextDeal(publicCard.length - publicCardNum);
                System.arraycopy(peekNext, 0, publicCard, publicCardNum, peekNext.length);
                return publicCard;
            }
            return room.getAiPocer();
        }else{
            Pocer[] pocer = new Pocer[5];
            PocerLink pocerLink = room.getPocerLink();
            //如果开启了控制发牌则直接从设置好的公共牌里取
            if(room.isCardControl() || Cache.p.getProperty("cards.control", "0").equals("1")){
                pocer = pocerLink.getPublicPoker();
            } else {
                for (int i = 0; i < 5 ; i++) {
                    pocer[i] = pocerLink.getPocer(pocerLink.getIndex() + i);
                }
            }
            room.setAiPocer(pocer);
            return pocer;
        }
    }

    /**
     * 细分计算ai的牌型
     * @param roomPersion
     * @param publicCard
     * @param publicCardNum
     */
    public static EPocerType calPokerType(RoomPersion roomPersion, Pocer[] publicCard, int publicCardNum){

        logger.debug("publicCardNum: " + publicCardNum);
        int pocerType = 10; //默认为高牌

        Pocer[] pocer = new Pocer[publicCardNum + 2];
        pocer[0] = roomPersion.getPocers()[0];
        pocer[1] = roomPersion.getPocers()[1];

        Pocer[] pulicPocer = new Pocer[publicCardNum];  //公共牌
        for (int m = 0; m < publicCardNum ; m++) {
            if (publicCard[m] != null) {
                pocer[m + 2] = publicCard[m];
                pulicPocer[m] = publicCard[m];
            }
        }

        Object[] obj = RoomService.zuidapai1(pocer);
        pocer = (Pocer[]) obj[0];
        pocerType = (int) obj[1];

        EPocerType finalType;

        switch (pocerType) { // 1皇家同花 2同花顺 3四条 4葫芦 5同花 6顺子 7三条 8两对 9一对 10高牌
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
                finalType = calPokerTypeByType(pocer, roomPersion.getPocers(), pocerType);
                break;
            case 9:
                finalType = calOneParis(pocer, pulicPocer, publicCardNum);
                break;
            case 10:
                finalType = calHighPoker(roomPersion.getPocers());
                break;
            default:
                finalType = EPocerType.kingTonghuashun;
        }

        if(finalType.value() > EPocerType.shunzi2.value() || finalType == EPocerType.shunzi0){ //如果牌型小于同花顺则需要在执行一次新牌型的计算 取最大的牌型为最终的牌型
            if(pulicPocer.length <= 4 && pulicPocer.length >= 3){  //公共牌3-4张时才需要重新计算牌型
                printPocers(Arrays.asList(pulicPocer),Arrays.asList(roomPersion.getPocers()));
                EPocerType newPokerType = PokerUtils.getCardType(Arrays.asList(pulicPocer),Arrays.asList(roomPersion.getPocers()));
                if (newPokerType.value() != EPocerType.gaopai.value()) {
                    logger.debug("重新计算后的牌型={}",newPokerType);
                    finalType = newPokerType;
                }
            }
        }

        logger.debug("at玩家计算权重的最终牌型={}",finalType);
        return finalType;
    }

    /**
     * 通过牌型类型和最大牌型组合包含手牌数量来细分牌型 (2同花顺 3四条 4葫芦 5同花 6顺子 7三条 8两对)
     * 手牌0张
     * 手牌1张
     * 手牌2张
     * @param pocer 最大的牌型
     * @param handPocer 手牌
     */
    private static EPocerType calPokerTypeByType(Pocer[] pocer, Pocer[] handPocer, int pocerType) {
        int maxHandPocers = 0;//手牌在最大组合的数量

        logger.debug("pocer={} handPocer={} pocerType={}", Arrays.toString(pocer), Arrays.toString(handPocer), pocerType);

        int length = pocer.length;
        if (pocerType == 7) { // 三条是放在最大牌型组合中的前面
            length = 3;
        } else if (pocerType == 8) { // 两对是放在最大牌型组合中的前面
            length = 4;
        }
        for (int i = 0; i < length && maxHandPocers < 2; i++) {
            Pocer maxPocer = pocer[i];
            if ((handPocer[0].getSize1() == maxPocer.getSize1()) || handPocer[1].getSize1() == maxPocer.getSize1()) {
                maxHandPocers++;
            }
        }

        EPocerType type;
        logger.debug("maxHandPocers={}", maxHandPocers);
        switch (pocerType) { // 2同花顺 3四条 4葫芦 5同花 6顺子 7三条 8两对
            case 2:
                if (maxHandPocers == 0) { //手牌0张
                    type =  EPocerType.tonghuashun0;
                } else if (maxHandPocers == 1) { //手牌1张
                    type =  EPocerType.tonghuashun1;
                } else { //手牌2张
                    type =  EPocerType.tonghuashun2;
                }
                break;
            case 3:
                if (maxHandPocers == 0) { //手牌0张
                    type =  EPocerType.sitiao0;
                } else if (maxHandPocers == 1) { //手牌1张
                    type =  EPocerType.sitiao1;
                } else { //手牌2张
                    type =  EPocerType.sitiao2;
                }
                break;
            case 4:
                if (maxHandPocers == 0) { //手牌0张
                    type =  EPocerType.hulu0;
                } else if (maxHandPocers == 1) { //手牌1张
                    type =  EPocerType.hulu1;
                } else { //手牌2张
                    type =  EPocerType.hulu2;
                }
                break;
            case 5:
                if (maxHandPocers == 0) { //手牌0张
                    type =  EPocerType.tonghua0;
                } else if (maxHandPocers == 1) { //手牌1张
                    type =  EPocerType.tonghua1;
                } else { //手牌2张
                    type =  EPocerType.tonghua2;
                }
                break;
            case 6:
                if (maxHandPocers == 0) { //手牌0张
                    type =  EPocerType.shunzi0;
                } else if (maxHandPocers == 1) { //手牌1张
                    type =  EPocerType.shunzi1;
                } else { //手牌2张
                    type =  EPocerType.shunzi2;
                }
                break;
            case 7:
                if (maxHandPocers == 0) { //手牌0张
                    type =  EPocerType.santiao0;
                } else if (maxHandPocers == 1) { //手牌1张
                    type =  EPocerType.santiao1;
                } else { //手牌2张
                    type =  EPocerType.santiao2;
                }
                break;
            case 8:
                if(maxHandPocers == 0){ //2对比脚
                    type =  EPocerType.liangdui0;
                }else if(maxHandPocers == 1){ //手牌1张
                    type =  EPocerType.liangdui1;
                }else { //手牌2张
                    type =  EPocerType.liangdui2;
                }
                break;
            default:
                throw new IllegalArgumentException("pocerType " + pocerType);
        }
        return type;
    }

    /**
     * 计算一对
     * 顶对
     * 中对
     * 底对
     * 一对比脚
     * 如果牌型是一对的话，则第一张和第二张是一对且后边的牌按从小到大排列
     * @param pocer 最大的牌型
     * @param publicPocer 公共牌
     * */
    private static EPocerType calOneParis(Pocer[] pocer,Pocer[] publicPocer,int publicCardNum){
        int maxPublicPocers = 0; //一对在公共牌里面的数量

        for (Pocer pPocer : publicPocer) {
            if(pPocer != null){
                if (pPocer.getSize1() == pocer[0].getSize1() || pPocer.getSize1() == pocer[1].getSize1()) {
                    maxPublicPocers++;
                }
            }
        }
        //logger.debug("maxPublicPocers: " + maxPublicPocers);

        if(maxPublicPocers == 2){//都是公共牌的话就是一对比脚
            return EPocerType.yiduibijiao;
        }else{
            sortPocer(publicPocer);  //将公共牌由小到大进行排序
//            for(Pocer pocer1: publicPocer){
//                logger.debug("pocer: " + pocer1.getName() + " " + pocer1.getSize2());
//            }
            if(pocer[0].getSize2() <= publicPocer[0].getSize2()){//比第一张还小 底对
                return EPocerType.didui;
            }else if(pocer[0].getSize2() >= publicPocer[publicCardNum-1].getSize2()){//比最后一张还大 顶对
                return EPocerType.dingdui;
            }else{ //中对
                return EPocerType.zhongdui;
            }
        }
    }

    /**
     * 计算高牌
     * 手牌为AK
     * @param handPocer 手牌
     */
    private static EPocerType calHighPoker(Pocer[] handPocer){
        int maxHandPocers = 0;//手牌在最大组合的数量

        for(Pocer pocer : handPocer){
            if(pocer.getSize2() == 13 || pocer.getSize2() == 14){
                maxHandPocers++;
            }
        }

        //logger.debug("maxHandPocers: " + maxHandPocers);

        if(maxHandPocers == 2){
            return EPocerType.akgaopai;
        }else{
            return EPocerType.gaopai;
        }
    }

    /**
     * 按从小到大对集合中的牌进行排序
     */
    private static void sortPocer(Pocer[] pocer){
        for (int i = 0; i < pocer.length; i++) {
            for (int j = 0; j < pocer.length - i - 1; j++) {
                if (pocer[j].getSize2() > pocer[j + 1].getSize2()){// 大的往上冒,由小到大
                    swap(pocer, j, j + 1);
                }
            }
        }
    }

    protected static void swap(Object[] array, int i, int j) {
        Object temp;
        temp = array[i];
        array[i] = array[j];
        array[j] = temp;
    }

    /**
     * 打印牌
     * @param publicPokers
     * @param handPokers
     */
    private static void printPocers(List<Pocer> publicPokers, List<Pocer> handPokers) {
        List<String> publicNames = publicPokers.stream()
                .map(poker -> poker.getName() + poker.getNumber().toString())
                .collect(Collectors.toList());
        List<String> handNames = handPokers.stream()
                .map(poker -> poker.getName() + poker.getNumber().toString())
                .collect(Collectors.toList());
        logger.debug("公共牌={},手牌={}",publicNames,handNames);
    }
}
