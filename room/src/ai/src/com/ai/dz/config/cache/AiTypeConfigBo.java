
package com.ai.dz.config.cache;

import com.ai.dz.config.constant.EAiType;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Getter
public class AiTypeConfigBo {
    private final int value;
    private final EAiType type;

    public static AiTypeConfigBo initialize(String name,int value, String label) {
        return new AiTypeConfigBo(name,value,label);
    }

    private AiTypeConfigBo(String name,int value, String label) {
        this.value = value;
        this.type = new EAiType(name,value,label);
    }
}

