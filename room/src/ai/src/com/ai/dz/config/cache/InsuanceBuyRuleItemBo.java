package com.ai.dz.config.cache;


import com.dzpk.common.utils.Helper;
import com.dzpk.common.utils.LogHelper;

public class InsuanceBuyRuleItemBo {
    /** outs范围 :小于0则不考虑 */
    private int minOuts;
    private int maxOuts;

    /**
     * 买/不买的比例
     * 小于等于0，则不考虑
     */
    private int buyRatio;
    private int notbuyRatio;

    /**
     * 购买值的比例范围
     * 小于等于0，则不考虑
     */
    private int priceMinRatio;
    private int priceMaxRatio;

    /**
     * 检查配置是否匹配
     * @param outs  当前请求的outs，必填
     * @return
     *    true : 匹配
     *    false : 不匹配
     */
    public boolean checkIfMatch(int outs){
        boolean result = false;

        if(this.minOuts<0 && outs <= this.maxOuts)
            result = true;
        else if(this.maxOuts<0 && outs >= this.minOuts)
            result = true;
        else if(this.minOuts<= outs && outs <= this.maxOuts)
            result = true;

        LogHelper.log("%s   check if match : outs=%s,minOuts=%s,maxOuts=%s -> %s",
                System.lineSeparator(),outs,this.minOuts,this.maxOuts,result);
        return result;
    }

    /**
     * 选择是否购买
     * 必须调用checkIfMath()==true时调用
     * @return
     *    true : 购买
     *    false : 不购买
     */
    public boolean selectBuyOrNot(){
        boolean result = false;

        int maxRatio = this.buyRatio + this.notbuyRatio;
        double selector = Helper.randomDoubleBy(0,maxRatio);
        selector = Helper.fixDecimalPlace(selector);

        if(this.buyRatio!=0 && selector<this.buyRatio){
            result = true;
        }

        LogHelper.log("%s   select buyOrNot : buyRatio=%s,notbuyRatio=%s,maxRatio=%s,selector=%s -> %s",
                System.lineSeparator(),this.buyRatio,this.notbuyRatio,maxRatio,selector, result);
        return result;
    }

    /**
     * 确定购买之后，则随机产生购买额的比例
     *
     * @return
     *
     *   >0   : 购买额的比例
     *   <=0  : 不购买
     */
    public int randomPriceRatio(){
        int priceRatio = 0;

        if((this.priceMinRatio<=0 && this.priceMaxRatio<=0) ||
                this.priceMinRatio>this.priceMaxRatio)
            LogHelper.log("%s   random price ratio : minRatio=%s,maxRatio=%s -> %s",
                    System.lineSeparator(),this.priceMinRatio,this.priceMaxRatio,
                    "minRatio/maxratio MUST greater than 0 and minRatio<=maxRatio.");

        if(this.priceMinRatio<=0 && this.priceMaxRatio>0)
            priceRatio = this.priceMaxRatio;
        else if(this.priceMinRatio>0 && this.priceMaxRatio<=0)
            priceRatio = this.priceMinRatio;
        else if(this.priceMaxRatio>0 && this.priceMaxRatio>0 &&
                this.priceMinRatio<= this.priceMaxRatio){
            priceRatio = Helper.randomIntBy(this.priceMinRatio,this.priceMaxRatio);
        }

        LogHelper.log("%s   random price ratio : minRatio=%s,maxRatio=%s -> %s",
                System.lineSeparator(),this.priceMinRatio,this.priceMaxRatio,
                priceRatio);
        return priceRatio;
    }

    private InsuanceBuyRuleItemBo(int minOuts, int maxOuts,
                                  int buyRatio, int notbuyRatio,
                                  int priceMinRatio, int priceMaxRatio){
        this.minOuts = minOuts;
        this.maxOuts = maxOuts;
        this.buyRatio = buyRatio;
        this.notbuyRatio = notbuyRatio;
        this.priceMinRatio = priceMinRatio;
        this.priceMaxRatio = priceMaxRatio;
    }

    public static InsuanceBuyRuleItemBo initialize(int minOuts, int maxOuts,
                                                   Integer buyRatio, Integer nobuyRatio,
                                                   Integer priceMinRatio, Integer priceMaxRatio){
        if(null == buyRatio || buyRatio.doubleValue()<=0)
            buyRatio = 0;
        if(null == nobuyRatio || nobuyRatio.doubleValue()<=0)
            nobuyRatio = 0;

        if(null == priceMinRatio || priceMinRatio.intValue()<=0)
            priceMinRatio = 0;

        if(null == priceMaxRatio || priceMaxRatio.intValue()<=0)
            buyRatio = 0;
        return new InsuanceBuyRuleItemBo(minOuts, maxOuts,buyRatio,nobuyRatio,priceMinRatio,priceMaxRatio);
    }

    public int getMinOuts() {
        return minOuts;
    }

    public int getMaxOuts() {
        return maxOuts;
    }

    public int getBuyRatio() {
        return buyRatio;
    }

    public int getNotbuyRatio() {
        return notbuyRatio;
    }

    public int getPriceMinRatio() {
        return priceMinRatio;
    }

    public int getPriceMaxRatio() {
        return priceMaxRatio;
    }
}
