package com.ai.dz.config;

import com.ai.dz.config.cache.*;
import com.ai.dz.config.constant.*;
import com.dzpk.commission.repositories.mysql.ClubTribeDao;
import com.dzpk.commission.repositories.mysql.impl.ClubTribeDaoImpl;
import com.dzpk.commission.repositories.mysql.model.ClubStatusPo;
import com.dzpk.commission.repositories.mysql.model.ClubTribeStatusPo;
import com.dzpk.common.utils.GsonHelper;
import com.dzpk.common.utils.Helper;
import com.dzpk.common.utils.LogHelper;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.repositories.mysql.dbpool.DBUtil;
import com.dzpk.component.repositories.redis.RedisService;
import com.dzpk.db.dao.UserInfoDao;
import com.dzpk.db.imp.UserInfoDaoImp;
import com.dzpk.db.model.UserAccount;
import com.google.gson.Gson;
import com.i366.cache.Cache;
import com.i366.model.player.RoomPlayer;
import com.i366.model.room.Room;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.Logger;

import java.sql.Connection;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 牌局内AI管理器
 */
public final class AiRoomManager {
    /**
     * 日志服务
     */
    private static final Logger logger = LogUtil.getLogger(AiRuleTemplate.class);

    /**
     * 换行符
     */
    private static final String NEWLINE = System.lineSeparator();

    /**
     * 获取用户的AI模式
     *
     * @param roomId 牌局ID，可选，用于输出日志
     * @param userId 玩家ID，必填
     * @return null   正常玩家
     */
    public static EAiMode getModeOfPlayer(int roomId, int userId) {
        EAiMode result = null;
        IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
        UserOptypeConfigBo userConfig = cache.getUserTypeConfig(userId);
        UserConfigBo autoUserConfig = cache.getAutoUserConfig(userId);

        if (null != userConfig)
            result = EAiMode.manual;
        else if (null != autoUserConfig)
            result = EAiMode.auto;

        //logger.debug("获取玩家模式:rid={} , uid={} 返回结果: {} ！", roomId, userId, result);
        return result;
    }

    /**
     * 获取AI类型
     *
     * @param roomId
     * @param roomPath
     * @param userId
     * @return
     */
    public static int getAiOperateType(int roomId, int roomPath, int userId) {
        return getAiOperateType(Cache.getRoom(roomId, roomPath), userId);
    }

    /**
     * 获取AI类型
     *
     * @param room
     * @param userId
     * @return
     */
    public static int getAiOperateType(Room room, int userId) {
        int type = 0;
        if (room != null) {
            RoomPlayer rp = room.getRoomPlayers().get(userId);
            if (rp != null && rp.getAiPlayer().isInitData()) {
                type = rp.getAiPlayer().getAiOpreateType();
            }
        }
        return type;
    }

    /**
     * 获取【自动模式】AI数据
     *
     * @param roomId 牌局ID，可选，用于输出日志
     * @param userId 玩家ID，必填
     * @return null   无法找到对应的玩家
     */
    public static EnterRoomAiPlayer getPlayerOfAuto(int roomId, int userId) {
        EnterRoomAiPlayer result = null;
        IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
        UserConfigBo autoUserConfig = cache.getAutoUserConfig(userId);

        if (null != autoUserConfig) {
            result = new EnterRoomAiPlayer();
            result.setUserId(autoUserConfig.getUserId());
            result.setType(autoUserConfig.getUserType());
            result.setStatus(autoUserConfig.getStatus());
            result.setAllowRoomMatching(autoUserConfig.getAllowRoomMatching());
        }

        logger.debug("获取自动模式玩家:rid={} , uid={} 返回结果: {} ！", roomId, userId, GsonHelper.toJson(result, false));
        return result;
    }

    /**
     * 随机计算AI【站起】的时间
     * 单位：秒
     *
     * @param mode        哪一类的AI ,必填
     * @param rPlayerNum  牌局当前的正常玩家数量(不包括AI) ， 必填
     * @param aiPlayerNum 牌局当前的AI玩家数量 ， 必填
     * @param roomId      牌局ID，可选，用于输出日志
     * @return 秒数，秒数后AI站起
     * =0 ，表示条件不满足，无需站起
     * <0 ,表示配置缺失，无法确定
     */
    public static int getStandingTimeInSec(EAiMode mode, int rPlayerNum, int aiPlayerNum, int roomId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("获取【%s模式】站起时间:rid=%s , rpNum=%s , aipNum=%s",
                    mode, roomId, rPlayerNum, aiPlayerNum));
        }
        int result = 0;
        try {
            if (null == mode) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    mode参数无效 ！", NEWLINE));
                }
                return result;
            }

            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
            if (mode == EAiMode.manual) { // 手动模式
                ManualStandupConfigBo standupConfig = cache.getManualStandupConfig();
                if (null == standupConfig) {
                    if (null != traceMsg) {
                        traceMsg.append(String.format("%s    Not found standup config ！", NEWLINE));
                    }
                    result = -1;
                    return result;
                }

                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    standupConfig : %s ！", NEWLINE, GsonHelper.toJson(standupConfig, false)));
                }

                // 仅有1个AI 且没有其它玩家
                boolean aiNumMatch = true;
                if (null != standupConfig.getAiPlayerNum() && aiPlayerNum != standupConfig.getAiPlayerNum())
                    aiNumMatch = false;
                boolean rlPlayerNumMatch = true;
                if (null != standupConfig.getRlPlayerNum() && rPlayerNum != standupConfig.getRlPlayerNum())
                    rlPlayerNumMatch = false;

                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    aiNumMatch=%s , rlPlayerNumMatch=%s",
                            NEWLINE, aiNumMatch, rlPlayerNumMatch));
                }

                if (!aiNumMatch || !rlPlayerNumMatch) {
                    if (null != traceMsg) {
                        traceMsg.append(String.format("%s    条件不满足，忽略 ！", NEWLINE));
                    }
                    return result;
                }

                // 满足条件，则随机站起的描述
                result = Helper.randomIntBy(standupConfig.getStartSec(), standupConfig.getEndSec());
            } else { // 自动模式
                // 3）牌桌内仅有1个ai（没有其他玩家），n（3-5分钟随机，可配置）分钟后自动站起。（站起前又有玩家坐下，取消站起任务）
                if (aiPlayerNum != 1 || rPlayerNum != 0) {
                    if (null != traceMsg) {
                        traceMsg.append(String.format("%s    条件不满足，忽略 ！", NEWLINE));
                    }
                    return result;
                }

                AutoOverallConfigBo config = cache.getAutoOverallConfig();
                if (null == config) {
                    if (null != traceMsg) {
                        traceMsg.append(String.format("%s    Not found overall config ！", NEWLINE));
                    }
                    return result;
                }
                AutoStandupConfigBo standupConfig = config.timeWithJustOnlyAi(aiPlayerNum);
                if (null == standupConfig) {
                    if (null != traceMsg) {
                        traceMsg.append(String.format("%s    Not found standup config just with only %s ai ！", NEWLINE, aiPlayerNum));
                    }
                    return result;
                }

                // 满足条件，则随机站起的描述
                result = Helper.randomIntBy(standupConfig.getStartSec(), standupConfig.getEndSec());
            }

            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }


    /**
     * 随机计算AT/MT【站起】手数
     * <p>
     * 初始时调用
     *
     * @param userId 当前请求的玩家ID，可选，用于输出日志
     * @param type   玩家类型，可选，用于输出日志
     * @param roomId 牌局ID，可选，用于输出日志
     * @param mode   当前的AT/MT模式，可选，用于输出日志
     * @return 手数,>0
     * <=0 ,表示配置缺失或配置错误
     */
    @Deprecated
    public static int randomInitStandingHandNum(int userId, EAiType type, int roomId, EAiMode mode) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("【%s模式】初始站起手数:rid=%s , uid=%s , type=%s ",
                    mode, roomId, userId, type));
        }
        int result = 0;
        try {
            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
            CommonStandupConfigBo standupConfigBo = cache.getCommonStandupConfig();
            if (null == standupConfigBo) {
                LogHelper.log("%s   此类型的配置缺失！", NEWLINE);
                return result;
            }

            result = standupConfigBo.random();
            LogHelper.log("%s   配置数据: minHandNum=%s , maxHandNum=%s ！", NEWLINE,
                    standupConfigBo.getStartHandNum(), standupConfigBo.getEndHandNum());

            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 随机计算【补充带入时剩余筹码小于最小带入筹码】的比例
     *
     * @param userId 当前请求的玩家ID，可选，用于输出日志
     * @param roomId 牌局ID，可选，用于输出日志
     * @param mode   当前的AT/MT模式，可选，用于输出日志
     * @return 【带入最小带入筹码】的比例
     * <=0    表示配置缺失或配置错误
     * >0    表示【带入最小带入筹码】的比例
     */
    public static int randomPercentOfMinBringin(int userId, int roomId, EAiMode mode) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("【%s模式】【补充带入时剩余筹码小于最小带入筹码】的比例:rid=%s , uid=%s",
                    mode, roomId, userId));
        }
        int result = 0;
        try {

            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
            CommonBringinConfigBo bringinConfig = cache.getCommonBringinConfig();
            if (null == bringinConfig) {
                LogHelper.log("%s   此类型的配置缺失！", NEWLINE);
                return result;
            }

            result = bringinConfig.randomPercentOfMinBringin();
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 随机【输完后补充筹码】的延时时间
     * 单位：秒
     *
     * @param userId 当前请求的玩家ID，可选，用于输出日志
     * @param roomId 牌局ID，可选，用于输出日志
     * @param mode   当前的AT/MT模式，可选，用于输出日志
     * @return 【输完后补充筹码】的延时时间
     * <=0    表示配置缺失或配置错误
     * >0    表示【输完后补充筹码】的延时时间
     */
    public static int randomDelayTimeOfBringin(int userId, int roomId, EAiMode mode) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("【%s模式】【输完后补充筹码】的延时时间:rid=%s , uid=%s",
                    mode, roomId, userId));
        }
        int result = 0;
        try {

            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
            CommonBringinConfigBo bringinConfig = cache.getCommonBringinConfig();
            if (null == bringinConfig) {
                LogHelper.log("%s   此类型的配置缺失！", NEWLINE);
                return result;
            }

            result = bringinConfig.randomDelayTimeOfBringin();
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 检查用户是否【实时战况观众不可见白名单】
     *
     * @param userId 当前请求的玩家ID，必填
     * @param roomId 牌局ID，可选，用于输出日志
     * @return true  : 是
     * false ： 不是
     */
    public static boolean checkIfRoomViewerWhitelist(int userId, int roomId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("【实时战况观众不可见白名单】检查用户:rid=%s , uid=%s",
                    roomId, userId));
        }
        boolean result = false;
        try {

            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
            Boolean temp = cache.checkIfRoomViewerWhitelist(userId);
            if (null == temp) {
                LogHelper.log("%s   此类型的配置缺失！", NEWLINE);
                return result;
            }

            result = temp;
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 随机计算AT/MT【带入倍数】
     *
     * @param type        玩家类型，必填，用于确定范围
     * @param bringinType 带入类型，必填：1=首次/补充带入 2=整体带入
     * @param userId      当前请求的玩家ID，可选，用于输出日志
     * @param roomId      牌局ID，可选，用于输出日志
     * @param mode        当前的AT/MT模式，可选，用于输出日志
     * @return 带入倍数,>0
     * <=0 ,表示配置缺失或配置错误
     */
    public static int randomBringinRatio(EAiType type, EBringinType bringinType, int userId, int roomId, EAiMode mode) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("【%s模式】带入倍数:rid=%s , uid=%s , type=%s , bringinType=%s",
                    mode, roomId, userId, type, bringinType));
        }
        int result = 0;
        try {
            // 参数校验
            if (null == type) {
                LogHelper.log("%s   玩家类型（type）未指定 ！", NEWLINE);
                return result;
            }
            if (null == bringinType) {
                LogHelper.log("%s   带入类型（bringinType）未指定 ！", NEWLINE);
                return result;
            }

            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
            BringinPerUserTypeConfigBo bringinConfig = cache.getBringinPerUserTypeConfig(type);
            if (null == bringinConfig) {
                LogHelper.log("%s   此类型的配置缺失！", NEWLINE);
                return result;
            }

            result = bringinConfig.random(bringinType);
            LogHelper.log("%s   配置数据: minTimes=%s , maxTimes=%s , totalMinTimes=%s , totalMaxTimes=%s ！", NEWLINE,
                    bringinConfig.getMinTimes(), bringinConfig.getMaxTimes(),
                    bringinConfig.getTotalMinTimes(), bringinConfig.getTotalMaxTimes());

            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 获取AI【满员离桌】是否开启
     *
     * @param userId 当前检测是否需要站起的AI玩家ID
     *               当前禁用了，则直接随机手数返回
     *               否则接着做其他逻辑判断
     * @param roomId 牌局ID，可选，用于输出日志
     * @return 是否开启满员离桌
     */
    public static boolean getStandUpConfigEnableOverNeverLeaving(int userId, int roomId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("[R-%s][U-%s]【JYM】获取是否开启满员离桌",
                    roomId, userId));
        }

        boolean result = false;

        try {
            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
            // 获取玩家
            UserConfigBo userConfig = cache.getAutoUserConfig(userId);
            if (null == userConfig) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    无法找到此玩家 ！", NEWLINE));
                }
                return false;
            }

            result = userConfig.getForceStandUp().isEnableOverNeverLeaving();
            return result;

        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format(",  返回结果: %s ！", result));
                logger.debug(traceMsg.toString());
            }
        }
    }
    /**
     * PP73 获取AI是否开启有真人才触发满员离桌
     *
     * @param userId 当前检测是否需要站起的AI玩家ID
     * @param roomId 牌局ID，可选，用于输出日志
     * @return 是否开启有真人才触发满员离桌
     */
    public static boolean getStandUpConfigEnableHasPlayerOverLeaving(int userId, int roomId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("[R-%s][U-%s]【JYM】获取是否开启有真人才触发满员离桌",
                    roomId, userId));
        }

        boolean result = false;

        try {
            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
            // 获取玩家
            UserConfigBo userConfig = cache.getAutoUserConfig(userId);
            if (null == userConfig) {
                if (null != traceMsg) {
                    traceMsg.append(" 无法找到此玩家 ！");
                }
                return false;
            }

            result = userConfig.getForceStandUp().isEnableHasPlayerOverLeaving();
            return result;

        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format(",  返回结果: %s ！", result));
                logger.debug(traceMsg.toString());
            }
        }
    }


    /**
     * 获取AI【离桌机制】盈利时离桌配置是否开启
     *
     * @param userId 当前检测是否需要站起的AI玩家ID
     *               当前禁用了，则直接随机手数返回
     *               否则接着做其他逻辑判断
     * @param roomId 牌局ID，可选，用于输出日志
     * @return 盈利时离桌配置是否开启
     */
    public static boolean getStandUpConfigEnableEarningConfig(EAiMode mode, int userId, int roomId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("[R-%s][U-%s]【JYM】获取【%s模式】是否开启离桌机制配置标识",
                    roomId, userId, mode));
        }

        boolean result = false;

        try {
            if (null == mode || mode != EAiMode.auto) {
                if (null != traceMsg) {
                    traceMsg.append(String.format(" mode参数无效 ！"));
                }
                return false;
            }

            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
            // 获取玩家
            UserConfigBo userConfig = cache.getAutoUserConfig(userId);
            if (null == userConfig) {
                if (null != traceMsg) {
                    traceMsg.append(String.format(" 无法找到此玩家 ！"));
                }
                return false;
            }
            result = userConfig.getForceStandUp().isEnableEarningConfig();
            return result;

        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format(",  返回结果: %s ！", result));
                logger.debug(traceMsg.toString());
            }
        }
    }
    /**
     * 获取AI【离桌机制】盈利时离桌需要增设的手数
     *
     * @param userId 当前检测是否需要站起的AI玩家ID
     *               当前禁用了，则直接随机手数返回
     *               否则接着做其他逻辑判断
     * @param roomId 牌局ID，可选，用于输出日志
     * @return 盈利时离桌需要增设的手数
     */
    public static int getStandUpConfigHandCntOfEarning(EAiMode mode, int userId, int roomId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("[R-%s][U-%s]【JYM】获取【%s模式】获取盈利离桌机制增设的手数",
                    roomId, userId, mode));
        }

        int result = 0;

        try {
            if (null == mode || mode != EAiMode.auto) {
                if (null != traceMsg) {
                    traceMsg.append(String.format(" mode参数无效 ！"));
                }
                return 0;
            }

            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
            // 获取玩家
            UserConfigBo userConfig = cache.getAutoUserConfig(userId);
            if (null == userConfig) {
                if (null != traceMsg) {
                    traceMsg.append(String.format(" 无法找到此玩家 ！"));
                }
                return 0;
            }
            result = userConfig.getForceStandUp().getHandCntOfEarningLeaving();
            return result;

        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format(",  返回结果: %s ！", result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 获取AI【离桌机制】是否开启觸發离桌设定後多玩幾手開關
     *
     * @param userId 当前检测是否需要站起的AI玩家ID
     *               当前禁用了，则直接随机手数返回
     *               否则接着做其他逻辑判断
     * @param roomId 牌局ID，可选，用于输出日志
     * @return 盈利时不离桌配置是否开启
     */
    public static boolean getStandUpConfigEnableLeavingRandomHand(EAiMode mode, int userId, int roomId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("[R-%s][U-%s]【JYM】获取【%s模式】是否开启觸發离桌设定後多玩幾手開關",
                    roomId, userId, mode));
        }

        boolean result = false;

        try {
            if (null == mode || mode != EAiMode.auto) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    mode参数无效 ！", NEWLINE));
                }
                return false;
            }

            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
            // 获取玩家
            UserConfigBo userConfig = cache.getAutoUserConfig(userId);
            if (null == userConfig) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    无法找到此玩家 ！", NEWLINE));
                }
                return false;
            }
            result = userConfig.getForceStandUp().isEnableLeavingRandomHand();
            return result;

        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format(",  返回结果: %s ！", result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 获取AI【站起】带入次数上限（配置）
     *
     * @param userId 当前检测是否需要站起的AI玩家ID
     *               当前禁用了，则直接随机手数返回
     *               否则接着做其他逻辑判断
     * @param roomId 牌局ID，可选，用于输出日志
     * @return 带入次数上限
     */
    public static int getStandUpConfigMaxBringInCnt(EAiMode mode, int userId, int roomId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("获取【%s模式】【JYM】带入次数上限 配置:rid=%s , userId=%s",
                    mode, roomId, userId));
        }

        int result = 0;
        try {
            if (null == mode || mode != EAiMode.auto) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    mode参数无效 ！", NEWLINE));
                }
                return result;
            }

            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
            // 获取玩家
            UserConfigBo userConfig = cache.getAutoUserConfig(userId);
            if (null == userConfig) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    无法找到此玩家 ！", NEWLINE));
                }
                result = -1;
                return result;
            }

            result = userConfig.getForceStandUp().getMaxBringInCnt();
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format(",  返回结果: %s ！", result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 获取AI【站起】带入总量上限（配置）
     *
     * @param userId 当前检测是否需要站起的AI玩家ID
     *               当前禁用了，则直接随机手数返回
     *               否则接着做其他逻辑判断
     * @param roomId 牌局ID，可选，用于输出日志
     * @return 带入总量上限
     */
    public static int getStandUpConfigMaxBringIn(EAiMode mode, int userId, int roomId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("获取【%s模式】【JYM】带入总量上限 配置:rid=%s , userId=%s",
                    mode, roomId, userId));
        }

        int result = 0;
        try {
            if (null == mode || mode != EAiMode.auto) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    mode参数无效 ！", NEWLINE));
                }
                return result;
            }

            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
            // 获取玩家
            UserConfigBo userConfig = cache.getAutoUserConfig(userId);
            if (null == userConfig) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    无法找到此玩家 ！", NEWLINE));
                }
                result = -1;
                return result;
            }

            result = userConfig.getForceStandUp().getMaxBringIn();
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 获取AT/MT【站起】手数（配置）
     * <p>
     * 初始时调用
     *
     * @param userId 当前请求的玩家ID，可选，用于输出日志
     * @param roomId 牌局ID，可选，用于输出日志
     * @param mode   当前的AT/MT模式，可选，用于输出日志
     * @return 手数,>0
     * <=0 ,表示配置缺失或配置错误
     */
    public static int initStandUpMaxHandCnt(int userId, int roomId, EAiMode mode) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("【%s模式】初始站起手数:rid=%s , uid=%s",
                    mode, roomId, userId));
        }
        int result = 0;
        try {
            if (null == mode || mode != EAiMode.auto) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    mode参数无效 ！", NEWLINE));
                }
                return result;
            }

            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
            // 获取玩家
            UserConfigBo userConfig = cache.getAutoUserConfig(userId);
            if (null == userConfig) { // 玩家被删除，咋办？
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    无法找到此玩家 ！", NEWLINE));
                }
                result = -1;
                return result;
            }
            if (userConfig.getStatus() == EUserStatus.off) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    此玩家已被终止 ！", NEWLINE));
                }
                result = Helper.randomIntBy(1, 5);
            } else {
                result = userConfig.getForceStandUp().getMaxHandCnt();
            }
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 获取AI【站起】手数（配置）
     *
     * @param userId        当前检测是否需要站起的AI玩家ID
     *                      当前禁用了，则直接随机手数返回
     *                      否则接着做其他逻辑判断
     * @param rPlayerNum    牌局当前的正常玩家数量(不包括AI) ， 必填
     * @param aiPlayerNum   牌局当前的AI玩家数量 ， 必填
     * @param roomPlayerNum 牌局的玩家数量，即几人桌
     * @param roomId        牌局ID，可选，用于输出日志
     * @return 手数
     * =0 ，表示条件不满足，无需站起
     * <0 ,表示配置缺失，无法确定
     */
    public static int getStandUpConfigMaxHand(EAiMode mode, int userId, int rPlayerNum, int aiPlayerNum, int roomPlayerNum, int roomId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("获取【%s模式】【JYM】站起手数配置:rid=%s , rpNum=%s , aipNum=%s , userId=%s",
                    mode, roomId, rPlayerNum, aiPlayerNum, userId));
        }

        int result = 0;
        try {
            result = initStandUpMaxHandCnt(userId, roomId, mode);
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 随机计算AI【站起】手数
     *
     * @param userId        当前检测是否需要站起的AI玩家ID
     *                      当前禁用了，则直接随机手数返回
     *                      否则接着做其他逻辑判断
     * @param rPlayerNum    牌局当前的正常玩家数量(不包括AI) ， 必填
     * @param aiPlayerNum   牌局当前的AI玩家数量 ， 必填
     * @param roomPlayerNum 牌局的玩家数量，即几人桌
     * @param roomId        牌局ID，可选，用于输出日志
     * @return 手数
     * =0 ，表示条件不满足，无需站起
     * <0 ,表示配置缺失，无法确定
     */
    public static int getRandomStandingHandNum(EAiMode mode, int userId, int rPlayerNum, int aiPlayerNum, int roomPlayerNum, int roomId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("获取【%s模式】站起手数:rid=%s , rpNum=%s , aipNum=%s , userId=%s",
                    mode, roomId, rPlayerNum, aiPlayerNum, userId));
        }
        int result = 0;
        try {
            if (null == mode || mode != EAiMode.auto) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    mode参数无效 ！", NEWLINE));
                }
                return result;
            }

            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();

            // 获取玩家
            UserConfigBo userConfig = cache.getAutoUserConfig(userId);
            if (null == userConfig) { // 玩家被删除，咋办？
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    无法找到此玩家 ！", NEWLINE));
                }
                result = -1;
                return result;
            }
            if (userConfig.getStatus() == EUserStatus.off) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    此玩家已被终止 ！", NEWLINE));
                }
                result = Helper.randomIntBy(1, 5);
                return result;
            }

            AutoOverallConfigBo config = cache.getAutoOverallConfig();
            if (null == config) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    Not found overall config ！", NEWLINE));
                }
                result = -1;
                return result;
            }

            AutoStandupConfigBo standupConfig = null;
            if (rPlayerNum + aiPlayerNum == roomPlayerNum) {
                // 1）牌桌内有3个ai，当空位=0时，打n（3-5手随机，可配置）手后站起1个盈利最少的ai。（站起前又有玩家离开，也站起）
                standupConfig = config.handWithAiAndNoSeat(aiPlayerNum);
                if (null == standupConfig) {
                    if (null != traceMsg) {
                        traceMsg.append(String.format("%s    Not found standup config with %s ai and no seat！", NEWLINE, aiPlayerNum));
                    }
                    result = -1;
                    return result;
                }
            } else if (aiPlayerNum == 2 && rPlayerNum == 0) {
                // 2）牌桌内仅有2个ai（没有其他玩家），当打满n（8-15手随机，可配置）手后随机站起一个。（站起前又有玩家坐下，也站起）
                standupConfig = config.handWithJustOnlyAi(aiPlayerNum);
                if (null == standupConfig) {
                    if (null != traceMsg) {
                        traceMsg.append(String.format("%s    Not found standup config with just only %s ai ！", NEWLINE, aiPlayerNum));
                    }
                    result = -1;
                    return result;
                }
            }

            // 满足条件，则随机站起的描述
            if (null != standupConfig) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    standupConfig : %s ！", NEWLINE, GsonHelper.toJson(standupConfig, false)));
                }
                result = Helper.randomIntBy(standupConfig.getStartHandNum(), standupConfig.getEndHandNum());
            } else {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    条件不满足，忽略 ！", NEWLINE));
                }
            }
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * AI需要站起的计时时间
     * 单位：秒
     *
     * @param roomId 牌桌ID，可选，用于日志
     * @return <= 0 配置数据缺失，无法确定
     * >0   游戏开始X秒后
     */
    public static int getTimingSecOfStandUp(int roomId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("获取自动模式的站起计时:rid=%s", roomId));
        }

        int result = 0;
        try {
            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();

            AutoOverallConfigBo config = cache.getAutoOverallConfig();
            if (null == config) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    Not found overall config ！", NEWLINE));
                }
                return result;
            }

            Integer timing = config.getStandupTiming();
            if (null == timing) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    Not found standup-timing config ！", NEWLINE));
                }
                return result;
            }

            result = timing;
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 获取【自动模式】开/关
     *
     * @param roomId 牌桌ID，可选，用于日志
     * @return true  : 开
     * false : 关
     */
    public static boolean getStatusOfAutoMode(int roomId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("获取自动模式的开关状态:rid=%s", roomId));
        }

        boolean result = false;
        try {
            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();

            FunctionSwitchConfigBo config = cache.getSwitchConfig();
            if (null == config) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    Not found [功能开关配置] config ！", NEWLINE));
                }
                return result;
            }

            result = config.isAutoModeOn();
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 获取【爆桌模式】开/关
     *
     * @param roomId 牌桌ID，可选，用于日志
     * @return true  : 开
     * false : 关
     */
    public static boolean getStatusOfExplosionTable(int roomId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("获取【爆桌模式】的开关状态:rid=%s", roomId));
        }

        boolean result = false;
        try {
            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();

            FunctionSwitchConfigBo config = cache.getSwitchConfig();
            if (null == config) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    Not found [功能开关配置] config ！", NEWLINE));
                }
                return false;
            }

            result = config.isExplosionTableOn();
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 获取【闲置模式】生效时间（在本局剩下多少时间时生效）
     *
     * @param roomId 牌桌ID，可选，用于日志
     * @return 秒
     */
    public static int getIdleModeLeftTimeSec(int roomId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("[R-%s]获取【闲置模式】在本局所剩时间（配置）生效", roomId));
        }

        int result = 0;
        try {
            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
            CommonIdleModeConfigBo config = cache.getCommonIdleModeConfigBo();
            if (null == config) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    Not found [闲置模式] config ！", NEWLINE));
                }
                return result;
            }

            result = config.getGameLeftTimeSec();
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 获取【闲置模式】离开手数
     *
     * @param roomId 牌桌ID，可选，用于日志
     * @return int 随机离开手数
     */
    public static int getIdleModeRandomHand(int roomId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("[R-%s]获取【闲置模式】的离开随机手数", roomId));
        }

        int result = Helper.randomIntBy(1, 3);
        try {
            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
            CommonIdleModeConfigBo config = cache.getCommonIdleModeConfigBo();
            if (null == config) {
                LogHelper.log("%s    Not found [闲置模式] config！", NEWLINE);
                return result;
            }

            result = config.getStandUpHand().randomHand();
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }


    /**
     * 获取【闲置模式】AI离开等待时间
     *
     * @param roomId 牌桌ID，可选，用于日志
     * @return 秒
     */
    public static int getIdleModeWaitingTimeSec(int roomId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("[R-%s]获取【闲置模式】AI离开等待时间", roomId));
        }

        int result = 0;
        try {
            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
            CommonIdleModeConfigBo config = cache.getCommonIdleModeConfigBo();
            if (null == config) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    Not found [闲置模式] config ！", NEWLINE));
                }
                return result;
            }

            result = config.getWaitingTimeSec();
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 获取【闲置模式】开/关
     *
     * @param roomId 牌桌ID，可选，用于日志
     * @return true  : 开
     * false : 关
     */
    public static boolean getStatusOfIdleMode(int roomId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("[R-%s]获取【闲置模式】的开关状态", roomId));
        }

        boolean result = false;
        try {
            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
            CommonIdleModeConfigBo config = cache.getCommonIdleModeConfigBo();
            if (null == config) {
                LogHelper.log("%s    Not found [闲置模式] config！", NEWLINE);
                return false;
            }

            result = config.isSwitchOn();
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 检查是否满足【爆桌模式】
     *
     * @param roomNum 当前在打牌桌数量, 必填
     * @param roomId  牌桌ID，可选，用于日志
     * @return true  : 开
     * false : 关
     */
    public static boolean checkIfExplosionTable(int roomNum, int roomId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("检查是否满足【爆桌模式】:rid=%s,roomNum=%s", roomId, roomNum));
        }

        boolean result = false;
        try {
            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();

            long sysTime = Helper.keepHourMinute(new Date());
            ExplosionTableConfigBo config = cache.getExplosionTableConfig(sysTime);
            if (null == config) {
                LogHelper.log("%s    Not found [爆桌模式] config : %s！", NEWLINE, sysTime);
                return false;
            }

            LogHelper.log("%s    配置(%s)：%s", NEWLINE, sysTime, config);
            result = roomNum < config.getTableNum();
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 随机提前离桌的模式
     *
     * @param userId 当前玩家ID，可选，用于日志
     * @param roomId 牌桌ID，可选，用于日志
     * @return null  : 配置缺失
     * true  : 离桌
     * false : 离房
     */
    public static Boolean randomInAdvanceMode(int roomId, int userId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("随机【提前离桌】的模式:rid=%s,uid=%s", roomId, userId));
        }

        Boolean result = null;
        try {
            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
            CommLeftInAdvanceConfigBo config = cache.getLeftInAdvanceConfig();
            if (null == config) {
                LogHelper.log("%s    Not found [提前离桌] config！", NEWLINE);
                return result;
            }

            LogHelper.log("%s    配置：%s", NEWLINE, config);
            result = config.random();
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 检查房间是否满足【自动模式】条件
     *
     * @param room           牌桌ID
     * @param playerNum      牌桌的玩家数量（几人桌），必填
     * @param smallBlindness 牌局的小盲注，必填
     * @return < 0      配置数据缺失，无法确定
     * > 0      符合条件
     * = 0     不符合条件
     */
    public static int checkIfRoomMatch(Room room, int playerNum, int smallBlindness) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("检查房间是否满足【自动模式】条件:rid=%s , playerNum=%s , smallBlind=%s",
                    room.getRoomId(), playerNum, smallBlindness));
        }

        int result = 0;
        try {
            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();

            AutoOverallConfigBo config = cache.getAutoOverallConfig();
            if (null == config) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    Not found overall config ！", NEWLINE));
                }
                result = -1;
                return result;
            }

            AutoDispatchConfigBo dispatchConfig = config.getDispatch();
            if (null == dispatchConfig) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    Not found dispatch config ！", NEWLINE));
                }
                result = -1;
                return result;
            }

            if (null != traceMsg) {
                traceMsg.append(String.format("%s    dispatch config : %s！",
                        NEWLINE, GsonHelper.toJson(dispatchConfig, false)));
            }

            result = dispatchConfig.roomIfMatch(playerNum, smallBlindness) ? 1 : 0;
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 确定派遣的时间,单位：秒
     *
     * @param roomCreatedMills 牌局创建时间，>0有效单位：毫秒
     * @param roomEndMills     牌局结束时间，可选，>0有效, 单位：毫秒
     * @param room             牌桌
     * @return 派遣时间，单位：秒
     * <0    表示配置缺失或配置错误
     * 0     表示不派遣
     * >0    表示派遣时间
     */
    public static int getDispatchingTimeInSec(long roomCreatedMills, Long roomEndMills, Room room) {
        //PP50 AI派遣间隔，房间指定配置如果有指定房间派遣配置，则使用房间指定的配置
        if (room.getAtGamePlan() != null && room.getAtGamePlan().getAdvancedDispatchEnable() && StringUtils.isNotBlank(room.getAtGamePlan().getAdvancedDispatchConfig())) {
            String dispatchConfigStr = room.getAtGamePlan().getAdvancedDispatchConfig();
            logger.debug("[R-{}][房间指定派遣配置] AI派遣间隔，使用指定房间配置的进阶派遣配置:{}", room.getRoomId(), dispatchConfigStr);
            Gson jsonMapper = new Gson();
            CommonDispatchConfigBo dispatchConfigBo = jsonMapper.fromJson(dispatchConfigStr, CommonDispatchConfigBo.class);
            return dispatchConfigBo.random(roomCreatedMills, roomEndMills);
        }

        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("获取派遣时间:rid=%s , roomCreatedMill=%s ,roomEndMills=%s ",
                    room.getRoomId(), roomCreatedMills, roomEndMills));
        }

        int result = 0;
        try {
            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();

            CommonDispatchConfigBo config = cache.getCommonDispatchConfig();
            if (null == config) {
                LogHelper.log("%s    Not found dispatch-time config ！", NEWLINE);
                return result;
            }

            result = config.random(roomCreatedMills, roomEndMills);
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 确定AI数量
     *
     * @param room              牌桌
     * @param unoccupiedSeatNum 空位数量，必填
     * @param nowAutoAiCount    当前AI数量
     * @return <0   配置数据缺失，无法确定
     * >= 0   AI数量
     */
    public static int ensureNumOfDispatching(Room room, int unoccupiedSeatNum, int nowAutoAiCount) {
        //PP50 当有房间派遣配置时，返回房间配置的最大AI派遣数量
        if (room.getAtGamePlan() != null && room.getAtGamePlan().getMaxAtUser() != null) {
            int maxNum = room.getAtGamePlan().getMaxAtUser();
            logger.debug("[R-{}][房间指定派遣配置] 返回房间派遣配置中的最大AI派遣数量:{}", room.getRoomId(), maxNum);
            return maxNum;
        }

        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("获取自动模式的派遣数量:rid=%s , unoccupiedSeatNum=%s", room.getRoomId(), unoccupiedSeatNum));
        }
        int result = 0;
        try {
            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();

            AutoOverallConfigBo config = cache.getAutoOverallConfig();
            if (null == config) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    Not found overall config ！", NEWLINE));
                }
                return result;
            }

            AutoDispatchConfigBo dispatchConfig = config.getDispatch();
            if (null == dispatchConfig) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    Not found dispatch config ！", NEWLINE));
                }
                return result;
            }

            if (null != traceMsg) {
                traceMsg.append(String.format("%s    dispatch config : %s！",
                        NEWLINE, GsonHelper.toJson(dispatchConfig, false)));
            }

            Integer temp = dispatchConfig.getDispatchingNum(unoccupiedSeatNum);
            if (null == temp) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    Not found empty-seat config ！", NEWLINE));
                }
                return result;
            }

            if (null != traceMsg) {
                traceMsg.append(String.format("%s    依空位数量需額外派遣AI数量: %s", NEWLINE, temp));
            }

            result = temp + nowAutoAiCount;
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 已派遣AI列表
     */
    private static final Map<Integer, UserConfigBo> dispatchedUserMap = new HashMap<>();
    /**
     * 牌局派遣的AI类型
     */
    private static final Map<Integer, List<UserConfigBo>> dispatchedUserOfRoomMap = new HashMap<>();
    /**
     * 牌局不允许再派遣的AI用户ID
     */
    // userId -> roomId
    private static final String DENYENTER_ROOMUSERID_FORMAT = "%s_%s";
    private static final Set<String> denyEnterroomUserIdMap = new HashSet<>();
    private static final Set<Integer> dispatchedMtSet = new HashSet<>(); //已经派遣的mt集合

    public static void addDispatchedMt(int userId) {
        dispatchedMtSet.add(userId);
    }

    public static void removeDispatchedMt(int userId) {
        if (dispatchedMtSet.contains(userId)) {
            logger.debug("释放mt玩家,uid={}", userId);
            dispatchedMtSet.remove(userId);

            RedisService redisService = RedisService.getRedisService();
            redisService.delDispatchMtSet(String.valueOf(userId));
        }
    }

    public static void clearAllDispatchMt() {
        logger.debug("牌局结束,释放所有mt");
        RedisService redisService = RedisService.getRedisService();
        if (dispatchedMtSet != null && dispatchedMtSet.size() > 0) {
            redisService.delAllDispatchMtSet(
                    dispatchedMtSet.stream().map(integer -> String.valueOf(integer)).collect(Collectors.toSet()));
        }
        dispatchedMtSet.clear();
    }

    public static boolean checkIfDispatched(int userId) {
        if (userId == 0) {
            return false;
        }
        return dispatchedMtSet.contains(userId);
    }


    private static boolean canDispatch(Room room, UserConfigBo userConfig, boolean lock) {
        boolean result = false;
        int roomId = room.getRoomId();
        synchronized (dispatchedUserMap) {
            if (checkIfUndispatched(userConfig.getUserId()) &&
                    checkIfUndispatchedGlobally(userConfig.getUserId())) {
                logger.debug("[R-{}]未派遣过此AI: uid={}",room.getRoomId(), userConfig.getUserId());
                result = true;
            } else {
                logger.debug("[R-{}]已派遣过此AI: uid={}",room.getRoomId(), userConfig.getUserId());
            }
            if (result && !dispatchedUserOfRoomMap.isEmpty()) {
                List<UserConfigBo> userLst = dispatchedUserOfRoomMap.get(roomId);
                if (null != userLst && !userLst.isEmpty()) {
                    for (UserConfigBo user : userLst) {
                        // 在拒绝派遣列表，则不允许派遣
                        String key = String.format(DENYENTER_ROOMUSERID_FORMAT, roomId, user.getUserId());
                        if (denyEnterroomUserIdMap.contains(key)) {
                            logger.debug("在拒绝派遣列表: rid={} uid={}", roomId, user.getUserId());
                            result = false;
                            break;
                        }

                        // 存在相同类型的用户，则不允许派遣
                       /* 暂时去掉此限制
                       if(user.getUserType() == userConfig.getUserType())
                        {
                            result = false;
                            break;
                        }*/
                    }
                }
            }

            if (result && lock) {//锁定
                dispatchedUserMap.put(userConfig.getUserId(), userConfig);
                List<UserConfigBo> userLst = dispatchedUserOfRoomMap.get(roomId);
                if (null == userLst) {
                    userLst = new ArrayList<>();
                    dispatchedUserOfRoomMap.put(roomId, userLst);
                }
                userLst.add(userConfig);
                // if game not begin yet, assume game end time to be max play time from now
                long gameStartTime = room.getRoomStatus() == 0 ? System.currentTimeMillis() : room.getGameBeginTime();
                long gameEndTime = gameStartTime + room.getMaxPlayTime() * 60 * 1000L;
                logger.debug("R-{} U-{} roomStatus={} maxPlayTime={} gameBeginTime={} gameEndTime={}", room.getRoomId(), userConfig.getUserId(), room.getRoomStatus(), room.getMaxPlayTime(), gameStartTime, gameEndTime);
                RedisService.getRedisService().addAiDispatchUser(userConfig.getUserId(), roomId, gameEndTime);
            }
        }

        return result;
    }

    private static List<UserConfigBo> releaseDispatched(int roomId, Integer userId, boolean denyEnterAgain) {
        List<UserConfigBo> resultLst = null;

        synchronized (dispatchedUserMap) {
            if (null != userId) {
                boolean removedOk = false;
                List<UserConfigBo> userLst = dispatchedUserOfRoomMap.get(roomId);
                if (null != userLst && !userLst.isEmpty()) {
                    Iterator<UserConfigBo> it = userLst.iterator();
                    while (it.hasNext()) {
                        UserConfigBo user = it.next();
                        if (user.getUserId() == userId) {
                            it.remove();
                            removedOk = true;
                        }
                    }
                }

                if (removedOk) {
                    UserConfigBo removedUser = dispatchedUserMap.remove(userId);
                    if (null != removedUser) {
                        resultLst = new ArrayList<>();
                        resultLst.add(removedUser);
                        RedisService.getRedisService().removeAiDispatchUser(userId, roomId);
                    }

                    // 如果不允许再次派遣到此房间的，则添加拒绝集合
                    if (denyEnterAgain) {
                        logger.debug("加入拒绝派遣列表: rid={} uid={}", roomId, userId);
                        String key = String.format(DENYENTER_ROOMUSERID_FORMAT, roomId, userId);
                        denyEnterroomUserIdMap.add(key);
                    }
                }
            } else {
                List<UserConfigBo> userLst = dispatchedUserOfRoomMap.remove(roomId);
                if (null != userLst && !userLst.isEmpty()) {
                    resultLst = userLst;
                    Iterator<UserConfigBo> it = userLst.iterator();
                    while (it.hasNext()) {
                        UserConfigBo user = it.next();
                        dispatchedUserMap.remove(user.getUserId());
                        logger.debug("移除拒绝派遣列表: rid={} uid={}", roomId, user.getUserId());
                        String key = String.format(DENYENTER_ROOMUSERID_FORMAT, roomId, user.getUserId());
                        denyEnterroomUserIdMap.remove(key);
                    }
                    RedisService.getRedisService().removeAiDispatchUsers(roomId);
                }
            }
        }

        return resultLst;
    }

    public static List<Integer> filterFromClubUserIdLst (Room room,List<Integer> okUserIdLst) {
        ClubTribeDao clubTribeDao = new ClubTribeDaoImpl();
        List<ClubStatusPo> clubStatusLst = clubTribeDao.getClubStatusOfUsers(okUserIdLst);
        if (null == clubStatusLst || clubStatusLst.isEmpty()) {
            logger.debug("[R-{}] All user-matched not bound to club ！",room.getRoomId());
            return null;
        }
        if (logger.isDebugEnabled()) {
            logger.debug("[R-{}:{}] 俱乐部状态正常的列表: room.isClubRoom()={},clubStatusLst size={}",
                    room.getRoomId(), room.getName(), room.isClubRoom(), clubStatusLst.size());
        }
        // 俱乐部状态正常的用户列表
        clubStatusLst = clubStatusLst.stream().filter(po -> po.getClubStatus() == 0  // 俱乐部未关闭
                        && (!room.isClubRoom() //大厅房或私人房
                        || room.getTribeRoomType() == 1 //联盟币房
                        || (room.getTribeRoomType() == 0 && po.getClubId() == room.getClubId()) //金币房需要同一俱乐部
                )
        ).collect(Collectors.toList());
        if (clubStatusLst.isEmpty()) {
            logger.debug("[R-{}] club of user-matched closed or not same as room！",room.getRoomId());
            return null;
        }
        // 查询俱乐部的联盟状态
        List<Integer> clubIdLst = clubStatusLst.stream().map(ClubStatusPo::getClubId).collect(Collectors.toList());
        final Map<Integer, ClubTribeStatusPo> clubTribeStatusMap = clubTribeDao.getTribeStatusOfClub(clubIdLst);
        if (clubTribeStatusMap == null || clubTribeStatusMap.isEmpty()) {
            logger.debug("[R-{}] club of user-matched not bound to tribe ！",room.getRoomId());
            return null;
        }
        // 联盟状态正常的俱乐部列表
        final List<Integer> resultUserIdLst = clubStatusLst.stream().filter(po -> {
            ClubTribeStatusPo tribe = clubTribeStatusMap.get(po.getClubId());
            if (null != tribe) {
                return tribe.getClubStatus() == 1 &&  // 非【踢出中或转移中】
                        tribe.getTribeStatus() == 0 &&  // 联盟未被关闭
                        (!room.isTribeRoom() || tribe.getTribeId() == room.getTribeId());
            }
            return false;
        }).map(ClubStatusPo::getUserId).collect(Collectors.toList());
        if (resultUserIdLst.isEmpty()) {
            logger.debug("[R-{}]  tribe of user-matched's club invalid ！",room.getRoomId());
            return null;
        }
        return resultUserIdLst;
    }
    /**
     * 确定派遣的AI
     * <p>
     * 逻辑:
     * 1. 根据系统时间确定时间范围段，如果为空，则直接返回null，否则继续执行下一步
     * 2. 根据牌局的盲注级别，确定AT列表，如果为空，则直接返回null，否则执行下一步
     * 2.1 移除终止状态的AT
     * 3. 检查上一步的AT是否已经派遣到牌局，如果未派遣的AT列表为空，则直接返回null；否则继续执行下一步
     * 4. 检查上一步的AT的可用列表
     * 玩家状态非冻结
     * 所属俱乐部存在，且非关闭状态 & 非踢出中 & 转移中
     * 俱乐部所属联盟非关闭状态
     * 如果AT列表为空，则直接返回null，否则执行下一步
     * 5.随机选择一个可派遣的玩家返回
     *
     * @param room    牌局信息，必填
     * @param minChip 最低要求金豆数,必填，大于0有效
     * @return null 无AI可派遣或配置数据缺失，无法确定
     * 否则返回待派遣的AI
     * userId    玩家用户ID
     * type      AI类型
     */
    public static EnterRoomAiPlayer ensureAiOfDispatching(Room room, int minChip) {
        StringBuilder traceMsg = null;
        int roomId = room.getRoomId();
        int smallBlind = room.getManzhu();
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("确定派遣的AI:rid=%s , smallBlind=%s , minChip=%s",
                    roomId, smallBlind, minChip));
        }

        EnterRoomAiPlayer result = null;
        try {
            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();

            AutoOverallConfigBo config = cache.getAutoOverallConfig();
            if (null == config) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    Not found overall config ！", NEWLINE));
                }
                return result;
            }

            // 1. 根据系统时间确定时间范围段，如果为空，则直接返回null，否则继续执行下一步
            // 2. 根据牌局的盲注级别，确定AT列表，如果为空，则直接返回null，否则执行下一步
            long sysTime = Helper.keepHourMinute(new Date());
            String blindCode = String.format("%s/%s", smallBlind, smallBlind * 2);
            if (null != traceMsg) {
                traceMsg.append(String.format("%s    get config user: blindCode=%s , sysTime=%s ！",
                        NEWLINE, blindCode, sysTime));
            }


            List<UserConfigBo> configUserLst = cache.getConfigUserIfMatch(blindCode, sysTime, room);
            if (null == configUserLst || configUserLst.isEmpty()) {
                if (null != traceMsg) {
                    traceMsg.append(String.format(" -> %s",
                            configUserLst == null ? 0 : configUserLst.size()));
                }
                return result;
            }

            // 2.1 过滤状态为终止的列表
            configUserLst = configUserLst.stream().filter(a -> {
                return EUserStatus.on == a.getStatus();
            }).collect(Collectors.toList());
            if (configUserLst.isEmpty()) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    No user-on to dispatch ！", NEWLINE));
                }
                return result;
            }
            // 2.2 过滤已加入踢出列表的用户
            Set<String> kickOutUser = RedisService.getRedisService().getKickOutUser(room.getRoomId());
            configUserLst = configUserLst.stream()
                    .filter(item -> !kickOutUser.contains(String.valueOf(item.getUserId())))
                    .collect(Collectors.toList());

            // 3. 检查上一步的AT是否已经派遣到牌局，如果未派遣的AT列表为空，则直接返回null；否则继续执行下一步
            // 4. 检查上一步的AT的类型未派遣到当前牌局中的AT列表，如果空，则直接返回null；否则继续执行下一步
            Iterator<UserConfigBo> configUserIt = configUserLst.iterator();
            if (logger.isDebugEnabled()) {
                logger.debug("configUserLst = {}", configUserLst.stream().map(UserOptypeConfigBo::getUserId).collect(Collectors.toList()));
            }
            while (configUserIt.hasNext()) {
                UserConfigBo userconfig = configUserIt.next();
                if (!canDispatch(room, userconfig, false))
                    configUserIt.remove();
            }
            if (configUserLst.isEmpty()) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    No user to dispatch ！", NEWLINE));
                }
                return result;
            }

            /**
             * 5. 检查上一步的AT的可用列表
             *      玩家状态非冻结
             *      所属俱乐部存在，且非关闭状态 & 非踢出中 & 转移中
             *      俱乐部所属联盟非关闭状态
             *   如果AT列表为空，则直接返回null，否则执行下一步
             */
            List<Integer> userIdLst = configUserLst.stream()
                    .map(UserConfigBo::getUserId).collect(Collectors.toList());
            // 查询未被冻结的玩家
            UserInfoDao userDao = new UserInfoDaoImp();
            final List<Integer> okUserIdLst = userDao.checkUserIfNotFozen(userIdLst);
            if (null == okUserIdLst || okUserIdLst.isEmpty()) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    All user-matched was frozen ！", NEWLINE));
                }
                return result;
            }
            // 查询玩家所属的俱乐部
            if (null != traceMsg) {
                traceMsg.append(String.format("%s    non-frozen user list:%s",
                        NEWLINE, String.join(",", Arrays.toString(okUserIdLst.toArray()))));
            }
            //过滤俱乐部状态正常的用户列表
            List<Integer> resultUserIdLst = filterFromClubUserIdLst(room,okUserIdLst);
            if (null == resultUserIdLst || resultUserIdLst.isEmpty()) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    tribe of user-matched's club invalid ！", NEWLINE));
                }
                return result;
            }
            configUserLst = configUserLst.stream()
                    .filter(u -> resultUserIdLst.contains(u.getUserId()))
                    .filter(u -> hasMinChip(u.getUserId(), minChip, room))
                    .filter(u -> isClubLossLimitNotExceeded(room, u.getUserId()))
                    .collect(Collectors.toList());

            if (configUserLst.isEmpty()) {
                logger.info("[R-{}]无AI可用于派遣！", room.getRoomId());
                return result;
            }
            // 6.开始获派遣AI
            UserConfigBo lockUser = null;
            if (room.getAtGamePlan() != null && !room.getAtGamePlan().getStandingConfig().isEmpty()) {
                logger.debug("[R-{}][房间指定派遣配置] 按离桌设定的ID顺序抽AI做派遣:{}", room.getRoomId(), room.getAtGamePlan().getStandingConfig());
                for (int cur = 0; cur < room.getAtGamePlan().getStandingConfig().size(); cur++) {
                    //6.1 PP50-FE2 获取离桌配置优先ID
                    final int configId = room.getAtGamePlan().getDispatchStandingConfigId();
                    // 6.2 在优先的离桌配置的AI中随机抽取一个ai，并且再次检查是否已经派遣
                    List<UserConfigBo> disppatchUsers = configUserLst.stream()
                            .filter(user -> configId == user.getForceStandUp().getStandingConfigId())
                            .collect(Collectors.toList());
                    Collections.shuffle(disppatchUsers);
                    logger.debug("[R-{}][房间指定派遣配置] 按离桌配置ID轮循得出离桌设定ID={},AI人数={}", room.getRoomId(), configId, disppatchUsers.size());
                    for (UserConfigBo matchUser : disppatchUsers) {
                        if (canDispatch(room, matchUser, true)) {
                            lockUser = matchUser;
                            break;
                        }
                    }
                    if (lockUser != null) {
                        logger.debug("[R-{}][房间指定派遣配置] 派遣ID={},离桌设定ID={}", room.getRoomId(), lockUser.getUserId(), configId);
                        break;
                    }
                }
            } else {
                logger.debug("[R-{}]无房间指定配置的离桌配置,随便抽取一个ai", room.getRoomId());
                // 6.3 随便抽取一个ai，并且再次检查是否已经派遣，否则设置到对应派遣集合中
                Collections.shuffle(configUserLst);
                for (UserConfigBo matchUser : configUserLst) {
                    if (matchUser != null && canDispatch(room, matchUser, true)) {
                        lockUser = matchUser;
                        break;
                    }
                }
            }

            if (null == lockUser) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    Find matched-user,but not lock because of dispatched ！", NEWLINE));
                }
                logger.info("[R-{}]找不到可用的AI用于派遣！", room.getRoomId());
                return result;
            }
            result = new EnterRoomAiPlayer();
            result.setUserId(lockUser.getUserId());
            result.setType(lockUser.getUserType());
            result.setStatus(lockUser.getStatus());
            result.setAllowRoomMatching(lockUser.getAllowRoomMatching());
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, GsonHelper.toJson(result, false)));
                logger.debug(traceMsg.toString());
            }
        }
    }

    public static boolean hasMinChip(int userId, int minChip, Room room) {
        UserInfoDao userInfoDao = new UserInfoDaoImp();

        try (Connection conn = DBUtil.getConnection()) {
            int userChip = 0;
            String remark = "";
            if (room.getTribeRoomType() == 1) { // 联盟
                ClubTribeDao tribeDao = new ClubTribeDaoImpl();
                List<Integer> userClubOfTribe = tribeDao.getUserClubOfTribe(room.getTribeId(), userId);
                int clubId = 0;
                if (!userClubOfTribe.isEmpty()) {
                    clubId = userClubOfTribe.get(0);
                    userChip = userInfoDao.queryUserTribeChip(conn, clubId, userId);
                }
                remark = "在俱乐部玩联盟币:用户的俱乐部ID=" + clubId + ",房间的俱乐部ID=" + room.getClubId();
            } else if (room.getClubRoomType() != 0) { // 俱乐部
                userChip = userInfoDao.queryUserGold(conn, userId);
                remark = "在俱乐部玩金币";
            } else { // 大厅
                UserAccount userAccount = userInfoDao.getUserAccountInfo(conn, userId);
                userChip = userAccount.getChip() + userAccount.getNotExtractChip();
                remark = "在大厅玩金币";
            }
            if (userChip >= minChip) {
                return true;
            }
            logger.info("[R-{}:{}][U-{}][{}]用户餘額不足：chip={},minChip={}",
                    room.getRoomId(), room.getName(), remark, userId, userChip, minChip);
        } catch (
                Exception ex) {
            logger.error("[R-{}:{}][U-{}]查询用户餘額失败",
                    room.getRoomId(), room.getName(), userId, ex);
        }
        return false;
    }

    private static boolean isClubLossLimitNotExceeded(Room room, int userId) {
        ClubTribeDao tribeDao = new ClubTribeDaoImpl();
        List<Integer> userClubOfTribe = tribeDao.getUserClubOfTribe(room.getTribeId(), userId);
        int clubId = 0;
        if (!userClubOfTribe.isEmpty()) {
            clubId = userClubOfTribe.get(0);
        }
        if (clubId > 0 && room.getRoomService().isClubLossLimitExceeded(clubId)) {
            logger.info("[R-{}:{}][U-{}]俱乐部正在虧損狀態,clubId={}",
                    room.getRoomId(), room.getName(), userId, clubId);
            return false;
        }
        return true;
    }

    /**
     * 释放已派遣的AI
     * 坐下不成功 / 站起 / 牌局结束 都需要调用此方法
     *
     * @param roomId         牌局ID，必填
     * @param userId         代表AI的用户ID，可填，如果未填写则表示释放此房间所有的AI
     * @param denyEnterAgain 是否允许在派遣进入本房间,userId存在时有效
     *                       true  不允许
     *                       false 允许
     */
    public static void releaseDispatchedAi(int roomId, Integer userId, boolean denyEnterAgain) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("释放已派遣/锁定的AI:rid=%s , user=%s , denyEnterAgain=%s",
                    roomId, userId, denyEnterAgain));
        }

        List<UserConfigBo> releasedLst = null;
        try {
            if (null != userId && userId <= 0)
                userId = null;
            releasedLst = releaseDispatched(roomId, userId, denyEnterAgain);
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, GsonHelper.toJson(releasedLst, false)));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 根据配置及outs计算是否购买保险
     *
     * @param config 配置项,必填
     * @param outs   outs数量,必填
     * @param roomId 牌桌ID，可选，用于日志
     * @param userId 用户ID，可选，用于日志
     * @return >0  : 购买额比例
     * <=0 ：不购买
     */
    public static int canBuyInsurance(EInsuranceConfig config, int outs, int roomId, int userId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("根据配置及outs计算是否购买保险:rid=%s ，uid=%s , outs=%s , config=%s",
                    roomId, userId, outs, config));
        }

        int buyPriceRatio = 0;
        boolean result = false;
        try {
            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();

            List<InsuanceBuyRuleItemBo> configLst = cache.getInsuanceBuyRuleConfig(config);
            if (null == configLst || configLst.isEmpty()) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    Not found insuance buy rule config ！", NEWLINE));
                }
                return buyPriceRatio;
            }

            for (InsuanceBuyRuleItemBo item : configLst) {
                if (null == item)
                    continue;

                if (item.checkIfMatch(outs)) {
                    result = item.selectBuyOrNot();
                    if (result)
                        buyPriceRatio = item.randomPriceRatio();
                    break;
                }
            }

            return buyPriceRatio;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * Check whether userId is dispatched locally.
     * @param userId
     * @return
     */
    public static boolean checkIfUndispatched(int userId) {
        return !dispatchedUserMap.containsKey(userId);
    }

    /**
     * Check whether userId is dispatched globally.
     * @param userId
     * @return
     */
    public static boolean checkIfUndispatchedGlobally(int userId) {
        return !RedisService.getRedisService().isAiDispatched(userId);
    }

    /**
     * 获取自动模式的玩家带入后牌局未开始的站起时间，单位：秒
     *
     * @param roomId 牌桌ID，可选，用于日志
     * @param userId 用户ID，可选，用于日志
     * @return <= 0 配置数据缺失，无法确定
     * >0   站起时间，单位：秒
     */
    public static int getStandupTimeAfterTakeinInSec(int roomId, int userId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("获取自动模式的带入后牌局未开始的站起时间:rid=%s ，uid=%s", roomId, userId));
        }

        int result = 0;
        try {
            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();

            AutoOverallConfigBo config = cache.getAutoOverallConfig();
            if (null == config) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    Not found overall config ！", NEWLINE));
                }
                return result;
            }

            int startSec = config.getStartSecOfTakeinNotopen();
            int endSec = config.getEndSecOfTakeinNotopen();
            if (null != traceMsg) {
                traceMsg.append(String.format("%s    config : startSec=%s , endSec=%s",
                        NEWLINE, startSec, endSec));
            }
            if (startSec <= 0 && endSec > 0)
                result = endSec;
            else if (startSec > 0 && endSec <= 0)
                result = startSec;
            else if (startSec <= 0 && endSec <= 0) {
                if (null != traceMsg) {
                    traceMsg.append(String.format("%s    config invalid : startSec & endSec not greater than 0！",
                            NEWLINE));
                }
            } else {
                if (startSec > endSec) {
                    if (null != traceMsg) {
                        traceMsg.append(String.format("%s    config invalid : startSec>endSec！",
                                NEWLINE));
                    }
                } else {
                    result = Helper.randomIntBy(startSec, endSec);
                }
            }

            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("%s   返回结果: %s ！", NEWLINE, result));
                logger.debug(traceMsg.toString());
            }
        }
    }

    /**
     * 获取当前所有的AT用户列表
     * 用于派发到房间中做观众
     *
     * @return
     */
    public static List<Integer> getAllUserOfAt() {
        IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
        return cache.getAllUserOfAt();
    }

    public static int recalculateBringIn(Room room ,int userId, int bringRate) {
        int needBringIn = room.getChouma() * bringRate * room.getMinRate();
        logger.debug("[R-{}][U-{}][機器人非整數帶入量] bringRate={}, before change : needBringIn={}",room.getRoomId(),userId,bringRate,needBringIn);
        boolean ifNeedChange = randomCheckIfNeedToChangeBringIn(userId,room.getRoomId());
        if (ifNeedChange) {
            // PP60 AI隨機帶入倍率是 2
            // 帶入量 = (in_chip x min_rate x 2) -  隨機值A  x (n-1) -  隨機值B
            // n = 房間BB數值/ 2 ，即小盲注
            //--------------------------
            // PP60-FE1 假设AI隨機帶入倍率是 1
            // 帶入量 = (in_chip x min_rate x 1) +  隨機值A  x (n-1) + 隨機值B

            int randomA = bringInChangeRandomNum(userId,room.getRoomId());
            int randomB = bringInChangeRandomNum(userId,room.getRoomId());
            //n = 房間BB數值/ 2 ，即小盲注
            int n = room.getManzhu();
            //处理小数点
            if (bringRate > 1) {
                needBringIn = (int) Math.ceil((needBringIn - randomA * (n-1) - randomB) / 100D) * 100;
            }else {
                needBringIn = (int) Math.ceil((needBringIn + randomA * (n-1) + randomB) / 100D) * 100;
            }

            logger.debug("[R-{}][U-{}][機器人非整數帶入量] bringRate={}, need to change bring in: randomA={}, randomB={},n={} ,after change needBringIn={}",
                    room.getRoomId(),userId,bringRate,randomA,randomB,n,needBringIn);
        }
        return needBringIn;
    }

    private static boolean randomCheckIfNeedToChangeBringIn(int userId, int roomId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("[機器人非整數帶入量] 获取是否需要改变带入量:rid=%s , uid=%s ",
                     roomId, userId));
        }
        boolean result = false;
        try {
            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
            CommonBringInVaryRangeConfigBo bringInVaryRangeConfigBo = cache.getCommonBringinConfig().getBringInVaryRange();
            if (null == bringInVaryRangeConfigBo) {
                LogHelper.log("此类型的配置缺失！");
                return false;
            }

            result = bringInVaryRangeConfigBo.randomCheckBringInIfNeedToChange();
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format("返回结果: %s ！", result));
                logger.debug(traceMsg.toString());
            }
        }
    }
    private static int bringInChangeRandomNum(int userId, int roomId) {
        StringBuilder traceMsg = null;
        if (logger.isDebugEnabled()) {
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("[機器人非整數帶入量] 获取改变带入量的随机数:rid=%s , uid=%s ",
                    roomId, userId));
        }
        int result = 0;
        try {
            IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
            CommonBringInVaryRangeConfigBo bringInVaryRange = cache.getCommonBringinConfig().getBringInVaryRange();
            if (null == bringInVaryRange) {
                LogHelper.log("此类型的配置缺失！");
                return 0;
            }

            result = bringInVaryRange.randomVaryOfBringIn();
            return result;
        } finally {
            if (null != traceMsg) {
                LogHelper.removeLog();
                traceMsg.append(String.format(" 返回结果: %s ！", result));
                logger.debug(traceMsg.toString());
            }
        }
    }
}
