package com.ai.dz.config.constant;

/**
 * 阶段性比牌情况
 */
public enum EPerHandPhase {
    preflopWin(1,"翻牌前-赢"),
    preflop<PERSON>oss(2,"翻牌前-输"),
    flopWin(3,"翻牌后-赢"),
    flop<PERSON>oss(4,"翻牌后-输"),
    turnWin(5,"转牌-赢"),
    turn<PERSON><PERSON>(6,"转牌-输"),
    riverWin(7,"河牌-赢"),
    riverLoss(8,"河牌-输");

    private int value;
    /** 描述 */
    private String desc;

    EPerHandPhase(int value,String desc){
        this.value = value;
        this.desc = desc;
    }

    public int value(){
        return this.value;
    }
    public String desc(){
        return this.desc;
    }

    public static EPerHandPhase fromValue(String value){
        if(null == value || "".equals(value.trim()))
            return null;

        EPerHandPhase[] posArr = EPerHandPhase.values();
        for(EPerHandPhase pos : posArr){
            if(String.valueOf(pos.value()).equals(value.trim())){
                return pos;
            }
        }

        return null;
    }
}
