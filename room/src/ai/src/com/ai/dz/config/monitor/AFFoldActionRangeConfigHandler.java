package com.ai.dz.config.monitor;

import com.ai.dz.config.cache.AFFoldActionRangeConfigBo;
import com.ai.dz.config.cache.impl.AiRuleConfigJsonCacheImpl;
import com.ai.dz.config.constant.EAiType;
import com.dzpk.common.utils.Helper;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.file.IFileChangedHandler;
import com.google.common.reflect.TypeToken;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.Logger;

import java.io.FileWriter;
import java.lang.reflect.Type;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

public class AFFoldActionRangeConfigHandler extends AbstractFileChangedHandler
        implements IFileChangedHandler {
    /** 日志服务 */
    private static final Logger logger = LogUtil.getLogger(AFFoldActionRangeConfigHandler.class);

    private String fileName;

    public String fileName(){
        return this.fileName;
    }

    public AFFoldActionRangeConfigHandler(AiRuleConfigJsonCacheImpl cache){
        super(cache);
        this.fileName = "af_fold_config.json";
    }

    public void handle(Path filePath){
        if(null == filePath)
            return;

        try {
            String json = this.readJson(filePath);
            if(null == json || "".equals(json.trim())) {
                if(logger.isDebugEnabled())
                    logger.debug("【AFFoldActionRange】【JYM】file content is empty -> {}",filePath);
                return;
            }

            json = json.trim();
            Type type = new TypeToken<List<AFFoldActionRangeConfigVo>>(){}.getType();
            List<AFFoldActionRangeConfigVo> dataLst = this.parseJson(json,type);
            if(null == dataLst || dataLst.isEmpty()) {
                if(logger.isDebugEnabled())
                    logger.debug("【AFFoldActionRange】【JYM】json is empty -> {}",filePath);
                return;
            }

            List<AFFoldActionRangeConfigBo> configList = new ArrayList<>();
            for(AFFoldActionRangeConfigVo vo : dataLst){
                for(AFFoldAiAction action: vo.aiActions) {
                    for (AFFoldActionRanges ranges: action.actions) {
                        AFFoldActionRangeConfigBo bo = AFFoldActionRangeConfigBo.initialize(vo.getRound(), action.aiType,ranges.weight,ranges.getUnit(), Helper.parseFromDivisionFormat(ranges.getRatios()),ranges.getFold());
                        if(null != bo){
                            configList.add(bo);
                        }
                    }
                }
            }

            this.cache.reloadAFFoldActionRangeConfig(configList);
        }catch (Exception ex){
            logger.warn("Handled 【AFFoldActionRange】【JYM】failed:{} -> {}",ex.getMessage(),filePath);
            if(logger.isDebugEnabled())
                logger.debug(ex);
        }
    }

    @Data
    @Slf4j
    @ToString
    public static class AFFoldActionRangeConfigVo {
        private String round;
        private List<AFFoldAiAction> aiActions;
    }
    @Data
    @Slf4j
    @ToString
    public static class AFFoldAiAction implements Cloneable {
        public AFFoldAiAction(String aiType,List<AFFoldActionRanges> actions) {
            this.aiType = aiType;
            this.actions = actions;
        }

        private String aiType;
        private List<AFFoldActionRanges> actions;

        @Override
        public AFFoldAiAction clone() {
            try {
                return (AFFoldAiAction) super.clone();
            } catch (CloneNotSupportedException e) {
                return null;
            }
        }

    }

    @Data
    @Slf4j
    @ToString
    public static class AFFoldActionRanges implements Cloneable {
        private int weight;

        /**
         * 单位
         * 特殊字符串： BB ， REMAIN ， POT
         */
        private String unit;

        /**
         * ratios
         *
         * ratios 支持的格式
         *  分数格式:    例如: 1/2 , 2/3
         *  浮点数格式：  例如：  1 ， 1.5
         */
        private String ratios;

        /**
         * fold(fold行为的执行几率)
         *  格式： 百分数格式
         *  形如：60.00% 或 60 或 60.00
         */
        private String fold;

        @Override
        public AFFoldActionRanges clone() {
            try {
                return (AFFoldActionRanges) super.clone();
            } catch (CloneNotSupportedException e) {
                return null;
            }
        }

    }
}
