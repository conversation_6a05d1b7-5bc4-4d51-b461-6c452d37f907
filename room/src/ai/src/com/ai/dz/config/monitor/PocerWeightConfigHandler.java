package com.ai.dz.config.monitor;

import com.ai.dz.config.cache.PocerWeightConfigBo;
import com.ai.dz.config.cache.impl.AiRuleConfigJsonCacheImpl;
import com.ai.dz.config.constant.EAiType;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.file.IFileChangedHandler;
import com.google.common.reflect.TypeToken;
import org.apache.logging.log4j.Logger;

import java.io.FileWriter;
import java.lang.reflect.Type;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class PocerWeightConfigHandler extends AbstractFileChangedHandler
        implements IFileChangedHandler {
    /** 日志服务 */
    private static final Logger logger = LogUtil.getLogger(PocerWeightConfigHandler.class);

    private String fileName;

    public String fileName(){
        return this.fileName;
    }

    public PocerWeightConfigHandler(AiRuleConfigJsonCacheImpl cache){
        super(cache);
        this.fileName = "pocer_weight_config.json";
    }

    public void handle(Path filePath){
        if(null == filePath)
            return;

        try {
            String json = this.readJson(filePath);
            if(null == json || "".equals(json.trim())) {
                if(logger.isDebugEnabled())
                    logger.debug("【手牌权重配置】file content is empty -> {}", filePath);
                return;
            }

            json = json.trim();
            // 使用新的Map結構來解析JSON
            Type type = new TypeToken<Map<String, List<PocerWeightConfigBo>>>(){}.getType();
            Map<String, List<PocerWeightConfigBo>> configMap = this.parseJsonAsSingle(json, type);

            if(configMap == null || configMap.isEmpty()) {
                if(logger.isDebugEnabled())
                    logger.debug("【手牌权重配置】json is empty -> {}", filePath);
                return;
            }
            // 遍歷 Map 和 List 的結構
            for (Map.Entry<String, List<PocerWeightConfigBo>> entry : configMap.entrySet()) {
                List<PocerWeightConfigBo> dataLst = entry.getValue();

                if (dataLst == null || dataLst.isEmpty()) {
                    continue;
                }

                Iterator<PocerWeightConfigBo> it = dataLst.iterator();
                while (it.hasNext()) {
                    PocerWeightConfigBo config = it.next();
                    if (config == null) {
                        it.remove();
                        continue;
                    }

                    if (config.getHandPocer() == null ||
                            "".equals(config.getHandPocer().trim())) {
                        it.remove();
                        continue;
                    }

                    String pocer = config.getHandPocer().trim().toUpperCase();
                    pocer = pocer.replace("O", "");
                    pocer = pocer.replace("0", "");
                    config.setHandPocer(pocer);
                }
            }

            // 更新緩存
            this.cache.reloadPocerWeightConfigMap(configMap);

        } catch (Exception ex) {
            logger.warn("Handled 【手牌权重配置】failed:{} -> {}", ex.getMessage(), filePath);
            if(logger.isDebugEnabled())
                logger.debug(ex);
        }
    }
}
