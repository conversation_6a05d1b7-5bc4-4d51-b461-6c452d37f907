package com.ai.dz.config.cache;

import com.dzpk.common.utils.Helper;

public class AutoDispatchPlayerConfigBo implements Comparable<AutoDispatchPlayerConfigBo> {
    /** 空位范围 */
    private Integer unSeatStartNum;
    private Integer unSeatEndNum;

    /** 玩家数量范围 */
    private int min;
    private int max;

    private AutoDispatchPlayerConfigBo(Integer unSeatStartNum,Integer unSeatEndNum,int min,int max){
        this.unSeatStartNum = unSeatStartNum;
        this.unSeatEndNum = unSeatEndNum;
        this.min = min;
        this.max = max;
    }

    public static AutoDispatchPlayerConfigBo initialize(Integer unSeatStartNum,Integer unSeatEndNum,int min,int max){
        return new AutoDispatchPlayerConfigBo(unSeatStartNum, unSeatEndNum, min, max);
    }

    public Integer getUnSeatStartNum() {
        return unSeatStartNum;
    }

    public Integer getUnSeatEndNum() {
        return unSeatEndNum;
    }

    public int getMin() {
        return min;
    }

    public int getMax() {
        return max;
    }

    public int compareTo(AutoDispatchPlayerConfigBo other){
        if(null == other)
            return 1;

        int minResult = Helper.compareTo(this.unSeatStartNum,other.unSeatStartNum,false);
        if(minResult != 0)
            return minResult;

        int maxResult = Helper.compareTo(this.unSeatEndNum,other.unSeatEndNum,true);
        return maxResult;
    }
}
