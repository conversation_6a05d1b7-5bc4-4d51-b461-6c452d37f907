
package com.ai.dz.config.cache;

import com.ai.dz.config.constant.EAiType;
import com.ai.dz.config.constant.EPerHandPhase;
import com.dzpk.common.utils.Helper;
import lombok.Data;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.Random;

/**
 * <AUTHOR>
 */
@Slf4j
@Getter
public class AFFoldActionRangeConfigBo {
    private final EPerHandPhase round;
    private final EAiType aiType;
    private final int weight;
    private final FoldRangeRatio foldRange;

    public static AFFoldActionRangeConfigBo initialize(String round, String aiType, int weight, String unit, double ratio, String foldProbability) {
        int startRatio = 0;
        Integer hitRatio = Helper.parseIntFromPercentFormat(foldProbability, false);
        int minHitRatio = startRatio;
        startRatio += hitRatio;
        int maxHitRatio = startRatio;
        log.trace("round={},aiType={},weight={},unit={},ratio={},foldProbability={}>>>minHitRatio={},maxHitRatio={}",
                round, aiType, weight, unit, ratio, foldProbability,minHitRatio,maxHitRatio);

        return (null == unit || unit.isEmpty()) ||
                (null == foldProbability || foldProbability.isEmpty()) ? null
                : new AFFoldActionRangeConfigBo(round, aiType, weight, new FoldRangeRatio(minHitRatio, maxHitRatio, ratio, ConfigUnit.valueOf(unit.toUpperCase())));

    }

    private AFFoldActionRangeConfigBo(String round, String aiType, int weight, FoldRangeRatio foldRange) {
        this.round = EPerHandPhase.fromValue(round);
        this.aiType = EAiType.fromValue(aiType);
        this.weight = weight;
        this.foldRange = foldRange;
    }

    /**
     * 计算是否需要FOLD
     *
     * @param remain    玩家剩下注码
     * @param bb        大盲注
     * @param pot       底池
     * @param checkChip 上手注码（跟注）
     * @return 是否应该执行FOLD行为
     */
    public Boolean compute(int roomId, int userId, int bb, int pot, int remain, int checkChip) {
        boolean fold = selectIfFoldAction(roomId, userId, bb, pot, remain, checkChip);
        log.debug("【Room-AI】【JYM】【R-{}】【U-{}】【计算是否强制fold传入参数】对手注码({})：大盲注(BB)={},底池(POT)={},当前玩家剩下筹码(REMAIN)={} >> fold={}",
                roomId, userId, checkChip, bb, pot, remain, fold);
        return fold;
    }


    /**
     * 根据跟注注码判断是否应该执行fold行为
     *
     * @param bb        大盲注
     * @param pot       底池
     * @param remain    玩家剩下筹码
     * @param lastBetChip 上家有效下注的注码
     * @return 是否执行FOLD行为
     */
    private boolean selectIfFoldAction(int roomId, int userId, int bb, int pot, int remain, int lastBetChip) {
        if (lastBetChip == 0) {
            //没人跟注，不需要执行FOLD行为
            return false;
        }
        //计算上一手下注额是否超过激进行为规定阀值
        int afValue = 0;
        switch (this.foldRange.unit) {
            case BB:
                afValue = (int) this.foldRange.ratios * bb;
                log.debug("【Room-AI】【R-{}】【U-{}】【JYM】对手注码({})：单位={},fold chip阀值=BB({})*倍数({})={}",
                        roomId, userId, lastBetChip, this.foldRange.unit, bb, this.foldRange.ratios, afValue);
                break;
            case POT://根据需求POT在此处指的是上一手玩家下注前的底池
                afValue = (int) this.foldRange.ratios * pot;
                log.debug("【Room-AI】【R-{}】【U-{}】【JYM】对手注码({})：单位={},fold chip阀值=POT({})*倍数({})={}",
                        roomId, userId, lastBetChip, this.foldRange.unit, pot, this.foldRange.ratios, afValue);
                break;
            case REMAIN:
                afValue = (int) this.foldRange.ratios * remain;
                log.debug("【Room-AI】【R-{}】【U-{}】【JYM】对手注码({})：单位={},fold chip阀值=REMAIN({})*倍数({})={}",
                        roomId, userId, lastBetChip, this.foldRange.unit, remain, this.foldRange.ratios, afValue);
                break;
            default:
                break;
        }

        if (afValue <= lastBetChip) {
            log.debug("【Room-AI】【R-{}】【U-{}】【JYM】【上一手下注额超过激进行为规定阀值】",
                    roomId, userId);
            //根据配置的FOLD几率範圍计算是否需要强制FOLD行为
            boolean foldIsInRange = this.getIsFoldActionInRange(this.foldRange);
            //如果随机数不在FOLD行为的范围内 直接返回否
            if (!foldIsInRange) {
                log.debug("【Room-AI】【R-{}】【U-{}】【JYM】【随机数不在FOLD行为的范围内】不需要强制FOLD",
                        roomId, userId);
            } else {
                log.debug("【Room-AI】【R-{}】【U-{}】【JYM】【随机数在FOLD行为的范围内】强制FOLD",
                        roomId, userId);
            }
            return foldIsInRange;
        }

        log.debug("【Room-AI】【R-{}】【U-{}】【JYM】【上一手下注额没有超过激进行为规定阀值】返回不需要强制FOLD",
                roomId, userId);
        return false;
    }


    /**
     * 加注类型的范围与概率
     */

    @Data
    @Slf4j
    @ToString
    public static class FoldRangeRatio {

        public FoldRangeRatio(int minHitRatio, int maxHitRatio, double ratio, ConfigUnit unit) {
            this.start = minHitRatio;
            this.end = maxHitRatio;
            this.ratios = ratio;
            this.unit = unit;
        }

        int start;//获取这个比例的概率范围区间的开始
        int end;//获取这个比例的概率范围区间的结尾

        ConfigUnit unit;//如：BB 、底池、剩余筹码
        double ratios;//倍数 如2、1/2、0.5


    }

    public enum ConfigUnit {
        /**
         * BB(大盲注)
         */
        BB("BB"),
        /**
         * 底池
         */
        POT("POT"),
        /**
         * 剩余筹码
         */
        REMAIN("REMAIN");

        private final String description;

        ConfigUnit(String description) {
            this.description = description;
        }

        @Override
        public String toString() {
            return description;
        }
    }

    private boolean getIsFoldActionInRange(FoldRangeRatio foldRange) {
        Random random = new Random();
        int totalWeight = 100;
        int randomNum = random.nextInt(totalWeight) + 1;
        boolean isFoldAction = randomNum > foldRange.start && randomNum <= foldRange.end;
        log.debug("[Room-AI][JYM] 随机数={},ranges ={},isFoldAction={}", randomNum, foldRange, isFoldAction);
        return isFoldAction;
    }
}

