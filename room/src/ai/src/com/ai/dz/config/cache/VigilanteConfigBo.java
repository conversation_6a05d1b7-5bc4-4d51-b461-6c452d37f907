package com.ai.dz.config.cache;

import com.dzpk.common.utils.Helper;
import com.dzpk.vigilance.auto.AutoVigilanceConfig;
import com.dzpk.vigilance.auto.rules.FlagByAllUserProfit;
import com.dzpk.vigilance.auto.rules.FlagByLifeCycleProfit;
import com.dzpk.vigilance.auto.rules.FlagByRoomProfit;
import com.dzpk.vigilance.auto.rules.UnflagByUserProfit;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@Slf4j
@Getter
public class VigilanteConfigBo {
    /** 功能开关 */
    private final boolean enabled;
    
    /** 最小胜出牌型 (1-10) */
    private final int winTypeMin;
    
    /** 翻牌前allin最小权重 (1-17) */
    private final int preFlopAllinWeightMin;

    /** 翻牌前最小权重 (1-17) */
    private final int preFlopWeightMin;

    /** 翻牌前动作最小权重 (1-17) */
    private final int preFlopActionWeightMin;

    /** 翻牌后牌型 */
    private final Map<String, Boolean> flopHandType;
    
    /** 搜索迭代次数 */
    private final int searchIterations;
    
    /** 搜索超时时间(毫秒) */
    private final int searchTimeout;
    
    /** 玩家规则列表 */
    private final List<PlayerRule> playerRules;

    private final List<AutoFlagRule> autoFlagRules;
    private final List<AutoUnflagRule> autoUnflagRules;

    @Getter
    @Setter
    public static class PlayerRule {
        /** 玩家数量范围-最小值 */
        private int min;
        
        /** 玩家数量范围-最大值 */
        private int max;
        
        /** 底池规则列表 */
        private List<PotRule> potRules;
    }

    @Getter
    @Setter
    public static class PotRule {
        /** 底池大于等于大盲注的倍数 */
        private int gteXBB;
        
        /** 
         * 触发概率
         * 格式：百分数格式
         * 形如：60% 或 60
         */
        private int chance;

        public boolean roll() {
            return Helper.randomIntBy(1, 100) <= chance;
        }
    }

    public PotRule selectPotRule(int players, int pot, int bb) {
        for (PlayerRule playerRule : playerRules) {
            if (players >= playerRule.getMin() && players <= playerRule.getMax()) {
                log.trace("{} <= {} <= {}", playerRule.getMin(), players, playerRule.getMax());
                for (PotRule potRule : playerRule.getPotRules()) {
                    if (pot >= potRule.getGteXBB() * bb) {
                        log.trace("{} >= {} * {}", pot, potRule.getGteXBB(), bb);
                        return potRule;
                    }
                    log.trace("{} < {} * {}", pot, potRule.getGteXBB(), bb);
                }
            } else {
                log.trace("{} not in [{}, {}]", players, playerRule.getMin(), playerRule.getMax());
            }
        }
        return null;
    }

    @Getter
    @Setter
    public static class AutoRule implements AutoVigilanceConfig.AutoRuleConfig {
        private String type;
        private boolean enabled;
        private int checkInterval;
    }

    @Getter
    @Setter
    public static class AutoFlagRule extends AutoRule implements AutoVigilanceConfig.AutoFlagRuleConfig {
        private String comment;
    }

    @Getter
    @Setter
    public static class AutoUnflagRule extends AutoRule implements AutoVigilanceConfig.AutoUnflagRuleConfig {
    }

    @Getter
    @Setter
    public static class FlagByAllUserProfitRule extends AutoFlagRule implements FlagByAllUserProfit.Config {
        private int checkPeriod;
        private int maxProfit;
    }

    @Getter
    @Setter
    public static class FlagByRoomProfitRule extends AutoFlagRule implements FlagByRoomProfit.Config {
        private int maxProfitXBB;
    }

    @Getter
    @Setter
    public static class FlagByLifeCycleProfitRule extends AutoFlagRule implements FlagByLifeCycleProfit.Config {
        private int maxProfit;
    }

    @Getter
    @Setter
    public static class UnflagByUserProfitRule extends AutoUnflagRule implements UnflagByUserProfit.Config {
        private int checkPeriod;
        private int minProfit;
    }

    private VigilanteConfigBo(boolean enabled, 
                             int winTypeMin,
                             int preFlopAllinWeightMin,
                             int preFlopWeightMin,
                             int preFlopActionWeightMin,
                             Map<String, Boolean> flopHandType,
                             int searchIterations,
                             int searchTimeout,
                             List<PlayerRule> playerRules,
                             List<AutoFlagRule> autoFlagRules,
                             List<AutoUnflagRule> autoUnflagRules) {
        this.enabled = enabled;
        this.winTypeMin = winTypeMin;
        this.preFlopAllinWeightMin = preFlopAllinWeightMin;
        this.preFlopWeightMin = preFlopWeightMin;
        this.preFlopActionWeightMin = preFlopActionWeightMin;
        this.flopHandType = flopHandType;
        this.searchIterations = searchIterations;
        this.searchTimeout = searchTimeout;
        this.playerRules = playerRules;
        this.autoFlagRules = autoFlagRules;
        this.autoUnflagRules = autoUnflagRules;
    }

    public static VigilanteConfigBo initialize(boolean enabled,
                                             int winTypeMin,
                                             int preFlopAllinWeightMin,
                                             int preFlopWeightMin,
                                             int preFlopActionWeightMin,
                                             Map<String, Boolean> flopHandType,
                                             int searchIterations,
                                             int searchTimeout,
                                             List<PlayerRule> playerRules,
                                             List<AutoFlagRule> autoFlagRules,
                                             List<AutoUnflagRule> autoUnflagRules) {
        return new VigilanteConfigBo(enabled,
                                    winTypeMin,
                                    preFlopAllinWeightMin,
                                    preFlopWeightMin,
                                    preFlopActionWeightMin,
                                    flopHandType,
                                    searchIterations,
                                    searchTimeout,
                                    playerRules,
                                    autoFlagRules,
                                    autoUnflagRules);
    }

}