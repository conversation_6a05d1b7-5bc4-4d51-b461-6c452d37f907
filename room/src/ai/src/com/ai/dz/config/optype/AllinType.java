package com.ai.dz.config.optype;

public class AllinType extends OperationType{
    /** 第一版： 弃用
     * 当前手所有玩家的初始筹码总数
     */
    @Deprecated
    private int totalChipOfHand;

    /** 第一版： 弃用
     * 当前手所有玩家数量
     */
    @Deprecated
    private int playerNumOfHand;

    /**  第二版：弃用
     * 每一轮所有allin操作中,allin下的最大筹码数
     */
    @Deprecated
    private int maxChipOfAllin;

    /**
     * 第二版：弃用
     * 最新的逻辑：
     * AI当前剩余筹码的50%
     */
    @Deprecated
    private int remainChip;

    /**
     * 第三版：分两种情况
     * 1. cp/当手AI已经打出去的筹码>=25% 或 当手AI已经打出去的筹码=0
     * 2. cp/当手AI已经打出去的筹码<25%
     * 当手AI已经打出去的筹码
     */
    private int raisedChipOfHand;

    /**
     * 跟注筹码（cp）
     */
    private int checkChip;

    public int getRemainChip() {
        return remainChip;
    }

    public void setRemainChip(int remainChip) {
        this.remainChip = remainChip;
    }

    public int getMaxChipOfAllin() {
        return maxChipOfAllin;
    }

    public void setMaxChipOfAllin(int maxChipOfAllin) {
        this.maxChipOfAllin = maxChipOfAllin;
    }

    public int getTotalChipOfHand() {
        return totalChipOfHand;
    }

    public void setTotalChipOfHand(int totalChipOfHand) {
        this.totalChipOfHand = totalChipOfHand;
    }

    public int getPlayerNumOfHand() {
        return playerNumOfHand;
    }

    public void setPlayerNumOfHand(int playerNumOfHand) {
        this.playerNumOfHand = playerNumOfHand;
    }

    public int getRaisedChipOfHand() {
        return raisedChipOfHand;
    }

    public void setRaisedChipOfHand(int raisedChipOfHand) {
        this.raisedChipOfHand = raisedChipOfHand;
    }

    public int getCheckChip() {
        return checkChip;
    }

    public void setCheckChip(int checkChip) {
        this.checkChip = checkChip;
    }

    @Override
    public String toString(){
        return String.format("%s opNum=%s , raisedChipOfHand=%s , checkChip=%s %s",
                "{",
                this.getPlayerNumOfOp(),
                this.getRaisedChipOfHand(),
                this.getCheckChip(),
                "}");
    }
}
