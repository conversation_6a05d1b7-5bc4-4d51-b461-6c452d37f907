package com.ai.dz.config.cache;

import com.ai.dz.config.constant.EAction;
import com.ai.dz.config.constant.EFlowCase;
import com.dzpk.common.utils.Helper;
import com.dzpk.common.utils.LogHelper;
import lombok.Getter;

@Getter
public class BetActionConfigBo {
    /** 对应本来类的动作标识 */
    private EAction action = EAction.bet;

    /**
     * 满足条件的比例
     * null或小于0 表示无效，不进行此条件判断
     */
    private Integer gtXBB=null;

    /**
     * 满足条件的比例: 剩余筹码比例
     * null或小于0 表示无效，不进行此条件判断
     * 80% -> 80
     */
    private Integer gteRemainRatio=null;

    private BetActionConfigBo(Integer gtXBB, Integer gteRemainRatio){
        this.gtXBB = gtXBB;
        this.gteRemainRatio = gteRemainRatio;
    }

    public static BetActionConfigBo initialize(Integer gtXBB , int gteRemainRatio){
        return new BetActionConfigBo(gtXBB,gteRemainRatio);
    }

    /**
     * win-preflop : 若bet值为XX，if((XX/当前筹码)>=70%) {allin} else {bet}
     * win-broad   : 若bet值为XX，if((XX/当前筹码)>=70%) {allin} else {bet}
     * lose-preflop: 若bet值为XX，if((XX/当前筹码)>=70%) {allin} else {bet}
     * lose-board  : 若bet值为XX，if((XX/当前筹码)>=70%) {allin} else {bet}
     *
     * @param flowCase     流程
     * @param betChip      Bet筹码
     * @param remainChip   剩余筹码
     * @param checkChip    cp筹码
     * @param bb           盲注
     *
     * @return  返回Action
     *    null  则表示不满足条件或条件缺失
     */
    public EAction selectAction(EFlowCase flowCase,int betChip, int remainChip, int checkChip, int bb){
        /*if(flowCase == EFlowCase.perflopWin)
            return this.winPreflop(flowCase,betChip,remainChip,checkChip,bb);
        else if(flowCase == EFlowCase.flopWin)
            return this.winBoard(flowCase,betChip,remainChip,checkChip);
        else
            return this.loseFlow(flowCase,betChip,remainChip,checkChip,bb);*/
        return this.winBoard(flowCase,betChip,remainChip,checkChip);
    }

    /**
     * win-preflop : 若bet值为XX，
     * if((XX/当前筹码 )>=70%) {
     *   if(XX<40BB) {allin} else {
     *     if(CP=0){CHECK} else {fold}
     *   }
     * } else {bet}
     *
     * @param betChip      Bet筹码
     * @param remainChip   剩余筹码
     * @param checkChip    cp筹码
     * @param bb           盲注
     *
     * @return  返回Action
     *    null  则表示不满足条件或条件缺失
     */
    private EAction winPreflop(EFlowCase flowCase,int betChip, int remainChip, int checkChip, int bb) {
        EAction resultAction = this.action;
        String ratioStr = "配置无效";
        boolean ratioMatch = false;
        String bbStr = "配置无效";
        boolean bbMatch = false;

        try {
            // 配置必须存在
            if (null == this.gteRemainRatio || this.gteRemainRatio < 0 ||
                    null == this.gtXBB || this.gtXBB< 0)
                return resultAction;

            int computeRatio = Helper.fixDecimalPlace(Helper.divide(betChip, remainChip),100);
            ratioStr = String.valueOf(computeRatio);
            if (computeRatio >= this.gteRemainRatio){
                ratioMatch = true;

                // BB
                computeRatio = Helper.fixDecimalPlace(Helper.multiply(this.gtXBB,bb),0);
                bbStr = String.valueOf(computeRatio);
                if(betChip < computeRatio){
                    resultAction = EAction.allin;
                    bbMatch = true;
                }else{
                    if(checkChip==0)
                        resultAction = EAction.check;
                    else
                        resultAction = EAction.fold;
                }
            }

            return resultAction;
        } finally {
            LogHelper.log("%s   %s-%s转换的Action:%sChip=%s,remainChip=%s,checkChip=%s,bb=%s" +
                            "-> remainRatioMatched[ %sChip(%s) / remainChip(%s) * 100 = %s 大于或等于 gteRemainRatio(%s) ]=%s," +
                            "BBMatched[ %sChip(%s) 小于 bb(%s) * gtXBB(%s) = %s ]=%s => %s !",
                    System.lineSeparator(), flowCase, this.action, this.action, betChip, remainChip, checkChip, bb,
                    this.action,betChip,remainChip,ratioStr,this.gteRemainRatio,ratioMatch,
                    this.action,betChip,bb,this.gtXBB,  bbStr,bbMatch, resultAction);
        }
    }

    /**
     * win-broad   : 若bet值为XX，if((XX/当前筹码)>=70%) {allin} else {bet}
     *
     * @param betChip      Bet筹码
     * @param remainChip   剩余筹码
     * @param checkChip    cp筹码
     *
     * @return  返回Action
     *    null  则表示不满足条件或条件缺失
     */
    private EAction winBoard(EFlowCase flowCase,int betChip, int remainChip, int checkChip){
        EAction resultAction = this.action;
        String ratioStr = "配置无效";
        boolean ratioMatch = false;

        try {
            // 配置必须存在
            if (null == this.gteRemainRatio || this.gteRemainRatio < 0)
                return resultAction;

            int computeRatio = Helper.fixDecimalPlace(Helper.divide(betChip, remainChip),100);
            if (computeRatio >= this.gteRemainRatio){
                ratioMatch = true;
                resultAction = EAction.allin;
            }

            ratioStr = String.format("%sChip(%s) / remainChip(%s) * 100 = %s 大于或等于 gteRemainRatio(%s)【%s】",
                    this.action,betChip,remainChip,computeRatio,this.gteRemainRatio,ratioMatch);
            return resultAction;
        } finally {
            LogHelper.log("%s   %s-%s转换的Action:%sChip=%s,remainChip=%s,checkChip=%s " +
                            "-> %s -> %s !",
                    System.lineSeparator(), flowCase, this.action, this.action, betChip, remainChip, checkChip,
                    ratioStr,resultAction);
        }
    }

    /**
     * lose-preflop: 若bet值为XX，if(XX>YY BB ||XX/当前筹码 >=45%) {if(cp=0) {check} else {fold}} else {bet}
     * lose-board  : 若bet值为XX，if(XX>YY BB ||XX/当前筹码 >=45%) {if(cp=0) {check} else {fold}} else {bet}
     *
     * @param betChip      Bet筹码
     * @param remainChip   剩余筹码
     * @param checkChip    cp筹码
     * @param bb           盲注
     *
     * @return  返回Action
     *    null  则表示不满足条件或条件缺失
     */
    private EAction loseFlow(EFlowCase flowCase,int betChip, int remainChip, int checkChip, int bb){
        EAction resultAction = this.action;
        String ratioStr = "配置无效";
        boolean ratioMatch = false;
        String bbStr = "配置无效";
        boolean bbMatch = false;

        try {
            // 剩余筹码
            if (null != this.gteRemainRatio && this.gteRemainRatio >= 0){
                int computeRatio = Helper.fixDecimalPlace(Helper.divide(betChip, remainChip),100);
                ratioStr = String.valueOf(computeRatio);
                if (computeRatio >= this.gteRemainRatio) {
                    ratioMatch = true;
                }
            }

            // BB
            if (null != this.gtXBB && this.gtXBB >= 0){
                int computeBB = Helper.fixDecimalPlace(Helper.multiply(this.gtXBB,bb),0);
                bbStr = String.valueOf(computeBB);
                if(betChip > computeBB) {
                    bbMatch = true;
                }
            }

            if(bbMatch || ratioMatch){
                if(checkChip==0)
                    resultAction = EAction.check;
                else
                    resultAction = EAction.fold;
            }

            return resultAction;
        } finally {
            LogHelper.log("%s   %s-%s转换的Action:%sChip=%s,remainChip=%s,checkChip=%s,bb=%s" +
                            "-> remainRatioMatched[ %sChip(%s) / remainChip(%s) * 100 = %s 大于或等于 gteRemainRatio(%s) ]=%s," +
                            "BBMatched[ %sChip(%s) 小于 bb(%s) * gtXBB(%s) = %s ]=%s => %s !",
                    System.lineSeparator(), flowCase, this.action, this.action, betChip, remainChip, checkChip, bb,
                    this.action,betChip,remainChip,ratioStr,this.gteRemainRatio,ratioMatch,
                    this.action,betChip,bb,this.gtXBB,  bbStr,bbMatch, resultAction);
        }
    }
}
