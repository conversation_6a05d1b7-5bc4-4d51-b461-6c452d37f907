package com.ai.dz.config.cache;

import com.dzpk.common.utils.LogHelper;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

public class FlowCaseActionConfigBo {
    /** 按【权重】范围升序 */
    private WeightActionConfigBo[] actionConfigArr = null;
    public int size(){
        return this.actionConfigArr==null?0:this.actionConfigArr.length;
    }

    private FlowCaseActionConfigBo(WeightActionConfigBo[] actionConfigArr){
        this.actionConfigArr = actionConfigArr;
    }

    public WeightActionConfigBo findActionConfigBy(int weight) {
        WeightActionConfigBo result = null;
        Integer minWeight = null;
        Integer maxWeigth = null;
        String actionConfitMsg = "未匹配";

        try {
            if (null == this.actionConfigArr ||
                    this.actionConfigArr.length == 0) {
                actionConfitMsg = "配置缺失";
                return result;
            }

            for (WeightActionConfigBo bo : this.actionConfigArr) {
                if (bo.hitInWeightRange(weight)) {
                    result = bo;
                    minWeight = bo.getMinWeight();
                    maxWeigth = bo.getMaxWeight();
                    actionConfitMsg = "size="+this.actionConfigArr.length;
                    break;
                }
            }
        } finally {
            LogHelper.log("%s   【权重Action配置】: weight=%s, minWeight=%s, maxWeight=%s -> %s",
                    System.lineSeparator(), weight,
                    minWeight,maxWeigth,
                    actionConfitMsg);
        }

        return result;
    }

    /**
     * 初始化
     * @param actionConfigLst
     * @return
     */
    public static FlowCaseActionConfigBo initialize(List<WeightActionConfigBo> actionConfigLst){
        FlowCaseActionConfigBo result = null;

        if(null == actionConfigLst || actionConfigLst.isEmpty())
            return result;

        Iterator<WeightActionConfigBo> it = actionConfigLst.iterator();
        while(it.hasNext()){
            WeightActionConfigBo config = it.next();
            if(null == config)
                it.remove();
        }

        if(actionConfigLst.isEmpty())
            return result;

        WeightActionConfigBo[] tempArr = new WeightActionConfigBo[actionConfigLst.size()];
        tempArr = actionConfigLst.toArray(tempArr);
        Arrays.sort(tempArr);

        result = new FlowCaseActionConfigBo(tempArr);
        return result;
    }
}
