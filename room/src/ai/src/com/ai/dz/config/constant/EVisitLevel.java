package com.ai.dz.config.constant;

/**
 * 可见级别
 */
public enum EVisitLevel {
    /** 普通用户 */
    normal(1),
    /** 超级用户 */
    operator(2);

    private int value;

    EVisitLevel(int value){
        this.value = value;
    }

    public int value(){
        return this.value;
    }

    public static EVisitLevel of(String str){
        if(null == str || "".equals(str.trim()))
            return null;

        str = str.trim().toUpperCase();
        EVisitLevel[] posArr = EVisitLevel.values();
        for(EVisitLevel pos : posArr){
            if(pos.name().toUpperCase().equals(str)){
                return pos;
            }
        }

        return null;
    }

    public static EVisitLevel getTypeByValue(int type){
        if(type <= 0){
            return null;
        }
        EVisitLevel[] posArr = EVisitLevel.values();

        for(EVisitLevel pos : posArr){
            if(pos.value() == type){
                return pos;
            }
        }

        return null;
    }
}
