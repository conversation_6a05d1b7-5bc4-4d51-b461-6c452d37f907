package com.ai.dz.config.constant;

/**
 * AI类型
 */
public enum EBringinType {
    /** 首次/补充带入 */
    bringin(1),
    /** 整体带入 */
    total(2);

    private int value;

    EBringinType(int value){
        this.value = value;
    }

    public int value(){
        return this.value;
    }

    public static EBringinType of(String str){
        if(null == str || "".equals(str.trim()))
            return null;

        str = str.trim().toUpperCase();
        EBringinType[] posArr = EBringinType.values();
        for(EBringinType pos : posArr){
            if(pos.name().toUpperCase().equals(str)){
                return pos;
            }
        }

        return null;
    }

    public static EBringinType getTypeByValue(int type){
        if(type <= 0){
            return null;
        }
        EBringinType[] posArr = EBringinType.values();

        for(EBringinType pos : posArr){
            if(pos.value() == type){
                return pos;
            }
        }

        return null;
    }
}
