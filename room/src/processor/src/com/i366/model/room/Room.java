
package com.i366.model.room;

import com.ai.dz.config.AiRoomManager;
import com.ai.dz.config.AiRuleTemplate;
import com.ai.dz.config.constant.EAiMode;
import com.ai.dz.room.init.AtRoomConfig;
import com.ai.dz.room.util.AiInsurer;
import com.ai.dz.vigilance.AiVigilante;
import com.dzpk.activity.ActivityConfig;
import com.dzpk.activity.redwallet.IRedWalletService;
import com.dzpk.activity.redwallet.RedWalletFactory;
import com.dzpk.commission.CommissionFactory;
import com.dzpk.commission.FeeManagerAdapter;
import com.dzpk.commission.IFeeService;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.db.dao.RoomDao;
import com.dzpk.db.imp.RoomDaoImpl;
import com.dzpk.db.model.AtGamePlan;
import com.dzpk.dealer.Dealer;
import com.dzpk.eventtrack.EventTrackService;
import com.dzpk.eventtrack.impl.EventTrackServiceImpl;
import com.dzpk.insurance.Holder;
import com.dzpk.insurance.Insurer;
import com.dzpk.jackpot.IJackpotService;
import com.dzpk.jackpot.JackpotFactory;
import com.dzpk.record.AnteAction;
import com.dzpk.record.ReplayCache;
import com.dzpk.record.RoomRecord;
import com.dzpk.record.RoomReplay;
import com.dzpk.vigilance.HighRiskPlayerDaoRegistry;
import com.dzpk.vigilance.HighRiskPlayerRegistry;
import com.dzpk.vigilance.VigilanceProviderManager;
import com.dzpk.vigilance.Vigilante;
import com.dzpk.weight.model.UserWeightRecord;
import com.dzpk.work.Task;
import com.dzpk.work.TaskConstant;
import com.dzpk.work.WorkThreadService;
import com.i366.cache.Cache;
import com.i366.constant.Constant;
import com.i366.constant.LeaveRoomCode;
import com.i366.constant.RemoveUserCode;
import com.i366.constant.StandUpRoomCode;
import com.i366.model.player.RoomPersion;
import com.i366.model.player.RoomPlayer;
import com.i366.model.player.StandUpUserInfo;
import com.i366.model.pocer.Pocer;
import com.i366.model.pocer.PocerLink;
import com.i366.model.pocer.PocerLinkInterceptorList;
import com.i366.model.pocer.PocerLinkFactory;
import com.i366.room.RoomProcedure;
import com.i366.room.RoomSeatChangeService;
import com.i366.room.RoomService;
import com.i366.util.JackPotUtil;
import com.i366.util.PublisherUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import lombok.Getter;
import org.apache.logging.log4j.Logger;
import org.bson.types.ObjectId;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;


/**
 * 游戏房间
 */
public class Room {

    private static final Logger logger = LogUtil.getLogger(Room.class);

    private Map<Integer, RoomPersion> audMap;                        //  当前进入房间的人
    private final Set<Integer> leaveAud = new LinkedHashSet<>(100);          //  不在房间内的观众
    private static Random r = new Random();
    private int roomId;                                             //  房间ID
    private String roomUUID = null;                                 //  房间全局唯一ID
    private int roomPath;                                           //  房间路径
    private int status = 1;                                         //  1待开局 2已经开局

    private PocerLink pocerLink;                                    //  一个房间一副扑克牌

    /**
     * 0房主开始游戏前 //新增
     * 1初始等待发牌状态
     * 2倒计时(发每人两张牌)
     * 3第一轮投注(发三张公共牌)
     * 4第二轮投注(发第四张公共牌)
     * 5第三轮投注(发第五张公共牌)
     * 6第四轮投注
     * 7投注完成 分配奖金
     * 8房间暂停
     */
    private int roomStatus;
    private int currentNumber = -999;                               //  当前操作玩家编号
    private int currentUserStatus = -999;                           //  当前操作玩家状态
    public int manzhuNumber = -999;                                 //  小盲注玩家编号
    public int damanzhuNumber = -999;                               //  大盲注玩家编号
    public int zhuangjiaNumber = -999;                              //  庄家编号
    public int guanshaNumber = -999;                                //  关煞位 (庄家右手边的第一个位置)
    private int straddleNumber=-999;                                 //抓头位
    private int maxChouma = 0;                                      //  当前桌面最大筹码
    private int minJaZhuChoma=0;                                    //  当前桌面有效最小加注筹码
    private int maxJaZhuChoma=0;                                    //  当前桌面有效最大加注筹码
    private int currentMaxChouma = 0;                               //  当前轮一开始的最大筹码
    private int manzhu;                                             //  盲注
    private int damanzhu;                                           //  大盲注
    private int wait;
    private int chouma;                                             //  需要携带的筹码量
    private int qianzhu;                                            //  前注
    private int minChip;                                            //  大盲注和前注之和(牌局开始前的筹码最低下限)
    private long t2 = 0;                                            //  房间倒计时
    private long t3;                                                //  玩家倒计时
    private long gameBeginTime = 0;                                 //  牌局开始时间
    private int jiaZhuCount = 0;                                    //  加注的次数
    private int genZhuCount = 0;                                    //  跟注的次数
    private int qiPaiCount = 0;                                     //  弃牌的次数
    private int actionCount = 0;                                    //  本轮玩家操作次数
    private int owner = -1;                                         //	房主
    private int maxPlayTime = -1;                                   //	牌局时间
    private int playerCount;                                        //  牌桌人数
    private int lastBetPlayer;                                      //  一局游戏，游戏第一轮加注的最后一个操作玩家
    private boolean isLastBetPlayerOp = false;                      //  一局游戏，最后一个下注玩家是否下注，防止没有操作就直接比牌
    private boolean control = false;                                //  控制带入 0关闭 1打开
    private int insurance = 0;                                      //  保险模式 0关闭 1打开
    private boolean insuranceActive = false;                        //  保险模式是否激活
    private int insuranceChip = 0;                                  //  房间内保险总收支
    private int insuranceLast = 0;                                  //  房间最近一手的保险收支
    private Set<Integer> winWithoutInsuranceUserIds = new HashSet<Integer>();  //  不计保险营收的赢牌用户集合
    private int poolChip = 0;                                        //  每局游戏池底筹码数
    /**
     * 翻牌前最初池底筹码数
     */
    private int basePoolChip = 0;
    /**
     * 翻牌、转牌、河牌的初始池底筹码数（每轮的初始底池）
     */
    private int stageInitPoolChip = 0;
    private int firstHandChip;                                       //上一手加注和跟住的筹码
    private boolean isPause = false;                                //  房主是否暂停了牌局，默认未暂停
    private long pauseBeginTime = 0;                                //  暂停开始时间 单位：ms
    private int lastRoomStatus = 0;                                    //  暂停前房间状态
    private long insuranceBeginTime = 0;                            //  保险开始时间 单位:ms
    private long pauseTime = 0;                                        //  暂停消耗时间 单位：ms
    private long pauseUserLeftOpTime = 0;                           //  暂停状态下当前操作玩家剩余操作时间
    private int delayTime = 0;                                        //  个人操作请求延时时间
    private int minRaiseNumber = 0;                                 //  最低加注金额限制
    private int raisePerson = -1;                                   //  达到最低加注金额的玩家
    private int minRate;                                            //  最小带入倍数
    private int maxRate;                                            //  最大带入倍数
    private int requestMinRate = 0;                                 //  请求修改下局最小带入倍数
    private int requestMaxRate = 0;                                 //  请求修改下局最大带入倍数
    private String name = "";                                       //  房间名字
    private boolean limitIp = false;                                //  是否开启ip限制
    private boolean limitGPS = false;                               //  是否开启gps限制  
    private int playType = 1;                                       //  打牌类型：0坐下既打 1过庄
    private boolean straddle = false;                               //  straddle局 false关 true开
    private boolean isClub = false;                                 //  是否是社区房 0不是 1是
    private long minPlayTime = 0;                                   //  最短打牌时间(s) 0表示无限制 >0则为最短时间
    private int clubId;                                             //  社区id
    private boolean muck = false;                                   //  触发盖牌
    private int muckAttackId = -1;                                  //  盖牌攻击者
    private int attackId = -1;                                      //  本轮最后的下注者(本轮第一个达到桌面上最大筹码的人)
    private int thisRoundPoolChip=0;                                //  本轮的底池
    private int preAttackId = -1;                                   //  上轮最后的下注者(上轮第一个达到桌面上最大筹码的人)
    private int betFirstId = -1;                                    //  当轮第一个操作的座位号(除了第一轮)
    private int tribeId = 0;                                        //  联盟id
    private String tribeName = "";                                  //  联盟名称
    private int muckSwitch = 1;                                     //  muck开关
    private int spectatorVoiceSwitch = 0;                           //  观众语音 0关闭1开启
	private int aheadLeaveMode = 0;                           		//  是否提前离桌模式  0否 1是
    private int opTime = 15;                                        //  房间思考时间 默认15s
    private int currentPlayerCount = -1;                            //  当前手有手牌能够参与游戏的玩家数，每手发牌时记录，发牌前置为-1
    private boolean autoStartRoom = false;                          //  是否自动开局
    private boolean jackPot = false;                                //  是否开启jackpot
    private int autoStartPlayerCount = 2;                           //  自动开局的人数
    private int firstOpSize = -1;                                   //  每手第一轮行动的人
    private long createTime;                                        //  牌局创建时间
    private Double pump;                                            //抽水的比例
    private Integer maxbet;
    private BigDecimal JKpump;                                      //彩金服务费比例
    private boolean insuranceRebate;                                   //保险返利是否开启
    private boolean canSeeHandCard;                            //是否延迟看牌
    private int pumpingMode;                               //抽水方式0为按有效池抽取1为用户赢取抽取
    private Map<Integer,Integer> plFee =new HashMap<>();                      //房间内的费用
    //第一次转牌投入的金额每一手结束重新接入
    private Map<Integer,Integer> oneInsurer=new HashMap<>();
    //第二次和牌投入的金额每一手结束重新接入
    private Map<Integer,Integer> twoInsurer =new HashMap<>();
    //上个有效下注玩家
    private int lastBet;
    //上上个有效下注玩家；
    private int lastAndLastBet;
    //补盲用户
    private Map<Integer,Integer> buMangPlay=new HashMap<>();
    //适用第一次
    private Map<Integer,Integer> disposableBuMangPlay=new HashMap<>();
    //是否为俱乐部牌局 0-大厅 1-俱乐部 2-私人
    private Integer clubRoomType;
    //是否同步联盟
    private Integer tribeRoomType;
    //是否请求带入0：false，1：true
    private boolean requestToBringIn;
    //最小入池率
    private int minVpip;
    //创建房间时扣除的钻石
    private int chipSpent;
    // 牌局层级ID
    private int tierId;

    @Getter
    private Vigilante vigilante;

    private final HighRiskPlayerRegistry highRiskPlayerRegistry = new HighRiskPlayerDaoRegistry();

    //指定房间派遣配配
    private AtGamePlan atGamePlan;
    public void setAtGamePlan(AtGamePlan gamePlan) {this.atGamePlan = gamePlan;}
    public AtGamePlan getAtGamePlan(){return atGamePlan;}


    public int getChipSpent() {
        return chipSpent;
    }

    public void setChipSpent(int chipSpent) {
        this.chipSpent = chipSpent;
    }

    private Set<Integer> tiqianjiesunSet = new HashSet<>();

    public Set<Integer> getTiqianjiesunSet() {
        return tiqianjiesunSet;
    }

    public void setTiqianjiesunSet(Set<Integer> tiqianjiesunSet) {
        this.tiqianjiesunSet = tiqianjiesunSet;
    }

    private final AtomicLong nextEventId = new AtomicLong(1);

    public long getNextEventId() {
        return nextEventId.getAndIncrement();
    }

    public boolean isRequestToBringIn() {
        return requestToBringIn;
    }

    public void setRequestToBringIn(boolean requestToBringIn) {
        this.requestToBringIn = requestToBringIn;
    }

    public Integer getClubRoomType() {
        return clubRoomType;
    }

    public void setClubRoomType(Integer clubRoomType) {
        this.clubRoomType = clubRoomType;
    }

    private void clearBuMangList(){
        buMangPlay.clear();
        disposableBuMangPlay.clear();
    }
    public Map<Integer, Integer> getDisposableBuMangPlay() {
        return disposableBuMangPlay;
    }

    public int getStraddleNumber() {
        return straddleNumber;
    }

    public void setStraddleNumber(int straddleNumber) {
        this.straddleNumber = straddleNumber;
    }

    public Map<Integer, Integer> getBuMangPlay() {
        return buMangPlay;
    }

    public void addGenZhuCount(){
        genZhuCount++;
    }
    public void addQiPaiCount(){
        qiPaiCount++;
    }
    public int getLastBet() {
        return lastBet;
    }

    public void setLastBet(int lastBet) {
        this.lastBet = lastBet;
    }

    public int getLastAndLastBet() {
        return lastAndLastBet;
    }

    public void setLastAndLastBet(int lastAndLastBet) {
        this.lastAndLastBet = lastAndLastBet;
    }

    public int getMinJaZhuChoma() {
        return minJaZhuChoma;
    }

    public void setMinJaZhuChoma(int minJaZhuChoma) {
        this.minJaZhuChoma = minJaZhuChoma;
    }

    public int getMaxJaZhuChoma() {
        return maxJaZhuChoma;
    }

    public void setMaxJaZhuChoma(int maxJaZhuChoma) {
        this.maxJaZhuChoma = maxJaZhuChoma;
    }

    public int getFirstHandChip() {
        return firstHandChip;
    }

    public void setFirstHandChip(int firstHandChip) {
        this.firstHandChip = firstHandChip;
    }
    public int getMinJaZhu(int betChip){
        int betChip1 = maxJaZhuChoma+maxJaZhuChoma-minJaZhuChoma-betChip;
        betChip1=betChip1<=0?damanzhu:betChip1;
        return betChip1;
    }
    public int getMin2JaZhu(int betChip,int qianzhu){
        if (roomStatus<=3&&lastBet==0){
            lastBet=straddleNumber>-1?damanzhu*2:damanzhu;
        }
        int betChip1 = lastBet+lastBet-lastAndLastBet-betChip+qianzhu;
        if (roomStatus>3){
            if (betChip1 <= 0) betChip1 = damanzhu;
        }
        return betChip1;
    }
    public Map<Integer, Integer> getOneInsurer() {
        return oneInsurer;
    }

    public void setOneInsurer(Map<Integer, Integer> oneInsurer) {
        this.oneInsurer = oneInsurer;
    }

    public Map<Integer, Integer> getTwoInsurer() {
        return twoInsurer;
    }

    public void setTwoInsurer(Map<Integer, Integer> twoInsurer) {
        this.twoInsurer = twoInsurer;
    }

    public Map<Integer, Integer> getPlFee() {
        return plFee;
    }

    public void setPlFee(Map<Integer, Integer> plFee) {
        this.plFee = plFee;
    }

    public int getPumpingMode() {
        return pumpingMode;
    }

    public void setPumpingMode(int pumpingMode) {
        this.pumpingMode = pumpingMode;
    }

    public boolean isCanSeeHandCard() {
        return canSeeHandCard;
    }

    public void setCanSeeHandCard(boolean canSeeHandCard) {
        this.canSeeHandCard = canSeeHandCard;
    }

    public boolean getInsuranceRebate() {
        return insuranceRebate;
    }

    public void setInsuranceRebate(boolean insuranceRebate) {
        this.insuranceRebate = insuranceRebate;
    }

    public BigDecimal getJKpump() {
        return JKpump;
    }

    public void setJKpump(BigDecimal JKpump) {
        this.JKpump = JKpump;
    }

    public Integer getMaxbet() {
        return maxbet;
    }

    public void setMaxbet(Integer maxbet) {
        this.maxbet = maxbet;
    }

    public Double getPump() {
        return pump;
    }

    public void setPump(Double pump) {
        this.pump = pump;
    }
    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public int getFirstOpSize() {
        return firstOpSize;
    }

    public void setFirstOpSize(int firstOpSize) {
        this.firstOpSize = firstOpSize;
    }

    private ActivityConfig activityConfig;                          //  活动相关配置

    public ActivityConfig getActivityConfig() {
        return activityConfig;
    }

    private IJackpotService jackpotService=null;
    public IJackpotService getJackpotService(){
        if(this.jackPot) {
            if (null == jackpotService) {
                synchronized (this) {
                    if (null == jackpotService) {
                        this.jackpotService = JackpotFactory.getInstance().getJapckpotService(this);
                    }
                }
            }
        }

        return this.jackpotService;
    }

    private IRedWalletService reaWalletService = null;
    public IRedWalletService getReaWalletService(){
        if(this.activityConfig.isSkyRedWallet()){
            if(null == reaWalletService){
                synchronized (this){
                    if(null == reaWalletService){
                        this.reaWalletService = RedWalletFactory.getInstance().getRedWalletService(this);
                    }
                }
            }
        }

        return this.reaWalletService;
    }

    public int getThisRoundPoolChip() {
        return thisRoundPoolChip;
    }

    public void setThisRoundPoolChip(int thisRoundPoolChip) {
        this.thisRoundPoolChip = thisRoundPoolChip;
    }

    private RoomSeatChangeService roomSeatChangeService;

    public RoomSeatChangeService getRoomSeatChangeService() {
        return roomSeatChangeService;
    }

    private EventTrackService eventTrackService;

    public EventTrackService getEventTrackService() {
        return eventTrackService;
    }

    private boolean jackPotPlayBigCard = false;                     //  jackpot牌局是否播放打牌动画
    private int vp = 0;                                        //  是否统计入池率

    private int firstFlopBet = 0; //翻牌后第一次加注

    private int currentRoomActionTimes = 0;                         //当前房间操作次数统计

    // 上局回顾的纪录
    private ReplayCache replayCache = new ReplayCache();

    // 房间所有进入过的玩家map
    private Map<Integer, RoomPlayer> roomPlayers = new HashMap<Integer, RoomPlayer>();
    private RoomService roomService;

    //房间座位
    private RoomPersion[] roomPersions;

    //玩家坐下等待下一盘开始列表
    private RoomPersion[] ddRoomPersions;
    // 纪录游戏中站起玩家
    private Map<Integer, StandUpUserInfo> standUpUserInfo;

    // 房间上局数据
    private RoomRecord roomRecord = new RoomRecord();
    // 房间每手数据
    private RoomReplay roomReplay = new RoomReplay();
    // 房间第几手
    private int stage = 0;
    private boolean isStageFin = false;

    // 保险人
    private Insurer insurer = new Insurer();

    // 荷官
    private Dealer dealer = new Dealer(this);

    // 翻牌前加注次数
    private int addedTimeBeforeFanpai;

    // 翻牌后加注次数
    private int addedTimeAfterFanpai;

    //翻牌前是否有激进行为 跟注/加注/全下
    private boolean isAfBeforeFanpai;

    // 当前牌桌是否处于偷盲状态
    private int stolenBind;

    // 大牌数，大于等于顺子即为大牌
    private int bigCardNum = 0;

    // 标记房间是否结束，防止房间结束时用户重连进房间
    private boolean finished = false;

    // 房间流程类
    public RoomProcedure roomProcedure;

    // 一手玩家下注编号
    private long betIndex = 0;

    // 保险触发次数（第几次触发保险）
    private long insureIndex = 0;

    // 每手straddle玩家
    private Map<Integer, Integer> straddlePlayers = new HashMap<Integer, Integer>();
    private boolean isStraddleFin = false;

    // 玩家可以主动翻牌的次数(消耗钻石查看下一圈牌)
    private int userCanTurnTimes = 0;   // 1:可以发河牌、2：可以发转牌和河牌、3：可以发翻牌、转牌和河牌
    // 5秒的花钻石看牌时间是否已超时
    private boolean pendingTimeOut = false;
    // 是否正在执行玩家的花钻石看牌操作
    private boolean inRequest410 = false;
    // 当前有玩家正在翻看赢家手牌
    private boolean inRequest419 = false;
    // 底牌类型 (0正常发牌，1通过玩家花钻石发牌)
    private int[] pocerType = {0, 0, 0, 0, 0};
    // 花钻石看底牌的玩家id
    private int showCardsUserId = -1;
    // 花钻石看底牌的玩家名字
    private String showCardsUserName = "";
    // 离桌留座玩家信息
    private HashMap<Integer, RoomPlayer> occupySeatPlayers = new HashMap<Integer, RoomPlayer>();
    private Task gameOverTask;

    private Map<Integer,Integer> userDevicesMap = new ConcurrentHashMap<>();//val  1安卓 2ios  用户设备类型记录


    //上次比牌时间  每局开始重置为-1  比牌完设置为比牌完成的时间戳
    private long lastComparePocerTime = -1;

    // 跟注到BB/straddle的人数;
    private int callToBBorStraddleCount = 0;
    //翻牌前是否有人加注过
    private boolean jiazhuPreFlop = false;
    // call/raise/bet次数 每次发牌都需要重置
    private int callOrRaiseCount = 0;
    //allin 次数
    private int allinCount = 0;
    //all最大筹码 只计算allin时下注的积分
    private int maxAllinChouma = 0;
    //所有人的初始积分总和
    private int totalBeginHandChouma = 0;
    //当手开始时座位上的玩家数
    private int beginHandPlayerCount = 0;
    //翻牌后每轮第一个行动的人座位号
    private int firstOpFlopSize = -1;
    //auto ai 下一手坐下userid 同个牌局里在一手中只会存在一个
    private int autoAiNextHandSeatUserId = 0;
    //派遣开关关闭后 是否执行过auto ai站起的任务
    private boolean stopDispatchAutoti = false;
    /*//是否有auto ai 符合站起策略，被选中需要指定手被站起
    @Deprecated
    private boolean autoAireadyToStandUp = false;*/
    //将要离开的AI ID
    private Integer readyToStandUpAi=null;
    //每轮第一个有激进行为的玩家是否为正常玩家 每次发牌重置 -1 无激进玩家  0 at 1正常玩家
    private int isfirstAfNormal = -1;
    //本轮激进玩家之后是否有at已经操作过  每次发牌重置
    private boolean isAtOperated = false;
    //每轮第一个有激进行为的玩家下注筹码 每次发牌重置
    private int firstAfBetChip = 0;
    //用户费用
    private Map<Integer,Integer> userFeeChip=new HashMap<>();
    //俱乐部费用
    private Map<Integer,Integer> clubFee=new HashMap<>();
    //联盟费用
    private Map<Integer,Integer> tribeFee=new HashMap<>();
    //平台费用
    private int systemFee;
    //彩金服务费
    private int jpFee;
    //俱乐部保险费用
    @Getter
    private final Map<Integer,Integer> clubInsurance=new HashMap<>();
    //联盟保险费用
    @Getter
    private final Map<Integer,Integer> tribeInsurance=new HashMap<>();
    //系统保险分给系统的
    private int insureSystemFee;
    private boolean isZhuaTou=false;
    private boolean zhuaTouRoomPersion;
    private List<Integer> requestToBrind = new ArrayList<>();
    //房间内总收取服务费记录
    private int roomChargeTotal = 0;

    public int getRoomChargeTotal() {
        return roomChargeTotal;
    }
    public void addRoomChargeTotal(int roomChargeTotal) {
        this.roomChargeTotal = this.roomChargeTotal+roomChargeTotal;
    }
    public List<Integer> getRequestToBrind() {
        return requestToBrind;
    }

    public boolean isZhuaTouRoomPersion() {
        return zhuaTouRoomPersion;
    }

    public void setZhuaTouRoomPersion(boolean zhuaTouRoomPersion) {
        this.zhuaTouRoomPersion = zhuaTouRoomPersion;
    }

    public boolean isZhuaTou() {
        return isZhuaTou;
    }

    public void setZhuaTou(boolean zhuaTou) {
        isZhuaTou = zhuaTou;
    }

    public int getInsureSystemFee() {
        return insureSystemFee;
    }

    public void setInsureSystemFee(int insureSystemFee) {
        this.insureSystemFee = insureSystemFee;
    }

    //控制公共牌
    private List<Integer> controlPublicCards = new ArrayList<>();
    public List<Integer> getControlPublicCards() {
        return controlPublicCards;
    }
    public Map<Integer, Integer> getClubFee() {
        return clubFee;
    }
    public void addClubFee(int clubId, int fee) {
        int oldFee = clubFee.getOrDefault(clubId, 0);
        clubFee.put(clubId, oldFee + fee);
    }
    public int getJpFee() {
        return jpFee;
    }
    public void setJpFee(int jpFee) {
        this.jpFee=jpFee;
    }
    public int getSystemFee() {
        return systemFee;
    }
    public void setSystemFee(int systemFee) {
         this.systemFee=systemFee;
    }

    public Map<Integer, Integer> getTribeFee() {
        return tribeFee;
    }
    public void addTribeFee(int tribeId, int fee) {
        int oldFee = tribeFee.getOrDefault(tribeId, 0);
        tribeFee.put(tribeId, oldFee + fee);
    }

    public Map<Integer, Integer> getUserFeeChip() {
        return userFeeChip;
    }

    public void setUserFeeChip(Map<Integer, Integer> userFeeChip) {
        this.userFeeChip = userFeeChip;
    }

    public int getFirstAfBetChip() {
        return firstAfBetChip;
    }

    public void setFirstAfBetChip(int firstAfBetChip) {
        this.firstAfBetChip = firstAfBetChip;
    }

    public int getIsfirstAfNormal() {
        return isfirstAfNormal;
    }

    public void setIsfirstAfNormal(int isfirstAfNormal) {
        this.isfirstAfNormal = isfirstAfNormal;
    }

    public boolean isAtOperated() {
        return isAtOperated;
    }

    public void setAtOperated(boolean atOperated) {
        isAtOperated = atOperated;
    }

    private boolean cardControl = false;  //是否开启控制发牌

    private UserWeightRecord userWeightRecord = new UserWeightRecord(); //干预玩家记录

    public UserWeightRecord getUserWeightRecord() {
        return userWeightRecord;
    }

    public void setUserWeightRecord(UserWeightRecord userWeightRecord) {
        this.userWeightRecord = userWeightRecord;
    }

    /** 牌局服务费相关 */
    private IFeeService feeService;
    public IFeeService getFeeService(){
        if(null == this.feeService) {
            synchronized (this){
                if(null == this.feeService) {
                    this.feeService = CommissionFactory.getInstance().getFeeService(this);
                }
            }
        }
        return this.feeService;
    }

    private FeeManagerAdapter feeManager;

    public FeeManagerAdapter getFeeManager() {
        return feeManager;
    }

    public boolean getFinished() {
        return finished;
    }

    public List<String> cardControlPublicCardsList = new ArrayList<>();  //控制发牌中已经发过的公共牌牌型
    public List<String> cardControlHandCardsList = new ArrayList<>();    //控制发牌中已经发过的手牌牌型

    public void setNewGameOverTask() {
        if(this.gameOverTask != null){
            this.gameOverTask.setValid(false);
        }
        this.gameOverTask = new Task(TaskConstant.TASK_ROOM_TIMESUP, null, roomId, roomPath);
        WorkThreadService.submitDelayTask(roomId, gameOverTask,
                maxPlayTime * 60000 - (System.currentTimeMillis() - gameBeginTime));
        logger.info(maxPlayTime * 60000 - (System.currentTimeMillis() - gameBeginTime));

        roomProcedure.delayTaskMap.put(gameOverTask.getId(),gameOverTask);

    }

    public void setFinished(boolean finished) {
        this.finished = finished;
    }

    public int getBigCardNum() {
        return bigCardNum;
    }

    public void setBigCardNum(int bigCardNum) {
        this.bigCardNum = bigCardNum;
    }

    public int getStage() {
        return stage;
    }

    public void setStage(int stage) {
        this.stage = stage;
    }

    private Pocer[] pocer = new Pocer[5];
    private Pocer[] aiPocer = null;

    public String getRoomUUID() {
        return roomUUID;
    }

    public void setRoomUUID(String roomUUID) {
        this.roomUUID = roomUUID;
    }

    public Room(int playerCount) {
        roomPersions = new RoomPersion[playerCount];
        ddRoomPersions = new RoomPersion[playerCount];
        standUpUserInfo = new HashMap<>(playerCount);
    }


    public void addActionCount() {
        actionCount++;
    }

    public void createActionCount() {
        actionCount = 0;
    }

    public int getActionCount() {
        return actionCount;
    }
    public int getGenZhuCount() {
        return genZhuCount;
    }
    public int getQiPaiCount() {
        return qiPaiCount;
    }
    public void addJiazhuCount() {
        jiaZhuCount++;
    }

    public void createJiazhuCount() {
        jiaZhuCount = 0;
    }
    public void createGenZhuCount() {
        genZhuCount = 0;
    }
    public void createQiPaiCount() {
        qiPaiCount = 0;
    }

    public int getJiaZhuCount() {
        return jiaZhuCount;
    }
    private AtRoomConfig atRoomConfig = new AtRoomConfig();

    public AtRoomConfig getAtRoomConfig() {
        return atRoomConfig;
    }

    public void setAtRoomConfig(AtRoomConfig atRoomConfig) {
        this.atRoomConfig = atRoomConfig;
    }

    public void anteClean() {
        for (RoomPersion r : roomPersions) {
            if (r != null) {
                r.setAnteNumber(0);
            }
        }
        minRaiseNumber = manzhu;
        raisePerson = -1;
    }

    public void nextGame() {
        clearBuMangList();
        setRoomStatus(1);
        for (RoomPersion r : audMap.values()) {
            if (r != null) {
                r.setBetStatus(0);
                r.setBetChouma(0);
                r.setAnteCount(0);
                r.setStatus(0);
                r.setYq(0);
                r.setYs(-1);
                r.setLastSize(-1);
                r.setAiInsurerBuyTimes(0);
                r.setHandBeginChip(0);
            }
        }
        // 此处中间修改了房间状态，如果是暂停状态需特殊处理
        if (isPause) {
            lastRoomStatus = 1;    // 记录房间当前状态
            setRoomStatus(8);
        }

        // 重置盖牌状态
        setMuck(false);
        setJackPotPlayBigCard(false);
        setBetFirstId(-1);
        setPreAttackId(-1);
        setCurrentMaxChouma(0);
        setTotalBeginHandChouma(0);
        setMaxAllinChouma(0);
        setJiazhuPreFlop(false);
        setBeginHandPlayerCount(0);
        setFirstOpFlopSize(-1);
        setAllinCount(0);
        setCallToBBorStraddleCount(0);
        setCallOrRaiseCount(0);
        setFirstOpSize(-1);

        setIsfirstAfNormal(-1);
        setAtOperated(false);
        setFirstAfBetChip(0);
        controlPublicCards.clear();
        this.getUserWeightRecord().getWeightInterveneRecordMap().clear();
        this.getPocerLink().getIntervenenPocerSet().clear();
        zhuaTouRoomPersion=false;
        setStraddleNumber(-999);
        oneInsurer.clear();
        twoInsurer.clear();
    }

    /**
     * 重置 //一盘结束时
     */
    public void reset() {
        for (int i = 0; i < pocer.length; i++) {
            pocer[i] = null;
        }
        for (RoomPersion rp : audMap.values()) {
            if (rp != null) {
                rp.setShowCardsId(0);
                rp.setFundLast(0);
                rp.setMuck(false);
                rp.setTanpai(false);
                rp.setAiWin(false); // 每手开始时将ai比牌结果置为默认值false
                rp.setCanSeeHandCard(false);//设置为不可查看手牌
                if (rp.getNextGameStandup()==1){
                    rp.setNextGameStandup(0);
                    roomService.zhanqi(rp, StandUpRoomCode.USER_SELF_STANDUP);
                }
                if (rp.getNextGameStandup()==2){
                    rp.setNextGameStandup(0);
                    roomService.likai(rp, LeaveRoomCode.USER_SELF_LEAVE);
                    Object[][] objs = {  // 通知本人离开成功
                            {60, 0, I366ClientPickUtil.TYPE_INT_1},
                            {61, 3, I366ClientPickUtil.TYPE_INT_1},
                            {130, rp.getNowcounma(), I366ClientPickUtil.TYPE_INT_4},
                            {131, rp.getChouma(), I366ClientPickUtil.TYPE_INT_4}
                    };
                    byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_SEND_SEAT_ACTION);
                    PublisherUtil.publisher(rp.getUserInfo(), bytes);
                }
            }
        }
        standUpUserInfo.clear();

        //重置比牌时间
        setLastComparePocerTime(-1);

        //重置当前手有多少个玩家参与打牌
        this.setCurrentPlayerCount(-1);

        //重置本手jackpot数据
        if(null != this.jackpotService){
            this.jackpotService.resetPerHand();
        }

        //重置临时战绩产生的服务费计算
        if(null != this.feeService)
            this.feeService.resetPerHand();

        maxChouma = damanzhu;
        poolChip = 0;
        delayTime = 0;
        // 此局生效的带入倍数修改请求
        if (requestMinRate > 0) {
            minRate = requestMinRate;
            requestMinRate = 0;
        }
        if (requestMaxRate > 0) {
            maxRate = requestMaxRate;
            requestMaxRate = 0;
        }
        bigCardNum = 0;

        //  保险人
        winWithoutInsuranceUserIds.clear();
        if (insurance == 1) {
            insurer = new Insurer();
            insuranceLast = 0;
            insuranceActive = false;
            dealer.setMaxPay(0);
            dealer.resetExceeOuts();
        }
        aiPocer = null;
        // 盖牌攻击者重置
        setMuckAttackId(-1);

        //重置翻牌后第一次加注
        setFirstFlopBet(0);
        firstHandChip=0;
        lastBet=0;
        lastAndLastBet=0;
        initJaZhuChoma();
        isZhuaTou=false;
    }

    /**
     * 初始化化房间对象
     */
    public void initRoom() {
        logger.debug("[R-{}] init room", roomId);
        audMap = new ConcurrentHashMap<Integer, RoomPersion>();
        if (roomUUID == null) {
            setRoomUUID(ObjectId.get().toString());
        }
        RoomDao dao = new RoomDaoImpl();
        Object[] obj = dao.getRoomInfo(roomId);
        if (obj != null) {
            int progress = (Integer) obj[0];
            control = false;
            if (progress >= 3) {
                gameBeginTime = (Long) obj[2];
            }
            roomStatus = progress == 4 ? 1 : 0;
            setRoomStatus(roomStatus);
            name = (String) obj[4];
        } else {
            roomStatus = 0;
        }
        if(this.isJackPot()){
            IJackpotService jpService = this.getJackpotService();
            JackPotUtil.addJackPotRoom(jpService.getJackpotId(),this);
        }

        vigilante = VigilanceProviderManager.getProvider(Cache.p).getVigilante(this, highRiskPlayerRegistry);
        pocerLink = PocerLinkFactory.newInstance(this, PocerLinkInterceptorList.of((PocerLink.PocerLinkInterceptor)vigilante, new AiInsurer.PocerLinkInterceptor()));
        roomService = RoomService.initRoomService(this);
        roomSeatChangeService = new RoomSeatChangeService(this);
        roomProcedure = new RoomProcedure(this);
        activityConfig = new ActivityConfig();
        eventTrackService = new EventTrackServiceImpl(this);
        feeManager = FeeManagerAdapter.newInstance(this);

        //加载房间派遣配置
        /*AtGamePlanDaoImpl gamePlanDao = new AtGamePlanDaoImpl();
        AtGamePlan gamePlan = gamePlanDao.getAtGamePlan(name,tribeId);
        if (gamePlan != null) {
            this.atGamePlan = gamePlan;
        }*/
    }

    public int getRoomId() {
        return roomId;
    }

    public int getRoomPath() {
        return roomPath;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public PocerLink getPocerLink() {
        return pocerLink;
    }
    /**
     * 获取进入房间人员列表
     *
     * @return
     */
    public Map<Integer, RoomPersion> getAudMap() {
        return audMap;
    }

    /**
     * 用户离开房间，把用户从房间人员列表删除
     */
    public  void delRoomAud(int userId, RemoveUserCode removeUserCode) {
        logger.debug("[R-{}][U-{}]删除玩家信息类型={}",this.getRoomId(),userId,removeUserCode.toString());
        synchronized(audMap) {
            if (AiRuleTemplate.isAiUser(userId)) { //如果是auto ai需要调用移除ai接口,同时牌局中ai数量-1
                if (EAiMode.auto == AiRoomManager.getModeOfPlayer(getRoomId(), userId)) {
                    // 2024/11/25 按最新的需求，AI离开了就不允许它再进来了
                    AiRoomManager.releaseDispatchedAi(getRoomId(), userId, false); //释放at
                    //如果还在房间内证明它未真证离开，所以在这里进行减操作
                    if (audMap.containsKey(userId)) {
                        this.getAtRoomConfig().setAutoAiCount(getAtRoomConfig().getAutoAiCount() - 1);
                        logger.debug("[R-{}][U-{}][AI] 用户离开房间,AI人数减1", userId, this.getRoomId());
                    }
                }

                if (EAiMode.manual == AiRoomManager.getModeOfPlayer(getRoomId(), userId)) {//释放mt
                    AiRoomManager.removeDispatchedMt(userId);
                }
            }
            audMap.remove(userId);
            Cache.deleteOnlineUserInfo(userId, roomId);
        }
    }

    /**
     * 0房主开始游戏前 //新增
     * 1初始等待发牌状态
     * 2倒计时(发每人两张牌)
     * 3第一轮投注(发三张公共牌)
     * 4第二轮投注(发第四张公共牌)
     * 5第三轮投注(发第五张公共牌)
     * 6第四轮投注
     * 7投注完成 分配奖金
     * 8暂停
     * @return
     */
    public int getRoomStatus() {
        return roomStatus;
    }

    /**
     * 当前操作玩家
     *
     * @return
     */
    public int getCurrentNumber() {
        return currentNumber;
    }

    public int getCurrentUserStatus() {
        return currentUserStatus;
    }

    public int getManzhuNumber() {
        return manzhuNumber;
    }

    public int getDamanzhuNumber() {
        return damanzhuNumber;
    }

    public int getZhuangjiaNumber() {
        return zhuangjiaNumber;
    }

    public RoomPersion[] getRoomPersions() {
        return roomPersions;
    }

    /**
     * 获取五张底牌
     *
     * @return
     */
    public Pocer[] getPocer() {
        return pocer;
    }

    public void setPocer(Pocer[] pocer) {
        this.pocer = pocer;
    }

    /**
     * 当前牌桌最大筹码
     *
     * @return
     */
    public int getMaxChouma() {
        return maxChouma;
    }

    public void setMaxChouma(int maxChouma) {
        this.maxChouma = maxChouma;
    }

    public int getManzhu() {
        return manzhu;
    }


    public void setManzhu(int manzhu) {
        this.manzhu = manzhu;
    }


    public int getDamanzhu() {
        return damanzhu;
    }


    public void setDamanzhu(int damanzhu) {
        this.damanzhu = damanzhu;
    }


    public int getWait() {
        return wait;
    }


    public void setWait(int wait) {
        this.wait = wait;
    }

    /**
     * 需要携带的筹码量
     *
     * @return
     */
    public int getChouma() {
        return chouma;
    }

    /**
     * 需要携带的筹码量
     *
     * @param chouma
     */
    public void setChouma(int chouma) {
        this.chouma = chouma;
    }

    public RoomPersion[] getDdRoomPersions() {
        return ddRoomPersions;
    }


    public int getQianzhu() {
        return qianzhu;
    }

    public void setQianzhu(int qianzhu) {
        this.qianzhu = qianzhu;
    }

    public int getMinChip() {
        return minChip;
    }

    public void setMinChip(int minChip) {
        this.minChip = minChip;
    }

    public void setRoomPath(int roomPath) {
        this.roomPath = roomPath;
    }
    //每次发牌初始化加注的筹码
    public void initJaZhuChoma(){
        lastBet=0;
        lastAndLastBet=0;
    }


    /**
     * 充血模型
     * 设置房间状态 非常重要 不能乱设置
     * 1初始等待发牌状态   2倒计时(发每人两张牌) 3第一轮投注(发三张公共牌) 4第二轮投注(发第四张公共牌) 5第三轮投注(发第五张公共牌) 6第四轮投注 7投注完成 分配奖金
     *
     * @param roomStatus
     */
    public void setRoomStatus(int roomStatus) {
        logger.debug("[R-{}] +++++房间状态由({})变更为( {})++++",roomId,this.roomStatus, roomStatus);
        createActionCount();
        createJiazhuCount();
        createGenZhuCount();
        createQiPaiCount();
        firstHandChip=0;
        if (roomStatus>3){
            thisRoundPoolChip=0;
            buMangPlay.clear();
        }
        this.roomStatus = roomStatus;
        dealer.recordGameData();
        initJaZhuChoma();
        if (roomStatus == 1) {
            Task task = new Task(TaskConstant.TASK_ROOMSTATUS1, null, roomId, roomPath);
            WorkThreadService.submit(roomId, task);
        } else if (roomStatus == 3) {
            logger.debug("【R-{}】翻牌前的底池:{}",roomId,poolChip);
            //保持翻牌前的底池
            basePoolChip = stageInitPoolChip = poolChip;
            if (roomReplay.getAnteAction() == null) {
                roomReplay.setAnteAction(new AnteAction());
            }
        } else if (roomStatus == 4) {
            //保持翻牌后的底池
            stageInitPoolChip = poolChip;
            roomReplay.setProgress(1);
            if (roomReplay.getAnteAction() == null) {
                roomReplay.setAnteAction(new AnteAction());
                roomReplay.getAnteAction().setPot(poolChip);
            }
        } else if (roomStatus == 5) {
            //保持转牌前的底池
            stageInitPoolChip = poolChip;
            roomReplay.setProgress(2);
            if (roomReplay.getAnteAction() == null) {
                roomReplay.setAnteAction(new AnteAction());
                roomReplay.getAnteAction().setPot(poolChip);
            }
        } else if (roomStatus == 6) {
            //保持河牌时的底池
            stageInitPoolChip = poolChip;
            roomReplay.setProgress(3);
            if (roomReplay.getAnteAction() == null) {
                roomReplay.setAnteAction(new AnteAction());
                roomReplay.getAnteAction().setPot(poolChip);
            }
        } else if (roomStatus == 7) {
            Task task = new Task(TaskConstant.TASK_ROOMSTATUS7, null, roomId, roomPath);
            WorkThreadService.submit(roomId, task);
        } else if (roomStatus == 9) {
            incrInsureIndex();
            /** bug fix 如果存在多个人购买保险 需要分别启动保险超时延时任务**/
            Map<Integer, Holder> holderMap = this.getInsurer().getHolderMap();
            if(null != holderMap && !holderMap.isEmpty()){
                for(int userId:holderMap.keySet()){
                    Map<Integer, Object> map = new HashMap<Integer, Object>();
                    map.put(1, insureIndex);
                    map.put(2, betIndex);
                    map.put(3, userId);
                    Task task = new Task(TaskConstant.TASK_INSURANCE_TIMEOUT, map, roomId, roomPath, userId);  // 20241118 add user id
                    logger.debug("new task 10011 for userId: {} id: {}", userId, task.getId());
                    WorkThreadService.submitDelayTask(roomId, task, Constant.INSURANCE_DELAY_OPERATE_TIME);
                    roomProcedure.delayTaskMap.put(task.getId(), task);
                }
            }
        }
    }

    public void setRoomId(int roomId) {
        this.roomId = roomId;
    }

    public RoomService getRoomService() {
        return roomService;
    }

    public static Random getR() {
        return r;
    }

    public void setZhuangjiaNumber(int zhuangjiaNumber) {
        this.zhuangjiaNumber = zhuangjiaNumber;
    }

    public void setGuanshaNumber(int guanshaNumber) {
        this.guanshaNumber = guanshaNumber;
    }

    public int getGuanshaNumber() {
        return guanshaNumber;
    }

    public void setCurrentNumber(int currentNumber) {
        this.currentNumber = currentNumber;
    }

    public long getT3() {
        return t3;
    }

    public void setT3(long t3) {
        this.t3 = t3;
    }

    public void setT2(long t2) {
        this.t2 = t2;
    }

    public long getT2() {
        return this.t2;
    }

    public void setGameBeginTime(long gameBeginTime) {
        this.gameBeginTime = gameBeginTime;
    }

    public long getGameBeginTime() {
        return this.gameBeginTime;
    }

    public int getOwner() {
        return owner;
    }

    public void setOwner(int owner) {
        this.owner = owner;
    }

    public int getMaxPlayTime() {
        return maxPlayTime;
    }

    public void setMaxPlayTime(int maxPlayTime) {
        this.maxPlayTime = maxPlayTime;
    }

    public int getPlayerCount(){
        return playerCount;
    }

    public void setPlayerCount(int playerCount){
        this.playerCount = playerCount;
    }

    public boolean getControl() {
        return control;
    }

    public void setControl(Boolean control) {
        this.control = control;
    }

    public int getPoolChip() {
        return poolChip;
    }

    /**
     * 翻牌前的最初池底筹码数
     * @return 筹码数
     */
    public int getBasePoolChip(){
        int chip = manzhu + damanzhu + qianzhu * audMap.size();
        for (Integer value : straddlePlayers.values()) {
            chip += value;
        }
        //TODO 还不能确定最初始池底筹码数（basePoolChip）=盲注+大盲注+前注*人数+每手straddle，暂时先用chip代替
        logger.debug("【R-{}】basePoolChip={},chip={}",roomId,basePoolChip,chip);
        return chip;
    }

    /**
     * 翻牌、转牌、河牌的初始池底筹码数（每轮的初始底池）
     * @return 筹码数
     */
    public int getStageInitPoolChip(){
        if(roomStatus<=3){
            return getBasePoolChip();
        }else{
            logger.debug("【R-{}】stageInitPoolChip={}",roomId,stageInitPoolChip);
            return stageInitPoolChip;
        }
    }

    public void setPoolChip(int poolChip) {
        logger.debug("【R-{}】poolChip = {} -> {}", roomId, this.poolChip, poolChip);
        this.poolChip = poolChip;
    }

    public Map<Integer, StandUpUserInfo> getStandUpUserInfo() {
        return standUpUserInfo;
    }

    public void setStandUpUserInfo(int userId, StandUpUserInfo userInfo) {
        standUpUserInfo.put(userId, userInfo);
    }

    public void setMinRate(int minRate) {
        this.minRate = minRate;
    }

    public int getMinRate() {
        return minRate;
    }

    public void setMaxRate(int maxRate) {
        this.maxRate = maxRate;
    }

    public int getMaxRate() {
        return maxRate;
    }

    public void setIsPause(boolean isPause) {
        this.isPause = isPause;
    }

    public boolean getIsPause() {
        return isPause;
    }

    public void setPauseBeginTime(long pauseBeginTime) {
        this.pauseBeginTime = pauseBeginTime;
    }

    public long getPauseBeginTime() {
        return pauseBeginTime;
    }

    public void setLastRoomStatus(int lastRoomStatus) {
        this.lastRoomStatus = lastRoomStatus;
    }

    public int getLastRoomStatus() {
        return lastRoomStatus;
    }

    public void setPauseTime(long pauseTime) {
        this.pauseTime = pauseTime;
    }

    public long getPauseTime() {
        return pauseTime;
    }

    public void setDelayTime(int delayTime) {
        this.delayTime = delayTime;
    }

    public int getDelayTime() {
        return delayTime;
    }

    public void setRequestMinRate(int requestMinRate) {
        this.requestMinRate = requestMinRate;
    }

    public int getRequestMinRate() {
        return requestMinRate;
    }

    public void setRequestMaxRate(int requestMaxRate) {
        this.requestMaxRate = requestMaxRate;
    }

    public int getRequestMaxRate() {
        return requestMaxRate;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public RoomRecord getRoomRecord() {
        return roomRecord;
    }

    public RoomReplay getRoomReplay() {
        return roomReplay;
    }

    public void setRoomReplay(RoomReplay roomReplay) {
        this.roomReplay = roomReplay;
    }

    public void setAddedTimeBeforeFanpai(int addedTimeBeforeFanpai) {
        this.addedTimeBeforeFanpai = addedTimeBeforeFanpai;
    }

    public int getAddedTimeBeforeFanpai() {
        return addedTimeBeforeFanpai;
    }

    public void setAddedTimeAfterFanpai(int addedTimeAfterFanpai) {
        this.addedTimeAfterFanpai = addedTimeAfterFanpai;
    }

    public int getAddedTimeAfterFanpai() {
        return addedTimeAfterFanpai;
    }

    public void setStolenBind(int stolenBind) {
        this.stolenBind = stolenBind;
    }

    public int getStolenBind() {
        return stolenBind;
    }

    public long getPauseUserLeftOpTime() {
        return pauseUserLeftOpTime;
    }

    public void setPauseUserLeftOpTime(long pauseUserLeftOpTime) {
        this.pauseUserLeftOpTime = pauseUserLeftOpTime;
    }

    public int getMinRaiseNumber() {
        return getMin2JaZhu(0,0);
    }

    public void setMinRaiseNumber(int minRaiseNumber) {
        this.minRaiseNumber = minRaiseNumber;
    }

    public int getRaisePerson() {
        return raisePerson;
    }

    public void setRaisePerson(int raisePerson) {
        this.raisePerson = raisePerson;
    }

    public int getInsurance() {
        return insurance;
    }

    public void setInsurance(int insurance) {
        this.insurance = insurance;
    }

    public Insurer getInsurer() {
        return insurer;
    }

    public Dealer getDealer() {
        return dealer;
    }

    public void setDealer(Dealer dealer) {
        this.dealer = dealer;
    }

    public void setInsuranceBeginTime(long insuranceBeginTime) {
        this.insuranceBeginTime = insuranceBeginTime;
    }

    public long getInsuranceBeginTime() {
        return insuranceBeginTime;
    }

    public void setInsuranceActive(boolean insuranceActive) {
        this.insuranceActive = insuranceActive;
    }

    public boolean getInsuranceActive() {
        return insuranceActive;
    }

    public void setInsuranceChip(int insuranceChip) {
        this.insuranceChip = insuranceChip;
    }

    public int getInsuranceChip() {
        return insuranceChip;
    }

    public void setInsuranceLast(int insuranceLast) {
        this.insuranceLast = insuranceLast;
    }

    public int getInsuranceLast() {
        return insuranceLast;
    }

    public boolean isLimitIp() {
        return limitIp;
    }

    public void setLimitIp(boolean limitIp) {
        this.limitIp = limitIp;
    }

    public void setWinWithoutInsuranceUserIds(int userId) {
        winWithoutInsuranceUserIds.add(userId);
    }

    public Set<Integer> getWinWithoutInsuranceUserIds() {
        return winWithoutInsuranceUserIds;
    }

    public int getPlayType() {
        return playType;
    }

    public long getBetIndex() {
        return betIndex;
    }

    public long incrBetIndex() {
        betIndex += 1;
        return betIndex;
    }

    public long getInsureIndex() {
        return insureIndex;
    }


    public long incrInsureIndex() {
        insureIndex += 1;
        return insureIndex;
    }

    public boolean getStraddle() {
        return straddle;
    }

    public void setStraddle(boolean straddle) {
        this.straddle = straddle;
    }

    public boolean isClub() {
        return isClub;
    }

    public void setClub(boolean isClub) {
        this.isClub = isClub;
    }

    public int getClubId() {
        return clubId;
    }

    public void setClubId(int clubId) {
        this.clubId = clubId;
    }

    public boolean isStageFin() {
        return isStageFin;
    }

    public void setStageFin(boolean isStageFin) {
        this.isStageFin = isStageFin;
    }

    public Map<Integer, Integer> getStraddlePlayers() {
        return straddlePlayers;
    }

    public boolean isStraddleFin() {
        return isStraddleFin;
    }

    public void setStraddleFin(boolean isStraddleFin) {
        this.isStraddleFin = isStraddleFin;
    }

    public int getLastBetPlayer() {
        return lastBetPlayer;
    }

    public void setLastBetPlayer(int lastBetPlayer) {
        this.lastBetPlayer = lastBetPlayer;
    }

    public boolean isLastBetPlayerOp() {
        return isLastBetPlayerOp;
    }

    public void setLastBetPlayerOp(boolean isLastBetPlayerOp) {
        this.isLastBetPlayerOp = isLastBetPlayerOp;
    }

    public long getMinPlayTime() {
        return minPlayTime;
    }

    public void setMinPlayTime(long minPlayTime) {
        this.minPlayTime = minPlayTime;
    }

    public Map<Integer, RoomPlayer> getRoomPlayers() {
        return roomPlayers;
    }

    public ReplayCache getReplayCache() {
        return replayCache;
    }

    public void setMuck(boolean muck) {
        this.muck = muck;
    }

    public boolean getMuck() {
        return muck;
    }

    public void setMuckAttackId(int muckAttackId) {
        this.muckAttackId = muckAttackId;
    }

    public int getMuckAttackId() {
        return muckAttackId;
    }

    public int getUserCanTurnTimes() {
        return userCanTurnTimes;
    }

    public void setUserCanTurnTimes(int userCanTurnTimes) {
        this.userCanTurnTimes = userCanTurnTimes;
    }

    public boolean isPendingTimeOut() {
        return pendingTimeOut;
    }

    public void setPendingTimeOut(boolean pendingTimeOut) {
        this.pendingTimeOut = pendingTimeOut;
    }

    public boolean isInRequest410() {
        return inRequest410;
    }

    public void setInRequest410(boolean inRequest410) {
        this.inRequest410 = inRequest410;
    }

    public int[] getPocerType() {
        return pocerType;
    }

    /**
     * @param index
     * @param type
     */
    public void setPocerType(int index, int type) {
        this.pocerType[index] = type;
    }

    public void resetPocerType() {
        for (int i = 0; i < this.pocerType.length; i++) {
            this.pocerType[i] = 0;
        }
    }

    public HashMap<Integer, RoomPlayer> getOccupySeatPlayers() {
        return occupySeatPlayers;
    }

    public int getShowCardsUserId() {
        return showCardsUserId;
    }

    public void setShowCardsUserId(int showCardsUserId) {
        this.showCardsUserId = showCardsUserId;
    }

    public String getShowCardsUserName() {
        return showCardsUserName;
    }

    public void setShowCardsUserName(String showCardsUserName) {
        this.showCardsUserName = showCardsUserName;
    }

    public void setAttackId(int attackId) {
        this.attackId = attackId;
    }

    public int getAttackId() {
        return attackId;
    }

    public void setPreAttackId(int preAttackId) {
        this.preAttackId = preAttackId;
    }

    public int getPreAttackId() {
        return preAttackId;
    }

    public void setBetFirstId(int betFirstId) {
        this.betFirstId = betFirstId;
    }

    public int getBetFirstId() {
        return betFirstId;
    }

    public void setCurrentMaxChouma(int currentMaxChouma) {
        this.currentMaxChouma = currentMaxChouma;
    }

    public int getCurrentMaxChouma() {
        return currentMaxChouma;
    }

    public boolean isLimitGPS() {
        return limitGPS;
    }

    public void setLimitGPS(boolean limitGPS) {
        this.limitGPS = limitGPS;
    }

    public int getTribeId() {
        return tribeId;
    }

    public void setTribeId(int tribeId) {
        this.tribeId = tribeId;
    }

    public String getTribeName() {
        return tribeName;
    }

    public void setTribeName(String tribeName) {
        this.tribeName = tribeName;
    }

    public int getMuckSwitch() {
        return muckSwitch;
    }

    public void setMuckSwitch(int muckSwitch) {
        this.muckSwitch = muckSwitch;
    }

    public Set<Integer> getLeaveAud() {
        return Collections.unmodifiableSet(leaveAud);
    }

    public void removeLeaveUser(int userId) {
        leaveAud.remove(userId);
    }
    public void addLeaveUser(int userId){
        leaveAud.add(userId);
    }

    public boolean isJackPot() {
        return jackPot;
    }

    public void setJackPot(boolean jackPot) {
        this.jackPot = jackPot;
    }

    public int getSpectatorVoiceSwitch() {
        return spectatorVoiceSwitch;
    }

    public void setSpectatorVoiceSwitch(int spectatorVoiceSwitch) {
        this.spectatorVoiceSwitch = spectatorVoiceSwitch;
    }

    public boolean isJackPotPlayBigCard() {
        return jackPotPlayBigCard;
    }

    public void setJackPotPlayBigCard(boolean jackPotPlayBigCard) {
        this.jackPotPlayBigCard = jackPotPlayBigCard;
    }

	public int getAheadLeaveMode() {
        return aheadLeaveMode;
    }

    public void setAheadLeaveMode(int aheadLeaveMode) {
        this.aheadLeaveMode = aheadLeaveMode;
    }

    public int getVp() {
        return vp;
    }

    public void setVp(int vp) {
        this.vp = vp;
    }

    public int getFirstFlopBet() {
        return firstFlopBet;
    }

    public void setFirstFlopBet(int firstFlopBet) {
        this.firstFlopBet = firstFlopBet;
    }

    public int getCurrentRoomActionTimes() {
        return currentRoomActionTimes;
    }

    public void updateCurrentRoomActionTimes() {
        this.currentRoomActionTimes += 1;
    }

    public Map<Integer, Integer> getUserDevicesMap() {
        return userDevicesMap;
    }

    public void setUserDevicesMap(Map<Integer, Integer> userDevicesMap) {
        this.userDevicesMap = userDevicesMap;
    }

    public boolean isAfBeforeFanpai() {
        return isAfBeforeFanpai;
    }

    public void setAfBeforeFanpai(boolean afBeforeFanpai) {
        isAfBeforeFanpai = afBeforeFanpai;
    }

    public int getCallToBBorStraddleCount() {
        return callToBBorStraddleCount;
    }

    public void setCallToBBorStraddleCount(int callToBBorStraddleCount) {
        this.callToBBorStraddleCount = callToBBorStraddleCount;
    }

    public boolean isJiazhuPreFlop() {
        return jiazhuPreFlop;
    }

    public void setJiazhuPreFlop(boolean jiazhuPreFlop) {
        this.jiazhuPreFlop = jiazhuPreFlop;
    }

    public int getAllinCount() {
        return allinCount;
    }

    public void setAllinCount(int allinCount) {
        this.allinCount = allinCount;
    }

    public int getCallOrRaiseCount() {
        return callOrRaiseCount;
    }

    public void setCallOrRaiseCount(int callOrRaiseCount) {
        this.callOrRaiseCount = callOrRaiseCount;
    }

    public int getMaxAllinChouma() {
        return maxAllinChouma;
    }

    public void setMaxAllinChouma(int maxAllinChouma) {
        this.maxAllinChouma = maxAllinChouma;
    }

    public int getTotalBeginHandChouma() {
        return totalBeginHandChouma;
    }

    public void setTotalBeginHandChouma(int totalBeginHandChouma) {
        this.totalBeginHandChouma = totalBeginHandChouma;
    }

    public int getBeginHandPlayerCount() {
        return beginHandPlayerCount;
    }

    public void setBeginHandPlayerCount(int beginHandPlayerCount) {
        this.beginHandPlayerCount = beginHandPlayerCount;
    }

    public int getFirstOpFlopSize() {
        return firstOpFlopSize;
    }

    public void setFirstOpFlopSize(int firstOpFlopSize) {
        this.firstOpFlopSize = firstOpFlopSize;
    }

    public Pocer[] getAiPocer() {
        return aiPocer;
    }

    public void setAiPocer(Pocer[] aiPocer) {
        this.aiPocer = aiPocer;
    }

    public int getAutoAiNextHandSeatUserId() {
        return autoAiNextHandSeatUserId;
    }

    public void setAutoAiNextHandSeatUserId(int autoAiNextHandSeatUserId) {
        this.autoAiNextHandSeatUserId = autoAiNextHandSeatUserId;
    }

    public boolean isStopDispatchAutoti() {
        return stopDispatchAutoti;
    }

    public void setStopDispatchAutoti(boolean stopDispatchAutoti) {
        this.stopDispatchAutoti = stopDispatchAutoti;
    }

    public boolean isAutoAireadyToStandUp() {
        //return autoAireadyToStandUp;
        return this.readyToStandUpAi!=null;
    }
    /*@Deprecated
    public void setAutoAireadyToStandUp(boolean autoAireadyToStandUp) {
        this.autoAireadyToStandUp = autoAireadyToStandUp;
    }*/
    public void setReadyToStandUpAi(Integer aiUserId){
        this.readyToStandUpAi=aiUserId;
    }

    public Integer getReadyToStandUpAi() {
        return readyToStandUpAi;
    }

    public List<String> getCardControlPublicCardsList() {
        return cardControlPublicCardsList;
    }

    public List<String> getCardControlHandCardsList() {
        return cardControlHandCardsList;
    }

    public boolean isCardControl() {
        return cardControl;
    }

    public void setCardControl(boolean cardControl) {
        this.cardControl = cardControl;
    }

    public int getOpTime() {
        return opTime;
    }

    public void setOpTime(int opTime) {
        this.opTime = opTime;
    }

    public long getLastComparePocerTime() {
        return lastComparePocerTime;
    }

    public void setLastComparePocerTime(long lastComparePocerTime) {
        this.lastComparePocerTime = lastComparePocerTime;
    }

    public int getCurrentPlayerCount() {
        return currentPlayerCount;
    }

    public void setCurrentPlayerCount(int currentPlayerCount) {
        this.currentPlayerCount = currentPlayerCount;
    }

    public boolean isAutoStartRoom() {
        return autoStartRoom;
    }

    public void setAutoStartRoom(boolean autoStartRoom) {
        this.autoStartRoom = autoStartRoom;
    }

    public int getAutoStartPlayerCount() {
        return autoStartPlayerCount;
    }

    public void setAutoStartPlayerCount(int autoStartPlayerCount) {
        this.autoStartPlayerCount = autoStartPlayerCount;
    }
    private Map<Integer, Integer[]> roleUsers = new HashMap<Integer, Integer[]>() {             //  保险人的赔率表
        {
            put(2, new Integer[]{1,9});
            put(3, new Integer[]{1,2,9});
            put(4, new Integer[]{1,2,3,9});
            put(5, new Integer[]{1,2,3,8,9});
            put(6, new Integer[]{1,2,3,7,8,9});
            put(7, new Integer[]{1,2,3,5,7,8,9});
            put(8, new Integer[]{1,2,3,4,6,7,8,9});
            put(9, new Integer[]{1,2,3,4,5,6,7,8,9});
        }
    };
    public  Integer[] getRoleUsers(int counts){
        return roleUsers.get(counts);
    }

    public Integer getTribeRoomType() {
        return tribeRoomType;
    }

    public void setTribeRoomType(Integer tribeRoomType) {
        this.tribeRoomType = tribeRoomType;
    }

    public boolean isClubRoom() {
        return clubRoomType == 1;
    }

    public boolean isTribeRoom() {
        return tribeRoomType == 1;
    }

    public int getMinVpip() {
        return minVpip;
    }

    public void setMinVpip(int minVpip) {
        this.minVpip = minVpip;
    }

    public int getTierId() {
        return this.tierId;
    }

    public void setTierId(int tierId) {
        this.tierId = tierId;
    }
}
