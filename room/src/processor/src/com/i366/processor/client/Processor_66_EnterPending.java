
package com.i366.processor.client;

import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;
import com.i366.constant.Constant;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.server.pack.I366ServerPickUtil;
import com.dzpk.common.utils.LogUtil;
import com.work.comm.io.Handler;
import com.work.comm.client.protocal.BaseRequest;
import com.work.comm.client.protocal.Request;
import org.apache.logging.log4j.Logger;

import java.util.Map;

/**
 * @deprecated
 *
 * 茉莉牌局中验证码局,进入pedding页
 * 疯狂扑克中废弃,暂时保留
 */
public class Processor_66_EnterPending extends Handler {

    private Logger logger = LogUtil.getLogger(Processor_66_EnterPending.class);

    public byte[] handleRequest(BaseRequest req) {
        Request request = (Request) req;
        logger.debug("EnterPending userId is : " + request.getUserId());
        int[][] int2 = {
                {60, I366ClientPickUtil.TYPE_INT_1},        //    房间类别
                {130, I366ClientPickUtil.TYPE_INT_4}        //    房间编号
        };
        Map<Integer, Object> map = I366ClientPickUtil.pickAll(request.getBt(), int2);
        int roomPath = (Integer) map.get(60);
        int roomId = (Integer) map.get(130);
        logger.info("roompath: " + roomPath + " ,roomId:" + roomId);
        
        Task task = new Task(Constant.REQ_GAME_ENTER_PENDING, map, request, roomId, roomPath);
        WorkThreadService.submit(roomId, task);
        return null;
    }

}

