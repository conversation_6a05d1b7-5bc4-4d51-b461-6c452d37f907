
package com.i366.processor.client;

import java.util.Map;

import com.dzpk.common.utils.LogUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import org.apache.logging.log4j.Logger;

import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;
import com.i366.constant.Constant;
import com.i366.room.RoomService;
import com.work.comm.io.Handler;
import com.work.comm.client.protocal.BaseRequest;
import com.work.comm.client.protocal.Request;

/**
 *  房主强制结束房间,如果在游戏中,会在打完当手后在解散
 */
public class Processor_159_ForceStop extends Handler {

    private final Logger logger = LogUtil.getLogger(Processor_159_ForceStop.class);

    @Override
    public byte[] handleRequest(BaseRequest req) {
        Request request = (Request) req;
        int userId = request.getUserId();
        int[][] int2 = {
                {130, I366ClientPickUtil.TYPE_INT_4},        // roomId
                {131, I366ClientPickUtil.TYPE_INT_4},        // roomPath
        };
        Map<Integer, Object> map = I366ClientPickUtil.pickAll(request.getBt(), int2);
        int roomId = (Integer) map.get(130);
        int roomPath = (Integer) map.get(131);
        logger.debug("Processor_159_ForceStop roomId " + roomId + " userId=" + userId);

        RoomService.setUserChannel(request, roomId);

        Task task = new Task(Constant.REQ_REQUEST_FORCE_STOP, map, request, roomId, roomPath);
        WorkThreadService.submit(roomId, task);
        return null;
    }

}
