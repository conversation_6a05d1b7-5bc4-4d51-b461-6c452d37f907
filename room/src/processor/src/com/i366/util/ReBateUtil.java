package com.i366.util;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.mq.rabbitmq.RabbitMQService;
import com.dzpk.component.mq.rabbitmq.constant.MqConstant;
import com.dzpk.component.mq.rabbitmq.model.record.GameRecordInfo;
import com.dzpk.component.mq.rabbitmq.model.record.PlayerPl;
import com.google.gson.Gson;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * 返佣计算金豆
 */
public class ReBateUtil {

    private static Logger logger = LogUtil.getLogger(ReBateUtil.class);

    public static void sendGameRecordInfo(int roomId, int roomPath, String roomName, boolean roomClubType,List<PlayerPl> playerPlList){

        logger.debug("sendGameRecordInfo, roomid={},roomPath={},roomName={}",roomId,roomPath,roomName);
        RabbitMQService rabbitMQService = RabbitMQService.getInstance();
        GameRecordInfo gameRecordInfo = new GameRecordInfo();
        gameRecordInfo.setRoomId(String.valueOf(roomId));
        gameRecordInfo.setRoomName(roomName);
        gameRecordInfo.setClubRoomType(String.valueOf(roomClubType ? 0 : 1));
        gameRecordInfo.setRoomPath(String.valueOf(roomPath));
        gameRecordInfo.setPlayerPlList(playerPlList);
        gameRecordInfo.setFinishTime(System.currentTimeMillis());
        Gson gson = new Gson();
        String message = gson.toJson(gameRecordInfo);

        rabbitMQService.sendMessage(MqConstant.EXCHANGE_TYPE_TOPIC,
                MqConstant.REBATE_CACULATECHIP_QUENE_NAME,MqConstant.REBATE_CACULATECHIP_EXCHANGE,MqConstant.REBATE_CACULATECHIP_ROUTEKEY,message);
    }
}
