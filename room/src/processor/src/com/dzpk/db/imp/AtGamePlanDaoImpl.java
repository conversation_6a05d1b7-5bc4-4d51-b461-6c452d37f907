package com.dzpk.db.imp;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.repositories.mysql.dbpool.DBUtil;
import com.dzpk.db.dao.AtGamePlanDao;
import com.dzpk.db.model.AtGamePlan;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.Logger;

import java.lang.reflect.Type;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class AtGamePlanDaoImpl implements AtGamePlanDao {
    /**
     * 日志服务
     */
    private static Logger logger = LogUtil.getLogger(AtGamePlanDaoImpl.class);

    public AtGamePlan getAtGamePlan(String roomName, int tribeId) {
        Connection dbConnection = null;
        PreparedStatement ps = null;

        ResultSet rs = null;
        String selectStatement = "select * from at_game_plan where room_name=? and tribe_id=?";
        List<AtGamePlan> plans = new ArrayList<>();
        try {
            dbConnection = DBUtil.getConnection();
            ps = dbConnection.prepareStatement(selectStatement);
            ps.setString(1, roomName);
            ps.setInt(2, tribeId);
            rs = ps.executeQuery();

            while (rs.next()) {
                AtGamePlan item = new AtGamePlan();
                item.setId(rs.getInt("id"));
                item.setRoomName(rs.getString("room_name"));
                item.setTribeNo(rs.getString("tribe_no"));
                String standingConfig = rs.getString("standing_config");
                List<Integer> standingConfigIds = null;
                if (StringUtils.isNotBlank(standingConfig)) {
                    standingConfigIds = new Gson().fromJson(standingConfig, TypeToken.getParameterized(List.class, new Type[]{Integer.class}).getType());
                } else {
                    standingConfigIds = new ArrayList<>();
                }
                item.setStandingConfig(standingConfigIds);
                item.setMaxAtUser(rs.getInt("max_at_user"));
                item.setDispatchType(rs.getString("dispatch_type"));//"single , loop"
                item.setSingleStartTime(rs.getTimestamp("single_start_time"));
                item.setSingleEndTime(rs.getTimestamp("single_end_time"));
                item.setRoomDuration(rs.getFloat("room_duration"));
                item.setLoopStartTime(rs.getTime("loop_start_time"));
                item.setLoopEndTime(rs.getTime("loop_end_time"));
                item.setAdvancedDispatchEnable(rs.getBoolean("advanced_dispatch_enable"));
                item.setAdvancedDispatchConfig(rs.getString("advanced_dispatch_config"));
                item.setAdvancedIdleEnable(rs.getBoolean("advanced_idle_enable"));
                item.setAdvancedIdleConfig(rs.getString("advanced_idle_config"));

                plans.add(item);
            }

        } catch (Exception se) {
            logger.error("[RN-{}][TribeId-{}] getAtGamePlan error", roomName, tribeId, se);
        } finally {
            DBUtil.closeResultSet(rs);
            DBUtil.closeStatement(ps);
            DBUtil.closeConnection(dbConnection);
        }
        logger.debug("[RN-{}][TribeId-{}][房间指定派遣配置] 找到数据记录{}", roomName, tribeId, plans);

        //PP50 获取满足时间段条件的房间配置
        Date now = new Date();
        plans = plans.stream().filter(item -> {
            if (item.getDispatchType().equalsIgnoreCase("single")) {
                long timestamp = now.getTime();
                return (item.getSingleStartTime().getTime() <= timestamp && item.getSingleEndTime().getTime() >= timestamp);
            } else {
                SimpleDateFormat format = new SimpleDateFormat("HHmmss");
                int nowTime = Integer.valueOf(format.format(now));
                int startTime = Integer.valueOf(format.format(item.getLoopStartTime()));
                int endTime = Integer.valueOf(format.format(item.getLoopEndTime()));
                if (endTime == 0) {
                    endTime = 235959;
                }
                return (startTime <= nowTime && nowTime <= endTime);
            }
        }).collect(Collectors.toList());
        logger.debug("[RN-{}][TribeId-{}][房间指定派遣配置] 匹配到符合条件的房间指定派遣配置：{}", roomName, tribeId, plans);

        if (plans.isEmpty()) {
            return null;
        }
        return plans.get(0);
    }

}
