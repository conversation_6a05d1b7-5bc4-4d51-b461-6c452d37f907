package com.dzpk.db.model;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;

import java.util.List;

@Builder
@Setter
@Getter
public class SingleCardScore implements Comparable<SingleCardScore>{
    private  ObjectId id;
    //因为关联属性加入roomid和version
    private Integer roomId;
    //角色SB BB UTG UTG+1  UTG+2   MP HJ CO BTN
    private Integer role;
    //0弃牌）fold
    //1（皇家同花顺）royal flush
    //2（同花顺）straight flush
    //3（四条）four of a kind
    //4（葫芦）full house
    //5（同花）flush
    //6（顺子）straight
    //7（三条）three of a kind
    //8（两对）two  Pairs
    //9（一对）one  Pair
    //10（高牌）high card
    private Integer brandType;
    //用户id
    private Integer userId;
    //用户名
    private String userName;
    //牌谱
    private List<Integer> card;
    //最后比牌高亮的标志位
    private List<Integer> liangPai;
    //盈利值
    private Integer profit;
    //保险输赢
    private Integer insurer;
    //类型是否显示0显示/1不显示
    private Integer cardeType;
    //手牌是否展示0不展示|1展示
    private Integer showCard;
    //第几局
    private Integer version;

    @Override
    public int compareTo(SingleCardScore o) {
        return role.compareTo(o.role);
    }
}
