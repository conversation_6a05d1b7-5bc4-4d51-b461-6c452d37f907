package com.dzpk.db.dao;

import java.sql.SQLException;

import com.i366.model.player.RoomPersion;

public interface PersionChipBindDao {
	
	/**
	 * 初始化绑定筹码
	 * 
	 * @param userId
	 * @param chip
	 * @param roomId
	 * @throws SQLException
	 */
	void initBind(int userId , int chip , int roomId) throws SQLException;
	
    /**
     * 更新绑定筹码
     * @param roomPersions
     * @throws SQLException
     */
    void updatBindAll(RoomPersion[] roomPersions) throws SQLException;
    
}
