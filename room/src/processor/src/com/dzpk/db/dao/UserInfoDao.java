package com.dzpk.db.dao;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.dzpk.db.model.UserAccount;
import com.dzpk.db.model.UserAccountLog;
import com.dzpk.db.model.UserInfo;

public interface UserInfoDao {

    /**
	 * 查询用户基本信息
	 * @param userId
	 * @return
	 * @throws SQLException
	 */
	UserInfo getUserInfo(int userId)  throws SQLException;
    /**
     * 查询用户基本信息
     * @param userId
     * @return
     * @throws SQLException
     */
    Integer getUserPrivilege(int userId)  throws SQLException;
	
	/**
	 * 更新用户信息
	 * @param model
	 * @throws SQLException
	 */
	void updateUserInfo(UserInfo model) throws SQLException;

    /**
     * 更新用户牌谱收藏数量
     *
     * @param userId
     * @param cnt
     * @throws SQLException
     */
    void updateSpectrumCnt(int userId, int cnt) throws SQLException;


    /**
     * 批量获取玩家的昵称和头像
     * @param userIds
     * @return
     * @throws SQLException
     */
    Map<Integer, Object[]> getUserHeadNickname(String userIds) throws SQLException;

    /**
     * 获取玩家的最大牌谱收藏数
     * @param userId 用户id
     * @return 最大收藏牌谱数
     */
    int getUserMaxCollectNum(int userId);
    /**
     * 获取俱乐部主id
     *  @param clubId 俱乐部Id
     *  @return 俱乐部主Id
     */
    int getClubByUserId(int clubId);
    /**
     * 获取俱乐部主id
     *  @param clubId 俱乐部Id
     *  @return 俱乐部主Id
     */
    int getClubByClubId(int clubId);

    /**
     * 添加带入的消息
     * @param clubId：俱乐部id
     * @param senderId：消息生成方userId
     * @param reciverId: 处理用户userId（俱乐部主）
     * @param userName：请求者的用户名
     * @param roomName：请求的房间名
     * @param chip：请求的筹码
     * @param roomId：房间id加入remark
     * @param roomPath：房间的类型加入remark
     */
    void saveRequestToBringInMessage(int clubId, int senderId, int reciverId,
                                     String userName, String roomName, int chip,
                                     int roomId, int roomPath, int bringIn) throws SQLException;
    /**
     * 获取别人对我的备注
     * @param userId
     * @param friendIds
     * @return
     */
    Map<String, String> getMyRemarks(int userId, String friendIds);

    /**
     * 查询玩家是否冻结
     * @param userId
     * @return
     */
    boolean checkUserIfFozen(int userId) throws SQLException;

    /**
     * 查询玩家钱包是否冻结
     * @param userId
     * @return
     */
    boolean checkUserAccountIfFozen(int userId) throws SQLException;

    /**
     * 返回未被冻结的用户ID
     *
     * @param userIdLst  待检查的用户ID,必填
     * @return
     * @throws SQLException
     */
    List<Integer> checkUserIfNotFozen(List<Integer> userIdLst);

    /**
     * 插入玩家账户变动日志记录
     * @param userAccountLog
     */
    void insertUserAccountLog(Connection conn,UserAccountLog userAccountLog) throws SQLException;

    /**
     * 更新玩家账户(非结算,不用考虑战绩流水)
     * @param conn
     * @param userId
     * @param chip 可提变化值 可正负
     * @param notExtractChip  不可提变化值 可正负
     * @return
     */
    void updateUserAccount(Connection conn,int userId, int chip,int notExtractChip) throws SQLException;

    /**
     * 更新玩家账户,结算(牌局结束,提前离桌)时, 需要计算战绩流水返佣
     * @param conn
     * @param userId
     * @param chip 可提变化值 可正负
     * @param notExtractChip 不可提变化值 可正负
     * @param plAcount  战绩流水返佣变化值 可正负
     * @throws SQLException
     */
    void updateUserAccountWithCommission(Connection conn,int userId, int chip,int notExtractChip,int plAcount) throws SQLException;


    /**
     * 获取玩家账户信息,更新玩家金豆数量时使用
     * @param userId
     * @return
     */
    UserAccount getUserAccountInfo(Connection conn,int userId) throws SQLException;

    /**
     * 更新玩家账户前先锁定账户信息
     * @param userId
     */
    void updateUserAccountBeforeChange(Connection conn,int userId, long updateTime) throws SQLException;

    /**
     * 查询俱乐部积分
     * @param clubId 俱乐部id
     * @param userId 用户id
     * @return
     */
    int queryUserIntegral(Connection conn,int clubId,int userId);

    int queryUserTribeChip(Connection conn, int clubId, int userId);

    int queryUserGold(Connection conn, int userId);

    void updateUserGold(Connection conn, int userId, int gold) throws SQLException;

    void updateUserTribeChip(Connection conn, int userId, int clubId, int tribeChip) throws SQLException;
}
