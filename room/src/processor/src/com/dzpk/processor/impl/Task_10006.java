package com.dzpk.processor.impl;

import com.dzpk.common.utils.LogUtil;
import org.apache.logging.log4j.Logger;

import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.cache.Cache;
import com.i366.model.room.Room;

/**
 * 上一手收尾和下手开始
 * <AUTHOR>
 *
 */
public class Task_10006 implements IProcessor {

    private Logger logger = LogUtil.getLogger(Task_10006.class);
    
    @Override
    public void handle(Task task) {
        if (!task.isValid()) {
            logger.debug("invalid message");
            return;
        }
        
        Room room = Cache.getRoom(task.getRoomId(), task.getRoomPath());
        if (room != null) {
            int lastTurn = (Integer) task.getMap().get(1);
            int turnIndex = (Integer) task.getMap().get(2);
            int bipaiIndex = (Integer) task.getMap().get(3);
            logger.debug("lastTurn: " + lastTurn + ", turnIndex: " + turnIndex);
            room.roomProcedure.bipai(lastTurn, turnIndex, bipaiIndex);
            
            room.roomProcedure.delayTaskMap.remove(task.getId());
        }
    }

}
