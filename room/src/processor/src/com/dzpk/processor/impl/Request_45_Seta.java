package com.dzpk.processor.impl;


import com.dzpk.atviewer.IViewerManager;
import com.dzpk.common.utils.GsonHelper;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.repositories.mysql.dbpool.DBUtil;
import com.dzpk.component.repositories.redis.RedisService;
import com.dzpk.db.dao.GameDataProfitDao;
import com.dzpk.db.imp.GameDataProfitDaoImpl;
import com.dzpk.db.model.GameDataProfit;
import com.dzpk.db.model.UserInfo;
import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.cache.Cache;
import com.i366.constant.Constant;
import com.i366.constant.LeaveRoomCode;
import com.i366.constant.StandUpRoomCode;
import com.i366.model.player.RoomPersion;
import com.i366.model.player.RoomPlayer;
import com.i366.model.room.Room;
import com.i366.room.RoomAutoOp;
import com.i366.room.RoomIpGps;
import com.i366.util.*;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.Request;
import org.apache.logging.log4j.Logger;

import java.sql.*;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;


public class Request_45_Seta implements IProcessor {

    private static final Logger logger = LogUtil.getLogger(Request_45_Seta.class);

    private static final Map<Integer, IActionHandler> handlers = new HashMap<>();

    static {
        handlers.put(1, new SeatHandler()); //坐下房间
        handlers.put(2, new StandupHandler()); //站起房间
        handlers.put(3, new LeaveHandler()); //离开房间
        handlers.put(4, new ForceStandupHandler()); //强制站起
        handlers.put(5, new ForceKickHandler()); //强制踢出
        handlers.put(6, new AheadLeaveHandler()); //提前离桌
        handlers.put(7, new FreezeHandler()); //冻结玩家
        handlers.put(8, new KickPlayerHandler()); //踢出玩家（观众+上台玩家）
    }

    public void handle(Task task) {
        Request request = (Request) task.getRequest();

        int action = (Integer) task.getMap().get(60);
        int seatId = (Integer) task.getMap().get(61);

        if (seatId == 255) {
            seatId = -1;
        }

        String longitudeStr = task.getMap().get(62) == null ? "" : (String) task.getMap().get(62);
        String latitudeStr  = task.getMap().get(63) == null ? "" : (String) task.getMap().get(63);
        int userId = request.getUserId();
        int roomId = task.getRoomId();
        int roomPath = task.getRoomPath();

        UserInfo userInfo = Cache.getOnlineUserInfo(userId, roomId);
        
        Room room = Cache.getRoom(roomId, roomPath);

        if (room == null) {
            Object[][] objs2 = {
                    {60, 1, I366ClientPickUtil.TYPE_INT_1}
            };

            byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
            PublisherUtil.publisher(request, bytes2);
            return;
        }

        if (userInfo == null) {
            Object[][] objs2 = {
                    {60, 1, I366ClientPickUtil.TYPE_INT_1}
            };

            byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
            PublisherUtil.publisher(request, bytes2);
            return;
        }

        RoomPlayer roomPlayer = room.getRoomPlayers().get(userId);
        if (roomPlayer == null) {
            Object[][] objs2 = {
                    {60, 1, I366ClientPickUtil.TYPE_INT_1}
            };

            byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
            PublisherUtil.publisher(request, bytes2);
            return;
        }

        String clientIp = IPUtil.getRealUserIp(request);
        RoomIpGps.updateUserIpGpsInfo(longitudeStr,latitudeStr,clientIp,userInfo); //更新玩家ip、gps信息

        IActionHandler handler = handlers.get(action);
        if (handler != null) {
            handler.handle(task, request, room, roomPlayer, userInfo, action, seatId);
        }
    }

    /**
     * 如果手數少於300, 則不受最低入池率限制
     *
     * @param userId
     * @param room
     * @return true 觸發入池率限制 false 不受限制
     */
    private static boolean checkMinVpip(int userId, Room room) {
        int handCnt = 0;
        int userVpip = 0;
        GameDataProfitDao profitDao = new GameDataProfitDaoImpl();
        GameDataProfit profit = profitDao.selectGameDataProfit(userId);
        if (null != profit) {
            handCnt = Optional.ofNullable(profit.getTotalHand()).orElse(0);
            userVpip = Optional.ofNullable(profit.getPoolRate()).orElse(0);
            logger.debug("userId={},handCnt={},userVpip={}", userId, handCnt, userVpip);
        }
        return handCnt >= 300 && userVpip < room.getMinVpip();
    }

    private static byte[] pusUser(int status, RoomPersion roomPersion, int action) {
        return pusUser(status, roomPersion, action, 0);
    }

    private static byte[] pusUser(int status, RoomPersion roomPersion, int action, int minChip) {
        if (roomPersion == null) {
            Object[][] objs = {
                    {60, status, I366ClientPickUtil.TYPE_INT_1},
                    {61, action, I366ClientPickUtil.TYPE_INT_1},
                    {130, minChip, I366ClientPickUtil.TYPE_INT_4},
            };
            return I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_SEND_SEAT_ACTION);
        } else {
            Object[][] objs = {
                    {60, status, I366ClientPickUtil.TYPE_INT_1},
                    {61, action, I366ClientPickUtil.TYPE_INT_1},
                    {130, roomPersion.getNowcounma(), I366ClientPickUtil.TYPE_INT_4},
                    {131, roomPersion.getChouma(), I366ClientPickUtil.TYPE_INT_4}
            };
            return I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_SEND_SEAT_ACTION);
        }
    }


    interface IActionHandler {
        void handle(Task task, Request request, Room room, RoomPlayer roomPlayer, UserInfo userInfo, int action, int seatId);
    }

    static class SeatHandler implements IActionHandler {

        @Override
        public void handle(Task task, Request request, Room room, RoomPlayer roomPlayer, UserInfo userInfo, int action, int seatId) {
            int roomId = room.getRoomId();
            int userId = roomPlayer.getUserId();

            if (room.getMinVpip() > 0 && checkMinVpip(userId, room)) { //开启最低入池率的房间
                Object[][] objs2 = {
                        {60, 8, I366ClientPickUtil.TYPE_INT_1}
                };
                logger.error("Request_45_Seta VPIP limit. room minVpip:{}, userId:{}, roomId:{}.", room.getMinVpip(), userId, room.getRoomId());

                byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
                PublisherUtil.publisher(request, bytes2);
                return;
            }
            logger.info("Request_45_Seta VPIP not limit. room minVpip:{}, userId:{}, roomId:{}.", room.getMinVpip(), userId, room.getRoomId());
            if (room.isLimitGPS() && RoomIpGps.checkSeatGPS(userId, userInfo,room)) {// 开启gps限制
                Object[][] objs2 = {
                        {60, 7, I366ClientPickUtil.TYPE_INT_1}
                };
                logger.error("Request_45_Seta GPS limit. room GPS:{}, userId:{}, roomId:{}.", room.isLimitGPS(), userId, room.getRoomId());

                byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
                PublisherUtil.publisher(request, bytes2);
                return;
            }
            logger.info("Request_45_Seta GPS not limit. room GPS:{}, userId:{}, roomId:{}.", room.isLimitGPS(), userId, room.getRoomId());
            if (room.isLimitIp() && RoomIpGps.checkSeatIp(userId, userInfo.getIp(),room)) {// 开启ip限制
                Object[][] objs2 = {
                        {60, 2, I366ClientPickUtil.TYPE_INT_1}
                };
                logger.error("Request_45_Seta IP limit. room IP:{}, userId:{}, roomId:{}.", room.isLimitIp(), userId, room.getRoomId());

                byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
                PublisherUtil.publisher(request, bytes2);
                return;
            }
            logger.info("Request_45_Seta IP not limit. room IP:{}, userId:{}, roomId:{}.", room.isLimitIp(), userId, room.getRoomId());

            logger.debug("seat down room,roomid={},userid={},seatid={}",roomId,userId,seatId);
            if (seatId < 0 || seatId >= Constant.ROOM_MAX_PLAYER) {    // 座位号不符合要求
                logger.error("seat down room error,seat size is illgeal,roomid={},userid={},seatid={}",roomId,userId,seatId);
                PublisherUtil.publisher(request, pusUser(Constant.SEAT_OCCUPIED, null, action));
                return;
            }

            int bringInClubId = roomPlayer.getBringInClubId();
            if (bringInClubId != 0 && bringInClubId != userInfo.getClubId()) {
                logger.error("不允许以另一俱乐部身份參與, rid={},uid={} 目前俱乐部={} 原來俱乐部={}", room.getRoomId(), userId, userInfo.getClubId(), bringInClubId);
                PublisherUtil.publisher(request, pusUser(Constant.SAME_CLUB_ONLY, null, action));
                return;
            }

            int limitSitStatus = isLimitSit(room, userId, userInfo.getClubId());
            if (limitSitStatus != 0) {
                logger.error("用户被限制上桌,limitSitStatus={} roomId={},userId={},clubId={}", limitSitStatus, room.getRoomId(), userId, userInfo.getClubId());
                PublisherUtil.publisher(request, pusUser(limitSitStatus, null, action));
                return;
            }

            boolean checkTier = CheckRoomTier.builder()
                    .roomId(roomId)
                    .tribeId(room.getTribeId())
                    .roomTierId(room.getTierId())
                    .clubId(userInfo.getClubId())
                    .userId(userId)
                    .roomOwner(room.getOwner())
                    .isPrivilege(userInfo.isPrivilege())
                    .isTribeRoom(room.isTribeRoom())
                    .build()
                    .check();
            if (!checkTier) {
                logger.error("用户房间等级不符合要求, roomId={},userId={},clubId={}", room.getRoomId(), userId, userInfo.getClubId());
                PublisherUtil.publisher(request, pusUser(Constant.USER_TIER_LIMIT, null, action));
                return;
            }

            if (room.getRoomService().isClubLossLimitExceeded(userInfo.getClubId())) {
                logger.error("俱乐部虧损限制, roomId={},userId={},clubId={}", room.getRoomId(), userId, userInfo.getClubId());
                PublisherUtil.publisher(request, pusUser(Constant.CLUB_LOSS_LIMIT_EXCEEDED, null, action));
                return;
            }

            if(1 == roomPlayer.getAheadLeave()){
                if (roomPlayer.getIsBringIn() == 1 && !roomPlayer.isSettlement()) {
                    logger.error("seat down room error,not settled yet,roomid={},userid={},aheadLeave={}",roomId,userId,roomPlayer.getAheadLeave());
                    PublisherUtil.publisher(request, pusUser(Constant.SITTING_DOWN_FAILED, null, action));
                    return;
                }

                logger.debug("ahead leave player returned, clear previous data -- roomid={},userid={},data={}",roomId,userId, GsonHelper.toJson(roomPlayer, false));
                int settledSum = roomPlayer.getSettledSum();
                int feeLocked = roomPlayer.getFeeLocked();
                boolean isSettlement = roomPlayer.isSettlement();
                int isAheadLeave = roomPlayer.getAheadLeave();
                roomPlayer = new RoomPlayer(userId);
                roomPlayer.setBringInClubId(bringInClubId);
                roomPlayer.setSettledSum(settledSum);
                roomPlayer.setFeeLocked(feeLocked);
                roomPlayer.setSettlement(isSettlement);
                roomPlayer.setAheadLeave(isAheadLeave);
                room.getRoomPlayers().put(userId, roomPlayer);

                RoomPersion p = room.getDdRoomPersions()[seatId];
                if(p == null || p.getOnlinerType() == -1){
                    p = room.getAudMap().get(userId);
                }
                if(p.getNowcounma() > 0) {
                    logger.debug("ahead leave player chouma reset -- roomid={},userid={}",roomId,userId);
                    p.setNowcounma(0);
                }
            }

            int seatStatus = room.getRoomService().downRoom(userId, seatId, userInfo); //坐下房间逻辑
            if (seatStatus > -1) {
                logger.error("seat down room error,roomid={},userid={},seatStatus={}",roomId,userId,seatStatus);
                if (seatStatus == Constant.INSUFFICIENT_BALANCE) {
                    PublisherUtil.publisher(request, pusUser(seatStatus, null, action, roomPlayer.getSettledSum()));
                } else {
                    PublisherUtil.publisher(request, pusUser(seatStatus, null, action));
                }
                return;
            }

            // 這裡才清除提前結算標記，否則會重複結算
            if(roomPlayer.isSettlement()) {
                room.getTiqianjiesunSet().remove(userId);
                roomPlayer.setSettlement(false);
            }

            // 這裡才清除提前離桌標記，否則會影響实时战况
            if (1 == roomPlayer.getAheadLeave()) {
                roomPlayer.setAheadLeave(0);
                room.getDealer().setUserStatus(userId, null);
            }

            RoomPersion p = room.getDdRoomPersions()[seatId];
            if(p == null || p.getOnlinerType() == -1){
                p = room.getAudMap().get(userId);
            }

            if(p.getNowcounma() <= (room.getQianzhu() + room.getDamanzhu())){  //筹码不足前注加大盲时设置为占座状态
                room.getRoomService().cancelOccupySeat(userId, 4);
            }else{
                room.getRoomService().cancelOccupySeat(userId, 1);
            }
            long requestToBringInTimes = roomPlayer.getRequestToBringInTimes();
            int time = (int) ((requestToBringInTimes - System.currentTimeMillis()) / 1000);

            if (room.getClubRoomType() != 0 && room.isRequestToBringIn() && requestToBringInTimes != 0 &&
                    0 == room.getRoomService().newOccupySeatTask(userId, time*1000L) &&
                    System.currentTimeMillis() < requestToBringInTimes){
                // 通知别的玩家有人占座了
                Object[][] objs3 = {
                        {60, 0, I366ClientPickUtil.TYPE_INT_1},// 0成功 1 失败 2未到最短时间 3下一手生效
                        {130, userId, I366ClientPickUtil.TYPE_INT_4},   // 玩家id
                        {132, p.getSize(), I366ClientPickUtil.TYPE_INT_4},     // 座位号
                        {133, time , I366ClientPickUtil.TYPE_INT_4}, // 剩余倒计时
                };
                PublisherUtil.send(room,I366ClientPickUtil.packAll(objs3, Constant.REQ_WAITE_SEAT));
            }
            //logger.debug(".....坐下用去时间: " + (System.currentTimeMillis() - l));
        }
    }

    static class StandupHandler implements IActionHandler {

        @Override
        public void handle(Task task, Request request, Room room, RoomPlayer roomPlayer, UserInfo userInfo, int action, int seatId) {
            int roomId = room.getRoomId();
            int userId = roomPlayer.getUserId();

            if (seatId < 0 || seatId >= room.getPlayerCount()) { //校验座位号
                PublisherUtil.publisher(request, pusUser(1, null, action));
                return;
            }

            RoomPersion rp = room.getRoomPersions()[seatId];
            if (rp == null || rp.getOnlinerType() == -1) {
                rp = room.getDdRoomPersions()[seatId];
            }
            if (null == rp) {
                // 判断该玩家是否在留座离桌状态

                /*
                 * 防止客户端下发过来的站起座位号不是该userid的座位号
                 * 比如4号位置的用户发送站起操作时，seatID传过来是5号位置
                 */

                RoomPersion sitRoomPersion = room.getAudMap().get(userId);
                if(sitRoomPersion != null && (sitRoomPersion.getType() == 3 || sitRoomPersion.getType() == 4)){ //考虑正在游戏中获取过庄补盲状态的情况
                    logger.error("the stand up user " + userId + " is not on seat " + seatId + " real seat: " + sitRoomPersion.getSize());
                    PublisherUtil.publisher(request, pusUser(Constant.SEAT_OCCUPIED, rp, action));
                    return;
                }

                logger.debug("the stand up user " + userId + " is occuping seat " + roomPlayer.getSeatSize());
                room.getRoomService().cancelOccupySeat(userId, 0);
                room.getRoomService().notifyOccupyUserStand(Constant.SAME_IP, userId);
                return;

            }

            if (RoomUtil.isOnlyPlayingUser(userId,room)) { // 玩家为唯一再玩玩家，该次站起请求失败
                logger.error("stand up room error,the only player,roomid={},userid={}",roomId,userId);
                PublisherUtil.publisher(request, pusUser(Constant.SEAT_OCCUPIED, rp, action));
                return;
            }

            if(room.getMinPlayTime() > 0){ //开启最短上桌时间的房间,游戏中未到最短打牌时间不让站起
                long userLeftPlayTime = RoomAutoOp.needAutoOp(room,userId);
                if(userLeftPlayTime > 0){
                    logger.error("stand up room error,player not play min playtime,roomid={},userid={},userLeftPlayTime={}",roomId,userId,userLeftPlayTime);
                    PublisherUtil.publisher(request, pusUser(Constant.SEAT_OCCUPIED, rp, action));
                    return;
                }
            }

            if (3 == rp.getType() && -3 != rp.getStatus()) { // 正在游戏中且未有操作行为时,不让站起
                rp.setNextGameStandup(1);
                rp.setStandupType(0); // 主动站起
                logger.error("stand up room error,player is playing game now, don't allow to stand,roomid={},userid={}" +
                        ",roomPersion type={},roomPersion status={}",roomId,userId,rp.getType(),rp.getStatus());
                PublisherUtil.publisher(request, pusUser(Constant.PLAY_NO_STAND_UP, rp, action));
                return;
            }

            rp.setStandupType(0); // 主动站起
            room.getRoomService().zhanqi(rp, StandUpRoomCode.USER_SELF_STANDUP); //调用站起逻辑
        }
    }

    static class LeaveHandler implements IActionHandler {

        @Override
        public void handle(Task task, Request request, Room room, RoomPlayer roomPlayer, UserInfo userInfo, int action, int seatId) {
            int roomId = room.getRoomId();
            int userId = roomPlayer.getUserId();
            RoomPersion persion = null;

            /* 重新确认玩家位置，防止客户端发送错误玩家座位id */
            for (int i = 0; i < room.getRoomPersions().length; i++) {
                if (room.getRoomPersions()[i] != null
                        && room.getRoomPersions()[i].getUserId() == request.getUserId()
                        && room.getRoomPersions()[i].getOnlinerType() != -1) {
                    seatId = i;
                    break;
                } else if (room.getDdRoomPersions()[i] != null
                        && room.getDdRoomPersions()[i].getUserId() == request.getUserId()) {
                    seatId = i;
                    break;
                }
            }

            if (seatId > -1 && seatId < Constant.ROOM_MAX_PLAYER) {
                persion = room.getRoomPersions()[seatId];
                if (persion == null || persion.getOnlinerType() == -1) {
                    persion = room.getDdRoomPersions()[seatId];
                }
            }

            logger.debug("after correct seat,seatid={}",seatId);

            if (persion == null) { //玩家还未坐下座位,点击离开房间
                byte[] bytes = pusUser(0, room.getRoomService().noDownLeave(userId), action);
                PublisherUtil.publisher(request, bytes);
                return;
            }

            if (RoomUtil.isOnlyPlayingUser(userId,room)) { // 玩家为唯一再玩玩家，该次离开请求失败
                logger.error("leave room error,the only player,roomid={},userid={}",roomId,userId);
                PublisherUtil.publisher(request, pusUser(1, persion, action));
                return;
            }

            /*
             * 离开逻辑
             * 开启最短上桌时间的牌局
             *      未到达最短上桌时间时,如果玩家未托管,让玩家设置托管;如果玩家已经托管，直接返回离开房间成功
             *      已经达到最短上桌时间时,直接返回离开房间成功,并且设置玩家下一手离开房间
             * 未开启最短上桌时间的牌局
             *      玩家在游戏中  直接返回离开房间成功,并且设置玩家下一手离开房间
             *      玩家未在游戏中  玩家离开房间
             */
            if(room.getMinPlayTime() > 0){

                long userLeftPlayTime = RoomAutoOp.needAutoOp(room,userId);
                if (userLeftPlayTime > 0) {
                    logger.debug("leave room,player not play min playtime,roomid={},userid={},userLeftPlayTime={}",roomId,userId,userLeftPlayTime);
                }else{
                    roomPlayer.setHasLeft(true);
                    roomPlayer.setSeat(seatId);
                }

                Object[][] objs = {  // 通知本人离开成功
                        {60, 0, I366ClientPickUtil.TYPE_INT_1},
                        {61, 3, I366ClientPickUtil.TYPE_INT_1},
                        {130, persion.getNowcounma(), I366ClientPickUtil.TYPE_INT_4},
                        {131, persion.getChouma(), I366ClientPickUtil.TYPE_INT_4}
                };
                byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_SEND_SEAT_ACTION);
                PublisherUtil.publisher(persion.getUserInfo(), bytes);
                return;
            }else{
                if (3 == persion.getType()) {
                    logger.debug("leave room,player is playing game now,roomid={},userid={},roomPersion type={}",roomId,userId,persion.getType());
                    roomPlayer.setHasLeft(true);
                    roomPlayer.setSeat(seatId);

                    Object[][] objs = {  // 通知本人离开成功
                            {60, 0, I366ClientPickUtil.TYPE_INT_1},
                            {61, 3, I366ClientPickUtil.TYPE_INT_1},
                            {130, persion.getNowcounma(), I366ClientPickUtil.TYPE_INT_4},
                            {131, persion.getChouma(), I366ClientPickUtil.TYPE_INT_4}
                    };
                    byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_SEND_SEAT_ACTION);
                    PublisherUtil.publisher(persion.getUserInfo(), bytes);

                    return;
                }
            }

            room.getRoomService().likai(persion, LeaveRoomCode.USER_SELF_LEAVE); //调用离开逻辑

            //logger.debug("....离开用去时间: " + (System.currentTimeMillis() - l));
        }
    }

    static class ForceStandupHandler implements IActionHandler {

        @Override
        public void handle(Task task, Request request, Room room, RoomPlayer roomPlayer, UserInfo userInfo, int action, int seatId) {
            int roomId = room.getRoomId();
            int userId = roomPlayer.getUserId();

            if (seatId < 0 || seatId >= room.getPlayerCount()) { //校验座位号
                PublisherUtil.publisher(request, pusUser(1, null, action));
                return;
            }

            RoomPersion rpStand = room.getRoomPersions()[seatId];
            if (rpStand == null || rpStand.getOnlinerType() == -1) {
                rpStand = room.getDdRoomPersions()[seatId];
            }

            if(null == rpStand){  // 判断该玩家是否在留座离桌/占座状态
                for (Integer key : room.getRoomPlayers().keySet()) {
                    RoomPlayer player = room.getRoomPlayers().get(key);
                    if(player.getSeat() == seatId){
                        logger.debug("the zhanqi user is occuping seat,userid={},seat={}", player.getUserId(), player.getSeat());
                        room.getRoomService().cancelOccupySeat(player.getUserId(), 0);
                        room.getRoomService().notifyOccupyUserStand(4,player.getUserId());
                        return;
                    }
                }
            }else{
                int permissionZhanqi;
                if (room.getAudMap().containsKey(userId)){
                    if (room.getAudMap().get(userId).getUserInfo().isPrivilege()){
                        permissionZhanqi=1;
                    }else {
                        permissionZhanqi = PermissionUtil.havePermission(userId,rpStand.getUserId(),room);  //查询申请踢出的玩家对被踢玩家是否有权限
                    }
                }else {
                    permissionZhanqi=0;
                }

                int status = permissionZhanqi == 1 ? 10 : 9;  //9没有权限  10有权限

                Object[][] objs = {
                        {60, status, I366ClientPickUtil.TYPE_INT_1},
                        {61, action, I366ClientPickUtil.TYPE_INT_1}
                };
                byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_SEND_SEAT_ACTION);
                if(status == 9){

                    logger.error("zhanqi error,not have permission,userid={},outuserid={},seat={},roomid={}",userId,rpStand.getUserId(),seatId,roomId);
                    PublisherUtil.publisher(request, bytes);
                }else{

                    if(rpStand.isKickOut() || rpStand.isStandUp()){ //如果该玩家已经被站起或者踢出牌局，则直接返回
                        PublisherUtil.publisher(request, bytes);
                        return;
                    }

                    rpStand.setStandupType(1);
                    rpStand.setStandUp(true);
                    roomPlayer.setAheadLeave(1);

                    if(rpStand.getType() != 3){
                        //占座状态马上站起
                        logger.debug("zhanqi user not playing game,zhanqi now,userid={},outuserid={},seat={}" +
                                ",roomid={},roomPersion type={}",userId,rpStand.getUserId(),seatId,roomId,rpStand.getType());
                        room.getRoomService().zhanqi2(rpStand,StandUpRoomCode.FORCE_STANDUP);
                    }

                    logger.debug("zhanqi user successfully,userid={},outuserid={},seat={},roomid={}",userId,rpStand.getUserId(),seatId,roomId);
                    PublisherUtil.publisher(request, bytes);
                }

            }
        }
    }

    static class ForceKickHandler implements IActionHandler {

        @Override
        public void handle(Task task, Request request, Room room, RoomPlayer roomPlayer, UserInfo userInfo, int action, int seatId) {
            int roomId = room.getRoomId();
            int userId = roomPlayer.getUserId();

            if (seatId < 0 || seatId >= room.getPlayerCount()) { //校验座位号
                PublisherUtil.publisher(request, pusUser(1, null, action));
                return;
            }

            RoomPersion rpOut = room.getRoomPersions()[seatId];
            if (rpOut == null || rpOut.getOnlinerType() == -1) {
                rpOut = room.getDdRoomPersions()[seatId];
            }

            if(null == rpOut){  // 判断该玩家是否在留座离桌/占座状态
                for (Integer key : room.getRoomPlayers().keySet()) {
                    RoomPlayer player = room.getRoomPlayers().get(key);
                    if(player.getSeat() == seatId){
                        logger.debug("the kickout user is occuping seat,userid={},seat={}", player.getUserId(), player.getSeat());
                        room.getRoomService().cancelOccupySeat(player.getUserId(), 0);
                        room.getRoomService().notifyOccupyUserStand(5,player.getUserId());

                        RedisService.getRedisService().addKickOutUser(player.getUserId(),roomId);   //将用户加入到该房间被踢的集合中，在进入房间时判断
                        return;
                    }
                }
            }else{
                int permissionKickOut;
                if (room.getAudMap().containsKey(userId)){
                    if (room.getAudMap().get(userId).getUserInfo().isPrivilege()){
                        permissionKickOut=1;
                    }else {
                        permissionKickOut = PermissionUtil.havePermission(userId,rpOut.getUserId(),room);  //查询申请踢出的玩家对被踢玩家是否有权限
                    }
                }else {
                    permissionKickOut=0;
                }
                int status2 = permissionKickOut == 1 ? 10 : 9; //9没有权限  10有权限

                Object[][] objs2 = {
                        {60, status2, I366ClientPickUtil.TYPE_INT_1},
                        {61, action, I366ClientPickUtil.TYPE_INT_1}
                };

                byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
                if(status2 == 9){   //没有权限 直接返回

                    logger.error("kickout error,not have permission,userid={},outuserid={},seat={},roomid={}",userId,rpOut.getUserId(),seatId,room.getRoomId());
                    PublisherUtil.publisher(request, bytes2);
                }else{

                    if(rpOut.isKickOut()){ //如果该玩家已经被踢出牌局，则直接返回
                        PublisherUtil.publisher(request, bytes2);
                        return;
                    }

                    if(rpOut.isStandUp()){ //如果该玩家之前被执行了站起操作，则按照踢出处理
                        rpOut.setStandUp(false);
                    }

                    rpOut.setStandupType(1);
                    rpOut.setKickOut(true);
                    roomPlayer.setAheadLeave(1);

                    if(rpOut.getType() != 3){
                        logger.debug("kickout user not playing game,kickout now,userid={},outuserid={},seat={}" +
                                ",roomid={},roomPersion type={}",userId,rpOut.getUserId(),seatId,roomId,rpOut.getType());
                        room.getRoomService().likai2(rpOut, LeaveRoomCode.KICK_OUT_LEAVE); //调用离开逻辑
                    }

                    RedisService.getRedisService().addKickOutUser(rpOut.getUserId(),roomId);   //将用户加入到该房间被踢的集合中，在进入房间时判断

                    logger.debug("kickout user successfully,userid={},outuserid={},seat={},roomid={}",userId,rpOut.getUserId(),seatId,roomId);
                    PublisherUtil.publisher(request, bytes2);
                }
            }
        }
    }

    static class AheadLeaveHandler implements IActionHandler {

        @Override
        public void handle(Task task, Request request, Room room, RoomPlayer roomPlayer, UserInfo userInfo, int action, int seatId) {
            int roomId = room.getRoomId();
            int userId = roomPlayer.getUserId();

            if(room.getAheadLeaveMode() == 0){//非提前离桌模式不走代码
                logger.error("ahead leave error,room is not aheadLeaveMode,userid={},roomid={}",userId,roomId);
                Object[][] objs2 = {
                        {60, 1, I366ClientPickUtil.TYPE_INT_1},
                        {61, action, I366ClientPickUtil.TYPE_INT_1}
                };

                byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
                PublisherUtil.publisher(request, bytes2);
                return;
            }

            if(1 == roomPlayer.getAheadLeave()){  //如果该玩家已经点击了提前离桌，则直接返回
                logger.error("ahead leave error,user have click aheadLeave button,userid={},roomid={},aheadLeave={}",userId,roomId,roomPlayer.getAheadLeave());
                Object[][] objs2 = {
                        {60, 1, I366ClientPickUtil.TYPE_INT_1},
                        {61, action, I366ClientPickUtil.TYPE_INT_1}
                };

                byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
                PublisherUtil.publisher(request, bytes2);
                return;
            }

            if (0 == roomPlayer.getSeatTimes()) { //玩家沒有坐下過，或因餘額不足無法坐下，離開房間時會觸發
                logger.error("ahead leave error,user has never seated,userid={},roomid={}",userId,roomId);
                Object[][] objs2 = {
                        {60, 1, I366ClientPickUtil.TYPE_INT_1},
                        {61, action, I366ClientPickUtil.TYPE_INT_1}
                };

                byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
                PublisherUtil.publisher(request, bytes2);
                return;
            }

            if(room.getMinPlayTime() > 0){ //游戏中未到最短打牌时间不让提前离桌

                long userLeftPlayTime = RoomAutoOp.needAutoOp(room,userId);
                if (userLeftPlayTime > 0) {
                    logger.debug("ahead leave error,player not play min playtime,roomid={},userid={},userLeftPlayTime={}",roomId,userId,userLeftPlayTime);
                    Object[][] objs2 = {
                            {60, 1, I366ClientPickUtil.TYPE_INT_1},
                            {61, action, I366ClientPickUtil.TYPE_INT_1}
                    };

                    byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
                    PublisherUtil.publisher(request, bytes2);
                    return;
                }
            }

            roomPlayer.setAheadLeave(1);  //设置成提前离桌即可
            roomPlayer.setSelectClubId(0);

            /*
             * 玩家站起状态点击提前离桌
             * 玩家在游戏中点击提前离桌
             * 玩家处于留座离桌状态点击提前离桌
             */
            if( -1 == seatId || roomPlayer.getSeatSize() == -1){
                logger.debug("ahead leave,user is stand up now,userid={},seat={},roomid={}",userId,roomPlayer.getSeatSize(),roomId);
            }else{

                RoomPersion aheadLeavePersion = room.getRoomPersions()[seatId];
                if (aheadLeavePersion == null || aheadLeavePersion.getOnlinerType() == -1) {
                    aheadLeavePersion = room.getDdRoomPersions()[seatId];
                }

                if(null == aheadLeavePersion){   //留座离桌状态
                    logger.debug("ahead leave,user is occuping seat,userid={},seat={},roomid={}",userId,roomPlayer.getSeatSize(),roomId);
                    room.getRoomService().cancelOccupySeat(userId, 0);
                    room.getRoomService().notifyOccupyUserStand(2, userId);
                }else{
                    aheadLeavePersion.setStandupType(0);
                    if(aheadLeavePersion.getType() != 3){  // 不在游戏中的时候直接站起
                        logger.debug("ahead leave,user is not playing game,stand up now,userid={},seat={},roomid={},roomPersion type={}",userId,roomPlayer.getSeatSize(),roomId,aheadLeavePersion.getType());
                        room.getRoomService().zhanqi2(aheadLeavePersion,StandUpRoomCode.AHEAD_LEAVE_STANDUP);
                    }
                }

            }

            /*
             *  1.房间状态为0，牌局还没有开始。玩家带入了.此时提前离桌
             *  2.房间状态为1，下一手发牌开始之前，此时还没有扣取大小盲。提前离桌
             */
            if(room.getRoomStatus() <= 1 ){
                room.getRoomService().settleAheadLeave();
            }

            Object[][] objs2 = {
                    {60, 11, I366ClientPickUtil.TYPE_INT_1},
                    {61, action, I366ClientPickUtil.TYPE_INT_1}
            };
            byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);

            logger.debug("ahead leave successfully,userId={},roomid={},seat={}",userId,roomId,seatId);
            PublisherUtil.publisher(request, bytes2);
        }
    }

    static class FreezeHandler implements IActionHandler {

        @Override
        public void handle(Task task, Request request, Room room, RoomPlayer roomPlayer, UserInfo userInfo, int action, int seatId) {
            int roomId = room.getRoomId();
            int userId = roomPlayer.getUserId();
            RoomPersion roomPersion = room.getAudMap().get(userId);

            if(null != roomPersion){
                int seat = roomPersion.getSize();
                logger.debug("forbbiden user,userId={},roomid={},seat={},iskickout={}",userId,roomId,seat,roomPersion.isKickOut());
                if(seat <= -1){  //站起状态
                    room.getRoomService().likai(roomPersion,LeaveRoomCode.FOBBIDEN_LEAVE);
                }else{   //在座位上
                    int roomStatus = room.getRoomStatus();
                    if(roomStatus <= 1){ //房间未在游戏中玩家在座位上
                        room.getRoomService().likai(roomPersion,LeaveRoomCode.FOBBIDEN_LEAVE);
                    }else{
                        if(!roomPersion.isKickOut()){ //此时未被踢出过
                            if(roomPersion.isStandUp()){    //如果该玩家之前被执行了站起操作，则按照踢出处理
                                roomPersion.setStandUp(false);
                            }

                            roomPersion.setStandupType(2);
                            roomPersion.setKickOut(true);
                        }

                    }
                }
                logger.debug("forbbiden user successfully,userId={},roomid={},seat={}",userId,roomId,seatId);
            }else{
                logger.error("forbbiden user error,user not in room,userId={},roomid={},seat={}",userId,roomId,seatId);
            }

            Object[][] objs = {
                    {60, Constant.ACCOUNT_FROZEN, I366ClientPickUtil.TYPE_INT_1},  //您的账号已经被冻结
                    {61, action, I366ClientPickUtil.TYPE_INT_1}
            };
            byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_SEND_SEAT_ACTION);
            PublisherUtil.publisher(request, bytes);
        }
    }


    /**
     * 检查是否超过限制坐下
     * @param room
     * @param userId
     * @param clubId
     * @return
     */
    static int isLimitSit(Room room, int userId, int clubId) {
        int roomType = getRoomType(room);
        // 目前只对联盟房进行限制检查
        if (roomType == 1) {
            // 先判断是否被冻结
            if (memberIsFroze(userId, clubId)) {
                return Constant.USER_LIMIT_SIT;
            }
            // 再判断俱乐部是否被冻结
            if (clubIsFroze(clubId)) {
                return Constant.CLUB_LIMIT_SIT;
            }
        }
        return 0;
    }

    static int getRoomType(Room room) {
        int clubRoomType = room.getClubRoomType();
        int tribeRoomType = room.getTribeRoomType();
        // 1联盟房，2俱乐部房，3私人房d
        return clubRoomType == 1 ? ( tribeRoomType == 1 ? 1 : 2 ) : 3;
    }

    static Boolean memberIsFroze(int userId, int clubId) {
        String queryMemberStatusSql = "select user_status from crazy_poker.club_members where user_id = ? and club_id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(queryMemberStatusSql)) {
            stmt.setInt(1, userId);
            stmt.setInt(2, clubId);
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                // 状态 1正常 2冻结
                return rs.getInt("user_status") == 2;
            }
        } catch (SQLException ex) {
            logger.error("", ex);
        }
        return false;
    }

    static Boolean clubIsFroze(int clubId) {
        String queryClubStatusSql = "select club_status from crazy_poker.tribe_members where status = 1 and club_id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(queryClubStatusSql)) {
            stmt.setInt(1, clubId);
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                // 状态 1正常 2冻结
                return rs.getInt("club_status") == 2;
            }
        } catch (SQLException ex) {
            logger.error("", ex);
        }
        return false;
    }

    /**
     * 踢出观众处理
     */
    static class KickPlayerHandler implements IActionHandler {
        @Override
        public void handle(Task task, Request request, Room room, RoomPlayer roomPlayer, UserInfo userInfo, int action, int seatId) {
            int roomId = room.getRoomId();
            int userId = roomPlayer.getUserId();
            Integer targetUserId = (Integer) task.getMap().get(132);

            if (targetUserId == null) {
                logger.error("kickout error, targetUserId is null,userid={},roomid={}",userId,roomId);
                return;
            }

            boolean isAiPlayer = isAiPlayer(targetUserId);
            logger.debug("KickPlayerHandler.handle targetUserId={},roomId={},isAiPlayer={}",targetUserId,roomId,isAiPlayer);

            // 判断是否为AI玩家
            if (isAiPlayer) {
                // 如果是AI玩家则直接踢出
                IViewerManager viewerManager = IViewerManager.getInstance();
                // 如果是踢AI，则直接踢出（不加入黑名单）
                viewerManager.kickOutWatcher(room.getRoomId(), targetUserId);

                Object[][] objs2 = {
                        {60, 10, I366ClientPickUtil.TYPE_INT_1},
                        {61, 5, I366ClientPickUtil.TYPE_INT_1}
                };

                byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
                PublisherUtil.publisher(request, bytes2);

            } else {
                RoomPersion rpOut = room.getAudMap().get(targetUserId);
                if (rpOut == null) {
                    logger.error("kickout error, targetUserId is not in room,userid={},targetUserId={},roomid={}",userId,targetUserId,roomId);
                    return;
                }
                // 默认-1，-1为观众
                int userSeatId = -1;
                // 根据userId获取座位号
                for (int i = 0; i < room.getRoomPersions().length; i++) {
                    RoomPersion out = room.getRoomPersions()[i];
                    if (out != null && room.getRoomPersions()[i].getUserId() == targetUserId) {
                        userSeatId = i;
                        break;
                    }
                }

                if (userSeatId != -1) {
                    // 如果不等于-1，则说明在位置上，则走正常的踢出逻辑
                    handlers.get(5).handle(task, request, room, roomPlayer, userInfo, action, userSeatId);
                    return;
                }

                int permissionKickOut;
                if (room.getAudMap().containsKey(userId)){
                    if (room.getAudMap().get(userId).getUserInfo().isPrivilege()){
                        permissionKickOut=1;
                    }else {
                        //查询申请踢出的玩家对被踢玩家是否有权限
                        permissionKickOut = PermissionUtil.havePermission(userId,targetUserId,room);
                    }
                }else {
                    permissionKickOut=0;
                }
                // 9没有权限  10有权限
                int status2 = permissionKickOut == 1 ? 10 : 9;

                Object[][] objs2 = {
                        {60, status2, I366ClientPickUtil.TYPE_INT_1},
                        {61, 5, I366ClientPickUtil.TYPE_INT_1}
                };

                byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);

                //没有权限 直接返回
                if(status2 == 9){
                    logger.error("kickout error,not have permission,userid={},outuserid={},roomid={}",userId,targetUserId,room.getRoomId());
                    PublisherUtil.publisher(request, bytes2);
                } else {
                    //调用离开逻辑
                    room.getRoomService().likai(rpOut, LeaveRoomCode.KICK_OUT_LEAVE);
                    //将用户加入到该房间被踢的集合中，在进入房间时判断（普通玩家需要加入黑名单）
                    RedisService.getRedisService().addKickOutUser(targetUserId,roomId);
                    logger.debug("kickout user successfully,userId={},outUserId={},roomid={}", userId, targetUserId, roomId);
                    PublisherUtil.publisher(request, bytes2);
                }
            }
        }

        /**
         * 判断是否是AI玩家
         * @param userId
         * @return
         */
        private boolean isAiPlayer(int userId) {
            // 查询上一次登录记录
            String sql = "select count(user_id) as count from at_user where user_id = ?";
            try (Connection connection = DBUtil.getConnection();
                 PreparedStatement ps = connection.prepareStatement(sql)) {
                ps.setInt(1, userId);
                ResultSet rs = ps.executeQuery();
                logger.debug("isAiPlayer Sql = {}", sql);
                if (rs.next()) {
                    // 获取渠道
                    int count = rs.getInt("count");
                    return count > 0;
                }
            } catch (Exception e) {
                logger.error("Failed to Query isAiPlayer: {}", sql, e);
            }
            return false;
        }
    }


}
