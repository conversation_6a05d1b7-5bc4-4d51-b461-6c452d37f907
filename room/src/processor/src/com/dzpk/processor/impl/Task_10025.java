package com.dzpk.processor.impl;

import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.cache.Cache;
import com.i366.constant.LeaveRoomCode;
import com.i366.model.player.RoomPersion;
import com.i366.model.room.Room;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.db.dao.RoomDao;
import com.dzpk.db.imp.RoomDaoImpl;
import org.apache.logging.log4j.Logger;

/**
 *  AI机器人自动离开(房主未点击开始按钮)
 */
public class Task_10025 implements IProcessor {

    private Logger logger = LogUtil.getLogger(Task_10025.class);

    @Override
    public void handle(Task task) {

        logger.debug("Task_10025 ai auto likai");

        if (!task.isValid()) {
            logger.debug("invalid message");
            return;
        }
        
        Room room = Cache.getRoom(task.getRoomId(), task.getRoomPath());
            if (null != room) {

                int userId = (int) task.getMap().get(1);

                logger.debug("Task_10025 ai auto likai, userId: " + userId + ", roomid: " + room.getRoomId());
                //手动点击离开房间，则不需要通知离开
                RoomPersion aiRoomPersion = room.getAudMap().get(userId);
                if(aiRoomPersion == null){
                    return;
                }
                RoomDao roomDao = new RoomDaoImpl();
                int roomStatus = roomDao.getRoomProgress(room.getRoomId());

                logger.debug(" roomStatus: " + roomStatus);
                if(roomStatus == 3){ //房主未点击开始按钮
                    room.getRoomService().likai(aiRoomPersion, LeaveRoomCode.AI_ROOM_NOT_START_LEAVE);
                }
            }
    }
}
