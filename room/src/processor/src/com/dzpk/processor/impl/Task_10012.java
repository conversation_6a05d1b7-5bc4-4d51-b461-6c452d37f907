package com.dzpk.processor.impl;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.db.dao.RoomDao;
import com.dzpk.db.imp.RoomDaoImpl;
import org.apache.logging.log4j.Logger;

import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.cache.Cache;
import com.i366.model.room.Room;

/**
 * 牌局时间到 结算
 * <AUTHOR>
 *
 */
public class Task_10012 implements IProcessor {

    private Logger logger = LogUtil.getLogger(Task_10012.class);
    
    @Override
    public void handle(Task task) {
        if (!task.isValid()) {
            logger.debug("invalid message");
            return;
        }
        
        Room room = Cache.getRoom(task.getRoomId(), task.getRoomPath());
        logger.debug("roomId: " + task.getRoomId() + ", roomPath: " + task.getRoomPath());
        if (room != null) {
            RoomDao roomDao = new RoomDaoImpl();
            int roomStatus = roomDao.getRoomProgress(room.getRoomId());
            logger.debug("Task_10012 roomstatus: " + room.getRoomStatus() + ", status: " + roomStatus);
            if (room.getRoomStatus() <= 1 && roomStatus == 4) {  //没有人再玩且已经点了开始按钮
                logger.debug("roomId: " + task.getRoomId() + " times up and no player in room now ");
                room.roomProcedure.forceCloseRoom = true;
                room.roomProcedure.status1(1);
            }
            
            room.roomProcedure.delayTaskMap.remove(task.getId());
        }
    }

}
