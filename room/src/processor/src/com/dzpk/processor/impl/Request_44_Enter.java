package com.dzpk.processor.impl;

import com.dzpk.commission.repositories.mysql.ClubTribeDao;
import com.dzpk.commission.repositories.mysql.impl.ClubTribeDaoImpl;
import com.dzpk.commission.repositories.mysql.model.ClubTribeModel;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.repositories.redis.RedisService;
import com.dzpk.db.dao.RoomDao;
import com.dzpk.db.dao.UserInfoDao;
import com.dzpk.db.imp.RoomDaoImpl;
import com.dzpk.db.imp.UserInfoDaoImp;
import com.dzpk.db.model.UserInfo;
import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.cache.Cache;
import com.i366.constant.Constant;
import com.i366.model.room.Room;
import com.i366.util.CheckRoomTier;
import com.i366.util.IPUtil;
import com.i366.util.PublisherUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.Request;
import org.apache.logging.log4j.Logger;

import java.sql.SQLException;
import java.util.Set;

/**
 * 进入
 * 进入房间
 */
public class Request_44_Enter implements IProcessor {

    private final Logger logger = LogUtil.getLogger(Request_44_Enter.class);

    public void handle(Task task) {
        long startTime = System.currentTimeMillis();
        Request request = (Request) task.getRequest();
        int roomId = task.getRoomId();
        int roomPath = task.getRoomPath();
        int userId = request.getUserId();
        Integer clubId = (Integer)task.getMap().get(133);

        Room room;
        logger.info("玩家加入房间...... roomId={} userId={} clubId={}", roomId, userId, clubId);
        try {
            room = Cache.getRoom(roomId, roomPath);
        } catch (Exception e) {
            logger.error("find room error", e);
            PublisherUtil.publisher(request, pusUser(1));
            return;
        }

        /**
         * room不存在可能存在2种情况  房间未开始,需要初始化;房间已经结束,被移除
         */
        if (room == null) {
            RoomDao roomDao = new RoomDaoImpl();
            if (roomDao.createRoom(roomPath, roomId)) {
                room = Cache.getRoom(roomId, roomPath);
                logger.debug("init room,roomId={},roomPath={}", roomId, roomPath);
            }

            if (room == null) {
                logger.error("can't found room,maybe it is finished, roomId={},roomPath={}", roomId, roomPath);
                PublisherUtil.publisher(request, pusUser(2));
                return;
            }
        }

        /** 房间即将结束,不允许进入 **/
        if (room.getFinished()) {
            logger.error("room will be finished soon,can't enter, roomId={},roomPath={}", roomId, roomPath);
            PublisherUtil.publisher(request, pusUser(2));
            return;
        }

        /** 判断是否到达房间人数上限 **/
        int size = room.getAudMap().size();
        if (size >= Constant.ROOM_MAX_AUD) {
            logger.error("too many persion in room,roomId={}, persion size={}", room.getRoomId(), size);
            PublisherUtil.publisher(request, pusUser(3));
            return;
        }

        /** 判断玩家是否被冻结 **/
        UserInfoDao userInfoDao = new UserInfoDaoImp();

        boolean frozen;
        try {
            frozen = userInfoDao.checkUserIfFozen(userId);
        } catch (SQLException e) {
            logger.error("checkUserIfFozen error", e);
            PublisherUtil.publisher(request, pusUser(1));
            return;
        }
        if (frozen) {
            logger.error("can't enter room, user is frozen roomId={},userId={}", room.getRoomId(), userId);
            PublisherUtil.publisher(request, pusUser(6));
            return;
        }
        ClubTribeModel clubTribeModel = null;
        if (clubId != null) {
            /** 判断俱乐部、联盟是否正常 **/
            ClubTribeDao clubTribeDao = new ClubTribeDaoImpl();
            clubTribeModel = clubTribeDao.getClubTribeModel(clubId);

            if (null != clubTribeModel) {
                int clubTribeStatus = clubTribeModel.getClubTribeStatus();
                int clubStatus = clubTribeModel.getClubStatus();
//                int tribeStatus = clubTribeModel.getTribeStatus();

                if (2 == clubTribeStatus || 3 == clubTribeStatus) { //处于被踢出中或者转移中的俱乐部
                    logger.error("can't enter room,club is kick out,userId={},clubInfo={}", userId, clubTribeModel.toString());
                    PublisherUtil.publisher(request, pusUser(7));
                    return;
                }

                if (1 == clubStatus) { //俱乐部处于关闭状态
                    logger.error("can't enter room,club or tribe is closed,userId={},clubInfo={}", userId, clubTribeModel.toString());
                    PublisherUtil.publisher(request, pusUser(8));
                    return;
                }
            } else {
                logger.error("can't enter room, club or tribe not found, userId={},clubId={},tribeId={}", userId, clubId, room.getTribeId());
                PublisherUtil.publisher(request, pusUser(1));
                return;
            }
        }

        // 判断是否被踢出且在黑名单
        if (checkKickOut(roomId, userId)) {
            PublisherUtil.publisher(request, pusUser(19));
            return;
        }

        /** 重新初始化基本信息 **/
        UserInfo userInfo;
        try {
            userInfo = userInfoDao.getUserInfo(userId);
            userInfo.setIp(IPUtil.getRealUserIp(request));
        } catch (SQLException e) {
            logger.error("find userInfo sql error", e);
            PublisherUtil.publisher(request, pusUser(1));
            return;
        }

        userInfo.setOnlineTime(System.currentTimeMillis());
        userInfo.setChannel(request.getChannel());
        userInfo.setRoomId(roomId);
        userInfo.setRoomPath(roomPath);
        if (clubTribeModel != null) {
            if (clubTribeModel.getClubId() != 0) {
                userInfo.setClubId(clubTribeModel.getClubId());
                userInfo.setClubProportion(clubTribeModel.getClubProportion());
            }
            if (clubTribeModel.getTribeId() != 0) {
                userInfo.setTribeId(clubTribeModel.getTribeId());
                userInfo.setTribeProportion(clubTribeModel.getTribeProportion());
                userInfo.settClubId(clubTribeModel.getTClubId());
            }
        }

        boolean checkTier = CheckRoomTier.builder()
                .roomId(roomId)
                .tribeId(room.getTribeId())
                .roomTierId(room.getTierId())
                .clubId(userInfo.getClubId())
                .userId(userId)
                .roomOwner(room.getOwner())
                .isPrivilege(userInfo.isPrivilege())
                .isTribeRoom(room.isTribeRoom())
                .build()
                .check();

        if (!checkTier) {
            // 如果是没有权限进入房间，则提示没有权限进入房间
            logger.error("user {} has no permission to enter room {}", userId, roomId);
            PublisherUtil.publisher(request, pusUser(20));
            return;
        }


        room.getUserDevicesMap().put(userId, request.getClientPlatform());
        //        int clubId = room.getClubId();
//        if (clubId != 0) {
//            // 俱乐部，才有可能进行带入审批,用户的总带入
//            int bringIn = room.getDealer().getPlayers().get(userId).getBringIn();
//            if(bringIn > 0){
//                // 存在用户带入，
//            }
//        }
        /** 调用进房 **/
        room.getRoomService().accessRoom(userInfo);

        logger.debug(".....进入房间用去的时间: " + (System.currentTimeMillis() - startTime));

    }

    private byte[] pusUser(int status) {
        Object[][] objs2 = {
                {60, status, I366ClientPickUtil.TYPE_INT_1}        //   返回状态 0成功 1失败 2解散 3人数已满 4请先在游戏中的房间站起再重新进入新房间 6玩家被冻结 7玩家所在俱乐部被踢出或处于转移中 8玩家所在俱乐部状态异常 19 玩家被踢出房间
        };
        return I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_ENTER_ROOM);
    }

    /**
     * 检查是否被踢出房间
     * @param roomId
     * @param userId
     * @return
     */
    private boolean checkKickOut(int roomId, int userId) {
        Set<String> kickOutUserSet = RedisService.getRedisService().getKickOutUser(roomId);
        logger.debug("[R-{}][U-{}] check kick out:{}", roomId, userId, kickOutUserSet);
        if (kickOutUserSet.contains(userId + "")) {
            logger.info("[R-{}][U-{}] enter user has been kicked out by this room!", roomId, userId);
            return true;
        }
        return false;
    }

}
