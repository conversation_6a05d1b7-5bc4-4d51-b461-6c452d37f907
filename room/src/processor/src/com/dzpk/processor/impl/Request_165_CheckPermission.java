
package com.dzpk.processor.impl;

import com.dzpk.db.dao.UserInfoDao;
import com.dzpk.db.imp.UserInfoDaoImp;
import com.dzpk.dealer.Player;
import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.constant.Constant;
import com.i366.cache.Cache;
import com.i366.model.player.RoomPersion;
import com.i366.model.player.RoomPlayer;
import com.i366.model.room.Room;
import com.i366.util.PermissionUtil;
import com.work.comm.client.protocal.Request;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.dzpk.common.utils.LogUtil;
import com.i366.util.PublisherUtil;
import com.dzpk.db.model.UserInfo;

import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * 查询对某玩家是否有踢出权限
 */
public class Request_165_CheckPermission implements IProcessor {

    private Logger logger = LogUtil.getLogger(Request_165_CheckPermission.class);

    @Override
    public void handle(Task task) {
        int roomId = task.getRoomId();
        int roomPath = task.getRoomPath();
        int outUserId = (Integer) task.getMap().get(132);  //被踢出的用户id
        Request request = (Request) task.getRequest();
        int userid = request.getUserId();
        UserInfo userInfo = Cache.getOnlineUserInfo(userid, roomId);
        if (userInfo == null) {
            logger.error("user is null, userid: " + userid);
            PublisherUtil.publisher(request, pusUser(1, 0, outUserId));
            return;
        }

        Room room = Cache.getRoom(roomId, roomPath);
        if (room == null) {
            logger.error("room is null, userid: " + outUserId);
            PublisherUtil.publisher(request, pusUser(1, 0, outUserId));
            return;
        }

        RoomPersion p = room.getAudMap().get(userid);
        if (p == null) {
            logger.error("user is null, userid: " + outUserId);
            PublisherUtil.publisher(request, pusUser(1, 0, outUserId));
            return;
        }
        int permission=0;

        if (userid!=outUserId) permission=(room.getOwner()==userid||userInfo.isPrivilege())?1:0;
//        int permission = PermissionUtil.havePermission(userid,outUserId,room.getRoomId());

        boolean isLeave = isLeave(room, outUserId);
        if (permission == 1 && isLeave) {
            // 如果有权限，且他离开了，则无法踢出
            permission = 0;
        }
        logger.debug("userid: " + userid + " outUserId: " + outUserId + " permission: " + permission);
        PublisherUtil.publisher(request, pusUser(0, permission, outUserId));

        return;

    }

    private byte[] pusUser(int status, int permission, int userId) {
        logger.debug("status: " + status);
        Object[][] objs2 = {
                {60, status, I366ClientPickUtil.TYPE_INT_1},        // 返回状态 0成功 1失败
                {130, permission, I366ClientPickUtil.TYPE_INT_1},        // 0:没有权限，1：有踢出站起权限
                {131, userId, I366ClientPickUtil.TYPE_INT_4}        // 被查询用户的userID(用于校对是否当前需查询的用户)
        };
        byte[] bytes = I366ClientPickUtil.packAll(objs2, Constant.REQ_CHECK_FOR_PERMISSION);
        return bytes;
    }


    private void add(Iterator<Integer> src, List<Integer> dst) {
        if (null == src || null == dst)
            return;
        while (src.hasNext()) {
            Integer w = src.next();
            if (null == w)
                continue;
            if (dst.contains(w))
                continue;
            dst.add(w);
        }
    }

    public boolean isLeave(Room room, int userId) {
        // 获取离开的用户ID集合
        Set<Integer> rpUserIds = room.getLeaveAud();
        // 如果离开集合不为空，添加离开的用户到观察者列表
        Optional.ofNullable(rpUserIds)
                .ifPresent(new LinkedList<>(room.getDealer().getWatchers())::addAll);
        // 直接检查用户是否在离开集合中
        return Optional.ofNullable(rpUserIds)
                .map(ids -> ids.contains(userId))
                .orElse(false);
    }

}
