package com.dzpk.processor.impl;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.db.dao.RoomDao;
import com.dzpk.db.imp.RoomDaoImpl;
import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.cache.Cache;
import com.i366.constant.Constant;
import com.i366.model.room.Room;
import com.i366.util.PublisherUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.Request;
import org.apache.logging.log4j.Logger;

public class Request_187_RoomPause implements IProcessor {

    private Logger logger = LogUtil.getLogger(Request_187_RoomPause.class);
    
    @Override
    public void handle(Task task) {
        int roomId = task.getRoomId();
        int roomPath = task.getRoomPath();
        Request request = (Request) task.getRequest();
        boolean isPause = (Integer) task.getMap().get(60) == 0 ? false : true;

        logger.debug("roomId:" + roomId + ", roomPath:" + roomPath + ", isPause:" + isPause);
        byte[] bytes;
        try {
            Room room = Cache.getRoom(roomId, roomPath);
            if (room != null && request.getUserId() == room.getOwner()) {
                if (isPause) {
                    room.getRoomService().pauseRoom();
                } else {
                    room.getRoomService().continueRoom();
                }
                logger.debug("pause begin time: " + room.getPauseBeginTime()
                        + ", current time: " + System.currentTimeMillis());
                RoomDao roomDao = new RoomDaoImpl();
                roomDao.updateRoomPause(roomId, isPause);
                long leftTime = room.getPauseBeginTime() + Constant.PAUSE_TIME - System.currentTimeMillis();
                if (leftTime < 0) {
                    leftTime = 0;
                }
                leftTime /= 1000;
                Object[][] objs = {
                        {60, isPause ? 1 : 0, I366ClientPickUtil.TYPE_INT_1},    // 0 continue 1 pause 2 failed
                        {130, (int) leftTime, I366ClientPickUtil.TYPE_INT_4},    // 暂停剩余时间(s)
                };
                bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_ROOM_PAUSE);
                PublisherUtil.send(room,bytes, request.getUserId());
                PublisherUtil.publisher(request, bytes);
                return;
            }
        } catch (Exception e) {
            logger.debug("", e);
        }
        PublisherUtil.publisher(request, pusUser(2));
    }

    public byte[] pusUser(int status) {
        Object[][] objs = {
                {60, status, I366ClientPickUtil.TYPE_INT_1},
        };
        byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_ROOM_PAUSE);
        return bytes;
    }
}
