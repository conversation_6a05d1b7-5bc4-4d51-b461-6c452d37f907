package com.dzpk.system;

import com.dzpk.jackpot.sync.JackpotFundWatchService;
import com.i366.constant.Constant;
import com.i366.cache.Cache;
import com.i366.model.room.Room;
import com.i366.util.PublisherUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.server.pack.I366ServerPickUtil;
import com.dzpk.common.utils.LogUtil;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * Created by peter<PERSON><PERSON> on 2018/3/30.
 * 监听zookeeper中彩池的变动
 */
public class JackPotFundManage {

    public static Logger logger = LogUtil.getLogger(JackPotFundManage.class);

    public static void init(){
        logger.debug("init zk jackpotFund。。。。。。。");

        JackpotFundWatchService jackpotFundWatchService = JackpotFundWatchService.getInstance();

        jackpotFundWatchService.setFundCallback(new JackpotFundWatchService.IJackpotFunCallback() {
            @Override
            public void notify(int jackpotId, int fund,long totalFund, long version) {
                logger.debug("JackPotFundManage notify =>jackpotId: " + jackpotId + " fund: " + fund +  ",totalFund:" + totalFund + " version: " + version);

                try {
                    List<Room> roomList = Cache.getJackpotRoomInfo();
                    byte[] toJackpotRoombytes = null;//推送往对应jackpotroom房间
                    byte[] toOtherRoombytes = null;//推送往其他房间
                    if(null != roomList && roomList.size() > 0){
                        Object[][] objs = {
                                {60, 0, I366ServerPickUtil.TYPE_INT_1},
                                {130, String.valueOf(totalFund), I366ServerPickUtil.TYPE_STRING_UTF16},//彩池总额
                                {131, String.valueOf(fund), I366ServerPickUtil.TYPE_STRING_UTF16},//子池数额
                        };
                        toJackpotRoombytes = I366ClientPickUtil.packAll(objs, Constant.REQ_JACKPOT_CHANGE);

                        Object[][] objs2 = {
                                {60, 0, I366ServerPickUtil.TYPE_INT_1},
                                {130, String.valueOf(totalFund), I366ServerPickUtil.TYPE_STRING_UTF16},//彩池总额
                                {131, "", I366ServerPickUtil.TYPE_STRING_UTF16},//子池数额
                        };
                        toOtherRoombytes = I366ClientPickUtil.packAll(objs2, Constant.REQ_JACKPOT_CHANGE);
                    }
                    for(Room room : roomList){
                        if(room != null){
                            if(room.getJackpotService().getJackpotId() == jackpotId){
                                PublisherUtil.send(room,toJackpotRoombytes);
                            }else{
                                PublisherUtil.send(room,toOtherRoombytes);
                            }
                        }

                    }

                    logger.debug("notify update jackpot successfully !!!");
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error(e.getMessage(),e);
                }
            }
        });
    }
}
