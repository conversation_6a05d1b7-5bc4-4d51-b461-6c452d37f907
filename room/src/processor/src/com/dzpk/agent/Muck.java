package com.dzpk.agent;

import com.i366.room.BiPai;
import com.dzpk.insurance.PoolChip;
import com.i366.model.player.RoomPersion;
import com.i366.model.room.Room;
import com.dzpk.common.utils.LogUtil;
import org.apache.logging.log4j.Logger;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;

/**
 * Created by baidu on 16/12/12.
 */
public class Muck {
    private static Logger logger = LogUtil.getLogger(Muck.class);

    /**
     * 获取盖牌用户座位号
     *
     * @param room
     */
    public static Integer[] getMuckSeatIds(Room room) {
        RoomPersion[] rps = room.getRoomPersions();
        Integer[] muckIds = new Integer[room.getPlayerCount()];
        // 初始化盖牌列表
        for (int i = 0; i < muckIds.length; i++) {
            muckIds[i] = 0;
        }

        try {
            Set<Integer> showIds = new HashSet<>();

            // 只有到河牌阶段才会触发盖牌
            logger.debug("muck:" + room.getMuck());
            if (true == room.getMuck()) {
                int attackId = room.getMuckAttackId();
                logger.debug("attackId:" + attackId);
                // 所有人check 那从小盲开始亮牌
                if (attackId == -1) {
                    room.setMuckAttackId(room.getManzhuNumber());
                    attackId = room.getManzhuNumber();

                    // 如果小盲站起来了 顺时针找下一个在座位上的玩家来开始亮牌
                    if (rps[attackId] == null) {
                        boolean found = false;
                        for (int i = attackId + 1; i < room.getPlayerCount() - 1; i++) {
                            if (rps[i] != null) {
                                attackId = i;
                                found = true;
                                break;
                            }
                        }

                        if (found == false) {
                            for (int i = 0; i < attackId; i++) {
                                if (rps[i] != null) {
                                    attackId = i;
                                    break;
                                }
                            }
                        }
                    }
                }
                logger.debug("attackId:" + attackId);

                // 攻击者必须亮牌
                // 从攻击者开始, 顺时针牌型依次递增找出所有亮牌用户座位号
                showIds.add(attackId);
                int curMaxId = attackId;

                // 列出以攻击者开始的座位环形列表
                List<Integer> seats = new ArrayList<Integer>();
                for (int i = attackId; i < room.getPlayerCount(); i++) {
                    seats.add(i);
                }
                for (int i = 0; i < attackId; i++) {
                    seats.add(i);
                }
                logger.debug("sort seats:" + seats.toString());

                // 不小于当前亮牌用户牌型的 才亮牌
                for (int i = 0; i < seats.size(); i++) {
                    int seat = seats.get(i);
                    if (rps[seat] == null || rps[seat].getStatus() < 0 || seat == curMaxId) {
                        continue;
                    }

                    int res = BiPai.bipai2(rps[seat], rps[curMaxId]);
                    logger.debug("bipai seat1:" + seat);
                    logger.debug("bipai seat2:" + curMaxId);
                    logger.debug("bipai res:" + res);
                    if (res <= 0) {
                        curMaxId = seat;
                        logger.debug("setting cur max id:" + seat);
                        showIds.add(seat);
                    }
                }
                logger.debug("show size ids:" + showIds.toString());

                // 亮牌要加上最终参与分池的用户(每个池的赢牌用户)
                Map<Integer, RoomPersion> map = new HashMap<>();
                for (RoomPersion rp : rps) {
                    if (rp != null && rp.getStatus() > 0) {
                        map.put(rp.getUserId(), rp);
                    }
                }

                ArrayList<PoolChip> poolList = room.getRoomService().getPoolList(false);
                for (int i = 0; i < poolList.size(); i++) {
                    PoolChip subPool = poolList.get(i);
                    Set<Integer> users = subPool.getUserIds();
                    // 如果是某个人多出的边池 不用管
                    if (users.size() > 1) {
                        // 找出这个池最大牌的人
                        List<RoomPersion> bipaiUsers = new ArrayList<>();
                        for (Integer user : users) {
                            if (map.containsKey(user)) {
                                bipaiUsers.add(map.get(user));
                            }
                        }

                        // 如果这个池单人赢了 其他人全部弃牌 这个人必须亮牌
                        if (bipaiUsers.size() == 1) {
                            showIds.add(bipaiUsers.get(0).getSize());
                            logger.debug("single win in pool " + i);
                        } else if (bipaiUsers.size() > 1) {
                            // 如果多人比牌 找出最大牌的人(可能有多个)
                            RoomPersion[] rps2 = new RoomPersion[bipaiUsers.size()];
                            for (int z = 0; z < bipaiUsers.size(); z++) {
                                rps2[z] = bipaiUsers.get(z);
                            }
                            for (int j = 0; j < rps2.length; j++) {
                                for (int k = 0; k < rps2.length - j - 1; k++) {
                                    if (BiPai.bipai2(rps2[k], rps2[k + 1]) == 1) {
                                        swap(rps2, k, k + 1);
                                    }
                                }
                            }

                            // 最大牌用户 可能有多个一样大
                            int maxEndIndex = 0;
                            boolean change = false;
                            for (int j = 0; j < rps2.length - 1; j++) {
                                if (BiPai.bipai2(rps2[j], rps2[j + 1]) == 0) {
                                    change = true;
                                    maxEndIndex = j;
                                    break;
                                }
                            }
                            // logger.debug(maxEndIndex);

                            int l = 0;
                            if (change == false) {
                                for (int j = 0; j < rps2.length; j++) {
                                    logger.debug("add max ID:" + rps2[j].getUserId() + ", size:" + rps2[j].getSize());
                                    showIds.add(rps2[j].getSize());
                                }
                            } else {
                                do {
                                    logger.debug("add max ID:" + rps2[l].getUserId() + ", size:" + rps2[l].getSize());
                                    showIds.add(rps2[l].getSize());
                                } while (l++ != maxEndIndex);
                            }
                        }
                    }
                }

                logger.debug("show size ids:" + showIds.toString());

                // 盖牌座位号
                for (int i = 0; i < rps.length; i++) {
                    if (rps[i] != null && rps[i].getStatus() > 0) {
                        logger.debug("size:" + rps[i].getSize());
                        if (showIds.contains(Integer.valueOf(rps[i].getSize()))) {
                            muckIds[i] = 0;
                        } else {
                            if (rps[i].getStatus() != 3) {             // allin的用户不能盖牌
                                muckIds[i] = 1;
                                rps[i].setMuck(true);
                            } else {
                                muckIds[i] = 0;
                            }
                        }
                    } else {
                        muckIds[i] = 0;
                    }
                }
                logger.debug("muck size ids:" + Arrays.toString(muckIds));
            }
        } catch (Exception e) {
            logger.error("muck cal error:", e);
        }

        return muckIds;
    }

    private static void swap(Object[] array, int i, int j) {
        Object temp;
        temp = array[i];
        array[i] = array[j];
        array[j] = temp;
    }
}
