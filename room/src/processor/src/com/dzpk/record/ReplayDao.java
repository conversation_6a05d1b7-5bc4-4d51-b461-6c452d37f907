package com.dzpk.record;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dzpk.commission.fee.TempRoomFee;
import com.dzpk.commission.repositories.mysql.ClubTribeDao;
import com.dzpk.commission.repositories.mysql.impl.ClubTribeDaoImpl;
import com.dzpk.commission.repositories.mysql.model.ClubFeeConfig;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.repositories.mongo.MongodbService;
import com.dzpk.db.dao.UserInfoDao;
import com.dzpk.db.imp.UserInfoDaoImp;
import com.dzpk.db.model.UserInfo;
import com.dzpk.dealer.Player;
import com.dzpk.jackpot.PlayerJpRewardDetail;
import com.i366.model.room.Room;
import com.mongodb.MongoClient;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import org.apache.logging.log4j.Logger;
import org.bson.Document;
import org.bson.types.ObjectId;

import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 历史牌局回放 数据持久化层
 *
 * <AUTHOR>
 */
public class ReplayDao {

    private static final Logger logger = LogUtil.getLogger(ReplayDao.class);
    private static final String split = "@%";
    private static final String db = "dzpk";
    private static final String sp = ",";
    private static final String _handRecordColl = "hand_record";
    private static final String _personHandRecordColl = "hand_person";
    private static final String _recordCollection = "game_record_temp";
    private static final String _detailCollection = "game_detail_temp";

    /**
     * 保存每手游戏记录
     *
     * @param room
     * @param roomReplay
     */
    public static void saveGameReplay(Room room, RoomReplay roomReplay) {
        Map<Integer, String> uriMap = new HashMap<>();
        MongoClient mongo = null;
        try {
            int roomType = 1;
            
            mongo = MongodbService.getMongoInstance();
            MongoDatabase database = mongo.getDatabase(db);
            MongoCollection<Document> handRecordColl = database.getCollection(_handRecordColl);
            MongoCollection<Document> personHandRecordColl = database.getCollection(_personHandRecordColl);

            // 插入房间整体数据
            String uuId = roomReplay.getId();
            Document handDoc = packHandDocument(room, 0, roomType, uuId);
            //logger.info("hand: " + handDoc.toJson());
            handRecordColl.insertOne(handDoc);

            // 插入房间个人数据
            List<Document> personList = new ArrayList<>();
            Map<Integer, PrevPlayer> players = roomReplay.getRoomRecord().getPrevGame().getPrevPlayers();
            for (Integer userId : players.keySet()) {
                if (roomType == 0) {
                    uuId = room.getRoomPlayers().get(userId).getRoomUUID();
                }
                String objId = ObjectId.get().toString();
                handDoc = packHandDocument(room, userId, roomType, uuId);
                handDoc.put("_id", new ObjectId(objId));
                //logger.info("[[userId=" + userId + " hand: " + handDoc.toJson());
                personList.add(handDoc);
                uriMap.put(userId, uuId + "_" + userId + "_" + objId);
            }
            personHandRecordColl.insertMany(personList);
        } catch (Exception e) {
            logger.error("save game replay error:", e);
        } finally {
            MongodbService.close(mongo);
        }

        // 该手数据存入内存
        room.getReplayCache().save(room, roomReplay, uriMap);

        saveHandRecord(room);
    }


    /**
     * 写入玩家的游戏记录
     *
     * @param room
     * @param id 玩家id
     * @param type 1:普通局 0:无限局
     * @param uuId 房间UUID
     * @return
     */
    public static Document packHandDocument(Room room, int id, int type, String uuId) {
        RoomReplay roomReplay = room.getRoomReplay();
        Document handDoc = new Document();

        handDoc.put("ID", uuId);
        handDoc.put("TIME", roomReplay.getStartTime());
        Player playerInfo = room.getDealer().getPlayers().get(id);

        handDoc.put("HAND",  roomReplay.getStage());

        int realHand = 0;
        if(null != playerInfo){
            realHand = playerInfo.getHandCnt();
        }else{
            realHand = roomReplay.getStage();
        }
        handDoc.put("REALHAND", realHand);  //需要记录玩家的实际手数

        handDoc.put("ENDTIME", roomReplay.getEndTime());
        handDoc.put("ROOMNAME", roomReplay.getRoomName());
        handDoc.put("ROOMID", roomReplay.getRoomId());
        handDoc.put("OWNER", roomReplay.getOwner());
        handDoc.put("STRADDLE", roomReplay.getStraddle());
        Integer[] winners = roomReplay.getWinners();
        if (winners != null) {
            handDoc.put("WINNERS", Arrays.toString(winners));
        }
        
        if (id != 0) {
            PrevPlayer player = roomReplay.getRoomRecord().getPrevGame().getPrevPlayers().get(id);
            handDoc.put("USERID", id);
            handDoc.put("NICKNAME", player.getNikeName());
            handDoc.put("ICON", player.getUserHead());
            handDoc.put("WIN", player.getWinChip());
            handDoc.put("JACKPOT", null== room.getJackpotService()?0:room.getJackpotService().getCurrentJpBet(player.getUserId()));
            handDoc.put("JACKPOT_REAL", null== room.getJackpotService()?0:room.getJackpotService().getCurrentRealJpBet(player.getUserId()));
            handDoc.put("JACKPOT_PL", null== room.getJackpotService()?0:room.getJackpotService().getCurrentJpReward(player.getUserId()));
            handDoc.put("JACKPOT_PL_FEE", null== room.getJackpotService()?0:room.getJackpotService().getCurrentJpRewardFee(player.getUserId()));
        }

        try {
            Map<String, Object> tableMap = packTableMap(roomReplay, id);
            handDoc.put("TABLE", tableMap);
        } catch (Exception e) {
            logger.error("table map error", e);
        }

        try {
            Map<String, Object> pokerCardMap = packPokerCardMap(room, type, id);
            // logger.info("pokercard: " + pokerCardMap.toString());
            handDoc.put("POKERCARD", pokerCardMap);
        } catch (Exception e) {
            logger.error("poker card map error", e);
        }

        try {
            Map<String, Object> showdownMap = packShowdownMap(roomReplay, id);
            // logger.info("showdown: " + showdownMap.toString());
            handDoc.put("SHOWDOWN", showdownMap);
        } catch (Exception e) {
            logger.error("show down map", e);
        }
        return handDoc;
    }

    /**
     * 所有玩家信息
     *
     * @param roomReplay
     * @param id
     * @return
     */
    public static Map<String, Object> packTableMap(RoomReplay roomReplay, int id) {
        // table data
        Map<String, Object> tableMap = new HashMap<String, Object>();
        // seat list
        List<Object> seatList = new ArrayList<Object>();

        PrevGame game = roomReplay.getRoomRecord().getPrevGame();
        int seatId = -1;
        if (id != 0) {
            seatId = game.getPrevPlayers().get(id).getSeatId();
        }
        UserInfoDao userDao = new UserInfoDaoImp();

        for (Integer userId : game.getPrevPlayers().keySet()) {
            // seat data
            Map<String, Object> seatMap = new HashMap<String, Object>();
            PrevPlayer player = game.getPrevPlayers().get(userId);
            seatMap.put("ID", userId);
            UserInfo userInfo = null;
            try {
                userInfo = userDao.getUserInfo(userId);
            } catch (SQLException e) {
                logger.error("find userInfo sql error", e);
            }
            if (id == 0) {
                seatMap.put("NUMBER", player.getSeatId());
            } else {
                seatMap.put("NUMBER", absSeatId(seatId, player.getSeatId(), game.getSeats()));
            }

            seatMap.put("NAME", player.getNikeName());
            if (userInfo != null) {
                if (userInfo.getUseCustom() == 1) {
                    seatMap.put("ICON", userInfo.getCustomUrl());
                } else {
                    seatMap.put("ICON", userInfo.getHead());
                }
            } else {
                seatMap.put("ICON", player.getUserHead());
            }
            seatMap.put("CHIPS", player.getChips());
            seatMap.put("WINCHIPS", player.getWinChip());
            if (player.getStraddleChip() > 0) {
                seatMap.put("STRADDLECHIP", player.getStraddleChip());
            }
            if (id == 0) {
                seatMap.put("CARD", player.getPocers()[0] + " " + player.getPocers()[1]);
            }
            seatList.add(seatMap);
        }
        if (id == 0) {
            tableMap.put("DEALER", game.getDealer());
        } else {
            tableMap.put("DEALER", absSeatId(seatId, game.getDealer(), game.getSeats()));
        }

        tableMap.put("SEAT", seatList);
        tableMap.put("SEATS", game.getSeats());
        // 小盲
        Map<String, Object> sBlind = new HashMap<String, Object>();
        sBlind.put("CHIPS", game.getSmallBlindChip());
        if (id == 0) {
            sBlind.put("NUMBER", game.getSmallBlind());
        } else {
            sBlind.put("NUMBER", absSeatId(seatId, game.getSmallBlind(), game.getSeats()));
        }

        tableMap.put("SBLIND", sBlind);

        // 大盲
        Map<String, Object> bBlind = new HashMap<String, Object>();
        bBlind.put("CHIPS", game.getBigBlindChip());
        if (seatId == -1) {
            bBlind.put("NUMBER", game.getBigBlind());
        } else {
            bBlind.put("NUMBER", absSeatId(seatId, game.getBigBlind(), game.getSeats()));
        }

        // 前注
        tableMap.put("QIANZHU", game.getQianzhu());

        tableMap.put("BBLIND", bBlind);
        return tableMap;
    }

    /**
     * 玩家出牌信息
     *
     * @param room
     * @param id
     * @return
     */
    public static Map<String, Object> packPokerCardMap(Room room, int type, int id) {
        RoomReplay roomReplay = room.getRoomReplay();
        Map<String, Object> pokerCardMap = new HashMap<String, Object>();
        PrevPlayer player = null;
        int seatId = -1;
        int total = roomReplay.getRoomRecord().getPrevGame().getSeats();
        if (id != 0) {
            player = roomReplay.getRoomRecord().getPrevGame().getPrevPlayers().get(id);
            seatId = player.getSeatId();
        }

        for (int i = 0; i < roomReplay.getAnteActions().length; i++) {
            AnteAction anteAction = roomReplay.getAnteActions()[i];
            if (anteAction != null) {
//                logger.debug("anteActionName: " + RoomReplay.anteActionName[i]
//                        + ", anteAction: " + anteAction.getActions().toString());
                pokerCardMap.put(RoomReplay.anteActionName[i],
                        packActions(anteAction, i, seatId, total));
            } else {
                break;
            }
        }

        if (id == 0) {
            if (!room.getReplayCache().isSetCacheFlag(room.getRoomReplay().getStage())) {
                // 将每一手的数据写入缓存
                int playerSize = roomReplay.getRoomRecord().getPrevGame().getPrevPlayers().size();
//                logger.debug("roomid:" + roomReplay.getRoomId());
//                logger.debug("hand:" + roomReplay.getStage());
//                logger.debug("card map:" + pokerCardMap.toString());
//                logger.debug("player size:" + playerSize);
                // 座位号 - userid 映射
                Map<Integer, Integer> seatUidMap = new HashMap<>();
                for (Integer uid : roomReplay.getRoomRecord().getPrevGame().getPrevPlayers().keySet()) {
                    int seat = roomReplay.getRoomRecord().getPrevGame().getPrevPlayers().get(uid).getSeatId();
                    seatUidMap.put(seat, uid);
                }
                room.getReplayCache().recordAction(type, roomReplay.getStage(), pokerCardMap,
                        playerSize, seatUidMap);
            }
        } else {
            // 手牌
            Map<String, Object> holeCards = new HashMap<String, Object>();
            String cards = player.getPocers()[0] + " " + player.getPocers()[1];
            holeCards.put("CARD", cards);
            holeCards.put("NUMBER", absSeatId(seatId, seatId, total));
            pokerCardMap.put("HOLECARD", holeCards);
        }

        return pokerCardMap;
    }

    /**
     * 游戏结果记录
     *
     * @param roomReplay
     * @param id
     * @return
     */
    public static Map<String, Object> packShowdownMap(RoomReplay roomReplay, int id) {
        Map<String, Object> showdownMap = new HashMap<String, Object>();
        List<Object> resultList = new ArrayList<Object>();
        PrevGame game = roomReplay.getRoomRecord().getPrevGame();
        int seatId = -1;
        if (id != 0) {
            seatId = game.getPrevPlayers().get(id).getSeatId();
        }

        for (Integer userId : game.getPrevPlayers().keySet()) {
            PrevPlayer player = game.getPrevPlayers().get(userId);
            if (player.getPocerType() != 0 && player.getPocerType() != -2) { // 没弃牌
                Map<String, Object> action = new HashMap<String, Object>();
                if (id == 0) {
                    action.put("NUMBER", player.getSeatId());
                } else {
                    action.put("NUMBER", absSeatId(seatId, player.getSeatId(), game.getSeats()));
                }
                if (game.getCompare()) {
                    action.put("CARD", player.getPocers()[0] + " " + player.getPocers()[1]);
                    action.put("ACTION", "show " + player.getWinChip());
                } else {
                    String cards = "";
                    if (player.getShowCardId() == 1) {
                        cards = player.getPocers()[0].toString();
                    } else if (player.getShowCardId() == 2) {
                        cards = player.getPocers()[1].toString();
                    } else if (player.getShowCardId() == 3) {
                        cards = player.getPocers()[0].toString() + " " + player.getPocers()[1].toString();
                    }
                    if (!cards.isEmpty()) {
                        action.put("CARD", cards);
                        action.put("ACTION", "show " + player.getWinChip());
                    } else {
                        action.put("ACTION", "noshow " + player.getWinChip());
                    }
                }
                resultList.add(action);
            } else {    // 弃牌
                Map<String, Object> action = new HashMap<String, Object>();
                if (id == 0) {
                    action.put("NUMBER", player.getSeatId());
                } else {
                    action.put("NUMBER", absSeatId(seatId, player.getSeatId(), game.getSeats()));
                }
                String cards = "";
                if (player.getShowCardId() == 1) {
                    cards = player.getPocers()[0].toString();
                } else if (player.getShowCardId() == 2) {
                    cards = player.getPocers()[1].toString();
                } else if (player.getShowCardId() == 3) {
                    cards = player.getPocers()[0].toString() + " " + player.getPocers()[1].toString();
                }
                if (!cards.isEmpty()) {
                    action.put("CARD", cards);
                    action.put("ACTION", "show " + player.getWinChip());
                } else {
                    action.put("ACTION", "noshow " + player.getWinChip());
                }
                resultList.add(action);
            }
        }
        showdownMap.put("PLAYER", resultList);
        return showdownMap;
    }

    /**
     * 游戏动作记录
     *
     * @param anteAction
     * @param stage
     * @param seatId
     * @param total
     * @return
     */
    public static Map<String, Object> packActions(AnteAction anteAction, int stage, int seatId, int total) {
        Map<String, Object> actionMap = new HashMap<String, Object>();
        if (seatId == -1) {
            actionMap.put("PLAYER", anteAction.getActions());
        } else {
            List<Object> actions = new ArrayList<Object>();
            for (int i = 0; i < anteAction.getActions().size(); i++) {
                Map<String, Object> action = (Map<String, Object>) anteAction.getActions().get(i);
                Map<String, Object> action1 = new HashMap<>(action);
                action1.put("NUMBER", absSeatId(seatId, (Integer) action.get("NUMBER"), total));
                actions.add(action1);
            }
            actionMap.put("PLAYER", actions);
        }

        if (stage > 0) {
            actionMap.put("CARD", anteAction.getCard());
            actionMap.put("POT", anteAction.getPot());
        }
        if (anteAction.getAllin() == 1) {
            actionMap.put("SHOWCARD", 1);
        }

        return actionMap;
    }

    /**
     * 计算相对座位号
     *
     * @param i 基准座位号
     * @param j 待计算座位号
     * @param t 总座位数
     * @return
     */
    public static final int absSeatId(int i, int j, int t) {
        return (j - i + t) % t + 1;
    }


    private static void saveHandRecord(Room room) {
        // 生成战绩数据
        ArrayList<Object[]> records = LogManage.genGameReport(room);
        int maxPot = room.getDealer().getMaxPot();

        int allBring = 0;

        //土豪 大鱼 鲨鱼
        int richer = 0;
        int richerBring = 0;
        int shark = 0;
        int sharkWin = -9999999;
        int fish = 0;
        int fishLoss = 9999999;

        List<Integer> tribeIdArray = new ArrayList<>(); //同盟id
        List<Integer> tribeIdRepeatArray = new ArrayList<>(); //同盟id(重复的)
        List<Double> tribeFeeArray = new ArrayList<>(); //联盟服务费
        List<String> clubNameArray = new ArrayList<>();  //俱乐部名称

        List<Integer> userIdArray = new ArrayList<>();
        List<Integer> plArray = new ArrayList<>();
        List<Double> plFeeArray = new ArrayList<>();
        List<Integer> handArray = new ArrayList<>();
        List<Integer> bringArray = new ArrayList<>();
        List<Integer> jpArray = new ArrayList<>();//玩家投入Jackpot的总额
        List<Integer> jpRealArray = new ArrayList<>();//玩家投入jackpot的实际总额
        List<Integer> jpPlArray = new ArrayList<>();//玩家击中jackpot的总额
        List<Double> jpPlFeeArray = new ArrayList<>();//玩家击中jackpot的服务费

        // 社区名字（用于展示玩家属于那个社区）
        List<Integer> clubIdArray = new ArrayList<>();
        List<Double> clubFeeArray = new ArrayList<>();
        // 社区和盈亏集合
        HashMap<Integer, Integer> clubMap = new HashMap<Integer, Integer>();
        // 保险
        List<Integer> insuranceArray = new ArrayList<Integer>();

        boolean includeOwnerId = false;
        StringBuilder userStr = new StringBuilder();

        /** 获取俱乐部分润配置 **/
        ClubTribeDao clubTribeDao = new ClubTribeDaoImpl();

        Set<Integer> bringClubIds = room.getFeeService().getBringClubs().stream()
                .filter(clubId -> clubId != 0)
                .collect(Collectors.toSet());
        Map<Integer, ClubFeeConfig> clubFeeConfigMap = clubTribeDao.getFeeConfig(bringClubIds);
        TempRoomFee tempRoomFee = (TempRoomFee)room.getFeeService().getRoomFeeByType(1);
        for (int i = 0; i < records.size(); i++) {
            Object[] obj = records.get(i);
            int userId = (Integer) obj[0];
            int pl = (Integer) obj[1];
            int hand = (Integer) obj[2];
            int bring = (Integer) obj[3];
            int clubId = (Integer)obj[6];
            int insurance = (Integer)obj[7];

            userStr.append(userId);
            if (i != records.size() - 1) {
                userStr.append(",");
                if (room.getOwner() == userId) {
                    includeOwnerId = true;
                }
            }

            // 重新拆成多个数组
            // 只有带入不为0和管理员或同盟创建人的才会保存
            if (bring != 0 ) {
                userIdArray.add(userId);
                plArray.add(pl);
                handArray.add(hand);
                bringArray.add(bring);

                clubIdArray.add(clubId);
                clubMap.put(clubId, (clubMap.get(clubId) == null ? 0 : clubMap.get(clubId)) + pl);
                insuranceArray.add(insurance);

                double plFee = 0;
                if(null != room.getFeeService() && clubId > 0) {
                    ClubFeeConfig clubFeeConfig = clubFeeConfigMap.get(clubId);
                    if(null != clubFeeConfig){
                        int tribeId = clubFeeConfig.getTribeId();
                        if (tribeId != 0) {
                            if(!tribeIdArray.contains(tribeId)){
                                tribeIdArray.add(tribeId);
                            }
                            tribeIdRepeatArray.add(tribeId);
                        }
//                        plFee = room.getFeeService().collectPayer(userId, pl, insurance,clubFeeConfig,tempRoomFee);

                        Player player = room.getDealer().getPlayers().get(userId);
                        if(null != player){
                            plFee = player.getPlFee();
                            clubNameArray.add(player.getClubName());
                        }else{
                            logger.warn("cannot find player when calculating commission:rid={},uid={},pl={},plFee={}",
                                    room.getRoomId(),userId,pl,plFee);
                        }

                        logger.debug("rid={},uid={},clubId={},tribeId={},pl={},plFee={}", room.getRoomId(), userId, clubId, tribeId, pl, plFee);
                    }else{
                        logger.warn("Cannot find clubFeeConfig when calculating commission:rid={},uid={},clubId={}",
                                room.getRoomId(),userId,clubId);
                    }
                }
                plFeeArray.add(plFee);

                if(null == room.getJackpotService()){
                    jpArray.add(0);
                    jpRealArray.add(0);
                    jpPlArray.add(0);
                    jpPlFeeArray.add(0.00d);
                }else {
                    jpArray.add(room.getJackpotService().getTotalJpBet(userId));
                    jpRealArray.add(room.getJackpotService().getTotalRealJpBet(userId));
                    jpPlArray.add(room.getJackpotService().getTotalJpReward(userId));
                    jpPlFeeArray.add(room.getJackpotService().getTotalJpRewardFee(userId));
                }
            }

            // 所有用户总带入
            allBring += bring;

            // 判断土豪
            if (richerBring <= bring) {
                richerBring = bring;
                richer = userId;
            }

            // 判断鲨鱼
            if (sharkWin <= pl) {
                sharkWin = pl;
                shark = userId;
            }

            // 判断大鱼
            if (fishLoss >= pl) {
                fishLoss = pl;
                fish = userId;
            }
        }

        for(Integer cid : clubMap.keySet()) { // 设置每个社区的战绩的服务费值
            clubFeeArray.add(null == room.getFeeService() ? 0 : room.getFeeService().clubFee(cid,tempRoomFee));
        }

        for(int trbieId: tribeIdArray){ // 设置每个联盟的战绩的服务费值
            tribeFeeArray.add(null == room.getFeeService() ? 0 : room.getFeeService().tribeFee(trbieId,tempRoomFee));
        }

        if (!records.isEmpty()) {
            //honorProcedure.calculate();
            MongoClient mongo = null;

            try {
                mongo = MongodbService.getMongoInstance();
                MongoDatabase database = mongo.getDatabase(db);
                MongoCollection<Document> detailCollection = database.getCollection(_detailCollection);
                MongoCollection<Document> recordCollection = database.getCollection(_recordCollection);
                
                String userIdStr = userStr.toString();
                if (!includeOwnerId) userIdStr += ("," + room.getOwner());

                UserInfoDao userInfoDao = new UserInfoDaoImp();
                Map<Integer, Object[]> userInfo = userInfoDao.getUserHeadNickname(userIdStr);

                StringBuilder userIconBuilder = new StringBuilder();
                StringBuilder userNickBuilder = new StringBuilder();
                Object[] richerUser = userInfo.get(richer);
                Object[] sharkUser = userInfo.get(shark);
                Object[] fishUser = userInfo.get(fish);
                 logger.debug("rich head:" + richerUser[0]);
                 logger.debug("rich nickname:" + richerUser[1]);
                 logger.debug("shark head:" + sharkUser[0]);
                 logger.debug("shark nickname:" + sharkUser[1]);
                 logger.debug("fish user:" + fishUser[0]);
                 logger.debug("fish user:" + fishUser[1]);
                userIconBuilder.append(richerUser[0]).append(sp).append(sharkUser[0]).append(sp).append(fishUser[0]);
                userNickBuilder.append(richerUser[1]).append(sp).append(sharkUser[1]).append(sp).append(fishUser[1]);
                String userIcon = userIconBuilder.toString();
                String userNick = userNickBuilder.toString();

                StringBuilder allNickBuilder = new StringBuilder();
                StringBuilder headBuilder = new StringBuilder();
                for (Integer id : userIdArray) {
                    Object[] userObj = userInfo.get(id);
                    allNickBuilder.append(userObj[1]).append(split);
                    headBuilder.append(userObj[0].toString()).append(split);
                }
                Object[] ownerUser = userInfo.get(room.getOwner());
                String ownerHead = ownerUser[0].toString();
                String orgOwnerName = ownerUser[1].toString();

                String userNickName = allNickBuilder.toString();
                userNickName = userNickName.substring(0, userNickName.length() - split.length());
                String headStr = headBuilder.toString();
                headStr = headStr.substring(0, headStr.length() - split.length());

                if (room.getStage() > 0) {
                    // 战绩数据保存 不包括统计数据
                    int roomStatus = 0;
                    long endTime = System.currentTimeMillis();

                    // 牌局详情记录
                    Document detailRecord = new Document();
                    detailRecord.put("id", new ObjectId(room.getRoomUUID()));
                    detailRecord.put("room_id", room.getRoomId());
                    detailRecord.put("room_name", room.getName());
                    detailRecord.put("room_path", room.getRoomPath());
                    detailRecord.put("time", room.getGameBeginTime());
                    detailRecord.put("end_time", endTime);
                    detailRecord.put("owner_id", room.getOwner());
                    detailRecord.put("user_nick", userNick);                           // 3个玩家的昵称（鲨鱼、土豪、鱼）
                    detailRecord.put("user_icon", userIcon);
                    detailRecord.put("user_head", headStr);
                    detailRecord.put("user_id_array", userIdArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
                    detailRecord.put("pl_array", plArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
                    detailRecord.put("hand_array", handArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
                    detailRecord.put("bring_array", bringArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
                    detailRecord.put("all_bring", allBring);
                    detailRecord.put("user_nick_name", userNickName);                 // 所有玩家
                    detailRecord.put("max_pot", maxPot);
                    detailRecord.put("creator", room.getOwner());
                    detailRecord.put("total_hand", room.getStage());
                    detailRecord.put("qianzhu", room.getQianzhu());                   // 前注
                    detailRecord.put("insurance", room.getInsurance());               // 保险
                    detailRecord.put("blind", String.valueOf(room.getManzhu()) + "/" + String.valueOf(room.getDamanzhu())); // 盲注
                    if (room.getInsurance() == 1) {
                        detailRecord.put("insurance_pool", room.getInsuranceChip());  // 保险收支
                    }

                    logger.debug("clubId=" + clubIdArray);
                    detailRecord.put("club_id", clubIdArray.toString().replace("[", "").replace("]", "").replace(" ", "")); // 社区id（重复的）
                    detailRecord.put("club_name",clubNameArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
                    detailRecord.put("tribe_id", tribeIdArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
                    detailRecord.put("tribe_id_repeat", tribeIdRepeatArray.toString().replace("[", "").replace("]", "").replace(" ", ""));

                    /** 服务费 */
                    detailRecord.put("jp_pl_fee_array", jpPlFeeArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
                    detailRecord.put("pl_fee_array", plFeeArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
                    detailRecord.put("commission_club", clubFeeArray.toString().replace("[", "").replace("]", "").replace(" ", ""));                 // 社区id（重复的）
                    detailRecord.put("commission_tribe", tribeFeeArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
                    detailRecord.put("commission_sys", null== room.getFeeService()?0:room.getFeeService().sysFee(tempRoomFee));
                    detailRecord.put("commission_income", null== room.getFeeService()?0:room.getFeeService().feeIncome(tempRoomFee));

                    List<Integer> clubIdlist = new ArrayList<>(clubMap.keySet());    // 社区id（去重后的）
                    List<Integer> clubPLlist = new ArrayList<>(clubMap.values());

                    List<Integer> clubIds = new ArrayList<>();
                    List<Integer> clubPLs = new ArrayList<>();
                    for (int i = 0; i < clubIdlist.size(); i++) {
                        clubIds.add(clubIdlist.get(i));
                        clubPLs.add(clubPLlist.get(i));
                    }

                    logger.debug("club_id_no_repeat=clubIds===" + clubIds);
                    logger.debug("club_id_no_repeat=clubPLs===" + clubPLs);
                    detailRecord.put("club_id_no_repeat", arrayToString(clubIds));     // 社区id
                    detailRecord.put("club_pl", arrayToString(clubPLs));               // 社区盈亏
                    detailRecord.put("all_insurance", arrayToString(insuranceArray));           // 所有个人保险
                    logger.debug("clubIdArray====" + arrayToString(clubIdArray) + "insuranceArray==" + arrayToString(insuranceArray));

                    detailRecord.put("jackpot",room.isJackPot() ? 1 : 0);//房间是否开启jackpot
                    detailRecord.put("jp_array", jpArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
                    detailRecord.put("jp_real_array", jpRealArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
                    detailRecord.put("jp_pl_array", jpPlArray.toString().replace("[", "").replace("]", "").replace(" ", ""));
                    StringBuilder jpUserIds = new StringBuilder();
                    StringBuilder jpProcerType = new StringBuilder();
                    StringBuilder jpHands = new StringBuilder();
                    StringBuilder jpReward = new StringBuilder();
                    StringBuilder jpRewardFee = new StringBuilder();
                    if(null != room.getJackpotService()) {
                        List<PlayerJpRewardDetail> list = room.getJackpotService().playerRewardDetailLst();
                        if (null != list && !list.isEmpty()) {
                            for (PlayerJpRewardDetail reward : list) {
                                if(jpUserIds.length() ==  0){
                                    jpUserIds.append(reward.getUserId());
                                    jpProcerType.append(reward.getProcerType());
                                    jpHands.append(reward.getHandNo());
                                    jpReward.append(reward.getReward());
                                    jpRewardFee.append(reward.getRewardFee());
                                }else{
                                    jpUserIds.append(",").append(reward.getUserId());
                                    jpProcerType.append(",").append(reward.getProcerType());
                                    jpHands.append(",").append(reward.getHandNo());
                                    jpReward.append(",").append(reward.getReward());
                                    jpRewardFee.append(",").append(reward.getRewardFee());
                                }
                            }
                        }
                    }
                    detailRecord.put("jp_user_array", jpUserIds.toString());
                    detailRecord.put("jp_poker_type", jpProcerType.toString());
                    detailRecord.put("jp_hand_array", jpHands.toString());
                    detailRecord.put("jp_reward_array", jpReward.toString());
                    detailRecord.put("jp_reward_fee_array", jpRewardFee.toString());

//                    //荣耀积分标签
//                    List<String> honorLables = new ArrayList<>(userIdArray.size());
//                    //存储积分
//                    List<Integer> honorNums = new ArrayList<>(userIdArray.size());
//                    if(room.getTribeId() > 0) {
//                        List<HonorPlayer> allHonorPlayers = honorProcedure.getHonorPlayers();
//                        for (int i = 0; i < userIdArray.size(); i++) {
//                            Integer userIdTemp = userIdArray.get(i);
//                            for (int j = 0; j < allHonorPlayers.size(); j++) {
//                                HonorPlayer temp = allHonorPlayers.get(j);
//                                if (userIdTemp.intValue() == temp.getUserId()) {
//                                    honorNums.add(i, temp.getTotalHonorNum());
//                                    List<Integer> lables = temp.getHonorLabels().stream().map(HonorLabel::getLabelId).collect(Collectors.toList());
//                                    honorLables.add(i, StringUtil.listToString(lables,sp));
//                                }
//                            }
//                        }
//                    }

                    //不管是不是同盟局都要记录标签信息，但不计算荣耀分
                    //detailRecord.put("honor_labels", StringUtil.listJoinString(honorLables,split));
                    //同盟局的话战绩表增加荣耀分信息

//                    if (room.getTribeId() > 0) {
//                        detailRecord.put("honor_nums", StringUtil.listToString(honorNums,split));
//
//                        StringBuffer stringBuffer = new StringBuffer();
//                        stringBuffer.append(honorProcedure.getAllWinnerHonorNum(honorProcedure.getWinners())).append(sp);
//                        stringBuffer.append(honorProcedure.getMvpHonorNum(honorProcedure.getMvp())).append(sp);
//                        stringBuffer.append(honorProcedure.getWorkerBeeHonorNum(honorProcedure.getWorkerBee())).append(sp);
//                        stringBuffer.append(honorProcedure.getFishHonorNum(honorProcedure.getFish())).append(sp);
//                        stringBuffer.append(honorProcedure.getAllLoserHonorNum(honorProcedure.getLoserHonorPlayers())).append(sp);
//                        stringBuffer.append(honorProcedure.getTotalPositiveReturnHonor());
//
//                        detailRecord.put("honor_earnings", StringUtil.listToString(honorNums,split));
//
//                    }


                    detailCollection.insertOne(detailRecord);
                    String roomId = detailRecord.getObjectId("_id").toString();

                    // 房间记录
                    List<Document> docList = new ArrayList<Document>();

                    for (Object[] obj : records) {
                        int userId = (Integer) obj[0];
                        int pl = (Integer) obj[1];

                        if (userIdArray.contains(userId)) {
                            Document roomRecord = new Document();
                            roomRecord.put("user_id", userId);
                            roomRecord.put("room_id", roomId);
                            roomRecord.put("room_name", room.getName());
                            roomRecord.put("blind", String.valueOf(room.getManzhu()) + "/" + String.valueOf(room.getDamanzhu()));
                            roomRecord.put("max_play_time", room.getMaxPlayTime());
                            roomRecord.put("time", room.getGameBeginTime());
                            roomRecord.put("end_time", endTime);
                            roomRecord.put("profit_lose", pl);
                            roomRecord.put("owner_name", orgOwnerName);
                            roomRecord.put("owner_head", ownerHead);
                            roomRecord.put("qianzhu", room.getQianzhu());               // 前注
                            roomRecord.put("insurance", room.getInsurance());           // 保险
                            roomRecord.put("status", roomStatus);                       // 牌局状态：0 未结束，1 已结束

                            //*********jackpot*********//
                            roomRecord.put("jackpot", null==room.getJackpotService()?0: room.getJackpotService().getTotalJpBet(userId));//玩家在当前牌局向彩池贡献总额
                            roomRecord.put("jackpot_real", null==room.getJackpotService()?0: room.getJackpotService().getTotalRealJpBet(userId));//玩家在当前牌局实际向彩池贡献总额
                            roomRecord.put("jackpot_pl", null==room.getJackpotService()?0: room.getJackpotService().getTotalJpReward(userId));//玩家在当前牌局的 JackPot 中奖额
                            roomRecord.put("jackpot_pl_fee", null==room.getJackpotService()?0: room.getJackpotService().getTotalJpRewardFee(userId));//玩家在当前牌局的 JackPot 中奖额的服务费

                            int type = (Integer) obj[4];
                            String clubName = (String) obj[5];
                            int clubId = (Integer) obj[6];

                            roomRecord.put("type", type);
                            roomRecord.put("club_name", clubName);
                            roomRecord.put("club_id", clubId);

                            docList.add(roomRecord);
//                            recordCollection.findOneAndUpdate(
//                                    new BasicDBObject("user_id", userId).append("room_id", roomId),
//                                    new Document("$set", roomRecord),
//                                    new FindOneAndUpdateOptions().upsert(true)
//                            );
                        }
                    }
                    recordCollection.insertMany(docList);
                }
            } catch (SQLException e) {
                logger.error("generate game report data error", e);
            } catch (Exception e) {
                logger.error("game report exception: ", e);
            } finally {
                MongodbService.close(mongo);
            }
        } else {
            logger.error("game report data is empty");
        }
    }

    /**
     * 数组转字符串
     *
     * @return
     */
    private static String arrayToString(List<Integer> ints) {
        StringBuilder res = new StringBuilder();
        for (int i = 0; i < ints.size(); i++) {
//            logger.info("arrayToString:" + i + "==" + ints.get(i));
            res.append(ints.get(i));
            if (i != ints.size() - 1) {
                res.append(",");
            }
        }

        return res.toString();
    }
}