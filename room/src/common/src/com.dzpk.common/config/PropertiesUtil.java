
package com.dzpk.common.config;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Properties;

import com.dzpk.common.utils.LogUtil;
import org.apache.logging.log4j.Logger;

/**
 * 读取配置文件
 */
public class PropertiesUtil {

    private static Logger logger = LogUtil.getLogger(PropertiesUtil.class);

	private static PropertiesUtil por;

	public static PropertiesUtil getSingleton() {
		return por == null ? new PropertiesUtil() : por;
	}

	public Properties initProperties(String propertiesName) {
    	Properties port = new Properties();
    	FileInputStream is = null;
        try {
            is = new FileInputStream(propertiesName);
            port.load(new InputStreamReader(is, "utf-8"));
            logger.info(propertiesName + " is loaded : ");
        } catch (Exception ex) {
            logger.error(ex.getMessage());
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
            } catch (IOException ex) {
                logger.error(" Fail to close InputStream ");
            }
        }
        return port;
	} 
}

