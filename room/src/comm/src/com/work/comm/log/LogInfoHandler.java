package com.work.comm.log;

import com.dzpk.db.model.UserInfo;
import com.dzpk.processor.IProcessor;
import com.dzpk.processor.impl.*;
import com.dzpk.work.Task;
import com.i366.util.IPUtil;
import com.work.comm.client.protocal.Request;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.ThreadContext;

import java.util.*;


/**
 * 日志信息工具类
 * <AUTHOR>
 */
public class LogInfoHandler {

    private static final Logger logger = LogManager.getLogger(LogInfoHandler.class);

    /**
     * 设置日志信息
     * @param task 任务
     */
    public static void setInfo(Task task) {
        try {
            Request request = (Request) task.getRequest();
            if (request == null) {
                // 如果request为空，则直接从task中获取UserId如果没有则没有
                Integer userId = task.getUserId();
                if (userId != null) {
                    setInfo(userId, "");
                }
            } else {
                setInfo(request);
            }
        } catch (Exception e) {
            logger.error("setInfo(Task) error: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 设置日志信息
     * @param request 请求
     */
    public static void setInfo(Request request) {
        // 获取IP
        setInfo(request.getUserId(), IPUtil.getRealUserIp(request));
    }

    private static void setInfo(int userId, String ip) {
        /*
         1. timestamp: 时间戳 auto
         2. level: 日志级别 auto
         3. service: 服务ID/名称 log4j2-pattern.json
         4. threadName: 线程名称 auto
         5. threadId: 线程ID auto
         6. className: 类名 auto
         7. methodName: 函数名 auto
         8. uid: 用户自增ID get from request
         9. userid: 用户编号 get from request or database
         10. userType: 用户类型 get from request or database
         11. ip: IP地址 get from request
         12. message: 日志内容 auto
         13. error: 异常信息 auto
         14. errorStack: 异常堆栈 auto
         */
        try {
            ThreadContext.put("ip", ip);
            // 获取用户ID，uid=userId，userid=random_num
            ThreadContext.put("uid", String.valueOf(userId));
            // 查询用户信息
            UserInfo userInfo = LogInfoCache.getUserInfo(userId);
            if (userInfo != null) {
                ThreadContext.put("userType", String.valueOf(userInfo.getUserType()));
                ThreadContext.put("userid", userInfo.getRandomNum());
            }
        } catch (Exception e) {
            logger.error("setInfo(Request) error: {}", e.getMessage(), e);
        }
    }

    /**
     * 清除
     */
    public static void clearInfo() {
        // clear MDC
        ThreadContext.clearMap();
    }


}
