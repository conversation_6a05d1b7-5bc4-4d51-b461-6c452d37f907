package com.work.comm.log;


import com.ai.dz.config.AiRuleTemplate;
import com.dzpk.db.imp.UserInfoDaoImp;
import com.dzpk.db.model.UserInfo;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * LogInfoCache
 *
 * <AUTHOR>
 * @date 2024/11/18
 */
public class LogInfoCache {

    // 用户缓存
    private static final Map<Integer, UserInfo> USER_CACHE = new ConcurrentHashMap<>();
    // 用户最后活跃时间
    private static final Map<Integer, Long> USER_LAST_ACTIVE_TIME = new ConcurrentHashMap<>();

    // 失效时间, 默认30分钟
    private static final long EXPIRATION_TIME = 30 * 60 * 1000;
    private static final ScheduledExecutorService SCHEDULER = Executors.newScheduledThreadPool(1);

    static {
        // 定时清理过期缓存
        SCHEDULER.scheduleAtFixedRate(LogInfoCache::updateCache, 0, EXPIRATION_TIME, TimeUnit.MILLISECONDS);
    }


    // 根据userId获取用户信息
    public static UserInfo getUserInfo(int userId) {
        USER_LAST_ACTIVE_TIME.put(userId, System.currentTimeMillis());
        return USER_CACHE.computeIfAbsent(userId, id -> {
            UserInfo userInfo = fetchUserInfoFromDatabase(id);
            userInfo.setUserType(AiRuleTemplate.getUserType(id));
            return userInfo;
        });
    }

    // 从数据库获取用户信息
    private static UserInfo fetchUserInfoFromDatabase(int userId) {
        return findUserInfoById(userId);
    }

    private static void updateCache() {
        long currentTime = System.currentTimeMillis();
        USER_LAST_ACTIVE_TIME.entrySet().removeIf(entry -> currentTime - entry.getValue() > EXPIRATION_TIME);
        USER_CACHE.keySet().removeIf(userId -> !USER_LAST_ACTIVE_TIME.containsKey(userId));
    }

    private static final Logger logger = LogManager.getLogger(LogInfoCache.class);

    // 查询用户信息
    private static UserInfo findUserInfoById(int userId) {
        try {
            return new UserInfoDaoImp().getUserInfo(userId);
        } catch (Exception e) {
            logger.error("findUserInfoById error uid = {}", userId, e);
            return null;
        }
    }

}
