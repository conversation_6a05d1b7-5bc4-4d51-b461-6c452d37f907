/**
 * $RCSfile: ServiceRequest.java,v $
 * $Revision: 1.1  $
 * $Date: 2012-8-9  $
 * <p/>
 * Copyright (C) 2005 Bettem, Inc. All rights reserved.
 * <p/>
 * This software is the proprietary information of Bettem, Inc.
 * Use is subject to license terms.
 */
package com.work.comm.server.protocal;

import com.work.comm.client.protocal.BaseRequest;
import com.work.comm.client.pack.Functions;
import io.netty.channel.Channel;

/**
 * 内部通讯协议封装
 * <p>Title: ServiceRequest</p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2006</p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public class ServiceRequest extends BaseRequest {

    // Package data
    private int protocolVersion;        //  协议版本
    private int languageId;             //  语言版本
    private String serverVersion;       //  服务器版本号如：1.1
    private int clientBuildLanguagId;   //  使用的语言版本
    private int clientBuildNumber;      //  暂时没用
    private int serverNumber;           //  服务器编号
    private int productId;              //  产品唯一标识
    private int packSzie;               //  协议大小

    public int getProtocolVersion() {
        return protocolVersion;
    }

    public void setProtocolVersion(int protocolVersion) {
        this.protocolVersion = protocolVersion;
    }

    public int getLanguageId() {
        return languageId;
    }

    public void setLanguageId(int languageId) {
        this.languageId = languageId;
    }

    public String getServerVersion() {
        return serverVersion;
    }

    public void setServerVersion(String serverVersion) {
        this.serverVersion = serverVersion;
    }

    public int getClientBuildVersion() {
        return clientBuildLanguagId;
    }

    public void setClientBuildVersion(int clientBuildLanguagId) {
        this.clientBuildLanguagId = clientBuildLanguagId;
    }

    public int getClientBuildNumber() {
        return clientBuildNumber;
    }

    public void setClientBuildNumber(int clientBuildNumber) {
        this.clientBuildNumber = clientBuildNumber;
    }

    public int getServerNumber() {
        return serverNumber;
    }

    public void setServerNumber(int serverNumber) {
        this.serverNumber = serverNumber;
    }

    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    public int getPackSzie() {
        return packSzie;
    }

    public void setPackSzie(int packSzie) {
        this.packSzie = packSzie;
    }

    public boolean init(Channel channel, byte[] buffer) {
        this.channel = channel;
        this.bt = buffer;
        return parse(bt);
    }

    public static int getPackageSize(byte[] bt) {
        if (bt == null || bt.length < ServiceProtocal.RP_SIZE_HIGH + 2) {
            return -1;
        }
        return Functions.byteArrayToShortInt(bt, ServiceProtocal.RP_SIZE_HIGH);
    }

    /**
     * 解析协议头
     *
     * @param bt2
     * @return
     */
    protected boolean parse(byte[] bt2) {

        if (bt2 == null || bt2.length < 17) {
            return false;
        }
        if (bt2[0] != ServiceProtocal.HEADER_INDICATER_0 || bt2[1] != ServiceProtocal.HEADER_INDICATER_1
                || bt2[2] != ServiceProtocal.HEADER_INDICATER_2 || bt2[3] != ServiceProtocal.HEADER_INDICATER_3) {
            return false;
        }
        this.protocolVersion = bt2[ServiceProtocal.RP_PROTOCOL_VERSION] & 0xff;
        this.languageId = bt2[ServiceProtocal.RP_LANGUGAD] & 0xff;
        this.serverVersion = (bt2[ServiceProtocal.RP_MAIN_VERSION] & 0xff) + "."
                + (bt2[ServiceProtocal.RP_MINOR_VERSION] & 0xff);
        this.clientBuildLanguagId = bt2[ServiceProtocal.RP_CLIENT_BUILD_LANGUGAD] & 0xff;
        this.clientBuildNumber = bt2[ServiceProtocal.RP_CLIENT_BUILD_NUMBER] & 0xff;
        this.serverNumber = Functions.byteArrayToInt(bt2, ServiceProtocal.RP_SERVER_NUMBER_1);
        this.productId = Functions.byteArrayToShortInt(bt2, ServiceProtocal.RP_PRODUCT_ID_HIGH);
        this.requestCode = Functions.byteArrayToShortInt(bt2, ServiceProtocal.RP_REQUEST_CODE_HIGH);
        this.packSzie = Functions.byteArrayToShortInt(bt2, ServiceProtocal.RP_SIZE_HIGH);
        return true;
    }

}
