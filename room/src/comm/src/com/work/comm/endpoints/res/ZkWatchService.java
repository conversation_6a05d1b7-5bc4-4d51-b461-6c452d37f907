package com.work.comm.endpoints.res;

import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.recipes.cache.TreeCache;
import org.apache.curator.framework.recipes.cache.TreeCacheEvent;
import org.apache.curator.framework.recipes.cache.TreeCacheListener;

import java.nio.charset.Charset;

/**
 * Res服务节点管理
 * 对外访问的IP/PORT  ：一般很少变
 * 对内访问的IP/PORT  ：一般很少变
 * 当前分配的玩家数量  : 初始化时从数据库表中统计设置到zk
 *   分配时  : +1
 *   踢出时  ：-1
 * 当前APP到Res服务的连接数量: 初始化时为0
 *   建立连接时 ： +1
 *   关闭连接时 ： -1
 */
@Slf4j
public class ZkWatchService {
    /**
     * 所有Res服务在一个类型标识的节点下，对应的值为空
     * - %s Res服务标识符的占位符
     * 每个Res服务对应一个节点，对应的值为空
     * - %s Res服务ID的占位符
     * 其下的结构是：
     * - /out    : 对应的值是外部访问的ip:port
     * - /in     : 对应的值是内部访问的ip:port
     * - /pu     : 对应的值是当前Res服务的已分配玩家数量
     * - /cu     : 对应的值是当前Res服务的已建立的连接数量
     */
    private static final String SERVER_TYPE_PATH = "/dzpk/roomsvr/%s";
    private static final String SERVER_PU_FLAG = "pu";
    private static final String SERVER_CU_FLAG = "cu";
    private static final String SERVER_OUTACS_FLAG = "out";
    private static final String SERVER_INACS_FLAG = "in";

    /** zookeeper客户端 */
    private CuratorFramework curatorFramework;

    /** 字符集 */
    private Charset defaultCharset;

    /** 对应的Res服务类型 */
    private String serverType;
    private ResNodeChgCallback callback;

    /** 本服务的节点路径 */
    private String typePath=null;     // 标识Res服务的节点路径
    private TreeCache typePathCache=null; // 所有Res服务的根节点，监控此节点

    public ZkWatchService(String serverType,
                          ResNodeChgCallback callback,
                          Charset defaultCharset,
                          CuratorFramework curatorFramework){
        this.serverType = serverType;
        this.callback = callback;
        this.curatorFramework = curatorFramework;
        this.defaultCharset = null == defaultCharset? Charset.forName("UTF-8"):defaultCharset;

        this.initialize();
    }

    public void initialize() {
        StringBuilder traceLog = null;
        if(log.isDebugEnabled()){
            traceLog = new StringBuilder(String.format("初始化Res服务ZK节点：type=%s",this.serverType));
        }
        Exception throwEx = null;
        try {
            // 初始化节点路径
            this.typePath = String.format(SERVER_TYPE_PATH, this.serverType);
            if(null != traceLog){
                traceLog.append(String.format("%s typePath=%s",
                        System.lineSeparator(),this.typePath));
            }

            //Room服务类的节点Cache
            this.typePathCache = new TreeCache(this.curatorFramework, this.typePath);
            this.typePathCache.getListenable().addListener(new ChildrenNodeListener(this));
            this.startTypeNodeCache();
            if(null != traceLog){
                traceLog.append(String.format("%s Room服务节点Cache启动成功：%s",
                        System.lineSeparator(),this.typePath));
            }
        }catch (Exception ex){
            throwEx = ex;
            throw ex;
        }finally {
            if(throwEx == null){
                if(null != traceLog){
                    traceLog.append(String.format("%s 初始化成功！",
                            System.lineSeparator()));
                    log.debug(traceLog.toString());
                }
            }else{
                if(null != traceLog){
                    traceLog.append(String.format("%s 初始化失败：%s",
                            System.lineSeparator(),throwEx.getMessage()));
                    log.debug(traceLog.toString(),throwEx);
                }
            }
        }
    }

    public void destroy(){
        this.stopTypeNodeCache();
    }

    /**
     * 启动Res服务节点监控
     */
    private void startTypeNodeCache(){
        try {
            this.typePathCache.start();
        }catch (Exception ex){
            throw new RuntimeException(String.format("启动Res服务节点Cache[ %s ]失败：%s",
                    this.typePath,ex.getMessage()),ex);
        }
    }
    /**
     * 关闭Res服务节点监控
     */
    private void stopTypeNodeCache(){
        if(null == this.typePathCache)
            return ;

        try{
            this.typePathCache.close();
        }catch (Exception ex){
            log.error(String.format("关闭Res服务节点Cache[ %s ]失败：%s",
                    this.typePath,ex.getMessage()),ex);
        }
    }

    private byte[] serializeInt(int value){
        return String.valueOf(value).getBytes(this.defaultCharset);
    }
    private int deserialize2Int(byte[] data){
        if(null == data || data.length<=0)
            return 0;

        return Integer.parseInt(new String(data,this.defaultCharset));
    }
    private byte[] serializeString(String value){
        return value.getBytes(this.defaultCharset);
    }
    private String deserialize2String(byte[] data){
        if(null == data || data.length<=0)
            return null;

        return new String(data,this.defaultCharset);
    }

    /** 节点变化事件处理 */
    private void nodeUpdated(String chgPath){
        StringBuilder traceLog = null;
        if(log.isDebugEnabled()){
            traceLog = new StringBuilder("Res服务节点变更："+chgPath);
        }
        Throwable throwEx = null;
        try {
            // 校验是否pu节点
            // 路径以pu结尾
            if (null == chgPath || "".equals(chgPath.trim())) {
                if (null != traceLog)
                    traceLog.append(String.format("%s   节点路径为空 -> 忽略！", System.lineSeparator()));
                return;
            }
            chgPath = chgPath.trim();
            if (!chgPath.endsWith(SERVER_PU_FLAG)) {
                if(null != traceLog)
                    traceLog.append(String.format("%s   非pu节点 -> 忽略！",System.lineSeparator()));
                return;
            }

            // 移除pu
            // 以/结尾
            String servicePath = chgPath.replace(SERVER_PU_FLAG, "");
            if (null != traceLog)
                traceLog.append(String.format("%s   service-path=%s", System.lineSeparator(),servicePath));

            // 解析出serverId
            // 格式：/dzpk/roomsvr/res/20010001/pu
            // 20010001是serverId
            String[] pathFieldArr = servicePath.split("/");
            String serverId = pathFieldArr[pathFieldArr.length - 1];
            if (null != traceLog)
                traceLog.append(String.format("%s   server-id=%s", System.lineSeparator(),serverId));

            // 获取对外访问IP:PORT
            // 获取对内访问IP:PORT
            // 获取当前连接数
            String path = String.format("%s%s", servicePath, SERVER_OUTACS_FLAG);
            String outIpPort = this.deserialize2String(this.typePathCache.getCurrentData(path).getData());
            if (null != traceLog)
                traceLog.append(String.format("%s   outIpPort-path=%s , value=%s", System.lineSeparator(),path,outIpPort));

            path = String.format("%s%s", servicePath, SERVER_INACS_FLAG);
            String inIpPort = this.deserialize2String(this.typePathCache.getCurrentData(path).getData());
            if (null != traceLog)
                traceLog.append(String.format("%s   inIpPort-path=%s , value=%s", System.lineSeparator(),path,inIpPort));

            path = String.format("%s%s", servicePath, SERVER_PU_FLAG);
            int pu = this.deserialize2Int(this.typePathCache.getCurrentData(path).getData());
            if (null != traceLog)
                traceLog.append(String.format("%s   pu-path=%s , value=%s", System.lineSeparator(),path,pu));

            path = String.format("%s%s", servicePath, SERVER_CU_FLAG);
            int cu = this.deserialize2Int(this.typePathCache.getCurrentData(path).getData());
            if (null != traceLog)
                traceLog.append(String.format("%s   cu-path=%s , value=%s", System.lineSeparator(),path,cu));

            String[] ipPortArr = outIpPort.split(":");
            String outIp = ipPortArr[0];
            int outPort = Integer.parseInt(ipPortArr[1]);
            ipPortArr = inIpPort.split(":");
            String inIp = ipPortArr[0];
            int inPort = Integer.parseInt(ipPortArr[1]);

            ZkResServerNode node = new ZkResServerNode();
            node.setServerId(serverId);
            node.setOutAccessIp(outIp);
            node.setOutAccessPort(outPort);
            node.setInAccessIp(inIp);
            node.setInAccessPort(inPort);
            node.setClientConnNum(cu);
            node.setAssignedNum(pu);
            if (null != traceLog)
                traceLog.append(String.format("%s   %s", System.lineSeparator(),node.toString()));

            this.callback.updateNode(node);
            if (null != traceLog)
                traceLog.append(String.format("%s   处理成功！", System.lineSeparator()));
        }catch (Exception ex){
            throwEx = ex;
            if (null != traceLog)
                traceLog.append(String.format("%s   处理失败：%s", System.lineSeparator(),ex.getMessage()));
        }finally {
            if(null == throwEx){
                if(null != traceLog)
                    log.info(traceLog.toString());
            }else{
                if(null != traceLog)
                    log.warn(traceLog.toString(),throwEx);
                else
                    log.warn("",throwEx);
            }
        }
    }
    private void nodeRemoved(String chgPath){
        StringBuilder traceLog = null;
        if(log.isDebugEnabled()){
            traceLog = new StringBuilder("Res服务节点移除："+chgPath);
        }
        Throwable throwEx = null;
        try {
            // 校验是否pu节点
            // 路径以pu结尾
            if (null == chgPath || "".equals(chgPath.trim())) {
                if (null != traceLog)
                    traceLog.append(String.format("%s   节点路径为空 -> 忽略！", System.lineSeparator()));
                return;
            }
            chgPath = chgPath.trim();
            if (!chgPath.endsWith(SERVER_PU_FLAG)) {
                if(null != traceLog)
                    traceLog.append(String.format("%s   非pu节点 -> 忽略！",System.lineSeparator()));
                return;
            }

            // 移除pu
            // 以/结尾
            String servicePath = chgPath.replace(SERVER_PU_FLAG, "");
            if (null != traceLog)
                traceLog.append(String.format("%s   service-path=%s", System.lineSeparator(),servicePath));

            // 解析出serverId
            // 格式：/dzpk/roomsvr/res/20010001/pu
            // 20010001是serverId
            String[] pathFieldArr = servicePath.split("/");
            String serverId = pathFieldArr[pathFieldArr.length - 1];
            if (null != traceLog)
                traceLog.append(String.format("%s   server-id=%s", System.lineSeparator(),serverId));

            this.callback.removeNode(serverId);
            if (null != traceLog)
                traceLog.append(String.format("%s   处理成功！", System.lineSeparator()));
        }catch (Exception ex){
            throwEx = ex;
            if (null != traceLog)
                traceLog.append(String.format("%s   处理失败：%s", System.lineSeparator(),ex.getMessage()));
        }finally {
            if(null == throwEx){
                if(null != traceLog)
                    log.info(traceLog.toString());
            }else{
                if(null != traceLog)
                    log.warn(traceLog.toString(),throwEx);
                else
                    log.warn("",throwEx);
            }
        }
    }

    /**
     * Res服务节点的事件监听器
     * Res服务中只需要关注连接重连的时候
     * 重新创建服务节点,其它事件无需考虑。
     */
    @Slf4j
    private static class ChildrenNodeListener implements TreeCacheListener {
        private ZkWatchService watchService;

        public ChildrenNodeListener(ZkWatchService watchService){
            this.watchService = watchService;
        }

        /**
         * 监控Res服务节点下的每个pu节点
         * 每个Res服务的pu节点的增加/修改/删除事件
         * 才能准确反映Res服务是否有效
         *
         * 每个Res服务的pu节点的增加/修改事件 -> 新增节点
         * 每个Res服务的pu节点的删除事件     ->  移除节点
         *
         * @param client
         * @param event
         * @throws Exception
         */
        public void childEvent(CuratorFramework client, TreeCacheEvent event) throws Exception{
            if(event.getType() == TreeCacheEvent.Type.NODE_ADDED ||
                    event.getType() == TreeCacheEvent.Type.NODE_UPDATED){
                this.watchService.nodeUpdated(event.getData().getPath());
            }else if(event.getType() == TreeCacheEvent.Type.NODE_REMOVED){
                this.watchService.nodeRemoved(event.getData().getPath());
            }
        }
    }
}
