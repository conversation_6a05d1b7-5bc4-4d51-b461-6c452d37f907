package com.work.comm.endpoints.res;

import com.dzpk.component.zk.ZkUtil;
import com.work.comm.s2s.callback.IChannelAuthCallback;
import com.work.comm.s2s.client.ServerManager;
import com.work.comm.s2s.common.ES2ServerType;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.util.concurrent.GenericFutureListener;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class ResServerManager implements IChannelAuthCallback,ResNodeChgCallback {
    /** 单例 */
    private static ResServerManager INSTANCE = new ResServerManager();
    public static ResServerManager getInstance(){
        return INSTANCE;
    }
    public static void initialize() {
        INSTANCE.setAppType(ServerManager.getInstance().getAppType());
        INSTANCE.setAppId(ServerManager.getInstance().getAppId());
        ServerManager.getInstance().subscribe(INSTANCE.authCallback());
    }
    public void destroy(){
        if(null != this.watchService)
            this.watchService.destroy();
    }
    private ResServerManager(){
        this.watchService = new ZkWatchService(ES2ServerType.res.name(),
                this,null, ZkUtil.getClient());
    }
    private IChannelAuthCallback authCallback(){
        return this;
    }

    /**
     * 发送数据包
     * 由上层业务调用
     *
     * @param serverId  对端App的标识符，类型内唯一，可选
     *                  当不指定时，则是向此类型的消息发送
     * @param sendAll   是否向所有服务器发送
     *                  true  : 是
     *                  false : 否
     * @param data       待发送的消息,必填
     *
     * @throws Exception
     *    IllegalArgumentException 参数无效
     */
    public void sendData(String serverId,boolean sendAll, byte[] data) throws Exception{
        if(null == serverId)
            serverId="";
        else
            serverId = serverId.trim();

        ServerManager.getInstance().sendDataBy(ES2ServerType.res.name(),serverId,sendAll,data);
    }

    /**
     * 发送数据包
     * 由上层业务调用
     *
     * @param serverId  对端App的标识符，类型内唯一，可选
     *                  当不指定时，则是向此类型的消息发送
     * @param sendAll   是否向所有服务器发送
     *                  true  : 是
     *                  false : 否
     * @param data       待发送的消息,必填
     *
     * @throws Exception
     *    IllegalArgumentException 参数无效
     */
    public void sendDataByExclude(String serverId,boolean sendAll, byte[] data) throws Exception{
        String[] serverIds;
        if(null == serverId || "".equals(serverId.trim()))
            serverIds = new String[0];
        else {
            serverId = serverId.trim();
            serverIds = new String[1];
            serverIds[0] = serverId;
        }

        ServerManager.getInstance().sendDataBy(ES2ServerType.res.name(),serverIds,sendAll,data);
    }

    /** Res服务的zk节点 */
    private ZkWatchService watchService;

    /** 所在应用标识 */
    private String appType;
    private String appId;

    /**
     * res服务节点集合
     * appId -> ServerNode
     */
    private Map<String,ServerNode> serverNodeMap = new ConcurrentHashMap<>();
    /** Res服务节点变更事件 */
    /**
     * 新增/更新res服务节点
     *
     * @param serverNode  待更新的节点
     */
    @Override
    public void updateNode(ZkResServerNode serverNode){
        if(null == serverNode)
            return;

        String oldMsg = "";
        String newMsg;
        ZkResServerNode oldNode;
        synchronized (this.serverNodeMap){
            ServerNode existing = this.serverNodeMap.get(serverNode.getServerId());
            if(null == existing)
            {
                existing = new ServerNode();
                this.serverNodeMap.put(serverNode.getServerId(),existing);
            }else{
                oldMsg = existing.toString();
            }

            oldNode = existing.updateNode(serverNode);
            newMsg = existing.toString();
        }

        String line = System.lineSeparator();
        log.info("变更Res节点:{}{}{}{}",line,oldMsg,line,newMsg);

        // 检测是否需要打开到Res的连接
        if(null != serverNode.getInAccessIp() &&
                !"".equals(serverNode.getInAccessIp().trim()) &&
                serverNode.getInAccessPort()>0 &&
                (null == oldNode ||
                        (!this.stringEqual(serverNode.getInAccessIp(),oldNode.getInAccessIp()) &&
                        serverNode.getInAccessPort() != oldNode.getInAccessPort())
                )
           ){
            ServerManager.getInstance().addServer(serverNode.getInAccessIp().trim(),
                    serverNode.getInAccessPort());
            log.debug("重新打开到Res服务器[{}:{}]的连接......",serverNode.getInAccessIp().trim(),
                    serverNode.getInAccessPort());
        }

        // 停止连接原来已经打开的连接
        this.disconnect(oldNode,serverNode);
    }
    /**
     * 移除res服务节点
     *
     * @param serverId  待移除的Res服务ID
     */
    @Override
    public void removeNode(String serverId){
        if(null == serverId || "".equals(serverId.trim()))
            return;

        serverId = serverId.trim();
        String oldMsg = "";
        ServerNode existing;
        synchronized (this.serverNodeMap){
            existing = this.serverNodeMap.remove(serverId);
        }

        if(null != existing)
            oldMsg = existing.toString();
        log.info("移除Res节点:{}",oldMsg);

        // 停止连接原来已经打开的连接
        if(null != existing)
            this.disconnect(existing.getServerNode(),null);
    }
    private boolean stringEqual(String old,String info){
        old = null == old?"":old.trim();
        info = null ==info?"":info.trim();
        return old.equals(info);
    }
    /**
     * 终止到Res服务的连接
     *
     * @param oldNode  旧节点
     * @param newNode  新节点
     */
    private void disconnect(ZkResServerNode oldNode,ZkResServerNode newNode){
        if(null == oldNode || null == oldNode.getInAccessIp() ||
                "".equals(oldNode.getInAccessIp().trim()) ||
                oldNode.getInAccessPort()<=0)
            return;

        // 新旧节点的IP/port相同
        // 则不能终止连接
        if(null != newNode &&
                this.stringEqual(oldNode.getInAccessIp(),newNode.getInAccessIp()) &&
                oldNode.getInAccessPort() == newNode.getInAccessPort()){
            return;
        }

        String existingIp = oldNode.getInAccessIp().trim();
        Integer existingPort = oldNode.getInAccessPort();

        if(log.isDebugEnabled()){
            log.debug("终止到Res服务器[{}:{}]的连接......",existingIp,existingPort);
        }

        ServerManager.getInstance().delServer(existingIp,existingPort);
    }
    /**
     * res服务节点的连接数
     *
     * 完成认证时添加
     */
    private void addConnNum(String serverId,boolean isAdd){
        if(null == serverId || "".equals(serverId.trim()))
            return;

        serverId = serverId.trim();
        synchronized (this.serverNodeMap){
            ServerNode node = this.serverNodeMap.get(serverId);
            if(null != node) {
                node.addConnNum(isAdd);
            }
        }
    }

    /**
     * 连接通道身份成功识别
     *
     * 增加此服务的计数器，并且设置close监听
     *
     * @param channel    被识别的连接通道，必填
     * @param appType    对端应用类型，必填
     * @param appId      对端应用ID，必填
     */
    @Override
    public void auth(Channel channel,String appType,String appId){
        log.debug("监听到连接已经完成身份标识：ch={},appType={},appId={}",channel,appType,appId);
        if(null == channel)
            return;
        if(null == appId || "".equals(appId.trim()))
            return;
        if(null == appType || !ES2ServerType.res.name().equalsIgnoreCase(appType.trim()))
            return;

        appId = appId.trim();
        this.addConnNum(appId,true);
        channel.closeFuture().addListener(new ResChannelCloseMonitor(appId));
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }
    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppType() {
        return this.appType;
    }

    public String getAppId() {
        return this.appId;
    }

    private class ResChannelCloseMonitor implements GenericFutureListener<ChannelFuture>{
        private String appId;

        public ResChannelCloseMonitor(String appId){
            this.appId = appId;
        }
        public void operationComplete(ChannelFuture f) throws Exception{
            if(f.isDone() && f.isSuccess()){
                // 减少服务器的计数器
                ResServerManager.this.addConnNum(this.appId,false);
            }
            f.removeListener(this);
        }
    }
}
