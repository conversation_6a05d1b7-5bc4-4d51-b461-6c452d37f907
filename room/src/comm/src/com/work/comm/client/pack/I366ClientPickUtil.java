package com.work.comm.client.pack;

import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import com.dzpk.common.utils.LogUtil;
import com.work.comm.client.protocal.Protocal;
import org.apache.logging.log4j.Logger;

public class I366ClientPickUtil {
	public static final Logger logger = LogUtil.getLogger(I366ClientPickUtil.class);

	//线程池共享变量读取，用于判断是否为内部通讯协议
	public static ThreadLocal<Integer> threadLocal = new ThreadLocal<>();

	/**
	 * 1个字节流类型
	 * */
	public static final int TYPE_INT_1 = 0;

	/**
	 * 4个字节流类型
	 * **/
	public static final int TYPE_INT_4 = 1;

	/**
	 * UTF-16  字节流转码
	 * */
	public static final int TYPE_STRING_UTF16 = 2;

	/**
	 * ascii unicode 字节流转码
	 * */
	public static final int TYPE_STRING_UNICODE = 3;

	/**
	 * int 数组
	 */
	public static final int TYPE_INT_4_ARRAY = 5;

	/**
	 * int 1 数组
	 * */

	public static final int TYPE_INT_1_ARRAY = 6;

	/**
	 * 一个bute itemid < 50
	 */
	public static final int TYPE_BYTE = 4;

	public static final String UTF_16 = "UTF-16";
	public static final String ISO_10646_UCS_2 = "ISO-10646-UCS-2";
	public static final String UNICODE = "UNICODE";

    private static final int MAX_CP_SIZE = 1024 * 16;

    /**
     * 返回包内容开始位置
     * */
    private static final int FEE_DATA_BT_LENGTH = 20;

	/**
	 * 解析最多的参数个数
	 */
	public static final int MAX_FOR = 20;
	/**
	 * 协议中协议内容开始的位置
	 * */
	private static final int START_LOCATION = 30 + 54;

    private static int byteArrayToInt(byte[] byteArrayData, int offset){
        return  (byteArrayData[offset]     & 0xff) << 24 |
                (byteArrayData[offset + 1] & 0xff) << 16 |
                (byteArrayData[offset + 2] & 0xff) << 8  |
                (byteArrayData[offset + 3] & 0xff);
    }

	/**
	 * @param bt2 字节流
	 * @param int2 2维数组 格式规定如：{{1,2} , {2 ,3}} 其中第一位是:itemID 第二位是:要转换的类型
	 * @return Map key itemId Value 对应解码出来的值
	 */
	public static Map<Integer, Object> pickAll(byte[] bt2, int[][] int2) {
		Map<Integer, Object> resMap = new HashMap<Integer, Object>();
		int beforeSite  = START_LOCATION;
		if(threadLocal.get() != null && threadLocal.get() == 1){//内部通讯协议读取位置沿用原先下标
			beforeSite -= 54;
		}
		int itemId;
		int inputSize;
		boolean isGive = true;
		for (int i=0 ; i < MAX_FOR ; i ++) {
    		//itemId 只有一位
			if ((beforeSite + 2) > bt2.length) {
				break;
			}
			isGive = true;
    		itemId = bt2[++ beforeSite] & 0xff;
    		for (int[] j : int2) {
    			if (j[0] == itemId) {
    				isGive = false;
    				switch ((Integer)j[1]) {
    				case TYPE_BYTE :
    					resMap.put(j[0], itemId);
    					break;
					case TYPE_INT_1:
						int type_int_1 = bt2[++ beforeSite] & 0xff;
						resMap.put(j[0], type_int_1);
						break;
					case TYPE_INT_4:
						try {
							inputSize = Functions.byteArrayToShortInt(bt2 , beforeSite + 1);
						} catch (Exception e) {
							logger.error("包长度出错！",e);
							break;
						}
						beforeSite = beforeSite + 2;
						int type_int_4 = byteArrayToInt(bt2 , beforeSite + 1);
						beforeSite = beforeSite + 4;
						resMap.put(j[0], type_int_4);
						break;
					case TYPE_STRING_UTF16:
						try {
							inputSize = Functions.byteArrayToShortInt(bt2 , beforeSite + 1);
						} catch (Exception e) {
							logger.error("包长度出错！",e);
							break;
						}
						beforeSite = beforeSite + 2;
		                String type_string_untf16 = null;
						try {
							if (inputSize != 0) {
								type_string_untf16 = new String(bt2,beforeSite + 1, inputSize,UTF_16);
							}
						} catch (UnsupportedEncodingException e) {
							logger.error("解析UTF_16类型出错" + e);
							logger.error("/////////////////////////////////////////////////////////////////");
							logger.error("inputSize  :" + inputSize);
							logger.error("beforeSite :" + beforeSite);
							logger.error("/////////////////////////////////////////////////////////////////");
							e.printStackTrace();
						}
						resMap.put(j[0] , type_string_untf16);
						beforeSite = beforeSite + inputSize;
						break;
					case TYPE_STRING_UNICODE:
						try {
							inputSize = Functions.byteArrayToShortInt(bt2 , beforeSite + 1);
						} catch (Exception e) {
//							logger.error("包长度出错！",e);
							break;
						}
						beforeSite = beforeSite + 2;
		                String type_string_unicode = null;
						try {
							if (inputSize != 0) {
								type_string_unicode = new String(bt2,beforeSite + 1, inputSize,UNICODE);
							}
						} catch (UnsupportedEncodingException e) {
							logger.error("解析UNICODE类型出错" + e);
							e.printStackTrace();
							logger.debug("/////////////////////////////////////////////////////////////////");
							logger.debug("inputSize :" + inputSize);
							logger.debug("beforeSite :" + beforeSite);
							logger.debug("/////////////////////////////////////////////////////////////////");
						}
						resMap.put(j[0] , type_string_unicode);
						beforeSite = beforeSite + inputSize;
						break;
					case TYPE_INT_4_ARRAY:
						try {
							inputSize = Functions.byteArrayToShortInt(bt2 , beforeSite + 1);
						} catch (Exception e) {
//							logger.error("包长度出错！",e);
							break;
						}
						beforeSite = beforeSite + 2;
		                Integer[] intArray = new Integer[inputSize/4];
						try {
							if (inputSize != 0) {
								for (int i2 = 0 ; i2 < intArray.length ; i2 ++) {
									int se = byteArrayToInt(bt2 , beforeSite + i2 * 4 + 1);
									intArray[i2] = se;

								}
							}
						} catch (Exception e) {
							logger.error("解析TYPE_INT_4_ARRAY类型出错" + e);
							e.printStackTrace();
							logger.debug("/////////////////////////////////////////////////////////////////");
							logger.debug("inputSize :" + inputSize);
							logger.debug("beforeSite :" + beforeSite);
							logger.debug("/////////////////////////////////////////////////////////////////");
						}
						resMap.put(j[0] , intArray);
						beforeSite = beforeSite + inputSize;
						break;
					case TYPE_INT_1_ARRAY:
						try {
							inputSize = Functions.byteArrayToShortInt(bt2 , beforeSite + 1);
						} catch (Exception e) {
//							logger.error("包长度出错！",e);
							break;
						}
						beforeSite = beforeSite + 2;
		                Integer[] intArray_ = new Integer[inputSize];
						try {
							if (inputSize != 0) {
								for (int i2 = 0 ; i2 < intArray_.length ; i2 ++) {
									int se =   bt2[beforeSite + i2 + 1] & 0xff;
									intArray_[i2] = se;

								}
							}
						} catch (Exception e) {
							logger.error("解析TYPE_INT_1_ARRAY类型出错" + e);
							e.printStackTrace();
							logger.debug("/////////////////////////////////////////////////////////////////");
							logger.debug("inputSize :" + inputSize);
							logger.debug("beforeSite :" + beforeSite);
							logger.debug("/////////////////////////////////////////////////////////////////");
						}
						resMap.put(j[0] , intArray_);
						beforeSite = beforeSite + inputSize;
						break;
					}
    				break;
    			}
    		}
    		//没有匹配到j[0] == itemId 时 这里做处理
    		//直接跳过改传值
    		if (isGive) {
    			//itemId
    			if (itemId < 50) {
    				continue;
    			}else if (50 <= itemId && itemId <128) {
    				++ beforeSite;
    				continue;
    			}else if (itemId >= 128) {
    				inputSize = Functions.byteArrayToShortInt(bt2 , beforeSite + 1);
    				beforeSite = beforeSite + 2;
    				beforeSite = beforeSite + inputSize;
    				continue;
    			}
    		}
		}
		return resMap;
	}

    /**
     * @param int2  2维数组 格式规定如：{{1,2,3} , {2 ,3,4}} 其中第一位是:itemID 第二位是:value 第三位是：type
     * @param requestCode
     * @return
     */
    public static byte[] packAll(Object[][] int2 , int requestCode) {
    	byte[] bt = packHead();
        bt[Protocal.SRP_REQUEST_HIGH] = (byte) (requestCode>>8); //Request Code (derived from client request)
        bt[Protocal.SRP_REQUEST_LOW] = (byte) (requestCode); //
        int feeDataBtLength = FEE_DATA_BT_LENGTH;
        bt[feeDataBtLength] = (byte)(int2.length);
        int itemId;
        byte[] feePageDataBt;
        for (Object[] objl : int2) {
        	itemId = (Integer) objl[0];
        	bt[++ feeDataBtLength] = (byte) itemId;
        	int type = (Integer) objl[2];
        	switch (type) {
        	case I366ClientPickUtil.TYPE_BYTE:
        		//不跟字节
        		break;

			case I366ClientPickUtil.TYPE_INT_1:
				//跟一位
				int i_ = (Integer)objl[1];
				bt[++ feeDataBtLength] = (byte) i_;
				break;

			case I366ClientPickUtil.TYPE_INT_4:
				if (itemId >= 128) {
					bt[++ feeDataBtLength] = (byte) (4>>8);
					bt[++ feeDataBtLength] = (byte) (4);
				}
				feePageDataBt = Functions.intToByteArray((Integer) objl[1]);
				System.arraycopy(feePageDataBt,0,bt, feeDataBtLength + 1,feePageDataBt.length);
				feeDataBtLength = feeDataBtLength + 4;
				break;

			case I366ClientPickUtil.TYPE_STRING_UNICODE:
				feePageDataBt = Functions.StringToBytesUNICODE((String) objl[1]);
	        	//放长度
	        	bt[++ feeDataBtLength] = (byte) (feePageDataBt.length>>8) ;
	        	bt[++ feeDataBtLength] = (byte) (feePageDataBt.length);
				System.arraycopy(feePageDataBt,0,bt, feeDataBtLength + 1,feePageDataBt.length);
				feeDataBtLength = feeDataBtLength + feePageDataBt.length;
				break;
			case I366ClientPickUtil.TYPE_STRING_UTF16:
				feePageDataBt = Functions.StringToBytes((String) objl[1]);
	        	//放长度
	        	bt[++ feeDataBtLength] = (byte) (feePageDataBt.length>>8) ;
	        	bt[++ feeDataBtLength] = (byte) (feePageDataBt.length);
				System.arraycopy(feePageDataBt,0,bt, feeDataBtLength + 1,feePageDataBt.length);
				feeDataBtLength = feeDataBtLength + feePageDataBt.length;
				break;
			case I366ClientPickUtil.TYPE_INT_4_ARRAY:
				//获取int数组
				Integer[] intArray = (Integer[]) objl[1];
				feePageDataBt = new byte[intArray.length * 4];
				for (int i3 = 0 ; i3 < intArray.length ; i3 ++) {
					if (intArray[i3] == null) {
						break;
					}
					int i4 = i3 * 4;
					byte[] bs = Functions.intToByteArray(intArray[i3]);
					feePageDataBt[i4] = bs[0];
					feePageDataBt[i4 + 1] = bs[1];
					feePageDataBt[i4 + 2] = bs[2];
					feePageDataBt[i4 + 3] = bs[3];
				}

	        	//放长度
	        	bt[++ feeDataBtLength] = (byte) (feePageDataBt.length>>8) ;
	        	bt[++ feeDataBtLength] = (byte) (feePageDataBt.length);
				System.arraycopy(feePageDataBt,0,bt, feeDataBtLength + 1,feePageDataBt.length);
				feeDataBtLength = feeDataBtLength + feePageDataBt.length;
				break;
			case I366ClientPickUtil.TYPE_INT_1_ARRAY:
				//获取int数组
				Integer[] intArray_ = (Integer[]) objl[1];
				feePageDataBt = new byte[intArray_.length];
				for (int i3 = 0 ; i3 < intArray_.length ; i3 ++) {
					if (intArray_[i3] == null) {
						break;
					}
					int a = intArray_[i3];
					feePageDataBt[i3] = (byte) a;
				}

	        	//放长度
	        	bt[++ feeDataBtLength] = (byte) (feePageDataBt.length>>8) ;
	        	bt[++ feeDataBtLength] = (byte) (feePageDataBt.length);
				System.arraycopy(feePageDataBt,0,bt, feeDataBtLength + 1,feePageDataBt.length);
				feeDataBtLength = feeDataBtLength + feePageDataBt.length;
				break;
			}
        }
        ++ feeDataBtLength;
    	//设置返回的长度
    	bt[Protocal.SRP_SIZE_HIGH] = (byte) (feeDataBtLength>>8) ;
    	bt[Protocal.SRP_SIZE_LOW] = (byte) (feeDataBtLength) ;
    	//把多余长度去掉
    	byte[] bt2 = new byte[feeDataBtLength];
    	System.arraycopy(bt,0,bt2,0,feeDataBtLength);
    	return bt2;
    }

    public static byte[] packHead() {
    	byte[] bt = new byte[MAX_CP_SIZE];
        bt[0] = Protocal.HEADER_INDICATER_0;
        bt[1] = Protocal.HEADER_INDICATER_1;
        bt[2] = Protocal.HEADER_INDICATER_2;
        bt[3] = Protocal.HEADER_INDICATER_3;
        bt[Protocal.SRP_PACKE_LEVEL] = (byte) 0;
        return bt;
    }

	public static String stringify(Object[][] objs) {
		StringBuilder sb = new StringBuilder("[");
		for (Object[] item : objs) {
			int index = (Integer) item[0];
			Object obj = item[1];
			int type = (Integer) item[2];
			sb.append("{i=").append(index);
			sb.append(" t=").append(type);
			sb.append(" v=");
			if (obj.getClass().isArray()) {
				sb.append(Arrays.toString((Object[]) obj));
			} else {
				sb.append(obj);
			}
			sb.append("}");
		}
		return sb.append("]").toString();
	}
	
}
