package com.work.comm.s2s.common.matcher;

import com.work.comm.s2s.common.ChannelAttributeKey;
import io.netty.channel.Channel;
import io.netty.util.Attribute;
import lombok.Getter;
import lombok.ToString;

/**
 * 每类客户端Channel对应一个实例
 * IP:PORT对标识channel
 */
@Getter
@ToString
public class ChannelIpPortMatcherImpl implements IExChannelMatcher {
    /** channal中的属性值 */
    // 端口,必填
    private int port;
    // IP,必填
    private String ip;

    public ChannelIpPortMatcherImpl(String ip, int port){
        this.port = port;
        this.ip = null==ip?"":ip.trim();
    }

    /**
     * 增加属性到channel
     *
     * @param ch  channel，必填
     */
    public void addAttr(Channel ch){
        if(null == ch)
            return;

        Attribute<Integer> port = ch.attr(ChannelAttributeKey.portKey);
        port.setIfAbsent(this.port);
        Attribute<String> ip = ch.attr(ChannelAttributeKey.ipKey);
        ip.setIfAbsent(this.ip);
    }

    @Override
    public boolean matches(Channel ch){
        boolean matched = false;
        if(null == ch)
            return matched;

        // 检查IP是否匹配
        if(ch.hasAttr(ChannelAttributeKey.ipKey)){
            String value = ch.attr(ChannelAttributeKey.ipKey).get();
            matched = this.ip.equalsIgnoreCase(value);
            if(!matched)
                return matched;
        }

        // 检查port是否匹配
        if(ch.hasAttr(ChannelAttributeKey.portKey)){
            Integer value = ch.attr(ChannelAttributeKey.portKey).get();
            matched = (null!=value && value==this.port);
        }

        return matched;
    }
}
