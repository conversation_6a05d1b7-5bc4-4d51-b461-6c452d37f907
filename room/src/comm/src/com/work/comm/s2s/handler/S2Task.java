package com.work.comm.s2s.handler;

import com.work.comm.s2s.processor.IProcessor;
import com.work.comm.s2s.processor.IProcessorFactory;
import com.work.comm.s2s.protocal.ServiceRequest;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class S2Task implements Runnable {
    private ServiceRequest serviceRequest;
    private ChannelHandlerContext ctx;
    private IProcessorFactory processorFactory;
    
    public S2Task(ServiceRequest serviceRequest, ChannelHandlerContext ctx,IProcessorFactory processorFactory) {
        this.serviceRequest = serviceRequest;
        this.ctx = ctx;
        this.processorFactory = processorFactory;
    }
    
    @Override
    public void run() {
        log.debug("ctx: " + ctx + ",current thread id ===>" + Thread.currentThread().getId());
        byte[] retBytes = null;
        try {
            IProcessor processor = this.processorFactory.getProcessor(serviceRequest.getReqCode());
            retBytes = processor.handle(serviceRequest);
        } catch (Exception e) {
            log.error("hand server error", e);
        }
        if (retBytes != null) {
            // printHexString(retBytes);
            log.debug("send to client");
            ctx.writeAndFlush(Unpooled.copiedBuffer(retBytes));
            // ctx.write(retBytes);
        } else {
            log.debug("nothing to send");
        }
    };
}
