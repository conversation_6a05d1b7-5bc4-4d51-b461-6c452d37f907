package com.work.comm.s2s.server;

import com.work.comm.s2s.common.*;
import com.work.comm.s2s.server.boostrap.ServerConfig;
import io.netty.channel.*;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 功能：
 * 1. 对接上层调用功能
 *    * 应用类型匹配一组connector
 *    * 应用类型及ID匹配对应的connector
 *    * 不改动上层调用，原来的IP&端口对映射应用类型及ID
 * 2. 面向底层网络处理，将连接上的连接放入管理连接池
 * 3. 认证连入的连接
 */
@Slf4j
public class ClientManager extends AbstractChannelManager{
    /** 单例模式 */
    private static final AtomicBoolean isInitialized = new AtomicBoolean(false);
    private static ClientManager INSTANCE = null;
    public static final ClientManager getInstance(){
        return ClientManager.INSTANCE;
    }

    /**
     * 系统启动时调用
     * 第一次调用有效
     * @param workerGroup
     * @param serverConfig
     */
    public static void initialize(EventLoopGroup workerGroup,ServerConfig serverConfig){
        boolean alreadyInitialize = isInitialized.getAndSet(true);
        if(alreadyInitialize)
            return;

        INSTANCE = new ClientManager(workerGroup,serverConfig);
    }

    private ClientManager(EventLoopGroup workerGroup, ServerConfig serverConfig){
        super(serverConfig.getAppType(),serverConfig.getAppId(),workerGroup,false);
        this.serverConfig = serverConfig;
    }

    /** 后续修改为动态配置 */
    private ServerConfig serverConfig;

    /** 实现父类方法 */
    protected int getMaxConnNumPerApp(){
        return this.serverConfig.getMaxConnNumPerApp();
    }
    protected int getReadIdleMaxNum(){
        return this.serverConfig.getReadIdleMaxNum();
    }
    protected int getAuthTimeoutSec(){
        return this.serverConfig.getAuthTimeoutSec();
    }
}
