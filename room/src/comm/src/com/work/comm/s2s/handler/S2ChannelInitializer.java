package com.work.comm.s2s.handler;

import com.work.comm.s2s.common.ChannelAttributeKey;
import com.work.comm.s2s.common.IChannelInitializerConfig;
import io.netty.channel.ChannelDuplexHandler;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.LengthFieldBasedFrameDecoder;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.timeout.IdleStateHandler;
import io.netty.util.Attribute;

/**
 * 服务端内部通信
 */
public class S2ChannelInitializer extends ChannelInitializer<SocketChannel> {
    private IChannelInitializerConfig config = null;
    private ChannelDuplexHandler chManageHandler = null;
    private ChannelHandler businessHandler;
    private long batchNum;

    public S2ChannelInitializer(long batchNum,
                                IChannelInitializerConfig config,
                                ChannelDuplexHandler chManageHandler,
                                ChannelHandler businessHandler){
        this.batchNum = batchNum;
        this.config = config;
        this.chManageHandler = chManageHandler;
        this.businessHandler = businessHandler;
    }
    
    @Override
    public void initChannel(SocketChannel channel) throws Exception {
        Attribute<Long> batch = channel.attr(ChannelAttributeKey.configBatchNumKey);
        batch.setIfAbsent(this.batchNum);

        ChannelPipeline pipeline = channel.pipeline();
        pipeline.addLast("logHandler",new LoggingHandler(this.parseFrom(config.getLogLevel())))
                .addLast("idleHandler",new IdleStateHandler(this.config.getReadIdleTimeSec(),0,0))
                .addLast("chManageHandler",this.chManageHandler);
        pipeline.addLast("decoder", new LengthFieldBasedFrameDecoder(this.config.getMaxFrameLength(),
                this.config.getLengthFieldOffset(), this.config.getLengthFieldLength(),
                this.config.getLengthAdjustment(), this.config.getInitialBytesToStrip(), true));
        pipeline.addLast("handler", this.businessHandler);
    }

    /**
     * 将配置值转换成NETTY的值
     *
     * @param value   日志级别的枚举值
     *
     * @return  枚举
     */
    private LogLevel parseFrom(int value){
        LogLevel result = LogLevel.WARN;

        for(LogLevel level : LogLevel.values()){
            if(level.ordinal() == value){
                result = level;
                break;
            }
        }

        return result;
    }
}