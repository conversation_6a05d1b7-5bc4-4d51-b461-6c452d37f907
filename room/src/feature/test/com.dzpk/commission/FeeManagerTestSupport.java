package com.dzpk.commission;

import com.dzpk.commission.IFeeManager.*;
import lombok.AllArgsConstructor;

import java.util.AbstractMap;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

public class FeeManagerTestSupport {

    void runFeeTest(IFeeManager feeManager, HandState[] game, Map<Integer, TestResult> results) {
        for (HandState hand : game) {
            feeManager.captureHandState(hand);
        }

        FeeSummary feeSummary = feeManager.calculateFee();
        assertNotNull(feeSummary, "should not be null");

        for (Map.Entry<Integer, TestResult> entry : results.entrySet()) {
            int playerId = entry.getKey();
            TestResult result = entry.getValue();
            boolean[] found = {false, false, false, false, false, false};
            feeSummary.getFeeItems().stream().filter(item -> item.getPlayerId() == playerId).forEach(actual -> {
                assertNotNull(actual, "should not be null");

                FeeItem expected = null;
                switch (actual.getFeeType()) {
                    case PLATFORM_FEE: expected = result.platformFee; break;
                    case TRIBE_FEE: expected = result.tribeFee; break;
                    case CLUB_FEE: expected = result.clubFee; break;
                    case WIN_REBATE: expected = result.winRebate; break;
                    case LOSE_REBATE: expected = result.loseRebate; break;
                    case INSURANCE_FEE: expected = result.insuranceFee; break;
                }

                if (expected != null) {
                    assertEquals(expected, actual, "incorrect fee item");
                } else {
                    assertEquals(expected, actual, "unexpected fee type");
                }

                found[actual.getFeeType().ordinal()] = true;
            });

            boolean[] required = result.required();
            for (int i = 0; i < required.length; i++) {
                assertEquals(required[i], found[i], String.format("%s fee type %s for player %d", required[i] ? "missing" : "unexpected", EFeeType.values()[i], playerId));
            }
        }
    }

    @AllArgsConstructor
    static class TestResult {
        static final TestResult NONE = new TestResult(null, null, null, null, null, null);

        final FeeItem platformFee;
        final FeeItem tribeFee;
        final FeeItem clubFee;
        final FeeItem winRebate;
        final FeeItem loseRebate;
        final FeeItem insuranceFee;

        boolean[] required() {
            return new boolean[]{platformFee != null, tribeFee != null, clubFee != null, winRebate != null, loseRebate != null, insuranceFee != null};
        }
    }

    @SafeVarargs
    static <K, V> Map<K, V> mapOf(Map.Entry<K, V>... entries) {
        return Stream.of(entries).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    static <K, V> Map.Entry<K, V> tuple(K k, V v) {
        return new AbstractMap.SimpleImmutableEntry<>(k, v);
    }
}
