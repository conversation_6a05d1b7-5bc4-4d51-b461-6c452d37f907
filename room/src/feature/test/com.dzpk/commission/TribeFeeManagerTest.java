package com.dzpk.commission;

import com.dzpk.commission.IFeeManager.*;
import com.dzpk.commission.impl.TribeFeeManager;
import com.dzpk.commission.impl.TribeFeeManager.TribeFeeConfig;
import org.junit.jupiter.api.Test;

import java.util.Map;

public class TribeFeeManagerTest extends FeeManagerTestSupport {

    private static final int CLUB_A = 10;
    private static final int CLUB_B = 20;

    TribeFeeConfig config1 = new TribeFeeConfig(
            0.005,
            1,
            0.1,
            0.05,
            mapOf(tuple(CLUB_A, 0.95), tuple(CLUB_B, 0.95)),
            mapOf(tuple(CLUB_A, 0.0), tuple(CLUB_B, 0.0)),
            mapOf(tuple(CLUB_A, 0.045), tuple(CLUB_B, 0.045)),
            mapOf(tuple(CLUB_A, 0.9), tuple(CLUB_B, 0.8)),
            null
    );

    HandState[] game1 = {
            new HandState(1, 11, CLUB_A, 200000, 0, 20000),
            new HandState(1, 12, CLUB_A, -50000, -50000, 0),
            new HandState(1, 21, CLUB_B, 50000, 0, 40000),
            new HandState(1, 22, CLUB_B, -250000, -60000, 0),
    };

    @Test
    public void givenConfig1AndModeIsPerGame_WhenGame1Finished_ThenFeeIsCorrect() {
        Map<Integer, TestResult> results = mapOf(
                tuple(11, new TestResult(
                        new FeeItem(11, CLUB_A, EFeeType.PLATFORM_FEE, 0, 1000, -1000, 0, 0),
                        new FeeItem(11, CLUB_A, EFeeType.TRIBE_FEE, -10000, 0, 10000, 0, 0),
                        null,
                        new FeeItem(11, CLUB_A, EFeeType.WIN_REBATE, 0, 0, 0, 0, 0),
                        null,
                        new FeeItem(11, CLUB_A, EFeeType.INSURANCE_FEE, 20000, 0, -2900, -17100, 0))),
                tuple(12, new TestResult(
                        null,
                        null,
                        null,
                        null,
                        new FeeItem(12, CLUB_A, EFeeType.LOSE_REBATE, 0, 0, -102, 102, 0),
                        new FeeItem(12, CLUB_A, EFeeType.INSURANCE_FEE, -50000, 0, 7250, 42750, 0))),
                tuple(21, new TestResult(
                        new FeeItem(21, CLUB_B, EFeeType.PLATFORM_FEE, 0, 250, -250, 0, 0),
                        new FeeItem(21, CLUB_B, EFeeType.TRIBE_FEE, -2500, 0, 2500, 0, 0),
                        null,
                        new FeeItem(21, CLUB_B, EFeeType.WIN_REBATE, 0, 0, 0, 0, 0),
                        null,
                        new FeeItem(21, CLUB_B, EFeeType.INSURANCE_FEE, 40000, 0, -9600, -30400, 0))),
                tuple(22, new TestResult(
                        null,
                        null,
                        null,
                        null,
                        new FeeItem(22, CLUB_B, EFeeType.LOSE_REBATE, 0, 0, -507, 507, 0),
                        new FeeItem(22, CLUB_B, EFeeType.INSURANCE_FEE, -60000, 0, 14400, 45600, 0))),
                tuple(0, new TestResult(
                        null,
                        null,
                        null,
                        null,
                        null,
                        new FeeItem(0, 0, EFeeType.INSURANCE_FEE, 0, 5000, -5000, 0, 0)))
        );

        runFeeTest(new TribeFeeManager(EFeeMode.PER_GAME, config1), game1, results);
    }

    TribeFeeConfig config2 = new TribeFeeConfig(
            0.005,
            1,
            0.001,
            0.05,
            mapOf(tuple(CLUB_A, 0.5), tuple(CLUB_B, 0.5)),
            mapOf(tuple(CLUB_A, 0.2), tuple(CLUB_B, 0.5)),
            mapOf(tuple(CLUB_A, 0.4), tuple(CLUB_B, 0.6)),
            mapOf(tuple(CLUB_A, 0.9), tuple(CLUB_B, 0.9)),
            null
    );

    HandState[] game2 = {
            new HandState(1, 11, CLUB_A, -20000, 0, 0),
            new HandState(1, 21, CLUB_B, 20000, 0, 0),
    };

    @Test
    public void givenConfig2AndModeIsPerGame_WhenGame2Finished_ThenFeeIsCorrect() {
        Map<Integer, TestResult> results = mapOf(
                tuple(11, new TestResult(
                        null,
                        null,
                        null,
                        null,
                        new FeeItem(11, CLUB_A, EFeeType.LOSE_REBATE, 0, 0, -360, 360, 0),
                        null)),
                tuple(21, new TestResult(
                        new FeeItem(21, CLUB_B, EFeeType.PLATFORM_FEE, 0, 100, -100, 0, 0),
                        new FeeItem(21, CLUB_B, EFeeType.TRIBE_FEE, -1000, 0, 1000, 0, 0),
                        null,
                        new FeeItem(21, CLUB_B, EFeeType.WIN_REBATE, 0, 0, -450, 450, 0),
                        null,
                        null))
        );

        runFeeTest(new TribeFeeManager(EFeeMode.PER_GAME, config2), game2, results);
    }
}
