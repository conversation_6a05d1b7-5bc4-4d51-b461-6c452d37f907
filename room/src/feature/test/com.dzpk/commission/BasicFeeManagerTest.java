package com.dzpk.commission;

import com.dzpk.commission.impl.BasicFeeManager;
import com.dzpk.commission.impl.BasicFeeManager.BasicFeeConfig;
import com.dzpk.commission.IFeeManager.*;
import org.junit.jupiter.api.Test;

import java.util.Map;

public class BasicFeeManagerTest extends FeeManagerTestSupport {

    private static final int CLUB_A = 10;

    BasicFeeConfig config1 = new BasicFeeConfig(0.005, 0.05);

    HandState[] game1 = {
            new HandState(1, 11, CLUB_A, 200000, 0, 0),
            new HandState(1, 12, CLUB_A, -40000, 0, 0),
            new HandState(1, 13, CLUB_A, 120000, 0, 0),
            new HandState(1, 14, CLUB_A, -220000, 0, 0),
    };

    @Test
    public void givenConfig1AndModeIsPerGame_WhenGame1Finished_ThenFeeIsCorrect() {
        Map<Integer, TestResult> results = mapOf(
                tuple(11, ClubTestResult.of(11, 1000, 10000)),
                tuple(12, TestResult.NONE),
                tuple(13, ClubTestResult.of(13, 600, 6000)),
                tuple(14, TestResult.NONE)
        );
        runFeeTest(new BasicFeeManager(EFeeMode.PER_GAME, config1), game1, results);
    }

    static class ClubTestResult extends TestResult {
        private ClubTestResult(FeeItem platformFee, FeeItem clubFee) {
            super(platformFee, null, clubFee, null, null, null);
        }

        static ClubTestResult of(int playerId, int platformFee, int clubFee) {
            FeeItem platformFeeItem = new FeeItem(playerId, CLUB_A, EFeeType.PLATFORM_FEE, 0, platformFee, 0, -platformFee, 0);
            FeeItem clubFeeItem = new FeeItem(playerId, CLUB_A, EFeeType.CLUB_FEE, -clubFee, 0, 0, clubFee, 0);
            return new ClubTestResult(platformFeeItem, clubFeeItem);
        }
    }
}
