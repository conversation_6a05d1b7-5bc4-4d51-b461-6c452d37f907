package com.dzpk.vigilance.auto.rules;

import com.dzpk.vigilance.auto.AutoRule;
import com.dzpk.vigilance.auto.AutoVigilanceConfig;
import com.dzpk.vigilance.auto.AutoVigilanceExecutor;
import com.dzpk.vigilance.auto.TimeTrigger;
import com.dzpk.vigilance.repositories.mysql.IHighRiskPlayerDao;
import com.dzpk.vigilance.repositories.mysql.impl.HighRiskPlayerDaoImpl;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
public class UnflagByUserProfit implements AutoRule {

    private final Config config;
    private final TimeTrigger trigger;

    public UnflagByUserProfit(AutoVigilanceConfig.AutoRuleConfig config) {
        this.config = (Config)config;
        this.trigger = new TimeTrigger(config.getCheckInterval(), TimeUnit.MINUTES);
    }

    @Override
    public void apply(AutoVigilanceExecutor executor) {
        log.debug("checkPeriod={} minProfit={} trigger={}", config.getCheckPeriod(), config.getMinProfit(), trigger);
        trigger.trigger(() -> {
            IHighRiskPlayerDao highRiskPlayerDao = new HighRiskPlayerDaoImpl();
            Set<Integer> result = highRiskPlayerDao.findHighRiskUsersBelowProfitInLastMinutes(100 * config.getMinProfit(), config.getCheckPeriod());
            executor.unflagUsers(result, config);
        });
    }

    public interface Config extends AutoVigilanceConfig.AutoUnflagRuleConfig {
        /**
         * Check period in minutes.
         * @return
         */
        int getCheckPeriod();

        /**
         * Minimum profit, without fractional part. (100 = 100.0)
         * @return
         */
        int getMinProfit();
    }
}
