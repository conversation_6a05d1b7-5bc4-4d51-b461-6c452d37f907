package com.dzpk.vigilance.auto;

import com.dzpk.vigilance.VigilanceProvider;
import com.dzpk.vigilance.VigilanceProviderManager;
import com.dzpk.vigilance.auto.AutoVigilanceConfig.AutoRuleConfig;
import com.dzpk.vigilance.repositories.mysql.IHighRiskPlayerDao;
import com.dzpk.vigilance.repositories.mysql.impl.HighRiskPlayerDaoImpl;
import com.dzpk.work.WorkThreadService;
import com.i366.cache.Cache;

import java.util.Collection;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AutoVigilanceManager {

    private static final long TASK_INTERVAL = 20_100;

    private static AutoVigilanceTask task;

    public static void start() {
        task = new AutoVigilanceTask();
        // add some randomness per server instance
        long initialDelay = (long) (TASK_INTERVAL * (0.5 + Math.random()));
        WorkThreadService.getSchedulePool().scheduleAtFixedRate(task, initialDelay, TASK_INTERVAL, TimeUnit.MILLISECONDS);
    }

    public static void stop() {
        if (task != null) {
            log.info("Stopping auto vigilance task");
            task.stop();
            log.info("Auto vigilance task stopped");
        }
    }

    @Slf4j
    static class AutoVigilanceTask implements Runnable {

        private AutoRuleRegistry registry;
        private AutoVigilanceConfig config;

        private volatile boolean stopped = false;

        public synchronized void stop() {
            stopped = true;
        }

        @Override
        public synchronized void run() {
            if (stopped) {
                log.debug("task stopped");
                return;
            }
            try {
                runTask();
                log.debug("task finished");
            } catch (Exception ex) {
                log.error("task failed", ex);
            }
        }

        private void runTask() {
            log.debug("task running");
            VigilanceProvider provider = VigilanceProviderManager.getProvider(Cache.p);
            AutoVigilanceConfig current = provider.getAutoVigilanceConfig();
            if (registry == null || current != config) {
                config = current;
                registry = new AutoRuleRegistry();
            }
            AutoVigilanceExecutor executor = new AutoVigilanceExecutor();
            config.getFlagRules().stream()
                    .filter(AutoRuleConfig::isEnabled)
                    .map(config -> registry.getFlagRule(config))
                    .forEach(rule -> rule.apply(executor));
            config.getUnflagRules().stream()
                    .filter(AutoRuleConfig::isEnabled)
                    .map(config -> registry.getUnflagRule(config))
                    .forEach(rule -> rule.apply(executor));
        }
    }
}
