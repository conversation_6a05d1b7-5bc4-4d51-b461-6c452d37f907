package com.dzpk.vigilance.auto.rules;

import com.dzpk.vigilance.auto.AutoRule;
import com.dzpk.vigilance.auto.AutoVigilanceConfig;
import com.dzpk.vigilance.auto.AutoVigilanceExecutor;
import com.dzpk.vigilance.auto.TimeTrigger;
import com.dzpk.vigilance.repositories.mysql.IHighRiskPlayerDao;
import com.dzpk.vigilance.repositories.mysql.impl.HighRiskPlayerDaoImpl;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
public class FlagByRoomProfit implements AutoRule {

    private final Config config;
    private final TimeTrigger trigger;

    public FlagByRoomProfit(AutoVigilanceConfig.AutoRuleConfig config) {
        this.config = (Config)config;
        this.trigger = new TimeTrigger(config.getCheckInterval(), TimeUnit.MINUTES);
    }

    @Override
    public void apply(AutoVigilanceExecutor executor) {
        log.debug("checkInterval={} maxProfitXBB={} comment={} trigger={}", config.getCheckInterval(), config.getMaxProfitXBB(), config.getComment(), trigger);
        trigger.trigger(() -> {
            IHighRiskPlayerDao highRiskPlayerDao = new HighRiskPlayerDaoImpl();
            Set<Integer> result = highRiskPlayerDao.findUsersAboveRoomProfit(config.getMaxProfitXBB());
            executor.flagUsers(result, config);
        });
    }

    public interface Config extends AutoVigilanceConfig.AutoFlagRuleConfig {
        /**
         * Maximum profit as multiple of big blind.
         * @return
         */
        int getMaxProfitXBB();
    }
}
