package com.dzpk.gameevent;

import com.i366.model.room.Room;

import java.util.List;

public interface GameEventService {
    void emitPublicCards(Room room, List<Integer> publicCard);

    void emitHandCards(Room room, Integer[] firstCardArray, Integer[] secondCardArray);

    void emitEnterRoom(Room room, int userId);

    void emitLeaveRoom(Room room, int userId);

    void emitBipaiResult(Room room, Integer[] playerIdArray, Integer[] isWinArray);

    void emitUserChips(Room room);

    void emitUserBringIn(Room room);
}
