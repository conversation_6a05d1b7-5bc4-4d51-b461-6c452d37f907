package com.dzpk.jackpot.impl;

import com.dzpk.commission.IFeeService;
import com.dzpk.commission.repositories.mysql.IPlatformAccountDao;
import com.dzpk.commission.repositories.mysql.impl.PlatformAccountDaoImpl;
import com.dzpk.commission.repositories.mysql.model.PlatformAccountLog;
import com.dzpk.common.constant.ERoundingMode;
import com.dzpk.component.repositories.mysql.dbpool.DBUtil;
import com.dzpk.currency.CurrencyFactory;
import com.dzpk.currency.IExchange;
import com.dzpk.db.dao.GameHistoryDao;
import com.dzpk.db.imp.GamehistoryImp;
import com.dzpk.db.model.JPHistory;
import com.dzpk.jackpot.*;
import com.dzpk.jackpot.constant.EJackpotIntoType;
import com.dzpk.jackpot.repositories.RepositoriesFactory;
import com.dzpk.jackpot.repositories.mongodb.IJackpotRewardDao;
import com.dzpk.jackpot.repositories.mysql.IRoomJackpotDao;
import com.i366.cache.Cache;
import com.i366.constant.ChipsCode;
import com.i366.constant.Constant;
import com.i366.model.player.RoomPersion;
import com.i366.model.pocer.JPPool;
import com.i366.model.room.Room;
import com.i366.model.pocer.Pocer;
import com.i366.util.ChipUtils;
import com.i366.util.JackPotUtil;
import com.dzpk.common.utils.LogUtil;
import com.i366.util.PublisherUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.server.pack.I366ServerPickUtil;
import org.apache.logging.log4j.Logger;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class JackpotServiceImpl implements IJackpotService {
    /** 日志服务 */
    private static final Logger logger = LogUtil.getLogger(JackpotServiceImpl.class);

    private Room room;

    // 牌局JP的设置
    private RoomJackpotConfig jackpotConfig;

    // 每个玩家在此牌局中JP【应投彩】【实际投彩】【奖励】【奖励的服务费】
    private Map<Integer,PlayerJackpotStatistic> playerJackpotMap = new ConcurrentHashMap<>();

    // 牌局玩家的产生JP奖励的手牌及奖励
    private List<PlayerJpRewardDetail> playerRewardDetailLst = new ArrayList<>();

    private IPlatformAccountDao accountDao = new PlatformAccountDaoImpl();
    private GameHistoryDao historyDao=new GamehistoryImp();
    /**
     * 初始化
     * *牌局JP的配置数据
     * *白名单控制
     * @param room 牌局对象
     *    roomId   牌局ID
     *    roomPath 牌局类型
     *    manzhu   牌局盲注
     */
    public JackpotServiceImpl(Room room){
        this.room = room;

        IRoomJackpotDao dao = RepositoriesFactory.getRoomJackpotDao();
        this.jackpotConfig = dao.getRoomJackpotConfig(this.room.getRoomId(),this.room.getRoomPath());
        if(null == this.jackpotConfig)
            return;

        int jackPotId = this.jackpotConfig.getJackpotId();

        // 不存在累计次数统计，初始化一个
        JackpotFactory.getInstance().initAccumulativeTime(jackPotId);
    }

    /**
     * 每手重置数据
     */
    public void resetPerHand(){
        Iterator<Integer> it = this.playerJackpotMap.keySet().iterator();
        while(it.hasNext()){
            int userId = it.next();
            PlayerJackpotStatistic statistic = this.playerJackpotMap.get(userId);
            if(null != statistic)
                statistic.resetPerHand();
        }
    }

    /**
     * 获取牌局对应的jackpotID
     * @return
     */
    public int getJackpotId(){
        int result = 0;
        if(null != this.jackpotConfig)
            result = this.jackpotConfig.getJackpotId();

        return result;
    }

    /**
     * 计算盈利扣减sb到jackpot池
     * @param userId  赢家
     * @param earn    盈利
     * @return 扣减sb之后的盈利值
     */
    public JPPool betToJackPot(int userId,String nickName,int earn,int count){
        int jackpotId = this.getJackpotId();
        int curTimes = JackpotFactory.getInstance().addAccumulativeTime(jackpotId);
        if (logger.isDebugEnabled())
            logger.debug("Check accumulative times {} : jackpotId={} , roomId={} , userId={} , manzhu={} , betValue={}",
                    curTimes,jackpotId,this.room.getRoomId(),userId,this.room.getManzhu(),this.room.getManzhu());

        //符合累计抽取的条件
        // JP投彩的【计分牌】转换【金豆】保存到【彩池】
        int jpBetPerHand = this.room.getDamanzhu();  // 玩家每手应投彩
        jpBetPerHand=jpBetPerHand/count;
        int sign=0;
        if(curTimes % 4 == 0){// 投入跑马灯奖池
            // 跑马灯奖池
            addHorseRaceLamp(userId, nickName, jpBetPerHand, jackpotId);
            sign=1;
        }else if (curTimes % 3 == 0){//投入系统池
            addJPB(userId, nickName, jpBetPerHand, jackpotId);
            sign=2;
        }else{//投入彩池
            // 彩池
            addjackpotBet(userId, nickName, jpBetPerHand, jackpotId);
            sign=3;
        }

        // 每手投彩记录 用于上局回顾显示投入数据显示
        playerJackpotStatistic(userId, nickName, jpBetPerHand, jpBetPerHand);

        earn -= jpBetPerHand;//收取2sb的积分投入jackpot（1sb投入彩池，1sb投入跑马灯奖池   或   2sb投入jpb）

        return new JPPool(earn,jpBetPerHand,sign);
    }

    /**
     * 投入跑马灯奖池
     * @param userId
     * @param nickName
     * @param scorecardForBet
     * @param jackpotId
     */
    private void addHorseRaceLamp(int userId, String nickName, int scorecardForBet, int jackpotId) {
        try {
            // 转换
            int addJackPot = scoreToJackPot(scorecardForBet);
            if(logger.isDebugEnabled()){
                logger.debug("addHorseRaceLamp Jackpot BB : jackpotId={} , roomId={} , userId={} , scorecardNum={} , jdouNum={}",
                        jackpotId,this.room.getRoomId(),userId,
                        scorecardForBet, addJackPot);
            }

            // 查出当前跑马灯奖池
            long current = accountDao.queryHorseRaceLampAccount();

            // 注入跑马灯奖池
            accountDao.updateHorseRaceLampAccount(addJackPot);

            //记录投彩到mongo
            IJackpotRewardDao rewardDao = RepositoriesFactory.getJackpotRewardDao();
            String recordId = rewardDao.saveJackportIntoRecord(userId,nickName,this.room.getDealer().getPlayers().get(userId).getClubId(),this.room.getDealer().getPlayers().get(userId).getTribeId(),this.room.getRoomId(),
                    this.room.getName(),this.room.getRoomPath(),getJackpotId(),this.jackpotConfig.getBlindCode(),
                    this.jackpotConfig.getBlindName(), EJackpotIntoType.INTO_HORSE_RACE_LAMP.getCode(), addJackPot);

            // 记录日志
            PlatformAccountLog platformAccountLog = PlatformAccountLog.builder().changeChip(addJackPot).currentChip(current)//记录日志
                    .externalId(recordId).description("" + room.getRoomId()).opId(userId).build();
            accountDao.insertHorseRaceLampAccountRecordForIntoByDz(platformAccountLog);

        } catch (SQLException e) {
            logger.error("addHorseRaceLamp error.玩家id：【{}】, 玩家昵称：【{}】， ",e);
        }
    }

    /**
     * 投入jpb
     * @param userId
     * @param nickName
     * @param scorecardForBet 记分牌
     */
    private void addJPB(int userId, String nickName, int scorecardForBet, int jackpotId) {
        try {
            // 转换
            int addJackPot = scoreToJackPot(scorecardForBet);
            if(logger.isDebugEnabled()){
                logger.debug("addJPB Jackpot BB : jackpotId={} , roomId={} , userId={} , scorecardNum={} , jdouNum={}",
                        jackpotId,this.room.getRoomId(),userId,
                        scorecardForBet,addJackPot);
            }

            // 查出当前
            long current = accountDao.queryJPBAccount();

            // 注入JPB仓
            accountDao.updateJPBAccount(addJackPot);

            //记录投彩到mongo
            IJackpotRewardDao rewardDao = RepositoriesFactory.getJackpotRewardDao();
            String recordId = rewardDao.saveJackportIntoRecord(userId,nickName,this.room.getDealer().getPlayers().get(userId).getClubId(),this.room.getDealer().getPlayers().get(userId).getTribeId(),this.room.getRoomId(),
                    this.room.getName(),this.room.getRoomPath(),getJackpotId(),this.jackpotConfig.getBlindCode(),
                    this.jackpotConfig.getBlindName(), EJackpotIntoType.INTO_JPB.getCode(), addJackPot);

            // 记录日志
            PlatformAccountLog platformAccountLog = PlatformAccountLog.builder().changeChip(addJackPot).currentChip(current)//记录日志
                    .externalId(recordId).description("" + room.getRoomId()).opId(userId).build();
            accountDao.insertJPBAccountRecordForIntoByDz(platformAccountLog);
        } catch (SQLException e) {
            logger.error("addJPB error.玩家id：【{}】, 玩家昵称：【{}】， ",e);
        }
    }

    /**
     * 玩家jackpot统计
     * @param userId
     * @param nickName
     * @param jpBetPerHand 玩家当前手的应投彩
     * @param realJpBetPerHand 玩家当前手的实际投彩
     */
    private void playerJackpotStatistic(int userId, String nickName, int jpBetPerHand, int realJpBetPerHand) {
        PlayerJackpotStatistic dst = this.playerJackpotMap.get(userId);
        if(null == dst){
            dst = new PlayerJackpotStatistic(userId,nickName);
            this.playerJackpotMap.put(userId,dst);
        }
        dst.addJackpotBet(jpBetPerHand, realJpBetPerHand);
    }


    /**
     *  投入彩池
     * @param userId
     * @param nickName
     * @param scorecardForBet
     */
    private void addjackpotBet(int userId, String nickName, int scorecardForBet, int jackpotId) {
        try {
            // 转换
            int addJackPot = scoreToJackPot(scorecardForBet);
            if(logger.isDebugEnabled()){
                logger.debug("addjackpotBet Jackpot BB : jackpotId={} , roomId={} , userId={} , scorecardNum={} , jdouNum={}",
                        jackpotId,this.room.getRoomId(),userId,
                        scorecardForBet, addJackPot);
            }

            // 查询当前彩池并增加
//            JackpotFundWatchService.IntegerUpdatedResult integerUpdatedResult = JackpotFundWatchService.getInstance().addFundByDelta(jackpotId, addJackPot);

            int current = accountDao.queryAndUpdateJPAccount(scorecardForBet,room.getManzhu(), room.getRoomPath());
            int totalFund=accountDao.queryJPTotalAccount(room.getRoomPath());
            //记录投彩到mongo
            IJackpotRewardDao rewardDao = RepositoriesFactory.getJackpotRewardDao();
            String recordId = rewardDao.saveJackportIntoRecord(userId,nickName,this.room.getDealer().getPlayers().get(userId).getClubId(),
                    this.room.getDealer().getPlayers().get(userId).getTribeId(),this.room.getRoomId(),
                    this.room.getName(),this.room.getRoomPath(),getJackpotId(),this.jackpotConfig.getBlindCode(),
                    this.jackpotConfig.getBlindName(), EJackpotIntoType.INTO_JACKPOT.getCode(), addJackPot);

            // 记录彩池仓日志
            PlatformAccountLog platformAccountLog = PlatformAccountLog.builder().changeChip(addJackPot).currentChip(current)//记录日志
                    .externalId(recordId).description("" + room.getRoomId()).opId(userId).build();
            accountDao.insertJPBetRecordByDz(platformAccountLog);
            updateJK();
        } catch (SQLException e) {
            logger.error("addjackpotBet error. 玩家id：【{}】, 玩家昵称：【{}】，jackpotId：【{}】，scorecardForBet：【{}】。",
            userId, nickName, jackpotId, scorecardForBet);
        }
    }

    /**
     * 记分牌转换金豆
     * @param scorecardForBet
     * @return
     */
    private int scoreToJackPot(int scorecardForBet) {
        IExchange exchange = CurrencyFactory.getInstance().getExchangeService();
        int addJackPot = exchange.scorecard2Jdou(scorecardForBet, ERoundingMode.HALF_UP);
        return addJackPot;
    }

    /**
     * 检查玩家是否击中JP的特殊牌型
     * 击中，则需要奖励玩家
     *
     * 规则:
     * * 已发公共牌
     * * 赢家在所有玩家弃牌前选择亮2张手牌
     * * 击中牌型分别为：1（皇家同花顺） 2 （同花顺） 3（四条）
     * * 玩家两张手牌都组成击中牌型
     *
     * @param userId              玩家ID
     * @param nickName            玩家昵称
     * @param pocerType           玩家当前牌型
     */
    public double rewardFromJackPot(int userId,String nickName,int pocerType,Pocer[] playerMaxPocers,Pocer[] roomPocers) throws SQLException {
        if(pocerType != 1 && pocerType !=2 && pocerType != 3)
            return 0;

        int jackpotId = this.getJackpotId();
        double ratio=0.0;
        if(pocerType == 1){
            ratio = this.jackpotConfig.getRoyalFlushRatio();
        }else if(pocerType == 2){
            ratio = this.jackpotConfig.getStraightFlushRatio();
        }else if(pocerType == 3){
            int publicPocerInHand = 0;//公共牌在最大牌组的数量
            int maxProcerNumber = playerMaxPocers[2].getSize2();//取出四条的数字
            for(Pocer p : roomPocers){//遍历公共牌
                if(p != null){
                    if(p.getSize2() == maxProcerNumber){//公共牌存在几张四条的数字牌
                        publicPocerInHand++;
                    }
                }
            }
            if(publicPocerInHand > 2 ){//公共牌四条的牌出现了两次以上，不识别为击中
                logger.warn("Jackpot with no hit: jackpotId={} , roomId={} , userId={} , pocerType={} , maxPocers={} , roomPocers={}",
                        jackpotId,this.room.getRoomId(),userId,pocerType,
                        Arrays.toString(playerMaxPocers),
                        Arrays.toString(roomPocers));
                return 0;
            }
            ratio = this.jackpotConfig.getFourOfAKindRatio();
        }

        // 该击中牌型的奖池比例必须大于0才给予记录等相关操作
        if(ratio > 0.0){
            //从zk里读取彩金并扣减（废弃掉的）
//        JackpotFundWatchService jpFundZkService = JackpotFundWatchService.getInstance();
            //         JackpotFundWatchService.IntegerUpdatedResult updatedResult = jpFundZkService.deductFundByRatio(jackpotId, ratio);
//            int reward = updatedResult.getIncrValue();
            BigDecimal current = accountDao.queryNewJPBAccount(room.getManzhu(), room.getRoomPath());
            int reward = current.multiply(new BigDecimal(ratio / 100.00)).intValue();
            accountDao.queryAndUpdateJPAccount(-reward,room.getManzhu(), room.getRoomPath());
            // 计算JP奖励的服务费
            double rewardFee = 0;
            if(reward > 0) {
                IFeeService feeService = room.getFeeService();
                if (null != feeService) {
                    rewardFee = feeService.calFeeOfJackpot(reward, ERoundingMode.HALF_UP);
                    reward -= rewardFee;
                }
            }
            try {

                RoomPersion persion = room.getAudMap().get(userId);
                historyDao.insertJP(JPHistory.builder().roomId(room.getRoomId())
                        .roomName(this.room.getName()).bet(reward)
                        .roomPath(room.getRoomPath()).mangzhu(room.getManzhu())
                        .rewardFee((int)rewardFee).userId(userId)
                        .cludId(persion.getUserInfo().getClubId())
                        .cludeProportion(persion.getUserInfo().getClubProportion())
                        .tribeId(persion.getUserInfo().getTribeId())
                        .tcludId(persion.getUserInfo().gettClubId()).paixing(pocerType)
                        .tribeProportion(persion.getUserInfo().getTribeProportion())
                        .version(room.getStage()).build());
            } catch (SQLException throwables) {
                logger.error("彩金保存异常");
                throwables.printStackTrace();
            }
            // 记录【jp奖励】到内存
            // 保存战绩时使用
            PlayerJackpotStatistic jpStatistic = this.playerJackpotMap.get(userId);
            if(null == jpStatistic){
                jpStatistic = new PlayerJackpotStatistic(userId,nickName);
                this.playerJackpotMap.put(userId,jpStatistic);
            }
            jpStatistic.addJackpotReward(reward,rewardFee);

            RoomPersion roomPersion = room.getAudMap().get(userId);
            ChipUtils.updateChipWithNoCommisson(roomPersion,null,reward,room.getRoomId(), ChipsCode.JACKPOT_RECORD);  // 保存【jp奖励】到玩家账户

            // 保存 【jp奖励记录】
            IJackpotRewardDao rewardDao = RepositoriesFactory.getJackpotRewardDao();
            rewardDao.saveJackportRewardRecord(jackpotId,this.jackpotConfig.getBlindCode(),
                    this.jackpotConfig.getBlindName(),userId,nickName,pocerType,this.room.getRoomId(),
                    this.room.getRoomPath(),this.room.getName(),current.intValue(),reward,rewardFee,
                    this.room.getDealer().getPlayers().get(userId).getClubId(),this.room.getDealer().getPlayers().get(userId).getTribeId());

            // 记录【jp奖励记录】到内存
            // 保存战绩时使用
            PlayerJpRewardDetail detail = new PlayerJpRewardDetail(userId,nickName,pocerType,this.room.getStage(),reward,rewardFee);
            this.playerRewardDetailLst.add(detail);

            // 击中奖励大于0才推送
            if(reward > 0){//击中奖励大于0才推送
                String jpId = String.valueOf(this.getJackpotId());
                JackPotUtil.pushJackPotPocerTypeMsgToRes(pocerType,nickName,userId,room.getRoomId(),room.getRoomPath(),jpId,reward,roomPersion.getSize());
                JackPotUtil.pushJackPotPocerTypeMsgToClient(Cache.getJackpotRoomInfo(),nickName,pocerType,reward,room.getRoomId(),roomPersion.getSize());
            }
            //更新彩金池
            updateJK();
            return rewardFee;
        }
        return 0;
    }

    /**
     * 返回玩家JP奖励的明细
     * @return
     */
    public List<PlayerJpRewardDetail> playerRewardDetailLst(){
        return this.playerRewardDetailLst;
    }

    /**
     * 玩家的应投彩
     * @param userId
     * @return
     */
    public int getTotalJpBet(int userId){
        PlayerJackpotStatistic dst = this.playerJackpotMap.get(userId);
        if(null == dst)
            return 0;

        return dst.getTotalJpBet();
    }

    /**
     * 玩家的实际投彩
     * @param userId
     * @return
     */
    public int getTotalRealJpBet(int userId){
        PlayerJackpotStatistic dst = this.playerJackpotMap.get(userId);
        if(null == dst)
            return 0;

        return dst.getTotalRealJpBet();
    }

    /**
     * 玩家的JP奖励
     * @param userId
     * @return
     */
    public int getTotalJpReward(int userId){
        PlayerJackpotStatistic dst = this.playerJackpotMap.get(userId);
        if(null == dst)
            return 0;

        return dst.getTotalJpReward();
    }

    /**
     * 玩家的JP奖励的服务费
     * @param userId
     * @return
     */
    public double getTotalJpRewardFee(int userId){
        PlayerJackpotStatistic dst = this.playerJackpotMap.get(userId);
        if(null == dst)
            return 0;

        return dst.getTotalJpRewardFee();
    }

    /**====== 玩家的每手指标数据 START ======*/
    /**
     * 玩家当前手的应投彩
     * @param userId
     * @return
     */
    public int getCurrentJpBet(int userId){
        PlayerJackpotStatistic dst = this.playerJackpotMap.get(userId);
        if(null == dst)
            return 0;

        return dst.getJpBetPerHand();
    }

    /**
     * 玩家当前手的实际投彩
     * @param userId
     * @return
     */
    public int getCurrentRealJpBet(int userId){
        PlayerJackpotStatistic dst = this.playerJackpotMap.get(userId);
        if(null == dst)
            return 0;

        return dst.getRealJpBetPerHand();
    }

    /**
     * 玩家当前手的JP奖励
     * @param userId
     * @return
     */
    public int getCurrentJpReward(int userId){
        PlayerJackpotStatistic dst = this.playerJackpotMap.get(userId);
        if(null == dst)
            return 0;

        return dst.getJpRewardPerHand();
    }

    /**
     * 玩家当前手的JP奖励的服务费
     * @param userId
     * @return
     */
    public double getCurrentJpRewardFee(int userId){
        PlayerJackpotStatistic dst = this.playerJackpotMap.get(userId);
        if(null == dst)
            return 0;

        return dst.getJpRewardFeePerHand();
    }

    /**
     * 获取牌局的当前手的JP奖励总值
     * @return
     */
    public int getCurrentTotalJpReward(){
        int result = 0;

        if(null != this.playerJackpotMap && !playerJackpotMap.isEmpty()) {
            Iterator<PlayerJackpotStatistic> it = playerJackpotMap.values().iterator();
            while(it.hasNext()){
                PlayerJackpotStatistic value = it.next();
                if(null == value)
                    continue;

                result += value.getJpRewardPerHand();
            }
        }

        return result;
    }
    /**
     * 更新彩金池并通知记录
     */
    private void updateJK(){
        Connection conn = null;
        PreparedStatement stam = null;
        ResultSet rs = null;
        try {
            List<Room> roomList = Cache.getJackpotRoomInfo();
            conn = DBUtil.getConnection();
            String  sql = "select sum(fund) as fund from jackpot_pool where jackpot_type=?";
            String sql1="select jackpot_pool.fund as fund from jackpot_pool inner join jackpot_pool_setting " +
                    "on jackpot_pool.jackpot_id=jackpot_pool_setting.jackpot_id where jackpot_pool.jackpot_type=? " +
                    "and jackpot_pool_setting.blind_code=?";
            stam = conn.prepareStatement(sql);
            int jk_type=0;
            switch (room.getRoomPath()){
                case 61:
                    jk_type=1;
                    break;
                case 91:
                    jk_type=2;
                    break;
                case 63:
                case 93:
                    jk_type=3;
                    break;
                default:
                    jk_type=4;
                    break;
            }
            stam = conn.prepareStatement(sql);
            stam.setInt(1,jk_type);
            rs = stam.executeQuery();
            int totalFund=0;
            int fund=0;
            if (rs.next()){
                totalFund=rs.getBigDecimal("fund").intValue();
            }
            stam = conn.prepareStatement(sql1);
            stam.setInt(1,jk_type);
            stam.setInt(2,room.getManzhu());
            rs = stam.executeQuery();
            if (rs.next()){
                fund=rs.getBigDecimal("fund").intValue();
            }
            byte[] toJackpotRoombytes = null;//推送往对应jackpotroom房间
            byte[] toOtherRoombytes = null;//推送往其他房间
            if(null != roomList && roomList.size() > 0){
                Object[][] objs = {
                        {60, 0, I366ServerPickUtil.TYPE_INT_1},
                        {130, String.valueOf(fund), I366ServerPickUtil.TYPE_STRING_UTF16},//子池数额
                        {131, String.valueOf(totalFund), I366ServerPickUtil.TYPE_STRING_UTF16},//彩池总额

                };
                toJackpotRoombytes = I366ClientPickUtil.packAll(objs, Constant.REQ_JACKPOT_CHANGE);

                Object[][] objs2 = {
                        {60, 0, I366ServerPickUtil.TYPE_INT_1},
                        {130, String.valueOf(totalFund), I366ServerPickUtil.TYPE_STRING_UTF16},//彩池总额
                        {131, "", I366ServerPickUtil.TYPE_STRING_UTF16},//子池数额
                };
                toOtherRoombytes = I366ClientPickUtil.packAll(objs2, Constant.REQ_JACKPOT_CHANGE);
            }
            int jackpotId = this.getJackpotId();
            for(Room room : roomList){
                if(room != null){
                    if(room.getJackpotService().getJackpotId() == jackpotId){
                        PublisherUtil.send(room,toJackpotRoombytes);
                    }else{
                        PublisherUtil.send(room,toOtherRoombytes);
                    }
                }

            }

            logger.debug("notify update jackpot successfully !!!");
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(),e);
        }finally {
            DBUtil.closeStatement(stam);
            DBUtil.closeConnection(conn);
            if (rs != null) {
                DBUtil.closeResultSet(rs);
            }
        }
    }

    /**
     * 获取牌局的当前手的JP头彩总值
     * @return
     */
    public int getCurrentTotalJpBet(){
        int result = 0;

        if(null != this.playerJackpotMap && !playerJackpotMap.isEmpty()) {
            Iterator<PlayerJackpotStatistic> it = playerJackpotMap.values().iterator();
            while(it.hasNext()){
                PlayerJackpotStatistic value = it.next();
                if(null == value)
                    continue;

                result += value.getJpBetPerHand();
            }
        }

        return result;
    }
    /**====== 玩家的每手指标数据 END ======*/
}
