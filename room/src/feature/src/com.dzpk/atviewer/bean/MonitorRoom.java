package com.dzpk.atviewer.bean;

import com.dzpk.common.utils.LogHelper;
import lombok.Getter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@ToString
public class MonitorRoom {
    private final int roomId;
    private final int roomPath;

    // 需派发观众数量
    private final int maxViewerNum;

    // 每次派发完后时变更
    private Date lastUpdatedTime;
    private int nextDispatchSec;
    public void setNextDispatchSec(int nextDispatchSec){
        this.nextDispatchSec = nextDispatchSec;
        this.lastUpdatedTime = new Date();
    }

    // 已派遣的用户列表
    private final List<MonitorUser> dispatchedUserLst;
    public synchronized boolean addViewer(MonitorUser user){
        boolean isAdd = false;
        if(null == user || user.getUserId()<=0)
            return isAdd;

        if(!this.dispatchedUserLst.contains(user)) {
            isAdd = user.addViewedRoomId(this.roomId);
            if(isAdd)
               isAdd = this.dispatchedUserLst.add(user);
            if(!isAdd)
                user.removeViewedRoomId(this.roomId);
        }

        return isAdd;
    }
    public synchronized boolean removeViewer(MonitorUser user){
        boolean isRemoved = false;
        if(null == user || user.getUserId()<=0)
            return isRemoved;

        if(this.dispatchedUserLst.contains(user)) {
            isRemoved = user.removeViewedRoomId(this.roomId);
            if(isRemoved) {
                isRemoved = this.dispatchedUserLst.remove(user);
            }
            if(!isRemoved)
                user.addViewedRoomId(this.roomId);
        }

        return isRemoved;
    }
    public boolean asViewer(int userId){
        return this.dispatchedUserLst.stream().anyMatch(u -> u.getUserId() == userId);
    }

    public MonitorRoom(int roomId,int roomPath,int maxViewerNum,int nextDispatchSec){
        this.roomId = roomId;
        this.roomPath = roomPath;
        this.maxViewerNum = maxViewerNum;
        this.nextDispatchSec = nextDispatchSec;
        this.lastUpdatedTime = new Date();
        this.dispatchedUserLst = new ArrayList<>();
    }

    public boolean enoughViewer(){
        long viewerStillInRoom = dispatchedUserLst.stream().filter(u -> u.getHighlightingRoomId() == roomId).count();
        LogHelper.log("%s   此房间【%s】当前派发观众数量：%s , 预期派发数量：%s",
                System.lineSeparator(),this.roomId,viewerStillInRoom,this.getMaxViewerNum());
        return viewerStillInRoom>=this.getMaxViewerNum();
    }
    public boolean needDispatching(){
        long sysTimeSec = System.currentTimeMillis()/1000;
        long lastTimeSec = this.lastUpdatedTime.getTime()/1000;

        LogHelper.log("%s   此房间【%s】上次派发时间（秒）：%s , 预期派发时间（秒）：%s 【%s】 <= 当前系统时间（秒）：%s",
                System.lineSeparator(),this.roomId,lastTimeSec,
                this.nextDispatchSec,lastTimeSec+this.nextDispatchSec,sysTimeSec);
        return lastTimeSec + this.nextDispatchSec <= sysTimeSec;
    }
}
