package com.dzpk.atviewer.bean;

import com.ai.dz.config.AiRoomManager;
import com.dzpk.atviewer.IViewerManager;
import com.i366.cache.Cache;
import com.i366.model.room.Room;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Getter
@ToString
@Slf4j
public class MonitorUser implements Comparable<MonitorUser> {
    private final int userId;

    // 当前高亮的房间ID
    private int highlightingRoomId = 0;
    private int highlightingRoomPath = 0;

    /**
     * 派发用户到牌局中做观众
     * 如果已经在其它房间中高亮【highlightingRoomId>0】,则取消其高亮
     * 设置参数指定房间中高亮
     *
     * @param room  牌局
     *
     * @return   错误代码
     *    0  = 成功
     *    1  = 已经高亮
     *    2  = 非高亮观众
     */
    public synchronized void dispatchAsViewer(Room room){
        if(null == room){
            return;
        }

        int oldHighlightingRoomId = 0;
        int oldHighlightingRoomPath=0;
        if(room.getDealer().getWatchers().contains(this.userId)){
            oldHighlightingRoomId = this.highlightingRoomId;
            oldHighlightingRoomPath = this.highlightingRoomPath;
            this.highlightingRoomId = room.getRoomId();
            this.highlightingRoomPath = room.getRoomPath();
        }else if(room.getLeaveAud().contains(this.userId)){
            // 什么都不处理
        }else{
            oldHighlightingRoomId = this.highlightingRoomId;
            oldHighlightingRoomPath = this.highlightingRoomPath;
            this.highlightingRoomId = room.getRoomId();
            this.highlightingRoomPath = room.getRoomPath();
            room.getDealer().addWatcher(this.userId);
        }

        // 取消高亮状态
        if(oldHighlightingRoomId>0){
            this.cancelHighlighting(oldHighlightingRoomId,oldHighlightingRoomPath);
        }
    }

    /**
     * Cancel highlighting in the room currently dispatched to.
     */
    public synchronized void cancelHighlighting() {
        cancelHighlighting(null, null);
    }

    /**
     * Cancel highlighting in the given room.
     * @param roomId
     * @param roomPath
     */
    public synchronized void cancelHighlighting(Integer roomId,Integer roomPath){
        int pendingRoomId;
        int pendingRoomPath;
        if(roomId != null){
            pendingRoomId = roomId;
            pendingRoomPath = roomPath;
        }else{
            pendingRoomId = this.highlightingRoomId;
            pendingRoomPath = this.highlightingRoomPath;
            this.highlightingRoomId = 0;
            this.highlightingRoomPath = 0;
        }

        Room room = Cache.getRoom(pendingRoomId, pendingRoomPath);
        if(null == room){
            log.debug("[R-{}][U-{}] [RP-{}]取消高亮 牌局不存在",pendingRoomId,userId,pendingRoomPath);
            return;
        }
        if(room.getFinished()){
            log.debug("[R-{}][U-{}] [RP-{}]取消高亮 牌局解散中",room.getRoomId(),userId,pendingRoomPath);
            return;
        }

        boolean highlightingRemoved = false;
        boolean unhighlightingAdd = false;
        if(room.getDealer().getWatchers().contains(this.userId)){
            highlightingRemoved = true;
            room.getDealer().removeWatcher(this.userId);
            log.debug("[R-{}][U-{}] 取消高亮（观众离开，已经从观众列表移除）",room.getRoomId(),userId);
        }
        //LeaveUser是指不在房间内的观众，heckIfUndispatched检查是否已被派遣到打牌，这里是指没有派遣打牌
        //这里的业务意义应该系 当移除了观众后，应该将其移出到"不在房间内的观众"列表中，以便于在Request_185_GameReport实时战况列表中得到正确的在线观众
        if(!room.getLeaveAud().contains(this.userId) && AiRoomManager.checkIfUndispatched(this.userId)){//？
            unhighlightingAdd = true;
            log.debug("[R-{}][U-{}] 取消高亮 加入不在房间内的观众列表",room.getRoomId(),userId);
            room.addLeaveUser(this.userId);
        }
        IViewerManager.getInstance().onViewerStateChange(userId, pendingRoomId, IViewerManager.ViewerState.OFF);
        log.debug("[R-{}][U-{}][RP-{}] 取消高亮 最终结果： highlightingRemoved={}，unhighlightingAdd={}",
                pendingRoomId,this.userId,pendingRoomPath,
                highlightingRemoved,unhighlightingAdd);
    }

    // 当前派遣到的房间列表
    private final List<Integer> viewedRoomIdLst = new ArrayList<>();
    public synchronized boolean addViewedRoomId(int roomId){
        boolean isAdd = false;

        if(!this.viewedRoomIdLst.contains(roomId)) {
            isAdd = this.viewedRoomIdLst.add(roomId);
        }

        return isAdd;
    }
    public synchronized boolean removeViewedRoomId(int roomId){
        boolean isRemoved = false;

        if(this.viewedRoomIdLst.contains(roomId)) {
            isRemoved = this.viewedRoomIdLst.remove((Object)roomId);
            if(this.highlightingRoomId>0 && this.highlightingRoomId == roomId)
                this.highlightingRoomId = roomId;
        }

        return isRemoved;
    }

    public MonitorUser(int userId){
        this.userId = userId;
    }

    @Override
    public int compareTo(MonitorUser that) {
        return Integer.compare(this.userId, that.getUserId());
    }
}
