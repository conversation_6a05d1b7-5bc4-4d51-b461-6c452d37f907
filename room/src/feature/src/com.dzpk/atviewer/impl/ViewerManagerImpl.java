package com.dzpk.atviewer.impl;

import com.ai.dz.config.AiRoomManager;
import com.ai.dz.config.EnterRoomAiPlayer;
import com.dzpk.atviewer.IViewerManager;
import com.dzpk.atviewer.bean.MonitorRoom;
import com.dzpk.atviewer.bean.MonitorUser;
import com.dzpk.atviewer.cache.IViewerConfigCache;
import com.dzpk.common.utils.Helper;
import com.dzpk.common.utils.LogHelper;
import com.dzpk.component.repositories.redis.RedisService;
import com.i366.cache.Cache;
import com.i366.model.player.RoomPersion;
import com.i366.model.room.Room;
import com.work.comm.s2s.client.AppConfig;
import com.work.comm.s2s.client.ServerManager;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.JedisPubSub;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
public class ViewerManagerImpl implements IViewerManager {

    private boolean needDispatch(int roomPath) {
        boolean allow = false;

        if (null == this.configCache.getDispatchConfig()) {
            log.debug("[RP-{}] 派遣观众 配置不存在",roomPath);
            return false;
        }
        if (!this.configCache.getDispatchConfig().getEnable()) {
            log.debug("[RP-{}] 派遣观众 配置派遣观众功能关闭",roomPath);
            return false;
        }
        if (null == this.configCache.getDispatchConfig().getAllowRoomPaths() ||
                this.configCache.getDispatchConfig().getAllowRoomPaths().isEmpty()) {
            log.debug("[RP-{}] 派遣观众 牌局类型配置不存在",roomPath);
            return false;
        }

        allow = this.configCache.getDispatchConfig().getAllowRoomPaths().contains(roomPath);
        return allow;
    }

    private int randomViewerNum() {
        int min = 6;
        int max = 30;
        if (null != this.configCache.getDispatchConfig()) {
            min = this.configCache.getDispatchConfig().getViewerMinNum();
            max = this.configCache.getDispatchConfig().getViewerMaxNum();
        }
        return Helper.randomIntBy(min, max);
    }

    private int randomDispatchSec() {
        int min = 60;
        int max = 300;
        if (null != this.configCache.getDispatchConfig()) {
            min = this.configCache.getDispatchConfig().getDispatchMinSec();
            max = this.configCache.getDispatchConfig().getDispatchMaxSec();
        }
        return Helper.randomIntBy(min, max);
    }

    private int randomCancelSec() {
        int min = 50;
        int max = 350;
        if (null != this.configCache.getDispatchConfig()) {
            min = this.configCache.getDispatchConfig().getCancelOnlineMinSec();
            max = this.configCache.getDispatchConfig().getCancelOnlineMaxSec();
        }
        return Helper.randomIntBy(min, max);
    }

    private int randomKickOutSec() {
        int min = 50;
        int max = 350;
        if (null != this.configCache.getDispatchConfig()) {
            min = this.configCache.getDispatchConfig().getEmptyRoomKickOutMinSec();
            max = this.configCache.getDispatchConfig().getEmptyRoomKickOutMaxSec();
        }
        return Helper.randomIntBy(min, max);
    }

    // 监控房间列表
    private final Map<Integer, MonitorRoom> roomMap = new ConcurrentHashMap<>();
    // 已经派遣列表
    private final Map<Integer, MonitorUser> userMap = new ConcurrentHashMap<>();

    // 分派观众定时器
    private final Timer timer;

    // 取消高亮定时器
    private Date lastCancelledTime;
    private int nextCancelSec;

    private boolean needCancelOnline() {
        long sysTimeSec = System.currentTimeMillis() / 1000;
        long lastTimeSec = this.lastCancelledTime.getTime() / 1000;

        log.debug("上次取消高亮时间（秒）：{} , 预期取消高亮时间（秒）：{} 【{}】 <= 当前系统时间（秒）：{}",
                lastTimeSec,
                this.nextCancelSec, lastTimeSec + this.nextCancelSec, sysTimeSec);
        return lastTimeSec + this.nextCancelSec <= sysTimeSec;
    }

    private void setNextCancelSec(int nextCancelSec) {
        this.nextCancelSec = nextCancelSec;
        this.lastCancelledTime = new Date();
    }

    // 清理空房观众定时器
    private Date lastKickOutTime;
    private int nextKickOutSec;

    private boolean needCheckEmptyRoom() {
        long sysTimeSec = System.currentTimeMillis() / 1000;
        long lastTimeSec = this.lastKickOutTime.getTime() / 1000;

        log.debug("上次检查是否有空房的时间（秒）：{} , 预期再次检查是否有空房的时间（秒）：{} 【{}】 <= 当前系统时间（秒）：{}",
                lastTimeSec,
                this.nextKickOutSec, lastTimeSec + this.nextKickOutSec, sysTimeSec);
        return lastTimeSec + this.nextKickOutSec <= sysTimeSec;
    }

    private void setNextKickOutSec(int nextKickOutSec) {
        this.nextKickOutSec = nextKickOutSec;
        this.lastKickOutTime = new Date();
    }

    // 配置缓存
    private final IViewerConfigCache configCache;

    public ViewerManagerImpl() {
        this.configCache = IViewerConfigCache.getInstance();
        int periodSec = 10_000;
        if (null != this.configCache.getDispatchConfig())
            periodSec = 1000 * this.configCache.getDispatchConfig().getTimerPeriodSec();
        this.timer = new Timer("ViewerDispatcher", true);
        this.timer.schedule(new TimerTask() {
            @Override
            public void run() {
                dispatchViewer();
            }
        }, periodSec, periodSec);
        int nextCancelSec = this.randomCancelSec();
        this.setNextCancelSec(nextCancelSec);
        this.timer.schedule(new TimerTask() {
            @Override
            public void run() {
                //随机找出一个观众取消高亮
                //（这里用了一个列表管理了所有房间的观众，这里的取消高亮是指获取所有房间中任意一个观众取消高亮）
                //cancelHighlighting();
                //PP80-FE2 遍历所有房间并分别在各房间随机找出一个观众踢出（取消高亮）
                try {
                    forEachAllRoomToKickOutOne();
                } catch (Exception ex) {
                    log.error("forEachAllRoomToKickOutOne error", ex);
                }
            }
        }, periodSec, periodSec);

        int nextKickOutSec = this.randomKickOutSec();
        this.setNextKickOutSec(nextKickOutSec);
        this.timer.schedule(new TimerTask() {
            @Override
            public void run() {
                //检查是否有空房
                //PP80-FE1 遍历所有房间，发现房间是空房，那么就清空该房间的观众（即将该房间的观众全部取消高亮）
                try {
                    checkRoomEmpty();
                } catch (Exception ex) {
                    log.error("checkRoomEmpty error", ex);
                }
            }
        }, periodSec, periodSec);

        RedisService.getRedisService().subscribe(new Watcher(), getWatcherChannel()).start();
    }

    private String getWatcherChannel() {
        return getClass().getName();
    }

    private void notifyViewerManagers(int userId, int roomId) {
        String message = String.format("%s,%s,%s", userId, roomId, getRoomServerId());
        log.debug("sending msg - {}", message);
        RedisService.getRedisService().publish(getWatcherChannel(), message);
    }

    private class Watcher extends JedisPubSub {
        @Override
        public void onMessage(String channel, String message) {
            log.debug("received msg - {}", message);
            String[] fields = message.split(",");
            if (fields.length == 3) {
                int userId = Integer.parseInt(fields[0]);
                int roomId = Integer.parseInt(fields[1]);
                String serverId = fields[2];
                if (getRoomServerId().equals(serverId)) {
                    // skip messages from ourselves
                    return;
                }
                MonitorUser user = userMap.get(userId);
                if (null != user) {
                    log.info("Viewer state changed - userId={} roomId={} serverId={}", userId, roomId, serverId);
                    if (user.getHighlightingRoomId() > 0) {
                        user.cancelHighlighting();
                    }
                }
            }
        }
    }

    private String getRoomServerId() {
        AppConfig appConfig = ServerManager.getInstance().getAppConfig();
        return appConfig.getAppId();
    }

    public void onViewerStateChange(int userId, int roomId, ViewerState state) {
        if (state == ViewerState.ON) {
            RedisService.getRedisService().addAiViewer(userId, roomId, getRoomServerId());
        } else {
            RedisService.getRedisService().removeAiViewer(userId, roomId, getRoomServerId());
        }
    }

    public void destroy() {
        RedisService.getRedisService().clearAiViewers(getRoomServerId());
    }

    private void checkRoomEmpty() {
        log.debug("检查是否有空房间（房间没有玩家打牌），是空房则清空房间观众列表");
        // 未到时间不需要
        if (!this.needCheckEmptyRoom()) {
            log.debug("检查是否有空房(房间没有玩家打牌,有则踢走该房所有观众）任务时间未到");
            return;
        }
        // 重置下次执行时间
        int nextKickOutSec = this.randomKickOutSec();
        this.setNextKickOutSec(nextKickOutSec);
        log.debug("检查是否有空房间（房间没有玩家打牌） 下次执行时间（秒）：{}",nextKickOutSec);

        // 获取未派够人数，并且已经到了派送时间的房间列表
        List<MonitorRoom> dispatchLst = new ArrayList<>(this.roomMap.values());
        if (!dispatchLst.isEmpty()) {
            for (MonitorRoom monitorRoom : dispatchLst) {
                int roomId = monitorRoom.getRoomId();
                Room room = Cache.getRoom(roomId, monitorRoom.getRoomPath());
                if (null == room) {
                    log.debug("[R-{}] 牌局未初始化或已解散", roomId);
                    continue;
                }
                if (roomIsEmpty(room)) {
                    log.debug("[R-{}]清空房间观众列表", room.getRoomId());
                    kickOutAllOfWatchers(room);
                } else {
                    log.debug("[R-{}]当前房间有玩家在玩牌，不用清空观众列表", room.getRoomId());
                }
            }
        }else {
            log.debug("检查是否有空房间（房间没有玩家打牌），当前没有可用房间");
        }
    }
    private void kickOutAllOfWatchers(Room room) {
        try {
            log.debug("[R-{}]踢出所有观众（取消高亮）",room.getRoomId());
            // 获取高亮用户列表
            List<MonitorUser> roomHighlightingUserLst = this.userMap.values().stream()
                    .filter(e -> (e.getHighlightingRoomId() > 0 && e.getHighlightingRoomId() == room.getRoomId()))
                    .collect(Collectors.toList());
            if (!roomHighlightingUserLst.isEmpty()) {
                roomHighlightingUserLst.forEach(pendingUser -> {
                    // 检查房间状态
                    int roomId = pendingUser.getHighlightingRoomId();
                    if (roomId == 0) {
                        log.debug("[R-{}][U-{}] 用户高亮状态的房间ID已不存在",roomId, pendingUser.getUserId());
                        return;
                    }
                    MonitorRoom monitorRoom = this.roomMap.get(roomId);
                    if (null == monitorRoom) {
                        log.debug("[R-{}][U-{}] 用户高亮状态的房间已解散",roomId, pendingUser.getUserId());
                        return;
                    }
                    // 取消高亮
                    pendingUser.cancelHighlighting(null,null);
                });
            }
        } catch (Exception ex) {
            log.debug("[R-{}]踢出所有观众 清理观众列表失败:{}",room.getRoomId(),ex.getMessage());
        }
    }
    private boolean roomIsEmpty(Room room) {
        int playerCount = 0;//所有玩家数量，不论AI玩家还是真实玩家
        for (int i = 0; i < room.getRoomPersions().length; i++) {
            RoomPersion roomPersion = room.getRoomPersions()[i];
            if (roomPersion != null){
                playerCount++;
            }
        }
        return playerCount == 0;
    }
    private void forEachAllRoomToKickOutOne() {
        log.debug("遍历所有房间，并随机找出一个观众踢出");
        // 未到时间不需要
        if (!this.needCancelOnline()) {
            log.debug("遍历所有房间，并随机找出一个观众踢出 取消观众高亮状态（踢出观众列表）任务时间未到");
            return;
        }
        // 重置下次执行时间
        int nextCancelSec = this.randomCancelSec();
        this.setNextCancelSec(nextCancelSec);
        log.debug("遍历所有房间，并随机找出一个观众踢出 取消观众高亮状态 下次执行时间（秒）：{}",nextCancelSec);

        List<MonitorRoom> dispatchLst = new ArrayList<>(this.roomMap.values());
        if (!dispatchLst.isEmpty()) {
            for (MonitorRoom monitorRoom : dispatchLst) {
                int roomId = monitorRoom.getRoomId();
                Room room = Cache.getRoom(roomId, monitorRoom.getRoomPath());
                if (room != null) {
                    kickOutOneOfWatchers(room);
                }
            }
        }else {
            log.debug("遍历所有房间，并随机找出一个观众踢出 当前没有可用房间");
        }
    }
    private void kickOutOneOfWatchers(Room room) {
        try {
            log.debug("[R-{}]踢出房间某观众（取消高亮）",room.getRoomId());
            // 获取高亮用户列表
            List<MonitorUser> roomHighlightingUserLst = this.userMap.values().stream()
                    .filter(e -> (e.getHighlightingRoomId() > 0 && e.getHighlightingRoomId() == room.getRoomId()))
                    .collect(Collectors.toList());
            if (!roomHighlightingUserLst.isEmpty()) {
                // 获取高亮用户列表
                MonitorUser pendingUser = roomHighlightingUserLst.get(Helper.randomIntBy(0, roomHighlightingUserLst.size() - 1));
                // 踢出观众
                kickOutWatcher(pendingUser);
            }
        } catch (Exception ex) {
            log.debug("[R-{}]踢出观众失败:{}",room.getRoomId(),ex.getMessage());
        }
    }
    private void kickOutWatcher(MonitorUser pendingUser) {
        if (null == pendingUser) {
            log.debug("观众管理 未找到高亮用户");
            return;
        }
        // 检查房间状态
        int roomId = pendingUser.getHighlightingRoomId();
        if (roomId == 0) {
            log.debug("[R-{}][U-{}] 用户高亮状态的房间ID已不存在",roomId, pendingUser.getUserId());
            return;
        }
        MonitorRoom monitorRoom = this.roomMap.get(roomId);
        if (null == monitorRoom) {
            log.debug("[R-{}][U-{}] 用户高亮状态的房间已解散",roomId, pendingUser.getUserId());
            return;
        }
        // 取消高亮
        pendingUser.cancelHighlighting(null,null);
    }

    /**
     * 手动踢出某个观众
     *
     * @param roomId    牌局ID，必填
     * @param userId  踢出观众USERID，必填
     *
     */
    public void kickOutWatcher(int roomId,int userId) {
        try {
            log.debug("[R-{}][U-{}][手动踢出观众]踢出房间某观众（取消高亮）",roomId,userId);
            // 获取高亮用户列表
            List<MonitorUser> roomHighlightingUserLst = this.userMap.values().stream()
                    .filter(e -> (e.getHighlightingRoomId() > 0 && e.getHighlightingRoomId() == roomId))
                    .collect(Collectors.toList());
            if (!roomHighlightingUserLst.isEmpty()) {
                MonitorUser matchedUser = roomHighlightingUserLst.stream()
                        .filter(user -> user.getUserId() == userId)
                        .findFirst()
                        .orElse(null);

                // 踢出观众
                kickOutWatcher(matchedUser);
            }
        } catch (Exception ex) {
            log.debug("[R-{}][U-{}][手动踢出观众]踢出观众失败:{}",roomId,userId,ex.getMessage());
        }
    }

    /**
     * 取消已高亮的状态
     */
    @Deprecated //pp80-fe2
    private void cancelHighlighting() {
        StringBuilder traceMsg = LogHelper.createLog();
        traceMsg.append("AT观众管理器-取消观众高亮");
        Throwable throwEx = null;

        try {
            // 未到时间不需要
            if (!this.needCancelOnline()) {
                log.debug("观众管理 取消观众高亮状态（踢出观众列表）任务时间未到");
                return;
            }

            // 重置下次执行时间
            int nextCancelSec = this.randomCancelSec();
            this.setNextCancelSec(nextCancelSec);
            log.debug("观众管理 取消观众高亮状态 下次执行时间（秒）：{}",nextCancelSec);

            // 获取高亮用户列表
            MonitorUser pendingUser = null;
            List<MonitorUser> highlightingUserLst = this.userMap.values().stream()
                    .filter(e -> e.getHighlightingRoomId() > 0)
                    .collect(Collectors.toList());
            if (!highlightingUserLst.isEmpty()) {
                pendingUser = highlightingUserLst.get(Helper.randomIntBy(0, highlightingUserLst.size() - 1));
            }
            if (null == pendingUser) {
                log.debug("观众管理 未找到高亮用户");
                return;
            }

            // 检查房间状态
            int roomId = pendingUser.getHighlightingRoomId();
            if (roomId == 0) {
                log.debug("[R-{}][U-{}] 用户高亮状态的房间ID已不存在",roomId, pendingUser.getUserId());
                return;
            }
            MonitorRoom monitorRoom = this.roomMap.get(roomId);
            if (null == monitorRoom) {
                log.debug("[R-{}][U-{}] 用户高亮状态的房间已解散",roomId, pendingUser.getUserId());
                return;
            }

            // 取消高亮
            pendingUser.cancelHighlighting();
        } catch (Exception ex) {
            throwEx = ex;
            LogHelper.log("%s   执行失败：%s",
                    System.lineSeparator(), ex.getMessage());
        } finally {
            String msg = "";
            if (null != traceMsg) {
                LogHelper.removeLog();
                msg = traceMsg.toString();
            }
            if (null != throwEx) {
                log.error(msg, throwEx);
            } else {
                log.info(msg);
            }
        }
    }

    /**
     * 检查需要派发观众的房间，并且派发AT用户
     * 由定时服务调用
     */
    private void dispatchViewer() {
        StringBuilder traceMsg = LogHelper.createLog();
        traceMsg.append("AT观众管理器-派发观众");
        Throwable throwEx = null;

        try {
            if (this.roomMap.isEmpty()) {
                log.debug("派遣观众 需派遣观众的牌局不存在");
                return;
            }

            // 获取AT用户列表 （PP80 条件之一：所有啟用AI都能被派遣至牌局打牌/成為觀眾）
            List<Integer> atUserIdLst = AiRoomManager.getAllUserOfAt();
            if (null == atUserIdLst || atUserIdLst.isEmpty()) {
                log.debug("派遣观众 无可派发的AT用户");
                return;
            }
            log.debug("派遣观众 启用状态的AT用户数量：{}",atUserIdLst.size());
            // 已关闭的房间列表
            List<MonitorRoom> closedRoomLst = new ArrayList<>();

            // 获取未派够人数，并且已经到了派送时间的房间列表
            List<MonitorRoom> dispatchLst = this.roomMap.values().stream()
                    .filter(e -> (!e.enoughViewer()) && e.needDispatching())
                    .collect(Collectors.toList());
            if (!dispatchLst.isEmpty()) {
                for (MonitorRoom monitorRoom : dispatchLst) {
                    Integer roomId = monitorRoom.getRoomId();

                    Room room = Cache.getRoom(roomId, monitorRoom.getRoomPath());
                    if (null == room) {
                        log.debug("[R-{}][RP-{}]派遣观众 当前牌局已被解散",roomId,monitorRoom.getRoomPath());
                        MonitorRoom temp = this.roomMap.remove(roomId);
                        if (null != temp) {
                            closedRoomLst.add(temp);
                        }
                        continue;
                    }
                    if (room.getFinished()) {
                        log.debug("[R-{}][RP-{}]派遣观众 当前牌局解散中..",roomId,monitorRoom.getRoomPath());
                        MonitorRoom temp = this.roomMap.remove(roomId);
                        if (null != temp) {
                            closedRoomLst.add(temp);
                        }
                        continue;
                    }

                    if (roomIsEmpty(room)) {
                        //不派遣观众
                        log.debug("[R-{}]派遣观众 房间里没有玩家打牌，则不派遣观众",room.getRoomId());
                        continue;
                    }

                    //计算下次派遣时间
                    int nextDispatchSec = this.randomDispatchSec();
                    //PP80 条件之一：加入的俱樂部屬於創建該房間的聯盟
                    List<Integer> resultUserIdLst = AiRoomManager.filterFromClubUserIdLst(room,atUserIdLst);
                    if (null == resultUserIdLst || resultUserIdLst.isEmpty()) {
                        log.debug("[R-{}] (dispatch watchers) tribe of user-matched's club invalid ！",room.getRoomId());
                        continue;
                    }
                    log.debug("[R-{}]派遣观众 加入的俱樂部屬於創建該房間的聯盟的AT用户数量：{}",roomId,atUserIdLst.size());
                    // 确定派遣的列表
                    // 未派遣到过此房间
                    List<Integer> availUserIdLst = resultUserIdLst.stream()
                            .filter(uid -> !monitorRoom.asViewer(uid) && AiRoomManager.checkIfUndispatchedGlobally(uid))
                            .filter(uid -> !RedisService.getRedisService().isAiViewerActiveElsewhere(uid, getRoomServerId()))
                            .collect(Collectors.toList());
                    Integer pendingUserId = null;
                    if (!availUserIdLst.isEmpty()) {
                        pendingUserId = availUserIdLst.get(Helper.randomIntBy(0, availUserIdLst.size() - 1));
                    }
                    if (null == pendingUserId) {
                        monitorRoom.setNextDispatchSec(nextDispatchSec);
                        log.debug("[R-{}][RP-{}]派遣观众 无法为牌局找到可派发的观众用户,可派发用户数：{}-> {}秒后再次派发",
                                roomId, monitorRoom.getRoomPath(),
                                availUserIdLst.size(), nextDispatchSec);
                        continue;
                    }

                    synchronized (this.userMap) {
                        if (room.getFinished()) {
                            LogHelper.log("%s   牌局解散中：rid=%s，rpath=%s，可派发用户数：%s",
                                    System.lineSeparator(), roomId, monitorRoom.getRoomPath(),
                                    availUserIdLst.size());
                            MonitorRoom temp = this.roomMap.remove(roomId);
                            if (null != temp) {
                                closedRoomLst.add(temp);
                            }
                            continue;
                        }

                        // 初始化结构
                        boolean isNew = false;
                        MonitorUser monitorUser = this.userMap.get(pendingUserId);
                        if (null == monitorUser) {
                            monitorUser = new MonitorUser(pendingUserId);
                            this.userMap.put(monitorUser.getUserId(), monitorUser);
                            isNew = true;
                        }
                        boolean isAdded = monitorRoom.addViewer(monitorUser);
                        if (!isAdded) {
                            LogHelper.log("%s   牌局派发观众失败，设置相关数据不成功：rid=%s，rpath=%s，uid=%s",
                                    System.lineSeparator(), roomId, monitorRoom.getRoomPath(), monitorUser.getUserId());
                            if (isNew)
                                this.userMap.remove(monitorUser.getUserId());
                            continue;
                        }

                        // 设置为观众及高亮状态
                        if (AiRoomManager.checkIfUndispatched(monitorUser.getUserId())) {
                            monitorUser.dispatchAsViewer(room);
                            monitorRoom.setNextDispatchSec(nextDispatchSec);
                            onViewerStateChange(monitorUser.getUserId(), room.getRoomId(), ViewerState.ON);
                            LogHelper.log("%s   牌局派发观众成功：rid=%s，rpath=%s，uid=%s -> %s秒后再次派发",
                                    System.lineSeparator(),
                                    roomId, monitorRoom.getRoomPath(),
                                    monitorUser.getUserId(), nextDispatchSec);
                        } else {
                            LogHelper.log("%s   牌局派发观众失败，派发的用户已派遣打牌：rid=%s，rpath=%s，uid=%s",
                                    System.lineSeparator(), roomId, monitorRoom.getRoomPath(), monitorUser.getUserId());
                            monitorRoom.removeViewer(monitorUser);
                            if (isNew)
                                this.userMap.remove(monitorUser.getUserId());
                        }
                    }
                }
            }

            // 处理已结束的房间
            for (MonitorRoom room : closedRoomLst) {
                this.processCloseRoom(room);
            }
        } catch (Exception ex) {
            throwEx = ex;
            LogHelper.log("%s   执行失败：%s", System.lineSeparator(), throwEx.getMessage());
        } finally {
            String msg = "";
            if (null != traceMsg) {
                LogHelper.removeLog();
                msg = traceMsg.toString();
            }
            if (null != throwEx) {
                log.error(msg, throwEx);
            } else {
                log.info(msg);
            }
        }
    }

    /**
     * 检查指定用户是否只被派遣到指定房间
     * 是则释放此用户
     *
     * @param user
     * @param roomId
     */
    private boolean checkAndReleaseIfOnlyRoom(MonitorUser user, int roomId) {
        boolean releaseOk = false;
        if (null == user)
            return releaseOk;

        synchronized (this.userMap) {
            user.removeViewedRoomId(roomId);
            if (user.getViewedRoomIdLst().isEmpty()) {
                this.userMap.remove(user.getUserId());
                releaseOk = true;
            }
        }

        return releaseOk;
    }

    /** 上层方法 */
    /**
     * 将需要派发观众的牌局加入派发队列
     * 初始化牌局时调用
     * <p>
     * 准入条件：
     * 指定类型的牌局
     * 查找到Room对象，并且非结束状态
     *
     * @param roomId   牌局ID，必填
     * @param roomPath 牌局类型，必填
     */
    public void onInitOfRoom(int roomId, int roomPath) {
        StringBuilder traceMsg = LogHelper.createLog();
        traceMsg.append(String.format("AT观众管理器-牌局初始化事件: rid=%s, rpath=%s", roomId, roomPath));
        Throwable throwEx = null;

        try {
            // 1.检查是否需要派发AT观众
            if (!needDispatch(roomPath)) {
                return;
            }

            // 2.检查是否关闭
            Room room = Cache.getRoom(roomId, roomPath);
            if (null == room) {
                LogHelper.log("%s   牌局未初始化或已解散", System.lineSeparator());
                return;
            }
            if (room.getFinished()) {
                LogHelper.log("%s   牌局正在解散", System.lineSeparator());
                return;
            }

            // 3.随机观众数
            //   随机下一次派发时间
            int viewerNum = this.randomViewerNum();
            int nextDispatchSec = this.randomDispatchSec();
            MonitorRoom monitorRoom = new MonitorRoom(roomId, roomPath, viewerNum, nextDispatchSec);
            LogHelper.log("%s   预期派发观众的时间（秒）：%s, 最大观众数量：%s",
                    System.lineSeparator(), nextDispatchSec, viewerNum);

            // 4.加入监控列表
            MonitorRoom result = this.roomMap.putIfAbsent(monitorRoom.getRoomId(), monitorRoom);
            if (result != null && result != monitorRoom) {
                LogHelper.log("%s   牌局已存在派发队列中：%s",
                        System.lineSeparator(), result);
                return;
            }

            LogHelper.log("%s   牌局放入派发队列中：%s",
                    System.lineSeparator(), monitorRoom.toString());
        } catch (Exception ex) {
            throwEx = ex;
            LogHelper.log("%s   执行失败：%s",
                    System.lineSeparator(), ex.getMessage());
        } finally {
            String msg = "";
            if (null != traceMsg) {
                LogHelper.removeLog();
                msg = traceMsg.toString();
            }
            if (null != throwEx) {
                log.error(msg, throwEx);
            } else {
                log.info(msg);
            }
        }
    }

    /**
     * 移除派发观众的牌局
     * 牌局关闭时调用
     * <p>
     * 准入条件：
     * 牌局在派发监控中
     * 移除牌局，不在监控
     *
     * @param roomId   牌局ID，必填
     * @param roomPath 牌局类型，必填
     */
    public void onCloseOfRoom(int roomId, int roomPath) {
        StringBuilder traceMsg = LogHelper.createLog();
        traceMsg.append("AT观众管理器-牌局关闭事件");
        Throwable throwEx = null;

        try {
            // 1.从派送队列中移除
            MonitorRoom room = this.roomMap.remove(roomId);
            this.processCloseRoom(room);
        } catch (Exception ex) {
            throwEx = ex;
            LogHelper.log("%s   执行失败：%s",
                    System.lineSeparator(), ex.getMessage());
        } finally {
            String msg = "";
            if (null != traceMsg) {
                LogHelper.removeLog();
                msg = traceMsg.toString();
            }
            if (null != throwEx) {
                log.error(msg, throwEx);
            } else {
                log.info(msg);
            }
        }
    }

    private void processCloseRoom(MonitorRoom room) {
        try {
            if (null == room) {
                LogHelper.log("%s   牌局不在派发队列中", System.lineSeparator());
                return;
            }

            int roomId = room.getRoomId();
            int roomPath = room.getRoomPath();
            LogHelper.log("%s   牌局：rid=%s，rpath=%s", System.lineSeparator(), roomId, roomPath);

            // 1.派发到此房间的用户是否存在
            if (null == room.getDispatchedUserLst() || room.getDispatchedUserLst().isEmpty()) {
                LogHelper.log("%s   牌局的派发观众列表为空", System.lineSeparator());
                return;
            }

            // 2.将派发到此房间的观众用户的牌局列表中移除此牌局
            for (MonitorUser user : room.getDispatchedUserLst()) {
                RedisService.getRedisService().removeAiViewer(user.getUserId(), room.getRoomId(), getRoomServerId());

                if (!userMap.containsKey(user.getUserId())) {
                    LogHelper.log("%s  派遣的观众不存在：uid=%s", System.lineSeparator(), user.getUserId());
                    continue;
                }

                // 检查是否只派遣到此房间，是则将用户从已派遣列表中移除
                boolean isReleased = this.checkAndReleaseIfOnlyRoom(user, room.getRoomId());
                LogHelper.log("%s  将牌局从用户的牌局列表中移除：uid=%s，是否删除结构=%s", System.lineSeparator(), user.getUserId(), isReleased);
            }
        } catch (RuntimeException ex) {
            log.error("processCloseRoom error", ex);
            throw ex;
        }
    }

    /**
     * 用户成功坐下时调用
     * 用于取消作为观众的高亮状态
     * <p>
     * 准入条件：
     * 用户必须是AT用户
     * 牌局必须是需分派观众的房间
     *
     * @param userId   用户ID，必填
     * @param roomId   牌局ID，必填
     * @param roomPath 牌局类型，可选
     */
    public void onSeatOfRoom(int userId, int roomId, int roomPath) {
        StringBuilder traceMsg = LogHelper.createLog();
        traceMsg.append(String.format("AT观众管理器-坐下事件：rid=%s，rpath=%s,uid=%s", roomId, roomPath, userId));
        Throwable throwEx = null;

        try {
            // 检查是否AT用户
            EnterRoomAiPlayer atPlayer = AiRoomManager.getPlayerOfAuto(roomId, userId);
            if (null == atPlayer) {
                LogHelper.log("%s   玩家非AT用户", System.lineSeparator());
                return;
            }

            // 检查房间是否派送观众
            MonitorRoom monitorRoom = this.roomMap.get(roomId);
            if (null == monitorRoom) {
                LogHelper.log("%s   房间不在派送观众牌局队列中", System.lineSeparator());
                return;
            }

            // 执行业务
            synchronized (this.userMap) {
                // 加载作为观众的身份对象
                MonitorUser monitorUser = this.userMap.get(userId);

                // 还不是观众，则不用再处理
                if (null == monitorUser) {
                    LogHelper.log("%s   用户未派送为观众", System.lineSeparator());
                } else {
                    // 取消高亮
                    monitorUser.cancelHighlighting();
                }
            }

            notifyViewerManagers(userId, roomId);
        } catch (Exception ex) {
            throwEx = ex;
            LogHelper.log("%s   执行失败：%s",
                    System.lineSeparator(), ex.getMessage());
        } finally {
            String msg = "";
            if (null != traceMsg) {
                LogHelper.removeLog();
                msg = traceMsg.toString();
            }
            if (null != throwEx) {
                log.error(msg, throwEx);
            } else {
                log.info(msg);
            }
        }
    }

    /**
     * 用户成功站起是调用
     * <p>
     * 准入条件：
     * 用户必须是AT用户
     * 牌局必须是需分派观众的房间
     *
     * @param userId   用户ID，必填
     * @param roomId   牌局ID，必填
     * @param roomPath 牌局类型，可选
     */
    public void onUnSeatOfRoom(int userId, int roomId, int roomPath) {
        // 暂时不需要
       /* StringBuilder traceMsg = LogHelper.createLog();
        traceMsg.append(String.format("AT观众管理器-离座事件：rid=%s，rpath=%s,uid=%s", roomId, roomPath, userId));
        Throwable throwEx = null;

        try {
            LogHelper.log("%s   业务不需要",System.lineSeparator());
        } catch (Exception ex) {
            throwEx = ex;
            LogHelper.log("%s   执行失败：%s",
                    System.lineSeparator(), ex.getMessage());
        } finally {
            String msg = "";
            if (null != traceMsg) {
                LogHelper.removeLog();
                msg = traceMsg.toString();
            }
            if (null != throwEx) {
                log.error(msg, throwEx);
            } else {
                log.info(msg);
            }
        }*/
    }
}
