package com.dzpk.eventtrack.impl;

import com.ai.dz.config.AiRuleTemplate;
import com.dzpk.component.mq.rabbitmq.model.eventtrack.EventTrackBasicInfo;
import com.dzpk.component.mq.rabbitmq.model.eventtrack.MessageInfo;
import com.dzpk.component.mq.rabbitmq.model.eventtrack.UserAddChipInfo;
import com.dzpk.component.mq.rabbitmq.model.eventtrack.UserBasicInfo;
import com.dzpk.db.model.UserInfo;
import com.dzpk.dealer.Player;
import com.dzpk.eventtrack.EventTrackService;
import com.google.gson.Gson;
import com.i366.cache.Cache;
import com.i366.model.player.RoomPersion;
import com.i366.model.room.Room;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 埋点服务
 *
 * 带入、站起、托管、坐下
 */
public class EventTrackServiceImpl extends EventTrackService {

    private static final Logger logger = LogManager.getLogger(EventTrackServiceImpl.class);

    private Room room;

    public EventTrackServiceImpl(Room room) {
        this.room = room;
    }

    @Override
    public MessageInfo addChip(int userId, int roomId, MessageInfo messageInfo) {
        UserAddChipInfo userAddChipInfo = new UserAddChipInfo();
        addEventTrackBasicInfo(userAddChipInfo,userId);  //添加基本埋点信息

        UserInfo userInfo = Cache.getOnlineUserInfo(userId, roomId);
        if(null != userInfo){

            /** 玩家基本信息 **/
            userAddChipInfo.setIp(userInfo.getIp());
            userAddChipInfo.setLongitude(userInfo.getLongitude());
            userAddChipInfo.setLatitude(userInfo.getLatitude());
            userAddChipInfo.setImei(userInfo.getImei());
            userAddChipInfo.setIsVirtual(userInfo.getIsVirtual());

            /** 玩家账户信息 **/
            userAddChipInfo.setExtractChip(userInfo.getExtractChip());
            userAddChipInfo.setNotExtractChip(userInfo.getNotExtractChip());

            // 可提/不可提的金豆变化值每次使用后需要重置
            userAddChipInfo.setExtractChangeChip(userInfo.getExtractChangeChip());
            userInfo.setExtractChangeChip(0);
            userAddChipInfo.setNotExtractChangeChip(userInfo.getNotExtractChangeChip());
            userInfo.setNotExtractChangeChip(0);
        }

        RoomPersion roomPersion = room.getAudMap().get(userId);
        if(null != roomPersion){
            userAddChipInfo.setSeatSize(roomPersion.getSize());
        }

        Player player = room.getDealer().getPlayers().get(userId);
        if(null != player){  //添加俱乐部联盟信息
            userAddChipInfo.setClubId(player.getClubId());
            userAddChipInfo.setTribeId(player.getTribeId());
            userAddChipInfo.setClubName(player.getClubName());
        }

        Gson gson = new Gson();
        String jsonMessage =  gson.toJson(userAddChipInfo);
        messageInfo.setMessage(jsonMessage);
        return messageInfo;
    }

    @Override
    protected MessageInfo standUp(int userId,int roomId,MessageInfo messageInfo) {
        UserBasicInfo userBasicInfo = new UserBasicInfo();
        addEventTrackBasicInfo(userBasicInfo,userId);  //添加基本埋点信息
        addBasicInfo(userBasicInfo,userId,roomId);  //添加玩家基本信息

        Gson gson = new Gson();
        String jsonMessage =  gson.toJson(userBasicInfo);
        messageInfo.setMessage(jsonMessage);
        return messageInfo;
    }

    @Override
    protected MessageInfo autoOp(int userId,MessageInfo messageInfo) {
        EventTrackBasicInfo eventTrackBasicInfo = new EventTrackBasicInfo();
        addEventTrackBasicInfo(eventTrackBasicInfo,userId);  //添加基本埋点信息

        Gson gson = new Gson();
        String jsonMessage =  gson.toJson(eventTrackBasicInfo);
        messageInfo.setMessage(jsonMessage);
        return messageInfo;
    }

    @Override
    protected MessageInfo downRoom(int userId,int roomId,MessageInfo messageInfo) {
        UserBasicInfo userBasicInfo = new UserBasicInfo();
        addEventTrackBasicInfo(userBasicInfo,userId);  //添加基本埋点信息
        addBasicInfo(userBasicInfo,userId,roomId);  //添加玩家基本信息

        Gson gson = new Gson();
        String jsonMessage =  gson.toJson(userBasicInfo);
        messageInfo.setMessage(jsonMessage);
        return messageInfo;
    }


    /**
     * 玩家基本信息
     * @param userBasicInfo
     * @param userId
     */
    private void addBasicInfo(UserBasicInfo userBasicInfo,int userId,int roomId){
        UserInfo userInfo = Cache.getOnlineUserInfo(userId, roomId);  //添加玩家账户信息
        if(null != userInfo){
            userBasicInfo.setIp(userInfo.getIp());
            userBasicInfo.setLongitude(userInfo.getLongitude());
            userBasicInfo.setLatitude(userInfo.getLatitude());
            userBasicInfo.setImei(userInfo.getImei());
            userBasicInfo.setIsVirtual(userInfo.getIsVirtual());
        }

        RoomPersion roomPersion = room.getAudMap().get(userId);
        if(null != roomPersion){
            userBasicInfo.setSeatSize(roomPersion.getSize());
        }
    }

    /**
     * 组装埋点基本信息
     * @param eventTrackBasicInfo
     * @param userId
     */
    private void addEventTrackBasicInfo(EventTrackBasicInfo eventTrackBasicInfo,int userId){
        eventTrackBasicInfo.setRoomId(room.getRoomId());
        eventTrackBasicInfo.setRoomPath(room.getRoomPath());
        eventTrackBasicInfo.setEventOpTime(System.currentTimeMillis());
        eventTrackBasicInfo.setUserId(userId);
        if(AiRuleTemplate.isAiUser(userId)){
            eventTrackBasicInfo.setIsAt(1);
        }else{
            eventTrackBasicInfo.setIsAt(0);
        }
    }

}
