package com.dzpk.commission.impl;

import com.ai.dz.config.AiRoomManager;
import com.dzpk.commission.IFeeService;
import com.dzpk.commission.RoomFeeConfig;
import com.dzpk.commission.constant.ESystemAccount;
import com.dzpk.commission.constant.EWho;
import com.dzpk.commission.fee.RoomFee;
import com.dzpk.commission.fee.TempRoomFee;
import com.dzpk.commission.fee.TotalRoomFee;
import com.dzpk.commission.repositories.mysql.*;
import com.dzpk.commission.repositories.mysql.impl.*;
import com.dzpk.commission.repositories.mysql.model.*;
import com.dzpk.common.constant.ERoundingMode;
import com.dzpk.common.utils.GsonHelper;
import com.dzpk.common.utils.Helper;
import com.dzpk.common.utils.LogHelper;
import com.dzpk.component.mq.rabbitmq.constant.EMessageCode;
import com.dzpk.component.repositories.mysql.dbpool.DBUtil;
import com.dzpk.component.repositories.redis.RedisService;
import com.dzpk.component.repositories.redis.lock.*;
import com.dzpk.db.dao.UserInfoDao;
import com.dzpk.db.imp.UserInfoDaoImp;
import com.dzpk.dealer.Player;
import com.i366.constant.ChipsCode;
import com.i366.model.player.RoomPlayer;
import com.i366.model.room.Room;
import com.i366.util.RabbitMqUtil;
import lombok.Getter;
import org.apache.commons.lang.NullArgumentException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @deprecated
 */
public class FeeServiceImpl implements IFeeService {

    /**
     * 日志服务
     */
    private static final Logger logger = LogManager.getLogger(FeeServiceImpl.class);

    private final Room room;
    private final int roomId;
    private final int roomPath;

    private final Set<Integer> clubIds;   //带入过的俱乐部集合

    private final Set<Integer> tribeIds;  //带入过的俱乐部所在同盟集合

    private final Set<String> clubNameSet; //带入的俱乐部名称集合

//    private final Set<String> tribeNameSet; //带入的同盟名称集合

    @Getter
    private final TempRoomFee tempRoomFee;  //临时计算的分润
    @Getter
    private final TotalRoomFee totalRoomFee; //总的分润

    /**
     * 平台收取比例配置
     */
    private final RoomFeeConfig platFormRoomFeeConfig;

    /**
     * 私有化构造函数
     */
    public FeeServiceImpl(Room room) {
        if (null == room)
            throw new NullArgumentException("room required!");

        this.room = room;
        this.roomId = room.getRoomId();
        this.roomPath = room.getRoomPath();
        this.clubIds = new HashSet<>();
        this.tribeIds = new HashSet<>();
        this.clubNameSet = new HashSet<>();
//        this.tribeNameSet = new HashSet<>();
        this.tempRoomFee = new TempRoomFee();
        this.totalRoomFee = new TotalRoomFee();

        //平台收取比例配置
        this.platFormRoomFeeConfig = new RoomFeeConfig(EWho.platform, 0.05, 1, room.getJKpump().doubleValue(), 1.0);

    }

    /**
     * 根据JP奖励，计算对应的服务费
     *
     * @param jackpotChip JP奖励
     * @param mode        取整模式,可选，默认：HALF_UP
     * @return 服务费
     */
    public double calFeeOfJackpot(int jackpotChip, ERoundingMode mode) {
        if (jackpotChip == 0)
            return jackpotChip;


        RoomFeeConfig config = platFormRoomFeeConfig;
        if (null == config ||
                0 == config.getJackpotRatio())
            return jackpotChip;

        double ratio = config.getJackpotRatio();
//        return Helper.multiplyForFee(jackpotChip * 0.01d,ratio);
        return Helper.multiplyForFee(jackpotChip * 1.00d, ratio);
    }

    /**
     * 计算服务费分3步
     * 1. 收集每位玩家相关数据
     * 2. 计算服务费
     * 3. 获取各项服务费
     *
     * @param userId
     * @param earning
     * @param insurance
     * @param clubFeeConfig
     * @return
     */
    public double collectPayer(int userId, int earning, int insurance, ClubFeeConfig clubFeeConfig, RoomFee roomFee) {
        StringBuilder traceLog = null;

        double userFee = 0;// 玩家扣除抽取的系统服务费后的盈亏 此处需要保存2位小数
        int clubId = clubFeeConfig.getClubId();
        int tribeId = clubFeeConfig.getTribeId();
        BigDecimal sysIncome = roomFee.getSysIncome();
        BigDecimal sysFee = roomFee.getSysFee();
        Map<Integer, BigDecimal> clubFeeMap = roomFee.getClubFeeMap();
        Map<Integer, BigDecimal> tribeFeeMap = roomFee.getTribeFeeMap();

        if (logger.isDebugEnabled()) {
            traceLog = LogHelper.createLog();
            LogHelper.log("calculate commission per player:rid=%s,path=%s," +
                            "uid=%s,earning=%s,insurance=%s,clubFeeConfig=%s",
                    this.roomId, this.roomPath, userId, earning, insurance, clubFeeConfig.toString());
        }

        try {

            RoomFeeConfig systemRoomFeeConfig = new RoomFeeConfig(EWho.sys, 0.025, 0.975, 0.025, clubFeeConfig.getSysConfig());
            RoomFeeConfig clubRoomFeeConfig = new RoomFeeConfig(EWho.tribe, 0.025, 0.975, 0.025, clubFeeConfig.getClubConfig());
            RoomFeeConfig tribeRoomFeeConfig = new RoomFeeConfig(EWho.club, 0.025, 0.975, 0.025, clubFeeConfig.getTribeConfig());

            // 这个是玩家的保险盈亏值
            // 此值为正，则表示玩家收入，系统就是支出，需要分摊到各个金主
            // 此值为负，则表示玩家买了保险不中，系统收入，需要分利到各个金主
            insurance = -insurance;

            userFee = this.calIncomeOfSys(userId, earning, insurance, sysIncome, roomFee);// 抽取系统服务费计算
            LogHelper.log("%s   userFee=%s", System.lineSeparator(), userFee);
            // 服务费支出计算
            // 系统仓
            {
                BigDecimal temp = this.calFee(userId, earning, insurance, systemRoomFeeConfig);
                sysFee = sysFee.add(temp);
                roomFee.setSysFee(sysFee);
                LogHelper.log("%s   sys fee=%s -> total=%s", System.lineSeparator(),
                        temp.doubleValue(), sysFee.doubleValue());
            }
            // 社区仓
            if (clubId > 0) {
                BigDecimal temp = this.calFee(userId, earning, insurance, clubRoomFeeConfig);
                BigDecimal clubFee = clubFeeMap.get(clubId);
                if (null == clubFee) {
                    clubFee = temp;
                } else {
                    clubFee = clubFee.add(temp);
                }
                clubFeeMap.put(clubId, clubFee);
                LogHelper.log("%s   clubId=%s , fee=%s -> total=%s", System.lineSeparator(),
                        clubId, temp.doubleValue(), clubFee.doubleValue());
            }
            // 同盟仓
            if (tribeId > 0) {
                BigDecimal temp = this.calFee(userId, earning, insurance, tribeRoomFeeConfig);
                BigDecimal tribeFee = tribeFeeMap.get(tribeId);
                if (null == tribeFee) {
                    tribeFee = temp;
                } else {
                    tribeFee = tribeFee.add(temp);
                }
                tribeFeeMap.put(tribeId, tribeFee);
                LogHelper.log("%s   tribeId=%s , fee=%s -> total=%s", System.lineSeparator(),
                        tribeId, temp.doubleValue(), tribeFee.doubleValue());
            }

            return userFee;
        } finally {
            if (null != traceLog) {
                LogHelper.log("%s   -> retValue=%s!", System.lineSeparator(), userFee);
                LogHelper.removeLog();
                logger.debug(traceLog.toString());
            }
        }
    }

    /**
     * 计算每个玩家的服务费支出
     * 公式 ((战绩*0.025)的绝对值+保险*0.975+JP奖励的0.025) * 对应基金仓的比例系数
     * 分别要计算对应的系统仓,同盟仓,俱乐部仓,即一个玩家需要调用3次这个方法
     *
     * @param userId    玩家id
     * @param earning   盈亏
     * @param insurance 保险
     * @param config    对应的分润配置
     * @return
     */
    private BigDecimal calFee(int userId, int earning, int insurance, RoomFeeConfig config) {
        BigDecimal result = new BigDecimal("0.00");;

        // 盈利绝对值的5%费用
        double earningRatio = config.getEarningRatio();
        if (earning != 0 && earningRatio != 0.00) {
            // 绝对值
            earning = Math.abs(earning);

            BigDecimal temp = new BigDecimal(earning);
            temp = temp.multiply(new BigDecimal(earningRatio));
            result = result.add(temp);
        }

        // 保险比例
        double insuranceRatio = config.getInsuranceRatio();
        if (insurance != 0 && insuranceRatio != 0.00) {
            BigDecimal temp = new BigDecimal(insurance);
            temp = temp.multiply(new BigDecimal(insuranceRatio));
            result = result.add(temp);
        }

        if (room.isJackPot()) {//兼容未开启jp的模式
            double jackPotRatio = config.getJackpotRatio();
            int jackPotBet = this.room.getJackpotService().getTotalJpReward(userId);// jackpot  这里要注意不是拿的jp奖励抽取的服务费
            if (jackPotBet != 0 && jackPotRatio != 0.00) {
                BigDecimal temp = new BigDecimal(jackPotBet);
                temp = temp.multiply(new BigDecimal("0.01"));  //统一单位 金豆要除以100 转换为记分牌
                temp = temp.multiply(new BigDecimal(jackPotRatio));
                result = result.add(temp);
            }
        }

        //对应基金仓的分润比例系数
        double ratio = config.getOverallRatio();
        if (ratio != 0.00) {
            result = result.multiply(new BigDecimal(ratio));
        }

        return result;
    }

    /**
     * 系统服务费抽取计算
     *
     * @param userId    玩家id
     * @param earning   盈利值
     * @param insurance 保险值
     * @return 玩家扣除收取的服务费后的盈亏
     */
    private double calIncomeOfSys(int userId, int earning, int insurance, BigDecimal sysIncome, RoomFee roomFee) {
        // 扣除正盈利的5%费用
        double userFee = 0;

        // 扣除正盈利的5%费用
        if (earning > 0) {
            double earningRatio = platFormRoomFeeConfig.getEarningRatio();
            if (earningRatio != 0.00) {
                userFee = Helper.multiplyForFee(earning * 1.00, earningRatio);
                if (userFee != 0) {
                    sysIncome = sysIncome.add(new BigDecimal(userFee));
                }
            }
        }

        // 保险比例
        double insuranceRatio = platFormRoomFeeConfig.getInsuranceRatio();
        if (insurance != 0 && insuranceRatio != 0.00) {
            BigDecimal insuranceFee = new BigDecimal(insurance);
            insuranceFee = insuranceFee.multiply(new BigDecimal(insuranceRatio));
            sysIncome = sysIncome.add(insuranceFee);
        }

        // jackpot
        double jackpotFee = 0;
        if (null != this.room.getJackpotService()) {
            jackpotFee = this.room.getJackpotService().getTotalJpRewardFee(userId); //击中时保存,已经扣除过比例
            if (jackpotFee != 0) {
                sysIncome = sysIncome.add(new BigDecimal(jackpotFee));
            }
            roomFee.setSysIncome(sysIncome);
        }

        return userFee;
    }

    /**
     * 费用单位转换  BigDecimal --> double
     * 支出保留两位小数点，正数向下取整，负数向上取整
     * 举例支出：
     * 100.234	100.23
     * -100.234	-100.24
     *
     * @param value
     * @return
     */
    private double transferFeeUnit(BigDecimal value) {
        double result = 0d;
        if (null == value) {
            return result;
        }

        result = Helper.multiply(value, 1.00d);
        return result;
    }

    /**
     * 获取系统回收费用
     *
     * @return
     */
    public double feeIncome(RoomFee roomFee) {
        return this.transferFeeUnit(roomFee.getSysIncome());
    }

    /**
     * 获取系统仓费用
     *
     * @return
     */
    public double sysFee(RoomFee roomFee) {
        return this.transferFeeUnit(roomFee.getSysFee());

    }

    /**
     * 获取对应的俱乐部仓
     *
     * @return
     */
    public double clubFee(int clubId, RoomFee roomFee) {
        BigDecimal value = roomFee.getClubFeeMap().get(clubId);
        return this.transferFeeUnit(value);
    }

    /**
     * 获取对应的同盟仓
     *
     * @return
     */
    public double tribeFee(int tribeId, RoomFee roomFee) {
        BigDecimal value = roomFee.getTribeFeeMap().get(tribeId);
        return this.transferFeeUnit(value);
    }

    /**
     * 保存战绩时，调用
     * 用于进行服务费相关的操作
     *
     * @deprecated
     */
    public void saveCommission() {
        Connection connection = null;
        boolean connAutoCommit = false;
        try {
            Set<Integer> tempClubIds = new HashSet<>();
            Set<Integer> tempTribeIds = new HashSet<>();

            // 同盟仓或俱乐部仓
            // 资金扣除及日志保存
            List<ClubFundHistoryPo> clubFundHistoryLst = new ArrayList<>();
            List<ClubChipLog> clubChipLogLst = new ArrayList<>();
            if (room.getTribeRoomType() == 0) {
                for (Map.Entry<Integer, Integer> entry : room.getClubFee().entrySet()) {
                    clubFundHistoryLst.add(this.genClubFundHistory(entry.getKey(), entry.getValue(),
                            room.getName(), room.getManzhu(), ClubFundHistoryPo.CLUB_FUND_GAME_PROFIT, room.getRoomId()));
                    tempClubIds.add(entry.getKey());
                }
                for (Map.Entry<Integer, Integer> entry : room.getClubInsurance().entrySet()) {
                    clubFundHistoryLst.add(this.genClubFundHistory(entry.getKey(), entry.getValue(),
                            room.getName(), room.getManzhu(), ClubFundHistoryPo.CLUB_FUND_INSURANCE_PROFIT, room.getRoomId()));
                }
            } else {
                for (Map.Entry<Integer, Integer> entry : room.getClubFee().entrySet()) {
                    clubChipLogLst.add(this.genClubChipHistory(entry.getKey(), entry.getValue(), ClubChipLog.TRIBE_FUND_GAME_PROFIT, room.getRoomId()));
                    tempClubIds.add(entry.getKey());
                }
                for (Map.Entry<Integer, Integer> entry : room.getClubInsurance().entrySet()) {
                    clubChipLogLst.add(this.genClubChipHistory(entry.getKey(), entry.getValue(), ClubChipLog.TRIBE_FUND_INSURANCE_PROFIT, room.getRoomId()));
                }
            }

            List<TribeChipLog> tribeChipLogLst = new ArrayList<>();
            for (Map.Entry<Integer, Integer> entry : room.getTribeFee().entrySet()) {
                tribeChipLogLst.add(this.genTribeChipHistory(entry.getKey(), entry.getValue(), TribeChipLog.TRIBE_FUND_GAME_PROFIT, room.getRoomId()));
                tempTribeIds.add(entry.getKey());
            }
            for (Map.Entry<Integer, Integer> entry : room.getTribeInsurance().entrySet()) {
                tribeChipLogLst.add(this.genTribeChipHistory(entry.getKey(), entry.getValue(), TribeChipLog.TRIBE_FUND_INSURANCE_PROFIT, room.getRoomId()));
            }

            //保存俱乐部收益
            connection = DBUtil.getConnection();
            connAutoCommit = connection.getAutoCommit();
            connection.setAutoCommit(false);
            IClubFundDao fundDao = new ClubFundDaoImpl();
            ITribeDao tribeDao = new TribeDaoImpl();
            ClubTribeDao clubTribeDao = new ClubTribeDaoImpl();
            IUserBalanceAuditDao auditDao = new UserBalanceAuditDaoImpl();

            String txUuid = UUID.randomUUID().toString();
            UserBalanceAuditLog bringInLog = UserBalanceAuditLog.builder()
                    .txUuid(txUuid)
                    .source(UserBalanceAuditLog.Source.ROOM.getValue())
                    .operatorId(-1)
                    .roomId(roomId)
                    .build();

            for (ClubFundHistoryPo po : clubFundHistoryLst) {
                ClubTribeModel clubTribe = clubTribeDao.getClubTribeModel(po.getClubId());
                if (po.getType() == ClubFundHistoryPo.CLUB_FUND_GAME_PROFIT) {
                    bringInLog.setType(UserBalanceAuditLog.Type.ROOM_FEE.getValue());
                    bringInLog.setDescription(String.format("%d %s", roomId, UserBalanceAuditLog.Type.ROOM_FEE.getDesc()));
                } else {
                    bringInLog.setType(UserBalanceAuditLog.Type.INSURANCE_FEE.getValue());
                    bringInLog.setDescription(String.format("%d %s", roomId, UserBalanceAuditLog.Type.INSURANCE_FEE.getDesc()));
                }
                bringInLog.setClubId(po.getClubId());
                bringInLog.setUserId(clubTribe.getClubCreator());
                bringInLog.setBalanceType(UserBalanceAuditLog.BalanceType.CLUB_CHIP.getValue());
                bringInLog.setBalanceBefore(fundDao.getFundOfClub(connection, po.getClubId()));
                bringInLog.setBalanceChange(po.getAmount());
                auditDao.addUserBalanceAuditLog(bringInLog);
                fundDao.addFundOfClub(connection, po.getClubId(), po.getAmount());//扣减对应俱乐部的基金
                fundDao.addProfitOfClub(connection, po.getClubId(), po.getClubProfit());//扣减对应俱乐部的累计分润
            }

            for (ClubChipLog log : clubChipLogLst) {
                tribeDao.addChipOfClub(connection, log.getClubId(), log.getChip());//扣减对应俱乐部的聯盟幣
            }

            for (TribeChipLog log : tribeChipLogLst) {
                tribeDao.addChipOfTribe(connection, log.getTribeId(), log.getChip());
            }

            //保存俱乐部基金变动历史
            if (!clubFundHistoryLst.isEmpty()) {
                fundDao.addFundHistory(connection, clubFundHistoryLst);
                if (logger.isDebugEnabled()) {
                    logger.debug("保存俱乐部基金： rid={} -> {} ", room.getRoomId(), GsonHelper.toJson(clubFundHistoryLst, false));
                }
            }
            if (!clubChipLogLst.isEmpty()) {
                tribeDao.addClubChipLog(connection, clubChipLogLst);
                if (logger.isDebugEnabled()) {
                    logger.debug("保存俱乐部同盟筹码： rid={} -> {} ", room.getRoomId(), GsonHelper.toJson(clubChipLogLst, false));
                }
            }
            if (!tribeChipLogLst.isEmpty()) {
                tribeDao.addChipLog(connection, tribeChipLogLst);
                if (logger.isDebugEnabled()) {
                    logger.debug("保存同盟筹码： rid={} -> {} ", room.getRoomId(), GsonHelper.toJson(tribeChipLogLst, false));
                }
            }

            //获取所有俱乐部创建者
            Map<Integer, Integer> clubCreatorsMap = clubTribeDao.getClubCreatorsToMap(tempClubIds.toString().substring(1, tempClubIds.toString().length() - 1));
            Map<Integer, Integer> tribeCreatorMap = clubTribeDao.getTribeCreatorsToMap(tempTribeIds.toString().substring(1, tempTribeIds.toString().length() - 1));

            List<ClubMessageRecordPo> clubRecordList = new ArrayList<>();
            List<TribeMessageRecordPo> tribeRecordList = new ArrayList<>();
            Map<String, MessageUnreadDetailPo> unreadClubRecordMap = new HashMap<>();
            Map<String, MessageUnreadDetailPo> unreadTribeRecordMap = new HashMap<>();
            //发送mq消息通知
            for (ClubFundHistoryPo po : clubFundHistoryLst) {
                String creatorId = String.valueOf(clubCreatorsMap.get(po.getClubId()));
                ClubMessageRecordPo clubMessage = ClubMessageRecordPo.builder().msgId(UUID.randomUUID().toString()).clubId(String.valueOf(po.getClubId()))
                        .senderId(creatorId).reciverId(creatorId).content(room.getName()).remark(String.valueOf(po.getAmount() / 100.00)).type(EMessageCode.CLUB_FUND_PROFIT.getCode()).build();
                clubRecordList.add(clubMessage);
                MessageUnreadDetailPo unreadDetailPo = MessageUnreadDetailPo.builder().time(new Date().getTime()).type(EMessageCode.CLUB_FUND_PROFIT.getCode())
                        .content(room.getName()).remark(String.valueOf(po.getAmount() / 100.00)).build();
                unreadClubRecordMap.put(creatorId, unreadDetailPo);
                RabbitMqUtil.sendGameProfitNotify(room.getName(), po.getAmount(), creatorId);
            }
            for (TribeChipLog log : tribeChipLogLst) {
                String creatorId = String.valueOf(tribeCreatorMap.get(log.getTribeId()));
                TribeMessageRecordPo tribeMessage = TribeMessageRecordPo.builder().msgId(UUID.randomUUID().toString()).tribeId(String.valueOf(log.getTribeId()))
                        .senderId(creatorId).reciverId(creatorId).content(room.getName()).remark(String.valueOf(log.getChip() / 100.00)).type(EMessageCode.TRIBE_FUND_PROFIT.getCode()).build();
                tribeRecordList.add(tribeMessage);
                MessageUnreadDetailPo unreadDetailPo = MessageUnreadDetailPo.builder().time(new Date().getTime()).type(EMessageCode.TRIBE_FUND_PROFIT.getCode())
                        .content(room.getName()).remark(String.valueOf(log.getChip() / 100.00)).build();
                unreadTribeRecordMap.put(creatorId, unreadDetailPo);
                RabbitMqUtil.sendGameProfitNotify(room.getName(), log.getChip(), creatorId);
            }
            IMessageDao messageDao = new MessageDaoImpl();
            messageDao.insertClubMessage(connection, clubRecordList);
            messageDao.insertTribeMessage(connection, tribeRecordList);
            messageDao.updateClubUnreadNum(connection, unreadClubRecordMap);
            messageDao.updateTribeUnreadNum(connection, unreadTribeRecordMap);
            //牌局服务费
            int sysChangeChip = room.getSystemFee();
            //彩金服务费
//            int JPFeeChip=room.getJpFee();
            //保险服务费
            int insureSystemFee = room.getInsureSystemFee();
            /**
             * 1 获取系统仓当前值
             * 2 更新系统仓
             * 3 插入更新系统仓日志
             **/
            IPlatformAccountDao platformAccountDao = new PlatformAccountDaoImpl();
            PlatformAccountLog.PlatformAccountLogBuilder platformAccountLog = PlatformAccountLog.builder()
                    .changeChip(sysChangeChip)
                    .externalId(String.valueOf(room.getRoomId()))
                    .description("系统仓收益=" + room.getRoomId());
            if (room.getTribeRoomType() != 0) {
                long currentChip = platformAccountDao.querySystemTribeAccount();
                platformAccountDao.updateSystemTribeAccount(sysChangeChip);
                platformAccountLog.currentChip(currentChip)
                        .platformCode(ESystemAccount.PLATFORM_TRIBE_ACCOUNT.getCode());
            } else if (room.getClubRoomType() == 1) {
                long currentChip = platformAccountDao.querySystemClubAccount();
                platformAccountDao.updateSystemClubAccount(sysChangeChip);
                platformAccountLog.currentChip(currentChip)
                        .platformCode(ESystemAccount.PLATFORM_CLUB_ACCOUNT.getCode());
            } else { // 大厅, 私人房
                long currentChip = platformAccountDao.querySystemAccount();
                platformAccountDao.updateSystemAccount(sysChangeChip);
                platformAccountLog.currentChip(currentChip)
                        .platformCode(ESystemAccount.PLATFORM_ACCOUNT.getCode());
            }
            platformAccountDao.insertSystemAccountRecordForGameProfitByDz(platformAccountLog.build());
            connection.commit();
//            PlatformAccountLog JPFeeAccountLog = PlatformAccountLog.builder().changeChip(JPFeeChip)
//                    .currentChip(nowPlatformAccountChip).externalId("" + room.getRoomId())
//                    .description("系统仓收益=" + room.getRoomId()).build();
//            platformAccountDao.insertJPSystemAccountRecordForGameProfitByDz(JPFeeAccountLog);
//            connection.commit();
            long nowPlatformAccountChip = platformAccountDao.querySystemAccount();
            platformAccountDao.updateSystemAccount(insureSystemFee);
            PlatformAccountLog insureAccountLog = PlatformAccountLog.builder().changeChip(insureSystemFee)
                    .currentChip(nowPlatformAccountChip).externalId("" + room.getRoomId())
                    .description("系统仓收益=" + room.getRoomId()).build();
            platformAccountDao.insertInsuranceSystemAccountRecordForGameProfitByDz(insureAccountLog);
            connection.commit();
        } catch (Exception ex) {
            DBUtil.rollback(connection);
            logger.error(String.format("Save commission failed:%s", ex.getMessage()), ex);
        } finally {
            if (null != connection) {
                DBUtil.resetAutoCommit(connection, connAutoCommit);
                DBUtil.closeConnection(connection);
            }
        }
    }

    /**
     * 俱乐部基金变动历史 对应数据库中的字段映射
     *
     * @param clubId   俱乐部id
     * @param fee      基金变动值
     * @param roomName 房间名称 对应数据库中的操作者名称
     * @param sb       小盲和大盲 对应数据库中的玩家昵称
     * @param fundType 基金变动类型  1俱乐部基金变动  2同盟基金变动
     * @param roomId
     * @return
     */
    private ClubFundHistoryPo genClubFundHistory(int clubId, int fee, String roomName, int sb, int fundType, int roomId) {
//        IExchange exchangeService = CurrencyFactory.getInstance().getExchangeService();

        ClubFundHistoryPo po = new ClubFundHistoryPo();
        po.setClubId(clubId);
        po.setAmount(fee);
        po.setOperatorName(null == roomName ? "" : roomName);//对应数据库中的操作者名称
        po.setUserName(sb + "/" + 2 * sb); //对应数据库中的玩家昵称
        po.setCreateTime(new Timestamp(System.currentTimeMillis()));
        po.setType(fundType);
        po.setOpId(roomId);
        po.setClubProfit(fee);
        return po;
    }

    private TribeChipLog genTribeChipHistory(int tribeId, int fee, int feeType, int roomId) {
        TribeChipLog po = new TribeChipLog();
        po.setTribeId(tribeId);
        po.setChip(fee);
        po.setOtherId(String.valueOf(roomId));
        po.setType(feeType);
        po.setCreateTime(new Timestamp(System.currentTimeMillis()));
        return po;
    }

    private ClubChipLog genClubChipHistory(int clubId, int fee, int feeType, int roomId) {
        ClubChipLog po = new ClubChipLog();
        po.setClubId(clubId);
        po.setChip(fee);
        po.setOtherId(String.valueOf(roomId));
        po.setType(feeType);
        po.setCreateTime(new Timestamp(System.currentTimeMillis()));
        return po;
    }

    @Override
    public void addBringClubAndTribe(int clubId, int tribeId, String clubName) {
        if (0 != clubId) {
            this.clubIds.add(clubId);
        }

        if (0 != tribeId) {
            this.tribeIds.add(tribeId);
        }

        if (null != clubName && !clubName.isEmpty()) {
            this.clubNameSet.add(clubName);
        }

    }

    @Override
    public Set<Integer> getBringClubs() {
        return this.clubIds;
    }

    @Override
    public Set<Integer> getBringTribes() {
        return this.tribeIds;
    }

    @Override
    public Set<String> getClubNames() {
        return this.clubNameSet;
    }

    @Override
    public void saveAheadCommission(int settlementType) {
        Set<Integer> bringClubIds = new HashSet<>();
        Set<Integer> needToSettlementUserIds = new HashSet<>();

        /* 获取提前离座且未结算的玩家及所在的俱乐部 */
        Collection<RoomPlayer> roomPlayers = room.getRoomPlayers().values();
        for (RoomPlayer roomPlayer : roomPlayers) {
            if (null != roomPlayer) {
                if (roomPlayer.getAheadLeave() == 1 && !roomPlayer.isSettlement()) {
                    int userId = roomPlayer.getUserId();
                    Player player = room.getDealer().getPlayers().get(userId);
                    if (null != player) {
                        if (player.getClubId() > 0) {
                            bringClubIds.add(player.getClubId());
                        }
                        needToSettlementUserIds.add(userId);
                    }
                }
            }
        }

        if (needToSettlementUserIds.isEmpty()) {
            logger.debug("saveAheadCommission no players aheadLeave");
            return;
        }

        logger.debug("有玩家需要提前结算,roomId={},bringClubIds={},needToSettlementUserIds={}", room.getRoomId(), bringClubIds.toString(), needToSettlementUserIds.toString());

        /* 获取俱乐部分润配置 */
        ClubTribeDao clubTribeDao = new ClubTribeDaoImpl();
        Map<Integer, ClubFeeConfig> clubFeeConfigMap = null;

        if (!bringClubIds.isEmpty()) {
            clubFeeConfigMap = clubTribeDao.getFeeConfig(bringClubIds);
            if (clubFeeConfigMap.isEmpty()) {
                logger.error("saveAheadCommission can't find club fee config info - clubIds={}", bringClubIds);
            }
        }

        /* 计算提前离桌的玩家服务费*/
        for (Integer userId : needToSettlementUserIds) {
            Player player = room.getDealer().getPlayers().get(userId);
            if (null != player) {
                int bringIn = player.getBringIn();
//                int clubId = player.getClubId();
                int pl = player.getEarn() + player.getInsurance();
                double plFee = player.getPlFee();
                logger.info("提前结算数据,数据详情：userid:{},pl:{},plFee:{},bringIn:{}", userId, pl, plFee, bringIn);
                room.getTiqianjiesunSet().add(userId);
//                double plFee = player.getPlFee();
//                if (null != clubFeeConfigMap && clubFeeConfigMap.containsKey(clubId)) {
//                    ClubFeeConfig clubFeeConfig = clubFeeConfigMap.get(clubId);
//                    int insurance = player.getTotalInsurance();
//                    plFee = room.getFeeService().collectPayer(userId, pl, insurance, clubFeeConfig, this.getTotalRoomFee());
//                    player.setPlFee(plFee);
//                    logger.info("saveAheadCommission calculate fee successfully,rid={},uid={},pl={},plFee={}", room.getRoomId(), userId, pl, plFee);
//                }
                try (Connection conn = DBUtil.getConnection()) {
                    LockedActuator.withLock(() -> {
                        saveAheadSettlement(userId, plFee, pl, bringIn, conn);
                    }, RedisDistributedLockGenerator.generate(
                            RedisLockConfigCode.ROOM_SETTLEMENT_USER,
                            RedisLockKeyGenerator.generateUserKey(userId)
                    ));
                } catch (SQLException ex) {
                    logger.error("saveAheadCommission failed: " + ex.getMessage(), ex);
                }
            }
        }

    }

    @Override
    public RoomFee getRoomFeeByType(int feeType) {
        if (1 == feeType) {
            return tempRoomFee;
        } else {
            return totalRoomFee;
        }
    }

    @Override
    public void resetPerHand() {
        this.getTempRoomFee().resetPerHand();
    }

    /**
     * 对提前离桌的玩家进行结算
     *
     * @param userId 玩家id
     * @param plFee  服务费
     * @param pl     盈亏
     */
    private void saveAheadSettlement(int userId, double plFee, int pl, int bringIn, Connection conn) throws SQLException {
        int sum = bringIn + pl;
        if (room.getClubRoomType() != 1) {
            room.getRoomService().updateUserInfo(userId, ChipsCode.USER_AHEAD_LEAVE);  //更新玩家信息
        } else if (sum != 0) {
            // 提前对用户进行结算
            logger.info("进行统计计算：userid:{},bringIn:{},pl:{},sum:{}", userId, bringIn, pl, sum);

            String txUuid = UUID.randomUUID().toString();
            UserBalanceAuditLog bringOutLog = UserBalanceAuditLog.builder()
                    .txUuid(txUuid)
                    .userId(userId)
                    .source(UserBalanceAuditLog.Source.ROOM.getValue())
                    .operatorId(-1)
                    .roomId(roomId)
                    .clubId(room.getClubId())
                    .build();

            bringOutLog.setType(UserBalanceAuditLog.Type.BRING_OUT.getValue());
            bringOutLog.setDescription(String.format("%d %s", roomId, UserBalanceAuditLog.Type.BRING_OUT.getDesc()));
            bringOutLog.setBalanceChange(sum);

            UserInfoDao userInfoDao = new UserInfoDaoImp();
            if(room.getTribeRoomType() == 0){
                int balanceBefore = userInfoDao.queryUserGold(conn, userId);
                userInfoDao.updateUserGold(conn, userId, sum);
                bringOutLog.setBalanceType(UserBalanceAuditLog.BalanceType.GOLD.getValue());
                bringOutLog.setBalanceBefore(balanceBefore);
            }else {
                int balanceBefore = userInfoDao.queryUserTribeChip(conn, room.getClubId(), userId);
                room.getRoomService().getRoomDao().updateTribeIntegral(room.getClubId(), userId, sum);
                bringOutLog.setTribeId(room.getTribeId());
                bringOutLog.setBalanceType(UserBalanceAuditLog.BalanceType.TRIBE_CHIP.getValue());
                bringOutLog.setBalanceBefore(balanceBefore);
            }
            IUserBalanceAuditDao auditDao = new UserBalanceAuditDaoImpl();
            auditDao.addUserBalanceAuditLog(bringOutLog);
        }
        RoomPlayer roomPlayer = room.getRoomPlayers().get(userId);
        roomPlayer.setSettlement(true);  //设置为已经结算过
        roomPlayer.setAheadLeaveFee(plFee);  //保存计算的服务费
        roomPlayer.setAheadLeavePl(pl);
        RedisService.getRedisService().addRoomSettlementSet(userId, roomId);
    }
}
