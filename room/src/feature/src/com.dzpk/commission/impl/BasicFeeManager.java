package com.dzpk.commission.impl;

import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

public class BasicFeeManager extends AbstractFeeManager {

    private final BasicFeeConfig config;

    public BasicFeeManager(@NonNull EFeeMode feeMode, BasicFeeConfig config) {
        super(feeMode);
        this.config = config;
    }

    @Override
    protected FeeHolder doCalculateFee(HandState hand) {
        FeeHolder result = new FeeHolder();
        int playerId = hand.getPlayerId();

        if (hand.getScore() > 0) { // 水上
            if (hand.getClubId() > 0) {
                int clubFee = mul(hand.getScore(), config.getRoomFeeRate(), Rounding.of(PLAYER, CLUB));
                result.clubFee = new FeeItem(playerId, hand.getClubId(), EFeeType.CLUB_FEE, -clubFee, 0, 0, clubFee, 0);

                int platformFee = mul(hand.getScore(), config.getPlatformFeeRate(), Rounding.of(CLUB, PLATFORM));
                result.platformFee = new FeeItem(playerId, hand.getClubId(), EFeeType.PLATFORM_FEE, 0, platformFee, 0, -platformFee, 0);
            } else {
                int roomFee = mul(hand.getScore(), config.getRoomFeeRate(), Rounding.of(PLAYER, PLAYER));
                result.roomFee = new FeeItem(playerId, 0, EFeeType.ROOM_FEE, -roomFee, 0, 0, 0, roomFee);

                int platformFee = mul(hand.getScore(), config.getPlatformFeeRate(), Rounding.of(PLAYER, PLATFORM));
                result.platformFee = new FeeItem(playerId, 0, EFeeType.PLATFORM_FEE, 0, platformFee, 0, 0, -platformFee);
            }

        }

        return result;
    }

    @Override
    public FeeItem calculateInsuranceFee(HandState hand) {
        throw new UnsupportedOperationException();
    }

    @Override
    public IFeeConfig getFeeConfig() {
        return config;
    }

    @Override
    public int calculateFeeLocked(int pl) {
        if (pl <= 0) return 0;
        return mul(pl, config.getRoomFeeRate(), Rounding.of(PLAYER, PLAYER));
    }

    /**
     * 基本费用配置 - 金币房適用
     */
    @Getter
    @RequiredArgsConstructor
    @ToString
    public static class BasicFeeConfig implements IFeeConfig {
        /**
         * 平台手续费率 0.005 = 0.5%
         */
        private final double platformFeeRate;
        /**
         * 房間手续费率 0.05 = 5%
         */
        private final double roomFeeRate;
    }
}
