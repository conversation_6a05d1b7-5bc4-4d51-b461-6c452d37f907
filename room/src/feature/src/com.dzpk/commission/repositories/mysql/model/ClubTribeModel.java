package com.dzpk.commission.repositories.mysql.model;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ClubTribeModel {

    private int clubCreator;  //俱乐部创建者

    private int tribeCreator;  //同盟创建者

    private String clubName;     //俱乐部名称

    private String tribeName;    //同盟名称

    private int clubId;       //俱乐部id

    private int tribeId;     //同盟id
    //同盟主俱乐部id
    private int tClubId;

    private int clubTribeStatus;     //俱乐部与同盟的关系 1正常 2被踢出 3转移中

    private int clubStatus;   //俱乐部状态 0正常 1 关闭

    private int tribeStatus;  //同盟状态  0正常  1 关闭

    private int userChips;

    //联盟分成
    private int tribeProportion;
    //俱乐部分成
    private int clubProportion;

    private String clubRandomId;

    @Override
    public String toString() {
        return "ClubTribeModel{" +
                "clubCreator=" + clubCreator +
                ", tribeCreator=" + tribeCreator +
                ", clubName='" + clubName + '\'' +
                ", tribeName='" + tribeName + '\'' +
                ", clubId=" + clubId +
                ", tribeId=" + tribeId +
                ", clubTribeStatus=" + clubTribeStatus +
                ", clubStatus=" + clubStatus +
                ", tribeStatus=" + tribeStatus +
                '}';
    }
}
