package com.dzpk.commission.repositories.mysql;

import com.dzpk.commission.repositories.mysql.model.ClubChipLog;
import com.dzpk.commission.repositories.mysql.model.TribeChipLog;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

public interface ITribeDao
{
    Integer getMainClubOfTribe(int tribeId);

    /**
     * 查找同盟下的所有社区ID
     * @param tribeId 同盟ID
     * @return
     */
    List<Integer> getClubOfTribe(int tribeId);

    /**
     * 检查用户是否加入指定的社区
     * @param uidCidMap  userId -> clubId
     * @return userId list
     */
    List<Integer> checkIfUserInClub(Map<Integer,List<Integer>> uidCidMap);

    boolean addChipOfClub(Connection conn, int clubId, int amount);

    boolean addChipOfTribe(Connection conn, int tribeId, int amount);

    int addChipLog(Connection conn, List<TribeChipLog> logLst);

    int addClubChipLog(Connection conn, List<ClubChipLog> logLst);

    int getChipOfClub(Connection conn, int clubId) throws SQLException;

    int getChipOfTribe(Connection conn, int tribeId) throws SQLException;
}
