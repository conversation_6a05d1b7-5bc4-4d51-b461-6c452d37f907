package com.dzpk.currency;

import com.dzpk.currency.impl.ExchangeServiceImpl;

public class CurrencyFactory {
    /** 实例化工厂 */
    private static final CurrencyFactory FACTORY_INSTANCE = new CurrencyFactory();
    public static CurrencyFactory getInstance(){
        return FACTORY_INSTANCE;
    }
    private CurrencyFactory(){
        this.exchangeService = new ExchangeServiceImpl();
    }

    private ExchangeServiceImpl exchangeService;
    public IExchange getExchangeService(){
        return this.exchangeService;
    }
}
