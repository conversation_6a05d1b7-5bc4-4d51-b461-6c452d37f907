package com.dzpk.weight.cache.handler;

import com.dzpk.common.utils.LogHelper;
import com.dzpk.component.file.IFileChangedHandler;
import com.dzpk.weight.bo.WeightFlopInterventionConfigBo;
import com.dzpk.weight.bo.WeightInverventionConfigBo;
import com.dzpk.weight.cache.handler.Bean.WeightInverventionConfigVo;
import com.dzpk.weight.cache.impl.UserWeightCacheImpl;
import com.dzpk.weight.constant.EAllinInterventionType;
import com.google.common.reflect.TypeToken;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Type;
import java.nio.file.Path;
import java.util.List;
import java.util.Map;

@Slf4j
public class TurnInterveneConfigChangedHandler extends AbstractFileChangedHandler
        implements IFileChangedHandler {
    private String fileName;
    private EAllinInterventionType type;
    public String fileName(){
        return this.fileName;
    }

    public TurnInterveneConfigChangedHandler(UserWeightCacheImpl cache, EAllinInterventionType type){
        super(cache);
        this.type = type;
        this.fileName = String.format("%s_intervene_config.json",type);
    }

    public void handle(Path filePath){
        if(null == filePath)
            return;

        StringBuilder traceMsg = null;
        Throwable exception = null;
        if(log.isDebugEnabled()){
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("【%s】: %s",this.type.desc(),filePath));
        }

        try {
            String json = this.readJson(filePath);
            if(null == json || "".equals(json.trim())) {
                LogHelper.log("%s   文件内容为空 -> 忽略！",System.lineSeparator());
                return;
            }

            json = json.trim();
            Type type = new TypeToken<ConfigVo>(){}.getType();
            ConfigVo configVo = this.parseJsonAsSingle(json,type);
            if(null == configVo) {
                LogHelper.log("%s   文件内容无法反序列化 -> 忽略！",System.lineSeparator());
                return;
            }

            // BB倍数配置
            if(null == configVo.getPotXBB() ||
                    configVo.getPotXBB()<=0){
                LogHelper.log("%s   【potXBB】必须大于0，不符合业务要求: %s -> 忽略！",System.lineSeparator(),configVo.getPotXBB());
                return;
            }

            // 干预次数配置
            if(null == configVo.getInterventions() ||
                    configVo.getInterventions().isEmpty()){
                LogHelper.log("%s   【interventions】配置不存在，不符合业务要求 -> 忽略！",System.lineSeparator());
                return;
            }
            Map<Integer,WeightInverventionConfigBo> configMap = this.parseInterventionConfig(configVo.getInterventions());
            if(null == configMap || configMap.isEmpty()){
                LogHelper.log("%s   【interventions】配置不存在，不符合业务要求 -> 忽略！",System.lineSeparator());
                return;
            }

            WeightFlopInterventionConfigBo configBo = WeightFlopInterventionConfigBo.builder()
                    .potXBB(configVo.getPotXBB())
                    .interventions(configMap).build();
            WeightFlopInterventionConfigBo oldConfig = this.cache.updateInterventionConfig(this.type,configBo);
            LogHelper.log("%s    配置修改成功：%s -> %s！",System.lineSeparator(),oldConfig,configBo);
        }catch (Exception ex){
            exception = ex;
            LogHelper.log("%s   系统异常: %s",System.lineSeparator(),ex.getMessage());
        }finally {
            if(null != traceMsg){
                LogHelper.removeLog();
                log.debug(traceMsg.toString());
            }
            if(null != exception)
                log.error(String.format("【%s】处理失败: %s",this.type.desc(),filePath),exception);
        }
    }

    @Getter
    @Setter
    private static class ConfigVo{
        /**
         * BB倍数
         * 必须>0
         */
        private Integer potXBB;

        /**
         * 干预次数配置
         */
        private List<WeightInverventionConfigVo> interventions;
    }
}
