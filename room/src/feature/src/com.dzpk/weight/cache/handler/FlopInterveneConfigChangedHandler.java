package com.dzpk.weight.cache.handler;

import com.dzpk.common.utils.Helper;
import com.dzpk.common.utils.LogHelper;
import com.dzpk.component.file.IFileChangedHandler;
import com.dzpk.weight.bo.*;
import com.dzpk.weight.cache.handler.Bean.WeightInverventionConfigVo;
import com.dzpk.weight.cache.impl.UserWeightCacheImpl;
import com.dzpk.weight.flipcard.FlipCardHandType;
import com.dzpk.weight.flipcard.FlipCardTargetType;
import com.google.common.reflect.TypeToken;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Type;
import java.nio.file.Path;
import java.util.*;

@Slf4j
public class FlopInterveneConfigChangedHandler extends AbstractFileChangedHandler
        implements IFileChangedHandler {
    private String fileName;
    public String fileName(){
        return this.fileName;
    }

    public FlopInterveneConfigChangedHandler(UserWeightCacheImpl cache){
        super(cache);
        this.fileName = "flop_intervene_config.json";
    }

    public void handle(Path filePath){
        if(null == filePath)
            return;

        StringBuilder traceMsg = null;
        Throwable exception = null;
        if(log.isDebugEnabled()){
            traceMsg = LogHelper.createLog();
            traceMsg.append(String.format("【干预内容-翻牌配置】: %s",filePath));
        }

        try {
            String json = this.readJson(filePath);
            if(null == json || "".equals(json.trim())) {
                LogHelper.log("%s   文件内容为空 -> 忽略！",System.lineSeparator());
                return;
            }

            json = json.trim();
            Type type = new TypeToken<ConfigVo>(){}.getType();
            ConfigVo configVo = this.parseJsonAsSingle(json,type);
            if(null == configVo) {
                LogHelper.log("%s   文件内容无法反序列化 -> 忽略！",System.lineSeparator());
                return;
            }

            // BB倍数配置
            if(null == configVo.getPotXBB() ||
                    configVo.getPotXBB()<=0){
                LogHelper.log("%s   【potXBB】必须大于0，不符合业务要求: %s -> 忽略！",System.lineSeparator(),configVo.getPotXBB());
                return;
            }

            // 干预次数配置
            if(null == configVo.getInterventions() ||
                    configVo.getInterventions().isEmpty()){
                LogHelper.log("%s   【interventions】配置不存在，不符合业务要求 -> 忽略！",System.lineSeparator());
                return;
            }
            Map<Integer,WeightInverventionConfigBo> configMap = this.parseInterventionConfig(configVo.getInterventions());
            if(null == configMap || configMap.isEmpty()){
                LogHelper.log("%s   【interventions】配置不存在，不符合业务要求 -> 忽略！",System.lineSeparator());
                return;
            }

            // 目标牌型比例
            Map<FlipCardHandType,WeightFlipCardConfigBo> ratioMap = this.parseFlipCardConfig(configVo.getFlipCards());
            if(null == ratioMap) return;

            WeightFlopInterventionConfigBo configBo = WeightFlopInterventionConfigBo.builder()
                    .potXBB(configVo.getPotXBB())
                    .interventions(configMap).build();
            WeightFlopInterventionConfigBo oldConfig = this.cache.updateInterventionConfig(configBo);
            LogHelper.log("%s    配置修改成功：%s -> %s！",System.lineSeparator(),oldConfig,configBo);

            Map<FlipCardHandType,WeightFlipCardConfigBo> oldRatioConfig = this.cache.updateFlipCardConfig(ratioMap);
            LogHelper.log("%s    配置修改成功：%s -> %s！",System.lineSeparator(),
                    oldRatioConfig==null?0:oldRatioConfig.size(),ratioMap.size());
        }catch (Exception ex){
            exception = ex;
            LogHelper.log("%s   系统异常: %s",System.lineSeparator(),ex.getMessage());
        }finally {
            if(null != traceMsg){
                LogHelper.removeLog();
                log.debug(traceMsg.toString());
            }
            if(null != exception)
                log.error(String.format("【干预内容-翻牌配置】处理失败: %s",filePath),exception);
        }
    }

    private Map<FlipCardHandType,WeightFlipCardConfigBo> parseFlipCardConfig(Map<String,Map<String,String>> flipCards){
        Map<FlipCardHandType,WeightFlipCardConfigBo> resultMap= null;
        if(null == flipCards || flipCards.isEmpty()){
            LogHelper.log("%s   【flipCards】配置不存在，不符合业务要求 -> 忽略！",System.lineSeparator());
            return resultMap;
        }

        Iterator<String> handPocerIt = flipCards.keySet().iterator();
        while(handPocerIt.hasNext()){
            String handPocer = handPocerIt.next();
            FlipCardHandType handType = FlipCardHandType.nameOf(handPocer);
            if(null == handType){
                LogHelper.log("%s   【flipCards】手牌[ %s ]不支持，不符合业务要求 -> 忽略！",System.lineSeparator(),handPocer);
                continue;
            }

            Map<String,String> values = flipCards.get(handPocer);
            if(null == values || values.isEmpty()){
                LogHelper.log("%s   【flipCards】手牌[ %s ] 对应的配置不存在，不符合业务要求 -> 忽略！",System.lineSeparator(),handPocer);
                continue;
            }

            int posRatio = 1;
            int minRatio;
            int maxRatio;
            List<WeightFlipCardRatioConfigBo> ratioConfigLst = new ArrayList<>();
            Iterator<String> targetTypeIt = values.keySet().iterator();
            while(targetTypeIt.hasNext()){
                String targetTypeName = targetTypeIt.next();
                FlipCardTargetType targetType = FlipCardTargetType.nameOf(targetTypeName);
                if(null == targetType){
                    LogHelper.log("%s   【flipCards】手牌[ %s ] 对应的目标牌型[ %s ]不支持，不符合业务要求 -> 忽略！",
                            System.lineSeparator(),handPocer,targetTypeName);
                    continue;
                }

                // 比例
                // >=0有效，其它当作0处理
                // 格式：50.00 或 50.00% , 最多2位小数
                String ratioStr = values.get(targetTypeName);
                Double ratio = Helper.parseIgnorePointFromPercentFormat(ratioStr);
                int ratioInt = 0;
                if(null == ratio || ratio <0){
                    LogHelper.log("%s   【flipCards】手牌[ %s ] 对应的目标牌型[ %s ]比例[ %s ] <0，不符合业务要求 -> 忽略！",
                            System.lineSeparator(),handPocer,targetTypeName,ratioStr);
                }else{
                    ratioInt = Helper.fixDecimalPlace(ratio,100);
                }

                if(ratioInt>0) {
                    minRatio = posRatio;
                    posRatio += ratioInt;
                    maxRatio = posRatio - 1;
                }else{
                    minRatio = 0;
                    maxRatio = 0;
                }

                WeightFlipCardRatioConfigBo config = WeightFlipCardRatioConfigBo.builder()
                        .flipCardType(targetType)
                        .ratio(ratioInt)
                        .minRatio(minRatio)
                        .maxRatio(maxRatio)
                        .build();
                ratioConfigLst.add(config);
            }

            if(ratioConfigLst.isEmpty()){
                LogHelper.log("%s   【flipCards】手牌[ %s ] 对应的配置不存在或无效，不符合业务要求 -> 忽略！",
                        System.lineSeparator(),handPocer);
                continue;
            }

            WeightFlipCardConfigBo configBo = WeightFlipCardConfigBo.builder()
                    .flipCardHandType(handType)
                    .maxRatio(posRatio)
                    .ratioConfigLst(ratioConfigLst)
                    .build();
            if(null == resultMap){
                resultMap = new HashMap<>();
            }
            resultMap.put(configBo.getFlipCardHandType(),configBo);
        }

        return resultMap;
    }

    @Getter
    @Setter
    private static class ConfigVo{
        /**
         * BB倍数
         * 必须>0
         */
        private Integer potXBB;

        /**
         * 干预次数配置
         */
        private List<WeightInverventionConfigVo> interventions;

        /**
         * 不同牌型的概率
         */
        private Map<String,Map<String,String>> flipCards;
    }
}