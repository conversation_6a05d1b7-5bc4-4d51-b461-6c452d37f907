package com.dzpk.weight.poker.constant;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum PokerType {
    /**
     * 高牌
     */
    HighCard(0),
    /**
     * 一对
     */
    OnePair(1),
    /**
     * 两对
     */
    TwoPair(2),
    /**
     * 三条
     */
    ThreeOfAKind(3),
    /**
     * 顺子
     */
    Straight(4),
    /**
     * 同花
     */
    Flush(5),
    /**
     * 葫芦：三条加一个对子
     */
    FullHouse(6),
    /**
     * 四条
     */
    FourOfAKind(7),
    /**
     * 同花顺
     */
    StraightFlush(8),
    /**
     * 皇家同花顺
     */
    RoyalStraightFlush(9);

    private static final Map<Integer, PokerType> VALUE_MAP = new HashMap<>();
    private Integer value;

    PokerType(Integer value) {
        this.value = value;
    }

    static {
        for (PokerType pokerType : PokerType.values()) {
            VALUE_MAP.put(pokerType.getValue(), pokerType);
        }
    }

    public static PokerType valueOf(Integer value){
        return VALUE_MAP.get(value);
    }
}
