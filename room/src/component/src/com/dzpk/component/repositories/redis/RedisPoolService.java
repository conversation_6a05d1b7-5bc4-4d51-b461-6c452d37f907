package com.dzpk.component.repositories.redis;

import com.dzpk.common.utils.LogUtil;
import org.apache.logging.log4j.Logger;

import com.i366.cache.Cache;


import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/**
 * redis 缓存服务类
 * <AUTHOR>
 *
 */
public class RedisPoolService {	
	private final static Logger logger = LogUtil.getLogger(RedisPoolService.class);

	private static JedisPool jedisPool = null;
	
	private static JedisPool getPool() {
		if (jedisPool == null) {
			JedisPoolConfig config = new JedisPoolConfig();
			config.setMaxTotal(5000);
			config.setMaxIdle(10);
			config.setTestOnBorrow(true);
			config.setTestOnReturn(true);

			jedisPool = new JedisPool(config,
					RedisUtil.getHostAddress(Cache.p, "redis.ip"),
					RedisUtil.getHostPort(Cache.p,"redis.port"),
					RedisUtil.getTimeout(Cache.p,"redis.timeout"),
					RedisUtil.getPasswd(Cache.p,"redis.pwd"));
		}
		return jedisPool;
	}
	
	/**
	 * 保持唯一调用出自RedisService
	 * @return
	 */
	public static Jedis getJedis() {
		JedisPool pool = getPool();
		return pool.getResource();
	}
	
	public static void close() {
		if (jedisPool != null) {
			jedisPool.close();
		}
	}
	
	public static void main(String[] args) {
		Jedis jedis = RedisPoolService.getJedis();
		jedis.set("sss", "bbb");
		System.out.println(jedis.get("sss"));
		jedis.close();
		//RedisPoolService.close();
		jedis = RedisPoolService.getJedis();
		jedis.set("ttt", "bbb");
		System.out.println(jedis.get("ttt"));
		jedis.close();
		RedisPoolService.close();
	}

}