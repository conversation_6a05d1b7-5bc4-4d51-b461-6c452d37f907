package com.dzpk.component.repositories.mysql.dbpool;

import com.dzpk.common.utils.LogUtil;
import org.apache.logging.log4j.Logger;

import java.util.Properties;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.DriverManager;



/**
 * Created by IntelliJ IDEA.
 * User: Administrator
 * To change this template use File | Settings | File Templates.
 */
public class ConnectionPool {

    public static Logger logger = LogUtil.getLogger(ConnectionPool.class);

    private static PoolProperties poolProperties = PoolProperties.getInstance() ;

    private static Properties args = new Properties();

    static {



        args.setProperty("proxool.driver-class",poolProperties.getProperty("jdbc-0.proxool.driver-class"));

        args.setProperty("proxool.driver-url",poolProperties.getProperty("jdbc-0.proxool.driver-url"));

        args.setProperty("user", poolProperties.getProperty("jdbc-0.user"));

        args.setProperty("password", poolProperties.getProperty("jdbc-0.password"));

        args.setProperty("proxool.alias", poolProperties.getProperty("jdbc-0.proxool.alias"));

        args.setProperty("proxool.house-keeping-sleep-time",poolProperties.getProperty("jdbc-0.proxool.house-keeping-sleep-time"));

        args.setProperty("proxool.verbose", poolProperties.getProperty("jdbc-0.proxool.verbose"));

        args.setProperty("proxool.trace", poolProperties.getProperty("jdbc-0.proxool.trace"));

        args.setProperty("proxool.maximum-connection-count",poolProperties.getProperty("jdbc-0.proxool.maximum-connection-count"));

        args.setProperty("proxool.minimum-connection-count",poolProperties.getProperty("jdbc-0.proxool.minimum-connection-count"));

        args.setProperty("proxool.maximum-connection-lifetime", poolProperties.getProperty("jdbc-0.proxool.maximum-connection-lifetime"));

        args.setProperty("proxool.simultaneous-build-throttle", poolProperties.getProperty("jdbc-0.proxool.simultaneous-build-throttle"));

        args.setProperty("proxool.recently-started-threshold", poolProperties.getProperty("jdbc-0.proxool.recently-started-threshold"));

        args.setProperty("proxool.overload-without-refusal-lifetime", poolProperties.getProperty("jdbc-0.proxool.overload-without-refusal-lifetime"));

        args.setProperty("proxool.maximum-active-time", poolProperties.getProperty("jdbc-0.proxool.maximum-active-time"));

        args.setProperty("proxool.fatal-sql-exception", poolProperties.getProperty("jdbc-0.proxool.fatal-sql-exception"));

        args.setProperty("proxool.house-keeping-test-sql", poolProperties.getProperty("jdbc-0.proxool.house-keeping-test-sql"));


    }



    public static Connection getConnection() {


        try {
            Class.forName("org.logicalcobwebs.proxool.ProxoolDriver");
            String url = "proxool." + args.getProperty("proxool.alias") + ":" + args.getProperty("proxool.driver-class") + ":" + args.getProperty("proxool.driver-url");
            Connection connection;
            connection = DriverManager.getConnection(url, args);
            return connection;

        }catch(Exception e) {

            e.printStackTrace();

            logger.error("Constuct JDBC Pool Error:" + e);

        }

        return null;
    }

    public static Connection getConnection(String urlName,String poolName) throws SQLException{
        logger.debug(">> Enter getConnection().") ;
        Connection conn ;
        try{
            String url = urlName + ":" + poolName;
            conn = DriverManager.getConnection(url);
            return conn;
        }catch(Exception ex){
            logger.error("Error in ConnectionPool.getConnection : " + ex.getMessage() );
            throw new SQLException(ex.getMessage());
        }
    }

}
