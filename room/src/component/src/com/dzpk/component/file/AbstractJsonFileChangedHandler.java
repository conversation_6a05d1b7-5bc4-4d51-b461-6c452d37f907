package com.dzpk.component.file;

import com.dzpk.common.utils.GsonHelper;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.lang.reflect.Type;
import java.nio.file.Path;
import java.util.List;
import java.util.Map;

public abstract class AbstractJsonFileChangedHandler {
    /**
     * 打开文件从文件中提取内容
     * 文件UTF-8的编码打开
     * @param filePath
     * @return
     */
    protected String readJson(Path filePath) throws Exception{
        if(null == filePath)
            return null;

        FileReader fileReader = null;
        BufferedReader bufferedReader = null;
        File file = filePath.toFile();
        try{
            fileReader = new FileReader(file);
            bufferedReader = new BufferedReader(fileReader);

            StringBuilder content = new StringBuilder();
            String line = null;
            while((line = bufferedReader.readLine()) != null){
                if(null == line || "".equals(line.trim()))
                    continue;

                content.append(line.trim());
            }

            return content.toString();
        }finally {
            if(null != bufferedReader)
                bufferedReader.close();
            if(null != fileReader)
                fileReader.close();
        }
    }

    protected <T> List<T> parseJson(String json,Type type){
        return GsonHelper.gson().fromJson(json,type);
    }

    protected <T> Map<String,T> parseMapJson(String json, Type type){
        return GsonHelper.gson().fromJson(json,type);
    }

    protected <T> T parseJsonAsSingle(String json,Type type){
        return GsonHelper.gson().fromJson(json,type);
    }

    public static String toJson(Object obj){
        return GsonHelper.gson().toJson(obj);
    }
}
