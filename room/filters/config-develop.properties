## port.properties
## 服务类型
service.type=room
## 服务器编号
## room从3001000开始
service.name=30010001

##客户端访问端口
socket.port=8051
##客户端访问IP/DNS
socket.ip=************

## 访问Res服务的配置
server.list=
client.maxWorkerThreadNum=8
client.channel.pool.maxNum=4
client.channel.pool.checkInterval=2
client.channel.readIdleTimeout=10
client.channel.readIdleMaxNum=3
client.auth.timeout=3
client.netty.logLevel=0

##定时读取配置文件间隔时间
load.room.space.time=1800000

## redis connected
#redis ip
redis.ip=127.0.0.1
##redis port
redis.port=6379
## redis password
redis.pwd=
## timeout(ms),default:5000
redis.timeout=5000

##mongo ip
mongodb.url=mongodb://账号:密码@*************:27079,*************:27080/?replicaSet=dzpk&maxPoolSize=2000&connectTimeoutMS=3000&socketTimeoutMS=5000
mongodb.database=crazy_poker

# mysql jdbc
jdbc.url=**************************************************************************************************************
jdbc.username=账号
jdbc.password=密码


## 控制发牌 1：控制发牌 0：不控制发牌
cards.control=0

#配置文件的ip限制开关  如果为0则是以房间为准，为1则ip限制失效
ip.limit = 0

#服务费的百分比 用带入记分牌*百分比即为服务费所需要的钻石数
service.fee=1

### Jackpot config
##jackpot白名单  example: 123456:盲注级别/实际(10/7)^^盲注级别/实际(20/14),5678910:盲注级别/实际(10/7)^^盲注级别/实际(20/14):
jackPot.white.list=
##jackpot累计次数抽水的盲注级别  jackpotid:盲注/盲注 逗号分隔符
jackPot.accumulative.blind=1:1/2/5/10/20/25/50/100
##zookeeper-START
##
# Zookeeper服务器的地址列表
# 格式: ip(hostname):port[,ip(hostname):port]*
# 必须提供
##
zk.connectString=127.0.0.1:2181,127.0.0.1:3181,127.0.0.1:4181

##
# 可选
# 必须大于0，否则忽略此设置
# 默认值：60 秒
##
zk.sessionTimeoutMs=60000

##
# 可选
# 必须大于0，否则忽略此设置
# 默认值：15 秒
##
zk.connectionTimeoutMs=15000

##
# 可选
# 必须大于0，否则忽略此设置
# 默认值：1 秒
##
zk.maxCloseWaitMs=1000

##
# 创建CuratorTempFramework时使用
# 可选
# 必须大于0，否则忽略此设置
# 默认值：3 分
##
zk.inactiveThresholdMs=180000

##
# 设置当前这个Zookeeper访问的命名空间
# 如果设置了，通过此实例访问的路径都将自动附加
# 上此设置作为路径的前缀。
# null或mepty，忽略此参数
##
zk.namespace=

##
# 1 = true
# otherwise false
##
zk.canBeReadOnly=
zk.useContainerParentsIfAvailable=
##zookeeper-END

##rabbitmq config
rabbitmq.host=地址
rabbitmq.user=账号
rabbitmq.password=密码
rabbitmq.port=端口

# ActiveMQ
activemq.url=failover://tcp://localhost:61616
activemq.userName=
activemq.password=