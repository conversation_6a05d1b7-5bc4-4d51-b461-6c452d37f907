package com.dzpk.crazypoker.message.constant;

import com.dzpk.crazypoker.common.rabbitmq.client.bean.ClubMessage;
import com.dzpk.crazypoker.common.utils.GsonUtils;
import com.google.gson.reflect.TypeToken;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * Created by jayce on 2019/4/9
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum EPushDeviceCode {

    ANDROID(0,"安卓"),
    IOS(1,"IOS")
    ;

    private int code;

    private String desc;

}
