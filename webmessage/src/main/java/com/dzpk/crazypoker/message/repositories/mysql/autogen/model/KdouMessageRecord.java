package com.dzpk.crazypoker.message.repositories.mysql.autogen.model;

import java.io.Serializable;
import java.util.Date;

public class KdouMessageRecord implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column message_kdou_record.msg_id
     *
     * @mbg.generated
     */
    private String msgId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column message_kdou_record.sender_id
     *
     * @mbg.generated
     */
    private String senderId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column message_kdou_record.reciver_id
     *
     * @mbg.generated
     */
    private String reciverId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column message_kdou_record.header
     *
     * @mbg.generated
     */
    private String header;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column message_kdou_record.title
     *
     * @mbg.generated
     */
    private String title;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column message_kdou_record.content
     *
     * @mbg.generated
     */
    private String content;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column message_kdou_record.remark
     *
     * @mbg.generated
     */
    private String remark;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column message_kdou_record.type
     *
     * @mbg.generated
     */
    private Integer type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column message_kdou_record.msg_status
     *
     * @mbg.generated
     */
    private Integer msgStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column message_kdou_record.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table message_kdou_record
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column message_kdou_record.msg_id
     *
     * @return the value of message_kdou_record.msg_id
     *
     * @mbg.generated
     */
    public String getMsgId() {
        return msgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column message_kdou_record.msg_id
     *
     * @param msgId the value for message_kdou_record.msg_id
     *
     * @mbg.generated
     */
    public void setMsgId(String msgId) {
        this.msgId = msgId == null ? null : msgId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column message_kdou_record.sender_id
     *
     * @return the value of message_kdou_record.sender_id
     *
     * @mbg.generated
     */
    public String getSenderId() {
        return senderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column message_kdou_record.sender_id
     *
     * @param senderId the value for message_kdou_record.sender_id
     *
     * @mbg.generated
     */
    public void setSenderId(String senderId) {
        this.senderId = senderId == null ? null : senderId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column message_kdou_record.reciver_id
     *
     * @return the value of message_kdou_record.reciver_id
     *
     * @mbg.generated
     */
    public String getReciverId() {
        return reciverId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column message_kdou_record.reciver_id
     *
     * @param reciverId the value for message_kdou_record.reciver_id
     *
     * @mbg.generated
     */
    public void setReciverId(String reciverId) {
        this.reciverId = reciverId == null ? null : reciverId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column message_kdou_record.header
     *
     * @return the value of message_kdou_record.header
     *
     * @mbg.generated
     */
    public String getHeader() {
        return header;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column message_kdou_record.header
     *
     * @param header the value for message_kdou_record.header
     *
     * @mbg.generated
     */
    public void setHeader(String header) {
        this.header = header == null ? null : header.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column message_kdou_record.title
     *
     * @return the value of message_kdou_record.title
     *
     * @mbg.generated
     */
    public String getTitle() {
        return title;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column message_kdou_record.title
     *
     * @param title the value for message_kdou_record.title
     *
     * @mbg.generated
     */
    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column message_kdou_record.content
     *
     * @return the value of message_kdou_record.content
     *
     * @mbg.generated
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column message_kdou_record.content
     *
     * @param content the value for message_kdou_record.content
     *
     * @mbg.generated
     */
    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column message_kdou_record.remark
     *
     * @return the value of message_kdou_record.remark
     *
     * @mbg.generated
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column message_kdou_record.remark
     *
     * @param remark the value for message_kdou_record.remark
     *
     * @mbg.generated
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column message_kdou_record.type
     *
     * @return the value of message_kdou_record.type
     *
     * @mbg.generated
     */
    public Integer getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column message_kdou_record.type
     *
     * @param type the value for message_kdou_record.type
     *
     * @mbg.generated
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column message_kdou_record.msg_status
     *
     * @return the value of message_kdou_record.msg_status
     *
     * @mbg.generated
     */
    public Integer getMsgStatus() {
        return msgStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column message_kdou_record.msg_status
     *
     * @param msgStatus the value for message_kdou_record.msg_status
     *
     * @mbg.generated
     */
    public void setMsgStatus(Integer msgStatus) {
        this.msgStatus = msgStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column message_kdou_record.create_time
     *
     * @return the value of message_kdou_record.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column message_kdou_record.create_time
     *
     * @param createTime the value for message_kdou_record.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}