package com.dzpk.crazypoker.message.repositories.mysql.autogen.mapper;

import com.dzpk.crazypoker.message.repositories.mysql.autogen.model.MessageRecord;
import com.dzpk.crazypoker.message.repositories.mysql.autogen.model.MessageRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MessageRecordMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_unread
     *
     * @mbg.generated
     */
    long countByExample(MessageRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_unread
     *
     * @mbg.generated
     */
    int deleteByExample(MessageRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_unread
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer userId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_unread
     *
     * @mbg.generated
     */
    int insert(MessageRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_unread
     *
     * @mbg.generated
     */
    int insertSelective(MessageRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_unread
     *
     * @mbg.generated
     */
    List<MessageRecord> selectByExample(MessageRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_unread
     *
     * @mbg.generated
     */
    MessageRecord selectByPrimaryKey(Integer userId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_unread
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") MessageRecord record, @Param("example") MessageRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_unread
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") MessageRecord record, @Param("example") MessageRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_unread
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MessageRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_unread
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MessageRecord record);
}