package com.dzpk.crazypoker.message.service.transaction;

import com.dzpk.crazypoker.message.service.bean.MessageUnreadInfo;
import com.dzpk.crazypoker.message.service.bean.PushInfoRecordBo;

import java.util.List;
import java.util.Map;

/**
 * Created by jayce on 2019/3/15
 *
 * <AUTHOR>
 */
public interface IMessageTransation {

    boolean createUserMessageRecord(Integer userId);

    boolean clearMsgUnReadNum(Integer userId, Integer clearType);

    boolean clearAllNum(Integer userId, int[] nums);

    boolean createNewSystemMessage(Map<Integer,String> lanMap,long messageTime,List<PushInfoRecordBo> dataList);

    Integer batchUpdateUserUnreadAndMsg(List<MessageUnreadInfo> dataList);

}
