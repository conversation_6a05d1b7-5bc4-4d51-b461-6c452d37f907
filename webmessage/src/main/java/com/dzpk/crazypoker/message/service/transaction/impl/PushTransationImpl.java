package com.dzpk.crazypoker.message.service.transaction.impl;

import com.dzpk.crazypoker.common.service.exception.ServiceException;
import com.dzpk.crazypoker.message.push.config.JPushProperties;
import com.dzpk.crazypoker.message.repositories.mysql.autogen.mapper.PushInfoRecordMapper;
import com.dzpk.crazypoker.message.repositories.mysql.autogen.model.PushInfoRecord;
import com.dzpk.crazypoker.message.service.transaction.IPushTransation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by jayce on 2019/3/26
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class PushTransationImpl implements IPushTransation {

    @Autowired
    private PushInfoRecordMapper pushInfoRecordMapper;

    @Autowired
    private JPushProperties pushProperties;

    @Transactional
    @Override
    public boolean updatePushInfo(Integer userId,Integer lanType, Integer deviceType) {
        Integer change = pushInfoRecordMapper.updateByPrimaryKeySelective(genPushInfoRecord(userId, lanType, deviceType));
        if(change<=0)throw new ServiceException(0);
        return true;
    }

    @Transactional
    @Override
    public boolean insertPushInfo(Integer userId,Integer lanType, Integer deviceType){
        Integer change = pushInfoRecordMapper.insertSelective(genPushInfoRecord(userId, lanType, deviceType));
        if(change<=0)throw new ServiceException(0);
        return true;
    }


    private PushInfoRecord genPushInfoRecord(int userId,Integer lanType, Integer deviceType) {
        PushInfoRecord pushInfo = new PushInfoRecord();
        pushInfo.setUserId(userId);
        pushInfo.setChannelId(pushProperties.getServerPrefix() + userId);//原先的regid 修改成了alias
        pushInfo.setLanType(lanType);
        pushInfo.setDeviceType(deviceType);
        pushInfo.setChannelSource(0);//默认值
        return pushInfo;
    }


    private PushInfoRecord genPushInfoRecord(int userId,Integer lanType, Integer deviceType, Integer channelSource) {
        PushInfoRecord pushInfo = new PushInfoRecord();
        pushInfo.setUserId(userId);
        pushInfo.setChannelId(pushProperties.getServerPrefix() + userId);//原先的regid 修改成了alias
        pushInfo.setLanType(lanType);
        pushInfo.setDeviceType(deviceType);
        pushInfo.setChannelSource(channelSource);
        return pushInfo;
    }

}
