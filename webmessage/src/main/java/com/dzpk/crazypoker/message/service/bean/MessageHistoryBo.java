package com.dzpk.crazypoker.message.service.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by jayce on 2019/4/1
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class MessageHistoryBo {

    @ApiModelProperty(notes = "加载方向，请求时，带的参数是多少，返回则是多少<br/>" +
            "lt = 上拉加载<br/>" +
            "gt = 下拉刷新")
    private Integer direction;

    private List<MessageHistoryListBo> list;

}
