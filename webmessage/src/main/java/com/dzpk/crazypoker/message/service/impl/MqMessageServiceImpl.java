package com.dzpk.crazypoker.message.service.impl;

import com.dzpk.crazypoker.common.rabbitmq.client.bean.*;
import com.dzpk.crazypoker.common.rabbitmq.constant.EMessageChannelCode;
import com.dzpk.crazypoker.common.rabbitmq.constant.EMessageCode;
import com.dzpk.crazypoker.common.redis.factory.IRedisInstanceFactory;
import com.dzpk.crazypoker.common.utils.GsonUtils;
import com.dzpk.crazypoker.common.utils.SecurityUtils;
import com.dzpk.crazypoker.message.constant.EMessageLanConfigCode;
import com.dzpk.crazypoker.message.constant.EMessagePushLanCode;
import com.dzpk.crazypoker.message.constant.EPushDeviceCode;
import com.dzpk.crazypoker.message.im.constant.EImMessageFormat;
import com.dzpk.crazypoker.message.push.config.FcmProperties;
import com.dzpk.crazypoker.message.push.config.SnsMessageCode;
import com.dzpk.crazypoker.message.service.IImService;
import com.dzpk.crazypoker.message.service.IMessageService;
import com.dzpk.crazypoker.message.service.IMqMessageService;
import com.dzpk.crazypoker.message.service.IPushService;
import com.dzpk.crazypoker.message.service.bean.ActivityRpUserInfo;
import com.dzpk.crazypoker.message.service.bean.ActivitySwitch;
import com.dzpk.crazypoker.message.service.bean.PushInfoRecordBo;
import com.dzpk.crazypoker.message.service.bean.RewardWinner;
import com.google.gson.reflect.TypeToken;
import com.dzpk.crazypoker.message.push.FcmPushClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import com.dzpk.crazypoker.message.repositories.mysql.autogen.mapper.UserDeviceMapper;
import com.dzpk.crazypoker.message.repositories.mysql.autogen.model.UserDevice;
import com.dzpk.crazypoker.message.repositories.mysql.autogen.model.UserDeviceExample;

import javax.annotation.PostConstruct;
import javax.validation.constraints.NotNull;
import java.net.UnknownHostException;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by jayce on 2019/3/27
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MqMessageServiceImpl implements IMqMessageService {

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private IPushService pushService;

    @Autowired
    private IImService imService;

    @Autowired
    private IMessageService messageService;

    @Autowired
    private UserDeviceMapper userDeviceMapper;

    @Autowired
    private FcmProperties fcmProperties;

    private FcmPushClient fcmPushClient;

    @Autowired
    private IRedisInstanceFactory redisInstanceFactory;

    //多设备
    private Set<Integer> deviceSet = new HashSet<>();

    /**
     * 初始化多设备
     */
    @PostConstruct
    private void initDeviceConfig() {
        deviceSet = Arrays.stream(EPushDeviceCode.values()).map(EPushDeviceCode::getCode).collect(Collectors.toSet());
        fcmPushClient = new FcmPushClient(fcmProperties);
        fcmPushClient.initialize();
    }

    private Set<Integer> refreshMessageSet = new HashSet<>();

    /**
     * 初始化刷新消息列表
     */
    @PostConstruct
    private void initRefreshMessageConfig() {
        refreshMessageSet = Arrays.stream(EMessageLanConfigCode.values()).filter(d -> d.isNeedRefresh()).map(EMessageLanConfigCode::getNum).collect(Collectors.toSet());
    }

    //文案代号
    private Map<Integer, String> pushContentMap = new HashMap<>();
    //文案对应参数数量
    private Map<Integer, Integer> pushParamMap = new HashMap<>();

    /**
     * 初始化文案代号
     */
    @PostConstruct
    private void initContentConfig() {
        pushContentMap = Arrays.stream(EMessageLanConfigCode.values()).collect(Collectors.toMap(EMessageLanConfigCode::getNum, EMessageLanConfigCode::getCode));
        pushParamMap = Arrays.stream(EMessageLanConfigCode.values()).collect(Collectors.toMap(EMessageLanConfigCode::getNum, EMessageLanConfigCode::getParamNum));
    }

    //多语言
    private Map<Integer, Locale> langMap = new HashMap<>();

    /**
     * 初始化多语言
     */
    @PostConstruct
    private void initLangConfig() {
        langMap = Arrays.stream(EMessagePushLanCode.values()).collect(Collectors.toMap(EMessagePushLanCode::getCode, EMessagePushLanCode::getLocaleArea));
    }

    @Override
    public void processHandler(@NotNull Integer type, Object message) throws UnknownHostException {
        if (null != type && null != message) {
            if (type >= 1 && type <= 200 && message instanceof ClubMessage) {//俱乐部消息
                ClubMessage cm = (ClubMessage) message;
                processMessage(cm.getPushChannel(), cm.getType(), cm.getContent(), cm.getRemark(), cm.getTitle(), cm.getReciverUserIds());
            } else if (type >= 201 && type <= 400 && message instanceof TribeMessage) {//联盟消息
                TribeMessage tm = (TribeMessage) message;
                processMessage(tm.getPushChannel(), tm.getType(), tm.getContent(), tm.getRemark(), tm.getTitle(), tm.getReciverUserIds());
            } else if (type >= 401 && type <= 600 && message instanceof SystemMessage) {//系统消息
                processSystemMessage((SystemMessage) message);
            } else if (type >= 601 && type <= 800 && message instanceof MoneyMessage) {//钱包消息
                MoneyMessage moneym = (MoneyMessage) message;
                processMessage(moneym.getPushChannel(), moneym.getType(), moneym.getContent(), moneym.getRemark(), moneym.getTitle(), moneym.getReciverUserIds());
            } else if (type >= 801 && type <= 1000 && message instanceof BagMessage) {//背包消息
                BagMessage bm = (BagMessage) message;
                processMessage(bm.getPushChannel(), bm.getType(), bm.getContent(), bm.getRemark(), bm.getTitle(), bm.getReciverUserIds());
            } else if (type >= 1001 && type <= 1200 && message instanceof ShareKdouMessage) {//分享赚金豆消息
                ShareKdouMessage skm = (ShareKdouMessage) message;
                processMessage(skm.getPushChannel(), skm.getType(), skm.getContent(), skm.getRemark(), skm.getTitle(), skm.getReciverUserIds());
            }else if (type >= 7000 && type <= 7200 && message instanceof SnsMessage) {//send fcm sns
                SnsMessage sns = (SnsMessage) message;
                processSns(sns);
            }else if (type >= 6001 && type <= 6200 && message instanceof InteriorMessage) {//内部消息
                processInteriorMessage((InteriorMessage) message);
            } else {//超出消息类型范围，报错提示
                log.error("processHandler error type={},={}", type, message.toString());
            }
        }
    }

    private void processMessage(int pushChannel, int messageType, String content, String remark, String title, List<String> reciverIds) {
        // 1、是否需要推送im
        if (pushChannel == EMessageChannelCode.TIM.getCode() || pushChannel == EMessageChannelCode.ALL.getCode()) {

            if (refreshMessageSet.contains(messageType)) {//属于需要刷新金豆的消息，发不同im消息
                imService.pushRefreshMessageNotify(reciverIds);
            } else {
                imService.pushNewMessageNotify(reciverIds);
            }
        }

        // 2、是否需要推送极光
        if (pushChannel == EMessageChannelCode.JPUSH.getCode() || pushChannel == EMessageChannelCode.ALL.getCode()) {

            Map<Integer, String> integerStringMap = genContentByMoreLan(pushContentMap.get(messageType), pushParamMap.get(messageType),
                    content, remark, title);
            sendPushMsg(reciverIds, integerStringMap);
        }
    }

    private void processSystemMessage(SystemMessage message) {
        if (message.getType() == EMessageCode.SYSTEM_CUSTOM.getCode()) {//自定义系统消息
            this.messageService.disposeCustomSystemMessage(message);
        } else {
            if (message.getPushChannel() == EMessageChannelCode.JPUSH.getCode() || message.getPushChannel() == EMessageChannelCode.ALL.getCode()) {

                Map<Integer, String> integerStringMap = genContentByMoreLan(pushContentMap.get(message.getType()), pushParamMap.get(message.getType()), message.getContent(), message.getRemark(), message.getTitle());
                sendPushMsg(message.getReciverUserIds(), integerStringMap);
            }

            if (message.getType() == EMessageCode.SYSTEM_USER_FORZEN.getCode()) {//冻结玩家
                imService.pushForzenMessageNotify(message.getReciverUserIds());
            } else {
                if (message.getPushChannel() == EMessageChannelCode.TIM.getCode() || message.getPushChannel() == EMessageChannelCode.ALL.getCode()) {
                    imService.pushNewMessageNotify(message.getReciverUserIds());
                }
            }
        }

    }

    private void processInteriorMessage(InteriorMessage message) throws UnknownHostException {
        log.info("进行消息处理：processInteriorMessage={}，请求参数如上", message.toString());
        // 内部消息处理
        if (message.getType() == EMessageCode.INTERIOR_CREATE_IM_GROUP.getCode()) {
            imService.createTempImGroup(message.getParam1(), message.getParam2());
        } else if (message.getType() == EMessageCode.INTERIOR_DIMISS_IM_GROUP.getCode()) {//解散im房间
            imService.dimissTempImGroup(message.getParam1(), message.getParam2());
        } else if (message.getType() == EMessageCode.INTERIOR_JOIN_IM_GROUP.getCode()) {//im加人
            imService.joinImGroup(message.getParam1(), message.getParam2());
        } else if (message.getType() == EMessageCode.INTERIOR_LEAVE_IM_GROUP.getCode()) {//im退群
            imService.leaveImGroup(message.getParam1(), message.getParam2());
        } else if (message.getType() == EMessageCode.INTERIOR_SWITCH_SERVER.getCode()) {//切换域名
            int pushChannel = Integer.parseInt(message.getParam3());//获取推送渠道
            if (pushChannel == EMessageChannelCode.TIM.getCode() || pushChannel == EMessageChannelCode.ALL.getCode()) {
                imService.pushSwitchServerToAllUser(message.getParam1());
            }
            if (pushChannel == EMessageChannelCode.JPUSH.getCode() || pushChannel == EMessageChannelCode.ALL.getCode()) {
                pushService.pushAllAndSetDataInExtra(message.getParam1());
            }
        } else if (message.getType() == EMessageCode.INTERIOR_REG_ACTIVITY.getCode()) {//活动消息
            //只会推送im
            imService.pushActivityNotify(message.getParam1(), message.getParam2(), message.getParam3());
        } else if (message.getType() == EMessageCode.INTERIOR_SKY_ACTIVITY.getCode()) {//天降红包活动
            List<ActivityRpUserInfo> dataList = GsonUtils.getInstance().fromJson(message.getParam1(), new TypeToken<List<ActivityRpUserInfo>>() {
            }.getType());
            //推送天降红包动画通知
            this.imService.pushRpActivityNotify(dataList);
            //记录未读消息、未读数量和消息记录
            this.messageService.batchUpdateUserUnreadAndMsg(dataList);

            //推送，红点通知
            for (ActivityRpUserInfo d : dataList) {
                List<String> list = new ArrayList<>();
                list.add(d.getUserId());
                processMessage(EMessageChannelCode.ALL.getCode(), EMessageCode.MONEY_SKY_ACTIVITY_GIVE.getCode(), String.valueOf(d.getNum()), null, null, list);
            }
        } else if (message.getType() == EMessageCode.INTERIOR_PMD_ACTIVITY.getCode()) {//跑马灯中奖
            List<RewardWinner> dataList = GsonUtils.getInstance().fromJson(message.getParam1(), new TypeToken<List<RewardWinner>>() {
            }.getType());
            //推送跑马灯中奖动画通知
            this.imService.pushPmdActivityNotify(dataList);
        } else if (message.getType() == EMessageCode.INTERIOR_PMD_ACTIVITY.getCode()) {//通用im通知
            this.imService.pushNewMessageNotify(GsonUtils.getInstance().fromJson(message.getParam1(), new TypeToken<List<String>>() {
            }.getType()));
        } else if (message.getType() == EMessageCode.INTERIOR_IMEI_CHANGE_NOTIFY.getCode()) {//限制的机器码修改
            List<String> userIdList = new ArrayList<>();
            userIdList.add(message.getParam1());
            List<String> imeiList = GsonUtils.getInstance().fromJson(message.getParam2(), new TypeToken<List<String>>() {
            }.getType());//机器码列表
            StringBuffer sb = new StringBuffer();
            for (String imei : imeiList) {
                if (sb.toString().isEmpty()) {
                    sb.append(imei);
                } else {
                    sb.append(",").append(imei);
                }
            }
            try {
                String key = message.getParam1() + SecurityUtils.AES_KEY;
                key = key.substring(0, 16);
                String data = SecurityUtils.Encrypt(sb.toString(), key);
                this.imService.pushMessageToUsers(userIdList, EImMessageFormat.CMD_IMEI_LIMIT_MESSAGE.getCode() + data);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (message.getType() == EMessageCode.INTERIOR_PUSH_IM_TO_USER_IN_ROOM.getCode()) {
            this.imService.putshToUserInRoom(message.getParam1(), message.getParam2());
        } else if (message.getType() == EMessageCode.INTERIOR_ACTIVITY_SWITCH.getCode()) {
            ActivitySwitch activity = GsonUtils.getInstance().fromJson(message.getParam1(), ActivitySwitch.class);
            this.imService.pushActivitySwitchToRoom(activity.getType(), activity.getSwitchType(), activity.getRoomIdList());
        }
    }

    /**
     * 获取多语言文案
     *
     * @param contentCode
     * @param paramStr
     * @return
     */
    private Map<Integer, String> genContentByMoreLan(String contentCode, Integer paramNum, String... paramStr) {
        if (contentCode == null || contentCode.isEmpty()) {
            return new HashMap<>();
        }
        Map<Integer, String> lanStrMap = new HashMap<>();
        if (paramStr.length == 0) {
            paramStr = null;
        }
        for (Integer langCode : langMap.keySet()) {
            lanStrMap.put(langCode, messageSource.getMessage(contentCode, Arrays.copyOfRange(paramStr, 0, paramNum), langMap.get(langCode)));
        }

        return lanStrMap;
    }

    /**
     * 极光推送
     *
     * @param reciveUserIds  接收id
     * @param pushContentMap 多语言文案
     */
    private void sendPushMsg(List<String> reciveUserIds, Map<Integer, String> pushContentMap) {
        if (null == pushContentMap) {
            log.error("language content is null");
            return;
        }
        // 查找用户id对应的alias 推送消息
        List<PushInfoRecordBo> pushInfoList = pushService.getPushInfoRecordsByStrings(reciveUserIds);
        if (null == pushInfoList) {
            log.error("query regid is null");
            return;
        }
        //去除null元素
        pushInfoList = pushInfoList.stream().filter(d -> d != null && !d.getChannelId().isEmpty()).collect(Collectors.toList());
        //遍历设备
        for (Integer deviceCode : deviceSet) {
            List<PushInfoRecordBo> deviceList = pushInfoList.stream().filter(d -> d.getDeviceType() == deviceCode).collect(Collectors.toList());
            //筛选多语言
            if (null != deviceList && deviceList.size() > 0) {//对应设备类型有数据
                for (Integer langCode : pushContentMap.keySet()) {//遍历多语言版本，推送
                    List<PushInfoRecordBo> langList = deviceList.stream().filter(d -> d.getLanType() == langCode).collect(Collectors.toList());
                    List<String> aliasList = langList.stream().map(PushInfoRecordBo::getChannelId).collect(Collectors.toList());
                    if (null != aliasList && aliasList.size() > 0) {//对应语言版本设备有数据
                        pushService.useRegIdPush(deviceCode, pushContentMap.get(langCode), aliasList.toArray(new String[aliasList.size()]));
                    }
                }
            }
        }
    }

    /**
     * FCM / AWS SNS
     * 
     */
    public void processSns(SnsMessage sns) {
        Integer type = sns.getType();
        String title = "";
        String message = "";
        if(type == 7000){
            message = sns.getMessage();
            title = sns.getTitle();
        }else{
            for (SnsMessageCode messageCode : SnsMessageCode.values()) {
                if (messageCode.getCode() == sns.getType()) {
                    title = messageCode.getTitle();
                    message =  messageCode.getDesc();
                    break;
                }
            }
        }

        if(sns.getUserId() == 0){
            List<List<String>> tokenList = processDeviceTokenList();
            fcmPushClient.sendSnsAll(tokenList, title, message);
            return;
        }

        StringRedisTemplate redisTemplate = redisInstanceFactory.defaultRedisInstance().getStringTemplate();

        // get heartbeat published from res server
        String onlineUserKey = "ous:" + sns.getUserId();
        String lastHeartbeat = redisTemplate.opsForValue().get(onlineUserKey);

        // skip if user is online (has heartbeat recently)
        if (lastHeartbeat != null && Instant.ofEpochMilli(Long.parseLong(lastHeartbeat)).isAfter(Instant.now().minusMillis(fcmProperties.getHeartbeatTolerance()))) {
            log.trace("Skipped SNS sending for user {} - user is online", sns.getUserId());
            return;
        }

        String deviceTokenCacheKey = "userDeviceToken:" + sns.getUserId();
        String deviceToken = redisTemplate.opsForValue().get(deviceTokenCacheKey);
        if (deviceToken == null) {
            UserDevice userDevice = userDeviceMapper.selectByUserId(sns.getUserId());
            if (userDevice != null) {
                deviceToken = userDevice.getDeviceToken();
                if (deviceToken == null || deviceToken.isEmpty()) {
                    deviceToken = "!!"; // magic string for missing token
                }
                redisTemplate.opsForValue().set(deviceTokenCacheKey, deviceToken, Duration.parse(fcmProperties.getDeviceTokenCachePeriod()));
            } else {
                log.trace("Device not found for user {}", sns.getUserId());
            }
        }
        if (deviceToken != null && !deviceToken.equals("!!")) {
            fcmPushClient.sendSns(deviceToken, title, message);
        } else {
            log.trace("Skipped SNS sending for user {} - missing device token", sns.getUserId());
        }
    }

    public List<List<String>> processDeviceTokenList() {

        UserDeviceExample select = new UserDeviceExample();
        select.createCriteria()
            .andDeviceTokenIsNotNull()
            .andDeviceTokenNotEqualTo("");

        List<UserDevice> devicesWithToken = userDeviceMapper.selectByExample(select);

        List<String> tokenList = devicesWithToken.stream()
            .map(UserDevice::getDeviceToken)
            .collect(Collectors.toList());

        List<List<String>> deviceTokenLists = new ArrayList<>();
        for (int i = 0; i < tokenList.size(); i += 500) {
            List<String> batchTokens = tokenList.subList(i, Math.min(i + 500, tokenList.size()));
            deviceTokenLists.add(batchTokens);
        }

        return deviceTokenLists;
    }
}
