package com.dzpk.crazypoker.message.repositories.mysql.autogen.mapper;

import com.dzpk.crazypoker.message.repositories.mysql.autogen.model.MessageTaskRecord;
import com.dzpk.crazypoker.message.repositories.mysql.autogen.model.MessageTaskRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MessageTaskRecordMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_task
     *
     * @mbg.generated
     */
    long countByExample(MessageTaskRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_task
     *
     * @mbg.generated
     */
    int deleteByExample(MessageTaskRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_task
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_task
     *
     * @mbg.generated
     */
    int insert(MessageTaskRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_task
     *
     * @mbg.generated
     */
    int insertSelective(MessageTaskRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_task
     *
     * @mbg.generated
     */
    List<MessageTaskRecord> selectByExample(MessageTaskRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_task
     *
     * @mbg.generated
     */
    MessageTaskRecord selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_task
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") MessageTaskRecord record, @Param("example") MessageTaskRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_task
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") MessageTaskRecord record, @Param("example") MessageTaskRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_task
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MessageTaskRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_task
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MessageTaskRecord record);
}