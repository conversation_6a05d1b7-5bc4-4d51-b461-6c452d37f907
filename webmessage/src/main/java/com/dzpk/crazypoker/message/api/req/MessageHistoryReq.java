package com.dzpk.crazypoker.message.api.req;

import com.dzpk.crazypoker.common.validators.AnyIntRange;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * Created by jayce on 2019/3/16
 *
 * <AUTHOR>
 */
@Setter
@Getter
@ApiModel(value = "消息-历史记录")
public class MessageHistoryReq {

    @ApiModelProperty(name = "请求历史消息类型",
            position = 0,
            notes = "请求历史消息类型<br/>" +
                    "1 = 俱乐部消息<br/>" +
                    "2 = 联盟消息<br/>" +
                    "3 = 系统消息<br/>" +
                    "4 = 钱包消息<br/>" +
                    "5 = 分享赚金豆消息<br/>" +
                    "6 = 背包消息")
    @NotNull(message = "messageType can't null")
    @AnyIntRange({1,2,3,4,5,6})
    private Integer messageType;

    @ApiModelProperty(name = "请求时间",
            position = 1,
            notes = "请求时间第一次传0，其余时间传上次请求时间")
    @NotNull(message = "time can't null")
    private Long time = 0L;         // 时间戳 int  第一次传0

    @ApiModelProperty(name = "加载方向",
            position = 2,
            notes = "加载方向<br/>" +
                    "0 = 下拉刷新<br/>" +
                    "1 = 上拉加载")
    @NotNull(message = "direction can't null")
    private Integer direction = 0;

    @ApiModelProperty(name = "请求语言类型",
            position = 3,
            notes = "请求语言类型<br/>" +
                    "0 = 中文<br/>" +
                    "1 = 英文<br/>" +
                    "2 = 繁体")
    @NotNull(message = "lanType can't null")
    @AnyIntRange({0,1,2})
    private Integer lanType;
}
