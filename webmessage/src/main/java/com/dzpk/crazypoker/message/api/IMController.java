package com.dzpk.crazypoker.message.api;

import com.dzpk.crazypoker.common.dozer.BeanUtil;
import com.dzpk.crazypoker.common.service.InvokedResult;
import com.dzpk.crazypoker.common.web.controller.AbstractController;
import com.dzpk.crazypoker.common.web.resp.CommonResponse;
import com.dzpk.crazypoker.message.api.req.ImUploadInfoReq;
import com.dzpk.crazypoker.message.api.vo.IMTokenVo;
import com.dzpk.crazypoker.message.service.IImService;
import com.dzpk.crazypoker.message.service.bean.IMTokenBo;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * Created by jayce on 2019/3/25
 *
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping(
        value = "/im/",
        method = RequestMethod.POST,
        consumes = "application/json;charset=UTF-8",
        produces = "application/json;charset=UTF-8")
@Api(tags = {"IM API"})
public class IMController extends AbstractController {

    @Autowired
    private BeanUtil beanUtil;

    @Autowired
    private IImService imService;

    @ResponseBody
    @RequestMapping("gentoken")
    public CommonResponse<IMTokenVo> genToken(@RequestAttribute(value = "user_id")int userId){
        log.info("获取生成票据信息，用户id:{}",userId);
        CommonResponse<IMTokenVo> response = new CommonResponse<>();
        InvokedResult<IMTokenBo> result = imService.genToken(userId);
        if(null != result.getData()){
            response.setData(this.beanUtil.map(result.getData(),IMTokenVo.class));
        }
        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());

        return response;
    }

    @ResponseBody
    @RequestMapping("upim_info")
    public CommonResponse uploadInfo(@RequestAttribute(value = "user_id")int userId,
                                     @Validated @RequestBody ImUploadInfoReq request,
                                     BindingResult paramValidResult){
        //常规校验
        handleValidatedError(paramValidResult);

        CommonResponse response = new CommonResponse<>();
        InvokedResult result = imService.uploadInfo(userId,request.getGroupId());

        response.setStatus(result.getCode());
        response.setMsg(result.getMsg());

        return response;
    }

}
