package com.dzpk.crazypoker.appmessage.utils;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * AppMessageLan
 *
 * <AUTHOR>
 * @date 2024/11/9
 */
@AllArgsConstructor
@Getter
public enum AppMessageLang {

    /**
     * 中文
     */
    ZH_CN(0, "zh_CN"),

    /**
     * 英文
     */
    EN_US(1, "en_US"),

    /**
     * 繁体中文
     */
    ZH_HK(2, "zh_HK");

    /**
     * 值
     */
    private final Integer value;

    /**
     * 代码
     */
    private final String code;


    public static AppMessageLang getByValue(Integer value) {
        for (AppMessageLang lang : AppMessageLang.values()) {
            if (lang.getValue().equals(value)) {
                return lang;
            }
        }
        return null;
    }

    public static AppMessageLang getByCode(String code) {
        for (AppMessageLang lang : AppMessageLang.values()) {
            if (lang.getCode().equals(code)) {
                return lang;
            }
        }
        return null;
    }

    /**
     * 根据value获取code
     * @param value 值
     * @return 代码
     */
    public static String getCodeByValue(Integer value) {
        AppMessageLang lang = getByValue(value);
        return lang == null ? null : lang.getCode();
    }
}
