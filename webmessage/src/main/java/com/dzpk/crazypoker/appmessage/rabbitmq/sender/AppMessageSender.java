package com.dzpk.crazypoker.appmessage.rabbitmq.sender;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dzpk.crazypoker.appmessage.rabbitmq.sender.bean.*;
import com.dzpk.crazypoker.appmessage.rabbitmq.sender.config.AppMessageRabbitMqKeys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * AppMessageSender
 * 消息发送器
 * <AUTHOR>
 * @date 2024/11/11
 */
@Slf4j
@Component
public class AppMessageSender {


    /**
     * 发送业务消息
     * @param param 参数
     * @return 是否发送成功
     */
    public Boolean sendBusinessMessage(BusinessMessage param) {
        // 校验参数
        if (param == null) {
            log.error("BusinessMessage is null");
            return false;
        }

        if (param.getBusinessCode() == null) {
            log.error("businessCode is null");
            return false;
        }

        if (param.getReceiverUserId() == null) {
            log.error("receiverUserId is null");
            return false;
        }

        sendMessage(AppMessage.builder()
                .messageId(param.getMessageId())
                .businessCode(param.getBusinessCode())
                .businessData(param.getBusinessData())
                .senderId(param.getSenderId())
                .receiverUserId(param.getReceiverUserId())
                .build());

        return true;
    }

    /**
     * 发送通知消息
     * @param param 参数
     * @return 是否发送成功
     */
    public Boolean sendNoticeMessage(NoticeMessage param) {

        // 校验参数
        if (param == null) {
            log.error("NoticeMessage is null");
            return false;
        }

        if (param.getNoticeId() == null) {
            log.error("noticeId is null");
            return false;
        }

        sendNotice(AppNotice.builder()
                .noticeId(param.getNoticeId())
                .messageId(param.getMessageId())
                .build());
        return true;
    }

    /**
     * 发送模板消息
     * @param param 参数
     * @return 是否发送成功
     */
    public Boolean sendTemplateMessage(TemplateMessage param) {
        // 校验参数
        if (param == null) {
            log.error("TemplateMessage is null");
            return false;
        }

        if (param.getTplCode() == null) {
            log.error("tplCode is null");
            return false;
        }

        if (param.getReceiverUserId() == null) {
            log.error("receiverUserId is null");
            return false;
        }

        sendMessage(AppMessage.builder()
                .messageId(param.getMessageId())
                .tplCode(param.getTplCode())
                .params(param.getParams())
                .senderId(param.getSenderId())
                .receiverUserId(param.getReceiverUserId())
                .build());

        return true;
    }

    /**
     * 发送内容消息
     * @param param 参数
     * @return 是否发送成功
     */
    public Boolean sendContentMessage(ContentMessage param) {
        // 校验参数
        if (param == null) {
            log.error("ContentMessage is null");
            return false;
        }

        if (param.getContent() == null) {
            log.error("content is null");
            return false;
        }

        // 处理内容消息

        if (param.getCategoryCode() == null) {
            log.error("categoryCode is null");
            return false;
        }

        if (param.getReceiverUserId() == null) {
            log.error("receiverUserId is null");
            return false;
        }

        sendMessage(AppMessage.builder()
                .messageId(param.getMessageId())
                .categoryCode(param.getCategoryCode())
                .content(JSONObject.toJSONString(param.getContent()))
                .renderType(param.getRenderType())
                .params(param.getParams())
                .senderId(param.getSenderId())
                .receiverUserId(param.getReceiverUserId())
                .build());
        return true;
    }


    @Resource
    private RabbitTemplate rabbitTemplate;


    /**
     * 发送 AppMessage 消息
     * @param appMessage 消息
     */
    private void sendMessage(AppMessage appMessage) {
        try {
            String messageJson = JSON.toJSONString(appMessage);
            // 使用 RabbitTemplate 发送消息到指定的 Exchange 和 RoutingKey
            rabbitTemplate.convertAndSend(
                    AppMessageRabbitMqKeys.Exchange.MESSAGE,
                    AppMessageRabbitMqKeys.RoutingKey.MESSAGE,
                    messageJson,
                    new MessagePostProcessor() {
                        @Override
                        public Message postProcessMessage(Message message) {
                            return message;
                        }
                    });
            log.info("Sent message: {}", messageJson);
        } catch (Exception e) {
            log.error("Failed to send message: {}", appMessage, e);
        }
    }

    /**
     * 发送 AppNotice 消息
     * @param appNotice 消息
     */
    private void sendNotice(AppNotice appNotice) {
        try {
            String noticeJson = JSON.toJSONString(appNotice);
            // 使用 RabbitTemplate 发送消息到指定的 Exchange 和 RoutingKey
            rabbitTemplate.convertAndSend(
                    AppMessageRabbitMqKeys.Exchange.NOTICE,
                    AppMessageRabbitMqKeys.RoutingKey.NOTICE,
                    noticeJson,
                    new MessagePostProcessor() {
                        @Override
                        public Message postProcessMessage(Message message) {
                            return message;
                        }
                    });
            log.info("Sent notice: {}", noticeJson);
        } catch (Exception e) {
            log.error("Failed to send notice: {}", appNotice, e);
        }
    }





}
