package com.dzpk.crazypoker.business.retry.core;


import com.dzpk.crazypoker.appmessage.utils.MapBuilder;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;


/**
 * MessageDelayRetryFactory
 *
 * <AUTHOR>
 * @date 2025/2/25
 */
interface MessageDelayRetryFactory {

    static TopicExchange createDelayRetryExchange(DelayRetryBean bean) {
        return new TopicExchange(bean.getExchange(), true, false);
    }

    static Queue createDelayRetryQueue(DelayRetryBean bean) {

        return new Queue(bean.getQueue(), true, false, false, MapBuilder.builder()
                // 死信到主交换器
                .put("x-dead-letter-exchange", bean.getExchange())
                // 路由到原队列
                .put("x-dead-letter-routing-key",bean.getRoutingKey())
                .build());
    }

    static Binding createDelayRetryBinding(DelayRetryBean bean) {
        return BindingBuilder.bind(createDelayRetryQueue(bean))
                .to(createDelayRetryExchange(bean))
                .with(bean.getRoutingKey());
    }
    
    
}
