package com.dzpk.crazypoker.common.web.token.service;

public interface IFrequencyService {
    /**
     * 检查用户是否允许访问此API
     * 1. api:fp:{token}:api:denysec作为key，检查redis是否存在
     *    存在则拒绝；否则进行第2步判断
     *    此key的值设置当前系统时间，超时时间：60秒
     *
     * 2. api:fp:{token}:api:acsnum不存在或其值+1后小于或等于最大值，则允许访问；
     *    否则设置第1步的key
     *
     *     此key的值设置为整数，表示累加次数，超时时间：1秒
     *
     * 以上操作必须保持原子性，由于前后值存在依赖关系，所以采用lua脚本方式执行
     * 调试命令：
     * eval "local t=redis.call('exists',KEYS[1]);if(t==1) then return 0; end;t=redis.call('get',KEYS[2]);if(not t) then t=1; redis.call('set',KEYS[2],t); redis.call('expire',KEYS[2],ARGV[1]);return 1; else t=tonumber(t)+1; end;if(t<=tonumber(ARGV[2])) then redis.call('incr',KEYS[2]);return 1 end; redis.call('del',KEYS[2]); redis.call('set',KEYS[1],1);redis.call('expire',KEYS[1],ARGV[3]); return 0" 2 "api:fp:4444:45555:denysec" "api:fp:4444:45555:acsnum" 60 10 60
     *
     * @param token        必填
     * @param apiName     必填
     * @return
     *    true  ：表示允许访问
     *    false ：表示不允许访问
     */
    boolean doPrivileged(String token, String apiName);
}
