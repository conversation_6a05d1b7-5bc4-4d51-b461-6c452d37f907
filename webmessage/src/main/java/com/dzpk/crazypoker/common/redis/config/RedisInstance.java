package com.dzpk.crazypoker.common.redis.config;

import lombok.Getter;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.StringUtils;

@Getter
public class RedisInstance {
    private String name;
    private boolean isDefault;
    private LettuceConnectionFactory connectionFactory;
    private RedisTemplate template;
    private StringRedisTemplate stringTemplate;
    private ReactiveRedisTemplate reactiveRedisTemplate;

    public RedisInstance(String name,
                         LettuceConnectionFactory connectionFactory,
                         RedisTemplate template,
                         StringRedisTemplate stringTemplate,
                         ReactiveRedisTemplate reactiveRedisTemplate){
        if(StringUtils.isEmpty(name))
            this.isDefault = true;
        this.name = name;
        this.connectionFactory = connectionFactory;
        this.template = template;
        this.stringTemplate = stringTemplate;
        this.reactiveRedisTemplate = reactiveRedisTemplate;
    }
}
