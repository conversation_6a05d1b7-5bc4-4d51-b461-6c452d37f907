package com.dzpk.crazypoker.common.validators.impl;

import com.dzpk.crazypoker.common.validators.ListIntRange;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;

@Slf4j
public class ListIntRangeValidatorImpl implements ConstraintValidator<ListIntRange,List<Integer>> {
    private int min = -1;
    private int max = -1;

    @Override
    public void initialize(ListIntRange constraint) {
       this.min = constraint.min();
       this.max = constraint.max();

       log.info("初始化List元素范围值validator: min={},max={}",this.min,this.max);
    }

    @Override
    public boolean isValid(List<Integer> target, ConstraintValidatorContext context) {
        if(null == target || target.isEmpty())
            return true;

        for(Integer e : target){
            if(e == null){
                return false;
            }

            if(!(e>=this.min && e<=this.max))
            {
                return false;
            }
        }

        return true;
    }
}
