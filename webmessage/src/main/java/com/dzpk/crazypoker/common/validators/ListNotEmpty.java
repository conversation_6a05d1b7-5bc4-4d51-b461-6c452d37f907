package com.dzpk.crazypoker.common.validators;

import com.dzpk.crazypoker.common.validators.impl.ListNotEmptyValidatorImpl;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target({ElementType.METHOD,
        ElementType.FIELD,
        ElementType.ANNOTATION_TYPE,
        ElementType.CONSTRUCTOR,
        ElementType.PARAMETER,
        ElementType.TYPE_USE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(
        validatedBy = {ListNotEmptyValidatorImpl.class}
)
public @interface ListNotEmpty {
    String message() default "{javax.validation.constraints.ListNotEmpty.message}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
