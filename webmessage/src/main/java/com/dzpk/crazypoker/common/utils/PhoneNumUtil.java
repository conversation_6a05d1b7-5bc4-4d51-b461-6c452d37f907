package com.dzpk.crazypoker.common.utils;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Base64;

@Slf4j
@Setter
@Getter
public class PhoneNumUtil {

    public static String encoder(String phone) {
        if (phone == null) {
            log.error("phone number {} encoder error!", phone);
            return null;
        }

        // 第三位和最后一位交换位置
        phone = exchange(phone.toCharArray(), 5, phone.length() - 1);
        return Base64.getEncoder().encodeToString(phone.getBytes());
    }

    public static String decoder(String phone) {
        phone = new String(Base64.getDecoder().decode(phone));
        if (phone == null || phone.equals("") || phone.length() < 4) {
            log.info("phone number decoder error!");
            return null;
        }
        return exchange(phone.toCharArray(), 5, phone.length() - 1);
    }

    // 号码打*
    public static String mark(String phone) {
        if (phone.length() <= 6) {
            return phone;
        }
        int pre = 3 + phone.indexOf("-");
        int end = 3;
        StringBuilder markPhone = new StringBuilder();
        markPhone.append(phone, 0, pre);
        for (int i = pre; i < phone.length() - end; i++) {
            markPhone.append("*");
        }
        markPhone.append(phone.substring(phone.length() - end));
        return markPhone.toString();
    }

    private static String exchange(char[] nums, int index, int index1) {
        char temp = nums[index];
        nums[index] = nums[index1];
        nums[index1] = temp;
        return new String(nums);
    }

    private static String[] splitPhoneNum(String phone) {
        if (StringUtils.isEmpty(phone)) {
            return null;
        }
        String[] strs = phone.split("-");

        if (strs == null
                || strs.length != 2
                || StringUtils.isEmpty(strs[0])
                || StringUtils.isEmpty(strs[1])) {
            return null;
        }
        return strs;
    }

    public static void main(String[] args) {
    }

}
