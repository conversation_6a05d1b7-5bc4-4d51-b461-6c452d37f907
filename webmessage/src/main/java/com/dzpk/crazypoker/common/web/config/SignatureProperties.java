package com.dzpk.crazypoker.common.web.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

@Setter
@Getter
@ConfigurationProperties(prefix = "signature")
public class SignatureProperties {
    /** 是否启用 */
    private boolean enable;
    /** 白名单URL列表 */
    private List<String> whitelistUrls;
    /** 签名密钥 */
    private String secretKey;
    /** 签名对应的HTTP头部名称 */
    private String fieldName;
    /** 字符集编码 */
    private String charsetName;
    /** 响应流的最大长度 */
    private String bufferSize;
}
