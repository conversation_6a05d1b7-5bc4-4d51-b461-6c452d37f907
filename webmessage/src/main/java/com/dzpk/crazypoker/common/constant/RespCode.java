package com.dzpk.crazypoker.common.constant;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 错误信息码表
 * 0~999为系统级别的错误码
 * <p>
 * 0-成功
 * <p>
 * 999-系统异常
 * <p>
 * 1000以后为业务性错误码,业务前缀码+两位错误码.(每一个业务模块占100个错误码)
 * <p>
 * 如:短信模块用使用的是1000到1099范围10就属于短信模块的前缀
 * <p>
 * 变量命名规范:模块名_错误描述(变量全为3大写,多个单词之间用下划线隔开)
 * <p>
 * 所有错误码统一到此类中,错误码的前缀按照先提交先占用,后提交后修改冲突错误码.
 */

@AllArgsConstructor
@Getter
public enum RespCode {

    SUCCEED(0, "ok"),

    ILLEGAL_REQ(995, "非法请求"),
    INVALID_SIGNATURE(996, "签名无效"),
    INVALID_TOKEN(997, "token无效"),
    FREQUENCY(998, "访问过于频繁"),
    FAILED(999, "failed"),

    // 公共错误代码开始
    PARAM_REQUIRED(101,"参数值必须指定！"),
    PARAM_TOLONG(102,"参数长度超出允许范围！"),
    PARAM_MINVALUE(103,"参数值未满足最小值约束！"),
    PARAM_INVALID(104,"参数值无效！"),
    PARAM_UNKNOWN(199,"未知参数错误！"),
    // 公共错误代码开始


    /**
     * 短信模块（1000-1099）
     */
    SMS_SEND_ERROR(1000, "发送短信失败"),
    SMS_CAPTCHA_CODE_ERROR(1001, "验证码校验错误"),


    //用户模块开始（1100-1199）
    USER_PHONE_ILLEGAL(1100,"手机不合法"),
    USER_PHONE_EXIST(1101,"手机已注册"),
    USER_PHONE_NOT_EXIST(1102,"手机未注册"),
    USER_DEVICE_NOT_IMEI(1103,"手机已注册"),
    USER_DEVICE_IMEI_LIMIT(1104,"超过了IMEI限制"),
    USER_FORBID_SIMULATOR(1105, "禁止模拟器注册"),
    USER_CHIP_NOT_ENOUGH(1106, "金豆数不足"),
    USER_NOT_EXIST(1107, "用户不存在"),
    USER_NICK_ILLEGAL(1108, "用户名非法,不能包含Emoji,@,%以及逗号(,)等违法字符"),
    USER_NAME_EXIST(1109, "用户名已经存在"),


    //用户模块结束

    //*******俱乐部模块开始(1200-1299)******
    CLUB_PARAM_ERROR(1200,"参数错误"),
    CLUB_PERMISSION_ERROR(1201,"无俱乐部权限操作"),
    CLUB_CRETING(1202,"俱乐部创建中"),
    CLUB_HAS(1203,"已有俱乐部"),
    CLUB_NAME_EXIST(1204,"俱乐部名称重名"),
    CLUB_NOT_EXIST(1205,"俱乐部不存在"),
    CLUB_MEMBER_LIMIT(1206,"俱乐部人员上限"),
    CLUB_NOT_RESULT(1207,"俱乐部接口查找无结果"),
    CLUB_FUND_INVALID(1208,"俱乐部基金冻结"),
    CLUB_FUND_NOT_ENOUGH(1209,"俱乐部基金不足"),
    CLUB_IS_PAY_CHANNEL(1210, "俱乐部为代充渠道,不允许更改交易模式"),
    CLUB_PAY_CHANNEL_TIME_LIMIT(1210, "更改交易模式过于频繁,30天内只允许更改一次"),

    //*******俱乐部模块结束*****************


    //*******联盟模块开始(1300-1399)******
    TRIBE_HAS(1300,"俱乐部已有联盟"),
    TRIBE_NAME_EXIST(1301,"联盟名称重名"),
    TRIBE_NOT_JOIN(1302,"没有加入的联盟"),
    TRIBE_PERMISSION_ERROR(1303,"无联盟权限操作"),
    TRIBE_REMOVE_LOGIC_ERROR(1304,"不能踢出主俱乐部"),
    TRIBE_CRETING(1305,"联盟创建中"),
    TRIBE_NOT_EXIST_CLUB(1306,"联盟不存在该俱乐部"),
    TRIBE_PARAM_ERROR(1307,"参数错误"),
    TRIBE_HAS_REQUEST(1308,"已经向该联盟申请过"),

    //*******联盟模块结束*****************


    //VIP用户模块开始（1400-1499）
    VIP_USER_NOT_OPEN(1400, "没有开通VIP"),
    VIP_NOT_ALLOW_TO_BREAK(1401, "无权破隐"),
    VIP_HAVE_BEEN_BREAK(1402, "已破隐"),
    VIP_NEED_TO_BREAK(1403, "可以破隐，未破隐"),
    VIP_MISS_PARAM(1404, "缺失必要参数"),
    VIP_ILLEGAL_PARAM(1405, "参数不合法"),
    VIP_NOT_ALLOW_DOWN_LEVEL(1406, "VIP无法降级"),
    VIP_NOT_ALLOW_UP_LEVEL(1407, "VIP升级无效"),
    //VIP用户模块结束（1200-1299）

    //*******推广模块模块开始(1500-1599)******
    POPULARIZE_USER_NOT_IN_CLUB(1500, "请先加入俱乐部，才可以成为推广员"),
    POPULARIZE_USER_DETAIL_INFO_NOT_EXIST(1501, "玩家详细信息不存在"),
    POPULARIZE_INSERT_RELATION_TABLE_ERROR(1502, "插入用户推广员绑定上下级关系表出错"),
    POPULARIZE_INSERT_USER_INFO_TABLE_ERROR(1503, "插入用户推广信息表出错"),
    //*******推广模块结束*****************

    //sns模块开始（1600-1699）
    SNS_NICKNAME_ILLEGAL(1600, "昵称非法,不能包含Emoji,@,%以及逗号(,)等违法字符"),
    SNS_NICKNAME_LIMIT(1601, "超过了最大备注数"),
    //sns模块结束（1600-1699）


    //BackPack模块开始（1700-1799）
    BACKPACK_NO_ADDRESS(1700, "没有收货地址"),
    BACKPACK_NO_PRIZE(1701, "没有奖品"),
    BACKPACK_APPLYING(1702, "正在申请"),
    BACKPACK_RECEIVED(1703, "已经领取"),
    //BackPack模块结束（1700-1799）

    //BrandSpe模块开始（1800-1899）
    BRANDSPE_COLLECT_LIMIT(1800, "收藏牌谱超过限制"),
    //BrandSpe模块结束（1800-1899）

    //DataStat模块开始（1900-1999）
    DATASTAT_NO_ROOMPATH(1900, "没有该牌局类型"),
    //BrandSpe模块结束（1900-1999）

    // 牌局(列表&组局&进房)模块开始（2000-20100）
    ROOM_JACKPOT_SETTING_NOTFOUND(2000,"jackpot配置数据缺失"),
    ROOM_NOT_EXISTING(2001,"牌局不存在或已解散"),
    ROOM_NO_PERMISSION(2002,"权限不足"),
    ROOM_SERVER_NOTONLINE(2003,"牌局服务不可用"),
    ROOM_ENTER_USERFREEZE(2004,"账号已被冻结"),
    ROOM_ENTER_USERNOCLUB(2005,"玩家未加入俱乐部"),
    ROOM_ENTER_CLUBCLOSED(2006,"玩家所属俱乐部已被关闭"),
    ROOM_ENTER_CLUBNOTRIBE(2007,"玩家所属俱乐部未绑定联盟"),
    ROOM_ENTER_CLUB_TRANSFERINGORKICKED(2008,"玩家所属俱乐部转移中或已被踢出"),
    ROOM_KICKOUT_USER(2009,"用户已被踢出"),
    // 牌局(列表&组局&进房)模块结束（2000-2199）

    //支付模块  开始[2100-2199]
    PAY_PHONE_NOT_EXISTING(2100,"指定的充值号码不存在"),
    PAY_PRODUCTION_NOT_EXISTING(2101,"指定的充值号码不存在"),
    PAY_METHOD_NOT_SUPPORTED(2103,"支付方式不支持 "),
    PAY_ORDER_CREATER_FAIlED(2104,"创建订单失败"),
    PAY_ORDERID_EXISTING(2105,"订单存在"),
    PAY_INITIATE_PAYMENT_TOO_FREQUENTLY(2106,"发起支付过于频繁"),
    PAY_WEB_URL_NOT_EXISTING(2107,"支付页面不存在"),
    //支付模块结束  [2100-2199]


    //*******分享赚金豆模块开始(2100-2199)******
    PROMOTION_FUNCTION_NOT_OPEN(2100, "分享赚金豆功能没有开启"),
    PROMOTION_SPREADER_NOT_IN_CLUIB(2101, "请先加入俱乐部，才可以成为推广员"),
    PROMOTION_HAS_BEEN_SPREADER(2102, "你已经是推广员"),
    PROMOTION_INVENT_CODE_ERROR(2103, "邀请码错误，请重新输入"),
    PROMOTION_ACCOUNT_DEPOSIT_NOT_ENOUGH(2104, "账户金豆数量不足！"),
    PROMOTION_LEVEL2_PASSWORD_ERROR(2105, "您输入的二级密码有误"),
    PROMOTION_REGISTER_WITH_INVENT_CODE_ERROR(2106, "邀请码注册失败！"),
    PROMOTION_HAS_BEEN_REGISTERD_WITH_INVENT_CODE(2107, "已通过邀请码注册过"),
    PROMOTION_CAN_NOT_QUERY_IN_SETTLEMENT_TIME(2108, "请在06:00:00后进行查询"),
    PROMOTION_CAN_NOT_QUERY(2109,"需要成为推广员才能查看"),
    PROMOTION_BIND_PROMOTION_RELATION_ERROR(2110,"绑定推广员上下级关系错误"),


    //*******分享赚金豆模块结束*****************


    //*******钱包模块开始(2200-2299)******


    WALLET_DISABLE(2200, "钱包已被停用"),


    //******钱包模块结束*****************

    ;

    private int code;

    private String desc;

    public static RespCode getByTypeCode(int typeCode) {
        RespCode[] respCodes = RespCode.values();
        for (int i = 0; i < respCodes.length; i++) {
            if (respCodes[i].getCode() == typeCode) {
                return respCodes[i];
            }
        }
        return FAILED;
    }
}
