package com.dzpk.crazypoker.common.web.config;

import com.dzpk.crazypoker.common.web.token.algorithm.ITokenChecker;
import com.dzpk.crazypoker.common.web.token.filter.AccessTokenFilter;
import com.dzpk.crazypoker.common.web.token.service.IFrequencyService;
import com.dzpk.crazypoker.common.web.token.service.impl.FrequencyServiceImpl;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(prefix = "token",value = "enable")
@ConditionalOnClass({
        WebAutoConfiguration.class
})
@EnableConfigurationProperties({TokenProperties.class})
public class TokenAutoConfiguration {
    @Bean("tokenFilter")
    public FilterRegistrationBean tokenFilter(TokenProperties config, ITokenChecker tokenChecker, IFrequencyService apiFrequencyService){
        AccessTokenFilter filter = new AccessTokenFilter(config.getTokenHeaderName(),
                config.getDeviceHeaderName(),config.getCharsetName(),
                config.getWhitelistUrls(),tokenChecker,apiFrequencyService);
        FilterRegistrationBean filterRegistrationBean=new FilterRegistrationBean(filter);
        filterRegistrationBean.setOrder(-2147483647);

        return filterRegistrationBean;
    }

    @Bean
    public IFrequencyService apiFrequencyService(TokenProperties config){
        FrequencyServiceImpl service = new FrequencyServiceImpl();
        service.setConfig(config.getFrequency());
        return service;
    }
}
