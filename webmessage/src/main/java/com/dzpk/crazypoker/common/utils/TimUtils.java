package com.dzpk.crazypoker.common.utils;

import com.dzpk.crazypoker.message.im.config.TimProperties;
import com.tls.tls_sigature.tls_sigature;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.Base64;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.zip.Deflater;

@Slf4j
public class TimUtils {

    private static TimUtils instance = null;

    public static synchronized TimUtils getInstance(){
        if(null == instance){
            synchronized (TimUtils.class){
                if(null == instance){
                    instance = new TimUtils();
                }
            }
        }
        return instance;
    }

    /**
     * 生成私钥签名后的可登陆签名
     * @param user 拼接后用户的账号
     * @return 返回可登陆的签名
     */
    public String genNormalUserSig(String user,TimProperties timProperties){
//        if(timProperties.getAdmin().equals(user)){
//            return "";
//        }
//        tls_sigature.GenTLSSignatureResult result = null;
//        try {
//            result = tls_sigature.genSig(timProperties.getAppId(), user, timProperties.getPriKey());
//            if (0 == result.urlSig.length()) {
//                System.out.println("GenTLSSignatureEx failed: " + result.errMessage);
//                return "";
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        return genUserSig(timProperties,user);
    }

    /**
     * 生成私钥签名后的管理员可登陆签名
     * @return 返回可登陆的签名
     */
    public String genAdminSig(TimProperties timProperties){
//        tls_sigature.GenTLSSignatureResult result = null;
//        try {
//            result = tls_sigature.genSig(timProperties.getAppId(), timProperties.getAdmin(), timProperties.getPriKey());
//            if (0 == result.urlSig.length()) {
//                System.out.println("GenTLSSignatureEx failed: " + result.errMessage);
//                return "";
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return result.urlSig;
        return genUserSig(timProperties,timProperties.getAdmin());
    }

    public boolean verifySig(TimProperties timProperties,String sig){
//        try {
//            System.out.println("sign=>" + sig);
////            System.out.println("pri=>" + timProperties.getPriKey());
////            System.out.println("pub=>" + timProperties.getPubKey());
//            System.out.println("appid=>" + timProperties.getAppId() + ",admin=>" + timProperties.getAdmin());
////            tls_sigature.CheckTLSSignatureResult result = tls_sigature.CheckTLSSignatureEx(sig, timProperties.getAppId(), timProperties.getAdmin(), timProperties.getPubKey());
//            if(result.verifyResult){
//                return true;
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        return false;
    }
    //TODO 测试IM
    /**
     * 【功能说明】用于签发 TRTC 和 IM 服务中必须要使用的 UserSig 鉴权票据
     * <p>
     * 【参数说明】
     *
     * @param userid - 用户id，限制长度为32字节，只允许包含大小写英文字母（a-zA-Z）、数字（0-9）及下划线和连词符。
     * UserSig 票据的过期时间，单位是秒，比如 86400 代表生成的 UserSig 票据在一天后就无法再使用了。
     * @return usersig -生成的签名
     */
    public String genUserSig(TimProperties timProperties,String userid) {
        return genUserSig(timProperties,userid, 86400000, null);
    }
    /**
     *
     * @param userid：用户id
     * @param expire：UserSig的有效期，单位为秒。
     * @param userbuf：即时通信 IM 中均默认使用不带 UserBuf 的接口，即该参数默认填写为null。
     * 实时音视频的部分使用场景中可能需要使用带 UserBuf 的接口，例如进房时
     * @return
     */
    private String genUserSig(TimProperties timProperties,String userid, long expire, byte[] userbuf) {
        long currTime = System.currentTimeMillis() / 1000;
        JSONObject sigDoc = new JSONObject();
        sigDoc.put("TLS.ver", "2.0");
        sigDoc.put("TLS.identifier", userid);
        sigDoc.put("TLS.sdkappid", timProperties.getAppId());
        sigDoc.put("TLS.expire", expire);
        sigDoc.put("TLS.time", currTime);

        String base64UserBuf = null;
        if (null != userbuf) {
            base64UserBuf = Base64.getEncoder().encodeToString(userbuf).replaceAll("\\s*", "");
            sigDoc.put("TLS.userbuf", base64UserBuf);
        }
        String sig = hmacsha256(timProperties,userid, currTime, expire, base64UserBuf);
        if (sig.length() == 0) {
            return "";
        }
        sigDoc.put("TLS.sig", sig);
        Deflater compressor = new Deflater();
        compressor.setInput(sigDoc.toString().getBytes(StandardCharsets.UTF_8));
        compressor.finish();
        byte[] compressedBytes = new byte[2048];
        int compressedBytesLength = compressor.deflate(compressedBytes);
        compressor.end();
        return (new String(Base64URL.base64EncodeUrl(Arrays.copyOfRange(compressedBytes,
                0, compressedBytesLength)))).replaceAll("\\s*", "");
    }
    private String hmacsha256(TimProperties timProperties,String identifier, long currTime, long expire, String base64Userbuf) {
        String contentToBeSigned = "TLS.identifier:" + identifier + "\n"
                + "TLS.sdkappid:" + timProperties.getAppId() + "\n"
                + "TLS.time:" + currTime + "\n"
                + "TLS.expire:" + expire + "\n";
        if (null != base64Userbuf) {
            contentToBeSigned += "TLS.userbuf:" + base64Userbuf + "\n";
        }
        try {
            log.info("{} ==============> 用户发送消息",identifier);
            byte[] byteKey = timProperties.getPrefixChat().getBytes(StandardCharsets.UTF_8);
            Mac hmac = Mac.getInstance("HmacSHA256");
            SecretKeySpec keySpec = new SecretKeySpec(byteKey, "HmacSHA256");
            hmac.init(keySpec);
            byte[] byteSig = hmac.doFinal(contentToBeSigned.getBytes(StandardCharsets.UTF_8));
            return (Base64.getEncoder().encodeToString(byteSig)).replaceAll("\\s*", "");
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            e.printStackTrace();
            return "";
        }
    }
    public static void main(String[] args) {
//        try {
//            // generate signature
//            tls_sigature.GenTLSSignatureResult result = tls_sigature.genSig(1400073168, "172204", timProperties.getPriKey());
//            if (0 == result.urlSig.length()) {
//                System.out.println("GenTLSSignatureEx failed: " + result.errMessage);
//                return;
//            }
//
//            System.out.println("---\ngenerate sig:\n" + result.urlSig + "\n---\n");
//
//            // check signature
//            tls_sigature.CheckTLSSignatureResult checkResult = tls_sigature.CheckTLSSignatureEx(result.urlSig, 1400073168, "172204", timProperties.getPubKey());
//            if (checkResult.verifyResult == false) {
//                System.out.println("CheckTLSSignature failed: " + result.errMessage);
//                return;
//            }
//
//            System.out.println("\n---\ncheck sig ok -- expire time " + checkResult.expireTime + " -- init time " + checkResult.initTime + "\n---\n");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }
}