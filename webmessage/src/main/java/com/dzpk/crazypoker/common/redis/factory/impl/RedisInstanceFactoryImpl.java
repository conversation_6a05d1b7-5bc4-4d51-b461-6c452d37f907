package com.dzpk.crazypoker.common.redis.factory.impl;


import com.dzpk.crazypoker.common.redis.config.RedisInstance;
import com.dzpk.crazypoker.common.redis.factory.IRedisInstanceFactory;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.Map;


/**
 * 描述：
 *
 * <AUTHOR>
 * @date 2017/5/5
 */
public class RedisInstanceFactoryImpl implements IRedisInstanceFactory {
    private Map<String,RedisInstance> instanceMap = null;
    private RedisInstance defaultInstance = null;

    public RedisInstanceFactoryImpl(Map<String,RedisInstance> instanceMap, RedisInstance defaultInstance){
        this.instanceMap = instanceMap;
        this.defaultInstance = defaultInstance;
    }

    public Map<String, RedisInstance> getInstanceMap() {
        return instanceMap;
    }

    public RedisConnectionFactory connectionFactory(String name){
        RedisInstance instance = redisInstance(name);
        if(null == instance)
            return null;

        return instance.getConnectionFactory();
    }
    public RedisTemplate<byte[],byte[]> template(String name){
        RedisInstance instance = redisInstance(name);
        if(null == instance)
            return null;

        return instance.getTemplate();
    }
    public StringRedisTemplate stringTemplate(String name){
        RedisInstance instance = redisInstance(name);
        if(null == instance)
            return null;

        return instance.getStringTemplate();
    }
    public ReactiveRedisTemplate reactiveTemplate(String name){
        RedisInstance instance = redisInstance(name);
        if(null == instance)
            return null;

        return instance.getReactiveRedisTemplate();
    }

    public RedisInstance redisInstance(String name){
        RedisInstance instance = this.instanceMap.get(name);
        return instance;
    }
    public RedisInstance defaultRedisInstance() {
        return this.defaultInstance;
    }
}
