package com.dzpk.crazypoker.common.web.token.service;

public interface ICacheService {
    /**
     * 获取token对象
     * @param token
     * @return
     */
    ICachedToken getToken(String token);

    /**
     * 创建token对象
     * @param token
     * @param userId
     * @param deviceId
     * @return
     */
    ICachedToken addToken(String token, int userId, String deviceId);

    /**
     * 将token设置为无效
     * @param token
     */
    void chgInvalid(ICachedToken token);
}
