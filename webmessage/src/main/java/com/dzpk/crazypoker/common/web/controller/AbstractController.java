package com.dzpk.crazypoker.common.web.controller;

import com.dzpk.crazypoker.common.constant.RespCode;
import com.dzpk.crazypoker.common.exception.ParamInvalidException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public abstract class AbstractController {
    /**
     * 处理校验结果
     * @param result
     */
    protected void handleValidatedError(BindingResult result){
        if(null == result)
            return;
        if(!result.hasFieldErrors())
            return;

        Map<RespCode,List<ParamInvalidException>> errorMap = new HashMap<>();
        for(FieldError fe : result.getFieldErrors()){
            String fieldName = fe.getField();
            RespCode errorType = getErrorType(fe.getCode());
            String message = fe.getDefaultMessage();
            if(null == message || "".equals(message.trim())){
                message = errorType.getDesc();
            }

            List<ParamInvalidException> list = errorMap.get(errorType);
            if(null == list) {
                list = new ArrayList<>();
                errorMap.put(errorType,list);
            }

            ParamInvalidException ex = new ParamInvalidException(errorType.getCode(),message,fieldName);
            list.add(ex);
        }

        throwException(errorMap);
    }

    /**
     * 如果存在校验错误，则按以下优先级抛出
     * // 1. EParamError.required
     * // 2. EParamError.toolong
     * // 3. EParamError.minValue
     * // 4. EParamError.unknown
     * @param errorMap
     */
    private void throwException(Map<RespCode,List<ParamInvalidException>> errorMap){
        if(null == errorMap || errorMap.isEmpty())
            return;

        RespCode[] errorTypes = RespCode.class.getEnumConstants();
        for(RespCode t : errorTypes) {
            List<ParamInvalidException> errorLst = errorMap.get(t);
            if(null != errorLst && !errorLst.isEmpty())
            {
                throw errorLst.get(0);
            }
        }
    }

    /**
     * 根据校验的类型code字符串
     * 转换系统自定义的错误类型
     * @param code
     * @return
     */
    private RespCode getErrorType(String code){
        RespCode result = RespCode.PARAM_UNKNOWN;
        if(null == code || "".equals(code.trim()))
            return result;

        code = code.trim();
        if("NotEmpty".equals(code) || "ListNotEmpty".equals(code))
            result = RespCode.PARAM_REQUIRED;
        else if("Length".equals(code) || "ListMaxSize".equals(code))
            result = RespCode.PARAM_TOLONG;
        else if("Min".equals(code))
            result = RespCode.PARAM_MINVALUE;
        else if("ListIntRange".equals(code) ||
                "Range".equals(code) ||
                "AnyIntRange".equals(code))
            result = RespCode.PARAM_INVALID;

        return result;
    }
}
