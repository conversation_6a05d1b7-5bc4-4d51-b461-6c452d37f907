package com.dzpk.crazypoker.common.web.config;

import com.google.common.base.Predicates;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.OncePerRequestFilter;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

@Configuration
@ConditionalOnProperty(prefix = "swagger",value = "enable")
@ConditionalOnClass({
        WebAutoConfiguration.class
})
@EnableConfigurationProperties({SwaggerProperties.class})
@EnableSwagger2
public class SwaggerAutoConfiguration {
    @Bean
    public Docket swaggerSpringMvcPlugin(SwaggerProperties swaggerConfigProps) {
        //添加head参数start
        ParameterBuilder tokenPar = new ParameterBuilder();
        List<Parameter> pars = new ArrayList<Parameter>();
        tokenPar.name("uid").description("当前请求用户ID")
                .modelRef(new ModelRef("int"))
                .parameterType("header").required(false)
                .build();
        pars.add(tokenPar.build());
        //添加head参数end

        Docket dt = new Docket(DocumentationType.SWAGGER_2)
                .enable(swaggerConfigProps.isEnable())
                .useDefaultResponseMessages(false)
                .apiInfo(apiInfo(swaggerConfigProps))
                .select()
                .paths(Predicates.not(PathSelectors.regex("/error.*")))
                .build().globalOperationParameters(pars);

        return dt;
    }
    private ApiInfo apiInfo(SwaggerProperties properties) {
        return new ApiInfoBuilder()
                .title(properties.getTitle())
                .description(properties.getDescription())
                .contact(new Contact(properties.getContactName(),
                        properties.getContactUrl(),
                        properties.getContactEmail()))
                .license("Apache License Version 2.0")
                .licenseUrl("https://github.com/springfox/springfox/blob/master/LICENSE")
                .version("2.0")
                .build();
    }

    @Bean
    public FilterRegistrationBean tokenReplaceFilter(){
        UserIdConverter converter = new UserIdConverter();
        FilterRegistrationBean filterRegistrationBean=new FilterRegistrationBean(converter);
        filterRegistrationBean.setOrder(-**********);

        return filterRegistrationBean;
    }

    @Slf4j
    private static class UserIdConverter extends OncePerRequestFilter {
        @Override
        protected void doFilterInternal(HttpServletRequest request,
                                        HttpServletResponse response,
                                        FilterChain filterChain) throws ServletException, IOException {
            if(!this.hasToken(request)) {
                String userId = request.getHeader("uid");
                request.setAttribute("user_id", userId);
            }
            filterChain.doFilter(request, response);
        }

        private boolean hasToken(HttpServletRequest request){
            boolean existing = false;

            Enumeration<String> headers = request.getHeaderNames();
            while(headers.hasMoreElements()){
                String h = headers.nextElement();
                if("md5at".equals(h))
                {
                    existing = true;
                    break;
                }
            }

            return existing;
        }
    }
}
