package com.dzpk.crazypoker.common.rabbitmq.client;

import com.dzpk.crazypoker.common.rabbitmq.client.bean.*;
import com.dzpk.crazypoker.common.rabbitmq.config.RabbitMqConfig;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.UUID;

/**
 * 俱乐部消息发送类
 * Created by jayce on 2019/3/14
 *
 * <AUTHOR>
 */
@Component
public class MessageSender {

    private RabbitTemplate rabbitTemplate;

    @Autowired
    public MessageSender(RabbitTemplate rabbitTemplate) {
        this.rabbitTemplate = rabbitTemplate;
        this.rabbitTemplate.setConfirmCallback(new RabbitTemplate.ConfirmCallback() {
            @Override
            public void confirm(CorrelationData correlationData, boolean ack, String cause){
                if(!ack){
                    //TODO ack 为false时  需要告知发送端 是否重试 没进去交换机的情况
                }
            }
        });
        this.rabbitTemplate.setReturnCallback(new RabbitTemplate.ReturnCallback() {
            @Override
            public void returnedMessage(Message message, int replyCode, String replyText, String exchange, String routingKey) {
                //TODO  消息被退回 是否重试 没进入队列的情况
            }
        });
    }

    /**
     * 发送俱乐部mq消息方法
     * @param message 消息不需要传入时，不要生成msgid，由此方法统一生成
     * @return 该返回值如果业务有依赖对应关系，最好拿到后打印
     */
    public String sendClubMessage(@NotNull ClubMessage message){
        String uuid = UUID.randomUUID().toString();
        this.rabbitTemplate.convertAndSend(RabbitMqConfig.EXCHANGE_CLUB,RabbitMqConfig.ROUTINGKEY_CLUB,message,new CorrelationData(uuid));
        return uuid;
    }

    /**
     * 发送联盟mq消息方法
     * @param message 消息不需要传入时，不要生成msgid，由此方法统一生成
     * @return 该返回值如果业务有依赖对应关系，最好拿到后打印
     */
    public String sendTribeMessage(@NotNull TribeMessage message){
        String uuid = UUID.randomUUID().toString();
        this.rabbitTemplate.convertAndSend(RabbitMqConfig.EXCHANGE_TRIBE,RabbitMqConfig.ROUTINGKEY_TRIBE,message,new CorrelationData(uuid));
        return uuid;
    }

    /**
     * 发送系统mq消息方法
     * @param message 消息不需要传入时，不要生成msgid，由此方法统一生成
     * @return 该返回值如果业务有依赖对应关系，最好拿到后打印
     */
    public String sendSystemMessage(@NotNull SystemMessage message){
        String uuid = UUID.randomUUID().toString();
        this.rabbitTemplate.convertAndSend(RabbitMqConfig.EXCHANGE_SYSTEM,RabbitMqConfig.ROUTINGKEY_SYSTEM,message,new CorrelationData(uuid));
        return uuid;
    }

    /**
     * 发送背包mq消息方法
     * @param message 消息不需要传入时，不要生成msgid，由此方法统一生成
     * @return 该返回值如果业务有依赖对应关系，最好拿到后打印
     */
    public String sendBagMessage(@NotNull BagMessage message){
        String uuid = UUID.randomUUID().toString();
        this.rabbitTemplate.convertAndSend(RabbitMqConfig.EXCHANGE_BAG,RabbitMqConfig.ROUTINGKEY_BAG,message,new CorrelationData(uuid));
        return uuid;
    }

    /**
     * 发送分享赚金豆mq消息方法
     * @param message 消息不需要传入时，不要生成msgid，由此方法统一生成
     * @return 该返回值如果业务有依赖对应关系，最好拿到后打印
     */
    public String sendShareKdouMessage(@NotNull ShareKdouMessage message){
        String uuid = UUID.randomUUID().toString();
        this.rabbitTemplate.convertAndSend(RabbitMqConfig.EXCHANGE_SHARE,RabbitMqConfig.ROUTINGKEY_SHARE,message,new CorrelationData(uuid));
        return uuid;
    }

    /**
     * 发送钱包mq消息方法
     * @param message 消息不需要传入时，不要生成msgid，由此方法统一生成
     * @return 该返回值如果业务有依赖对应关系，最好拿到后打印
     */
    public String sendMoneyMessage(@NotNull MoneyMessage message){
        String uuid = UUID.randomUUID().toString();
        this.rabbitTemplate.convertAndSend(RabbitMqConfig.EXCHANGE_MONEY,RabbitMqConfig.ROUTINGKEY_MONEY,message,new CorrelationData(uuid));
        return uuid;
    }

    /**
     * 发送内部mq消息方法
     * @param message 消息不需要传入时，不要生成msgid，由此方法统一生成
     * @return 该返回值如果业务有依赖对应关系，最好拿到后打印
     */
    public String sendInteriorMessage(@NotNull InteriorMessage message){
        String uuid = UUID.randomUUID().toString();
        this.rabbitTemplate.convertAndSend(RabbitMqConfig.EXCHANGE_INTERIOR,RabbitMqConfig.ROUTINGKEY_INTERIOR,message,new CorrelationData(uuid));
        return uuid;
    }


}
