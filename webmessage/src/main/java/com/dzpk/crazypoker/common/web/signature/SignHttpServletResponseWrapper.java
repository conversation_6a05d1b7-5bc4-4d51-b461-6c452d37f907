package com.dzpk.crazypoker.common.web.signature;

import javax.servlet.ServletOutputStream;
import javax.servlet.WriteListener;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;

public class SignHttpServletResponseWrapper extends HttpServletResponseWrapper {
    private CustServletOutputStream outputStream;
    private PrintWriter printWriter;

    public SignHttpServletResponseWrapper(HttpServletResponse response,int size){
        super(response);
        this.outputStream = new CustServletOutputStream(size);
        this.printWriter = new PrintWriter(this.outputStream);
    }

    /**
     * 返回body数据
     * @return
     */
    public byte[] getBody(){
        return this.outputStream.getData();
    }

    @Override
    public ServletOutputStream getOutputStream() throws IOException{
        return this.outputStream;
    }

    @Override
    public PrintWriter getWriter() throws IOException{
        return this.printWriter;
    }

    private static class CustServletOutputStream extends ServletOutputStream{
        private ByteArrayOutputStream byteArrayInputStream;

        public CustServletOutputStream() {
            this(8192);
        }
        public CustServletOutputStream(int size){
            this.byteArrayInputStream = new ByteArrayOutputStream(size);
        }

        @Override
        public boolean isReady(){
            return true;
            //return this.originalOutputStream.isReady();
        }

        @Override
        public void setWriteListener(WriteListener var1){
            //this.originalOutputStream.setWriteListener(var1);
        }

        /**
         * Writes the specified byte to this output stream. The general
         * contract for <code>write</code> is that one byte is written
         * to the output stream. The byte to be written is the eight
         * low-order bits of the argument <code>b</code>. The 24
         * high-order bits of <code>b</code> are ignored.
         * <p>
         * Subclasses of <code>OutputStream</code> must provide an
         * implementation for this method.
         *
         * @param      b   the <code>byte</code>.
         * @exception IOException  if an I/O error occurs. In particular,
         *             an <code>IOException</code> may be thrown if the
         *             output stream has been closed.
         */
        @Override
        public void write(int b) throws IOException{
            this.byteArrayInputStream.write(b);
        }

        public byte[] getData(){
            return this.byteArrayInputStream.toByteArray();
        }
    }
}
