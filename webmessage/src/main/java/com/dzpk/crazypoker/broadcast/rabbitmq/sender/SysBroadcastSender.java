package com.dzpk.crazypoker.broadcast.rabbitmq.sender;


import com.alibaba.fastjson.JSONObject;
import com.dzpk.crazypoker.broadcast.bean.SysBroadcastMessage;
import com.dzpk.crazypoker.broadcast.rabbitmq.SysBroadcastRabbitMqKeys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * SysBroadcastSender
 *
 * <AUTHOR>
 * @date 13/2/2025
 */
@Slf4j
@Component
public class SysBroadcastSender {

    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送系统广播
     */
    public void sendSysBroadcast(SysBroadcastMessage message) {
        // 发送 MQ 到 res service 通过 res 的 TCP 协议发送广播
        log.info("send broadcast message: {}", JSONObject.toJSONString(message));
        rabbitTemplate.convertAndSend(
                SysBroadcastRabbitMqKeys.Exchange.BROADCAST_SEND,
                SysBroadcastRabbitMqKeys.RoutingKey.BROADCAST_SEND,
                JSONObject.toJSONString(message)
        );
    }
}
