#!/bin/sh

######
# check if path is absolute path
# 0 : no 
# 1 : yes
isAbsolutePath(){
  arg1st=$1
  if test ".$arg1st" = "."
  then
    return 0
  fi

  absoluteFlag=${arg1st:0:1}
  if test "$absoluteFlag" = "/"
  then
    return 1
  fi

  return 0
}

######
# make path absolutely
makeDir(){
  arg1st=$1
  if test -z $arg1st
  then
    echo $arg1st
    return
  fi

  # no permission
  # not existing
  # not directory
  if test ! -d $arg1st
  then
    if test -e $arg1st
    then
       echo "$arg1st is not direcotory : require as directory"
       exit 1
    fi

    mkdir -p $arg1st
    if test $? -ne 0
    then
       echo "No [r/w] permission on : $arg1st"
       exit 1
    fi
  else
    if test ! -w $arg1st
    then
      echo "No [r/w] permission on : $arg1st"
      exit 1
    fi
  fi
}

######
# read the first arg
# if it empty or 
# it not [ start , stop , restart , usage]
# set shCmd to usage
detectCmd(){
  arg1st=$1
  shCmd=usage
  if test -n "$arg1st"
  then
     for c in start stop restart usage
     do
        if test "$c" = "$arg1st"
        then
            shCmd=$c
            break
        fi
     done
  fi
}

######
# load env.sh and setup the default setting for supported item.
loadAppConfig(){
  envFile=$binDir/env.sh
  if test -r $envFile
  then
    source $envFile
  fi

  if test -z $APP_HOME
  then
    APP_HOME=$APP_BASE
  fi

  if test -z $APP_NAME
  then
    APP_NAME=$APP_BASENAME
  fi

  if test -z "$APP_JVM_OPTS"
  then
    APP_JVM_OPTS=-server
  fi

  if test -z $APP_PROFILES
  then
    APP_PROFILES=common,$APP_NAME
  else
    APP_PROFILES=${APP_PROFILES/APP_NAME/$APP_NAME}
  fi

  if test -z $APP_MAIN_JAR
  then
    APP_MAIN_JAR=framework-common.jar
  fi

  if test -z $APP_DATA_DIR
  then
    APP_DATA_DIR=$APP_HOME/datas
  else
    isAbsolutePath $APP_DATA_DIR
    isFull=$?
    if test $isFull -ne "1"
    then
      APP_DATA_DIR=$APP_HOME/$APP_DATA_DIR
    fi
  fi

  if test -z $APP_LOG_DIR
  then
    APP_LOG_DIR=$APP_HOME/logs
  else
    isAbsolutePath $APP_LOG_DIR
    isFull=$?
    if test $isFull -ne "1"
    then
      APP_LOG_DIR=$APP_HOME/$APP_LOG_DIR
    fi
  fi

  if test -z $APP_TMP_DIR
  then
    APP_TMP_DIR=$APP_HOME/tmp
  else
    isAbsolutePath $APP_TMP_DIR
    isFull=$?
    if test $isFull -ne "1"
    then
      APP_TMP_DIR=$APP_HOME/$APP_TMP_DIR
    fi
  fi

  makeDir $APP_DATA_DIR 
  makeDir $APP_LOG_DIR 
  makeDir $APP_TMP_DIR 
}

######
# make sure the pid file
makePidFile(){
   pidFile=$1
   if test -z $pidFile
   then
     APP_PID_FILE=$APP_HOME/$APP_NAME.pid
   else
     isAbsolutePath $pidFile
     isFull=$?
     if test $isFull -ne "1"
     then
       echo "PID file required as absolute path : $pidFile"
       exit 1
     fi

     pidFileDir="`dirname $pidFile`"
     makeDir $pidFileDir

     APP_PID_FILE=$pidFile
   fi
}

######
# check process if existing
# if existing , return 1
# otherwise ,return 0
processExist(){
  procId=0
  if [ ! -r $APP_PID_FILE ]; then
     return
  fi

  procId=`cat $APP_PID_FILE`
  echo "PID-1 : $procId"
  if [ ! -z $procId ]; then
    pidCount=`ps -ef | grep $procId |  grep -v grep | wc -l`
    echo "pidCount : $pidCount"
    if [ $pidCount -eq 1 ]; then
      echo "PID-1-1 : $procId"
      return
    fi
  fi

  procId=$(ps -ef | grep $APP_MAIN_JAR | grep -v grep | awk '{print $2}')
  echo "PID-2 : $procId"
  if [ ! -z $procId ]; then
    return
  fi

  procId=0
}

######
# print usage
usage(){
  echo "usage   : $self [cmd] [pidfile]"
  echo "    cmd : start / stop / restart / usage"
  echo "          optional"
  echo "          default is usage"
  echo "pidfile : store the pid of proc"
  echo "          optional"
  echo "          if specified, it must be a full path and have r/w permission"
  echo "          otherwise it is set to : $APP_PID_FILE"
  echo "detail environment for current:"
  echo "   APP_NAME : $APP_NAME"
  echo "   APP_HOME : $APP_HOME"
  echo "   APP_DATA_DIR : $APP_DATA_DIR"
  echo "   APP_LOG_DIR : $APP_LOG_DIR"
  echo "   APP_TMP_DIR : $APP_TMP_DIR"
  echo "   APP_JVM_OPTS : $APP_JVM_OPTS"
  echo "   APP_PROFILES : $APP_PROFILES"
  echo "   APP_MAIN_JAR : $APP_MAIN_JAR"
  exit 0
}

######
# stop proc
stop(){
  exitFlag=$1
  processExist
  exitStatus=$procId
  echo "PID-3 : $exitStatus"
  if [ $exitStatus -eq 0 ]; then
     echo "Process [ $exitStatus : $APP_MAIN_JAR ] is not running!"
     if [ -z $exitFlag ]; then
        exit 0
     fi

     return
  fi

  tryNum=1
  while [ $tryNum != 4 ];
  do
      kill -s 15 $exitStatus
      sleep 5
      processExist
      exitStatus2=$procId
      echo "PID-3-1 : $exitStatus2"
      if [ $exitStatus2 -eq 0 ]; then
         echo "Stopped process [ $exitStatus : $APP_MAIN_JAR ] successfully at the $tryNum times !"
         rm -rf $APP_PID_FILE > /dev/null 2>&1
         if [ -z $exitFlag ]; then
           exit 0
         fi

          return
      fi

      # tryNum=$( ($tryNum + 1) )
      tryNum=`expr $tryNum + 1`
      echo "Stopped process [ $exitStatus : $APP_MAIN_JAR ] unsuccessfully at the $tryNum times , will try again!"
      # exit 1
  done

  echo "Stopped process [ $exitStatus : $APP_MAIN_JAR ] unsuccessfully!"
  exit 1
}

######
# start proc
start(){
  processExist
  exitStatus=$procId
  if [ $exitStatus -ne 0 ]; then
     echo "Process [ $exitStatus : $APP_MAIN_JAR ] is running!"
     exit 0
  fi

  cd $APP_HOME
  nohup $JAVA_BIN -Djava.io.tmpdir=$APP_TMP_DIR $APP_JVM_OPTS -jar lib/$APP_MAIN_JAR --spring.profiles.active=$APP_PROFILES > $APP_LOG_DIR/nohup.log 2>&1 &
  echo $! > $APP_PID_FILE
  processExist
  exitStatus=$procId
  if [ $exitStatus -ne 0 ]; then
    echo "Started process [ $exitStatus : $APP_MAIN_JAR ] successfully!"
    exit 0
  fi

  echo "Started process [ $exitStatus : $APP_MAIN_JAR ] unsuccessfully!"
  exit 1
}

######
# restart proc
restart(){
  stop 0
  start
}