{"timestamp": {"$resolver": "timestamp", "epoch": {"unit": "millis", "rounded": true}}, "level": {"$resolver": "level", "field": "name"}, "serviceId": "res", "threadName": {"$resolver": "thread", "field": "name"}, "threadId": {"$resolver": "thread", "field": "id"}, "className": {"$resolver": "pattern", "pattern": "%C"}, "methodName": {"$resolver": "pattern", "pattern": "%M"}, "uid": {"$resolver": "mdc", "key": "uid"}, "userid": {"$resolver": "mdc", "key": "userid"}, "userType": {"$resolver": "mdc", "key": "userType"}, "ip": {"$resolver": "mdc", "key": "ip"}, "message": {"$resolver": "message"}, "params": {"$resolver": "messageParameter"}, "exception": {"$resolver": "exception", "field": "message"}, "exceptionStack": {"$resolver": "exception", "field": "stackTrace", "stackTrace": {"stringified": true}}}