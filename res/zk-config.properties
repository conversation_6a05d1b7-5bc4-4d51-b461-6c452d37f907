##
# Zookeeper服务器的地址列表
# 格式: ip(hostname):port[,ip(hostname):port]*
# 必须提供
##
zk.connectString=${zk.connectString}

##
# 可选
# 必须大于0，否则忽略此设置
# 默认值：60 秒
##
zk.sessionTimeoutMs=${zk.sessionTimeoutMs}

##
# 可选
# 必须大于0，否则忽略此设置
# 默认值：15 秒
##
zk.connectionTimeoutMs=${zk.connectionTimeoutMs}

##
# 可选
# 必须大于0，否则忽略此设置
# 默认值：1 秒
##
zk.maxCloseWaitMs=${zk.maxCloseWaitMs}

##
# 创建CuratorTempFramework时使用
# 可选
# 必须大于0，否则忽略此设置
# 默认值：3 分
##
zk.inactiveThresholdMs=${zk.inactiveThresholdMs}

##
# 设置当前这个Zookeeper访问的命名空间
# 如果设置了，通过此实例访问的路径都将自动附加
# 上此设置作为路径的前缀。
# null或mepty，忽略此参数
##
zk.namespace=${zk.namespace}

##
# 1 = true
# otherwise false
##
zk.canBeReadOnly=${zk.canBeReadOnly}
zk.useContainerParentsIfAvailable=${zk.useContainerParentsIfAvailable}