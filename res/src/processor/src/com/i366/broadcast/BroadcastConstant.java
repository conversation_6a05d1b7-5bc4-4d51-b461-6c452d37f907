package com.i366.broadcast;


/**
 * BroadcastConstant
 *
 * <AUTHOR>
 * @date 2025/2/12
 */
public interface BroadcastConstant {

    interface BroadcastMode {
        // 按次数广播
        int COUNT = 0;
        // 按时间广播
        int TIME = 1;
    }

    interface BroadcastRange {
        // 全服广播
        int ALL = 0;
        // 牌局广播
        int ROOM = 1;
    }

    interface BroadcastStatus {
        // 未开始
        int UNPUBLISHED = 0;
        // 进行中
        int PUBLISHED = 1;
        // 已结束
        int FINISHED = 2;
        // 失败
        int FAILED = 3;
    }

}
