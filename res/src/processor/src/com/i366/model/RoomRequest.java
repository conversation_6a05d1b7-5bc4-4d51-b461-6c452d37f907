package com.i366.model;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

import com.i366.data.Data;
import com.i366.service.MttMongodbService;
import com.i366.service.RedisService;
import com.i366.service.SngRedisService;
import com.i366.util.AuditingMsgUtil;
import com.i366.util.ChipUtils;
import com.work.client.config.Config;
import com.work.client.socket.Registration;
import com.work.client.util.I366ClientPickUtil;
import com.work.comm.MttRegister;
import com.work.comm.SngRegister;
import com.work.comm.protocal.Request;
import com.work.comm.util.I366PickUtil;
import com.work.comm.util.PublisherUtil;
import com.work.db.imp.DZPKDao;

import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.Logger;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.json.JSONObject;

import com.i366.data.Constant;
import com.i366.service.MongodbService;
import com.mongodb.BasicDBObject;
import com.mongodb.MongoClient;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoDatabase;

/**
 * Created by baidu on 16/8/15.
 */
public class RoomRequest {
    public static Logger logger = com.work.comm.util.LogUtil.getLogger(RoomRequest.class);
    public static Map<Integer, ConcurrentHashMap<String, Long>> requestMap =
            new HashMap<Integer, ConcurrentHashMap<String, Long>>();

    public static Map<Integer, Set<Integer>> processingMap = new HashMap<Integer, Set<Integer>>();

    private static final String db = "dzpk";
    private static final String _requestCollection = "game_request";
    private static final String _requestCollectionRecord = "game_request_record";   // 审批消息记录表

    public static final String _mttRequestCollection = "mtt_game_request";
    private static final String _requestInfoMttRecord = "mtt_request_info"; // mtt 带入成功的信息表

    public static final String _sngRequestCollection = "sng_game_request"; // sng 申请带入信息表
    private static final String _requestInfoSngRecord = "sng_request_info"; // sng 带入成功的信息表

    public static final int REQUEST_AGREE = 0;      // 审批记录同意标识
    public static final int REQUEST_DENY = 1;       // 审批记录拒绝标识

    /**
     * 增加消息通知缓存
     *
     * @param roomId
     * @param msgIdUserId
     * @param time
     */
    public static void addCache(int roomId, String msgIdUserId, Long time) {
        boolean success = false;
        Map<String, Long> map = requestMap.get(roomId);
        if (map == null) {
            ConcurrentHashMap<String, Long> roomRequest = new ConcurrentHashMap<String, Long>();
            roomRequest.put(msgIdUserId, time);
            success = true;
            requestMap.put(roomId, roomRequest);
        } else {
            map.put(msgIdUserId, time);
            success = true;
        }

        if (success == true) {
            logger.debug("add request id and user id:" + msgIdUserId + " to cache");
            logger.debug("add request time:" + time + " to cache");
        }
    }


    /**
     * 新增请求
     *
     * @param userId
     * @param nickName
     * @param chips
     * @param type
     * @param tribeName 
     * @param startTime 
     */
    public static Object[] add(int userId, String ownerId, String nickName, int chips, int type, int roomId,
            int roomPath, String roomName, int seatId, int ignoreBring, int selectClubId, String head, int roomType,
            String clubName, String tribeName, int tribeId, int startTime, String randomNum, String collectName) {
        String msgId = "";
        Long time = null;
        MongoClient mongo = null;
        try {
            if (_mttRequestCollection.equals(collectName)) {
                mongo = MttMongodbService.getMongoInstance();
            } else {
                mongo = MongodbService.getMongoInstance();
            }
            MongoDatabase database = mongo.getDatabase(db);
            MongoCollection<Document> requestCollection = database.getCollection(collectName);

            Document requestRecord = new Document();
            msgId = ObjectId.get().toString();
            time = System.currentTimeMillis();

            requestRecord.put("_id", new ObjectId(msgId));              //  消息ID
            requestRecord.put("room_id", roomId);                       //  所属房间ID
            requestRecord.put("room_path", roomPath);                   //  所属房间path
            requestRecord.put("room_name", roomName);                   //  所属房间名
            requestRecord.put("user_id", userId);                       //  请求用户ID
            requestRecord.put("owner_id", ownerId);                     //  所属房主ID
            requestRecord.put("nick_name", nickName);                   //  请求用户昵称
            requestRecord.put("take_in", chips);                        //  请求带入筹码数
            requestRecord.put("time", time);                            //  请求时间
            requestRecord.put("seat", seatId);                          //  请求座位号
            requestRecord.put("type", type);                            //  请求类型
            requestRecord.put("read", -1);                              //  是否已经读
            requestRecord.put("ignore_bring", ignoreBring);             //  本次带入的筹码是否不计入总和
            requestRecord.put("club_id", selectClubId);                 //  选择的社区id
            requestRecord.put("head", head);                            //  玩家头像
            requestRecord.put("room_type", roomType);                   //  房间类型
            requestRecord.put("club_name", clubName);                   //  社区名称
            requestRecord.put("tribe_id", tribeId);                     //  同盟id
            requestRecord.put("tribe_name", tribeName);                  //  同盟名称
            requestRecord.put("room_fee", chips);                        //  mtt比赛传入报名费或者重购费 （跟带入筹码数一致）
            requestRecord.put("begin_time", startTime);                 //  牌局开始时间
            requestRecord.put("random_num", randomNum);                 //  随机ID

            requestCollection.insertOne(requestRecord);

            // 加入房间待处理请求列表
            if (processingMap.containsKey(roomId)) {
                processingMap.get(roomId).add(userId);
            } else {
                Set<Integer> users = new HashSet<Integer>();
                users.add(userId);
                processingMap.put(roomId, users);
            }
        } catch (Exception e) {
            logger.debug("mongo error fail", e);
            e.printStackTrace();
        } finally {
            if (mongo != null) {
                MongodbService.close(mongo);
            }
        }

        Object[] obj = new Object[]{msgId, time};

        return obj;
    }

    /**
     * 监测重复请求
     *
     * @param userId
     * @param roomId
     * @return
     */
    public static boolean checkRequestRepeat(int userId, int roomId, String requestCollectName) {
        if (processingMap.containsKey(roomId)) {
            Set<Integer> users = processingMap.get(roomId);
            if (users.contains(userId)) {
                // 监控是否过期请求
                if (checkExpire(roomId, userId, requestCollectName)) { // 都是过期请求
                    if (processingMap.containsKey(roomId)) {
                        processingMap.get(roomId).remove(userId);
                    }
                    return true;
                }
                return false;
            }
        }

        return true;
    }


    /**
     * 批量获取消息列表(某条消息后的所有消息)
     *
     * @return
     */
    public static List<JSONObject> batchGet(String msgId, int ownerId) {
    	List<JSONObject> jsonArray = new ArrayList<JSONObject>();
        MongoClient mongo = null;
        MongoCursor<Document> mc = null;
        MongoCursor<Document> mc2 = null;
        MongoCursor<Document> mc1 = null;
        try {
            mongo = MongodbService.getMongoInstance();
            MongoDatabase database = mongo.getDatabase(db);
            MongoCollection<Document> requestCollection = database.getCollection(_requestCollection);

            Long time = null;
            if (!"0".equals(msgId)) {
                BasicDBObject filter = new BasicDBObject("_id", new ObjectId(msgId));
                mc = MongodbService.findOne(requestCollection, filter);

                while (mc.hasNext()) {
                    Document doc = mc.next();
                    time = doc.getLong("time");
                }

                if (time == null) {
                    logger.debug("id not found!");
                    return null;
                }
            }


            /**
             * 获取普通房的带入请求
             */
            BasicDBObject fil2 = new BasicDBObject("owner_id", ownerId);
            if (time != null) {
                fil2.append("time", new BasicDBObject("$gt", time));
            }

            mc2 = MongodbService.find(requestCollection, fil2, 99, "time", -1);
            while (mc2.hasNext()) {
                Document doc1 = mc2.next();
                //if (doc1.getInteger("room_path") != 51) { // 大菠萝控制带入不在消息页显示
                    JSONObject row = new JSONObject();
                    row.put("roomId", doc1.getInteger("room_id"));
                    row.put("name",  doc1.getString("nick_name"));
                    row.put("userId", doc1.getInteger("user_id"));
                    row.put("chips", doc1.getInteger("take_in"));
                    row.put("roomType", doc1.getInteger("room_type"));
                    row.put("clubName", doc1.getString("club_name"));
                    row.put("roomName", doc1.getString("room_name"));
                    row.put("icon", doc1.getString("head"));
                    row.put("roomPath", doc1.getInteger("room_path"));
                    row.put("msgId", doc1.getObjectId("_id").toString());
                    boolean expire = System.currentTimeMillis() - Long.parseLong(doc1.get("time").toString()) > Constant.REQUEST_MIN_TIMEOUT;
                    row.put("expire", expire);
                    row.put("time", Long.parseLong(doc1.get("time").toString()));
                    row.put("clubType", doc1.getInteger("club_type"));
                    jsonArray.add(row);
                //}
            }

            /**
             * 获取MTT比赛的带入请求
             */

            BasicDBObject fil = new BasicDBObject();
            if (time != null) {
                fil.append("time", new BasicDBObject("$gt", time));
            }

            Pattern pattern = Pattern.compile("^.*" + String.valueOf(ownerId) + ".*$", Pattern.CASE_INSENSITIVE);
            fil.append("owner_id", pattern);

            BasicDBObject data = new BasicDBObject();
            data.put("$eq", -1);  // -1未读 0同意 1拒绝 2已满人，自动拒绝 3牌局解散 9已读
            fil.append("read", data);

            MongoCollection<Document> mttRequestCollection = database.getCollection(_mttRequestCollection);
            mc1 = MongodbService.find(mttRequestCollection, fil, 1000, "time", -1);  //扩大查询的范围暂时设置为1000
            
            while (mc1.hasNext()) {
                    Document doc1 = mc1.next();
                    JSONObject row = new JSONObject();
                    row.put("roomId", doc1.getInteger("room_id"));
                    row.put("name",  doc1.getString("nick_name"));
                    row.put("userId", doc1.getInteger("user_id"));
                    row.put("chips", doc1.getInteger("take_in"));
                    row.put("roomType", doc1.getInteger("room_type"));
                    row.put("clubName", doc1.getString("club_name"));
                    row.put("roomName", doc1.getString("room_name"));
                    row.put("icon", doc1.getString("head"));
                    row.put("roomPath", doc1.getInteger("room_path"));
                    row.put("msgId", doc1.getObjectId("_id").toString());
                    row.put("expire", false);
                    row.put("time", Long.parseLong(doc1.get("time").toString()));
                    row.put("clubType", doc1.getInteger("club_type"));

                    int type = doc1.getInteger("type");
                    int mttRequestType = 0 ;
                    if(type == 4){
                        mttRequestType = 0;
                        row.put("roomType",2);
                    }else if (type == 5){
                        mttRequestType = 1;
                        row.put("roomType",2);
                    }
                    
                    logger.debug("消息类型:" + "roomType:" + doc1.getInteger("room_type")+ "请求类型:" + type);

                    row.put("mttRequestType",mttRequestType);
                    jsonArray.add(row);
            }


        } catch (Exception e) {
            logger.error("mongo connect fail:" + e.getMessage(), e);
            //e.printStackTrace();
        } finally {
            if (mongo != null) {
                MongodbService.closeCursor(mc);
                MongodbService.closeCursor(mc2);
                MongodbService.closeCursor(mc1);
                MongodbService.close(mongo);
            }
        }
        return jsonArray;
    }

    public static int removeExpire(int roomId, int userId) {
        MongoClient mongo = null;
        try {
            mongo = MongodbService.getMongoInstance();
            MongoDatabase database = mongo.getDatabase(db);
            MongoCollection<Document> requestCollection = database.getCollection(_requestCollection);
            
            Long expireTime = System.currentTimeMillis() - Constant.REQUEST_MIN_TIMEOUT;
            BasicDBObject fil = new BasicDBObject("room_id", roomId);
            fil.append("time", new BasicDBObject("$lte", expireTime));
            MongoCursor<Document> mc = MongodbService.find(requestCollection, fil);
            while (mc.hasNext()) {
                // Document doc = mc.next();
                // logger.debug("_id: " + doc.getString("_id"));
                // 从待处理请求列表中移除
                if (processingMap.containsKey(roomId)) {
                    processingMap.get(roomId).remove(userId);
                }
            }
            long cnt = MongodbService.removeMany(requestCollection, fil);
            logger.debug("expire request cnt: " + cnt);
            return (int) cnt;
        } catch (Exception e) {
            logger.error("", e);
        } finally {
            if (mongo != null) {
                MongodbService.close(mongo);
            }
        }
        return 0;
    }
    
    private static boolean checkExpire(int roomId, int userId, String requestCollectName) {
        MongoClient mongo = null;
        MongoCursor<Document> mc = null;
        boolean expired = true;
        try {
            if (_mttRequestCollection.equals(requestCollectName)) {
                mongo = MttMongodbService.getMongoInstance();
            } else {
                mongo = MongodbService.getMongoInstance();
            }
            MongoDatabase database = mongo.getDatabase(db);
            MongoCollection<Document> requestCollection = database.getCollection(requestCollectName);
            
            Long expireTime = System.currentTimeMillis() - Constant.REQUEST_MIN_TIMEOUT;
            BasicDBObject fil = new BasicDBObject("room_id", roomId);
            fil.append("user_id", new BasicDBObject("user_id",userId));
            fil.append("time", new BasicDBObject("$gte", expireTime));
            mc = requestCollection.find(fil).iterator();
            if (mc.hasNext()) {
                expired = false;
            }
        } catch (Exception e) {
            logger.error("", e);
        } finally {
            if (mongo != null) {
                MongodbService.closeCursor(mc);
                MongodbService.close(mongo);
            }
        }
        return expired;
    }
    
    /**
     * 清理过期消息
     *
     * @param msgId
     * @param ownerId
     * @return
     */
    public static int removeExpire(String msgId, int ownerId) {
        int status = 1;
        MongoClient mongo = null;
        MongoCursor<Document> mc = null;
        MongoCursor<Document> mc1 = null;
        try {
            mongo = MongodbService.getMongoInstance();
            MongoDatabase database = mongo.getDatabase(db);
            MongoCollection<Document> requestCollection = database.getCollection(_requestCollection);

            Long expireTime = System.currentTimeMillis() - Constant.REQUEST_MIN_TIMEOUT;
            BasicDBObject filter = new BasicDBObject("_id", new ObjectId(msgId));
            mc = MongodbService.findOne(requestCollection, filter);

            Long msgTime = null;
            while (mc.hasNext()) {
                Document doc = mc.next();
                msgTime = doc.getLong("time");
            }

            if (msgTime == null) {
                return status;
            } else {
                Long finalTime = expireTime < msgTime ? expireTime : msgTime;
//                logger.debug("expire time:" + expireTime);
//                logger.debug("msg time:" + msgTime);
//                logger.debug("final time:" + finalTime);
                BasicDBObject fil = new BasicDBObject();
                Pattern pattern = Pattern.compile("^.*" + String.valueOf(ownerId) + ".*$", Pattern.CASE_INSENSITIVE);
                filter.append("owner_id", pattern);
                fil.append("time", new BasicDBObject("$lte", finalTime));
                //fil.append("_id", new ObjectId(msgId));
                mc1 = MongodbService.find(requestCollection, fil);
                while (mc1.hasNext()) {
                    Document doc = mc1.next();
                    int requestUid = doc.getInteger("user_id");
                    int roomId = doc.getInteger("room_id");
                    // 从待处理请求列表中移除
                    if (processingMap.containsKey(roomId)) {
                        processingMap.get(roomId).remove(requestUid);
                    }
                }
                long cnt = MongodbService.removeMany(requestCollection, fil);
                logger.debug("cnt:" + cnt);
                status = 0;
            }
        } catch (Exception e) {
            logger.error("remove expire msg error");
            e.printStackTrace();
        } finally {
            if (mongo != null) {
                MongodbService.closeCursor(mc);
                MongodbService.closeCursor(mc1);
                MongodbService.close(mongo);
            }
        }

        return status;
    }

    /**
     * 获取最近一个请求的剩余时间
     *
     * @param roomId
     * @param userId
     * @return
     */
    public static int getRecentlyLeftTime(int roomId, int userId) {
        int leftTime = 0;

        MongoClient mongo = null;
        try {
            mongo = MongodbService.getMongoInstance();
            MongoDatabase database = mongo.getDatabase(db);
            MongoCollection<Document> requestCollection = database.getCollection(_requestCollection);

            BasicDBObject filter = new BasicDBObject("room_id", roomId);
            filter.append("user_id", userId);

            MongoCursor<Document> mc = MongodbService.find(requestCollection, filter, 1, "time", -1);
            while (mc.hasNext()) {
                Document doc = mc.next();
                int curTime = (int) (System.currentTimeMillis() / 1000L);
                int addTime = (int) (doc.getLong("time") / 1000L);
                int time = curTime - addTime;
                if (time < Constant.REQUEST_MAX_TIMEOUT / 1000) {
                    leftTime = Constant.REQUEST_MAX_TIMEOUT / 1000 - time;
                }
            }
        } catch (Exception e) {
            logger.error("Mongo connect error");
            e.printStackTrace();
        } finally {
            if (mongo != null) {
                MongodbService.close(mongo);
            }
        }

        return leftTime;
    }

    /**
     * SNG 批量处理请求
     *
     * @param ownerId
     * @param operate
     * @param ids
     * @return
     */
    public static void sngBatchOperate(int ownerId, int operate, String ids) {
        String[] ret = new String[]{"", "", "0", ""};
        List<ObjectId> expireIdsArr = new ArrayList<>();
        List<ObjectId> successIdsArr = new ArrayList<>();

        int result = 0;
        boolean expire = false;  //消息是否已经过期
        MongoClient mongo = null;
        MongoCursor<Document> mc = null;
        try {
            mongo = MongodbService.getMongoInstance();
            MongoDatabase database = mongo.getDatabase(db);
            MongoCollection<Document> requestCollection = database.getCollection(_sngRequestCollection);

            String[] idArray = ids.split("@%");
            if (idArray.length > 0) {

                logger.debug("id in arr:" + idArray[0]);

                BasicDBObject filter = new BasicDBObject("_id", new ObjectId(idArray[0]));
                mc = MongodbService.findOne(requestCollection, filter);

                String successIds = "";
                String expireIds = "";
                String creditNotEnouthIds = ""; // 社区积分不足，审批者通过该字段识别提示该消息对应的社区积分不足
                StringBuilder otherMgrUserLst = new StringBuilder();
                int roomId;
                String msgId;
                String mgrUserIdStr;
                int msgType=4;

                if (mc.hasNext()) {
                    Document doc = mc.next();
                    logger.debug("get result doc :" + doc.toJson());
                    msgId = doc.getObjectId("_id").toString();
                    long time = doc.getLong("time");
                    int userId = doc.getInteger("user_id");
                    roomId = doc.getInteger("room_id");
                    int chips = doc.getInteger("take_in"); // 报名费，不包括服务费
                    int tribeId = doc.getInteger("tribe_id");
                    int read = doc.getInteger("read");
                    int clubId = doc.getInteger("club_id");
                    String roomName = doc.getString("room_name");
                    String nickName = doc.getString("nick_name");
                    String clubName = doc.getString("club_name");
                    String tribeName = doc.getString("tribe_name");
                    String head = doc.getString("head");
                    String ownerIds = doc.getString("owner_id");
                    String randomNum = doc.getString("random_num");
                    mgrUserIdStr = ownerIds;

                    DZPKDao dao = new DZPKDao();
                    SngRoom room = dao.findSngRoom(roomId);

                    if (room == null) {
                        expireIds += msgId + "@%";
                        expireIdsArr.add(new ObjectId(msgId));
                        expire = true;
                        result = 1;
                    } else {
                        SngRedisService redisService = SngRedisService.getSngRedisService();

                        long startTime = room.getCreateTime();
                        logger.debug("now: " + System.currentTimeMillis() + " startTime: " + startTime * 1000 + " read: " + read);
                        if (read != Constant.REQ_TYPE_UNREAD) {  //已审批的消息，按照过期处理
                            expireIds += msgId + "@%";
                            expireIdsArr.add(new ObjectId(msgId));
                            expire = true;
                            result = 1;
                            AuditingMsgUtil.addMgrUser(otherMgrUserLst, msgType, msgId, roomId, null, mgrUserIdStr, ownerId);
                        } else {
                            int nowTime = (int) (System.currentTimeMillis() / 1000);                // 系统当前时间
                            String auditor = dao.getUserNickById(ownerId);  //审批者的昵称

                            if (operate == 1) {
                                result = 1;
                                ChipUtils.addChipRecord(userId, chips, ChipUtils.CONSUME_SNGAPPLY, ChipUtils.SOURCE_SNG, roomId, dao.getChip(userId));

                                //管理员直接拒绝 退还筹码
                                dao.updateChip(userId, chips);
                                redisService.removeApplyPlayerSet(roomId, userId);   //从报名的玩家集合里删除该玩家信息
                                redisService.removePlayerHashMapInfo(roomId, userId);  //删除该玩家报名的信息
                                redisService.delSelectClub(roomId, userId, clubId);   //删除该玩家带入的社区id
                            } else {
                                // 已经成功报名的人数
                                int applyCount = 0;
                                Set<String> enters = redisService.getEnterPlayers(roomId);
                                if (enters != null && !enters.isEmpty()) {
                                    applyCount = enters.size();
                                }
                                logger.info("applyJoin applyCount=" + applyCount + " userId=" + userId);
                                if (applyCount >= room.getPlayerCount()) {
                                    result = 3;

                                    // 从申请列表中移除

                                    ChipUtils.addChipRecord(userId, chips, ChipUtils.CONSUME_SNGAPPLY, ChipUtils.SOURCE_SNG, roomId, dao.getChip(userId));

                                    //管理员直接拒绝 退还筹码
                                    dao.updateChip(userId, chips);
                                    redisService.removeApplyPlayerSet(roomId, userId);   //从报名的玩家集合里删除该玩家信息
                                    redisService.removePlayerHashMapInfo(roomId, userId);  //删除该玩家报名的信息
                                    redisService.delSelectClub(roomId, userId, clubId);   //删除该玩家带入的社区id
                                }

                                if (tribeId > 0 && result == 0) { // 同盟局且满足报名条件
                                    Map<Object, Object> tribeInfo = dao.getTribeInfo(tribeId);
                                    if (tribeInfo != null && Integer.valueOf(tribeInfo.get("credit_check").toString()) == 1) { // 该同盟开启了信用积分限制
                                        int[] clubCredit = dao.getClubCredit(tribeId, clubId);
                                        logger.debug("club leftCredit=" + clubCredit[0] + " pause=" + clubCredit[1] + " takeIn=" + chips + " selectClubName=" + clubName);
                                        if (clubCredit[0] >= chips && clubCredit[1] == 0) { // 二次带入也要扣积分
                                            dao.updateClubCredit(tribeId, clubId, -chips);
                                        } else {
//                                            // 暂停带入
//                                            if (clubCredit[1] == 1) {
//                                                dao.insertTribeRequestMessage(roomId, clubId, tribeId, tribeInfo.get("name").toString());
//                                            }

                                            // 社区积分不足，玩家带入失败。通知玩家带入失败，拒绝
                                            logger.debug("club leftCredit=" + clubCredit[0] + " pause=" + clubCredit[1] + " takeIn=" + chips + " tribeName=" + tribeName + " selectClubName=" + clubName);
                                            creditNotEnouthIds += tribeName + "," + clubName;
                                            if (mc.hasNext()) { // 现在没有批量处理，这里暂时不会进入
                                                creditNotEnouthIds += "@%";
                                            }

                                            operate = 1; // 社区积分不足，就算管理员点同意也被当成拒绝
                                            result = 1; // 管理员点了同意，但社区积分不足，系统也拒绝玩家报名

                                            ChipUtils.addChipRecord(userId, chips, ChipUtils.CONSUME_SNGAPPLY, ChipUtils.SOURCE_SNG, roomId, dao.getChip(userId));

                                            //管理员直接拒绝 退还筹码
                                            dao.updateChip(userId, chips);
                                            redisService.removeApplyPlayerSet(roomId, userId);   //从报名的玩家集合里删除该玩家信息
                                            redisService.removePlayerHashMapInfo(roomId, userId);  //删除该玩家报名的信息
                                            redisService.delSelectClub(roomId, userId, clubId);   //删除该玩家带入的社区id
                                        }
                                    }
                                }

                                if (result == 0) {
                                    addRequestInfoSngRecord(userId, redisService.getSngPlayerInfo(roomId, userId));

                                    // 从申请列表中移除
                                    redisService.removeApplyPlayerSet(roomId, userId);
                                    redisService.addEnterPlayerSet(roomId, userId);

                                    redisService.addUserRoomApplySet(userId, 81, roomId);  //牌局发现页使用

                                    logger.info("sng apply successfully userId=" + userId + " id=" + roomId);
                                    redisService.updatePlayerStatus(roomId, userId, "1");   // 报名成功
                                    result = 0;        // 立即进入
                                }

                            }
                            //存入审批记录表中
                            RoomRequest.addApproveRecord(
                                    roomName, roomId, 81, userId, ownerIds, nickName, chips, System.currentTimeMillis(), Constant.SNG_APPLY,
                                    operate, clubId, head, 0, msgId, chips, ownerId, Constant.SNG_ROOM_TYPE, tribeId, tribeName, randomNum,room.getControl()
                            );
                        }
                    }


                    // 从待处理请求列表中移除
                    if (processingMap.containsKey(roomId)) {
                        processingMap.get(roomId).remove(userId);
                    }

                    logger.debug("expire: " + expire);  //如果该条消息被处理过 则不需要通知该玩家审批结果和更改审批消息状态
                    if (!expire) {
                        //通知玩家审批结果
                        SngRequest req = new SngRequest();
                        req.setCreateTime(String.valueOf(time));
                        req.setRoomId(roomId);
                        req.setRoomName(roomName);
                        req.setUserId(userId);
                        req.setOid(msgId);
                        req.setStatus(result);

                        SngRegister.noticePlayer(req, result == 0 ? 4 : 5);
                        successIdsArr.add(new ObjectId(msgId));

                        //更新玩家的审批信息
                        BasicDBObject data = new BasicDBObject();
                        data.put("read", result);  // -1未读 0同意 1拒绝 2已满人，自动拒绝 3牌局解散 4、已过报名/重购时间
                        data.put("owner_id", "");  // 处理完以后设置为空
                        boolean changeStatus = MongodbService.updateFieldsDById(requestCollection, idArray[0], data);
                        logger.debug("ownerId: " + ownerId + " deal msgid: " + msgId + " status: " + result + " mongostatus: " + changeStatus);
                    }

                    // if(result != 5){    //正在拆分卓时，该条消息不当做已处理成功返回
                    AuditingMsgUtil.addMgrUser(otherMgrUserLst, msgType, msgId, roomId, null, mgrUserIdStr, ownerId);
                    successIds += msgId;
                    if (mc.hasNext()) {
                        successIds += "@%";
                    }
                    // }
                } else {  //该条消息被删除了 返回已过期
                    logger.debug("msgid : " + idArray[0] + " has been removed !!! ");
                    expireIds += idArray[0] + "@%";
                    result = 1;
                }

                logger.debug("success ids:" + successIds);
                logger.debug("expire ids:" + expireIds);

                ret[0] = successIds;
                ret[1] = expireIds;
                ret[3] = creditNotEnouthIds; // 社区积分不足，该字段才不为0
                if(null != otherMgrUserLst && otherMgrUserLst.length()>0){
                    AuditingMsgUtil.sendOtherMgr(otherMgrUserLst.toString(),logger);
                }
            }

            if(expireIdsArr.size() > 0){  //删除过期的消息
                BasicDBObject filter = new BasicDBObject("_id", new ObjectId(idArray[0]));
                long removed = MongodbService.removeMany(requestCollection, filter);
                logger.debug("remove cnt:" + removed);
            }

//            //  删除已处理请求
//            logger.debug("success len:" + successIdsArr.size());
//            if (successIdsArr.size() > 0) {
//                BasicDBObject fil = new BasicDBObject("_id", new BasicDBObject("$in", successIdsArr));
//                long removed = MongodbService.removeMany(requestCollection, fil);
//                logger.debug("remove cnt:" + removed);
//            }
        } catch (Exception e) {
            logger.error("batch operate fail", e);
        } finally {
            if (mongo != null) {
                MongodbService.closeCursor(mc);
                MongodbService.close(mongo);
            }
        }

        int status = result <= 1 ? 0 : result; //同意或者拒绝都算是处理成功

        if(!"".equals(ret[1])){ //当该条消息按过期处理时，应该返回已过期1
            status = 1;
        }

        logger.debug("status: " + status + " successIds: " + ret[0] + " expireIds: " + ret[1]);
        Object[][] objs = {
                { 60, status, I366ClientPickUtil.TYPE_INT_1 }, // 0成功 1过期 2申请人不在房间内 3、报名人数已满 4、已过报名/重购时间 5 正在分卓,请稍后处理
                { 130, ret[0] == null?"":ret[0], I366ClientPickUtil.TYPE_STRING_UTF16 }, // 成功处理的请求id
                { 131, ret[1] == null?"":ret[1], I366ClientPickUtil.TYPE_STRING_UTF16 }, // 过期的请求id
                { 133, ret[3], I366ClientPickUtil.TYPE_STRING_UTF16}, //  成功处理的请求id, 但社区积分不足，同意审批失败
        };

        byte[] sendBytes = I366PickUtil.packAll(objs, com.i366.data.Constant.REQ_295_RES_BATCH_CONTROL);

        Request ownerRequest = Data.onlineMap.get(ownerId);
        if (ownerRequest != null) {
            PublisherUtil.publisher(ownerRequest, sendBytes); // 发送给管理员
            logger.debug("MTT APPLY Processor_1715_BatchControl send to:" + ownerRequest.getUserId());
        }

    }

    /**
     * MTT 批量处理请求
     *
     * @param ownerId
     * @param operate
     * @param ids
     * @return
     */
    public static void mttBatchOperate(int ownerId, int operate, String ids) {

        String[] ret = new String[]{"", "", "0", ""};
        List<ObjectId> expireIdsArr = new ArrayList<ObjectId>();

        int result = 0;
        boolean expire = false;  //消息是否已经过期
        MongoClient mongo = null;
        MongoCursor<Document> mc = null;
        StringBuilder otherMgrUserLst = new StringBuilder();
        try {
            mongo = MttMongodbService.getMongoInstance();
            MongoDatabase database = mongo.getDatabase(db);
            MongoCollection<Document> requestCollection = database.getCollection(_mttRequestCollection);

            String[] idArray = ids.split("@%");
            if (idArray.length > 0) {

                logger.debug("id in arr:" + idArray[0]);

                BasicDBObject filter = new BasicDBObject("_id", new ObjectId(idArray[0]));
                mc = MttMongodbService.findOne(requestCollection, filter);

                String successIds = "";
                String expireIds = "";
                String creditNotEnouthIds = ""; // 社区积分不足，审批者通过该字段识别提示该消息对应的社区积分不足

                if (mc.hasNext()) {
                    Document doc = mc.next();
                    logger.debug("get result doc :" + doc.toJson());
                    String msgId = doc.getObjectId("_id").toString();
                    long time = doc.getLong("time");
                    int userId = doc.getInteger("user_id");
                    int matchId = doc.getInteger("room_id");
                    int chips = doc.getInteger("take_in"); // 报名费，不包括服务费
                    int tribeId = doc.getInteger("tribe_id");
                    int read = doc.getInteger("read");
                    int clubId = doc.getInteger("club_id");
                    String gameName = doc.getString("room_name");
                    String nickName = doc.getString("nick_name");
                    String clubName = doc.getString("club_name");
                    String tribeName = doc.getString("tribe_name");
                    String head = doc.getString("head");
                    String ownerIds = doc.getString("owner_id");
                    String randomNum = doc.getString("random_num");


                    RedisService redisService = RedisService.getRedisService();
                    Map<String, String> game = redisService.getMTTGame(matchId);
                    int allowDelay = Integer.parseInt(game.get("allowDelay"));
                    long startTime = Integer.valueOf(game.get("startTime"));

                    logger.debug("initLeveRoom matchid: " + matchId + " mttInfo: " + game + " size: " + game.keySet().size());

                    if (game.keySet().size() == 0 || System.currentTimeMillis() - time > Constant.REQUEST_MTT_APPLY_TIMEOUT) {
                        expireIds += msgId + "@%";
                        expireIdsArr.add(new ObjectId(msgId));
                        if(game.keySet().size() == 0){
                            expire = true;
                        }
                        //expire = true;
                        result = 1;
                    } else {

                        logger.debug("now: " + System.currentTimeMillis() + " startTime: " + startTime * 1000 + " allowDelay: " + allowDelay + " read: " + read);
                        if (System.currentTimeMillis() > (startTime * 1000) && allowDelay == 0) {  //不开延时报名时如果比赛已开始，则返回该条消息已过期
                            //  处理过期请求
                            if (expireIds.length() > 0 && !expireIds.endsWith("@%")) {
                                expireIds += "@%";
                            }
                            expireIds += msgId;
                            if (mc.hasNext()) {
                                expireIds += "@%";
                            }
                            expireIdsArr.add(new ObjectId(msgId));

                            result = 1;
                        } else if (read != Constant.REQ_TYPE_UNREAD) {  //已审批的消息，按照过期处理
                            expireIds += msgId + "@%";
                            expireIdsArr.add(new ObjectId(msgId));
                            expire = true;
                            result = 1;
                        } else {

                            DZPKDao dao = new DZPKDao();

                            int nowTime = (int) (System.currentTimeMillis() / 1000);                // 系统当前时间
                            int upperLimit = Integer.parseInt(game.get("upperLimit"));              // 参赛人数上限
                            int lowerLimit = Integer.parseInt(game.get("lowerLimit"));              // 参赛人数下限
                            int participants = Integer.parseInt(game.get("participants"));          // 报名人数

                            String auditor = dao.getUserNickById(ownerId);  //审批者的昵称

                            if (operate == 1) {

                                result = 1;
                                ChipUtils.addChipRecord(userId, chips, ChipUtils.CONSUME_MTTAPPLY, ChipUtils.SOURCE_RES, matchId, dao.getChip(userId));

                                //管理员直接拒绝 退还筹码
                                dao.updateChip(userId, chips);
                                redisService.removeApplyPlayerSet(matchId, userId);   //从报名的玩家集合里删除该玩家信息
                                redisService.removeApplyPlayerInfo(matchId, userId);  //删除该玩家报名的信息
                                redisService.delSelectClub(matchId, userId, clubId);   //删除该玩家带入的社区id
                            } else {

                                // 已经成功报名的人数
                                int applyCount = 0;
                                Set<String> enters = redisService.getMTTEnterSet(matchId);
                                if (enters != null) {
                                    applyCount = enters.size();
                                }
                                logger.info("applyJoin applyCount=" + applyCount + " userId=" + userId + " participants=" + participants);
                                if (nowTime < startTime && nowTime + 60 >= startTime) {

                                    if (allowDelay == 0) { // 开赛前1min 且没有打开延时报名；报名已经截止
                                        result = 4;

                                        expireIds += msgId + "@%";
                                        expireIdsArr.add(new ObjectId(msgId));

                                        ChipUtils.addChipRecord(userId, chips, ChipUtils.CONSUME_MTTAPPLY, ChipUtils.SOURCE_RES, matchId, dao.getChip(userId));
                                        //退还报名筹码
                                        dao.updateChip(userId, chips);
                                        redisService.removeApplyPlayerSet(matchId, userId);   //从报名的玩家集合里删除该玩家信息
                                        redisService.removeApplyPlayerInfo(matchId, userId);  //删除该玩家报名的信息
                                        redisService.delSelectClub(matchId, userId, clubId);   //删除该玩家带入的社区id
                                    } else {
                                        result = 5;  //正在分桌，请稍后再审批
                                        expire = true;
                                    }

                                } else if (applyCount >= upperLimit) {
                                    result = 3;    // 报名人数已达到参赛人数上限；报名已经截止
                                } else if (nowTime >= startTime && (allowDelay == 0 || (!redisService.isAllowDelayAplly(matchId, userId)))) {
                                    result = 4;    // 当前已经开赛且延时报名开关关闭或者不满足延时报名的条件

                                    //此时该条审批消息处理为已过期,需要删除
                                    expireIds = msgId + "@%";
                                    expireIdsArr.add(new ObjectId(msgId));

                                    ChipUtils.addChipRecord(userId, chips, ChipUtils.CONSUME_MTTAPPLY, ChipUtils.SOURCE_RES, matchId, dao.getChip(userId));
                                    //退还报名筹码
                                    dao.updateChip(userId, chips);
                                    redisService.removeApplyPlayerSet(matchId, userId);   //从报名的玩家集合里删除该玩家信息
                                    redisService.removeApplyPlayerInfo(matchId, userId);  //删除该玩家报名的信息
                                    redisService.delSelectClub(matchId, userId, clubId);   //删除该玩家带入的社区id
                                } else if (nowTime <= startTime && nowTime + 60 >= startTime && applyCount < lowerLimit) {
                                    // 开赛前1min且允许延时报名，人数小于开赛人数下限，不能报名 （因为开赛前1min在拆并桌，如果人数不够会被解散；如果人数够，但还未分桌,会直接进入房间。所以直接提示报名失败）
                                    logger.info("isAllowDelayAplly id=" + matchId + " gameName=" + gameName + " userId=" + userId);
                                    result = 4; // 延时报名，提示人数不够，正在解散房间
                                }

                                if (tribeId > 0 && result == 0) { // 同盟局且满足报名条件
                                    Map<Object, Object> tribeInfo = dao.getTribeInfo(tribeId);
                                    if (tribeInfo != null && Integer.valueOf(tribeInfo.get("credit_check").toString()) == 1) { // 该同盟开启了信用积分限制
                                        int[] clubCredit = dao.getClubCredit(tribeId, clubId);
                                        logger.debug("club leftCredit=" + clubCredit[0] + " pause=" + clubCredit[1] + " takeIn=" + chips + " selectClubName=" + clubName);
                                        if (clubCredit[0] >= chips && clubCredit[1] == 0) { // 二次带入也要扣积分
                                            dao.updateClubCredit(tribeId, clubId, -chips);
                                        } else {
//                                                // 暂停带入
//                                                if (clubCredit[1] == 1) {
//                                                    dao.insertTribeRequestMessage(matchId, clubId, tribeId, tribeInfo.get("name").toString());
//                                                }

                                            // 社区积分不足，玩家带入失败。通知玩家带入失败，拒绝
                                            logger.debug("club leftCredit=" + clubCredit[0] + " pause=" + clubCredit[1] + " takeIn=" + chips + " tribeName=" + tribeName + " selectClubName=" + clubName);
                                            creditNotEnouthIds += tribeName + "," + clubName;
                                            if (mc.hasNext()) { // 现在没有批量处理，这里暂时不会进入
                                                creditNotEnouthIds += "@%";
                                            }

                                            operate = 1; // 社区积分不足，就算管理员点同意也被当成拒绝
                                            result = 1; // 管理员点了同意，但社区积分不足，系统也拒绝玩家报名

                                            ChipUtils.addChipRecord(userId, chips, ChipUtils.CONSUME_MTTAPPLY, ChipUtils.SOURCE_ROOM, matchId, dao.getChip(userId));

                                            //管理员直接拒绝 退还筹码
                                            dao.updateChip(userId, chips);
                                            redisService.removeApplyPlayerSet(matchId, userId);   //从报名的玩家集合里删除该玩家信息
                                            redisService.removeApplyPlayerInfo(matchId, userId);  //删除该玩家报名的信息
                                            redisService.delSelectClub(matchId, userId, clubId);   //删除该玩家带入的社区id
                                        }
                                    }
                                }

                                if (result == 0) {
                                    addRequestInfoMttRecord(userId, redisService.getMTTPlayerInfo(matchId, userId));

                                    // 从申请列表中移除
                                    redisService.removeApplyPlayerSet(matchId, userId);
                                    redisService.addMTTEnterSet(matchId, userId, nowTime);
                                    redisService.addMTTParticipants(matchId);            // 增加成功报名人数

                                    redisService.addUserRoomApplySet(userId, 71, matchId);  //牌局发现页使用
                                    redisService.updateMttPlayer(matchId, userId, "auditor", auditor); //更新报名信息中审核人的昵称
                                    dao.updateMTTParticipants(matchId);        // 往数据库增加成功报名人数

                                    logger.info("apply game successfully userId=" + userId + " id=" + matchId);
                                    redisService.setMTTPlayerStatu(matchId, userId, "1");// 报名成功
                                    redisService.updateMttClubUsers(matchId,String.valueOf(clubId),String.valueOf(userId));

                                    result = 0;        // 立即进入

                                    if (allowDelay == 1 && System.currentTimeMillis() > (startTime * 1000)) {

                                        //审批时如果发现比赛已经开始且开启了延时报名需要将该玩家放入延时报名的集合中
                                        //redisService.addDelayPlayer(matchId,userId,nowTime);
//                                        Registration reg = Registration.getShortReg(Config.CONNECT_MTT_SERVER);
                                        Object[][] objs = {
                                                {130, matchId, I366ClientPickUtil.TYPE_INT_4}, // matchId
                                                {131, 71, I366ClientPickUtil.TYPE_INT_4}, // roompath
                                                {132, userId, I366ClientPickUtil.TYPE_INT_4}, // userid
                                                {133, result, I366ClientPickUtil.TYPE_INT_4}, // result
                                                {134, 0, I366ClientPickUtil.TYPE_INT_4},      // 是否需要审批 0不需要 1需要
                                                {135, String.valueOf(time), I366ClientPickUtil.TYPE_STRING_UTF16},      // time
                                                {136, msgId, I366ClientPickUtil.TYPE_STRING_UTF16},      // msgId
                                        };
                                        byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_180_MTT_APPLY);
//                                        reg.publishByte(bytes);

                                    }
                                }

                            }
                            //存入审批记录表中
                            RoomRequest.addApproveRecord(
                                    gameName, matchId, 71, userId, ownerIds, nickName, chips, System.currentTimeMillis(),
                                    Constant.MTT_OPERATE_APPLY, operate, clubId, head, 0, msgId, chips, ownerId,
                                    Constant.MTT_ROOM_TYPE, tribeId, tribeName, randomNum,1
                            );
                        }
                    }


                    // 从待处理请求列表中移除
                    if (processingMap.containsKey(matchId)) {
                        processingMap.get(matchId).remove(userId);
                    }

                    logger.debug("expire: " + expire + " allowDelay: " + allowDelay);  //如果该条消息被处理过 则不需要通知该玩家审批结果和更改审批消息状态
                    if (!expire) {

                        if (allowDelay == 0 || (System.currentTimeMillis() < (startTime * 1000) && allowDelay == 1)) {
                            //通知玩家审批结果
                            MttRequest req = new MttRequest();
                            req.setCreateTime(String.valueOf(time));
                            req.setMatchId(matchId);
                            req.setMatchName(gameName);
                            req.setUserId(userId);
                            req.setUid(msgId);
                            req.setStatus(result);

                            MttRegister.noticePlayer(req, Constant.MTT_APPLY);
                        }

                        //更新玩家的审批信息
                        BasicDBObject data = new BasicDBObject();
                        data.put("read", result);  // -1未读 0同意 1拒绝 2已满人，自动拒绝 3牌局解散 4、已过报名/重购时间
                        data.put("owner_id", "");  // 处理完以后设置为空
                        boolean changeStatus = MttMongodbService.updateFieldsDById(requestCollection, idArray[0], data);
                        logger.debug("ownerId: " + ownerId + " deal msgid: " + msgId + " status: " + result + " mongostatus: " + changeStatus);
                    }

                    if (result != 5) {    //正在系统分卓时，该条消息不当做已处理成功返回
                        successIds += msgId;
                        if (mc.hasNext()) {
                            successIds += "@%";
                        }
                        AuditingMsgUtil.addMgrUser(otherMgrUserLst,0, msgId, null, matchId, ownerIds, ownerId);
                    }

                } else {  //该条消息被删除了 返回已过期
                    logger.debug("msgid : " + idArray[0] + " has been removed !!! ");
                    expireIds += idArray[0] + "@%";
                    result = 1;
                }

                logger.debug("success ids:" + successIds);
                logger.debug("expire ids:" + expireIds);

                ret[0] = successIds;
                ret[1] = expireIds;
                ret[3] = creditNotEnouthIds; // 社区积分不足，该字段才不为0
            }

            if (expireIdsArr.size() > 0) {  //删除过期的消息
                BasicDBObject filter = new BasicDBObject("_id", new ObjectId(idArray[0]));
                long removed = MttMongodbService.removeMany(requestCollection, filter);
                logger.debug("remove cnt:" + removed);
            }

        } catch (Exception e) {
            logger.error("batch operate fail", e);
        } finally {
            if (mongo != null) {
                MongodbService.closeCursor(mc);
                MongodbService.close(mongo);
            }
        }

        int status = result <= 1 ? 0 : result; //同意或者拒绝都算是处理成功

        if (StringUtils.isNotEmpty(ret[1])) { //当该条消息按过期处理时，应该返回已过期1
            status = 1;
        }

        logger.debug("status: " + status + " successIds: " + ret[0] + " expireIds: " + ret[1]);
        Object[][] objs = {
                {60, status, I366ClientPickUtil.TYPE_INT_1}, // 0成功 1过期 2申请人不在房间内 3、报名人数已满 4、已过报名/重购时间 5 正在系统分卓,请稍后处理
                {130, ret[0] == null ? "" : ret[0], I366ClientPickUtil.TYPE_STRING_UTF16}, // 成功处理的请求id
                {131, ret[1] == null ? "" : ret[1], I366ClientPickUtil.TYPE_STRING_UTF16}, // 过期的请求id
                {133, ret[3], I366ClientPickUtil.TYPE_STRING_UTF16}, //  成功处理的请求id, 但社区积分不足，同意审批失败
        };

            byte[] sendBytes = I366PickUtil.packAll(objs, com.i366.data.Constant.REQ_295_RES_BATCH_CONTROL);

        Request ownerRequest = Data.onlineMap.get(ownerId);
        if (ownerRequest != null) {
            PublisherUtil.publisher(ownerRequest, sendBytes); // 发送给管理员
            logger.debug("MTT APPLY Processor_1715_BatchControl send to:" + ownerRequest.getUserId());
        }

        /**
         * 向其它管理员发送通知消息
         */
        if(otherMgrUserLst.length()==0){
            logger.warn(String.format("No msg need be sent to other manager : " +
                            "op=%s , opUserId=%s , msgIds=%s",
                     operate,ownerId,ids
                    ));
            return;
        }
        AuditingMsgUtil.sendOtherMgr(otherMgrUserLst.toString(),logger);
    }

    /**
     * 新增带入消息审批记录
     * @param userId 请求带入的userid
     * @param ownerId 管理权限的字符串
     * @param nickName 请求带入的username
     * @param chips 带入的筹码
     * @param time 请求时间
     * @param type 请求类型
     * @param status 处理状态
     * @param selectClubId  选择的社区id
     * @param head  请求者的头像
     * @param clubType 房间类型
     * @param msgId 消息id
     * @param entry_fee 报名费  非mtt局传0
     * @param operator 处理者id
     * @param roomType 房间类型
     */

    public static void addApproveRecord(String matchName, int matchId, int roomPath, int userId, String ownerId, String nickName, int chips,
                                        long time, int type, int status, int selectClubId, String head, int clubType, String msgId,
                                        int entry_fee, int operator, int roomType, int tribeId, String tribeName, String randomNum,int isControl) {
        MongoClient mongo = null;
        try {
            mongo = MongodbService.getMongoInstance();
            MongoDatabase database = mongo.getDatabase(db);
            MongoCollection<Document> requestCollection = database.getCollection(_requestCollectionRecord);

            Document requestRecord = new Document();

            requestRecord.put("room_id", matchId);                      //  所属比赛id
            requestRecord.put("room_path", roomPath);                   //  所属房间path
            requestRecord.put("room_name", matchName);                  //  所属房间名
            requestRecord.put("user_id", userId);                       //  请求用户ID
            requestRecord.put("owner_id", ownerId);                     //  所属房主ID／或者管理员、创建者id
            requestRecord.put("nick_name", nickName);                   //  请求用户昵称
            requestRecord.put("take_in", chips);                        //  请求带入筹码数
            requestRecord.put("msg_id", msgId);                         //  原请求消息id
            requestRecord.put("type", type);                            //  请求消息类型
            requestRecord.put("operator", operator);                    //  操作者id
            requestRecord.put("sb", 0);                                 //  房间小盲注
            requestRecord.put("bb", 0);                                 //  房间大盲注
            requestRecord.put("start_time",0);                          //  牌局开始时间
            requestRecord.put("time", time);                            //  请求时间
            requestRecord.put("status",status);                         //  消息处理状态  0同意带入  1是拒绝带入
            requestRecord.put("ignore_bring", 0);                       //  本次带入的筹码是否不计入总和
            requestRecord.put("club_id", selectClubId);                 //  选择的社区id
            requestRecord.put("head", head);                            //  玩家头像
            requestRecord.put("room_type", roomType);                   //  房间类型
            requestRecord.put("club_type", clubType);                   //  社区类型
            requestRecord.put("tribe_id", tribeId);                     //  同盟id  默认0
            requestRecord.put("tribe_name", tribeName);                 //  同盟名称
            requestRecord.put("entry_fee", entry_fee);                  //  报名费 用于mtt
            requestRecord.put("random_num", randomNum);                 //  随机ID
            requestRecord.put("control", isControl);                    //  是否开启控制带入 0否1是

            requestCollection.insertOne(requestRecord);
        }catch(Exception e){
            logger.error(e.getMessage());
        }finally {
            if (mongo != null) {
                MongodbService.close(mongo);
            }
        }
    }
    
    /**
     * 记录玩家成功带入的信息
     * @param userRequetInfo
     */
    public static void addRequestInfoSngRecord(int userId,UserRequetInfo userRequetInfo){
        MongoClient mongo = null;
        try {
            mongo = MongodbService.getMongoInstance();
            MongoDatabase database = mongo.getDatabase(db);
            MongoCollection<Document> requestCollection = database.getCollection(_requestInfoSngRecord);

            Document requestRecord = new Document();
            
            requestRecord.put("user_id", userId);					  //用户id
            requestRecord.put("ip", userRequetInfo.getIp());          //带入时ip   
            requestRecord.put("gps", userRequetInfo.getGps());        //带入时经纬度
            requestRecord.put("imei", userRequetInfo.getImei());      //带入时设备机器码


            requestCollection.insertOne(requestRecord);
        }catch(Exception e){
            logger.error(e.getMessage());
        }finally {
            if (mongo != null) {
                MongodbService.close(mongo);
            }
        }
    }
    
    /**
     * 记录玩家成功带入的信息
     * @param userId 
     * @param userRequetInfo
     */
    public static void addRequestInfoMttRecord(int userId, UserRequetInfo userRequetInfo){
        MongoClient mongo = null;
        try {
            mongo = MttMongodbService.getMongoInstance();
            MongoDatabase database = mongo.getDatabase(db);
            MongoCollection<Document> requestCollection = database.getCollection(_requestInfoMttRecord);

            Document requestRecord = new Document();
            
            requestRecord.put("user_id", userId);					  //用户id
            requestRecord.put("ip", userRequetInfo.getIp());          //带入时ip   
            requestRecord.put("gps", userRequetInfo.getGps());        //带入时经纬度
            requestRecord.put("imei", userRequetInfo.getImei());      //带入时设备机器码


            requestCollection.insertOne(requestRecord);
        }catch(Exception e){
            logger.error(e.getMessage());
        }finally {
            if (mongo != null) {
                MongodbService.close(mongo);
            }
        }
    }

}
