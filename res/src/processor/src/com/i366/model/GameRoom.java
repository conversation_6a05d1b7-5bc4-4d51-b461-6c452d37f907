/*
 * $RCSfile: GameRoom.java,v $
 * $Revision: 1.1  $
 * $Date: 2012-8-14  $
 *
 * Copyright (C) 2005 Bettem, Inc. All rights reserved.
 *
 * This software is the proprietary information of Bettem, Inc.
 * Use is subject to license terms.
 */
package com.i366.model;

import java.sql.Timestamp;

/**
 * <p>Title: GameRoom</p> 
 * <p>Description: </p> 
 * <p>Copyright: Copyright (c) 2006</p> 
 * <AUTHOR>
 * @version 1.0
 */
public class GameRoom {
	
	private int id;          // 房间ID
	private int roomSpeed;   // 房间速度(0 普通 1 快速)
	private int waitTime;    // 等待时间
	private int smallBlind;  // 小盲注
	private int bigBlind;    // 大盲注
	private int playNumber;  // 在玩人数
	private int commission;  // 服务费
	private int roomUrl;     // 房间路径
	private int status;      // 房间状态
	private int minChip;     // 最少携带筹码
	private int maxChip;     // 最大携带筹码
	private int minqj;       // 最少区间
	private int maxqj;       // 最大区间
	private int bisaiType;   // 比赛房间类型
	private int onePrize;    // 第一名奖金（比赛房间）
	private int twoPrize;    // 第二名奖金（比赛房间）
	
	private String name;     // 房间名字
	private int control;     // 是否控制带入
	private int maxPlayTime; // 牌局时间，过期结束牌局
	private int creator;     // 房间创建者
	private int progress;	 // 房间整体状态 0:close 1:create 2:pending 3:playing 4:played
	private long createTime; // 房间创建时间 
	private long startTime;	 // 房间开始游戏时间
	private int minRate;	 // 房间带入最小倍数
	private int maxRate;	 // 房间带入最大倍数
	private int advanceCharge;	// 高级功能扣除砖石数 0表示未开启高级功能
	
	
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public int getRoomSpeed() {
		return roomSpeed;
	}
	public void setRoomSpeed(int roomSpeed) {
		this.roomSpeed = roomSpeed;
	}
	public int getWaitTime() {
		return waitTime;
	}
	public void setWaitTime(int waitTime) {
		this.waitTime = waitTime;
	}
	public int getSmallBlind() {
		return smallBlind;
	}
	public void setSmallBlind(int smallBlind) {
		this.smallBlind = smallBlind;
	}
	public int getBigBlind() {
		return bigBlind;
	}
	public void setBigBlind(int bigBlind) {
		this.bigBlind = bigBlind;
	}
	public int getMinChip() {
		return minChip;
	}
	public void setMinChip(int minChip) {
		this.minChip = minChip;
	}
	public int getPlayNumber() {
		return playNumber;
	}
	public void setPlayNumber(int playNumber) {
		this.playNumber = playNumber;
	}
	public int getCommission() {
		return commission;
	}
	public void setCommission(int commission) {
		this.commission = commission;
	}
	public int getRoomUrl() {
		return roomUrl;
	}
	public void setRoomUrl(int roomUrl) {
		this.roomUrl = roomUrl;
	}
	public int getStatus() {
		return status;
	}
	public void setStatus(int status) {
		this.status = status;
	}
	public int getMaxChip() {
		return maxChip;
	}
	public void setMaxChip(int maxChip) {
		this.maxChip = maxChip;
	}
	public int getMinqj() {
		return minqj;
	}
	public void setMinqj(int minqj) {
		this.minqj = minqj;
	}
	public int getMaxqj() {
		return maxqj;
	}
	public void setMaxqj(int maxqj) {
		this.maxqj = maxqj;
	}
	public int getBisaiType() {
		return bisaiType;
	}
	public void setBisaiType(int bisaiType) {
		this.bisaiType = bisaiType;
	}
	public int getOnePrize() {
		return onePrize;
	}
	public void setOnePrize(int onePrize) {
		this.onePrize = onePrize;
	}
	public int getTwoPrize() {
		return twoPrize;
	}
	public void setTwoPrize(int twoPrize) {
		this.twoPrize = twoPrize;
	}
	public int getProgress() {
		return progress;
	}
	public void setProgress(int progress) {
		this.progress = progress;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public int getMaxPlayTime() {
		return maxPlayTime;
	}
	public void setMaxPlayTime(int maxPlayTime) {
		this.maxPlayTime = maxPlayTime;
	}
	public int getControl() {
		return control;
	}
	public void setControl(int control) {
		this.control = control;
	}
	public int getCreator() {
		return creator;
	}
	public void setCreator(int creator) {
		this.creator = creator;
	}
	public long getCreateTime() {
		return createTime;
	}
	public void setCreateTime(long createTime) {
		this.createTime = createTime;
	}
	public long getStartTime() {
		return startTime;
	}
	public void setStartTime(long startTime) {
		this.startTime = startTime;
	}
	public void setMinRate(int minRate) {
		this.minRate = minRate;
	}
	public void setMaxRate(int maxRate) {
		this.maxRate = maxRate;
	}
	public int getMinRate() {
		return minRate;
	}
	public int getMaxRate() {
		return maxRate;
	}
	public void setAdvanceCharge(int advanceCharge) {
		this.advanceCharge = advanceCharge;
	}
	public int getAdvanceCharge() {
		return advanceCharge;
	}
}
