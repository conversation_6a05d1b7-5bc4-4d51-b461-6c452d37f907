package com.i366.model;

import lombok.*;

import java.io.Serializable;

@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SystemConfigUpdateMessage implements Serializable {

    /**
     * 配置类型
     */
    private int configType;

    /**
     * 时间戳
     */
    private long timestamp;

    public interface ConfigType {
        int PLATFORM_CONSUMPTION = 1;
    }
}
