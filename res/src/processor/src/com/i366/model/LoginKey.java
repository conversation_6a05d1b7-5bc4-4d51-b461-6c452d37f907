package com.i366.model;

public class LoginKey {

	public LoginKey(final String key, final long createTime, final String photoURL, 
			final int userProductId, final String userNickName, final String phoneNumber,
			final String country, final String city, final String province, int sex) {
		this.key = key;
		this.createTime = createTime;
		this.photoURL = photoURL;
		this.userProductId = userProductId;
		this.userNickName = userNickName;
		this.phoneNumber = phoneNumber;
		this.country = country;
		this.city = city;
		this.province = province;
		this.sex = sex;
	}
	
	private String key; 			// 用户登录验证密匙
	private long createTime; 		// 创建时间 
	private String photoURL; 		// 用户照片URL
	private int userProductId; 		// 用户的产品号
	private String userNickName;	// 用户昵称
	private String phoneNumber;		// 手机号
	private String country;			// 国家
	private String city;			// 城市
	private String province;		// 省份
	private int sex;                // 性别
	
	public String getKey() {
		return key;
	}
	public void setKey(String key) {
		this.key = key;
	}
	public long getCreateTime() {
		return createTime;
	}
	public void setCreateTime(long createTime) {
		this.createTime = createTime;
	}
	public String getPhotoURL() {
	    return photoURL;
	}
	public void setPhotoURL(String url) {
	    photoURL = url;
	}
	public int getUserProdID() {
	    return userProductId;
	}
	public void setUserProductId(int id) {
	    userProductId = id;
    }
	public String getUserNickName() {
		return userNickName;
	}
	public String getPhoneNumber() {
		return phoneNumber;
	}
	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}
	public String getCountry() {
		return country;
	}
	public void setCountry(String country) {
		this.country = country;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getProvince() {
		return province;
	}
	public void setProvince(String province) {
		this.province = province;
	}
	public int getSex() {
	    return sex;
	}
	public void setSex(int sex) {
	    this.sex = sex;
	}
}
