/**
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.i366.model;

/**
 * 记录用户带入的ip ,gps,机器码
 * <AUTHOR>
 *
 */
public class UserRequetInfo {
    
    private String ip = "";//ip
    
    private String gps = "";//经度,纬度
    
    private String imei = "";//机器码
    
    public UserRequetInfo() {
    }

    /**
     * @param ip
     * @param gps
     * @param imei
     */
    public UserRequetInfo(String ip, String gps, String imei) {
        this.ip = ip;
        this.gps = gps;
        this.imei = imei;
    }
    
    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getGps() {
        return gps;
    }

    public void setGps(String gps) {
        this.gps = gps;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }
    
    

}
