package com.i366.service;

import com.work.comm.util.RedisUtil;
import org.apache.logging.log4j.Logger;

import com.i366.data.Data;


import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/**
 * redis 缓存服务类
 * <AUTHOR>
 *
 */
public class RedisPoolService {	
	private final static Logger logger = com.work.comm.util.LogUtil.getLogger(RedisPoolService.class);

	private static JedisPool jedisPool = null;
	
	private static JedisPool getPool() {
		if (jedisPool == null) {
			JedisPoolConfig config = new JedisPoolConfig();
			config.setMaxTotal(5000);
			config.setMaxIdle(10);
			config.setTestOnBorrow(true);
			config.setTestOnReturn(true);

			jedisPool = new JedisPool(config,
					RedisUtil.getHostAddress(Data.PRO, "redis.ip"),
					RedisUtil.getHostPort(Data.PRO,"redis.port"),
					RedisUtil.getTimeout(Data.PRO,"redis.timeout"),
					RedisUtil.getPasswd(Data.PRO,"redis.pwd"));
		}
		return jedisPool;
	}
	
	/**
	 * 保持在每个RedisService中只实现一次
	 * @return
	 */
	public static Jedis getJedis() {
		JedisPool pool = getPool();
		return pool.getResource();
	}
	
	public static void close() {
		if (jedisPool != null) {
			jedisPool.close();
		}
	}
	
	public static void main(String[] args) throws InterruptedException {
		Jedis jedis = null;
		for (int i = 0; i < 20; i++) {
			jedis = RedisPoolService.getJedis();
			jedis.set("sss", "bbb");
			System.out.println(jedis.get("sss"));
			Thread.sleep(10000);
		}
		jedis.set("sss", "bbb");
		System.out.println(jedis.get("sss"));
		//jedis.close();
		//RedisPoolService.close();
		jedis = RedisPoolService.getJedis();
		jedis.set("ttt", "bbb");
		System.out.println(jedis.get("ttt"));
		//jedis.close();
		//RedisPoolService.close();
		Thread.sleep(10000);
		
	}

}
