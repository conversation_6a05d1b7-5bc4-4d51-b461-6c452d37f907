package com.i366.processor.service;

import java.util.Map;

import com.i366.data.Constant;
import com.work.comm.endpoints.res.ResServerManager;
import com.work.comm.s2s.processor.impl.AbstractProcessorImpl;
import com.work.comm.s2s.protocal.Protocal;
import com.work.comm.s2s.protocal.S2PacckageUtil;
import com.work.comm.s2s.protocal.ServiceRequest;
import lombok.extern.slf4j.Slf4j;

import com.i366.data.Data;
import com.work.comm.util.I366PickUtil;
import com.work.comm.util.PublisherUtil;
import com.work.db.imp.DZPKDao;

/**
 * 删除服务器已登录用户（用户在其他服务器登录）
 * <p>Title: Processor_1301_DeleteUser </p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2006</p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class Processor_1391_DeleteUser extends AbstractProcessorImpl {
    /**
     * 接收的数据包的处理方法
     *
     * @param req  请求对象，必填
     *
     * @return  返回的业务数据
     *   null或byte[0] 表示不需要返回客户端
     */
    public byte[] handle(ServiceRequest req) {
        int[][] int2 = {
                {128, Protocal.TYPE_INT_4},
                {129, Protocal.TYPE_INT_4}
        };
        Map<Integer, Object> map = S2PacckageUtil.pickAll(req.getDataBuffer(), int2);
        int key = (int)map.get(128);
        int loginType = (int)map.get(129);
        log.debug("1391 delete user ID: {},loginType:{}" ,key,loginType);
        // 用户已登录
        if (Data.onlineMap.containsKey(key)) {
            log.info("1391 user in onlineMap : UID: {} ,loginType:{}",key,loginType);
            if(loginType==12){ // 用户/密码登陆才需要下发踢出协议
                // 通知用户账号在其他地方登录
                Object[][] clientObjs = {
                        {60, 1, I366PickUtil.TYPE_INT_1}
                };
                byte[] bytes = I366PickUtil.packAll(clientObjs, Constant.REQ_55_EXIT);
                PublisherUtil.publisher(key, bytes);

                ResServerManager.getInstance().decreasePuByDelta();
            }

            // 踢掉用户
            Data.onlineMap.get(key).getChannel().close();
            Data.onlineMap.remove(key);
            try {
                DZPKDao dao = new DZPKDao();
                dao.operation(Data.SQL_MAP.get("update_login_records"), new Object[]{key});
            } catch (Exception e) {
                log.error("update user logout record fail", e);
            }

            log.info("kick user away successfully" + key);
        }
        return new byte[]{};
    }

}

