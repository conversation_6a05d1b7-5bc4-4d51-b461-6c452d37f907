/**
 * $RCSfile: Processor_1301_DeleteUser.java,v $
 * $Revision: 1.1  $
 * $Date: 2012-10-9  $
 *
 * Copyright (C) 2005 Bettem, Inc. All rights reserved.
 *
 * This software is the proprietary information of Bettem, Inc.
 * Use is subject to license terms.
 */
package com.i366.processor.service;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;

import com.work.comm.SngRegister;
import org.apache.logging.log4j.Logger;

import com.i366.data.Data;
import com.i366.util.CommonBytes;
import com.work.client.util.I366ClientPickUtil;
import com.work.comm.io.Handler;
import com.work.comm.protocal.BaseRequest;
import com.work.comm.protocal.ServiceRequest;
import com.work.comm.util.PublisherUtil;

/**
 * 通知客户端MTT比赛1分钟后开始
 * <p>Title: Processor_504_OneMinNotify </p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2016</p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public class Processor_1711_SNG_Notify extends Handler {

    private static Logger logger = com.work.comm.util.LogUtil.getLogger(Processor_1711_SNG_Notify.class);

    @Override
    public byte[] handleRequest(BaseRequest req) {
        ServiceRequest rquest = (ServiceRequest) req;
        byte[] bytes = rquest.getBt();
        int[][] int2 = {
                {130, I366ClientPickUtil.TYPE_INT_4},           // 通知类型1开赛通知 2主动解散 3被动解散 4审核通过 5审核未通过
                {131, I366ClientPickUtil.TYPE_INT_4},           // sng id
                {132, I366ClientPickUtil.TYPE_STRING_UTF16},    // sng name
                {133, I366ClientPickUtil.TYPE_STRING_UTF16},    // sng ip
                {134, I366ClientPickUtil.TYPE_INT_4},           // sng port
                {135, I366ClientPickUtil.TYPE_INT_4_ARRAY},     // 通知玩家id
        };
        Map<Integer, Object> map = I366ClientPickUtil.pickAll(bytes, int2);
        int type = Integer.valueOf(map.get(130).toString());
        int roomId = Integer.valueOf(map.get(131).toString());
        String name = map.get(132).toString();
        String sngIp = map.get(133).toString();
        int sngPort = Integer.valueOf(map.get(134).toString());
        Integer[] userIds = (Integer[]) map.get(135);

        // 通知已报名但未处理的玩家报名失效
        SngRegister.expireNotice(roomId, type);
        SngRegister.noticePlayerExpire(roomId, type);

        logger.info("roomId: " + roomId + ", type: " + type + ", name: " + name);
        logger.info("userId: " + Arrays.toString(userIds));
        Set<Integer> rejectUserIds = SngRegister.getRejectUserIds(roomId);
        for (Integer userId : userIds) {
            if (Data.onlineMap.containsKey(userId) && !rejectUserIds.contains(userId)) {
//                Object[][] clientObjs = {
//                        {60, 0, I366PickUtil.TYPE_INT_1},
//                        {130, type, I366ClientPickUtil.TYPE_INT_4},             // 通知类型
//                        {131, roomId, I366ClientPickUtil.TYPE_INT_4},           // sng id
//                        {132, name, I366ClientPickUtil.TYPE_STRING_UTF16},      // sng name
//                        {133, sngIp, I366ClientPickUtil.TYPE_STRING_UTF16},     // sng ip
//                        {134, sngPort, I366ClientPickUtil.TYPE_INT_4},          // sng port
//                };
//                bytes = I366PickUtil.packAll(clientObjs, Constant.REQ_SNG_NOTICE);
                bytes = CommonBytes.sngNoticeBytes(0, type, roomId, name, sngIp, sngPort);
                PublisherUtil.publisher(userId, bytes);
                logger.info("notify user " + userId + " sng begin success!");
            }
        }
        return new byte[]{};
    }

}

