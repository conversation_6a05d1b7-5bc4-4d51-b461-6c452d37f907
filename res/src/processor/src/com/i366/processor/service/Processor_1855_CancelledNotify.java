package com.i366.processor.service;

import java.util.Map;

import com.work.comm.s2s.processor.impl.AbstractProcessorImpl;
import com.work.comm.s2s.protocal.Protocal;
import com.work.comm.s2s.protocal.S2PacckageUtil;
import com.work.comm.s2s.protocal.ServiceRequest;
import org.apache.logging.log4j.Logger;

import com.i366.data.Constant;
import com.i366.data.Data;
import com.work.client.util.I366ClientPickUtil;
import com.work.comm.protocal.BaseRequest;
import com.work.comm.util.I366PickUtil;
import com.work.comm.util.PublisherUtil;

/**
 * <AUTHOR>
 * 
 */
public class Processor_1855_CancelledNotify extends AbstractProcessorImpl {
	
	private static Logger logger = com.work.comm.util.LogUtil.getLogger(Processor_1855_CancelledNotify.class);

	@Override
	public byte[] handle(ServiceRequest req) {
        try {
            int[][] int2 = {
                    {130, Protocal.TYPE_INT_4},                          // MTT id
                    {131, Protocal.TYPE_STRING_UTF16},     				// MTT name
                    {132, Protocal.TYPE_INT_4_ARRAY},                    // MTT players
                    {133, Protocal.TYPE_STRING_UTF16},                   // msg
            };
            Map<Integer, Object> map = S2PacckageUtil.pickAll(req.getDataBuffer(), int2);
            int matchId = Integer.valueOf(map.get(130).toString());
            String mttName = map.get(131).toString();
            int[] userIds = (int[]) map.get(132);
            String message = map.get(133).toString();

            logger.info("matchId: " + matchId + ", mttName: " + mttName + ", message: " + message + ", userIds: " + userIds.length);

            for (Integer userId : userIds) {
                logger.debug("mtt cancelled userId: " + userId);
                if (Data.onlineMap.containsKey(userId)) {
                    Object[][] clientObjs = {
                            {60, 0, I366PickUtil.TYPE_INT_1},
                            {130, matchId, I366ClientPickUtil.TYPE_INT_4},                          // MTT id
                            {131, mttName, I366ClientPickUtil.TYPE_STRING_UTF16},     				// MTT name
                            {132, message, I366ClientPickUtil.TYPE_STRING_UTF16},                   // msg
                    };
                    byte[] bytes = I366PickUtil.packAll(clientObjs, Constant.REQ_222_MTT_GAME_CANCELLED);
                    PublisherUtil.publisher(userId, bytes);
                    logger.info("notify user " + userId + " mtt cancelled success!");
                }
            }
        } catch (Exception e) {
            logger.error("Processor_1855_CancelledNotify error.", e);
        }
        return new byte[]{};
	}

}
