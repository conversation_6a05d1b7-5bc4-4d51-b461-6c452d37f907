package com.i366.processor.client;

import java.util.Map;

import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;

import com.i366.data.Data;
import com.i366.service.RedisService;
import com.work.client.util.I366ClientPickUtil;
import com.work.comm.io.Handler;
import com.work.comm.protocal.BaseRequest;
import com.work.comm.protocal.Request;
import com.work.comm.util.I366PickUtil;
import com.work.db.imp.DZPKDao;

/**
 * MTT创建者对玩家的申请(报名、重购、增购)进行管理
 * <AUTHOR>
 * @email  <EMAIL>
 * @date   2016年8月19日
 *
 */
public class Processor_520_MTT_ApplyManage extends Handler {
	private static Logger logger = com.work.comm.util.LogUtil.getLogger(Processor_520_MTT_ApplyManage.class);

	@Override
	public byte[] handleRequest(BaseRequest req) {
		Request request = (Request) req;
		byte[] bytes = req.getBt();	
		
		int[][] keyArray = {
				{130, I366PickUtil.TYPE_INT_4},				// MTT ID
				{131, I366ClientPickUtil.TYPE_STRING_UTF16},// JSONObject数组转换成的String:  ID: 223232, Type: 报名(apply)、重购(rebuy)、增购(append)
				{132, I366PickUtil.TYPE_STRING_UTF16}		// 行为类型: 通过、拒绝
			}; 
		
		Map<Integer, Object> map = I366PickUtil.pickAll(bytes, keyArray);
		// 获取客户端发送过来的数值
		int id            = (Integer)map.get(130);
		String jsonString = (String)map.get(131);
		String operate    = (String)map.get(132);
		
		JSONObject jsonObject = new JSONObject(jsonString);
		JSONArray applyList   = jsonObject.getJSONArray("applyList");
    	RedisService redisService = RedisService.getRedisService();
    	for (int i = 0; i < applyList.length(); i ++) {
    		JSONObject object = applyList.getJSONObject(i);
			int userID  = (Integer) object.get("ID");
			String type = (String) object.get("type");

    		if (operate.equals("pass")) {
    			if (type.equals("apply")) {
    				// 报名成功
    				
					int nowTime   = (int)(System.currentTimeMillis()/1000);
					// 玩家报名成功，ID直接放入 enter set中
	    			redisService.addMTTEnterSet(id, userID, nowTime);
	    			redisService.addMTTParticipants(id);			// 增加成功报名人数
    			
	    			logger.info("apply game successfully");
	    			redisService.setMTTPlayerStatu(id, userID, "1");// 报名成功
    			
    			} else if (type.equals("rebuy")) {
    				// 重购
    			} else if (type.equals("append")) {
    				// 增购
    			}
    		} else {
    			if (type.equals("apply")) {
	    			// 拒绝参赛????需要哪些操作
	    			
	        		DZPKDao dao = new DZPKDao();    		
					Map<String, String> gameDetail = redisService.getMTTGame(id);
					// 设置玩家存放在redis HashMap中的状态为【已拒绝】
					redisService.setMTTPlayerStatu(id, userID, "2");
					// 从申请列表中移除
					redisService.removeApplyPlayerSet(id, userID);
					
	    			int initialCoin = Integer.parseInt(gameDetail.get("initialChip"));
		    		int mttFees     = (int) (0.1 * initialCoin + 0.01 * initialCoin);
		    		// 返回玩家筹码
		    		dao.updateChip(userID, mttFees);
    			} else if (type.equals("rebuy")) {
					// 拒绝重购
				} else if (type.equals("append")) {
					// 拒绝增购
				}
    		}
    		// 如果玩家在线，则发送处理结果的通知
    		if (Data.onlineMap.containsKey(userID)) {
    			// 
    		}
    	}		
		return null;
	}

}
