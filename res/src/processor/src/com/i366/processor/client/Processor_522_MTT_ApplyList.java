package com.i366.processor.client;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

import org.apache.logging.log4j.Logger;
import org.json.JSONObject;

import com.i366.service.RedisService;
import com.work.client.util.I366ClientPickUtil;
import com.work.comm.io.Handler;
import com.work.comm.protocal.BaseRequest;
import com.work.comm.protocal.Request;
import com.work.comm.util.I366PickUtil;

/**
 * 返回玩家的申请列表 (报名、重购、增购)
 * <AUTHOR>
 * @email  <EMAIL>
 * @date   2016年9月1日
 *
 */
public class Processor_522_MTT_ApplyList extends Handler {
	private static Logger logger = com.work.comm.util.LogUtil.getLogger(Processor_522_MTT_ApplyList.class);

	@Override
	public byte[] handleRequest(BaseRequest req) {
		// TODO Auto-generated method stub
		Request request = (Request) req;
		byte[] bytes = req.getBt();	
		JSONObject object = new JSONObject();
		
		int[][] keyArray = {
			{130, I366PickUtil.TYPE_INT_4},	// MTT_ID
			{131, I366PickUtil.TYPE_INT_4},	// GAME_PATH
		};
		
		Map<Integer, Object> map = I366PickUtil.pickAll(bytes, keyArray);
		// 获取客户端发送过来的参数
		int id       = (Integer)map.get(130);
		int gamePath = (Integer)map.get(131);		
    	int status = -1;
    	gamePath = 71;
    	
    	RedisService redisService = RedisService.getRedisService();
    	Map<String, String> gameDetail = redisService.getMTTGame(id);
    	
    	if (redisService.checkMTTExist(gamePath, id)) {
    		// 该ID对应的MTT存在redis中，且打开了"控制带入"功能
    		if (gameDetail.get("isControl").equals("1")) {

    			// 获取该MTT所有的申请参赛玩家列表
        		Set<String> userIdSet = redisService.getApplyPlayerSet(id);
        		ArrayList<JSONObject> userList = new ArrayList<JSONObject>();
        		Iterator<String> it   = userIdSet.iterator();
        		while (it.hasNext()) {
					int playerId = Integer.parseInt(it.next());
					Map<String, String> userDetail = redisService.getMTTPlayerHashMap(id, playerId);
					JSONObject jsonObject = new JSONObject();
					jsonObject.put("nickName", userDetail.get("nickName"));
					jsonObject.put("head", userDetail.get("head"));
					jsonObject.put("behavior", userDetail.get("behavior"));			
					userList.add(jsonObject);
					
					logger.info("apply player id=" + playerId);
				}
        		object.put("playerList", userList);
        		status = 1;
    		} else {
    			// 没有设置控制带入功能
    			status = 0;
    		}
    	} else {
    		status = 2;
    	}
    	Object[][] objs = {
    		{60, status, I366ClientPickUtil.TYPE_INT_1},            	// 0:未打开控制带入,不需审核;1:返回列表成功；2:MTT不存在
    		{130, object.toString(), I366ClientPickUtil.TYPE_STRING_UTF16}	// 
    	};
    	byte[] rBytes = I366PickUtil.packAll(objs, 522);
    	return rBytes;
		
	}

}
