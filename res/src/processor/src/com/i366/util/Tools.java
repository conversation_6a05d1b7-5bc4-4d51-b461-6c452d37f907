/*
  * $RCSfile: Tools.java,v $
 * $Revision: 1.1  $
 * $Date: 2012-8-14  $
 *
 * Copyright (C) 2005 Bettem, Inc. All rights reserved.
 *
 * This software is the proprietary information of Bettem, Inc.
 * Use is subject to license terms.
 */
package com.i366.util;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.URL;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.Logger;
import org.jdom.Attribute;
import org.jdom.DataConversionException;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.JDOMException;
import org.jdom.input.SAXBuilder;
import org.json.JSONObject;

import rath.msnm.util.StringUtil;

import com.i366.data.Data;
import com.i366.model.GameRoom;
import com.i366.model.Level;
import com.i366.model.Rankings;
import com.i366.model.RoomsServer;
import com.i366.model.Tasking;
import com.i366.model.TypeRooms;
import com.i366.net.UnitData;
import com.i366.service.RedisService;
import com.work.client.config.Config;
import com.work.comm.util.PropertiesUtil;
import com.work.db.imp.DZPKDao;
import com.work.db.imp.QudaoByProductDao;

import io.netty.channel.Channel;


/**
 * 工具类
 * <p>Title: Tools</p> 
 * <p>Description: </p> 
 * <p>Copyright: Copyright (c) 2006</p> 
 * <AUTHOR>
 * @version 1.0
 */
public class Tools {

    public static final int SIZE = 1024;
	private static final Logger logger = com.work.comm.util.LogUtil.getLogger(Tools.class);
	/**
	 * 加载服务器资源
	 */
	public static void loadConfig() {
		try {
			loadSqlMap();
		} catch (Exception e) {
			logger.error("update loadConfig  loadSqlMap err", e);
		}
		String xmlpath = "resource/appserver/config/config.xml";
		SAXBuilder builder = new SAXBuilder(false); 
		Document doc = null;
		try {
			doc = builder.build(xmlpath);
		} catch (JDOMException e1) {
			e1.printStackTrace();
		} catch (IOException e1) {
			e1.printStackTrace();
		}
		try {
			loadMinCrray(doc);
		} catch (Exception e) {
			logger.error("update loadConfig  loadMinCrray err", e);
		}
        try {
            loadIPGPSLimitWhite(doc);
        } catch (Exception e) {
            logger.error("update loadConfig  loadIPGPSLimitWhite err", e);
        }
		try {
			loadTaskConfig(doc);
		} catch (Exception e) {
			logger.error("update loadConfig  loadTaskConfig err", e);
		}
		logger.debug("loadConfig ....success");
	}


	/**
	 * 验证比赛房间类型
	 * @param fee
	 * @return
	 */
	public static int checkBisaiType(int fee) {
		int bisai_2 = Data.MIN_CRRAY.get("bisai_2");
		int bisai_3 = Data.MIN_CRRAY.get("bisai_3"); 
		int bisaiType = 1;
		if(fee >= bisai_2) {
			bisaiType = 2;		
		}
		if(fee >= bisai_3) {
			bisaiType = 3;	
		}
		return bisaiType;
	}



	/**
	 * 生成一个图片ID
	 * @return
	 */
	public static String createImageID(int productId, int userId) {
		String time = String.valueOf( System.currentTimeMillis() );
		Random ra = new Random();
		String dom = ra.nextInt(10)+""+ra.nextInt(10)+""+ra.nextInt(10);
		StringBuffer imageId = new StringBuffer();
		imageId.append("0001");
		imageId.append(productId);
		String uId = String.valueOf(userId);
		int i = 0;
		int length = 10 - uId.length();
		while (i < length) {
			uId = 9 + uId;
			i++;
		}
		imageId.append(uId);
		imageId.append(time.substring(0, 10));
		imageId.append(dom);
		return imageId.toString();
	}	
	public static void main(String[] args) {
		System.out.println(new Integer(2).toString());
	}
	/**
	 * 验证是否超时
	 * @param createTime 创建时间
	 * @param timeout 超时毫秒数
	 * @return
	 */
	public static boolean checkTime(long createTime, int timeout) {
		long now = System.currentTimeMillis();
		return (now - createTime) > timeout;
	}
	/**
	 * 5张牌组合字符串转byte[]数组
	 * @param str
	 * @return
	 */
	public static Integer[] getCards(String str) {
		if(str == null || StringUtils.isBlank(str)) {
			return new Integer[]{};
		}
		Integer[] cards = new Integer[5];
		for (int i = 0; i < 5; i++) {
			int begin = i*2;
			int end = begin + 2; 
			String one = str.substring(begin, begin+1);
			if(one.equals("0")) {
				begin++;
			}
			cards[i] = Integer.valueOf(str.substring(begin, end));
		}
		return cards;
	}
	/**
	 * 加载config.xml里面的任务列表配置
	 * @param doc
	 * @return
	 * @throws DataConversionException 
	 */
	public static void loadTaskConfig(Document doc) throws DataConversionException {	
		List<?> list = doc.getRootElement().getChild("TASK_CONFIG" ).getChildren("TASK");
		if(list != null && list.size() > 0) {
			Data.ACTIVITY_CONFIG.clear();
			for (Object object : list) {
				Element element = (Element) object;
				Tasking tasking = new Tasking();
				tasking.setTaskId( element.getAttribute("id").getIntValue() );
				tasking.setType( element.getAttribute("type").getIntValue() );
				tasking.setRewardDown( element.getAttribute("reward_down").getIntValue() );
				tasking.setRewardUp( element.getAttribute("reward_up").getIntValue() );
				tasking.setFinishCount( element.getAttribute("finish_count").getIntValue() );							
				Data.ACTIVITY_CONFIG.put(tasking.getTaskId(), tasking);
			}
		}
	}	

	
    /**
     * 加载config.xml里面的IP、GPS白名单配置
     * @param doc
     * @return
     * @throws DataConversionException 
     */
    public static void loadIPGPSLimitWhite(Document doc) throws DataConversionException {
        List<?> list = doc.getRootElement().getChild("USERID_LIST").getChildren();
        if(list != null && list.size() > 0) {
            Data.USERID_LIST.clear();
            for (Object elemt : list) {
                String userIds = ((Element)elemt).getAttributeValue("user_id");
                logger.info("userIds ================" + userIds);
                String[] userIdArray = userIds.split(",");
                for(int i = 0; i < userIdArray.length; i++) {
                    Data.USERID_LIST.add(userIdArray[i]);
                }
            }
        }
    }
    
	/**
	 * 加载config.xml里面的最少筹码携带比例数据
	 * @param doc
	 * @return
	 * @throws DataConversionException 
	 */
	public static void loadMinCrray(Document doc) throws DataConversionException {
		Element sql = doc.getRootElement().getChild("MIN_CRRAY_CHIP");
		List<?> list = sql.getChildren();
		if(list != null && list.size() > 0) {
			Data.MIN_CRRAY.clear();
			for (Object elemt : list) {
				int value = ((Element)elemt).getAttribute("value").getIntValue();	
				Data.MIN_CRRAY.put( ((Element)elemt).getName(), value);
			}
		}
	}	
	/**
	 * 加载SQL.xml里面的sql语句集合
	 * @param
	 * @return
	 */
	public static void loadSqlMap() {
		String xmlpath = "resource/appserver/sql/SQL.xml";
		SAXBuilder builder = new SAXBuilder(false);
		try {
			Document doc = builder.build(xmlpath);
			List<?> list = doc.getRootElement().getChild("sql").getChildren();
			if(list != null && list.size() > 0) {
				Data.SQL_MAP.clear();
				for (Object elemt : list) {	
					Element element = (Element) elemt;
					Data.SQL_MAP.put( element.getName(), element.getAttributeValue("sql") );
				}
			}
		} catch (JDOMException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	
	/**
	 * 读取URL内容到BUFFER
	 */
	public static byte[] getURLContent(String urlString) {
	    HttpURLConnection httpURLConn = null;
        try {
            URL url = new URL(urlString);
            httpURLConn = (HttpURLConnection)url.openConnection();
            httpURLConn.setIfModifiedSince(999999999);
            httpURLConn.setConnectTimeout(10 * 1000);
            httpURLConn.connect();
            InputStream in = httpURLConn.getInputStream();
            ByteArrayOutputStream buffer = new ByteArrayOutputStream(Tools.SIZE);
            int read = 0;
            // 读取数据
            byte[] tmpBytes = new byte[Tools.SIZE];
            while(read != -1) {
            	buffer.write(tmpBytes, 0, read);
                read = in.read(tmpBytes, 0, Tools.SIZE);
            }
            in.close();
            return buffer.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (httpURLConn != null) {
                httpURLConn.disconnect();
            }
        }
        return new byte[0];
	}
	
	public static boolean sendDataToFileServer(String fileName, byte[] content) {
	    Socket socket = new Socket();
        InputStream inputStream;
        OutputStream outputStream;
        try {
            if(fileName.equals("") || content.length == 0){
                return false;
            }

            socket.connect(
                    new InetSocketAddress(Data.PRO.getProperty("imageServer.ip"),
                            Integer.parseInt(Data.PRO.getProperty("imageServer.port"))),
                    10000);

            // 文件长度
            int len = content.length;

            // 文件名
            logger.info("uphead: filename=" + fileName+ "len==" + fileName.length());

            // 文件名长度
            byte[] lenname = fileName.getBytes();

            // 文件头
            byte[] title = new byte[14];

            String filetitle = "FILE";
            byte[] ti = filetitle.getBytes("UTF-8");
            for (int i = 0; i < ti.length; i++) {
                title[i] = ti[i];
            }
            // 协议总长度
            int lz = len + 14 + 8 + lenname.length;
            byte[] l = UnitData.intToByte(lz);
            for (int i = 0; i < l.length; i++) {
                title[4 + i] = l[i];
            }
            // 文件头长度
            byte[] ll = UnitData.intToByte(14);
            for (int i = 0; i < ll.length; i++) {
                title[8 + i] = ll[i];
            }
            // 请求ID号
            short s = 42;
            byte[] id = UnitData.shortToBytes(s);
            for (int i = 0; i < id.length; i++) {
                title[12 + i] = id[i];
            }

            String titlelog = "";
            for (byte b:title) {
            	titlelog += String.valueOf(b);
            }
            logger.debug(titlelog);
            // 发送流
            outputStream = socket.getOutputStream();
            outputStream.write(title);// 发送文件头

            outputStream.write(UnitData.intToByte(lenname.length));// 文件名长度
            outputStream.write(lenname);// 文件名写入
            outputStream.write(UnitData.intToByte(len));// 文件长度

            outputStream.flush();

            // 传输实际数据
            outputStream.write(content); // 文件内容
            outputStream.flush();

            // 接收消息
            inputStream = socket.getInputStream();
            if(inputStream==null){
                logger.error("uphead: inputStream is null");
            }
            byte[] b = new byte[14];
            int offset = 0;
            int len1 = 14;
            while (offset < len1) {
                int length = 0;
                length = inputStream.read(b,offset,len1-offset);
                if (length == -1) {
                    logger.error("uphead: return false 135");
                    return false;
                }
                offset += length;
            }
            int size = (int)(b[7]&0xff)+(int)((b[6]&0xff)<<8)+(int)((b[5]&0xff)<<16)+(int)((b[4]&0xff)<<24);
            int size1 = size-14;
            byte[] b1 = new byte[size1];
            offset = 0;
            while (offset < size1) {
                int length = 0;
                length = inputStream.read(b1,offset,size1-offset);
                offset += length;
            }
            socket.close();
            int x = (int)(b[11]&0xff)+(int)((b[10]&0xff)<<8)+(int)((b[9]&0xff)<<16)+(int)((b[8]&0xff)<<24);
            if(b.length > 13)
            {
                int flag = (b1[x-14] & 0xff);
                if (flag != 1) {
                    return false;
                } else {
                    return true;
                }
            }else {
                return false;
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (!socket.isClosed()) {
                try {
                    socket.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return false;
	}
	
	public static String getIPByChannel(Channel channel) {
        try {
            InetSocketAddress socketAddress = (InetSocketAddress) channel.remoteAddress();
            InetAddress inetAddress = (InetAddress) socketAddress.getAddress();
            return inetAddress.getHostAddress();
        } catch (Exception e) {
            return "";
        }
    }
	
	 /**
     * http get, return json
     * @param urlstr
     * @return
     */
    public static JSONObject getJsonData(String urlstr) {
        logger.debug("request url: " + urlstr);
        HttpURLConnection httpsURLConn = null;
        JSONObject inJson = null;
        try {
            URL url = new URL(urlstr);
            httpsURLConn = (HttpURLConnection) url.openConnection();
            // urlConn.setDoOutput(true);
            // urlConn.setRequestMethod("GET");
            httpsURLConn.setIfModifiedSince(999999999);
            httpsURLConn.setConnectTimeout(10 * 1000);
            httpsURLConn.setRequestProperty("Accept-Charset", "utf-8");
            httpsURLConn.connect();
            InputStream in = httpsURLConn.getInputStream();
            InputStreamReader inputStreamReader = null;
            BufferedReader bufferedReader = null;
            
            inputStreamReader = new InputStreamReader(in, "UTF-8");
            bufferedReader = new BufferedReader(inputStreamReader);
            StringBuffer sb = new StringBuffer();
            String str = null;
            while ((str = bufferedReader.readLine()) != null) {
                sb.append(str);
            }
            logger.debug("result string: " + sb.toString());
            // 根据得到的序列化对象 构建JSON对象
            inJson = new JSONObject(sb.toString());
        } catch (Exception e) {
            logger.error(e);
        } finally {
            if (httpsURLConn != null) {
                httpsURLConn.disconnect();
            }
        }
        return inJson;
    }

	/**
	 * 计算两个经纬度之间的距离
	 * http://tech.meituan.com/lucene-distance.html
	 * @param lat1 纬度值1
	 * @param lng1 经度值1
	 * @param lat2 纬度值2
	 * @param lng2 经度值2
	 * @return
	 */
	public static double distanceSimplify(double lat1, double lng1, double lat2, double lng2) {
		double dx = lng1 - lng2; // 经度差值
		double dy = lat1 - lat2; // 纬度差值
		double b = (lat1 + lat2) / 2.0; // 平均纬度
		double Lx = toRadians(dx) * 6367000.0 * Math.cos(toRadians(b)); // 东西距离
		double Ly = 6367000.0 * toRadians(dy); // 南北距离
		return Math.sqrt(Lx * Lx + Ly * Ly);  // 用平面的矩形对角距离公式计算总距离
	}

	private static double toRadians(double d) {
		return d * Math.PI / 180.0;
	}
}
