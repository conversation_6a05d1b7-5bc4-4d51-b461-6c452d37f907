/*
 * $RCSfile: Data.java,v $
 * $Revision: 1.1  $
 * $Date: 2012-8-14  $
 *
 * Copyright (C) 2005 Bettem, Inc. All rights reserved.
 *
 * This software is the proprietary information of Bettem, Inc.
 * Use is subject to license terms.
 */
package com.i366.data;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.logging.log4j.Logger;

import com.i366.model.Level;
import com.i366.model.LoginKey;
import com.i366.model.Rankings;
import com.i366.model.RoomsServer;
import com.i366.model.ShareSMS;
import com.i366.model.Tasking;
import com.i366.processor.client.Processor_2_Check;
import com.work.comm.protocal.Request;
import com.work.comm.util.PropertiesUtil;
import com.work.db.model.ProductGroupConfig;
import com.work.db.model.QudaoByProduct;
import com.work.engine.socket.Client;

/**
 * 服务器数据存放类
 * <p>
 * Title: Data
 * </p>
 * <p>
 * Description:
 * </p>
 * <p>
 * Copyright: Copyright (c) 2006
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class Data {
	private static Logger logger = com.work.comm.util.LogUtil.getLogger(Data.class);

	/**
	 * 此类不允许实例化
	 * 
	 * @throws Exception
	 */
	private Data() throws Exception {
		throw new Exception();
	}

	/**
	 * 德州扑克游戏在线人数
	 */
	public static int USER_COUNT = 0;
	/**
	 * 当前服务器在线用户集合
	 */
	public static final Map<Integer, Request> onlineMap = new ConcurrentHashMap<Integer, Request>(10000);
	/**
	 * 游戏房间服务器集合
	 */
	public static final Map<Integer, RoomsServer> RSMap = new HashMap<Integer, RoomsServer>();
	/**
	 * 用户登录key集合
	 */
	public static final Map<Integer, LoginKey> keyMap = new HashMap<Integer, LoginKey>();
	/**
	 * 端口IP属性
	 */
	public static Properties PRO = PropertiesUtil.getSingleton().initProperties("port.properties");
	/**
	 * SQL语句集合
	 */
	public static final Map<String, String> SQL_MAP = new HashMap<String, String>(50);
	/**
	 * 最少携带筹码比例配置
	 */
	public static final Map<String, Integer> MIN_CRRAY = new HashMap<String, Integer>();

    /**
     * IP、GPS限制白名单配置
     */
    public static final List<String> USERID_LIST = new ArrayList<String>();
	/**
	 * 活动/任务配置文件
	 */
	public static final Map<Integer, Tasking> ACTIVITY_CONFIG = new HashMap<Integer, Tasking>();


	public synchronized static boolean addUser(int userId, Request request) {
		try {
			Request older = Data.onlineMap.get(userId);
			if (older != null && older.getChannel() != null)
				older.getChannel().close();
			// 添加一位用户到在线人数列表
			request.setUpdateTime(System.currentTimeMillis());
			Data.onlineMap.put(userId, request);
			return true;
		} catch (Exception e) {
			logger.error("添加用户失败：" + e.toString());
			return false;
		}
	}

	public synchronized static void printOnlineUser(){
		Set keys = Data.onlineMap.keySet();
		StringBuilder onlineUser = new StringBuilder("Current online-user : ");
		boolean first = true;
		for(Object key : keys) {
			if(!first)
				onlineUser.append(" , ");
			onlineUser.append("["+key.toString()+"] : ["+Data.onlineMap.get(key)+"]");
		}
		logger.info(onlineUser.toString());
	}

	/**
	 * 重新加载配置文件
	 */
	public static Properties reloadProperties(){
		PRO = PropertiesUtil.getSingleton().initProperties("port.properties");
		return PRO;
	}
}
