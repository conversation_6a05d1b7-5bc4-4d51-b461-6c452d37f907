package com.work.engine.server.handler;


import com.work.engine.socket.cmwap.CmwapSocketWriteHandler;
import com.work.engine.socket.Client;
import com.work.engine.server.ObjectFactory;
import com.work.engine.service.Service;
import com.work.engine.protocal.Response;
import com.work.engine.protocal.request.Request;
import org.apache.commons.pool.ObjectPool;
import org.apache.logging.log4j.Logger;
import java.io.IOException;


/**
 * Created by IntelliJ IDEA.
 * User: Administrator
 * To change this template use File | Settings | File Templates.
 */
public class CmwapGameRequestHandler implements RequestHandler {

    private final static Logger logger = com.work.comm.util.LogUtil.getLogger(CmwapGameRequestHandler.class);

    protected final static HandlerFactory handlerCacheFactory = HandlerFactory.getInstance();

    protected CmwapSocketWriteHandler socketWriteHandler;
    protected ObjectFactory facotry;
     protected Request gameReq;

    protected ObjectPool objPool = null;
    private Service service;

    public Service getService() {
		return service;
	}

	public void setService(Service service) {
		this.service = service;
	}

    public CmwapGameRequestHandler(Service service) {
    	this.service = service;
    }

    public void init(Object req) {
    	
        this.gameReq = (Request)req;

        this.socketWriteHandler = new CmwapSocketWriteHandler();
        // this.facotry = service.getFactory();
    }

    public void run() {

        int requestCode = gameReq.getRequestCode();
//        int subRequestCode = gameReq.getSubRequestCode();
        logger.info("\r\ngameReq=" + gameReq);
        Handler handler = null;

        try {

            handler = getHandler(requestCode);

                handleGameRequest(handler);



        } catch (Exception ex) {
            logger.error("error", ex);
        } finally {
            returnHandler(handler, objPool);

            gameReq = null;
        }


    }



    private void handleGameRequest(Handler handler) {

        byte[] outInf = handler.handleRequest();



        writeResponse(outInf, gameReq.getClient());
    }

    private void returnHandler(Handler handler, ObjectPool obj) {
        try {
            if (handler != null) {
                obj.returnObject(handler);
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    private Handler getHandler(int requestCode) throws Exception {
        try {
            objPool = (ObjectPool) handlerCacheFactory.get(new Integer(requestCode));

            Handler handler = (Handler) objPool.borrowObject();
            if (handler == null)
                throw new Exception("handler pool is null for requestCode=" + requestCode);

            handler.init(gameReq);
            return handler;
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            throw ex;
        }
    }

    private void writeResponse(byte[] outInf, Client client) {
        Response response = facotry.getResponse();
        response.init(client, outInf);
        try {
            socketWriteHandler.write(response);
        } catch (IOException ex) {
            //本非致命错误
            logger.debug(ex);
        }
    }





}
