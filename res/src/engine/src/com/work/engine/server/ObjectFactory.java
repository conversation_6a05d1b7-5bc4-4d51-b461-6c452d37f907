package com.work.engine.server;

import com.work.engine.protocal.Response;
import com.work.engine.protocal.request.Request;
import com.work.engine.protocal.request.ServiceRequest;
import com.work.engine.socket.Client;
import com.work.engine.server.handler.EventHandler;
import com.work.engine.server.handler.RequestHandler;

/**
 * Created by IntelliJ IDEA.
 * User: Administrator
 * To change this template use File | Settings | File Templates.
 */
public interface ObjectFactory {

    public RequestHandler getRequestHandler();

    public RequestHandler getCmwapRequestHandler();

    public EventHandler getEventHandler();

    public Client getClient();

    public Request getRequest();

    public Response getResponse();
    
    public ServiceRequest getServiceRequest();
    
    RequestHandler getServiceRequestHandler();
}
