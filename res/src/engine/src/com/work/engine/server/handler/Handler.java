package com.work.engine.server.handler;

import org.apache.commons.pool.BasePoolableObjectFactory;
import org.apache.logging.log4j.Logger;


/**
 * Created by IntelliJ IDEA.
 * User: Administrator
 * To change this template use File | Settings | File Templates.
 */
public abstract class Handler extends BasePoolableObjectFactory {
    private Logger logger = com.work.comm.util.LogUtil.getLogger(Handler.class);

    protected Object gameReq = null;


    /**
     * 入口 参数
     * @param gameReq
     */
    public void init(Object gameReq) {
        logger.debug("init");
        this.gameReq = gameReq;

    }

    public Object getGameRequest() {
        return gameReq;
    }

    public abstract byte[] handleRequest();


    protected int getImages(int imageID) {
        return imageID;

    }

    /**
     * active object pool
     * @return
     * @throws Exception
     */
    public abstract Object makeObject() throws Exception;

    /**
     * Uninitialize an instance to be returned to the pool.
     * @param obj the instance to be passivated
     */
    public void passivateObject(Object obj) throws Exception {
        Handler handler = (Handler) obj;
        handler.clear();
    }

    private void clear() {
        gameReq = null;
    }

}
