package com.work.engine.server.handler;

import org.apache.logging.log4j.Logger;
import org.apache.commons.pool.ObjectPool;
import java.io.IOException;
import java.nio.channels.SocketChannel;

import com.i366.data.Data;
import com.work.engine.protocal.*;
import com.work.engine.protocal.request.Request;
import com.work.engine.socket.Client;
import com.work.engine.socket.write.SocketWriteHandler;
import com.work.engine.server.ObjectFactory;
import com.work.engine.service.Service;



/**
 * Created by IntelliJ IDEA.
 * User: Administrator
 * To change this template use File | Settings | File Templates.
 */
public class GameRequestHandler implements RequestHandler {

    private final static Logger logger = com.work.comm.util.LogUtil.getLogger(GameRequestHandler.class);

    protected final static HandlerFactory handlerCacheFactory = HandlerFactory.getInstance();

    public final static boolean showSmsPage = false;

    protected Request gameReq;
    protected SocketWriteHandler socketWriteHandler;
    protected ObjectFactory facotry;

    protected ObjectPool objPool = null;
    private Service service;

    public Service getService() {
		return service;
	}

	public void setService(Service service) {
		this.service = service;
	}

	public GameRequestHandler(Service service) {
    	this.service = service;
    }

    public void init(Object req) {
        this.gameReq = (Request)req;
        this.socketWriteHandler = new SocketWriteHandler();
        // this.facotry = service.getFactory();
    }

    public void init(Request req, byte[] bt) {
        this.gameReq = req;
        this.socketWriteHandler = new SocketWriteHandler();
        // this.facotry = service.getFactory();
    }

    public void run() {
        int requestCode = gameReq.getRequestCode();
        logger.debug("\r\ngameReq=" + gameReq + ", requestCode: " + requestCode);

        Handler handler = null;
        try {
	        handler = getHandler(requestCode);
	        handleGameRequest(handler);

        } catch (Exception ex) {
            logger.error("error requestId = " + requestCode, ex);
        } finally {
            returnHandler(handler, objPool);
            gameReq = null;
        }
    }

    /**
     * 访问App Server(Game)
     */
    private void handleGameRequest(Handler handler) {
    	if(handler != null) {
    		byte[] outInf = handler.handleRequest();
    		writeResponse(outInf, gameReq.getClient());
    	}
    }


    private Handler getHandler(int requestCode) throws Exception {
        try {
        	Handler handler = null;
        	// 用户已登录/用户请求登录
        	//||( requestCode == 107 ||requestCode==108||requestCode==109) || requestCode==110
        	if (requestCode == 2 || Data.onlineMap.containsKey(gameReq.getUserId())) {
        	// if(handlerCacheFactory.get(requestCode) != null) {
        		
        		objPool = (ObjectPool) handlerCacheFactory.get(requestCode);

                handler = (Handler) objPool.borrowObject();
                if (handler == null) {
                    logger.error("handler pool is null for requestCode=" + requestCode);
                    throw new Exception("handler pool is null for requestCode=" + requestCode);
                }
                handler.init(gameReq);
    		}
        	// 用户没有登录（非法请求）
        	else {
    			logger.error("Illegal request・・・");
    			throw new Exception("requestCode=" + requestCode + " not ...");
    		}
        	return handler;
        } catch (Exception ex) {
            logger.error("handler pool is null for requestCode=" + requestCode);
            logger.error(ex.getMessage(), ex);
            throw ex;
        }
        
    }

    private void returnHandler(Handler handler, ObjectPool obj) {
        try {
            if (handler != null) {
                obj.returnObject(handler);
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    private void writeResponse(byte[] outInf, Client client) {
        Response response = facotry.getResponse();
        response.init(client, outInf);
        try {
            socketWriteHandler.write(response);
        } catch (IOException ex) {
            SocketChannel sc = client.getSocket().getChannel();
            try {
                sc.keyFor(client.getReaderSelector()).cancel();
                sc.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            logger.debug(ex);
        }
    }

}
