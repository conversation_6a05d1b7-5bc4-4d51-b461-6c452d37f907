package com.work.db.imp;

import org.apache.logging.log4j.Logger;
import com.i366.model.BroadcastModel;
import com.work.db.conn.ConnDB;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 * User: ThinkPad
 * Date: 2010-11-30
 * Time: 8:46:43
 * To change this template use File | Settings | File Templates.
 */
public class BroadcastDao extends ConnDB{

    private static Logger logger = com.work.comm.util.LogUtil.getLogger(BroadcastDao.class);
    
    public BroadcastDao() {
        super("proxool.dzpk");
        //this.createConnection();
    }
    
    /**
     * 获取全局广播
     * @param sendType
     * @return
     */
    private static String selectVaildBroadcastSql = "select id,broadcastContent,broadcastCount," +
            "broadcastTime,broadcastTribe,roomPaths,sendType," +
            "broadcastClub from broadcast " +
            "where sendType in(?,?,?) and finishFlag = 0 " +
            "order by broadcastTime";
    public List<BroadcastModel> selectVaildBroadcast(int sendType1, int sendType2, int sendType3) {
        List<BroadcastModel> result = new ArrayList<>();

        Connection conn = null;
        PreparedStatement preStmt = null;
        ResultSet rs = null;
        try {
            conn = this.createConnection();
        	preStmt = this.createStam(conn,selectVaildBroadcastSql, new Object[]{sendType1,sendType2,sendType3});
            rs = preStmt.executeQuery();
            while (rs.next()) {
                BroadcastModel data = new BroadcastModel();
                data.setId(rs.getInt("id"));
                data.setBroadcastContent(rs.getString("broadcastContent"));
                data.setBroadcastCount(rs.getInt("broadcastCount"));
                data.setBroadcastTime(rs.getInt("broadcastTime"));
                data.setBroadcastTribe(rs.getInt("broadcastTribe"));
                data.setRoomPaths(rs.getString("roomPaths"));
                data.setSendType(rs.getInt("sendType"));
                data.setBroadcastClub(rs.getInt("broadcastClub"));
                result.add(data);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            this.clear(rs,preStmt,conn);
        }

        return result;
    }

    private static String queryGameVaildBroadcastSql = "select id,broadcastContent,broadcastCount," +
            "broadcastTime,broadcastTribe,roomPath,sendType,roomId," +
            "broadcastClub from broadcast " +
            "where sendType in(?,?) and finishFlag = 0 " +
            "order by broadcastTime";
    public List<BroadcastModel> queryGameVaildBroadcast(int sendType1,int sendType2) {
        List<BroadcastModel> result = new ArrayList<>();

        Connection conn = null;
        PreparedStatement preStmt = null;
        ResultSet rs = null;
        try {
            conn = this.createConnection();
            preStmt = this.createStam(conn,queryGameVaildBroadcastSql, new Object[]{sendType1,sendType2});
            rs = preStmt.executeQuery();
            while (rs.next()) {
                BroadcastModel data = new BroadcastModel();
                data.setId(rs.getInt("id"));
                data.setBroadcastContent(rs.getString("broadcastContent"));
                data.setBroadcastCount(rs.getInt("broadcastCount"));
                data.setBroadcastTime(rs.getInt("broadcastTime"));
                data.setBroadcastTribe(rs.getInt("broadcastTribe"));
                data.setRoomId(rs.getInt("roomId"));
                data.setRoomPath(rs.getInt("roomPath"));
                data.setSendType(rs.getInt("sendType"));
                data.setBroadcastClub(rs.getInt("broadcastClub"));
                result.add(data);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            this.clear(rs,preStmt,conn);
        }

        return result;
    }
    
    
    /**
     * 获取某个同盟在房间里的所有用户
     * @param tribe_id
     * @return
     */
    private static String getTribeRoomUserIdsSql = "select group_concat(a.user_id) as tribeRoomUserIds " +
                                "from club_members a,club_record b where a.club_id = b.id and a.club_id in " +
                                 "(select d.club_id from tribe_record c,tribe_members d where c.id = d.tribe_id and c.random_id = ?)";
    public String getTribeRoomUserIds(int tribe_id) {
        String result = "";

        Connection conn = null;
        PreparedStatement preStmt = null;
        ResultSet rs = null;
        try {
            conn = this.createConnection();
            preStmt = this.createStam(conn,getTribeRoomUserIdsSql, new Object[]{tribe_id});
            rs = preStmt.executeQuery();
            if (rs.next()) {
            	result = rs.getString("tribeRoomUserIds");
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            this.clear(rs,preStmt,conn);
        }

        return result;
    }
    
    /**
     * 获取某个社区在房间里的所有用户
     * @param tribe_id
     * @return
     */
    private static  String getClubRoomUserIdsSql = "select group_concat(a.user_id) as clubRoomUserIds " +
            "from club_members a,club_record b " +
            "where a.club_id = b.id and b.random_id = ?";
    public String getClubRoomUserIds(int random_id) {
        String result = "";

        Connection conn = null;
        PreparedStatement preStmt = null;
        ResultSet rs = null;
        try {
            conn = this.createConnection();
            preStmt = this.createStam(conn,getClubRoomUserIdsSql, new Object[]{random_id});
            rs = preStmt.executeQuery();
            if (rs.next()) {
            	result = rs.getString("clubRoomUserIds");
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            this.clear(rs,preStmt,conn);
        }

        return result;
    }
    
    /**
     * 更改广播的状态
     * @param id
     * @return
     */
    private static String updateRowSql = "update broadcast set finishFlag = 1 where id = ?";
    public boolean updateRow(int id) {
        if (id == 0) {
            logger.error("id is not anvaildable!!");
            return false;
        }

        Connection conn = null;
        PreparedStatement preStmt = null;
        try {
            conn = this.createConnection();
            preStmt = this.createStam(conn,updateRowSql, new Object[]{id});
            preStmt.execute();
            return true;
        } catch (Exception se) {
            logger.error(se.getMessage(), se);
            return false;
        } finally {
            this.clear(null,preStmt,conn);
        }
    }
}
