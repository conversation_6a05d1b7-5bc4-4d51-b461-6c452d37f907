/*
 * $RCSfile: QudaoByProductDao.java,v $
 * $Revision: 1.1  $
 * $Date: 2013-3-19  $
 *
 * Copyright (C) 2005 Bettem, Inc. All rights reserved.
 *
 * This software is the proprietary information of Bettem, Inc.
 * Use is subject to license terms.
 */
package com.work.db.imp;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.i366.data.Data;
import com.work.db.model.QudaoByProduct;

/**
 * <p>Title: QudaoByProductDao</p> 
 * <p>Description: </p> 
 * <p>Copyright: Copyright (c) 2006</p> 
 * <AUTHOR>
 * @version 1.0
 */
public class QudaoByProductDao extends DZPKDao {
	
	/**
	 * 查询渠道对应的产品过滤列表
	 * @return
	 */
	public Map<Integer, QudaoByProduct> getQudaoByProducts() {
		Map<Integer, QudaoByProduct> QUDAO_BY_PRODUCTS = new HashMap<>();
		String sql = Data.SQL_MAP.get("find_qudao_by_product");

		Connection conn = null;
		PreparedStatement preStmt = null;
		ResultSet rs = null;
		try {
			conn = this.createConnection();
			preStmt = this.createStam(conn,sql, null);

			rs = preStmt.executeQuery();
			QudaoByProduct qudaoByProduct = null;
			while(rs.next()) {
				qudaoByProduct = new QudaoByProduct();
				qudaoByProduct.setQudao(rs.getInt("QUDAO_CODE"));
				String LEACH_GROUP_CODE = rs.getString("LEACH_GROUP_CODE");
				if (StringUtils.isBlank(LEACH_GROUP_CODE)) {
					continue;
				}
				for (String s : LEACH_GROUP_CODE.split("@")) {
					qudaoByProduct.getGroupCodes().add(s);
				}
				QUDAO_BY_PRODUCTS.put(qudaoByProduct.getQudao(), qudaoByProduct);
			}
		}catch (SQLException e) {
			logger.error("查询渠道对应的产品过滤列表失败：" + e.getMessage(), e);
		}finally {
			this.clear(rs,preStmt,conn);
		}

		return QUDAO_BY_PRODUCTS;
	}
}

