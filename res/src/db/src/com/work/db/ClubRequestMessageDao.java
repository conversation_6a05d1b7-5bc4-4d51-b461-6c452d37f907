package com.work.db;

import com.work.db.model.ClubRequestMessage;

import java.util.List;

public interface ClubRequestMessageDao {

    int CLUB_MSG   = 0;
    int FRIEND_MSG = 1;
    int TRIBE_MSG = 2;

    /**
     * 插入一条消息
     * @param clubRequestMessage
     */
    void insert(ClubRequestMessage clubRequestMessage);

    /**
     * 根据sender_id,club_id,type查询消息
     */

    List<ClubRequestMessage> selectBySenderIdAndClubIdAndType(String sender_id, int clubId, int type);

    /**
     *
     */

    void updateRemarkById(int id, String remark);

    /**
     * 插入一条用户消息关系
     * @param msgId
     * @param userId
     * @return
     */
    int insertUMRelation(int msgId, int userId);

    // 新建消息，重复加1
    int upsertUnreadMsgInfo(int sender, int receiver, int clubId, int msgType, int count,
                            int type);

}
