/*
 * $RCSfile: QudaoByProduct.java,v $
 * $Revision: 1.1  $
 * $Date: 2013-3-19  $
 *
 * Copyright (C) 2005 Bettem, Inc. All rights reserved.
 *
 * This software is the proprietary information of Bettem, Inc.
 * Use is subject to license terms.
 */
package com.work.db.model;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title: QudaoByProduct</p> 
 * <p>Description: </p> 
 * <p>Copyright: Copyright (c) 2006</p> 
 * <AUTHOR>
 * @version 1.0
 */
public class QudaoByProduct {
	private int qudao;
	private List<String> groupCodes = new ArrayList<String>();
	public int getQudao() {
		return qudao;
	}
	public void setQudao(int qudao) {
		this.qudao = qudao;
	}
	public List<String> getGroupCodes() {
		return groupCodes;
	}
	public void setGroupCodes(List<String> groupCodes) {
		this.groupCodes = groupCodes;
	}
	
	
}

