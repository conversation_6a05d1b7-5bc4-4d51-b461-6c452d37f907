package com.work.comm.endpoints.res;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.recipes.cache.*;
import org.apache.zookeeper.CreateMode;
import org.apache.zookeeper.data.Stat;

import java.nio.charset.Charset;
import java.util.concurrent.Callable;

/**
 * Res服务节点管理
 * 对外访问的IP/PORT  ：一般很少变
 * 对内访问的IP/PORT  ：一般很少变
 * 当前分配的玩家数量  : 初始化时从数据库表中统计设置到zk
 *   分配时  : +1
 *   踢出时  ：-1
 * 当前APP到Res服务的连接数量: 初始化时为0
 *   建立连接时 ： +1
 *   关闭连接时 ： -1
 */
@Slf4j
public class ZkWatchService {
    /**
     * 所有Res服务在一个类型标识的节点下，对应的值为空
     * - %s Res服务标识符的占位符
     * 每个Res服务对应一个节点，对应的值为空
     * - %s Res服务ID的占位符
     * 其下的结构是：
     * - /out    : 对应的值是外部访问的ip:port
     * - /in     : 对应的值是内部访问的ip:port
     * - /pu     : 对应的值是当前Res服务的已分配玩家数量
     * - /cu     : 对应的值是当前Res服务的已建立的连接数量
     */
    private static final String SERVER_TYPE_PATH = "/dzpk/roomsvr/%s";
    private static final String SERVER_PU_FLAG = "pu";
    private static final String SERVER_CU_FLAG = "cu";
    private static final String SERVER_OUTACS_FLAG = "out";
    private static final String SERVER_INACS_FLAG = "in";

    /** zookeeper客户端 */
    private CuratorFramework curatorFramework;

    /** 字符集 */
    private Charset defaultCharset;

    /** 对应的Res服务类型 */
    private String serverType;
    /** 本服务对应的ID */
    private String serverId;
    /** 当前服务的访问IP */
    private String outAccessIp;
    /** 当前服务的访问端口 */
    private int outAccessPort;
    /** 当前服务的访问IP */
    private String inAccessIp;
    /** 当前服务的访问端口 */
    private int inAccessPort;

    /** 本服务的节点路径 */
    private String typePath=null;     // 标识Res服务的节点路径
    private String servicePath=null;  // 本Res服务的节点路径
    private String outAcsPath=null;   // 外部访问的ip:port节点
    private String inAcsPath=null;    // 内部访问的ip:port节点
    private String puPath=null;       // 已分配玩家数量节点
    private String cuPath=null;       // 已建立连接的连接数量节点
    private TreeCache typePathCache=null; // 所有Res服务的根节点，监控此节点

    /** 查询已分配玩家数量 */
    private IUserResServerDao roomDao;

    public ZkWatchService(String serverType,String serverId,
                          String outAccessIp,int outAccessPort,
                          String inAccessIp,int inAccessPort,
                          Charset defaultCharset,
                          CuratorFramework curatorFramework,
                          IUserResServerDao roomDao){
        this.serverType = serverType;
        this.serverId = serverId;
        this.outAccessIp = outAccessIp;
        this.outAccessPort = outAccessPort;
        this.inAccessIp = inAccessIp;
        this.inAccessPort = inAccessPort;
        this.curatorFramework = curatorFramework;
        this.defaultCharset = null == defaultCharset? Charset.forName("UTF-8"):defaultCharset;
        this.roomDao = roomDao;

        this.initialize();
    }

    /**
     * 创建本Res服务节点
     * - /%sout    : %s则是serverId的占位符，对应的值是外部访问的ip:port
     * - /%sin     : %s则是serverId的占位符，对应的值是内部访问的ip:port
     * - /%spu     : 当前Res服务的已分配玩家数量
     * - /%scu     : 当前Res服务的已建立的连接数量
     * @return
     */
    public void createNodeIfNeed(){
        try{
            // 本服务根节点
            this.delServiceNode(true);
            this.curatorFramework.create()
                    .creatingParentsIfNeeded()
                    .forPath(this.servicePath);

            // Res服务节点的外部访问IP/PORT节点
            // 临时应用节点（session失效就自动删除）
            // 节点值是对外访问的IP:PORT
            String ipPort = this.outAccessIp+":"+this.outAccessPort;
            byte[] ipPortArr = this.serializeString(ipPort);
            Stat nodeStat = this.curatorFramework.checkExists().forPath(this.outAcsPath);
            if(nodeStat != null){
                this.curatorFramework.delete()
                        .forPath(this.outAcsPath);
            }
            this.curatorFramework.create()
                    .withMode(CreateMode.EPHEMERAL)
                    .forPath(this.outAcsPath,ipPortArr);

            // Res服务节点的内部访问IP/PORT节点
            // 临时应用节点（session失效就自动删除）
            // 节点值是对内部访问的IP:PORT
            ipPort = this.inAccessIp+":"+this.inAccessPort;
            ipPortArr = this.serializeString(ipPort);
            nodeStat = this.curatorFramework.checkExists().forPath(this.inAcsPath);
            if(nodeStat != null){
                this.curatorFramework.delete()
                        .forPath(this.inAcsPath);
            }
            this.curatorFramework.create()
                    .withMode(CreateMode.EPHEMERAL)
                    .forPath(this.inAcsPath,ipPortArr);

            // Res服务节点的已建立连接数节点
            // 临时应用节点（session失效就自动删除）
            // 节点值是已建立连接数
            nodeStat = this.curatorFramework.checkExists().forPath(this.cuPath);
            if(nodeStat != null){
                this.curatorFramework.delete()
                        .forPath(this.cuPath);
            }
            this.curatorFramework.create()
                    .withMode(CreateMode.EPHEMERAL)
                    .forPath(this.cuPath,this.serializeInt(0));

            // Res服务节点的已分配玩家数节点
            // 临时应用节点（session失效就自动删除）
            // 节点值是已分配玩家数
            nodeStat = this.curatorFramework.checkExists().forPath(this.puPath);
            if(nodeStat != null){
                this.curatorFramework.delete()
                        .forPath(this.puPath);
            }
            int playerNum = this.roomDao.getNumOfPlayer(this.serverId);
            this.curatorFramework.create()
                    .withMode(CreateMode.EPHEMERAL)
                    .forPath(this.puPath,this.serializeInt(playerNum));
        }catch (Exception ex){
            throw new RuntimeException("创建&初始化Res服务节点失败",ex);
        }
    }

    public void initialize() {
        StringBuilder traceLog = null;
        if(log.isDebugEnabled()){
            traceLog = new StringBuilder(String.format("初始化Res服务ZK节点：type=%s,id=%s,outIp=%s,outPort=%s,inIp=%s,inPort=%s",
                    this.serverType,this.serverId,this.outAccessIp,this.outAccessPort,this.inAccessIp,this.inAccessPort));
        }
        Exception throwEx = null;
        try {
            // 初始化节点路径
            this.typePath = String.format(SERVER_TYPE_PATH, this.serverType);
            this.servicePath = String.format("%s/%s", this.typePath,this.serverId);
            this.outAcsPath = String.format("%s/%s", this.servicePath,SERVER_OUTACS_FLAG);
            this.inAcsPath = String.format("%s/%s", this.servicePath,SERVER_INACS_FLAG);
            this.puPath = String.format("%s/%s", this.servicePath,SERVER_PU_FLAG);
            this.cuPath = String.format("%s/%s", this.servicePath,SERVER_CU_FLAG);
            if(null != traceLog){
                traceLog.append(String.format("%s typePath=%s",
                        System.lineSeparator(),this.typePath));
                traceLog.append(String.format("%s servicePath=%s",
                        System.lineSeparator(),this.servicePath));
                traceLog.append(String.format("%s outAcsPath=%s",
                        System.lineSeparator(),this.outAcsPath));
                traceLog.append(String.format("%s inAcsPath=%s",
                        System.lineSeparator(),this.inAcsPath));
                traceLog.append(String.format("%s puPath=%s",
                        System.lineSeparator(),this.puPath));
                traceLog.append(String.format("%s cuPath=%s",
                        System.lineSeparator(),this.cuPath));
            }

            // 创建本服务的节点
            this.createNodeIfNeed();
            if(null != traceLog){
                traceLog.append(String.format("%s 创建本Res服务节点成功!", System.lineSeparator()));
            }

            //本服务对应的节点Cache
            this.typePathCache = new TreeCache(this.curatorFramework, this.typePath);
            this.typePathCache.getListenable().addListener(new ChildrenNodeListener(this));
            this.startTypeNodeCache();
            if(null != traceLog){
                traceLog.append(String.format("%s Res服务节点Cache启动成功：%s",
                        System.lineSeparator(),this.typePath));
            }
        }catch (Exception ex){
            throwEx = ex;
            throw ex;
        }finally {
            if(throwEx == null){
                if(null != traceLog){
                    traceLog.append(String.format("%s 初始化成功！",
                            System.lineSeparator()));
                    log.debug(traceLog.toString());
                }
            }else{
                if(null != traceLog){
                    traceLog.append(String.format("%s 初始化失败：%s",
                            System.lineSeparator(),throwEx.getMessage()));
                    log.debug(traceLog.toString(),throwEx);
                }
            }
        }
    }

    public void destroy(){
        this.delServiceNode(false);
        this.stopTypeNodeCache();
    }

    /**
     * 减少已分配玩家数量，踢出时调用
     *
     * @param deltaNum  减少数量，必须大于等于1
     * @param tryNum    重试次数，默认：3
     *
     * @return
     */
    public IntegerUpdatedResult decreasePuByDelta(int deltaNum,Integer tryNum){
        if(deltaNum<=0){
            return null;
        }

        if(null == tryNum || tryNum<=0)
            tryNum = 3;

        IntegerIncrUpdator updator = new IntegerIncrUpdator(this,this.curatorFramework,this.puPath,
                true,deltaNum,null,null,tryNum);
        IntegerUpdatedResult result = updator.call();

        return result;
    }

    /**
     * 减少已建立的连接数量，连接close时调用
     *
     * @param deltaNum  减少数量，必须大于等于1
     * @param tryNum    重试次数，默认：3
     *
     * @return
     */
    public IntegerUpdatedResult decreaseCuByDelta(int deltaNum,Integer tryNum){
        if(deltaNum<=0){
            return null;
        }

        if(null == tryNum || tryNum<=0)
            tryNum = 3;

        IntegerIncrUpdator updator = new IntegerIncrUpdator(this,this.curatorFramework,this.cuPath,
                true,deltaNum,null,null,tryNum);
        IntegerUpdatedResult result = updator.call();

        return result;
    }

    /**
     * 增加已建立的连接数量，连接建立时调用
     *
     * @param deltaNum  增量数量，必须大于等于1
     * @param tryNum    重试次数，默认：3
     *
     * @return
     */
    public IntegerUpdatedResult addCuByDelta(int deltaNum,Integer tryNum){
        if(deltaNum<=0){
            return null;
        }

        if(null == tryNum || tryNum<=0)
            tryNum = 3;

        IntegerIncrUpdator updator = new IntegerIncrUpdator(this,this.curatorFramework,this.cuPath,
                false,deltaNum,null,null,tryNum);
        IntegerUpdatedResult result = updator.call();

        return result;
    }

    /**
     * 启动Res服务节点监控
     */
    private void startTypeNodeCache(){
        try {
            this.typePathCache.start();
        }catch (Exception ex){
            throw new RuntimeException(String.format("启动Res服务节点Cache[ %s ]失败：%s",
                    this.typePath,ex.getMessage()),ex);
        }
    }
    /**
     * 关闭Res服务节点监控
     */
    private void stopTypeNodeCache(){
        if(null == this.typePathCache)
            return ;

        try{
            this.typePathCache.close();
        }catch (Exception ex){
            log.error(String.format("关闭Res服务节点Cache[ %s ]失败：%s",
                    this.typePath,ex.getMessage()),ex);
        }
    }

    /**
     * 移除本Res服务节点
     * 关闭服务时调用
     * @param throwEx
     */
    private void delServiceNode(boolean throwEx){
        try {
            Stat nodeStat = this.curatorFramework.checkExists().forPath(this.servicePath);
            if (null != nodeStat) {
                this.curatorFramework.delete()
                        .deletingChildrenIfNeeded()
                        .forPath(this.servicePath);
            }
            log.info("移除本Res服务节点成功：{}",this.servicePath);
        }catch (Exception ex){
            if(throwEx)
                throw new RuntimeException(String.format("移除本Res服务节点失败：%s -> %s",
                        ex.getMessage(),this.servicePath),ex);
            else
                log.error(String.format("移除本Res服务节点失败：%s -> %s",
                    ex.getMessage(),this.servicePath),ex);
        }
    }

    private byte[] serializeInt(int value){
        return String.valueOf(value).getBytes(this.defaultCharset);
    }
    private int deserialize2Int(byte[] data){
        if(null == data || data.length<=0)
            return 0;

        return Integer.parseInt(new String(data,this.defaultCharset));
    }
    private byte[] serializeString(String value){
        return value.getBytes(this.defaultCharset);
    }
    private String deserialize2String(byte[] data){
        if(null == data || data.length<=0)
            return null;

        return new String(data,this.defaultCharset);
    }

    /**
     * Res服务节点的事件监听器
     * Res服务中只需要关注连接重连的时候
     * 重新创建服务节点,其它事件无需考虑。
     */
    @Slf4j
    private static class ChildrenNodeListener implements TreeCacheListener {
        private ZkWatchService watchService;

        public ChildrenNodeListener(ZkWatchService watchService){
            this.watchService = watchService;
        }

        /**
         * 在Res服务节点，网络重连
         *
         * @param client
         * @param event
         * @throws Exception
         */
        public void childEvent(CuratorFramework client, TreeCacheEvent event) throws Exception{
            if(event.getType() == TreeCacheEvent.Type.CONNECTION_RECONNECTED){
                try {
                    // 连接重新建立，创建指定服务节点
                    this.watchService.createNodeIfNeed();
                    log.info("重新初始化本Res服务节点成功!");
                }catch (Exception ex){
                    log.error("重新初始化本Res服务节点失败："+ex.getMessage(),ex);
                }
            }
        }
    }

    @Getter
    @Setter
    public static class IntegerUpdatedResult{
        /** 扣减时的值 */
        private int value;
        private int version;
        private int incrValue;

        /** 扣减后的值 */
        private int newValue;
        private int newVersion;
    }

    /**
     * 整数增量更新器
     */
    @Slf4j
    private static class IntegerIncrUpdator implements Callable<IntegerUpdatedResult>{
        private CuratorFramework curatorFramework;
        private ZkWatchService watchService;

        private String nodePath;
        private Integer originalValue;
        private Integer originalVersion;
        private Integer incrementalValue;
        private boolean isDeduct = false;

        private int tryNum;

        /**
         * 构造函数
         * @param curatorFramework  zookeeper客户端
         * @param path              节点路径
         * @param originalValue     原始值
         * @param originalVersion   原始值的版本号
         * @param incrementalValue  增量值，可赋值
         * @param isDeduct          是否扣减：true=扣减 ， false=增加
         */
        public IntegerIncrUpdator(ZkWatchService service, CuratorFramework curatorFramework, String path, boolean isDeduct,
                                  int incrementalValue, Integer originalValue, Integer originalVersion,
                                  int tryNum){
            this.curatorFramework = curatorFramework;
            this.watchService = service;
            this.nodePath = path;
            this.originalValue = originalValue;
            this.originalVersion = originalVersion;
            this.incrementalValue = incrementalValue;
            this.isDeduct = isDeduct;
            this.tryNum = tryNum;
        }

        public IntegerUpdatedResult call() {
            IntegerUpdatedResult result = new IntegerUpdatedResult();
            int tryNum = this.tryNum<=0?3:this.tryNum>10?3:this.tryNum;
            while (tryNum>0) {
                tryNum ++;
                if(null == this.originalValue) {
                    ChildData nodeData = this.watchService.typePathCache.getCurrentData(this.nodePath);
                    if(nodeData == null){
                        log.warn("Res服务节点[{}}不存在，无法完成更新：{}",this.nodePath,this.incrementalValue);
                        return null;
                    }

                    this.originalValue = this.watchService.deserialize2Int(nodeData.getData());
                    this.originalVersion = nodeData.getStat().getVersion();
                }

                int newValue = 0;
                int newVersion = 0;
                int incrValue = 0;
                try {
                    incrValue = this.incrementalValue;
                    if(this.isDeduct){
                        newValue = this.originalValue - incrValue;
                    }else{
                        newValue = this.originalValue + incrValue;
                    }
                    if(newValue<0)
                        newValue = 0;

                    newVersion = this.curatorFramework
                            .setData()
                            .withVersion(this.originalVersion)
                            .forPath(this.nodePath, this.watchService.serializeInt(newValue))
                            .getVersion();

                    if (log.isDebugEnabled()) {
                        log.debug(String.format("成功修改Res服务节点：路径=%s , 增加/扣减=%s , 增量值=%s , 原节点值=%s , 原版本号=%s , 新节点值=%s , 新版本号=%s",
                                this.nodePath,
                                this.isDeduct?"减少":"增加",
                                incrValue,
                                this.originalValue,
                                this.originalVersion,
                                newValue,
                                newVersion));
                    }
                    result.setValue(this.originalValue);
                    result.setVersion(this.originalVersion);
                    result.setIncrValue(incrValue);
                    result.setNewValue(newValue);
                    result.setNewVersion(newVersion);
                    break;
                } catch (Exception ex) {
                    log.debug(String.format("修改Res服务节点失败：路径=%s , 增加/扣减=%s , 增量值=%s , 原节点值=%s , 原版本号=%s , 新节点值=%s , 新版本号=%s -> %s",
                            this.nodePath,
                            this.isDeduct?"减少":"增加",
                            incrValue,
                            this.originalValue,
                            this.originalVersion,
                            newValue,
                            newVersion,
                            ex.getMessage()),ex);
                    this.originalValue=null;
                    this.originalVersion=null;
                }
            }

            return result;
        }
    }
}
