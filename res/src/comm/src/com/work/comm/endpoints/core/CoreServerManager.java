package com.work.comm.endpoints.core;

import com.work.comm.s2s.callback.IChannelActiveCallback;
import com.work.comm.s2s.callback.IChannelAuthCallback;
import com.work.comm.s2s.client.AppConfig;
import com.work.comm.s2s.client.ServerManager;
import com.work.comm.s2s.common.DateUtil;
import com.work.comm.s2s.common.ES2ServerType;
import com.work.comm.s2s.protocal.Protocal;
import com.work.comm.s2s.protocal.S2PacckageUtil;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.util.concurrent.GenericFutureListener;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class CoreServerManager implements IChannelActiveCallback,IChannelAuthCallback {
    /** 单例 */
    private static CoreServerManager INSTANCE = new CoreServerManager();
    public static CoreServerManager getInstance(){
        return INSTANCE;
    }
    public static void initialize() {
        AppConfig appConfig = ServerManager.getInstance().getAppConfig();
        INSTANCE.setAppType(appConfig.getAppType());
        INSTANCE.setAppId(appConfig.getAppId());
        INSTANCE.setOutAccessIp(appConfig.getOutAccessIp());
        INSTANCE.setOutAccessPort(appConfig.getOutAccessPort());
        INSTANCE.setInAccessIp(appConfig.getInAccessIp());
        INSTANCE.setInAccessPort(appConfig.getInAccessPort());
        ServerManager.getInstance().subscribe(INSTANCE.authCallback());
    }
    private CoreServerManager(){
        this.seqNoStartTime = DateUtil.getDateTillHour(null);
        this.seqNo = 0;
    }
    public IChannelActiveCallback activeCallback(){
        return this;
    }
    public IChannelAuthCallback authCallback(){
        return this;
    }
    /**
     * 发送数据包
     * 由上层业务调用
     *
     * @param serverId  对端App的标识符，类型内唯一，可选
     *                  当不指定时，则是向此类型的消息发送
     * @param sendAll   是否向所有服务器发送
     *                  true  : 是
     *                  false : 否
     * @param data       待发送的消息,必填
     *
     * @throws Exception
     *    IllegalArgumentException 参数无效
     */
    public void sendData(String serverId,boolean sendAll,byte[] data) throws Exception{
        if(null == serverId)
            serverId="";
        else
            serverId = serverId.trim();

        ServerManager.getInstance().sendDataBy(ES2ServerType.core.name(),serverId,sendAll,data);
    }

    /** 所在应用标识 */
    private String appType;
    private String appId;

    /** 客户端的访问地址 */
    // IP
    private String outAccessIp;
    // port
    private int outAccessPort=0;

    /** 服务之间的访问地址 */
    // IP
    private String inAccessIp;
    // port
    private int inAccessPort=0;

    // 数据包的流水号
    // 开始计时的时间，每小时重置
    private Date seqNoStartTime;
    private int seqNo;
    private int[] getSeqNo(){
        int[] result = new int[2];
        synchronized (this.seqNoStartTime){
            Date sysTime = DateUtil.getDateTillHour(null);
            if(sysTime.after(this.seqNoStartTime)){
                this.seqNoStartTime = sysTime;
                this.seqNo = 1;
            }else{
                this.seqNo += 1;
            }
            result[0] = (int)(this.seqNoStartTime.getTime()/1000);
            result[1] = this.seqNo;
        }

        return result;
    }

    /** 客户端接入的连接数 */
    private Object connNumLock = new Object();
    private int appConnNum = 0;
    private void addConnNum(boolean isAdd){
        synchronized (this.connNumLock){
            int currVal = this.appConnNum<0?0:this.appConnNum;
           if(isAdd)
               currVal +=1;
           else if(currVal>0)
               currVal -=1;

           this.appConnNum = currVal;
        }
    }

    /**
     * core服务的连接数
     * 大于0，认为不再需要发送初始化数据
     *
     * appId -> 计数器
      */
    private Map<String,Integer> serverCounterMap = new ConcurrentHashMap<>();
    private boolean addCounter(String appId,boolean isAdd){
        appId = appId.trim();
        boolean needReport;
        synchronized (this.serverCounterMap) {
            Integer counter = this.serverCounterMap.get(appId);
            counter = (null == counter || counter < 0 ? 0 : counter);
            needReport = counter == 0;
            boolean needDel = false;
            if (isAdd)
                counter += 1;
            else if (counter > 0) {
                counter -= 1;
                needDel = counter <= 0;
            }
            if (needDel)
                this.serverCounterMap.remove(appId);
            else
                this.serverCounterMap.put(appId, counter);
        }

        return needReport;
    }

    /**
     * 客户端连接通道激活时调用
     *
     * @param channel  当前激活的连接通道
     */
    @Override
    public void active(Channel channel){
        if(null == channel)
            return;

        // 增加计数器及设置关闭监听器，用于递减计数器
        this.addConnNum(true);
        channel.closeFuture().addListener(new PlayerChannelCloseMonitor());

        // 向core上报当前的连接数
        this.report2Core(null);
    }
    private void report2Core(String appId){
        int[] seqNoGroup = this.getSeqNo();
        int reqCode = 1000;

        Object[][] dataObjs = {
                {130, this.appType, Protocal.TYPE_STRING_UTF16},
                {131, this.appId, Protocal.TYPE_STRING_UTF16},
                {132, this.outAccessIp, Protocal.TYPE_STRING_UTF16},
                {133, this.outAccessPort<0?0:this.outAccessPort, Protocal.TYPE_INT_4},
                {134, this.inAccessIp, Protocal.TYPE_STRING_UTF16},
                {135, this.inAccessPort<0?0:this.inAccessPort, Protocal.TYPE_INT_4},
                {136, this.appConnNum, Protocal.TYPE_INT_4},
                {137, seqNoGroup[0], Protocal.TYPE_INT_4},
                {138, seqNoGroup[1], Protocal.TYPE_INT_4}
        };
        if(log.isDebugEnabled())
            log.debug("上报Res客户端连接数到Core服务-{}：appType={},appId={},outIp={},outPort={},inIp={},inPort={},connNum={},seqNoTime={},seqNo={}",
                    reqCode, this.appType,this.appId,
                    this.outAccessIp,this.outAccessPort,
                    this.inAccessIp,this.inAccessPort,this.appConnNum,
                    seqNoGroup[0],seqNoGroup[1]);
        try {
            byte[] bytes = S2PacckageUtil.packAll(dataObjs, reqCode);
            if(log.isDebugEnabled())
                log.debug("上报Res客户端连接数到Core服务-{}：{}",reqCode, Arrays.toString(bytes));
            sendData(appId,false, bytes);
        }catch (Exception ex){
            log.error(String.format("上报Res客户端连接数到Core服务-%s失败：%s",reqCode,ex.getMessage()),ex);
        }
    }

    /**
     * 连接通道身份成功识别
     *
     * @param channel    被识别的连接通道，必填
     * @param appType    对端应用类型，必填
     * @param appId      对端应用ID，必填
     */
    @Override
    public void auth(Channel channel,String appType,String appId){
        log.debug("监听到连接已经完成身份标识：ch={},appType={},appId={}",channel,appType,appId);
        if(null == channel)
            return;
        if(null == appId || "".equals(appId.trim()))
            return;
        if(null == appType || !ES2ServerType.core.name().equalsIgnoreCase(appType.trim()))
            return;

        appId = appId.trim();
        boolean needReport = this.addCounter(appId,true);
        channel.closeFuture().addListener(new CoreChannelCloseMonitor(appId));

        if(needReport){
            this.report2Core(appId);
        }
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }
    public void setAppId(String appId) {
        this.appId = appId;
    }
    public void setOutAccessIp(String outAccessIp) {
        this.outAccessIp = outAccessIp;
    }
    public void setOutAccessPort(int outAccessPort) {
        this.outAccessPort = outAccessPort;
    }
    public void setInAccessIp(String inAccessIp) {
        this.inAccessIp = inAccessIp;
    }
    public void setInAccessPort(int inAccessPort) {
        this.inAccessPort = inAccessPort;
    }

    private class PlayerChannelCloseMonitor implements GenericFutureListener<ChannelFuture> {
        public void operationComplete(ChannelFuture f) throws Exception{
            if(f.isDone() && f.isSuccess()){
                CoreServerManager.this.addConnNum(false);

                // 向core上报当前的连接数
                CoreServerManager.this.report2Core(null);
            }
            f.removeListener(this);
        }
    }
    private class CoreChannelCloseMonitor implements GenericFutureListener<ChannelFuture>{
        private String appId;

        public CoreChannelCloseMonitor(String appId){
            this.appId = appId;
        }
        public void operationComplete(ChannelFuture f) throws Exception{
            if(f.isDone() && f.isSuccess()){
                // 减少服务器的计数器
                CoreServerManager.this.addCounter(this.appId,false);
            }
            f.removeListener(this);
        }
    }
}
