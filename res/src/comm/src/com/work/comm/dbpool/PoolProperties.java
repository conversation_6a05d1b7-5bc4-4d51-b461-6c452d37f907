package com.work.comm.dbpool;

import org.apache.logging.log4j.Logger;

import java.util.Properties;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * Created by IntelliJ IDEA.
 * User: Administrator
 * To change this template use File | Settings | File Templates.
 */
public class PoolProperties {

        private static Logger logger = com.work.comm.util.LogUtil.getLogger(PoolProperties.class);
        private static Properties prop = new Properties();
        private static PoolProperties instance = new PoolProperties();


        private PoolProperties(){
            if(prop.isEmpty()) {
                loadProperties();
            }
        }

        public static PoolProperties getInstance(){
            return instance;
        }

        public String getProperty(String key) {

            return prop.getProperty(key);
        }

        private void loadProperties() {
            FileInputStream is = null;
            try {
                is = new FileInputStream("DZPKproxool.properties");
                //is = new FileInputStream("pool.properties");
                prop.load(is);
                logger.info("Prop is loaded : ");
            } catch (Exception ex) {
                logger.error(ex.getMessage());
            } finally {
                try {
                    if (is != null) {
                        is.close();
                    }
                } catch (IOException ex) {
                    logger.error(" Fail to close InputStream ");
                }
            }
        }
}

