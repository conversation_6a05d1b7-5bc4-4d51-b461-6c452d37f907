package com.work.comm;

import org.apache.logging.log4j.Logger;

import java.util.Properties;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;

/**
 * Created by IntelliJ IDEA.
 * User: Administrator
 * To change this template use File | Settings | File Templates.
 */
public class LoadProperties {
    private static Logger logger = com.work.comm.util.LogUtil.getLogger(LoadProperties.class);
    private static Properties prop = new Properties();

    public static String getProperty(String key){
        if (prop.isEmpty())
            loadWGProperties();
        return prop.getProperty(key);
    }


    public static void loadWGProperties(){
        FileInputStream wgStream=null;
        try{
            String osName = System.getProperty("os.name");
            if(osName.indexOf("Windows") == -1)
                wgStream = new FileInputStream("wg.properties");
            else
                wgStream = new FileInputStream("wg-win.properties");
            prop.load(wgStream);
        }catch(FileNotFoundException ex){
            logger.error("file not found", ex);
        }catch(Exception ex){
            logger.debug("error", ex);
        }finally{
            try{
                if(wgStream!=null){
                    wgStream.close();
                    wgStream = null;
                }
            } catch(IOException ex){
                logger.error(" Fail to close wgStream ");
            }
        }
    }

}
