package com.work.comm.io;

import org.apache.logging.log4j.Logger;

import com.dzpk.work.ServerTask;
import com.dzpk.work.WorkThreadService;
import com.work.comm.protocal.ServiceRequest;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;

/**
 * 消息接受类
 * <AUTHOR>
 *
 */
public class SServerHandler extends SimpleChannelInboundHandler<Object> {

    private static Logger logger = com.work.comm.util.LogUtil.getLogger(SServerHandler.class);
    
    @Override
    public void handlerAdded(ChannelHandlerContext ctx) throws Exception {
        Channel incoming = ctx.channel();
        incoming.writeAndFlush("[SERVER] - " + incoming.remoteAddress() + " add in\n");
    }
    
    @Override
    public void handlerRemoved(ChannelHandlerContext ctx) throws Exception {
        Channel incoming = ctx.channel();
        incoming.writeAndFlush("[SERVER] - " + incoming.remoteAddress() + " leave\n");
    }
    
    public static void printHexString( byte[] b) {
        for (int i = 0; i < b.length; i++) {
            String hex = Integer.toHexString(b[i] & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            System.out.print(hex.toUpperCase());
        }
    }
    
    @Override
    protected void channelRead0(ChannelHandlerContext ctx, Object msg) throws Exception {
        ByteBuf inMsg = (ByteBuf) msg;
        byte[] bytes = new byte[inMsg.readableBytes()];
        inMsg.readBytes(bytes);
        // printHexString(bytes);
        ServiceRequest serviceRequest = new ServiceRequest();
        boolean result = serviceRequest.init(ctx.channel(), bytes);
        if (!result) {
            logger.debug("invalid request");
            ctx.close();
        }
        logger.debug("ctx: " + ctx);
        logger.debug("request code: " + serviceRequest.getRequestCode());
        ServerTask serverTask = new ServerTask(serviceRequest, ctx);
        WorkThreadService.pool.execute(serverTask);
//        byte[] retBytes = HandlerFactory.getInstance().getHandler(serviceRequest.getRequestCode())
//                .handleRequest(serviceRequest);
//        if (retBytes != null) {
//            // printHexString(retBytes);
//            logger.debug("send to client");
//            ctx.writeAndFlush(Unpooled.copiedBuffer(retBytes));
//            // ctx.write(retBytes);
//        } else {
//            logger.debug("nothing to send");
//        }
    }
    
    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        Channel incoming = ctx.channel();
        logger.debug("client: " + incoming.remoteAddress() + " online");
    }
    
    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        Channel incoming = ctx.channel();
        logger.debug("client: " + incoming.remoteAddress() + " offline");
    }
    
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        Channel incoming = ctx.channel();
        logger.debug("client: " + incoming.remoteAddress() + " exception " + cause.toString());
        // 当出现异常就关闭连接
        ctx.close();
    }
}
