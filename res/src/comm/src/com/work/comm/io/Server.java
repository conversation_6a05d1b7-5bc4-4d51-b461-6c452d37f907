package com.work.comm.io;

import org.apache.logging.log4j.Logger;

import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;

public class Server implements Runnable {

    private static Logger logger = com.work.comm.util.LogUtil.getLogger(Server.class);
    
    private int port = 0;
    // 服务类型 0客户端 1服务端
    private int type = 0;
    
    public Server(int port, int type) {
        this.port = port;
        this.type = type;
    }
    
    @Override
    public void run() {
        EventLoopGroup bossGroup = new NioEventLoopGroup();
        EventLoopGroup workGroup = new NioEventLoopGroup();
        try {
            ServerBootstrap bootstrap = new ServerBootstrap();
            if (type == 0) {
                bootstrap.group(bossGroup, workGroup)
                        .channel(NioServerSocketChannel.class)
                        .childHandler(new ServerInitializer())
                        .option(ChannelOption.SO_BACKLOG, 1024)
                        .childOption(ChannelOption.SO_KEEPALIVE, true);
            } else {
                bootstrap.group(bossGroup, workGroup)
                        .channel(NioServerSocketChannel.class)
                        .childHandler(new SServerInitializer())
                        .option(ChannelOption.SO_BACKLOG, 128)
                        .childOption(ChannelOption.SO_KEEPALIVE, true);
            }

            logger.info("netty server (:{}) start!", port);
            
            ChannelFuture future = null;
            try {
                future = bootstrap.bind(port).sync();
            } catch (InterruptedException e) {
                logger.error("bootstrap bind error", e);
            }
            try {
                if (future != null) {
                    future.channel().closeFuture().sync();
                }
            } catch (InterruptedException e) {
                logger.error("close netty server error", e);
            }
        } finally {
            workGroup.shutdownGracefully();
            bossGroup.shutdownGracefully();
            logger.info("netty server stop!");
        }
    }
    
    public static void main(String[] args) {
        try {
            new Server(8088, 0).run();
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }
}
