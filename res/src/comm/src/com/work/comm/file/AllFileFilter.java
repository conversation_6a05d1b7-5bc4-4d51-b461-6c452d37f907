package com.work.comm.file;

import java.io.File;

/**
 * Created by IntelliJ IDEA.
 * User: Administrator
 * To change this template use File | Settings | File Templates.
 */
public class AllFileFilter extends CombineFileFilter {

    /**
     * 构造一个针对swing的AllFileFilter。
     * @since  0.6
     */
    public AllFileFilter() {
      super(SWING);
    }
    /**
     * 根据指定类型构造一个AllFileFilter。
     * @param type 过滤器类型
     * @since  0.6
     */
    public AllFileFilter(int type) {
      super(type);
    }

    /**
     * 判断指定的文件是否可以被接受。
     * @param file 需要判断的文件
     * @return 在任何情况都返回true。
     * @since  0.6
     */
    protected boolean acceptFile(File file) {
      return true;
    }

    /**
     * 返回过滤器的描述字符串。
     * @return 过滤器的描述字符串“All Files”
     * @since  0.1
     */
    public String getDescription() {
      return "All Files";
    }

}
